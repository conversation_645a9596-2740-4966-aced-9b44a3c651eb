/**
 * 简单的ID生成器，替代nanoid
 * 生成类似nanoid的短ID
 */

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

function generateId(size = 21) {
  let id = '';
  let i = size;
  
  while (i--) {
    id += alphabet[(Math.random() * alphabet.length) | 0];
  }
  
  return id;
}

// 生成基于时间戳的ID（更短，但仍然唯一）
function generateTimeId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// 生成UUID v4格式的ID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

module.exports = {
  generateId,
  generateTimeId,
  generateUUID,
  // 默认导出，兼容nanoid的使用方式
  nanoid: generateId
};
