/* src/assets/main.css */

@font-face {
  /* 1. 定义字体名称 */
  font-family: 'MyWebAppFont'; 

  /* 2. 引用你的 WOFF2 文件路径 (确保文件名和路径正确) */
  src: url('/fonts/NotoSansSC-Regular.woff2') format('woff2');


}

html {
    /* 4. 全局应用这个字体 */
    font-family: 'MyWebAppFont', sans-serif;

}
  

/*:root{*/
/*    --el-color-primary:#201dba;*/
/*}*/

html.light{
    --el-color-primary: #201dba;
    --el-bg-color: #ffffff;
    --el-bg-color-page: #f5f7fa;
    --el-bg-color-overlay: #ffffff;
    --el-text-color-primary: #303133;
    --el-text-color-regular: #606266;
    --el-border-color-light: #e4e7ed;
    --el-border-color-lighter: #ebeef5;
    --el-fill-color-blank: #ffffff;




    --editor-main-bg: #f0f0f2;
}

html.dark {
    /* 主背景色 - 更深邃的背景色调 */
    --el-bg-color: #1a1a1a;
    --el-bg-color-page: #242424;
    --el-bg-color-overlay: #2a2a2a;

    /* 菜单和悬停状态 */
    --el-menu-active-bg-color: #2d4b6d;
    --el-menu-hover-bg-color: #2a2a2a;


    /*编辑器页面相关*/
    --editor-main-bg: #2d2d2d;
    --editor-left-bg: #363636;




    /* 主色调：深邃但明亮的蓝色 */
    --el-color-primary: #4080ff;
    --el-color-primary-light-3: #5c93ff;
    --el-color-primary-light-5: #79a6ff;
    --el-color-primary-light-7: #96b9ff;
    --el-color-primary-light-8: #2d4b6d;
    --el-color-primary-light-9: #1c2b3d;
    --el-color-primary-dark-2: #3366cc;

    /* 文字颜色：柔和的灰白色调 */
    --el-text-color-primary: #e0e0e0;
    --el-text-color-regular: #b0b0b0;
    --el-text-color-secondary: #808080;
    --el-text-color-placeholder: #606060;
    --el-text-color-disabled: #404040;

    /* 边框和分割线 - 更柔和的深色 */
    --el-border-color: #333333;
    --el-border-color-light: #3d3d3d;
    --el-border-color-lighter: #474747;
    --el-border-color-extra-light: #515151;

    /* 填充色 - 更协调的深色层次 */
    --el-fill-color: #2a2a2a;
    --el-fill-color-light: #303030;
    --el-fill-color-lighter: #363636;
    --el-fill-color-extra-light: #3d3d3d;
    --el-fill-color-dark: #242424;
    --el-fill-color-darker: #1f1f1f;
    --el-fill-color-blank: #1a1a1a;

    /* 功能色 - 柔和但清晰的状态颜色 */
    --el-color-success: #67c23a;
    --el-color-success-light: #529a2e;
    --el-color-success-lighter: #3e7523;

    --el-color-warning: #e6a23c;
    --el-color-warning-light: #b88230;
    --el-color-warning-lighter: #8a6224;

    --el-color-danger: #f56c6c;
    --el-color-danger-light: #c45656;
    --el-color-danger-lighter: #934141;

    --el-color-info: #909399;
    --el-color-info-light: #73757a;
    --el-color-info-lighter: #56575c;

    /* 遮罩颜色 */
    --el-mask-color: rgba(0, 0, 0, 0.7);
    --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);

    /* 卡片特殊效果 */
    --card-shadow-color: rgba(0, 0, 0, 0.2);
    --card-hover-shadow: rgba(64, 128, 255, 0.1);
    
    /* 应用背景色 */
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
}