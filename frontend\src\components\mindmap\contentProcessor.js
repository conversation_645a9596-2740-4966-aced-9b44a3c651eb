import { createUid } from 'simple-mind-map/src/utils'

/**
 * 内容处理器 - 负责思维导图与markdown内容的双向转换
 */
export class ContentProcessor {
  constructor() {
    this.listeners = []
  }

  /**
   * 解析Markdown为思维导图数据
   * @param {string} markdown - markdown内容
   * @param {string} title - 文档标题（仅在没有H1标题时使用）
   * @param {Object} options - 解析选项
   * @param {boolean} options.showContentAsNodes - 是否将内容显示为子节点
   * @returns {Object} 思维导图数据
   */
  parseMarkdownToMindmap(markdown, title = '未命名文档', options = {}) {
    const { showContentAsNodes = true } = options
    if (!markdown) {
      return this.createEmptyMindmap(title)
    }

    const lines = markdown.split('\n')
    let root = null
    const stack = []
    let currentContent = []
    let hasFoundRoot = false

    lines.forEach((line) => {
      const trimmed = line.trim()
      if (!trimmed) {
        currentContent.push('')
        return
      }

      // 检查是否是标题 - 支持任意级别，但限制最大为6级
      const headingMatch = trimmed.match(/^(#+)\s+(.+)$/)
      if (headingMatch) {
        const rawLevel = headingMatch[1].length
        const level = Math.min(rawLevel, 6) // 限制最大为6级
        const nodeTitle = headingMatch[2]

        // 如果原始级别超过6级，在标题前添加层级指示
        const displayTitle = rawLevel > 6 ? `${'  '.repeat(rawLevel - 6)}${nodeTitle}` : nodeTitle

        // 保存之前的内容
        if (stack.length > 0) {
          stack[stack.length - 1].content = currentContent.join('\n').trim()
        }
        currentContent = []

        // 创建新节点
        const node = {
          id: createUid(),
          title: displayTitle,
          level,
          rawLevel, // 保存原始级别
          children: [],
          content: '',
          expanded: true,
          x: 0,
          y: 0
        }

        // 如果是第一个H1标题，作为根节点
        if (level === 1 && !hasFoundRoot) {
          root = node
          root.isRoot = true
          stack.push(root)
          hasFoundRoot = true
        } else if (hasFoundRoot) {
          // 找到正确的父节点
          while (stack.length > 0 && stack[stack.length - 1].level >= level) {
            stack.pop()
          }

          // 如果栈为空，说明这是一个新的顶级节点，需要特殊处理
          if (stack.length === 0) {
            // 如果没有根节点，这个节点成为根节点
            if (!root) {
              root = node
              root.isRoot = true
              stack.push(root)
            } else {
              // 如果已有根节点，将这个节点作为根节点的子节点
              root.children.push(node)
              stack.push(root, node)
            }
          } else {
            stack[stack.length - 1].children.push(node)
            stack.push(node)
          }
        } else {
          // 还没找到H1标题，但遇到了其他级别的标题
          // 将第一个遇到的标题作为根节点
          root = node
          root.isRoot = true
          stack.push(root)
          hasFoundRoot = true
        }
      } else {
        currentContent.push(line)
      }
    })

    // 保存最后的内容
    if (stack.length > 0) {
      stack[stack.length - 1].content = currentContent.join('\n').trim()
    }

    // 如果没有找到任何标题，创建一个默认根节点
    if (!root) {
      root = this.createEmptyMindmap(title)
    }

    // 如果启用了内容显示为子节点，处理所有节点的内容
    if (showContentAsNodes) {
      this.convertContentToChildNodes(root)
    }

    return root
  }

  /**
   * 将节点内容转换为子节点 - 优化版本，避免中间层级
   * @param {Object} node - 要处理的节点
   */
  convertContentToChildNodes(node) {
    if (!node) return

    // 处理当前节点的内容
    if (node.content && node.content.trim()) {
      const parsedNodes = this.parseContentToNodes(node.content, node.level, false) // 不强制为内容节点
      if (parsedNodes.length > 0) {
        // 直接将解析出的节点作为当前节点的子节点，避免中间层级
        // 调整子节点的层级，确保它们是当前节点的直接子节点
        const adjustedNodes = parsedNodes.map(childNode => ({
          ...childNode,
          level: node.level + 1 // 确保是下一级
        }))

        // 将新解析的节点添加到现有子节点前面
        node.children = [...adjustedNodes, ...node.children]

        // 清空content字段，因为已经转换为子节点
        node.content = ''
      }
    }

    // 递归处理所有子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => this.convertContentToChildNodes(child))
    }
  }

  /**
   * 解析内容为节点列表
   * @param {string} content - 内容文本
   * @param {number} parentLevel - 父节点层级
   * @param {boolean} isContentParsing - 是否是内容解析模式
   * @returns {Array} 节点列表
   */
  parseContentToNodes(content, parentLevel, isContentParsing = false) {
    if (!content || !content.trim()) return []

    const nodes = []
    const lines = content.split('\n')

    // 处理不同类型的内容
    let currentParagraph = []

    lines.forEach(line => {
      const trimmedLine = line.trim()

      if (!trimmedLine) {
        // 空行，结束当前段落
        if (currentParagraph.length > 0) {
          const paragraphText = currentParagraph.join(' ').trim()
          if (paragraphText) {
            nodes.push(this.createContentOrTitleNode(paragraphText, parentLevel, isContentParsing))
          }
          currentParagraph = []
        }
      } else {
        // 检查是否是标题
        const headingMatch = trimmedLine.match(/^(#+)\s+(.+)$/)
        if (headingMatch) {
          // 处理之前累积的段落
          if (currentParagraph.length > 0) {
            const paragraphText = currentParagraph.join(' ').trim()
            if (paragraphText) {
              nodes.push(this.createContentOrTitleNode(paragraphText, parentLevel, true))
            }
            currentParagraph = []
          }

          const rawLevel = headingMatch[1].length
          const nodeTitle = headingMatch[2]

          // 智能计算层级：
          // 1. 如果是内容解析模式，标题直接作为下一级
          // 2. 如果不是内容解析模式，根据原始标题层级计算
          let targetLevel
          if (isContentParsing) {
            targetLevel = parentLevel + 1
          } else {
            // 保持相对层级关系，但确保不超过6级
            targetLevel = Math.min(rawLevel, 6)
            // 确保至少比父级高一级
            targetLevel = Math.max(targetLevel, parentLevel + 1)
          }

          nodes.push({
            id: createUid(),
            title: nodeTitle,
            level: targetLevel,
            rawLevel: rawLevel, // 保存原始层级用于调试
            children: [],
            content: '',
            expanded: true,
            isTitle: true
          })
        } else if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ') || trimmedLine.startsWith('+ ')) {
          // 列表项，单独作为节点
          if (currentParagraph.length > 0) {
            const paragraphText = currentParagraph.join(' ').trim()
            if (paragraphText) {
              nodes.push(this.createContentOrTitleNode(paragraphText, parentLevel, isContentParsing))
            }
            currentParagraph = []
          }

          const listItemText = trimmedLine.replace(/^[-*+]\s+/, '').trim()
          if (listItemText) {
            nodes.push(this.createContentOrTitleNode(listItemText, parentLevel, true))
          }
        } else {
          // 普通文本行，加入当前段落
          currentParagraph.push(trimmedLine)
        }
      }
    })

    // 处理最后的段落
    if (currentParagraph.length > 0) {
      const paragraphText = currentParagraph.join(' ').trim()
      if (paragraphText) {
        nodes.push(this.createContentOrTitleNode(paragraphText, parentLevel, isContentParsing))
      }
    }

    return nodes
  }

  /**
   * 创建内容节点或标题节点
   * @param {string} text - 节点文本
   * @param {number} parentLevel - 父节点层级
   * @param {boolean} isContent - 是否是内容节点
   * @returns {Object} 节点对象
   */
  createContentOrTitleNode(text, parentLevel, isContent = false) {
    return {
      id: createUid(),
      title: text,
      level: isContent ? parentLevel : parentLevel + 1, // 内容节点不增加层级
      children: [],
      content: '',
      expanded: true,
      isContent: isContent, // 标记是否是内容节点
      isTitle: !isContent
    }
  }

  /**
   * 分离内容节点和标题节点
   * @param {Array} nodes - 节点列表
   * @returns {Object} {titleNodes, contentNodes}
   */
  separateContentAndTitleNodes(nodes) {
    const titleNodes = []
    const contentNodes = []

    nodes.forEach(node => {
      if (node.isContent) {
        contentNodes.push(node)
      } else {
        titleNodes.push(node)
      }
    })

    return { titleNodes, contentNodes }
  }

  /**
   * 创建空的思维导图
   * @param {string} title - 文档标题
   * @returns {Object} 空的思维导图数据
   */
  createEmptyMindmap(title) {
    return {
      id: createUid(),
      title: title || '新建思维导图',
      level: 1,
      children: [],
      content: '',
      expanded: true,
      isRoot: true
    }
  }

  /**
   * 将思维导图数据转换为Markdown
   * @param {Object} mindmapData - 思维导图数据
   * @returns {string} markdown内容
   */
  convertMindmapToMarkdown(mindmapData) {
    if (!mindmapData) return ''

    return this.convertNodeToMarkdown(mindmapData, 0)
  }

  /**
   * 递归转换节点为markdown
   * @param {Object} node - 节点数据
   * @param {number} level - 层级（0表示根节点）
   * @param {boolean} isContentNode - 是否是内容节点
   * @returns {string} markdown内容
   */
  convertNodeToMarkdown(node, level = 0, isContentNode = false) {
    let markdown = ''

    // 判断是否是内容节点
    const isContent = isContentNode || node.isContent || this.isContentNode(node, level)

    if (node.isRoot || level === 0) {
      // 根节点使用其实际的level，如果没有则默认为1
      const actualLevel = node.level || 1
      const heading = '#'.repeat(actualLevel)
      markdown += `${heading} ${node.title}\n\n`

      if (node.content) {
        markdown += `${node.content}\n\n`
      }

      // 处理子节点
      if (node.children) {
        const { titleNodes, contentNodes } = this.separateContentNodes(node.children)

        // 先输出内容节点作为段落
        if (contentNodes.length > 0) {
          contentNodes.forEach(contentNode => {
            markdown += `${contentNode.title}\n\n`
          })
        }

        // 再输出标题节点
        titleNodes.forEach((child) => {
          markdown += this.convertNodeToMarkdown(child, actualLevel, false)
        })
      }
    } else if (isContent) {
      // 内容节点，作为段落输出
      markdown += `${node.title}\n\n`
    } else {
      // 标题节点
      const heading = '#'.repeat(level + 1)
      markdown += `${heading} ${node.title}\n\n`

      if (node.content) {
        markdown += `${node.content}\n\n`
      }

      if (node.children) {
        const { titleNodes, contentNodes } = this.separateContentNodes(node.children)

        // 先输出内容节点作为段落
        if (contentNodes.length > 0) {
          contentNodes.forEach(contentNode => {
            markdown += `${contentNode.title}\n\n`
          })
        }

        // 再输出标题节点
        titleNodes.forEach((child) => {
          markdown += this.convertNodeToMarkdown(child, level + 1, false)
        })
      }
    }

    return markdown
  }

  /**
   * 判断是否是内容节点
   * @param {Object} node - 节点
   * @param {number} level - 当前层级
   * @returns {boolean} 是否是内容节点
   */
  isContentNode(node, level) {
    // 如果节点没有子节点，且不是明显的标题格式，则认为是内容节点
    if (!node.children || node.children.length === 0) {
      // 检查标题是否像是内容而不是标题
      const title = node.title.trim()

      // 如果标题很长（超过50个字符）或包含句号，可能是内容
      if (title.length > 50 || title.includes('。') || title.includes('.')) {
        return true
      }

      // 如果标题包含多个句子，可能是内容
      if (title.split(/[。.!！?？]/).length > 2) {
        return true
      }
    }

    return false
  }

  /**
   * 分离标题节点和内容节点
   * @param {Array} children - 子节点列表
   * @returns {Object} {titleNodes, contentNodes}
   */
  separateContentNodes(children) {
    const titleNodes = []
    const contentNodes = []

    children.forEach(child => {
      if (this.isContentNode(child, 0)) {
        contentNodes.push(child)
      } else {
        titleNodes.push(child)
      }
    })

    return { titleNodes, contentNodes }
  }

  /**
   * 获取节点的完整路径（用于AI上下文）
   * @param {Object} node - 目标节点
   * @param {Object} rootNode - 根节点
   * @returns {string} 节点路径
   */
  getNodePath(node, rootNode) {
    const path = []

    const findPath = (current, target, currentPath) => {
      if (current.id === target.id) {
        path.push(...currentPath, current.title)
        return true
      }

      if (current.children) {
        for (const child of current.children) {
          if (findPath(child, target, [...currentPath, current.title])) {
            return true
          }
        }
      }

      return false
    }

    findPath(rootNode, node, [])
    return path.join(' > ')
  }

  /**
   * 获取节点的上下文信息（用于AI生成）
   * @param {Object} node - 目标节点
   * @param {Object} rootNode - 根节点
   * @returns {Object} 上下文信息
   */
  getNodeContext(node, rootNode) {
    const path = this.getNodePath(node, rootNode)
    const siblings = this.getSiblings(node, rootNode)
    const parentNode = this.getParentNode(node, rootNode)

    return {
      path,
      currentTitle: node.title,
      currentContent: node.content || '',
      level: node.level,
      siblings: siblings.map(s => s.title),
      parentTitle: parentNode ? parentNode.title : '',
      parentContent: parentNode ? parentNode.content || '' : '',
      existingChildren: node.children ? node.children.map(c => c.title) : []
    }
  }

  /**
   * 获取兄弟节点
   * @param {Object} targetNode - 目标节点
   * @param {Object} rootNode - 根节点
   * @returns {Array} 兄弟节点列表
   */
  getSiblings(targetNode, rootNode) {
    const parentNode = this.getParentNode(targetNode, rootNode)
    if (!parentNode) return []

    return parentNode.children.filter(child => child.id !== targetNode.id)
  }

  /**
   * 获取父节点
   * @param {Object} targetNode - 目标节点
   * @param {Object} rootNode - 根节点
   * @returns {Object|null} 父节点
   */
  getParentNode(targetNode, rootNode) {
    const findParent = (current) => {
      if (current.children) {
        for (const child of current.children) {
          if (child.id === targetNode.id) {
            return current
          }
          const found = findParent(child)
          if (found) return found
        }
      }
      return null
    }

    return findParent(rootNode)
  }

  /**
   * 应用AI生成的内容
   * @param {Object} params - 参数对象
   * @param {Object} params.node - 目标节点
   * @param {string} params.type - 生成类型
   * @param {string} params.content - 生成的内容
   * @param {number} params.childrenCount - 子节点数量
   * @param {Object} params.mindmapData - 思维导图数据
   * @returns {boolean} 是否成功应用
   */
  applyAIGeneratedContent({ node, type, content, childrenCount, mindmapData }) {
    if (!content || !node) return false

    try {
      switch (type) {
        case 'expand':
          return this.expandNodeContent(node, content)
        case 'children':
          return this.generateChildNodes(node, content, childrenCount)
        case 'related':
          return this.generateRelatedContent(node, content, mindmapData)
        default:
          console.warn('未知的生成类型:', type)
          return false
      }
    } catch (error) {
      console.error('应用AI生成内容失败:', error)
      return false
    }
  }

  /**
   * 扩展节点内容
   * @param {Object} node - 目标节点
   * @param {string} content - 生成的内容
   * @returns {boolean} 是否成功
   */
  expandNodeContent(node, content) {
    const existingContent = node.content || ''
    node.content = existingContent + (existingContent ? '\n\n' : '') + content.trim()
    return true
  }

  /**
   * 生成子节点
   * @param {Object} node - 父节点
   * @param {string} content - 生成的内容
   * @param {number} expectedCount - 期望的子节点数量
   * @returns {boolean} 是否成功
   */
  generateChildNodes(node, content, expectedCount = 5) {
    try {
      // 解析生成的内容，支持多种格式
      const childNodes = this.parseAIGeneratedNodes(content, node.level + 1)

      // 限制子节点数量
      const limitedNodes = childNodes.slice(0, expectedCount)

      // 添加到父节点
      if (!node.children) {
        node.children = []
      }
      node.children.push(...limitedNodes)

      return limitedNodes.length > 0
    } catch (error) {
      console.error('生成子节点失败:', error)
      return false
    }
  }

  /**
   * 解析AI生成的节点内容
   * @param {string} content - AI生成的内容
   * @param {number} level - 节点层级
   * @returns {Array} 解析出的节点列表
   */
  parseAIGeneratedNodes(content, level) {
    const nodes = []
    const lines = content.split('\n').filter(line => line.trim())

    for (const line of lines) {
      const trimmed = line.trim()
      if (!trimmed) continue

      // 支持多种格式：
      // 1. 纯文本
      // 2. 数字序号：1. 标题
      // 3. 符号：- 标题、* 标题、+ 标题
      // 4. 标题：内容 格式
      // 5. Markdown标题：## 标题
      let title = ''
      let nodeContent = ''

      // 检查是否是Markdown标题 - 支持任意级别但限制在6级内
      const headingMatch = trimmed.match(/^(#+)\s+(.+)$/)
      if (headingMatch) {
        const rawLevel = headingMatch[1].length
        title = headingMatch[2]

        // 如果超过6级，调整节点层级并在标题前添加缩进指示
        if (rawLevel > 6) {
          const indentLevel = rawLevel - 6
          title = `${'  '.repeat(indentLevel)}${title}`
          // 将节点层级限制为6级，但保持相对关系
          level = Math.min(level + (rawLevel - 1), 6)
        }
      } else {
        // 检查是否有冒号分隔的标题和内容
        const colonIndex = trimmed.indexOf('：') !== -1 ? trimmed.indexOf('：') : trimmed.indexOf(':')
        if (colonIndex !== -1) {
          const beforeColon = trimmed.substring(0, colonIndex).trim()
          const afterColon = trimmed.substring(colonIndex + 1).trim()

          // 清理标题前的序号和符号
          title = beforeColon.replace(/^[\d\.\-\*\+\s]+/, '').trim()
          nodeContent = afterColon
        } else {
          // 没有冒号，整行作为标题，清理前缀
          title = trimmed.replace(/^[\d\.\-\*\+\s]+/, '').trim()
        }
      }

      if (title) {
        const node = {
          id: createUid(),
          title: title,
          level: level,
          children: [],
          content: nodeContent,
          expanded: true,
          x: 0,
          y: 0
        }
        nodes.push(node)
      }
    }

    return nodes
  }

  /**
   * 生成相关内容
   * @param {Object} node - 当前节点
   * @param {string} content - 生成的内容
   * @param {Object} mindmapData - 思维导图数据
   * @returns {boolean} 是否成功
   */
  generateRelatedContent(node, content, mindmapData) {
    const parentNode = this.findParentNode(mindmapData, node.id)
    if (!parentNode) {
      console.warn('无法找到父节点')
      return false
    }

    const relatedNode = {
      id: createUid(),
      title: `${node.title} - 相关内容`,
      level: node.level,
      children: [],
      content: content.trim(),
      expanded: true,
      x: 0,
      y: 0
    }

    if (!parentNode.children) {
      parentNode.children = []
    }
    parentNode.children.push(relatedNode)

    return true
  }

  /**
   * 查找父节点
   * @param {Object} root - 根节点
   * @param {string} targetId - 目标节点ID
   * @returns {Object|null} 父节点
   */
  findParentNode(root, targetId) {
    if (!root.children) return null

    for (const child of root.children) {
      if (child.id === targetId) {
        return root
      }

      const found = this.findParentNode(child, targetId)
      if (found) return found
    }

    return null
  }

  /**
   * 查找节点
   * @param {Object} root - 根节点
   * @param {string} targetId - 目标节点ID
   * @returns {Object|null} 找到的节点
   */
  findNode(root, targetId) {
    if (root.id === targetId) return root

    if (root.children) {
      for (const child of root.children) {
        const found = this.findNode(child, targetId)
        if (found) return found
      }
    }

    return null
  }

  /**
   * 深度克隆思维导图数据
   * @param {Object} data - 原始数据
   * @returns {Object} 克隆的数据
   */
  cloneMindmapData(data) {
    return JSON.parse(JSON.stringify(data))
  }

  /**
   * 验证思维导图数据结构
   * @param {Object} data - 思维导图数据
   * @returns {boolean} 是否有效
   */
  validateMindmapData(data) {
    if (!data || typeof data !== 'object') return false
    if (!data.id || !data.title) return false
    if (data.children && !Array.isArray(data.children)) return false

    // 递归验证子节点
    if (data.children) {
      return data.children.every(child => this.validateMindmapData(child))
    }

    return true
  }
}

// 创建单例实例
export const contentProcessor = new ContentProcessor()
