<template>
  <div v-if="visible" class="code-preview-modal">
    <div class="modal-overlay" @click="close"></div>
    <div class="modal-container" :class="{ 'preview-fullscreen': isPreviewFullscreen }">
      <div class="modal-header">
        <div class="header-actions">

          <el-button class="close-btn" text circle @click="close" title="关闭">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
      <div class="modal-body">
        <div class="code-panel" :class="{ 'hidden': isPreviewFullscreen }" :style="{ width: codeWidth + '%' }">
          <div class="code-header">
            <h4><el-icon><Document /></el-icon> HTML代码</h4>
          </div>
          <pre class="code-content"><code>{{ code }}</code></pre>
        </div>
        
        <!-- 拖动分隔线 -->
        <div 
          v-if="!isPreviewFullscreen" 
          class="panel-resizer"
          @mousedown.prevent="startResize"
          :title="'拖动调整宽度'"
        >
          <div class="resizer-handle"></div>
        </div>
        
        <div class="preview-panel" :class="{ 'fullscreen': isPreviewFullscreen }" :style="previewWidth">
          <div class="preview-header">
            <h4><el-icon><View /></el-icon> 预览效果</h4>
            <div class="preview-controls">
              <el-tooltip :content="isPreviewFullscreen ? '退出全屏' : '全屏预览'" placement="top">
                <el-button class="control-btn" text circle size="small" @click="toggleFullscreen">
                  <el-icon><component :is="isPreviewFullscreen ? 'FullScreen' : 'Expand'" /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="刷新预览" placement="top">
                <el-button class="control-btn" text circle size="small" @click="refreshPreview">
                  <el-icon><RefreshRight /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
          <div class="preview-content">
            <iframe 
              ref="previewIframe" 
              class="preview-iframe"
              sandbox="allow-same-origin allow-scripts allow-popups allow-popups-to-escape-sandbox allow-presentation"
              loading="lazy"
              title="HTML Preview"
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { 
  Close, Refresh, Document, 
  View, RefreshRight, FullScreen, Expand 
} from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  code: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible']);
const previewIframe = ref(null);
const isPreviewFullscreen = ref(false);
const codeWidth = ref(50); // 默认左侧面板宽度 50%

// 计算右侧预览面板宽度
const previewWidth = computed(() => {
  if (isPreviewFullscreen.value) {
    return { width: '100%' };
  }
  return { width: (100 - codeWidth.value) + '%' };
});

// 拖动调整面板大小相关变量
const isResizing = ref(false);
const startX = ref(0);
const startWidth = ref(0);

// 直接引用DOM元素
let codePanel = null;
let previewPanel = null;
let modalContainer = null;

// 初始化DOM引用
function initDomRefs() {
  codePanel = document.querySelector('.code-panel');
  previewPanel = document.querySelector('.preview-panel');
  modalContainer = document.querySelector('.modal-container');
}

// 开始拖动调整大小
function startResize(e) {
  // 确保DOM引用已初始化
  if (!codePanel || !previewPanel || !modalContainer) {
    initDomRefs();
  }
  
  e.stopPropagation(); // 阻止事件冒泡
  isResizing.value = true;
  startX.value = e.clientX;
  
  // 获取当前实际宽度作为起始宽度
  const containerWidth = modalContainer.offsetWidth;
  const currentCodeWidth = codePanel.offsetWidth;
  startWidth.value = (currentCodeWidth / containerWidth) * 100;
  
  // 添加临时样式类，提供视觉反馈
  document.body.classList.add('resizing');
  
  // 直接设置样式，而不是通过Vue响应式系统
  modalContainer.style.setProperty('--code-panel-width', `${startWidth.value}%`);
  modalContainer.style.setProperty('--preview-panel-width', `${100 - startWidth.value}%`);
  
  // 使用事件捕获模式，确保不会错过任何鼠标事件
  document.addEventListener('mousemove', resizePanel, { capture: true });
  document.addEventListener('mouseup', stopResize, { capture: true });
  document.body.style.cursor = 'col-resize';
  document.body.style.userSelect = 'none'; // 防止拖动时选中文本
}

// 拖动调整面板大小 - 使用requestAnimationFrame优化性能
let rafId = null;
function resizePanel(e) {
  if (!isResizing.value) return;
  
  // 阻止默认行为和冒泡，确保拖动顺畅
  e.preventDefault();
  e.stopPropagation();
  
  // 取消上一帧动画，避免堆积
  if (rafId) {
    cancelAnimationFrame(rafId);
  }
  
  // 请求动画帧，优化性能
  rafId = requestAnimationFrame(() => {
    if (!codePanel || !previewPanel || !modalContainer) return;
    
    const containerWidth = modalContainer.offsetWidth;
    const dx = e.clientX - startX.value; // 鼠标移动距离
    const newWidthPercent = startWidth.value + (dx / containerWidth * 100);
    
    // 限制拖动范围，不能太窄
    if (newWidthPercent >= 20 && newWidthPercent <= 80) {
      // 直接更新DOM元素样式
      codePanel.style.width = `${newWidthPercent}%`;
      previewPanel.style.width = `${100 - newWidthPercent}%`;
      
      // 同时更新CSS变量，以便CSS过渡效果可以使用
      modalContainer.style.setProperty('--code-panel-width', `${newWidthPercent}%`);
      modalContainer.style.setProperty('--preview-panel-width', `${100 - newWidthPercent}%`);
      
      // 更新Vue状态，但不影响实时拖动
      codeWidth.value = newWidthPercent;
    }
  });
}

// 停止拖动调整大小
function stopResize(e) {
  if (!isResizing.value) return;
  
  // 阻止默认行为和冒泡
  e.preventDefault();
  e.stopPropagation();
  
  isResizing.value = false;
  
  // 取消所有动画帧
  if (rafId) {
    cancelAnimationFrame(rafId);
    rafId = null;
  }
  
  // 移除临时样式类
  document.body.classList.remove('resizing');
  
  document.removeEventListener('mousemove', resizePanel, { capture: true });
  document.removeEventListener('mouseup', stopResize, { capture: true });
  document.body.style.cursor = '';
  document.body.style.userSelect = '';
  
  // 拖动结束后刷新预览，确保内容正确显示
  refreshPreview();
}

// 切换预览全屏
function toggleFullscreen() {
  isPreviewFullscreen.value = !isPreviewFullscreen.value;
  nextTick(() => {
    refreshPreview();
  });
}

// 检测当前是否为暗色主题
function isDarkTheme() {
  return document.documentElement.classList.contains('dark');
}

// 更新预览
watch(() => props.code, async (newCode) => {
  if (newCode && props.visible) {
    await nextTick();
    updatePreview(newCode);
  }
}, { immediate: true });

// 在模态框显示时更新预览
watch(() => props.visible, async (isVisible) => {
  if (isVisible && props.code) {
    await nextTick();
    updatePreview(props.code);
  }
});

// 处理 ESC 键按下事件
function handleKeyDown(e) {
  if (e.key === 'Escape' && props.visible) {
    close();
  }
}

// 添加键盘监听
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
  
  // 初始化DOM引用
  nextTick(() => {
    initDomRefs();
    
    // 设置初始CSS变量
    if (modalContainer) {
      modalContainer.style.setProperty('--code-panel-width', '50%');
      modalContainer.style.setProperty('--preview-panel-width', '50%');
    }
  });
});

// 移除键盘监听和其他事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('mousemove', resizePanel, { capture: true });
  document.removeEventListener('mouseup', stopResize, { capture: true });
  
  // 清理动画帧
  if (rafId) {
    cancelAnimationFrame(rafId);
  }
});

// 更新预览内容
function updatePreview(html) {
  if (!previewIframe.value) return;
  
  try {
    const iframe = previewIframe.value;
    const isDark = isDarkTheme();
    
    // 创建适当的HTML文档结构
    const content = `<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML预览</title>
    <style>
      /* 隔离样式 */
      :root {
        --preview-bg: ${isDark ? '#1a1a1a' : '#ffffff'};
        --preview-text: ${isDark ? '#e0e0e0' : '#333333'};
        --preview-code-bg: ${isDark ? '#2a2a2a' : '#f5f5f5'};
        --preview-border: ${isDark ? '#333' : '#dcdfe6'};
      }
      
      html, body {
        margin: 0;
        padding: 0;
        background-color: var(--preview-bg);
        color: var(--preview-text);

        line-height: 1.6;
        font-size: 14px;
      }
      
      body {
        padding: 20px;
      }
      
      * {
        box-sizing: border-box;
      }
      
      /* 基础样式 */
      img { max-width: 100%; height: auto; border-radius: 4px; }
      pre, code { 

        background-color: var(--preview-code-bg);
        border-radius: 3px;
      }
      pre { padding: 16px; overflow: auto; }
      code { padding: 2px 4px; }
      pre code { padding: 0; }
      
      a { color: #409EFF; }
      h1, h2, h3, h4, h5, h6 { margin-top: 1.5em; margin-bottom: 0.5em; }
      p, ul, ol { margin-bottom: 1em; }
      table { width: 100%; border-collapse: collapse; }
      th, td { border: 1px solid var(--preview-border); padding: 8px; }
    </style>
  </head>
  <body>${html}</body>
</html>`;
    
    // 使用 srcdoc 属性直接设置内容，避免 blob URL
    iframe.srcdoc = content;
  } catch (error) {
    console.error('预览更新失败:', error);
    
    // 简化的错误显示
    const errorHTML = `
      <div style="color: red; padding: 20px; background: ${isDarkTheme() ? '#1a1a1a' : '#fff'};">
        <h3>预览加载失败</h3>
        <p>${error.message || '未知错误'}</p>
      </div>
    `;
    previewIframe.value.srcdoc = errorHTML;
  }
}

// 刷新预览
function refreshPreview() {
  updatePreview(props.code);
}

// 关闭模态框
function close() {
  // 退出全屏状态
  isPreviewFullscreen.value = false;
  emit('update:visible', false);
}
</script>

<style scoped>
/* 基础样式 */
.code-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-container {
  position: relative;
  width: 95%;
  height: 95%;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 2001;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  background-color: var(--el-bg-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* CSS变量用于面板宽度 */
  --code-panel-width: 50%;
  --preview-panel-width: 50%;
}

.modal-container.preview-fullscreen .preview-panel {
  width: 100%;
  border-radius: 0;
}

.modal-container.preview-fullscreen .code-panel {
  width: 0;
  opacity: 0;
}

.modal-header {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: var(--el-bg-color-overlay);
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-header .el-button {
  transition: all 0.3s ease;
}

.refresh-btn {
  background-color: var(--el-fill-color-lighter);
  box-shadow: 
    2px 2px 5px rgba(0, 0, 0, 0.05),
    -2px -2px 5px rgba(255, 255, 255, 0.05);
}

.close-btn {
  color: var(--el-color-danger);
  background-color: var(--el-fill-color-lighter);
  box-shadow: 
    2px 2px 5px rgba(0, 0, 0, 0.05),
    -2px -2px 5px rgba(255, 255, 255, 0.05);
}

.refresh-btn:hover, .close-btn:hover {
  transform: translateY(-2px);
  box-shadow: 
    3px 3px 8px rgba(0, 0, 0, 0.1),
    -3px -3px 8px rgba(255, 255, 255, 0.1);
}

.modal-body {
  flex: 1;
  display: flex;
  overflow: hidden;
  background-color: var(--el-bg-color);
  position: relative;
}

/* 拖动分隔线样式 */
.panel-resizer {
  width: 16px; /* 增加宽度，便于点击 */
  margin: 0 -8px; /* 使用负边距将实际点击区域扩大，但不占用布局空间 */
  background-color: transparent;
  cursor: col-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100; /* 提高z-index确保在最上层 */
  position: relative; /* 确保z-index生效 */
  flex-shrink: 0; /* 确保不会被压缩 */
}

.panel-resizer:hover,
.panel-resizer:active {
  background-color: var(--el-color-primary-light-8);
}

.resizer-handle {
  width: 4px;
  height: 40px; /* 增加高度使其更容易看到和点击 */
  background-color: var(--el-border-color);
  border-radius: 2px;
  transition: background-color 0.3s, height 0.3s;
  position: relative;
}

.panel-resizer:hover .resizer-handle,
.panel-resizer:active .resizer-handle,
:deep(body.resizing) .resizer-handle {
  background-color: var(--el-color-primary);
  height: 60px;
}

.code-panel, .preview-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.code-panel {
  border-right: 1px solid var(--el-border-color-lighter);
  min-width: 100px; /* 防止拖动时完全消失 */
  width: var(--code-panel-width); /* 使用CSS变量 */
}

.preview-panel {
  min-width: 100px; /* 防止拖动时完全消失 */
  width: var(--preview-panel-width); /* 使用CSS变量 */
}

.code-header, .preview-header {
  padding: 10px 16px;
  background-color: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.code-header h4, .preview-header h4 {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.code-header h4 .el-icon, .preview-header h4 .el-icon {
  font-size: 16px;
  color: var(--el-color-primary);
}

.preview-controls {
  display: flex;
  gap: 4px;
}

.control-btn {
  font-size: 14px;
  transition: all 0.2s ease;
}

.control-btn:hover {
  color: var(--el-color-primary);
  transform: rotate(15deg);
}

.code-content {
  flex: 1;
  overflow: auto;
  margin: 0;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  background-color: var(--el-bg-color-page);
  white-space: pre-wrap;
  word-break: break-all;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 0 8px;
  transition: all 0.3s ease;
}

.preview-content {
  flex: 1;
  overflow: auto;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 8px 0;
  transition: all 0.3s ease;
  position: relative;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
  transition: all 0.3s ease;
}

@media (max-width: 768px) {
  .modal-body {
    flex-direction: column;
  }
  
  .code-panel, .preview-panel {
    width: 100% !important; /* 覆盖内联样式 */
    height: 50%;
  }
  
  .panel-resizer {
    width: 100%;
    height: 16px; /* 增加高度，便于点击 */
    margin: -8px 0; /* 使用负边距扩大点击区域 */
    cursor: row-resize;
  }
  
  .resizer-handle {
    width: 40px;
    height: 4px;
  }
  
  .panel-resizer:hover .resizer-handle,
  .panel-resizer:active .resizer-handle,
  :deep(body.resizing) .resizer-handle {
    width: 60px;
    height: 4px;
  }
  
  .code-panel {
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .code-content {
    border-radius: 0;
  }
  
  .preview-content {
    border-radius: 0 0 8px 8px;
  }
}

.code-panel.hidden {
  display: none;
}

.preview-panel.fullscreen {
  width: 100%;
}

/* 移除拖动时的过渡动画，确保立即响应 */
body.resizing .code-panel,
body.resizing .preview-panel {
  transition: none !important;
}

/* 移除可能导致延迟的过渡效果 */
.code-panel, .preview-panel {
  transition: width 0.1s linear; /* 减少过渡时间，使用线性缓动 */
}
</style>

<!-- 暗色主题特定样式，使用全局选择器确保能覆盖Element Plus的暗色主题 -->
<style>
/* 黑色主题下的样式调整 */
html.dark .code-preview-modal .modal-container {
  background-color: #141414;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2);
}

html.dark .code-preview-modal .panel-resizer:hover,
html.dark .code-preview-modal .panel-resizer:active {
  background-color: rgba(64, 158, 255, 0.2);
}

html.dark .code-preview-modal .resizer-handle {
  background-color: #333;
}

html.dark .code-preview-modal .panel-resizer:hover .resizer-handle,
html.dark .code-preview-modal .panel-resizer:active .resizer-handle {
  background-color: var(--el-color-primary);
}

html.dark .code-preview-modal .modal-header {
  background-color: #1d1d1d;
  border-bottom-color: #2c2c2c;
}

html.dark .code-preview-modal .refresh-btn,
html.dark .code-preview-modal .close-btn {
  background-color: #2c2c2c;
  box-shadow: 
    2px 2px 5px rgba(0, 0, 0, 0.2),
    -2px -2px 5px rgba(255, 255, 255, 0.02);
}

html.dark .code-preview-modal .refresh-btn:hover,
html.dark .code-preview-modal .close-btn:hover {
  background-color: #333;
  box-shadow: 
    3px 3px 8px rgba(0, 0, 0, 0.25),
    -3px -3px 8px rgba(255, 255, 255, 0.03);
}

html.dark .code-preview-modal .code-header, 
html.dark .code-preview-modal .preview-header {
  background-color: #1d1d1d;
  border-bottom-color: #2c2c2c;
}

html.dark .code-preview-modal .code-header h4, 
html.dark .code-preview-modal .preview-header h4 {
  color: #e0e0e0;
}

html.dark .code-preview-modal .code-header h4 .el-icon, 
html.dark .code-preview-modal .preview-header h4 .el-icon {
  color: var(--el-color-primary-light-3);
}

html.dark .code-preview-modal .code-content {
  background-color: #141414;
  color: #e0e0e0;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  border-color: #2c2c2c;
}

html.dark .code-preview-modal .preview-content {
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

html.dark .code-preview-modal .code-panel {
  border-right-color: #2c2c2c;
}

html.dark .code-preview-modal .modal-body {
  background-color: #141414;
}

html.dark .code-preview-modal .control-btn {
  color: #bbb;
}

html.dark .code-preview-modal .control-btn:hover {
  color: var(--el-color-primary-light-3);
}

/* 修改预览iframe的背景色适应主题 */
html.dark .code-preview-modal .preview-iframe {
  background-color: #1a1a1a; /* 暗色主题下的背景色 */
}

/* 修复移动端样式 */
@media (max-width: 768px) {
  html.dark .code-preview-modal .code-panel {
    border-bottom-color: #2c2c2c;
  }
}
</style>

<style>
/* 拖动时添加全局样式 */
body.resizing {
  cursor: col-resize !important;
}

body.resizing * {
  user-select: none !important;
}

@media (max-width: 768px) {
  body.resizing {
    cursor: row-resize !important;
  }
}

/* 在拖动过程中禁用所有过渡效果以提高响应速度 */
body.resizing * {
  transition: none !important;
  animation: none !important;
  user-select: none !important;
}
</style> 