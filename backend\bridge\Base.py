import json
import os


class ResponsePacket:
    def _success_response(self, message, data=None):
        """ 统一成功响应格式 """
        return json.dumps({"status": "success", "message": message, "data": data})

    def _error_response(self, message):
        """ 统一错误响应格式 """
        return json.dumps({"status": "error", "message": message})


class BaseManager(ResponsePacket):
    def __init__(self, base_dir, directory_name):
        self.directory = os.path.join(base_dir, directory_name)
        os.makedirs(self.directory, exist_ok=True)

    def _save_json(self, file_path, data):
        """确保正确保存JSON数据"""
        try:
            # 确保数据是字典类型而非字符串
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except:
                    raise ValueError("无效的JSON字符串")
                
            # 使用原子写入方式避免写入冲突
            with open(file_path + '.tmp', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 原子重命名操作
            if os.path.exists(file_path):
                os.remove(file_path)
            os.rename(file_path + '.tmp', file_path)
        
            return True
        except Exception as e:
            print(f"保存JSON失败: {str(e)}")
            return False

    @staticmethod
    def _load_json(file_path):
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
