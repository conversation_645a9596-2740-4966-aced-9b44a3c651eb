import base64
import functools
import os
import time
import shutil
from PIL import Image
from io import BytesIO
from datetime import datetime, timedelta
import json
import subprocess
import threading
import platform
import urllib.parse
import uuid
import hashlib
import re
import sys

from webview.window import FixPoint

import webview
from .BookController import BookController
from .InjectProjectController import InjectProjectController
from .LocalController import LocalController
from .ModelController import ModelController
from .ProjectController import ProjectController
from .DrssionController import DrssionController
from .ConfigManager import ConfigManager
from .Base import ResponsePacket
from .UserManager import UserManager
from .EdgeTtsController import EdgeTtsController
from .HardwareIdentifier import HardwareIdentifier
from .TimeValidator import get_network_time, has_valid_time, get_time_info, get_network_time_info



def ensure_window(func):
    """装饰器：确保窗口存在"""

    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        if self.window is None:
            self._get_window()
        return func(self, *args, **kwargs)

    return wrapper


class API(ResponsePacket):
    def __init__(self):
        """初始化API控制器"""
        # os.pardir 表示上级目录,如果是编译后就是这个exe的指定当前目录
        self.base_dir = os.path.join(os.path.abspath(os.curdir), "backup")
        config_file = os.path.join(self.base_dir, "config", "settings.json")
        self.background_image_dir = os.path.join(self.base_dir, "backgrounds")
        
        # 初始化所有控制器
        self.project_controller = ProjectController(self.base_dir)
        self.config_controller = ConfigManager(config_file)
        self.drssion_controller = DrssionController(config_file)
        self.book_controller = BookController(self.base_dir)
        self.edge_tts_controller = EdgeTtsController(config_file)
        self.user_manager = UserManager()  # 用户管理器
        # 不再需要 self.time_manager = TimeManager()
        
        # 本地化菜单的功能
        self.local_controller = LocalController(self.base_dir)

        # 安全地获取 OpenAI 配置
        config = self.config_controller.load_config()
        openai_config = config.get('openai', {})
        api_key = openai_config.get('api_key', '')
        base_url = openai_config.get('base_url', '')

        self.model_controller = ModelController(
            api_key=api_key,
            base_url=base_url,
            base_dir=self.base_dir,
        )
        self.window = None
        self.inject_project = InjectProjectController()
        
        # 自动备份相关属性
        self._auto_backup_timer = None
        self._auto_backup_thread = None
        self._last_backup_time = None
        self._backup_running = False
        self._backup_lock = threading.Lock()
        self._stop_backup_event = threading.Event()
        
        # 在初始化时读取配置并启动自动备份（如果启用）
        self._init_auto_backup()

        # 添加背景图片目录路径
        if not os.path.exists(self.background_image_dir):
            os.makedirs(self.background_image_dir)




    # @property
    # def user_manager(self):
    #     """获取用户管理器实例"""
    #     return self._user_manager

    def _get_window(self):
        """获取窗口对象"""
        if self.window is None:
            import webview
            self.window = webview.windows[0] if webview.windows else None
        return self.window

    def get_settings(self):
        """获取应用设置"""
        config = self.config_controller.load_config()
        return self._success_response("设置获取成功", config)

    def save_settings(self, settings):
        """保存应用设置"""
        try:
            # 强制立即保存，不使用延迟机制
            if self.config_controller.save_config(settings):
                return self._success_response("设置保存成功")
            return self._error_response("设置保存失败")
        except Exception as e:
            return self._error_response(f"设置保存出错: {str(e)}")

    def select_directory(self):
        """选择目录"""
        try:
            import webview
            result = webview.windows[0].create_file_dialog(webview.FOLDER_DIALOG)

            if result and len(result) > 0:
                return self._success_response("目录选择成功", result[0])

            return self._error_response("未选择目录")
        except Exception as e:
            return self._error_response(f"选择目录出错: {str(e)}")

    def select_file_path(self):
        """
        打开文件选择对话框
        :param file_types: 文件类型过滤器列表，格式为 [('描述', '*.扩展名'), ...]
        :param allow_multiple: 是否允许多选
        :return: 选中的文件路径（多选时为路径列表）
        """

        try:

            import webview

            result = webview.windows[0].create_file_dialog(webview.OPEN_DIALOG)

            if result is None:
                return self._error_response("未选择文件")

            return self._success_response("文件选择成功", result)
        except Exception as e:
            return self._error_response(f"选择文件出错: {str(e)}")

    # def get_image_base64(self, file_path):
    #     try:
    #         with open(file_path, 'rb') as image_file:
    #             # 读取图片文件并转换为base64
    #             base64_data = base64.b64encode(image_file.read()).decode('utf-8')
    #
    #             return self._success_response("图片转base64成功", f'data:image/jpeg;base64,{base64_data}')
    #     except Exception as e:
    #         return self._error_response(f"图片转base64出错: {str(e)}")

    def optimize_image(self, image, max_size=(1920, 1080), quality=85):
        """
        优化图片大小和质量
        """
        try:
            # 计算新的尺寸，保持宽高比
            width, height = image.size
            ratio = min(max_size[0] / width, max_size[1] / height)
            if ratio < 1:  # 只有当图片大于目标尺寸时才调整
                new_size = (int(width * ratio), int(height * ratio))
                image = image.resize(new_size, Image.Resampling.LANCZOS)
            
            # 如果是RGBA模式（PNG），转换为RGB
            if image.mode == 'RGBA':
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[3])  # 使用alpha通道作为mask
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # 创建内存缓冲区来存储压缩后的图片
            output = BytesIO()
            image.save(output, format='JPEG', quality=quality, optimize=True)
            return output.getvalue()
            
        except Exception as e:
            raise Exception(f"图片优化失败: {str(e)}")

    def get_background_image_data(self, file_path, thumbnail=False):
        """
        获取背景图片的二进制数据并转换为base64
        :param thumbnail: 是否返回缩略图，默认为False
        """
        try:
            if not os.path.exists(file_path):
                return self._error_response("图片文件不存在")

            # 使用Pillow打开图片
            from PIL import Image
            from io import BytesIO
            
            with Image.open(file_path) as img:
                # 如果需要缩略图，生成较小的图片
                if thumbnail:
                    img.thumbnail((300, 200), Image.Resampling.LANCZOS)
                
                # 优化图片
                optimized_data = self.optimize_image(img)
                
                # 转换为base64
                base64_data = base64.b64encode(optimized_data).decode('utf-8')
                return self._success_response("获取图片数据成功", f'data:image/jpeg;base64,{base64_data}')

        except Exception as e:
            return self._error_response(f"获取图片数据失败: {str(e)}")

    def copy_to_background_dir(self, source_path):
        """
        复制图片到背景图片目录，并进行优化
        """
        try:
            # 确保背景图片目录存在
            if not os.path.exists(self.background_image_dir):
                os.makedirs(self.background_image_dir)

            # 获取原始文件名和扩展名
            file_name = os.path.basename(source_path)
            file_ext = os.path.splitext(file_name)[1].lower()
            
            # 生成唯一的文件名（使用时间戳）
            new_file_name = f"bg_{int(time.time())}.jpg"  # 统一使用jpg格式
            target_path = os.path.join(self.background_image_dir, new_file_name)
            
            # 优化并保存图片
            from PIL import Image
            with Image.open(source_path) as img:
                optimized_data = self.optimize_image(img)
                with open(target_path, 'wb') as f:
                    f.write(optimized_data)
            
            return self._success_response("图片复制成功", target_path)
            
        except Exception as e:
            return self._error_response(f"复制图片失败: {str(e)}")

    def process_background_image(self, file_path):
        """
        处理背景图片：验证图片并复制到背景目录
        """
        try:
            # 验证文件是否存在
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
            
            # 验证是否是图片文件
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                return self._error_response("不支持的图片格式")
            
            # 复制到背景目录
            return self.copy_to_background_dir(file_path)
            
        except Exception as e:
            return self._error_response(f"处理背景图片出错: {str(e)}")

    # 小说规则管理API
    def load_novel_rules(self):
        """加载小说规则"""
        return self.drssion_controller.load_novel_rules()

    def add_novel_rule(self, rule_data):
        """添加或更新小说规则"""
        return self.drssion_controller.add_novel_rule(rule_data)

    def delete_novel_rule(self, rule_id):
        """删除小说规则"""
        return self.drssion_controller.delete_novel_rule(rule_id)

    # 任务管理相关API
    def get_tasks(self):
        """获取所有下载任务"""
        return self.drssion_controller.get_tasks()

    def stop_task(self, task_id):
        """停止下载任务"""
        return self.drssion_controller.stop_task(task_id)

    def create_download_task(self, task_config):
        """创建下载任务"""
        return self.drssion_controller.create_download_task(task_config)

    # AI角色管理接口
    def get_ai_roles(self):
        """获取所有AI角色"""
        return self.model_controller.get_ai_roles()

    def add_ai_role(self, role_data):
        """添加新的AI角色"""
        return self.model_controller.add_ai_role(role_data)

    def update_ai_role(self, role_id, role_data):
        """更新AI角色"""
        return self.model_controller.update_ai_role(role_id, role_data)

    def delete_ai_role(self, role_id):
        """删除AI角色"""
        return self.model_controller.delete_ai_role(role_id)

    def open_directory(self, path):
        """打开指定的目录"""
        try:
            if not os.path.exists(path):
                return self._error_response(f"目录不存在: {path}")
            
            if not os.path.isdir(path):
                return self._error_response(f"指定的路径不是目录: {path}")
            
            # 规范化路径，避免跨平台问题
            path = os.path.normpath(path)
                
            # 根据不同操作系统使用不同命令打开目录
            system = platform.system()
            if system == "Windows":
                # Windows 使用 explorer
                try:
                    os.startfile(path)  # 推荐使用，不会显示控制台窗口
                except AttributeError:
                    # 如果os.startfile不可用，回退到subprocess
                    subprocess.Popen(["explorer", path])
            elif system == "Darwin":
                # macOS 使用 open
                subprocess.Popen(["open", path])
            else:
                # Linux 使用 xdg-open 或尝试多种文件管理器
                success = False
                file_managers = ["xdg-open", "gio", "gvfs-open", "nautilus", "thunar", "pcmanfm"]
                
                for manager in file_managers:
                    try:
                        subprocess.Popen([manager, path])
                        success = True
                        break
                    except FileNotFoundError:
                        continue
                
                if not success:
                    return self._error_response("找不到可用的文件管理器，请安装xdg-open或其他文件管理器")
                
            return self._success_response(f"已打开目录: {path}")
        except Exception as e:
            return self._error_response(f"打开目录失败: {str(e)}")
    
    def list_directory(self, path):
        """列出目录内容"""
        try:
            if not os.path.exists(path):
                return self._error_response(f"目录不存在: {path}")
            
            if not os.path.isdir(path):
                return self._error_response(f"指定的路径不是目录: {path}")
            
            # 获取目录内容
            items = []
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                is_dir = os.path.isdir(item_path)
                size = os.path.getsize(item_path) if not is_dir else 0
                mtime = os.path.getmtime(item_path)
                
                items.append({
                    "name": item,
                    "path": item_path,
                    "is_directory": is_dir,
                    "size": size,
                    "modified_time": mtime
                })
            
            return self._success_response("获取目录内容成功", items)
        except Exception as e:
            return self._error_response(f"获取目录内容失败: {str(e)}")
    
    def copy_file(self, src_path, dst_path):
        """复制文件到指定位置"""
        try:
            if not os.path.exists(src_path):
                return self._error_response(f"源文件不存在: {src_path}")
            
            # 创建目标目录（如果不存在）
            dst_dir = os.path.dirname(dst_path)
            if not os.path.exists(dst_dir):
                os.makedirs(dst_dir, exist_ok=True)
            
            # 复制文件
            if os.path.isdir(src_path):
                shutil.copytree(src_path, dst_path)
            else:
                shutil.copy2(src_path, dst_path)
            
            return self._success_response(f"文件复制成功: {src_path} -> {dst_path}")
        except Exception as e:
            return self._error_response(f"复制文件失败: {str(e)}")
    
    def rename_file(self, src_path, dst_path):
        """重命名文件或目录"""
        try:
            if not os.path.exists(src_path):
                return self._error_response(f"源文件不存在: {src_path}")
            
            if os.path.exists(dst_path):
                return self._error_response(f"目标文件已存在: {dst_path}")
            
            # 重命名文件
            os.rename(src_path, dst_path)
            
            return self._success_response(f"文件重命名成功: {src_path} -> {dst_path}")
        except Exception as e:
            return self._error_response(f"重命名文件失败: {str(e)}")
    
    def delete_file(self, path):
        """删除文件或目录"""
        try:
            if not os.path.exists(path):
                return self._error_response(f"文件不存在: {path}")
            
            # 删除文件或目录
            if os.path.isdir(path):
                shutil.rmtree(path)
            else:
                os.remove(path)
            
            return self._success_response(f"文件删除成功: {path}")
        except Exception as e:
            return self._error_response(f"删除文件失败: {str(e)}")

    def create_directory(self, path):
        """创建新目录"""
        try:
            if os.path.exists(path):
                return self._error_response(f"目录已存在: {path}")
            
            # 创建目录
            os.makedirs(path, exist_ok=True)
            
            return self._success_response(f"目录创建成功: {path}")
        except Exception as e:
            return self._error_response(f"创建目录失败: {str(e)}")

    def get_file_info(self, path):
        """获取文件或目录信息"""
        try:
            if not os.path.exists(path):
                return self._error_response(f"文件不存在: {path}")

            # 获取文件信息
            is_dir = os.path.isdir(path)
            size = os.path.getsize(path) if not is_dir else 0
            mtime = os.path.getmtime(path)
            atime = os.path.getatime(path)
            ctime = os.path.getctime(path)

            info = {
                "name": os.path.basename(path),
                "path": path,
                "is_directory": is_dir,
                "size": size,
                "modified_time": mtime,
                "accessed_time": atime,
                "created_time": ctime
            }

            return self._success_response("获取文件信息成功", info)
        except Exception as e:
            return self._error_response(f"获取文件信息失败: {str(e)}")

    def read_file(self, path):
        """读取文件内容"""
        try:
            if not os.path.exists(path):
                return self._error_response(f"文件不存在: {path}")

            if os.path.isdir(path):
                return self._error_response(f"指定的路径是目录，不是文件: {path}")

            # 读取文件内容
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()

            return self._success_response("读取文件成功", content)
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(path, 'r', encoding='gbk') as f:
                    content = f.read()
                return self._success_response("读取文件成功", content)
            except Exception as e:
                return self._error_response(f"文件编码不支持: {str(e)}")
        except Exception as e:
            return self._error_response(f"读取文件失败: {str(e)}")

    def write_file(self, path, content):
        """写入文件内容"""
        try:
            # 确保目录存在
            directory = os.path.dirname(path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)

            # 写入文件
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)

            return self._success_response(f"文件写入成功: {path}")
        except Exception as e:
            return self._error_response(f"写入文件失败: {str(e)}")

    # 备份相关方法
    def check_auto_backup(self):
        """检查并启动自动备份"""
        try:
            # 加载配置
            config = self.config_controller.load_config()
            backup_config = config.get('backup', {})
            
            # 如果没有启用自动备份，停止现有的定时器
            if not backup_config.get('autoBackup', False):
                self.stop_auto_backup()
                print("自动备份未启用，已停止定时器")
                return self._success_response("自动备份未启用")
            
            # 验证必要的配置
            if not backup_config.get('backupDir') or not backup_config.get('targetDir'):
                self.stop_auto_backup()
                print("备份目录未配置，无法启动自动备份")
                return self._error_response("备份目录未配置")
            
            # 启动自动备份
            self._start_auto_backup(backup_config)
            return self._success_response("自动备份已启动")
            
        except Exception as e:
            self.stop_auto_backup()
            print(f"初始化自动备份失败: {str(e)}")
            return self._error_response(f"初始化自动备份失败: {str(e)}")

    def _start_auto_backup(self, backup_config):
        """启动自动备份定时器，优化版本确保精确的时间间隔"""
        import threading
        
        # 停止现有的定时器
        if self._auto_backup_timer:
            self._auto_backup_timer.cancel()
            self._auto_backup_timer = None
            
        # 获取备份间隔（分钟）
        interval_minutes = backup_config.get('backupInterval', 30)
        interval_seconds = interval_minutes * 60
        
        def backup_task():
            try:
                # 记录当前任务开始时间
                start_time = time.time()
                print(f"开始执行定时备份任务，时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
                
                # 构建备份配置
                current_config = {
                    'backup_dir': backup_config.get('backupDir'),
                    'target_dir': backup_config.get('targetDir'),
                    'is_auto': True,
                    'use_zip': backup_config.get('useZip', False)
                }
                
                # 执行自动备份
                result = self._auto_backup_data(current_config)
                
                # 计算任务执行时间
                elapsed_time = time.time() - start_time
                print(f"备份结果: {result}, 耗时: {elapsed_time:.2f}秒")
                
                # 计算下一次备份的精确时间(考虑任务执行时间)
                next_interval = max(1, interval_seconds - elapsed_time)
                print(f"下一次备份将在 {next_interval/60:.2f} 分钟后执行 (预定间隔: {interval_minutes}分钟)")
                
                # 设置下一次备份
                self._auto_backup_timer = threading.Timer(
                    next_interval,
                    backup_task
                )
                self._auto_backup_timer.daemon = True
                self._auto_backup_timer.start()
                
            except Exception as e:
                print(f"自动备份任务执行失败: {str(e)}")
                # 即使失败，也要设置下一次备份
                self._auto_backup_timer = threading.Timer(
                    interval_seconds,
                    backup_task
                )
                self._auto_backup_timer.daemon = True
                self._auto_backup_timer.start()
                print(f"设置了下一次备份，将在 {interval_minutes} 分钟后尝试")
        
        # 计算首次执行时间
        last_backup_time = backup_config.get('lastBackupTime', 0)
        current_time = int(time.time())
        
        # 检查上次备份时间，计算下一次应执行时间
        time_since_last_backup = current_time - last_backup_time
        
        if time_since_last_backup >= interval_seconds:
            # 如果距离上次备份时间超过间隔，立即执行
            print(f"距离上次备份已经过去了 {time_since_last_backup/60:.2f} 分钟，立即执行备份")
            self._auto_backup_timer = threading.Timer(1, backup_task)  # 1秒后执行，避免阻塞当前线程
        else:
            # 计算精确的延迟时间
            next_backup_delay = interval_seconds - time_since_last_backup
            formatted_time = time.strftime(
                "%Y-%m-%d %H:%M:%S", 
                time.localtime(current_time + next_backup_delay)
            )
            print(f"距离上次备份时间 {time_since_last_backup/60:.2f} 分钟，将在 {next_backup_delay/60:.2f} 分钟后执行下一次备份")
            print(f"预计备份时间: {formatted_time}")
            self._auto_backup_timer = threading.Timer(
                next_backup_delay,
                backup_task
            )
        
        self._auto_backup_timer.daemon = True
        self._auto_backup_timer.start()

    def _auto_backup_data(self, backup_config):
        """
        执行自动备份，不包含进度显示等交互逻辑
        :param backup_config: 包含备份配置的字典
            - backup_dir: 备份源目录
            - target_dir: 备份目标目录
        """
        try:
            backup_dir = backup_config.get('backup_dir')
            target_dir = backup_config.get('target_dir')
            
            if not os.path.exists(backup_dir):
                return self._error_response("备份源目录不存在")
                
            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)
            
            # 生成备份文件夹名称
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_folder = os.path.join(target_dir, f"backup_{timestamp}")
            
            # 执行备份
            shutil.copytree(backup_dir, backup_folder)
            
            # 创建备份信息文件
            backup_info = {
                "type": "auto",
                "time": int(time.time()),
                "source": backup_dir
            }
            # 写入备份信息文件
            info_path = os.path.join(backup_folder, "backup_info.json")
            # 使用原子写入方式
            temp_info_path = info_path + ".tmp"
            with open(temp_info_path, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)

            # 原子重命名操作
            if os.path.exists(info_path):
                os.remove(info_path)
            os.rename(temp_info_path, info_path)
            
            # 更新最后备份时间
            config = self.config_controller.load_config()
            config['backup']['lastBackupTime'] = int(time.time())
            self.config_controller.save_config(config)
            
            # 检查并清理旧备份
            keep_count = config.get('backup', {}).get('keepBackups', 5)
            self._cleanup_old_backups(keep_count)
            
            return self._success_response("自动备份完成")
            
        except Exception as e:
            error_msg = f"自动备份失败: {str(e)}"
            print(error_msg)
            return self._error_response(error_msg)

    def stop_auto_backup(self):
        """停止自动备份"""
        if self._auto_backup_timer:
            self._auto_backup_timer.cancel()
            self._auto_backup_timer = None

    def backup_now(self, backup_config):
        """执行数据备份"""
        try:
            if not backup_config:
                return self._error_response("备份配置不能为空")
                
            if not backup_config.get('backup_dir') or not backup_config.get('target_dir'):
                return self._error_response("备份目录未指定")
                
            return self.backup_data(backup_config)
        except Exception as e:
            error_msg = f"执行备份失败: {str(e)}"
            print(error_msg)
            return self._error_response(error_msg)

    @ensure_window
    def backup_data(self, backup_config):
        """
        执行数据备份，支持手动备份。优化版本支持压缩和更高效的备份方式。
        :param backup_config: 包含备份配置的字典
            - backup_dir: 备份源目录
            - target_dir: 备份目标目录
            - is_auto: 是否为自动备份
        """
        try:
            backup_dir = backup_config.get('backup_dir')
            target_dir = backup_config.get('target_dir')
            is_auto = backup_config.get('is_auto', False)
            
            if not backup_dir or not target_dir:
                return self._error_response("备份目录未指定")
            
            if not os.path.exists(backup_dir):
                return self._error_response("备份源目录不存在")
            
            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)
            
            # 生成备份文件名（使用时间戳）
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}"
            backup_path = os.path.join(target_dir, backup_name)

            # 获取源目录的总文件数和大小
            total_files = 0
            total_size = 0
            
            # 需要忽略的文件和目录列表
            ignore_patterns = ['__pycache__', '*.pyc', '*.tmp', '.DS_Store', 'node_modules', '.git']
            
            # 创建忽略函数用于copytree
            def ignore_function(dir, names):
                ignored = set()
                for pattern in ignore_patterns:
                    if pattern.startswith('*'):
                        # 处理扩展名模式
                        ext = pattern[1:]
                        for name in names:
                            if name.endswith(ext):
                                ignored.add(name)
                    else:
                        # 处理确切名称
                        if pattern in names:
                            ignored.add(pattern)
                return ignored
            
            # 计算总大小和文件数（忽略某些文件）
            for root, dirs, files in os.walk(backup_dir):
                # 忽略某些目录
                dirs[:] = [d for d in dirs if d not in ['__pycache__', 'node_modules', '.git']]  # 移除.git从忽略列表
                
                for file in files:
                    # 忽略某些文件
                    if any(file.endswith(ext[1:]) for ext in ignore_patterns if ext.startswith('*')):
                        continue
                    if any(file == pattern for pattern in ignore_patterns if not pattern.startswith('*')):
                        continue
                    
                    file_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(file_path)
                        total_size += file_size
                        total_files += 1
                    except (OSError, IOError) as e:
                        print(f"跳过文件 {file_path}: {str(e)}")
                        continue

            # 初始化进度
            processed_files = 0
            processed_size = 0

            # 定义进度更新函数
            def update_progress(current_file="", percent_override=None):
                nonlocal processed_files, processed_size
                if self.window:  # 只在有窗口时更新进度
                    if percent_override is not None:
                        percent = percent_override
                    else:
                        percent = int((processed_size / total_size) * 100) if total_size > 0 else 0
                    progress_data = {
                        "percent": percent,
                        "message": f"正在备份: {current_file}",
                        "status": "normal"
                    }
                    js_code = f'window.receiveBackupProgress({json.dumps(progress_data)})'
                    self.window.evaluate_js(js_code)
                    time.sleep(0.05)  # 减小延迟以提高性能

            # 发送开始消息
            update_progress("准备开始备份...", 0)

            try:
                # 创建目标目录
                os.makedirs(backup_path, exist_ok=True)
                
                # 选择使用压缩模式还是目录复制模式
                use_zip = backup_config.get('use_zip', False)  # 可由前端控制是否使用压缩模式
                
                if use_zip:
                    # 压缩模式 - 创建ZIP文件
                    import zipfile
                    
                    zip_path = f"{backup_path}.zip"
                    update_progress("正在创建备份压缩文件...", 5)
                    
                    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        for root, dirs, files in os.walk(backup_dir):
                            # 应用相同的忽略规则
                            dirs[:] = [d for d in dirs if d not in ['__pycache__', 'node_modules', '.git']]
                            
                            for file in files:
                                # 忽略特定文件
                                if any(file.endswith(ext[1:]) for ext in ignore_patterns if ext.startswith('*')):
                                    continue
                                if any(file == pattern for pattern in ignore_patterns if not pattern.startswith('*')):
                                    continue
                                
                                file_path = os.path.join(root, file)
                                try:
                                    # 计算相对路径
                                    rel_path = os.path.relpath(file_path, backup_dir)
                                    
                                    # 更新进度
                                    update_progress(rel_path)
                                    
                                    # 添加到ZIP
                                    zipf.write(file_path, rel_path)
                                    
                                    # 更新进度计数
                                    processed_files += 1
                                    processed_size += os.path.getsize(file_path)
                                except Exception as e:
                                    print(f"添加文件到ZIP时出错 {file_path}: {str(e)}")
                                    continue
                    
                    # 使用ZIP文件路径作为备份路径
                    os.makedirs(backup_path, exist_ok=True)  # 确保目录存在用于存放备份信息
                    
                else:
                    # 目录复制模式 - 使用改进的方法
                    for root, dirs, files in os.walk(backup_dir):
                        # 应用忽略规则
                        dirs[:] = [d for d in dirs if d not in ['__pycache__', 'node_modules', '.git']]
                        
                        # 计算相对路径
                        rel_path = os.path.relpath(root, backup_dir)
                        target_root = os.path.join(backup_path, rel_path)
                        
                        # 创建目标目录
                        os.makedirs(target_root, exist_ok=True)
                        
                        # 复制文件
                        for file in files:
                            # 应用文件忽略规则
                            if any(file.endswith(ext[1:]) for ext in ignore_patterns if ext.startswith('*')):
                                continue
                            if any(file == pattern for pattern in ignore_patterns if not pattern.startswith('*')):
                                continue
                            
                            src_file = os.path.join(root, file)
                            dst_file = os.path.join(target_root, file)
                            
                            try:
                                # 更新进度
                                update_progress(file)
                                
                                # 使用更高效的复制方法
                                shutil.copy2(src_file, dst_file)
                                
                                # 更新进度计数
                                processed_files += 1
                                processed_size += os.path.getsize(src_file)
                            except (OSError, IOError) as e:
                                print(f"复制文件失败 {src_file}: {str(e)}")
                                continue

                # 创建备份信息文件
                backup_info = {
                    "type": "auto" if is_auto else "manual",
                    "time": int(time.time()),
                    "source": backup_dir,
                    "compressed": use_zip
                }
                
                # 写入备份信息文件
                info_path = os.path.join(backup_path if not use_zip else os.path.dirname(zip_path), "backup_info.json")
                # 使用原子写入方式
                temp_info_path = info_path + ".tmp"
                with open(temp_info_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_info, f, ensure_ascii=False, indent=2)

                # 原子重命名操作
                if os.path.exists(info_path):
                    os.remove(info_path)
                os.rename(temp_info_path, info_path)

                # 更新最后备份时间
                config = self.config_controller.load_config()
                if 'backup' not in config:
                    config['backup'] = {}
                config['backup']['lastBackupTime'] = int(time.time())
                self.config_controller.save_config(config)

                # 检查并清理旧备份
                keep_count = config.get('backup', {}).get('keepBackups', 5)
                self._cleanup_old_backups(keep_count)

                # 发送完成消息
                if self.window:
                    progress_data = {
                        "percent": 100,
                        "message": "备份完成",
                        "status": "success",
                        "path": zip_path if use_zip else backup_path
                    }
                    js_code = f'window.receiveBackupProgress({json.dumps(progress_data)})'
                    self.window.evaluate_js(js_code)
                
                result_path = zip_path if use_zip else backup_path
                return self._success_response("备份成功", {
                    "path": result_path,
                    "timestamp": timestamp,
                    "type": "auto" if is_auto else "manual"
                })
                
            except Exception as e:
                # 发送错误消息
                if self.window:
                    progress_data = {
                        "percent": 0,
                        "message": f"备份失败: {str(e)}",
                        "status": "exception"
                    }
                    js_code = f'window.receiveBackupProgress({json.dumps(progress_data)})'
                    self.window.evaluate_js(js_code)
                raise e
            
        except Exception as e:
            # 发送错误消息
            if self.window:
                progress_data = {
                    "percent": 0,
                    "message": f"备份失败: {str(e)}",
                    "status": "exception"
                }
                js_code = f'window.receiveBackupProgress({json.dumps(progress_data)})'
                self.window.evaluate_js(js_code)
            return self._error_response(f"备份失败: {str(e)}")

    def get_backup_history(self, backup_config):
        """
        获取备份历史记录
        :param backup_config: 备份配置
            - target_dir: 备份目标目录
        :return: 备份历史列表，按时间倒序排序
        """
        try:
            target_dir = backup_config.get('target_dir')
            if not target_dir or not os.path.exists(target_dir):
                return self._error_response("备份目录不存在")

            backup_list = []
            for item in os.listdir(target_dir):
                item_path = os.path.join(target_dir, item)
                if os.path.isdir(item_path) and item.startswith("backup_"):
                    try:
                        # 从文件夹名称获取时间戳
                        # backup_20231227_123456 格式
                        timestamp_str = item[7:]  # 去掉 "backup_" 前缀
                        timestamp = time.mktime(time.strptime(timestamp_str, "%Y%m%d_%H%M%S"))
                        
                        # 获取备份类型（从备份文件夹中的标记文件或配置）
                        backup_type = "manual"  # 默认为手动备份
                        backup_info_file = os.path.join(item_path, "backup_info.json")
                        if os.path.exists(backup_info_file):
                            try:
                                with open(backup_info_file, 'r', encoding='utf-8') as f:
                                    info = json.load(f)
                                    backup_type = info.get('type', 'manual')
                            except:
                                pass

                        # 计算文件夹大小
                        total_size = 0
                        for root, dirs, files in os.walk(item_path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                total_size += os.path.getsize(file_path)

                        backup_list.append({
                            "name": item,
                            "path": item_path,
                            "time": int(timestamp),  # 确保时间戳是整数
                            "size": total_size,
                            "type": backup_type
                        })
                        
                        # print(f"处理备份记录: {item}, 时间戳: {timestamp}, 类型: {backup_type}")  # 添加调试日志
                        
                    except Exception as e:
                        # print(f"处理备份记录时出错 {item_path}: {str(e)}")
                        continue

            # 按时间戳倒序排序
            backup_list.sort(key=lambda x: x["time"], reverse=True)
            
            print(f"返回备份列表: {backup_list}")  # 添加调试日志
            return self._success_response("获取成功", backup_list)

        except Exception as e:
            error_msg = f"获取备份历史失败: {str(e)}"
            print(error_msg)  # 添加调试日志
            return self._error_response(error_msg)

    def restore_backup(self, backup_config):
        """
        从备份恢复数据
        :param backup_config: 备份配置
            - backup_path: 备份文件路径
        """
        try:
            backup_path = backup_config.get('backup_path')
            if not backup_path or not os.path.exists(backup_path):
                print(f"备份文件不存在: {backup_path}")
                return self._error_response("备份文件不存在")
            
            # 检查是否是ZIP备份
            is_zip_backup = False
            if backup_path.endswith('.zip'):
                is_zip_backup = True
                # 如果是ZIP备份，使用目录名
                backup_dir = os.path.splitext(backup_path)[0]
            else:
                backup_dir = backup_path
                # 检查可能相关的ZIP文件
                zip_path = f"{backup_path}.zip"
                if os.path.exists(zip_path):
                    is_zip_backup = True
                    backup_path = zip_path
            
            # 获取原始备份目录
            config = self.config_controller.load_config()
            original_dir = config.get('backup', {}).get('backupDir')
            
            if not original_dir:
                print("未找到原始备份目录配置")
                return self._error_response("未找到原始备份目录配置")
            
            print(f"开始从 {backup_path} 恢复到 {original_dir}")
            
            # 如果原始目录存在，先重命名为临时备份
            temp_backup = None
            if os.path.exists(original_dir):
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                temp_backup = f"{original_dir}_temp_{timestamp}"
                print(f"重命名原始目录 {original_dir} 为临时目录 {temp_backup}")
                shutil.move(original_dir, temp_backup)
            
            try:
                # 确保目标目录的父目录存在
                os.makedirs(os.path.dirname(original_dir), exist_ok=True)
                
                # 根据备份类型执行恢复
                if is_zip_backup:
                    print(f"正在从ZIP备份 {backup_path} 恢复...")
                    # 解压ZIP文件到原始目录
                    import zipfile
                    
                    # 创建目标目录
                    os.makedirs(original_dir, exist_ok=True)
                    
                    with zipfile.ZipFile(backup_path, 'r') as zip_ref:
                        # 获取压缩包中所有文件列表
                        files_to_extract = [f for f in zip_ref.namelist() 
                                           if not f.startswith('.git/') and not f == '.git']
                        # 只解压非Git相关文件
                        for file in files_to_extract:
                            try:
                                zip_ref.extract(file, original_dir)
                            except Exception as extract_error:
                                print(f"解压文件 {file} 失败: {str(extract_error)}")
                    print("ZIP文件解压完成")
                else:
                    print(f"正在从目录备份 {backup_dir} 恢复...")
                    # 直接复制目录，但排除.git相关文件
                    os.makedirs(original_dir, exist_ok=True)
                    
                    # 定义忽略函数，排除.git文件夹和相关文件
                    def ignore_git_files(dir, files):
                        return [f for f in files if f == '.git' or 
                                (os.path.isdir(os.path.join(dir, f)) and f == '.git')]
                    
                    # 使用自定义忽略函数复制目录
                    for item in os.listdir(backup_dir):
                        src = os.path.join(backup_dir, item)
                        dst = os.path.join(original_dir, item)
                        
                        if item == '.git':
                            print(f"跳过Git目录: {src}")
                            continue
                            
                        try:
                            if os.path.isdir(src):
                                shutil.copytree(src, dst, ignore=ignore_git_files)
                            else:
                                shutil.copy2(src, dst)
                        except Exception as copy_error:
                            print(f"复制 {src} 到 {dst} 失败: {str(copy_error)}")
                    
                    print("目录复制完成")
                
                # 恢复成功后删除临时备份
                if temp_backup and os.path.exists(temp_backup):
                    print(f"恢复成功，删除临时备份 {temp_backup}")
                    # 安全删除临时备份目录，避免权限问题
                    try:
                        shutil.rmtree(temp_backup)
                    except PermissionError:
                        print(f"无法删除临时备份，可能有文件被占用: {temp_backup}")
                        # 尝试使用系统命令强制删除
                        if os.name == 'nt':  # Windows系统
                            try:
                                os.system(f'rd /s /q "{temp_backup}"')
                            except:
                                print(f"强制删除失败，请手动删除目录: {temp_backup}")
                
                return self._success_response("恢复成功")
                
            except Exception as e:
                print(f"恢复过程出错: {str(e)}")
                # 如果恢复过程出错，尝试回滚
                if os.path.exists(original_dir):
                    print(f"删除不完整的恢复目录 {original_dir}")
                    shutil.rmtree(original_dir)
                if temp_backup and os.path.exists(temp_backup):
                    print(f"回滚：恢复临时备份 {temp_backup} 到 {original_dir}")
                    shutil.move(temp_backup, original_dir)
                raise e
            
        except Exception as e:
            error_msg = f"恢复失败: {str(e)}"
            print(error_msg)
            return self._error_response(error_msg)

    def delete_backup(self, backup_config):
        """
        删除指定的备份
        :param backup_config: 备份配置
            - backup_path: 备份文件路径
        """
        try:
            backup_path = backup_config.get('backup_path')
            if not backup_path or not os.path.exists(backup_path):
                return self._error_response("备份文件不存在")
            
            # 记录操作
            print(f"尝试删除备份: {backup_path}")
            
            # 检查是否包含 .git 目录
            git_dir = os.path.join(backup_path, '.git')
            has_git = os.path.exists(git_dir) and os.path.isdir(git_dir)
            
            if has_git:
                print(f"检测到 .git 目录，使用特殊处理")
                
            # 尝试处理可能的特殊情况（权限拒绝）
            try:
                # 如果包含 .git 目录，先尝试删除 .git 目录内的特殊文件
                if has_git:
                    self._safely_delete_git_dir(git_dir)
                
                # 再尝试用普通方法删除整个目录
                if os.path.isdir(backup_path):
                    shutil.rmtree(backup_path)
                else:
                    os.remove(backup_path)
                    
            except PermissionError as pe:
                print(f"遇到权限问题: {str(pe)}，尝试强制删除")
                
                # 强制删除逻辑
                if os.path.isdir(backup_path):
                    # 1. 尝试修改权限
                    try:
                        for root, dirs, files in os.walk(backup_path, topdown=False):
                            for name in files:
                                try:
                                    file_path = os.path.join(root, name)
                                    os.chmod(file_path, 0o666)
                                except:
                                    pass
                            for name in dirs:
                                try:
                                    dir_path = os.path.join(root, name)
                                    os.chmod(dir_path, 0o777)
                                except:
                                    pass
                    except:
                        pass
                        
                    # 2. 尝试使用系统命令强制删除，防止弹出命令窗口
                    if os.name == 'nt':  # Windows系统
                        try:
                            import subprocess
                            # 创建无窗口的进程
                            startupinfo = subprocess.STARTUPINFO()
                            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                            startupinfo.wShowWindow = 0  # SW_HIDE
                            
                            # 使用startupinfo参数隐藏窗口
                            subprocess.run(
                                ['cmd', '/c', 'rd', '/s', '/q', backup_path], 
                                capture_output=True, 
                                check=False, 
                                timeout=30,
                                startupinfo=startupinfo,
                                creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
                            )
                        except Exception as cmd_e:
                            print(f"使用系统命令删除失败: {str(cmd_e)}")
                            # 3. 最后尝试忽略错误删除
                            shutil.rmtree(backup_path, ignore_errors=True)
                    else:
                        # 在非Windows系统上尝试使用忽略错误删除
                        shutil.rmtree(backup_path, ignore_errors=True)
                else:
                    # 文件删除
                    try:
                        # 修改文件权限
                        os.chmod(backup_path, 0o666)
                        
                        # 使用系统命令删除，防止弹出命令窗口
                        if os.name == 'nt':
                            import subprocess
                            # 创建无窗口的进程
                            startupinfo = subprocess.STARTUPINFO()
                            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                            startupinfo.wShowWindow = 0  # SW_HIDE
                            
                            subprocess.run(
                                ['cmd', '/c', 'del', '/f', '/q', backup_path],
                                capture_output=True, 
                                check=False, 
                                timeout=10,
                                startupinfo=startupinfo,
                                creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
                            )
                        else:
                            os.remove(backup_path)
                    except Exception as f_e:
                        print(f"删除文件失败: {str(f_e)}")
                        
                # 检查文件是否仍然存在
                if os.path.exists(backup_path):
                    # 最后尝试使用清空目录并删除的方式
                    try:
                        if os.path.isdir(backup_path):
                            # 先清空目录内容
                            for root, dirs, files in os.walk(backup_path, topdown=False):
                                for f in files:
                                    try:
                                        os.unlink(os.path.join(root, f))
                                    except:
                                        pass
                                for d in dirs:
                                    try:
                                        os.rmdir(os.path.join(root, d))
                                    except:
                                        pass
                                        
                            # 最后删除目录本身
                            try:
                                os.rmdir(backup_path)
                            except:
                                pass
                    except Exception as clear_e:
                        print(f"清空目录失败: {str(clear_e)}")
            
            # 检查删除是否成功
            if not os.path.exists(backup_path):
                return self._success_response("备份删除成功")
            else:
                return self._warning_response("备份部分删除，可能有文件无法删除")
            
        except Exception as e:
            error_msg = f"删除备份失败: {str(e)}"
            print(error_msg)
            return self._error_response(error_msg)
            
    def _safely_delete_git_dir(self, git_dir):
        """
        安全地删除Git目录，处理只读文件和锁定文件
        :param git_dir: .git目录路径
        """
        try:
            # 特别处理index.lock文件
            index_lock = os.path.join(git_dir, 'index.lock')
            if os.path.exists(index_lock):
                try:
                    os.chmod(index_lock, 0o666)  # 修改权限
                    os.remove(index_lock)
                    print(f"已删除 Git 索引锁定文件: {index_lock}")
                except:
                    pass
            
            # 处理objects目录下的文件（通常是只读的）
            objects_dir = os.path.join(git_dir, 'objects')
            if os.path.exists(objects_dir):
                for root, dirs, files in os.walk(objects_dir, topdown=False):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            os.chmod(file_path, 0o666)  # 修改为可写
                        except:
                            pass
                    
                    # 尝试更改目录权限
                    for dir_name in dirs:
                        dir_path = os.path.join(root, dir_name)
                        try:
                            os.chmod(dir_path, 0o777)
                        except:
                            pass
                            
            # 处理引用锁定文件
            refs_dir = os.path.join(git_dir, 'refs')
            if os.path.exists(refs_dir):
                for root, dirs, files in os.walk(refs_dir, topdown=False):
                    for file in files:
                        if file.endswith('.lock'):
                            file_path = os.path.join(root, file)
                            try:
                                os.chmod(file_path, 0o666)
                                os.remove(file_path)
                                print(f"已删除引用锁定文件: {file_path}")
                            except:
                                pass
        except Exception as e:
            print(f"处理Git目录时出错: {str(e)}")

    def select_backup_file(self):
        """选择备份文件进行恢复"""
        try:
            import webview
            result = webview.windows[0].create_file_dialog(
                webview.FOLDER_DIALOG,
                title="选择备份文件夹"
            )
            
            if result and len(result) > 0:
                selected_path = result[0]
                # 验证是否是有效的备份文件夹
                if os.path.basename(selected_path).startswith("backup_"):
                    return self._success_response("选择备份文件成功", selected_path)
                else:
                    return self._error_response("请选择有效的备份文件夹")
            
            return self._error_response("未选择备份文件")
            
        except Exception as e:
            return self._error_response(f"选择备份文件失败: {str(e)}")

    def _format_size(self, size):
        """将字节大小转换为人类可读的格式"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    def _cleanup_old_backups(self, keep_count):
        """
        清理旧的备份文件，只保留指定数量的最新备份
        :param keep_count: 要保留的最新备份数量
        """
        try:
            # 获取备份目录
            config = self.config_controller.load_config()
            target_dir = config.get('backup', {}).get('targetDir')
            
            if not target_dir or not os.path.exists(target_dir):
                print(f"备份目录不存在: {target_dir}")
                return
            
            # 分别存储自动备份和手动备份
            auto_backups = []
            manual_backups = []
            
            for item in os.listdir(target_dir):
                item_path = os.path.join(target_dir, item)
                # 只处理以 backup_ 开头的目录
                if os.path.isdir(item_path) and item.startswith("backup_"):
                    try:
                        # 检查备份信息文件
                        info_path = os.path.join(item_path, "backup_info.json")
                        if os.path.exists(info_path):
                            with open(info_path, 'r', encoding='utf-8') as f:
                                backup_info = json.load(f)
                                # 从目录名中提取时间戳
                                timestamp_str = item[7:]  # 去掉 "backup_" 前缀
                                timestamp = time.mktime(time.strptime(timestamp_str, "%Y%m%d_%H%M%S"))
                                
                                # 根据备份类型分类
                                if backup_info.get('type') == 'auto':
                                    auto_backups.append((item_path, timestamp))
                                else:
                                    manual_backups.append((item_path, timestamp))
                        else:
                            # 如果没有信息文件，默认为手动备份
                            timestamp_str = item[7:]
                            timestamp = time.mktime(time.strptime(timestamp_str, "%Y%m%d_%H%M%S"))
                            manual_backups.append((item_path, timestamp))
                        
                    except Exception as e:
                        print(f"处理备份目录时出错 {item_path}: {str(e)}")
                        continue
            
            # 按时间戳升序排序（最旧的在前面）
            auto_backups.sort(key=lambda x: x[1])
            manual_backups.sort(key=lambda x: x[1])
            
            # 只清理自动备份
            total_auto_backups = len(auto_backups)
            if total_auto_backups <= keep_count:
                print(f"当前自动备份数量 ({total_auto_backups}) 未超过保留限制 ({keep_count})")
                return
                
            # 要删除的是最旧的自动备份
            to_delete = auto_backups[:-keep_count]  # 保留最后 keep_count 个
            print(f"将删除 {len(to_delete)} 个旧的自动备份，保留最新的 {keep_count} 个")
            print(f"手动备份数量: {len(manual_backups)}，这些将被保留")
            
            # 删除旧的自动备份
            for folder_path, timestamp in to_delete:
                try:
                    backup_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                    folder_name = os.path.basename(folder_path)
                    print(f"尝试删除自动备份: {folder_path}")
                    
                    # 使用增强的删除函数
                    self._force_delete_directory(folder_path)
                    print(f"已删除旧的自动备份: {folder_name} (创建于 {backup_time})")
                    
                except Exception as e:
                    print(f"删除备份失败 {folder_path}: {str(e)}")
                    continue
            
            remaining_auto = len(auto_backups) - len(to_delete)
            print(f"清理完成，当前剩余自动备份数量: {remaining_auto}，手动备份数量: {len(manual_backups)}")
            
        except Exception as e:
            print(f"清理旧备份时出错: {str(e)}")

    def _force_delete_directory(self, path):
        """
        强制删除目录，处理只读文件和访问权限问题
        :param path: 要删除的目录路径
        """
        def handle_error(func, path, exc_info):
            """处理删除错误的回调函数"""
            print(f"处理删除错误: {path}")
            try:
                # 获取文件/目录的完整访问权限
                os.chmod(path, 0o777)
                
                if os.path.isfile(path):
                    # 对于文件，先尝试解除只读属性
                    if os.name == 'nt':
                        import subprocess
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        subprocess.run(
                            ['attrib', '-R', '-S', '-H', path],
                            startupinfo=startupinfo,
                            creationflags=subprocess.CREATE_NO_WINDOW,
                            capture_output=True
                        )
                    os.remove(path)
                elif os.path.isdir(path):
                    # 对于目录，先尝试解除只读属性
                    if os.name == 'nt':
                        import subprocess
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        subprocess.run(
                            ['attrib', '-R', '-S', '-H', '/S', '/D', path],
                            startupinfo=startupinfo,
                            creationflags=subprocess.CREATE_NO_WINDOW,
                            capture_output=True
                        )
                    os.rmdir(path)
            except Exception as e:
                print(f"常规删除失败: {str(e)}，尝试使用系统命令")
                if os.name == 'nt':  # Windows系统
                    try:
                        import subprocess
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = 0  # SW_HIDE
                        
                        if os.path.isfile(path):
                            # 使用del命令删除文件
                            subprocess.run(
                                ['cmd', '/c', 'del', '/f', '/a', path],
                                startupinfo=startupinfo,
                                creationflags=subprocess.CREATE_NO_WINDOW,
                                capture_output=True
                            )
                        else:
                            # 使用rd命令删除目录
                            subprocess.run(
                                ['cmd', '/c', 'rd', '/s', '/q', path],
                                startupinfo=startupinfo,
                                creationflags=subprocess.CREATE_NO_WINDOW,
                                capture_output=True
                            )
                    except Exception as cmd_e:
                        print(f"系统命令删除失败: {str(cmd_e)}")

        try:
            # 首先尝试修改目录的权限
            if os.name == 'nt':
                try:
                    # 使用attrib命令递归移除只读、系统、隐藏属性
                    import subprocess
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    subprocess.run(
                        ['attrib', '-R', '-S', '-H', '/S', '/D', path],
                        startupinfo=startupinfo,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        capture_output=True
                    )
                except:
                    pass

            # 修改所有文件和目录的权限
            for root, dirs, files in os.walk(path):
                try:
                    os.chmod(root, 0o777)
                except:
                    pass
                for d in dirs:
                    try:
                        os.chmod(os.path.join(root, d), 0o777)
                    except:
                        pass
                for f in files:
                    try:
                        os.chmod(os.path.join(root, f), 0o777)
                    except:
                        pass

            # 如果是Git仓库，先尝试清理Git相关文件
            git_dir = os.path.join(path, '.git')
            if os.path.exists(git_dir):
                try:
                    # 在删除前先尝试清理Git锁文件
                    index_lock = os.path.join(git_dir, 'index.lock')
                    if os.path.exists(index_lock):
                        os.remove(index_lock)
                    
                    # 清理其他可能的锁文件
                    for root, dirs, files in os.walk(git_dir):
                        for f in files:
                            if f.endswith('.lock'):
                                try:
                                    os.remove(os.path.join(root, f))
                                except:
                                    pass
                
                    # 尝试清理Git对象
                    objects_dir = os.path.join(git_dir, 'objects')
                    if os.path.exists(objects_dir):
                        try:
                            # 使用Git命令清理
                            subprocess.run(
                                ['git', 'gc', '--prune=now'],
                                cwd=path,
                                startupinfo=subprocess.STARTUPINFO() if os.name == 'nt' else None,
                                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                                capture_output=True
                            )
                        except:
                            pass
                except:
                    pass

            # 使用shutil.rmtree进行删除，并使用error_handler处理错误
            shutil.rmtree(path, onerror=handle_error)
            
        except Exception as e:
            print(f"常规删除失败: {str(e)}，尝试使用系统命令强制删除")
            if os.name == 'nt':  # Windows系统
                try:
                    import subprocess
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = 0  # SW_HIDE
                    
                    # 先尝试解除文件属性
                    subprocess.run(
                        ['attrib', '-R', '-S', '-H', '/S', '/D', path],
                        startupinfo=startupinfo,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        capture_output=True
                    )
                    
                    # 强制删除
                    subprocess.run(
                        ['cmd', '/c', 'rd', '/s', '/q', path],
                        startupinfo=startupinfo,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        capture_output=True
                    )
                except Exception as final_e:
                    print(f"最终删除尝试失败: {str(final_e)}")
                    raise

    def stop_auto_backup(self):
        """停止自动备份"""
        if self._auto_backup_timer:
            self._auto_backup_timer.cancel()
            self._auto_backup_timer = None

    @ensure_window
    def destroy(self):
        if self.window:
            self.window.destroy()

    @ensure_window
    def maximize(self):
        if self.window:
            self.window.maximize()

    @ensure_window
    def minimize(self):
        if self.window:
            self.window.minimize()

    @ensure_window
    def move(self, x, y):
        if self.window:
            self.window.move(x, y)

    @ensure_window
    def my_resize(self, width, height, fix_point):
        if self.window:
            if fix_point in "NORTH":
                self.window.resize(width, height, FixPoint.NORTH)
            if fix_point in "SOUTH":
                self.window.resize(width, height, FixPoint.SOUTH)
            if fix_point in "EAST":
                self.window.resize(width, height, FixPoint.EAST)
            if fix_point in "WEST":
                self.window.resize(width, height, FixPoint.WEST)

    @ensure_window
    def restore(self):
        if self.window:
            self.window.restore()

    @ensure_window
    def show(self):
        if self.window:
            self.window.show()

    @ensure_window
    def toggle_fullscreen(self):
        if self.window:
            self.window.toggle_fullscreen()

    @ensure_window
    def hide(self):
        if self.window:
            self.window.hide()

    @ensure_window
    def load_css(self, stylesheet):
        if self.window:
            self.window.load_css(stylesheet)

    @ensure_window
    def eval_js(self, script):
        if self.window:
            return self.window.evaluate_js(script)

    @ensure_window
    def get_cookies(self):
        if self.window:
            return self.window.get_cookies()

    @ensure_window
    def get_current_url(self):
        if self.window:
            return self.window.get_current_url()

    @ensure_window
    def get_elements(self, css_selector):
        if self.window:
            return self.window.get_elements(css_selector)

    @ensure_window
    def resize_window(self, width, height, fix_point=None, delta_x=0, delta_y=0):
        """调整窗口大小和位置
        Args:
            width (int): 窗口宽度
            height (int): 窗口高度
            fix_point (str, optional): 固定点位置 ('n', 's', 'e', 'w', 'nw', 'ne', 'sw', 'se')
            delta_x (int, optional): X轴位置调整
            delta_y (int, optional): Y轴位置调整
        """
        try:
            # 将前端传来的方向转换为对应的固定点
            fix_point_map = {
                'n': FixPoint.SOUTH,  # 拖北边时，南边固定
                's': FixPoint.NORTH,  # 拖南边时，北边固定
                'e': FixPoint.WEST,  # 拖东边时，西边固定
                'w': FixPoint.EAST,  # 拖西边时，东边固定
                'nw': FixPoint.SOUTH | FixPoint.EAST,  # 拖西北角时，东南角固定
                'ne': FixPoint.SOUTH | FixPoint.WEST,  # 拖东北角时，西南角固定
                'sw': FixPoint.NORTH | FixPoint.EAST,  # 拖西南角时，东北角固定
                'se': FixPoint.NORTH | FixPoint.WEST  # 拖东南角时，西北角固定
            }

            fix = fix_point_map.get(fix_point, 0) if fix_point else 0

            # 先调整窗口大小
            self.window.resize(width, height, fix)

            # 如果需要调整位置
            if delta_x != 0 or delta_y != 0:
                # 获取当前窗口位置
                current_x, current_y = self.window.x, self.window.y
                # 调整窗口位置
                self.window.move(current_x + delta_x, current_y + delta_y)

            return self._success_response("窗口大小和位置已调整")
        except Exception as e:
            return self._error_response(f"调整窗口大小和位置失败: {str(e)}")

    @ensure_window
    def get_window_size(self):
        """获取窗口当前大小"""
        if self.window:
            width = self.window.width
            height = self.window.height
            return self._success_response("获取窗口大小成功", {
                "width": width,
                "height": height
            })

    @ensure_window
    def get_window_position(self):
        """获取窗口当前位置"""
        if self.window:
            x = self.window.x
            y = self.window.y
            return self._success_response("获取窗口位置成功", {
                "x": x,
                "y": y
            })

    @ensure_window
    def set_mouse_position(self, x, y):
        """设置鼠标位置
        Args:
            x (int): 屏幕X坐标
            y (int): 屏幕Y坐标
        """
        try:
            system = platform.system()
            
            if system == "Windows":
                try:
                    import win32api
                    win32api.SetCursorPos((int(x), int(y)))
                    return self._success_response("鼠标位置已设置")
                except ImportError:
                    return self._error_response("Windows系统需要安装pywin32库: pip install pywin32")
            elif system == "Darwin":  # macOS
                try:
                    # 使用PyObjC调用macOS API
                    try:
                        from Quartz import CGEventCreateMouseEvent, CGEventPost, kCGEventMouseMoved, kCGHIDEventTap, CGPoint
                        point = CGPoint(x=int(x), y=int(y))
                        event = CGEventCreateMouseEvent(None, kCGEventMouseMoved, point, 0)
                        CGEventPost(kCGHIDEventTap, event)
                        return self._success_response("鼠标位置已设置")
                    except ImportError:
                        return self._error_response("macOS系统需要安装PyObjC库: pip install pyobjc-framework-Quartz")
                except Exception as e:
                    return self._error_response(f"设置鼠标位置失败: {str(e)}")
            else:  # Linux
                try:
                    # 使用xdotool (需要已安装)
                    subprocess.run(['xdotool', 'mousemove', str(int(x)), str(int(y))], check=True)
                    return self._success_response("鼠标位置已设置")
                except FileNotFoundError:
                    return self._error_response("Linux系统需要安装xdotool: sudo apt-get install xdotool")
                except Exception as e:
                    return self._error_response(f"设置鼠标位置失败: {str(e)}")
        except Exception as e:
            return self._error_response(f"设置鼠标位置失败: {str(e)}")

    @ensure_window
    def get_request_header(self, header_name):
        """获取请求头"""
        # 这里需要实现获取请求头的逻辑
        # 由于pywebview环境的限制，这里简单返回None
        return None

    def chat(self, chat_id, model_id, messages, config_data={}):
        """发送聊天请求"""
        return self.model_controller.chat(chat_id, model_id, messages, config_data)

    def get_timeline(self, book_id):
        """获取时间线数据"""
        return self.book_controller.get_timeline(book_id)

    def export_timeline_to_txt(self, book_id, timeline_data):
        return self.book_controller.export_timeline_to_txt(book_id, timeline_data)
    
    def save_timeline(self, book_id, timeline_data):
        """保存时间线数据"""
        return self.book_controller.save_timeline(book_id, timeline_data)

    def save_timeline_event_type(self, book_id, event_types):
        """保存时间线事件类型配置"""
        return self.book_controller.save_timeline_event_type(book_id, event_types)

    def get_timeline_event_type(self, book_id):
        """获取时间线事件类型配置"""
        return self.book_controller.get_timeline_event_type(book_id)

    # TTS相关方法
    def get_voices(self):
        """获取所有可用的TTS语音"""
        return self.edge_tts_controller.get_voices_sync()

    def get_tts_config(self):
        """获取TTS配置"""
        return self.edge_tts_controller.get_config()

    def update_tts_config(self, config):
        """更新TTS配置"""
        return self.edge_tts_controller.update_config(config)

    def tts_speak(self, text, output_format='base64'):
        """文本转语音
        :param text: 要转换的文本
        :param output_format: 输出格式，支持 'base64' 或 'file'
        """
        return self.edge_tts_controller.speak(text, output_format)



    def stop_tts_stream(self):
        """停止当前的TTS流"""
        return self.edge_tts_controller.stop_stream()

    def stream_speak_sync(self, text, voice=None, rate=None, volume=None, pitch=None):
        """流式文本转语音"""
        return self.edge_tts_controller.stream_speak_sync(text, voice, rate, volume, pitch)

    def restart_application(self):
        """重启应用"""
        try:
            # 通知前端关闭窗口
            if self.window:
                self.window.evaluate_js('window.onbeforeunload = null;')  # 避免确认对话框
            
            # 获取当前进程ID
            import os
            import sys
            import subprocess
            import platform
            
            # 获取应用路径和参数
            app_path = sys.executable
            app_args = sys.argv[1:]
            
            # 构建重启命令
            restart_cmd = [app_path] + app_args
            
            # 使用subprocess在新进程中启动应用
            system = platform.system()
            if system == "Windows":
                # Windows: 使用DETACHED_PROCESS标志确保新进程独立于当前进程
                subprocess.Popen(restart_cmd, close_fds=True, creationflags=0x08000000)
            elif system == "Darwin":
                # macOS: 使用nohup确保进程在父进程退出后继续运行
                restart_cmd = ["nohup"] + restart_cmd
                subprocess.Popen(restart_cmd, close_fds=True, start_new_session=True,
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            else:
                # Linux: 使用setsid创建新的会话
                subprocess.Popen(restart_cmd, close_fds=True, start_new_session=True,
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # 延迟一秒后退出当前进程
            import threading
            def exit_app():
                import time
                time.sleep(1)
                os._exit(0)
            
            threading.Thread(target=exit_app, daemon=True).start()
            
            return self._success_response("应用将重启")
        
        except Exception as e:
            return self._error_response(f"重启应用失败: {str(e)}")

    def get_background_images(self):
        """获取所有已保存的背景图片列表"""
        try:
            if not os.path.exists(self.background_image_dir):
                os.makedirs(self.background_image_dir)
            
            images = []
            for file in os.listdir(self.background_image_dir):
                file_path = os.path.join(self.background_image_dir, file)
                if os.path.isfile(file_path) and file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                    # 获取文件信息
                    stat = os.stat(file_path)
                    created_time = stat.st_ctime
                    
                    # 从文件名中提取时间戳（如果格式为bg_timestamp.jpg）
                    timestamp = 0
                    try:
                        if file.startswith('bg_') and '_' in file:
                            timestamp = int(file.split('_')[1].split('.')[0])
                    except:
                        timestamp = created_time
                    
                    images.append({
                        'path': file_path,
                        'name': file,
                        'timestamp': timestamp,
                        'created_time': created_time
                    })
            
            # 按时间戳排序，最新的排在前面
            images.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return self._success_response("获取背景图片列表成功", images)
        except Exception as e:
            return self._error_response(f"获取背景图片列表失败: {str(e)}")

    def delete_background_image(self, file_path):
        """
        删除指定的背景图片
        :param file_path: 背景图片的完整路径
        :return: 删除结果
        """
        try:
            # 验证文件路径是否在背景图片目录中
            if not os.path.dirname(file_path) == self.background_image_dir:
                return self._error_response("无效的背景图片路径")
            
            # 验证文件是否存在
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
            
            # 如果是当前正在使用的背景图片，不允许删除
            current_config = self.config_controller.load_config()
            if current_config.get('editor', {}).get('bgImage') == file_path:
                return self._error_response("无法删除当前正在使用的背景图片")
            
            # 删除文件
            os.remove(file_path)
            return self._success_response("背景图片删除成功")
            
        except Exception as e:
            return self._error_response(f"删除背景图片失败: {str(e)}")

    # 自定义卡池相关方法
    def get_custom_pools(self):
        """获取所有自定义卡池"""
        return self.book_controller.get_custom_pools()
    
    def save_custom_pool(self, pool_data):
        """保存自定义卡池"""
        return self.book_controller.save_custom_pool(pool_data)
    
    def update_custom_pool(self, pool_id, pool_data):
        """更新自定义卡池"""
        return self.book_controller.update_custom_pool(pool_id, pool_data)
    
    def delete_custom_pool(self, pool_id):
        """删除自定义卡池"""
        return self.book_controller.delete_custom_pool(pool_id)
        
    # 剧情桥段设计相关方法
    def get_bridge_configs(self, book_id=None):
        """获取剧情桥段配置"""
        return self.book_controller.get_bridge_configs(book_id)
        
    def save_bridge_config(self, book_id=None, config_data=None):
        """保存剧情桥段配置"""
        # 确保config_data不为None
        if config_data is None:
            return self._error_response("配置数据不能为空")
        return self.book_controller.save_bridge_config(book_id, config_data)
        
    def delete_bridge_config(self, book_id=None, config_id=None):
        """删除剧情桥段配置"""
        return self.book_controller.delete_bridge_config(book_id, config_id)

    # Git相关方法
    def check_git_installation(self):
        """
        检查系统是否安装了Git
        :return: Git安装状态和版本信息
        """
        try:
            # 使用_execute_git_command执行git版本命令
            version = self._execute_git_command('git --version').strip()
            
            # Git命令执行成功，表示已安装
            return {
                "status": "success",
                "message": "Git已安装",
                "data": {
                    "installed": True,
                    "version": version
                }
            }
        except Exception as e:
            # 如果命令执行失败，检查是否是命令不存在错误
            error_msg = str(e)
            if "命令执行失败" in error_msg or "not found" in error_msg or "not recognized" in error_msg:
                # Git未安装或不在PATH中
                return {
                    "status": "success",
                    "message": "Git未安装",
                    "data": {
                        "installed": False,
                        "version": None
                    }
                }
            else:
                # 其他错误
                return {
                    "status": "error",
                    "message": f"检查Git安装失败: {error_msg}"
                }

    def init_git_repo(self, params):
        """初始化Git仓库"""
        try:
            repo_url = params.get('repo_url', '')
            auth_type = params.get('auth_type', 'token')
            username = params.get('username', '')
            password = params.get('password', '')
            token = params.get('token', '')
            token_username = params.get('token_username', '')
            backup_dir = params.get('backup_dir', '')
            
            if not repo_url or not backup_dir:
                return self._error_response("仓库URL和备份目录不能为空")
                
            # 确保备份目录存在
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir, exist_ok=True)
            
            # 检查是否已经是Git仓库
            if os.path.exists(os.path.join(backup_dir, '.git')):
                return {'status': 'warning', 'message': "目录已是Git仓库，将重新初始化"}
            
            # 构建Git URL
            git_url = self._build_git_url(repo_url, auth_type, username, password, token, token_username)
            
            # 初始化Git仓库
            self._execute_git_command('git init -b main', cwd=backup_dir)  # 直接初始化为main分支
            self._execute_git_command(f'git remote add origin {git_url}', cwd=backup_dir)
            
            # 设置Git用户信息
            git_username = username or token_username or "PVV User"
            git_email = f"{git_username}@example.com"
            
            self._execute_git_command(f'git config user.name "{git_username}"', cwd=backup_dir)
            self._execute_git_command(f'git config user.email "{git_email}"', cwd=backup_dir)
            
            # 创建初始README文件
            readme_path = os.path.join(backup_dir, 'README.md')
            with open(readme_path, 'w') as f:
                f.write(f"# Git备份\n\n初始化时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 添加并提交初始文件
            self._execute_git_command('git add README.md', cwd=backup_dir)
            self._execute_git_command('git commit -m "初始化仓库"', cwd=backup_dir)
            
            # 备份模式不需要拉取，直接推送到远程
            
            # 推送到远程仓库
            try:
                self._execute_git_command('git push -u origin main --force', cwd=backup_dir)
                return self._success_response("Git仓库初始化成功")
            except Exception as e:
                error_msg = str(e)
                print(f"推送失败: {error_msg}")
                return self._warning_response(f"仓库初始化成功，但首次推送失败: {error_msg}")
            
        except Exception as e:
            return self._error_response(f"初始化Git仓库失败: {str(e)}")

    def backup_to_git(self, params):
        """执行Git备份操作 - 直接在备份目录初始化仓库"""
        repo_url = params.get('repo_url', '')
        auth_type = params.get('auth_type', 'token')
        username = params.get('username', '')
        password = params.get('password', '')
        token = params.get('token', '')
        token_username = params.get('token_username', '')
        backup_dir = params.get('backup_dir', '')
        # 添加对标签名的获取
        tag_name = params.get('tag_name', '')
        # 添加日志，记录收到的commit_message参数
        commit_message = params.get('commit_message')
        if not commit_message:
            commit_message = f'自动备份于 {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        print(f"提交信息: {commit_message}")
        
        force = params.get('force', False)  # 获取force参数
        
        try:
            print(f"Using backup_dir as Git repository: {backup_dir}")
            
            # 验证备份目录
            if not backup_dir or not os.path.exists(backup_dir):
                return {'status': 'error', 'message': f"备份目录不存在: {backup_dir}"}
            
            # 规范化路径
            backup_dir = os.path.normpath(backup_dir)
            
            # 构建Git URL 
            git_url = self._build_git_url(repo_url, auth_type, username, password, token, token_username)
            
            # 初始化Git仓库（如果尚未初始化）
            if not os.path.exists(os.path.join(backup_dir, '.git')):
                self._execute_git_command('git init', cwd=backup_dir)
                self._execute_git_command(f'git remote add origin {git_url}', cwd=backup_dir)
                # 对于初始化的仓库，不需要拉取，直接创建初始提交
                initial_file = os.path.join(backup_dir, 'README.md')
                if not os.path.exists(initial_file):
                    with open(initial_file, 'w') as f:
                        f.write(f"# 备份仓库\n\n初始化于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 设置Git用户信息 - 解决提交时的身份问题
            git_username = username or token_username or "PVV User"
            git_email = f"{git_username}@example.com"
            
            self._execute_git_command(f'git config user.name "{git_username}"', cwd=backup_dir)
            self._execute_git_command(f'git config user.email "{git_email}"', cwd=backup_dir)
            
            # 添加所有文件到Git
            self._execute_git_command('git add -A', cwd=backup_dir)
            
            # 检查是否有变更需要提交
            status_output = self._execute_git_command('git status --porcelain', cwd=backup_dir)
            if not status_output.strip():
                # 关键修改：处理force参数
                if force:
                    # 如果强制备份，创建一个时间戳文件来产生变更
                    timestamp_file = os.path.join(backup_dir, '.backup_timestamp')
                    with open(timestamp_file, 'w') as f:
                        f.write(f"Forced backup timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    # 添加该文件到Git
                    self._execute_git_command('git add .backup_timestamp', cwd=backup_dir)
                    print("强制备份：已创建时间戳文件")
                else:
                    return {'status': 'info', 'message': '没有检测到文件变更，无需备份'}
            
            # 修复处理：处理提交信息中的特殊字符
            # 1. 使用单引号而不是双引号
            # 2. 转义提交信息中的单引号和其他特殊字符
            safe_commit_message = commit_message.replace("'", "'\\''")
            
            # 提交变更
            try:
                print(f"准备提交，信息: {commit_message}")
                # 直接传递命令列表，而不是格式化字符串
                result = self._execute_git_command(['git', 'commit', '-m', commit_message], cwd=backup_dir)
                print(f"提交结果: {result}")
            except Exception as e:
                # 更详细的错误信息
                error_msg = str(e)
                print(f"提交时出错: {error_msg}")
                
                if "Please tell me who you are" in error_msg:
                    return {'status': 'error', 'message': '请先配置Git用户名和邮箱'}
                else:
                    return {'status': 'error', 'message': f'提交失败: {error_msg}'}
            
            # 尝试获取远程分支信息
            try:
                self._execute_git_command('git fetch origin', cwd=backup_dir)
            except Exception as e:
                print(f"获取远程信息失败，尝试直接推送: {str(e)}")
            
            # 检查master分支是否存在
            try:
                self._execute_git_command('git checkout main', cwd=backup_dir)
            except Exception as e:
                print(f"切换到main分支失败: {str(e)}")
                # 创建并切换到main分支
                try:
                    self._execute_git_command('git checkout -b main', cwd=backup_dir)
                except Exception:
                    print("创建main分支失败，使用当前分支")
            
            # 推送到远程，添加-f参数强制推送
            try:
                self._execute_git_command('git push -u origin HEAD:main --force', cwd=backup_dir)
            except Exception as e:
                error_msg = str(e)
                print(f"推送失败: {error_msg}")
                
                # 提交成功但推送失败也算部分成功
                return {
                    'status': 'warning', 
                    'message': f'本地提交成功，但推送到远程仓库失败: {error_msg}'
                }
            
            # 在backup_to_git方法中，成功提交后添加标签
            # 推送成功后添加这段代码
            try:
                # 标签处理逻辑 - 自动或手动
                is_auto_backup = 'tag_name' not in params or not tag_name
                
                if is_auto_backup:
                    # 自动备份使用时间戳格式标签
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    tag_name = f"backup_{timestamp}"
                    tag_message = f"自动备份于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                else:
                    # 手动备份使用自定义标签名，默认前缀为"manual_"
                    if not tag_name.startswith(('backup_', 'manual_')):
                        tag_name = f"manual_{tag_name}"
                    tag_message = commit_message
                
                # 使用列表形式传递参数，避免命令行解析问题
                self._execute_git_command(['git', 'tag', '-a', tag_name, '-m', tag_message], cwd=backup_dir)
                
                # 同样使用列表形式推送标签
                self._execute_git_command(['git', 'push', 'origin', '--tags'], cwd=backup_dir)
                print(f"已创建并推送标签: {tag_name}")
            except Exception as e:
                print(f"创建标签失败: {str(e)}")
                # 标签失败不影响备份结果，只记录日志
            
            return {'status': 'success', 'message': '备份成功'}
        except Exception as e:
            error_msg = str(e)
            print(f"Git备份过程中出错: {error_msg}")
            return {'status': 'error', 'message': error_msg}

    def _execute_git_command(self, command, cwd=None):
        """
        执行Git命令
        :param command: 要执行的命令或命令参数列表
        :param cwd: 工作目录
        :return: 命令输出
        """
        try:
            print(f"执行Git命令: {command}")
            
            # 判断command是字符串还是列表
            if isinstance(command, str):
                if command.startswith('git commit -m'):
                    # 特殊处理提交命令，分离参数
                    cmd_parts = ['git', 'commit', '-m']
                    # 提取提交信息，去掉 'git commit -m' 和引号
                    msg_start = command.find('-m') + 3
                    commit_msg = command[msg_start:].strip()
                    # 去掉首尾的引号
                    if (commit_msg.startswith('"') and commit_msg.endswith('"')) or \
                       (commit_msg.startswith("'") and commit_msg.endswith("'")):
                        commit_msg = commit_msg[1:-1]
                    # 添加提交信息作为单独参数
                    cmd_parts.append(commit_msg)
                    command = cmd_parts
                else:
                    # 其他命令按空格分割
                    command = command.split()
            
            # 关键修改：明确指定 UTF-8 编码
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = 0  # SW_HIDE
                
            # 使用 Popen 而不是 run，可以更好地控制编码
            process = subprocess.Popen(
                command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' and hasattr(subprocess, 'CREATE_NO_WINDOW') else 0,
                env=dict(os.environ, PYTHONIOENCODING='utf-8')  # 确保Python IO使用UTF-8
            )
            
            # 手动读取并解码输出，捕获可能的编码错误
            stdout_bytes, stderr_bytes = process.communicate()
            
            try:
                stdout = stdout_bytes.decode('utf-8')
            except UnicodeDecodeError:
                # 尝试使用errors='replace'选项
                stdout = stdout_bytes.decode('utf-8', errors='replace')
                
            try:
                stderr = stderr_bytes.decode('utf-8')
            except UnicodeDecodeError:
                stderr = stderr_bytes.decode('utf-8', errors='replace')
                
            # 检查命令是否成功执行
            if process.returncode != 0:
                error_message = stderr.strip()
                raise Exception(f"Git命令执行失败: {error_message}")
            
            return stdout.strip()
        except Exception as e:
            print(f"执行Git命令出错: {str(e)}")
            raise

    def get_git_history(self, params):
        """获取Git仓库的提交历史记录"""
        repo_url = params.get('repo_url', '')
        local_path = params.get('local_path', '')
        count = params.get('count', 50)  # 默认获取50条记录
        
        try:
            if not repo_url or not local_path:
                return {'status': 'error', 'message': '仓库URL或本地路径未提供'}
                
            if not os.path.exists(local_path):
                return {'status': 'error', 'message': f'本地路径 {local_path} 不存在'}
                
            if not os.path.exists(os.path.join(local_path, '.git')):
                return {'status': 'error', 'message': f'{local_path} 不是一个Git仓库'}
            
            # 获取提交历史
            git_log_cmd = f'git log --pretty=format:"%H|%an|%at|%s" -n{count}'
            
            stdout = self._execute_git_command(git_log_cmd, cwd=local_path)
            
            # 如果没有提交记录，返回空列表
            if not stdout.strip():
                return {'status': 'success', 'history': []}
                
            # 解析日志输出
            commits = []
            for line in stdout.strip().split('\n'):
                try:
                    if not line.strip():
                        continue
                        
                    # 防御性编程：确保有足够的字段
                    parts = line.split('|')
                    if len(parts) < 4:
                        continue
                        
                    commit_hash, author, timestamp, message = parts[0], parts[1], parts[2], parts[3]
                    
                    # 转换时间戳为人类可读格式
                    try:
                        timestamp = int(timestamp)
                        date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        date = '未知日期'
                    
                    commits.append({
                        'hash': commit_hash,
                        'author': author,
                        'date': date,
                        'timestamp': timestamp,
                        'message': message
                    })
                except Exception as e:
                    # 单条记录解析错误不应该影响整个结果
                    print(f"Error parsing commit: {line}, error: {str(e)}")
                    continue
                
            return {'status': 'success', 'history': commits}
        except Exception as e:
            return {'status': 'error', 'message': f'获取Git历史失败: {str(e)}'}

    def get_git_diff(self, params):
        """
        获取Git仓库的文件变更差异
        :param params: 包含 backup_dir 和可选的 commit_hash
        :return: 变更差异内容
        """
        try:
            backup_dir = params.get('backup_dir', '')
            commit_hash = params.get('commit_hash', None)
            
            if not backup_dir or not os.path.exists(backup_dir):
                return {'status': 'error', 'message': '备份目录不存在'}
                
            if not os.path.exists(os.path.join(backup_dir, '.git')):
                return {'status': 'error', 'message': '无效的Git仓库'}
            
            # 获取差异内容
            try:
                if commit_hash:
                    # 获取指定提交与其父提交之间的差异
                    diff_command = f'git diff {commit_hash}^ {commit_hash}'
                else:
                    # 获取工作区与最新提交之间的差异
                    diff_command = 'git diff HEAD'
                    
                # 添加选项以获取更好的差异输出
                diff_command += ' --color=never'
                
                # 执行差异命令
                diff_output = self._execute_git_command(diff_command, cwd=backup_dir)
                
                # 获取变更的文件列表
                if commit_hash:
                    files_command = f'git diff --name-status {commit_hash}^ {commit_hash}'
                else:
                    files_command = 'git diff --name-status HEAD'
                    
                files_output = self._execute_git_command(files_command, cwd=backup_dir)
                
                # 解析变更文件列表
                changed_files = []
                for line in files_output.splitlines():
                    if line.strip():
                        parts = line.strip().split()
                        if len(parts) >= 2:
                            status, filename = parts[0], parts[1]
                            status_text = {
                                'M': '修改',
                                'A': '新增',
                                'D': '删除',
                                'R': '重命名',
                                'C': '复制'
                            }.get(status, '变更')
                            
                            changed_files.append({
                                'status': status,
                                'status_text': status_text,
                                'filename': filename
                            })
                
                return {
                    'status': 'success',
                    'data': {
                        'diff': diff_output,
                        'changed_files': changed_files
                    }
                }
            except Exception as e:
                return {'status': 'error', 'message': f'获取差异失败: {str(e)}'}
                
        except Exception as e:
            return {'status': 'error', 'message': f'处理Git差异请求失败: {str(e)}'}

    def restore_from_git(self, git_config):
        """
        从Git提交恢复数据
        :param git_config: Git配置
            - local_path: 本地仓库路径
            - commit_id: 提交ID
            - backup_dir: 需要恢复的目标目录
        :return: 恢复结果
        """
        try:
            local_path = git_config.get('local_path')
            commit_id = git_config.get('commit_id')
            backup_dir = git_config.get('backup_dir')
            
            if not all([commit_id, backup_dir]):
                return self._error_response("恢复参数不完整")
            
            # 获取Git仓库路径
            config = self.config_controller.load_config()
            git_config = config.get('git', {})
            
            repo_url = git_config.get('repoUrl', '')
            
            if not local_path:
                local_path = os.path.join(self.base_dir, "git_repos")
            
            repo_name = repo_url.split('/')[-1].replace('.git', '')
            repo_path = os.path.join(local_path, repo_name)
            
            # 检查仓库是否存在
            if not os.path.exists(os.path.join(repo_path, '.git')):
                return self._error_response("Git仓库未初始化")
            
            # 进入仓库目录
            os.chdir(repo_path)
            
            # 创建临时分支，检出指定提交
            temp_branch = f"restore_{int(time())}"
            subprocess.run(['git', 'checkout', '-b', temp_branch, commit_id], check=True)
            
            # 创建备份目录的临时副本
            if os.path.exists(backup_dir):
                backup_timestamp = int(time())
                temp_backup = f"{backup_dir}_backup_{backup_timestamp}"
                shutil.move(backup_dir, temp_backup)
            
            # 创建目标目录
            os.makedirs(backup_dir, exist_ok=True)
            
            # 复制仓库内容到备份目录
            for item in os.listdir(repo_path):
                # 排除.git目录和临时文件
                if item == '.git' or item.startswith('.'):
                    continue
                    
                src_path = os.path.join(repo_path, item)
                dst_path = os.path.join(backup_dir, item)
                
                try:
                    if os.path.isdir(src_path):
                        # 如果目标存在，先删除
                        if os.path.exists(dst_path):
                            shutil.rmtree(dst_path)
                        shutil.copytree(src_path, dst_path)
                    else:
                        # 如果是文件，直接复制
                        shutil.copy2(src_path, dst_path)
                except Exception as e:
                    print(f"复制 {item} 失败: {str(e)}")
            
            # 切换回主分支并删除临时分支
            main_branch = 'main'
            if subprocess.run(['git', 'rev-parse', '--verify', 'main'], check=False).returncode != 0:
                main_branch = 'master'
            
            subprocess.run(['git', 'checkout', main_branch], check=True)
            subprocess.run(['git', 'branch', '-D', temp_branch], check=True)
            
            return self._success_response("从Git恢复成功")
        except Exception as e:
            # 如果恢复失败，尝试恢复备份
            try:
                if 'temp_backup' in locals() and os.path.exists(temp_backup):
                    # 如果目标目录存在，先删除
                    if os.path.exists(backup_dir):
                        shutil.rmtree(backup_dir)
                    shutil.move(temp_backup, backup_dir)
            except:
                pass
            
            return self._error_response(f"从Git恢复失败: {str(e)}")

    # Git自动备份相关
    _git_auto_backup_timer = None

    def start_auto_git_backup(self):
        """
        开始Git自动备份
        :return: 开始结果
        """
        try:
            from time import time
            
            # 首先检查Git是否已安装
            git_check = self.check_git_installation()
            git_check_data = json.loads(git_check) if isinstance(git_check, str) else git_check
            
            if not git_check_data.get('data', {}).get('installed', False):
                return self._error_response("Git未安装，无法启动自动备份")
            
            # 检查Git配置
            config = self.config_controller.load_config()
            git_config = config.get('git', {})
            
            if not git_config.get('autoBackup', False):
                return self._success_response("Git自动备份未启用")
            
            # 验证Git配置是否完整
            auth_type = git_config.get('authType', 'password')
            
            if not git_config.get('repoUrl') or not git_config.get('backupDir'):
                return self._error_response("Git配置不完整，至少需要仓库URL和备份目录")
                
            if auth_type == 'password' and not (git_config.get('username') and git_config.get('password')):
                return self._error_response("Git配置不完整，密码认证方式需要用户名和密码")
                
            if auth_type == 'token' and not git_config.get('token'):
                return self._error_response("Git配置不完整，令牌认证方式需要访问令牌")
                
            if auth_type == 'token' and 'coding.net' in git_config.get('repoUrl', '') and not git_config.get('tokenUsername'):
                return self._error_response("Git配置不完整，Coding平台需要提供用户名")
            
            # 设置自动备份的间隔时间（分钟）
            interval_minutes = max(10, git_config.get('backupInterval', 60))
            interval_seconds = interval_minutes * 60
            
            def backup_task():
                try:
                    print(f"开始执行Git自动备份任务，时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    # 构建备份配置
                    backup_config = {
                        'repo_url': git_config.get('repoUrl'),
                        'auth_type': git_config.get('authType', 'password'),
                        'username': git_config.get('username', ''),
                        'password': git_config.get('password', ''),
                        'token': git_config.get('token', ''),
                        'token_username': git_config.get('tokenUsername', ''),
                        'local_path': git_config.get('localPath', ''),
                        'backup_dir': git_config.get('backupDir')
                    }
                    
                    # 执行备份
                    self.backup_to_git(backup_config)
                    
                    # 设置下一次备份任务
                    self._git_auto_backup_timer = threading.Timer(
                        interval_seconds,
                        backup_task
                    )
                    self._git_auto_backup_timer.daemon = True
                    self._git_auto_backup_timer.start()
                    
                except Exception as e:
                    print(f"Git自动备份任务执行失败: {str(e)}")
                    
                    # 即使失败，也要设置下一次备份
                    self._git_auto_backup_timer = threading.Timer(
                        interval_seconds,
                        backup_task
                    )
                    self._git_auto_backup_timer.daemon = True
                    self._git_auto_backup_timer.start()
            
            # 计算首次执行时间
            last_backup_time = git_config.get('lastBackupTime', 0)
            current_time = int(time())
            
            # 检查上次备份时间，计算下一次应执行时间
            time_since_last_backup = current_time - last_backup_time
            
            if time_since_last_backup >= interval_seconds:
                # 如果距离上次备份时间超过间隔，立即执行
                print(f"距离上次Git备份已经过去了 {time_since_last_backup/60:.2f} 分钟，立即执行备份")
                self._git_auto_backup_timer = threading.Timer(1, backup_task)  # 1秒后执行
            else:
                # 计算精确的延迟时间
                next_backup_delay = interval_seconds - time_since_last_backup
                formatted_time = time.strftime(
                    "%Y-%m-%d %H:%M:%S", 
                    time.localtime(current_time + next_backup_delay)
                )
                print(f"距离上次Git备份时间 {time_since_last_backup/60:.2f} 分钟，将在 {next_backup_delay/60:.2f} 分钟后执行下一次备份")
                print(f"预计Git备份时间: {formatted_time}")
                self._git_auto_backup_timer = threading.Timer(
                    next_backup_delay,
                    backup_task
                )
            
            self._git_auto_backup_timer.daemon = True
            self._git_auto_backup_timer.start()
            
            return self._success_response("Git自动备份已启动")
            
        except Exception as e:
            return self._error_response(f"启动Git自动备份失败: {str(e)}")

    def stop_auto_git_backup(self):
        """
        停止Git自动备份
        :return: 停止结果
        """
        try:
            if self._git_auto_backup_timer:
                self._git_auto_backup_timer.cancel()
                self._git_auto_backup_timer = None
            
            return self._success_response("Git自动备份已停止")
        except Exception as e:
            return self._error_response(f"停止Git自动备份失败: {str(e)}")

    def test_git_credentials(self, git_config):
        """测试Git凭据是否有效"""
        try:
            repo_url = git_config.get('repo_url')
            auth_type = git_config.get('auth_type', 'password')
            
            if not repo_url:
                return self._error_response("Git仓库URL不能为空")
            
            # 构建带认证的URL
            auth_url = self._build_git_url(
                repo_url, 
                auth_type, 
                git_config.get('username', ''), 
                git_config.get('password', ''),
                git_config.get('token', ''),
                git_config.get('token_username', '')
            )
            
            # 创建临时目录测试连接
            import tempfile
            temp_dir = tempfile.mkdtemp()
            
            try:
                # 修改点: 删除URL周围的引号，避免引号解析问题
                try:
                    # 关键修改: 不要给URL加额外的引号，_execute_git_command会处理命令拆分
                    cmd = f'git ls-remote --heads {auth_url}'
                    result = self._execute_git_command(cmd, cwd=temp_dir)
                    
                    return self._success_response(
                        "Git凭据验证成功" + 
                        (", 仓库可访问" if result.strip() else ", 但仓库可能是空的")
                    )
                except Exception as e:
                    error_msg = str(e)
                    print(f"Git连接测试失败，错误信息: {error_msg}")
                    
                    if "Authentication failed" in error_msg or "could not read Username" in error_msg:
                        return self._error_response("Git认证失败，请检查凭据信息")
                    else:
                        return self._error_response(f"Git仓库访问失败: {error_msg}")
            finally:
                # 清理临时目录
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
        except Exception as e:
            return self._error_response(f"测试Git凭据时出错: {str(e)}")

    def _build_git_url(self, repo_url, auth_type, username, password, token, token_username):
        """构建带认证的Git URL"""
        import urllib.parse
        
        # 解析URL
        url_parts = repo_url.split('://')
        protocol = url_parts[0] if len(url_parts) > 1 else 'https'
        domain_path = url_parts[1] if len(url_parts) > 1 else repo_url
        
        if auth_type == 'password':
            if not all([username, password]):
                raise Exception("请填写用户名和密码")
            
            # 对用户名和密码进行URL编码
            encoded_username = urllib.parse.quote(username, safe='')
            encoded_password = urllib.parse.quote(password, safe='')
            auth_url = f"{protocol}://{encoded_username}:{encoded_password}@{domain_path}"
        else:  # token认证方式
            if not token:
                raise Exception("请填写访问令牌")
            
            # 对令牌进行URL编码
            encoded_token = urllib.parse.quote(token, safe='')
            
            # 不同平台的token URL构建方式可能不同
            if 'github.com' in domain_path:
                auth_url = f"{protocol}://{encoded_token}:x-oauth-basic@{domain_path}"
            elif 'gitlab.com' in domain_path:
                auth_url = f"{protocol}://oauth2:{encoded_token}@{domain_path}"
            elif 'coding.net' in domain_path:
                if not token_username:
                    raise Exception("Coding平台需要提供用户名")
                    
                encoded_username = urllib.parse.quote(token_username, safe='')
                auth_url = f"{protocol}://{encoded_username}:{encoded_token}@{domain_path}"
            elif 'gitee.com' in domain_path:
                auth_url = f"{protocol}://{encoded_token}@{domain_path}"
            else:
                auth_url = f"{protocol}://x-access-token:{encoded_token}@{domain_path}"
        
        return auth_url

    def _copy_files_to_backup(self, source_dir, target_dir):
        """将源目录中的文件复制到目标目录"""
        # 清空目标目录(保留.git)
        for item in os.listdir(target_dir):
            if item != '.git':
                item_path = os.path.join(target_dir, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                else:
                    os.remove(item_path)
        
        # 复制源目录内容到目标目录
        for item in os.listdir(source_dir):
            source_path = os.path.join(source_dir, item)
            target_path = os.path.join(target_dir, item)
            
            if os.path.isdir(source_path):
                shutil.copytree(source_path, target_path)
            else:
                shutil.copy2(source_path, target_path)

    def check_path_exists(self, params):
        """检查给定路径是否存在"""
        try:
            path = params.get('path', '')
            if not path:
                return {'status': 'error', 'message': '路径不能为空', 'exists': False}
            
            # 规范化路径
            normalized_path = os.path.normpath(path)
            exists = os.path.exists(normalized_path)
            
            return {
                'status': 'success', 
                'exists': exists, 
                'path': normalized_path
            }
        except Exception as e:
            return {'status': 'error', 'message': str(e), 'exists': False}

    def reset_git_changes(self, params):
        """重置Git仓库状态，撤销未提交的更改"""
        backup_dir = params.get('backup_dir', '')
        mode = params.get('mode', 'soft')  # soft, mixed, hard
        
        try:
            if not backup_dir or not os.path.exists(backup_dir):
                return {'status': 'error', 'message': f"备份目录不存在: {backup_dir}"}
            
            if not os.path.exists(os.path.join(backup_dir, '.git')):
                return {'status': 'error', 'message': f"{backup_dir} 不是一个Git仓库"}
            
            # 根据模式选择重置方式
            if mode == 'hard':
                # 危险操作：放弃所有更改，回到最后一次提交
                self._execute_git_command('git reset --hard HEAD', cwd=backup_dir)
                result = "已强制重置仓库，所有未提交的更改已丢失"
            elif mode == 'mixed':
                # 中等模式：保留文件更改，但清除暂存区
                self._execute_git_command('git reset --mixed HEAD', cwd=backup_dir)
                result = "已重置暂存区，保留文件更改"
            else:
                # 软模式：仅撤销上一次提交，保留暂存和文件更改
                try:
                    self._execute_git_command('git reset --soft HEAD^', cwd=backup_dir)
                    result = "已撤销上一次提交，保留暂存的更改"
                except Exception:
                    # 如果没有提交历史，清空暂存区
                    self._execute_git_command('git reset', cwd=backup_dir)
                    result = "已清空暂存区"
            
            return {'status': 'success', 'message': result}
        except Exception as e:
            error_msg = str(e)
            print(f"重置Git仓库状态失败: {error_msg}")
            return {'status': 'error', 'message': f"重置失败: {error_msg}"}

    def check_git_status(self, params):
        """检查Git仓库状态"""
        backup_dir = params.get('backup_dir', '')
        
        try:
            if not backup_dir or not os.path.exists(backup_dir):
                return {'status': 'error', 'message': f"备份目录不存在: {backup_dir}"}
            
            if not os.path.exists(os.path.join(backup_dir, '.git')):
                return {'status': 'error', 'message': f"{backup_dir} 不是一个Git仓库"}
            
            # 检查是否有未提交的更改
            status = self._execute_git_command('git status --porcelain', cwd=backup_dir)
            has_changes = bool(status.strip())
            
            # 检查是否有暂存的更改
            staged = self._execute_git_command('git diff --cached --name-only', cwd=backup_dir)
            has_staged = bool(staged.strip())
            
            # 获取最后一次提交信息
            try:
                last_commit = self._execute_git_command('git log -1 --pretty=format:"%h - %s"', cwd=backup_dir)
            except Exception:
                last_commit = "无提交记录"
            
            # 获取当前分支
            try:
                branch = self._execute_git_command('git branch --show-current', cwd=backup_dir).strip()
            except Exception:
                branch = "未知"
            
            return {
                'status': 'success',
                'git_status': {
                    'has_changes': has_changes,
                    'has_staged': has_staged,
                    'last_commit': last_commit,
                    'branch': branch,
                    'status_details': status
                }
            }
        except Exception as e:
            error_msg = str(e)
            print(f"检查Git状态失败: {error_msg}")
            return {'status': 'error', 'message': f"状态检查失败: {error_msg}"}

    def get_git_commit_details(self, params):
        """获取Git提交详情"""
        commit_hash = params.get('commit_hash', '')
        repo_path = params.get('repo_path', '')
        
        if not commit_hash or not repo_path:
            return {'status': 'error', 'message': '提交哈希或仓库路径未提供'}
            
        try:
            # 确保仓库路径存在并且是Git仓库
            if not os.path.exists(repo_path) or not os.path.exists(os.path.join(repo_path, '.git')):
                return {'status': 'error', 'message': f'仓库路径不存在或不是有效的Git仓库: {repo_path}'}
                
            # 添加详细日志记录
            print(f"查看提交详情 - 仓库路径: {repo_path}, 提交哈希: {commit_hash}")
            
            # 首先验证提交是否存在
            try:
                # 使用rev-parse验证提交哈希是否存在
                verify_cmd = ['git', 'rev-parse', '--verify', f'{commit_hash}^{{commit}}']
                self._execute_git_command(verify_cmd, cwd=repo_path)
            except Exception as e:
                error_msg = str(e)
                print(f"验证提交哈希失败: {error_msg}")
                return {'status': 'error', 'message': f'提交 "{commit_hash}" 在仓库中不存在。可能已被删除或尚未同步。'}
            
            try:
                # 获取完整的diff (不要使用-p参数，它可能导致某些版本的Git出问题)
                diff_cmd = ['git', 'show', '--no-color', commit_hash]
                diff_output = self._execute_git_command(diff_cmd, cwd=repo_path)
                
                # 获取文件列表
                files_cmd = ['git', 'show', '--name-only', '--pretty=format:', commit_hash]
                files_output = self._execute_git_command(files_cmd, cwd=repo_path)
                changed_files = [f for f in files_output.split('\n') if f.strip()]
                
                return {
                    'status': 'success',
                    'diff': diff_output,
                    'changed_files': changed_files
                }
            except Exception as e:
                error_msg = str(e)
                print(f"获取提交内容失败: {error_msg}")
                return {'status': 'error', 'message': f'获取提交内容失败: {error_msg}'}
        except Exception as e:
            error_msg = str(e)
            print(f"查看提交详情出错: {error_msg}")
            return {'status': 'error', 'message': f'查看提交详情出错: {error_msg}'}

    def reset_to_commit(self, params):
        """恢复到指定的提交"""
        backup_dir = params.get('backup_dir', '')
        commit_hash = params.get('commit_hash', '')
        
        try:
            if not backup_dir or not os.path.exists(backup_dir):
                return {'status': 'error', 'message': f"备份目录不存在: {backup_dir}"}
            
            if not commit_hash:
                return {'status': 'error', 'message': "提交ID不能为空"}
            
            # 先确保在main分支上
            try:
                self._execute_git_command('git checkout main', cwd=backup_dir)
            except Exception as e:
                print(f"切换到main分支失败: {str(e)}")
                # 尝试创建main分支
                try:
                    self._execute_git_command('git checkout -b main', cwd=backup_dir)
                except Exception:
                    print("创建main分支失败，使用当前分支")
            
            # 执行强制重置到指定提交
            self._execute_git_command(f'git reset --hard {commit_hash}', cwd=backup_dir)
            
            return {'status': 'success', 'message': f"已成功恢复到提交 {commit_hash[:7]}"}
        except Exception as e:
            error_msg = str(e)
            print(f"恢复到指定提交失败: {error_msg}")
            return {'status': 'error', 'message': f"恢复失败: {error_msg}"}

    def _warning_response(self, message, data=None):
        """
        创建警告级别的响应
        :param message: 警告信息
        :param data: 可选的数据
        :return: 格式化的响应字典
        """
        response = {
            'status': 'warning',
            'message': message
        }
        if data is not None:
            response['data'] = data
        return response

    def fix_git_repository(self, params):
        """修复Git仓库常见问题"""
        backup_dir = params.get('backup_dir', '')
        
        try:
            if not backup_dir or not os.path.exists(backup_dir):
                return {'status': 'error', 'message': f"备份目录不存在: {backup_dir}"}
                
            if not os.path.exists(os.path.join(backup_dir, '.git')):
                return {'status': 'error', 'message': f"{backup_dir} 不是一个Git仓库"}
                
            # 检查当前分支
            current_branch = self._execute_git_command('git rev-parse --abbrev-ref HEAD', cwd=backup_dir).strip()
            
            # 如果不是main分支，创建main分支或切换到main分支
            if current_branch != 'main':
                # 检查是否有main分支
                branches = self._execute_git_command('git branch', cwd=backup_dir)
                if 'main' in branches:
                    # 切换到main分支
                    self._execute_git_command('git checkout main', cwd=backup_dir)
                else:
                    # 创建并切换到main分支
                    self._execute_git_command('git checkout -b main', cwd=backup_dir)
                    
            # 添加其他修复步骤...
            
            return {'status': 'success', 'message': '仓库修复完成'}
        except Exception as e:
            return {'status': 'error', 'message': f"修复仓库失败: {str(e)}"}

    def _init_auto_backup(self):
        """初始化自动备份设置"""
        try:
            config = self.config_controller.load_config()
            git_config = config.get('git', {})
            
            auto_backup_enabled = git_config.get('autoBackup', False)
            if auto_backup_enabled:
                backup_interval = git_config.get('backupInterval', 60)
                self.start_auto_backup(backup_interval)
        except Exception as e:
            print(f"初始化自动备份失败: {str(e)}")
    
    def start_auto_backup(self, interval_minutes=60):
        """
        启动自动备份线程
        :param interval_minutes: 备份间隔，单位分钟
        """
        try:
            # 如果已经有备份线程在运行，先停止它
            if self._auto_backup_thread and self._auto_backup_thread.is_alive():
                self.stop_auto_backup()
            
            # 重置停止事件
            self._stop_backup_event.clear()
            
            # 创建并启动新的备份线程
            self._auto_backup_thread = threading.Thread(
                target=self._auto_backup_worker,
                args=(interval_minutes,),
                daemon=True
            )
            self._auto_backup_thread.start()
            
            print(f"自动备份已启动，间隔: {interval_minutes}分钟")
            return {'status': 'success', 'message': f'自动备份已启动，间隔: {interval_minutes}分钟'}
        except Exception as e:
            error_msg = str(e)
            print(f"启动自动备份失败: {error_msg}")
            return {'status': 'error', 'message': f'启动自动备份失败: {error_msg}'}
    
    def stop_auto_backup(self):
        """停止自动备份线程"""
        try:
            if self._auto_backup_thread and self._auto_backup_thread.is_alive():
                # 发送停止信号
                self._stop_backup_event.set()
                
                # 等待线程结束（最多等待5秒）
                self._auto_backup_thread.join(timeout=5)
                
                # 清理
                self._auto_backup_thread = None
                print("自动备份已停止")
                
            return {'status': 'success', 'message': '自动备份已停止'}
        except Exception as e:
            error_msg = str(e)
            print(f"停止自动备份失败: {error_msg}")
            return {'status': 'error', 'message': f'停止自动备份失败: {error_msg}'}
    
    def _auto_backup_worker(self, interval_minutes):
        """
        自动备份工作线程
        :param interval_minutes: 备份间隔，单位分钟
        """
        interval_seconds = interval_minutes * 60
        
        while not self._stop_backup_event.is_set():
            try:
                # 获取当前配置
                config = self.config_controller.load_config()
                git_config = config.get('git', {})
                
                # 检查是否需要执行备份
                now = datetime.now()
                if self._last_backup_time is None or (now - self._last_backup_time).total_seconds() >= interval_seconds:
                    # 检查是否启用自动备份
                    if not git_config.get('autoBackup', False):
                        print("自动备份已在配置中禁用")
                        self._stop_backup_event.set()
                        break
                    
                    # 获取所需参数
                    backup_params = {
                        'repo_url': git_config.get('repoUrl', ''),
                        'auth_type': git_config.get('authType', 'token'),
                        'username': git_config.get('username', ''),
                        'password': git_config.get('password', ''),
                        'token': git_config.get('token', ''),
                        'token_username': git_config.get('tokenUsername', ''),
                        'backup_dir': git_config.get('backupDir', ''),
                        'commit_message': f'自动备份于 {now.strftime("%Y-%m-%d %H:%M:%S")}',
                        'force': True  # 自动备份强制执行
                    }
                    
                    # 检查必要参数
                    if not backup_params['repo_url'] or not backup_params['backup_dir']:
                        print("自动备份缺少必要参数（仓库URL或备份目录）")
                        time.sleep(60)  # 等待一分钟后再次检查
                        continue
                    
                    # 使用线程锁防止多个备份同时执行
                    if self._backup_lock.acquire(blocking=False):
                        try:
                            print(f"开始执行自动备份，时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
                            self._backup_running = True
                            
                            # 执行备份操作
                            result = self.backup_to_git(backup_params)
                            
                            # 更新上次备份时间
                            self._last_backup_time = now
                            
                            # 记录结果
                            status = result.get('status', 'unknown')
                            message = result.get('message', '未知结果')
                            print(f"自动备份完成，状态: {status}, 消息: {message}")
                            
                            # 为防止频繁备份，即使失败也更新上次备份时间
                            if status == 'error':
                                # 可以在这里添加失败通知机制
                                pass
                        finally:
                            self._backup_running = False
                            self._backup_lock.release()
                    else:
                        print("另一个备份操作正在进行，跳过本次自动备份")
                
                # 检查是否应该停止
                for _ in range(min(interval_seconds, 60)):
                    if self._stop_backup_event.is_set():
                        break
                    time.sleep(1)
            except Exception as e:
                print(f"自动备份过程中出错: {str(e)}")
                time.sleep(60)  # 出错后等待一分钟再次尝试
    
    def get_backup_status(self):
        """获取备份状态信息"""
        try:
            status_info = {
                'auto_backup_enabled': bool(self._auto_backup_thread and self._auto_backup_thread.is_alive()),
                'backup_in_progress': self._backup_running,
                'last_backup_time': self._last_backup_time.strftime('%Y-%m-%d %H:%M:%S') if self._last_backup_time else None
            }
            
            return {'status': 'success', 'data': status_info}
        except Exception as e:
            return {'status': 'error', 'message': f'获取备份状态失败: {str(e)}'}
    
    # 修改更新配置的方法，使其能够响应自动备份设置的变化
    def update_config(self, key, value):
        """更新配置并响应相关变更"""
        try:
            result = self.config_controller.update_config(key, value)
            
            # 特殊处理git配置变更
            if key == 'git' and isinstance(value, dict):
                auto_backup = value.get('autoBackup', False)
                backup_interval = value.get('backupInterval', 60)
                
                # 根据配置启动或停止自动备份
                if auto_backup:
                    self.start_auto_backup(backup_interval)
                else:
                    self.stop_auto_backup()
            
            return result
        except Exception as e:
            return {'status': 'error', 'message': f'更新配置失败: {str(e)}'}

    def select_save_path(self, params):
        """
        打开保存文件对话框
        :param params: 对话框参数
            - title: 对话框标题
            - file_types: 文件类型过滤器 [('描述', '*.扩展名'), ...]
            - default_name: 默认文件名
        :return: 选择的保存路径
        """
        try:
            title = params.get('title', '保存文件')
            file_types = params.get('file_types', [['All Files', '*.*']])
            default_name = params.get('default_name', '')
            
            import webview
            result = webview.windows[0].create_file_dialog(
                webview.SAVE_DIALOG,
                title=title,
                file_types=file_types,
                directory='',
                save_filename=default_name
            )
            
            if result:
                return self._success_response("已选择保存路径", result)
            else:
                return self._error_response("未选择保存路径")
        except Exception as e:
            return self._error_response(f"选择保存路径失败: {str(e)}")

    def save_entity_card(self, params):
        """
        保存实体卡片图片到指定路径
        :param params: 参数字典
            - file_path: 保存的文件路径
            - image_data: Base64编码的图片数据
        :return: 保存结果
        """
        try:
            file_path = params.get('file_path')
            image_data = params.get('image_data')
            
            if not file_path or not image_data:
                return self._error_response("缺少必要的参数")
            
            # 从Base64字符串解码图片数据
            if ',' in image_data:
                image_data = image_data.split(',')[1]  # 移除data:image/png;base64,前缀
                
            image_binary = base64.b64decode(image_data)
            
            # 确保目录存在
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            
            # 保存图片
            with open(file_path, 'wb') as f:
                f.write(image_binary)
                
            return self._success_response(f"卡片已成功保存到 {file_path}")
        except Exception as e:
            return self._error_response(f"保存卡片失败: {str(e)}")

    def get_machine_code(self):
        """获取当前机器的机器码"""
        try:
            # 导入并使用硬件标识符
            from .HardwareIdentifier import HardwareIdentifier
            
            # 创建标识符实例并获取机器ID
            identifier = HardwareIdentifier()
            machine_code = identifier.get_machine_id()
            
            # 保持一致长度，方便使用
            if len(machine_code) > 32:
                machine_code = machine_code[:32]
            
            # 存储机器码供后续使用
            if hasattr(self, 'user_manager'):
                self.user_manager.save_user_settings(machine_code=machine_code)
                
            return self._success_response("获取机器码成功", machine_code)
        except Exception as e:
            return self._error_response(f"获取机器码失败: {str(e)}")

    def generate_activation_code(self, params):
        """生成激活码 - 纯转发"""
        return self.user_manager.generate_activation_code_with_params(params)



    def get_network_time_info(self):
        """获取网络时间信息 - 调用工具函数"""
        from .TimeValidator import get_network_time_info
        return get_network_time_info(force_refresh=True)

    def get_network_time_now(self):
        """获取当前网络时间信息 - 调用工具函数的别名"""
        return self.get_network_time_info()

    # 用户相关方法 - 纯调用层
    def login(self, data_json):
        """用户登录方法 - 纯转发"""
        return self.user_manager.login(data_json)

    def verify_activation_code(self, data):
        """验证激活码 - 纯转发"""
        return self.user_manager.verify_activation_code(data)

    def check_activation_status(self):
        """检查激活状态 - 纯转发"""
        return self.user_manager.check_activation_status()

    def get_machine_code(self):
        """获取机器码 - 纯转发"""
        return self.user_manager.get_machine_code()

    def get_user_settings(self):
        """获取用户设置 - 纯转发"""
        return self.user_manager.get_user_settings()

    def save_user_settings(self, settings):
        """保存用户设置 - 纯转发"""
        return self.user_manager.save_user_settings(**settings)

    def logout(self):
        """用户登出 - 纯转发"""
        return self.user_manager.logout()

    @ensure_window
    def close_application(self, reason="用户请求"):
        """关闭应用程序"""
        try:
            print(f"应用程序将关闭，原因: {reason}")
            
            # 简单记录退出原因（可选）
            if self.window:
                # 使用setTimeout给前端一点时间显示消息
                self.window.evaluate_js(f"""
                    setTimeout(() => {{
                        alert("应用程序将关闭: {reason}");
                    }}, 100);
                """)
                
                # 给UI时间显示消息
                import time
                time.sleep(1.5)
                
                # 关闭窗口
                self.window.destroy()
            
            # 作为备用，也通过系统退出
            import sys
            sys.exit(0)
            
            return {"status": "success", "message": "正在关闭应用程序"}
        except Exception as e:
            return self._error_response(f"关闭应用程序失败: {str(e)}")

    def delete_ai_role(self, role_id):
        """删除AI角色"""
        return self.model_controller.delete_ai_role(role_id)
        
    # AI服务商管理接口
    def get_ai_providers(self):
        """获取所有AI服务商配置"""
        return self.model_controller.get_ai_providers()
        
    def save_ai_providers(self, providers_data):
        """保存AI服务商配置"""
        return self.model_controller.save_ai_providers(providers_data)
        
    def test_api_key(self, params):
        """测试API密钥是否有效"""
        return self.model_controller.test_api_key(params)
        
    def fetch_models(self, params):
        """获取特定服务商的模型列表"""
        return self.model_controller.fetch_models(params)

    def open_directory(self, path):
        """打开指定的目录"""
        try:
            if not os.path.exists(path):
                return self._error_response(f"目录不存在: {path}")
            
            if not os.path.isdir(path):
                return self._error_response(f"指定的路径不是目录: {path}")
                
            # 规范化路径，避免跨平台问题
            path = os.path.normpath(path)
                
            # 根据不同操作系统使用不同命令打开目录
            system = platform.system()
            if system == "Windows":
                # Windows 使用 explorer
                try:
                    os.startfile(path)  # 推荐使用，不会显示控制台窗口
                except AttributeError:
                    # 如果os.startfile不可用，回退到subprocess
                    subprocess.Popen(["explorer", path])
            elif system == "Darwin":
                # macOS 使用 open
                subprocess.Popen(["open", path])
            else:
                # Linux 使用 xdg-open 或尝试多种文件管理器
                success = False
                file_managers = ["xdg-open", "gio", "gvfs-open", "nautilus", "thunar", "pcmanfm"]
                
                for manager in file_managers:
                    try:
                        subprocess.Popen([manager, path])
                        success = True
                        break
                    except FileNotFoundError:
                        continue
                
                if not success:
                    return self._error_response("找不到可用的文件管理器，请安装xdg-open或其他文件管理器")
                
            return self._success_response(f"已打开目录: {path}")
        except Exception as e:
            return self._error_response(f"打开目录失败: {str(e)}")
    
    def open_browser_window(self, url, title="Web Browser"):
        """
        使用系统默认浏览器打开指定URL
        
        Args:
            url (str): 需要打开的URL
            title (str, optional): 窗口标题. 默认为 "Web Browser".
            
        Returns:
            dict: 包含打开状态的结果
        """
        try:
            import webbrowser
            
            # 使用系统默认浏览器打开URL
            webbrowser.open(url, new=2)  # new=2表示在新标签页打开
            
            return self._success_response(f"已在系统浏览器打开 {url}")
        except Exception as e:
            import traceback
            return self._error_response(f"打开浏览器失败: {str(e)}", {
                "details": traceback.format_exc()
            })
    
    def copy_to_clipboard(self, text):
        """
        复制文本到系统剪贴板
        
        Args:
            text (str): 要复制到剪贴板的文本
            
        Returns:
            dict: 包含操作状态的结果
        """
        try:
            # 优先使用pyperclip库 - 跨平台且无界面
            try:
                import pyperclip
                pyperclip.copy(text)
                return self._success_response("文本已复制到剪贴板")
            except ImportError:
                # 如果没有安装pyperclip，提示用户安装
                print("推荐安装pyperclip库以获得更好的剪贴板支持: pip install pyperclip")
                
                # 继续尝试平台特定的方法
                system = platform.system()
                
                if system == "Windows":
                    try:
                        # 尝试使用win32clipboard (不会显示黑窗口)
                        import win32clipboard
                        import win32con
                        
                        win32clipboard.OpenClipboard()
                        win32clipboard.EmptyClipboard()
                        win32clipboard.SetClipboardText(text, win32con.CF_UNICODETEXT)
                        win32clipboard.CloseClipboard()
                        
                        return self._success_response("文本已复制到剪贴板")
                    except ImportError:
                        # 使用subprocess调用clip，但隐藏窗口
                        startupinfo = None
                        if hasattr(subprocess, 'STARTUPINFO'):
                            startupinfo = subprocess.STARTUPINFO()
                            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                            startupinfo.wShowWindow = 0  # SW_HIDE
                        
                        subprocess.run(
                            ['clip'], 
                            input=text.encode('utf-16-le'), 
                            check=True,
                            startupinfo=startupinfo,
                            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
                        )
                        return self._success_response("文本已复制到剪贴板")
                
                elif system == "Darwin":  # macOS
                    # 使用pbcopy，macOS上通常不会显示窗口
                    subprocess.run(['pbcopy'], input=text.encode('utf-8'), check=True)
                    return self._success_response("文本已复制到剪贴板")
                
                else:  # Linux
                    # 尝试使用xclip或xsel，并使用参数隐藏窗口
                    try:
                        subprocess.run(['xclip', '-selection', 'clipboard', '-quiet'], 
                                      input=text.encode('utf-8'), check=True)
                        return self._success_response("文本已复制到剪贴板")
                    except FileNotFoundError:
                        try:
                            subprocess.run(['xsel', '--clipboard', '--input', '--quiet'], 
                                          input=text.encode('utf-8'), check=True)
                            return self._success_response("文本已复制到剪贴板")
                        except FileNotFoundError:
                            return self._error_response("复制失败：系统缺少xclip或xsel支持，请安装pyperclip库")
                            
        except Exception as e:
            return self._error_response(f"复制到剪贴板失败: {str(e)}")

    # 添加获取平台信息的API方法
    def get_platform_info(self):
        """获取系统平台信息"""
        import platform
        try:
            system_info = {
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor()
            }
            return {'status': 'success', 'message': '获取成功', 'data': system_info}
        except Exception as e:
            return {'status': 'error', 'message': f'获取平台信息失败: {str(e)}'}

    