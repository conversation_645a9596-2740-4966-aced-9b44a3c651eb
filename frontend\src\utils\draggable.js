// draggable.js
export function enableDragging(selector) {
    let initialX = 0;
    let initialY = 0;

    function onMouseMove(ev) {
        const x = ev.screenX - initialX;
        const y = ev.screenY - initialY;
        window.pywebview._jsApiCallback('pywebviewMoveWindow', [x, y], 'move');
    }

    function onMouseUp() {
        window.removeEventListener('mousemove', onMouseMove);
        window.removeEventListener('mouseup', onMouseUp);
    }

    function onMouseDown(ev) {
        console.log("这里监控拖拽点击成功。")
        initialX = ev.clientX;
        initialY = ev.clientY;
        window.addEventListener('mouseup', onMouseUp);
        window.addEventListener('mousemove', onMouseMove);
    }

    // 获取所有匹配的元素
    const draggableElements = document.querySelectorAll(selector);
    draggableElements.forEach(element => {
        element.addEventListener('mousedown', onMouseDown);
    });
}
