<template>
  <div class="editor-wrapper">
    <!-- 背景层，始终在底部 -->
    <div class="background-layer" :style="backgroundStyle"></div>
    
    <!-- 添加此行 - 强制背景图在全屏模式下可见 -->
    <div class="fullscreen-bg-layer" v-if="isFullscreen" :style="backgroundStyle"></div>
    
    <div class="book-editor"
         :class="[{ 'is-fullscreen': isFullscreen }, editorClass]"
         :style="editorStyle">
      <!-- 顶部工具栏 -->
      <div class="editor-toolbar">
        <div class="left-tools">
          <el-button-group>
            <el-tooltip content="返回">
              <el-button @click="back">
                <el-icon><Close /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="字体设置">
              <el-button @click="showFontSettings = true">字体</el-button>
            </el-tooltip>
            <el-tooltip content="背景设置">
              <el-button @click="showBackgroundSettings = true">背景</el-button>
            </el-tooltip>
            <el-tooltip content="排序设置">
              <el-button @click="showSortSettings = true">排序</el-button>
            </el-tooltip>
            <el-tooltip content="菜单配置">
              <el-button @click="showContextMenuSettings = true">菜单配置</el-button>
            </el-tooltip>
            <el-tooltip content="写作统计">
              <el-button @click="toggleWritingStats">
                <el-icon><DataLine /></el-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>

          <el-divider direction="vertical" />

          <!-- TTS功能按钮组 -->
          <div class="tts-controls">
            <el-tooltip 
              content="播放选中文本或从光标处开始播放" 
              placement="bottom"
            >
              <el-button 
                class="tts-btn"
                :class="{ 'is-active': isPlaying }"
                @click="playSelectedText"
                :disabled="isPlaying || !ttsServiceAvailable || ttsLoading"
                :loading="ttsLoading"
              >
                <el-icon><CaretRight /></el-icon>
                <span>选中播放</span>
              </el-button>
            </el-tooltip>
            
            <el-tooltip 
              content="播放全文" 
              placement="bottom"
            >
              <el-button 
                class="tts-btn"
                :class="{ 'is-active': isPlaying }"
                @click="playFullText"
                :disabled="isPlaying || !ttsServiceAvailable || ttsLoading"
                :loading="ttsLoading"
              >
                <el-icon><VideoPlay /></el-icon>
                <span>全文播放</span>
              </el-button>
            </el-tooltip>

            <el-tooltip 
              :content="!ttsServiceAvailable ? '语音服务不可用，请检查网络' : '停止播放'" 
              placement="bottom"
            >
              <el-button 
                class="tts-btn stop-btn"
                :class="{ 'can-stop': isPlaying || ttsLoading }"
                @click="stopPlayback"
                :disabled="!isPlaying && !ttsLoading"
              >
                <el-icon><VideoPause /></el-icon>
                <span>停止</span>
              </el-button>
            </el-tooltip>

            <el-tooltip 
              content="TTS设置" 
              placement="bottom"
            >
              <el-button 
                class="tts-btn"
                :disabled="isPlaying || ttsLoading"
                @click="showTTSSettings = true"
              >
                <el-icon><Setting /></el-icon>
                <span>设置</span>
              </el-button>
            </el-tooltip>

            <!-- 新增：新建实体按钮 -->
            <el-divider direction="vertical" />
            <el-tooltip 
              content="新建实体" 
              placement="bottom"
            >
              <el-button 
                class="entity-btn"
                @click="showEntityCreate = true"
              >
                <el-icon><Plus /></el-icon>
                <span>实体</span>
              </el-button>
            </el-tooltip>

            <el-tooltip 
              content="新建场景" 
              placement="bottom"
            >
              <el-button 
                class="scene-create-btn"
                @click="showSceneCreate = true"
              >
                <el-icon><Plus /></el-icon>
                <span>场景</span>
              </el-button>
            </el-tooltip>
          </div>

          <el-divider direction="vertical" />
          <el-tooltip content="聊天助手">
            <el-button @click="toggleChatSidebar">
              <el-icon><ChatDotRound /></el-icon>
              <span>聊天</span>
            </el-button>
          </el-tooltip>
          
          <!-- Add search button -->
          <el-tooltip content="网络查询">
            <el-button @click="toggleSearchPanel">
              <el-icon><Connection /></el-icon>
              <span>查询</span>
            </el-button>
          </el-tooltip>
          
          <!-- 添加查找按钮 -->
          <el-tooltip content="查找和替换 (Ctrl+F)">
            <el-button @click="toggleFindReplace">
              <el-icon><Search /></el-icon>
              <span>查找</span>
            </el-button>
          </el-tooltip>

          <!-- 添加标点符号面板按钮 -->
          <el-tooltip content="标点符号面板">
            <el-button @click="togglePunctuationPanel" :class="{ 'is-active': showPunctuationPanel }">
              <el-icon><Edit /></el-icon>
              <span>标点</span>
            </el-button>
          </el-tooltip>
        </div>

        <div class="drag-area" @dblclick="maximizeApp"></div>

        <div class="right-tools">
          <el-button type="primary" @click="manualSave">保存</el-button>

          <el-button @click="maximizeApp">
            <el-icon><Position /></el-icon>
          </el-button>

        </div>

        <!-- 在工具栏中添加一个下拉菜单用于小屏幕 -->
        <div class="toolbar-dropdown">
          <el-dropdown trigger="click">
            <el-button class="toolbar-dropdown-btn">
              <el-icon><More /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="showFontSettings = true">
                  <el-icon><Edit /></el-icon>字体设置
                </el-dropdown-item>
                <el-dropdown-item @click="showBackgroundSettings = true">
                  <el-icon><Picture /></el-icon>背景设置
                </el-dropdown-item>
                <el-dropdown-item @click="showSortSettings = true">
                  <el-icon><Sort /></el-icon>排序设置
                </el-dropdown-item>
                <el-dropdown-item @click="showContextMenuSettings = true">
                  <el-icon><Menu /></el-icon>菜单配置
                </el-dropdown-item>
                <el-dropdown-item @click="toggleWritingStats">
                  <el-icon><DataLine /></el-icon>写作统计
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 主编辑区域 -->
      <div class="editor-container">
        <!-- 左侧目录 -->
        <div class="chapters-sidebar chapter-list" v-show="!isFullscreen" :class="{ 'collapsed': sidebarCollapsed }">
            <div class="sidebar-header">
              <!-- 搜索框 -->
              <div class="search-container">
                <el-input
                  v-model="searchQuery"
                  placeholder="搜索章节..."
                  clearable
                  @input="handleSearch"
                  @clear="clearSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              
              <!-- 操作按钮 -->
              <div class="actions">
                <el-button-group>
                  <el-button 
                    type="primary" 
                    @click="createNewVolume"
                    :icon="Plus"
                  >
                    新建卷
                  </el-button>
                  <el-button
                    type="primary"
                    @click="createNewChapter"
                    :disabled="!currentVolume"
                    :icon="Plus"
                  >
                    新建章节
                  </el-button>
                </el-button-group>
              </div>
            </div>

            <!-- 使用虚拟滚动章节列表 -->
            <VirtualChapterList
              :volumes="sortedVolumes"
              :expanded-volume-ids="expandedVolumeIds"
              :active-chapter-id="activeChapterId"
              :search-results="searchResults"
              :show-search-results="showSearchResults"
              :search-loading="searchLoading"
              :container-height="500"
              :loading="volumesLoading"
              @chapter-click="selectChapter"
              @volume-command="handleVolumeCommandExtended"
              @chapter-command="handleChapterCommandExtended"
              @toggle-volume="toggleVolume"
              @create-volume="createNewVolume"
              @load-more="loadMoreVolumes"
            />

            <!-- 新建卷对话框 -->
            <el-dialog
                v-model="showNewVolumeDialog"
                title="新建卷"
                width="500px"
                class="create-volume-dialog"
                :append-to-body="true"
                :destroy-on-close="true"
                @opened="handleNewVolumeDialogOpened"
            >
              <el-form 
                :model="newVolumeForm" 
                @submit.prevent="confirmCreateVolume"
                @keyup.enter="confirmCreateVolume"
              >
                <el-form-item label="卷标题" required>
                  <el-input
                      v-model="newVolumeForm.title"
                      placeholder="请输入卷标题"
                      maxlength="50"
                      show-word-limit
                      ref="volumeTitleInput"
                      @keyup.enter="confirmCreateVolume"
                  />
                </el-form-item>
              </el-form>
              <template #footer>
                <div class="dialog-footer">
                  <el-button @click="showNewVolumeDialog = false">取消</el-button>
                  <el-button type="primary" @click="confirmCreateVolume">确定</el-button>
                </div>
              </template>
            </el-dialog>

            <!-- 新建章节对话框 -->
            <el-dialog
                v-model="showNewChapterDialog"
                title="新建章节"
                width="500px"
                class="create-chapter-dialog"
                :append-to-body="true"
                :destroy-on-close="true"
                @opened="handleNewChapterDialogOpened"
            >
              <el-form 
                :model="newChapterForm" 
                @submit.prevent="confirmCreateChapter"
                @keyup.enter="confirmCreateChapter"
              >
                <el-form-item label="所属卷" required>
                  <el-select
                      v-model="newChapterForm.volumeId"
                      placeholder="请选择所属卷"
                      style="width: 100%;"
                  >
                    <el-option
                        v-for="volume in sortedVolumes"
                        :key="volume.id"
                        :label="volume.title"
                        :value="volume.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="章节标题" required>
                  <el-input
                      v-model="newChapterForm.title"
                      placeholder="请输入章节标题"
                      maxlength="50"
                      show-word-limit
                      ref="chapterTitleInput"
                      @keyup.enter="confirmCreateChapter"
                  />
                </el-form-item>
              </el-form>
              <template #footer>
                <div class="dialog-footer">
                  <el-button @click="showNewChapterDialog = false">取消</el-button>
                  <el-button type="primary" @click="confirmCreateChapter">确定</el-button>
                </div>
              </template>
            </el-dialog>
          </div>

        <!-- 右侧编辑区域 -->
        <div class="editor-main" :class="{'with-chat-sidebar': showChatSidebar, 'with-search-panel': showSearchPanel, 'sidebar-collapsed': sidebarCollapsed}">
          <!-- 侧边栏切换按钮 -->
          <div class="sidebar-toggle-btn" @click="toggleSidebar">
            <el-icon v-if="!sidebarCollapsed"><ArrowLeft /></el-icon>
            <el-icon v-else><ArrowRight /></el-icon>
          </div>
          
          <div class="editor-content" @contextmenu.prevent="handleContextMenu">
            <div class="chapter-header">
              <input
                  type="text"
                  class="chapter-title-input"
                  v-model="currentChapter.title"
                  placeholder="请输入章节标题"
                  @change="updateChapterTitle"
                  @keydown.enter="focusEditor"
              >
            </div>
            <editor-content :editor="editor" />

            <!-- Context Menu -->
            <ContextMenu
                v-if="showContextMenu"
                :visible="showContextMenu"

                :x="contextMenuX"
                :y="contextMenuY"
                :menuItems="configStore.editor.contextMenus"
                :selectedText="selectedText"
                @select="handleMenuSelect"
                @close="showContextMenu = false"
            />

            <!-- 添加查找替换面板 -->
            <FindReplacePanel
              :visible="showFindReplace"
              :editor="editor"
              @update:visible="showFindReplace = $event"
              @close="showFindReplace = false"
            />

            <!-- 添加标点符号面板 -->
            <PunctuationPanel
              :visible="showPunctuationPanel"
              :editor="editor"
              @close="showPunctuationPanel = false"
              @insert="handlePunctuationInsert"
            />

            <!-- AI Assistant Window -->

          </div>
          
          <!-- 状态栏 -->
          <div class="editor-status-bar">
            <div class="status-left">
              <div class="status-item">
                <span class="status-label">总字数：</span>
                <span class="status-value">{{ formattedChapterWords }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">选中字数：</span>
                <span class="status-value">{{ selectedTextCount }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">当前段落：</span>
                <span class="status-value">{{ currentParagraphCount }} 字</span>
              </div>
            </div>
            <div class="status-right">
              <div class="status-item">
                <span class="status-label">当前时间：</span>
                <span class="status-value">{{ currentTime }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天侧边栏 -->
        <ChatSidebar
          :visible="showChatSidebar"
          :book-id="bookId"
          :selected-text="selectedText"
          :editor="editor"
          @close="showChatSidebar = false"
          @insert-text="insertChatContentToEditor"
        />

        <!-- 网络查询侧边栏 -->
        <SearchPanel
          :visible="showSearchPanel"
          :book-id="bookId"
          :selected-text="selectedText"
          :editor="editor"
          @close="showSearchPanel = false"
          @show-config="showSearchConfig = true"
        />

        <!-- 在editor-container内添加写作统计面板 -->
        <transition name="fade">
          <div v-if="showWritingStats" class="writing-stats-panel" :style="writingStatsPanelStyle">
            <div class="stats-header">
              <span class="title">写作统计</span>
              <el-icon class="drag-handle" @mousedown="initDrag"><Rank /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stat-row">
                <div class="stat-item">
                  <div class="stat-label">本次码字</div>
                  <div class="stat-value">{{ formattedSessionWords }}</div>
                </div>
              </div>
              <div class="stat-row">
                <div class="stat-item">
                  <div class="stat-label">码字速率(字/时)</div>
                  <div class="stat-value">{{ formattedTypingSpeed }}</div>
                </div>
              </div>

              <div class="stat-row">
                <div class="stat-item">
                  <div class="stat-label">码字时间</div>
                  <div class="stat-value">{{ formattedActiveTime }}</div>
                </div>
              </div>
              <div class="stat-row">
                <div class="stat-item">
                  <div class="stat-label">空闲时间</div>
                  <div class="stat-value">{{ formattedIdleTime }}</div>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>

      <!-- 字体设置弹窗 -->
      <el-drawer
          v-model="showFontSettings"
          title="字体设置"
          direction="rtl"
          size="400px"
          :destroy-on-close="false"
          :append-to-body="true"
          custom-class="font-settings-drawer"
      >
        <div class="settings-content">
          <div class="settings-section">
            <div class="setting-group">
              <div class="setting-item">
                <div class="setting-header">
                  <span class="setting-label">字体</span>
                  <span class="setting-value">{{ editorSettings.fontFamily }}</span>
                </div>
                <el-select
                    v-model="editorSettings.fontFamily"
                    placeholder="选择字体"
                    class="font-select"
                >
                  <!-- 无衬线字体（黑体）- 适合标题和正文 -->
                  <el-option-group label="无衬线字体">
                    <el-option label="思源黑体" value="'Noto Sans SC', sans-serif" />
                    <el-option label="微软雅黑" value="'Microsoft YaHei', sans-serif" />
                    <el-option label="黑体" value="SimHei, sans-serif" />
                    <el-option label="阿里巴巴普惠体" value="'AlibabaPuHuiTi', sans-serif" />
                    <el-option label="苹方" value="'PingFang SC', 'Hiragino Sans GB', sans-serif" />
                  </el-option-group>
                  
                  <!-- 衬线字体（宋体）- 适合正文 -->
                  <el-option-group label="衬线字体">
                    <el-option label="思源宋体" value="'Noto Serif SC', serif" />
                    <el-option label="宋体" value="SimSun, serif" />
                    <el-option label="方正书宋" value="'FZShuSong-Z01', 'STSong', serif" />
                    <el-option label="Georgia Pro" value="'Georgia Pro', 'Times New Roman', SimSun, serif" />
                  </el-option-group>
                  
                  <!-- 等宽字体 - 适合代码或特殊排版 -->
                  <el-option-group label="等宽字体">
                    <el-option label="思源等宽" value="'Source Han Mono SC', monospace" />
                    <el-option label="等线" value="'DengXian', 'Microsoft YaHei', sans-serif" />
                  </el-option-group>
                  
                  <!-- 手写/楷体字体 - 适合轻松内容 -->
                  <el-option-group label="手写/楷体">
                    <el-option label="楷体" value="KaiTi, STKaiti, serif" />
                    <el-option label="霞鹜文楷" value="'LXGW WenKai', 'Noto Serif SC', serif" />
                    <el-option label="隶书" value="LiSu, STLiti, serif" />
                  </el-option-group>
                </el-select>
              </div>

              <!-- 添加字体颜色设置 -->
              <div class="setting-item">
                <div class="setting-header">
                  <span class="setting-label">字体颜色</span>
                  <span class="setting-value">{{ editorSettings.fontColor || '#303133' }}</span>
                </div>
                <div class="color-settings">
                  <el-color-picker
                      v-model="editorSettings.fontColor"
                      :predefine="['#303133', '#606266', '#909399', '#2D2D2D', '#000000', '#1E1E1E', '#494949', '#5E0000', '#4A2604', '#214001', '#004138', '#012A4A', '#171064', '#3C0051']"
                      show-alpha
                      class="font-color-picker"
                      @change="handleFontColorChange"
                  />
                  
                  <!-- 最近使用的颜色 -->
                  <div class="recent-colors" v-if="editorSettings.recentFontColors && editorSettings.recentFontColors.length > 0">
                    <div class="recent-colors-title">最近使用:</div>
                    <div class="recent-colors-list">
                      <div 
                        v-for="(color, index) in editorSettings.recentFontColors" 
                        :key="index"
                        class="recent-color-item"
                        :style="{backgroundColor: color}"
                        :title="color"
                        @click="editorSettings.fontColor = color"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="setting-item">
                <div class="setting-header">
                  <span class="setting-label">字体大小</span>
                  <span class="setting-value">{{ editorSettings.fontSize }}px</span>
                </div>
                <el-slider
                    v-model="editorSettings.fontSize"
                    :min="16"
                    :max="58"
                    :step="1"
                    :format-tooltip="value => `${value}px`"
                    class="custom-slider"
                />
              </div>

              <div class="setting-item">
                <div class="setting-header">
                  <span class="setting-label">行间距</span>
                  <span class="setting-value">{{ editorSettings.lineHeight }}</span>
                </div>
                <el-slider
                    v-model="editorSettings.lineHeight"
                    :min="1"
                    :max="3"
                    :step="0.1"
                    :format-tooltip="value => value.toFixed(1)"
                    class="custom-slider"
                />
              </div>

              <div class="setting-item">
                <div class="setting-header">
                  <span class="setting-label">正文宽度</span>
                  <span class="setting-value">{{ editorSettings.contentWidth }}%</span>
                </div>
                <el-slider
                    v-model="editorSettings.contentWidth"
                    :min="20"
                    :max="100"
                    :step="1"
                    :format-tooltip="value => `${value}%`"
                    class="custom-slider"
                />
              </div>

              <!-- 添加段落间距设置 -->
              <div class="setting-item">
                <div class="setting-header">
                  <span class="setting-label">段落间距</span>
                  <span class="setting-value">{{ editorSettings.paragraphSpace ? '开启' : '关闭' }}</span>
                </div>
                <el-switch
                    v-model="editorSettings.paragraphSpace"
                    active-text="开启"
                    inactive-text="关闭"
                />
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="drawer-footer">
            <el-button @click="showFontSettings = false">关闭</el-button>
          </div>
        </template>
      </el-drawer>

      <!-- 背景设置弹窗 -->
      <el-dialog
          v-model="showBackgroundSettings"
          title="背景设置"
          width="800px"
          :top="'5vh'"
          :append-to-body="true"
          destroy-on-close
          :close-on-click-modal="false"
          class="background-settings-dialog"
      >
        <el-form label-position="top" class="bg-settings-form">
          <el-form-item label="启用背景图片">
            <el-switch
                v-model="editorSettings.bgEnabled"
                @change="handleBgEnabledChange"
            />
          </el-form-item>

          <template v-if="editorSettings.bgEnabled">
            <el-form-item label="选择背景方式">
              <el-tabs v-model="bgSelectionTab">
                <el-tab-pane label="历史背景" name="history">
                  <!-- 替换整个历史背景容器的结构 -->
                  <div class="bg-history-wrapper">
                    <div class="loading-state" v-if="loadingBackgrounds">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      <span>加载中...</span>
                    </div>
                    
                    <div class="empty-state" v-else-if="historyBackgrounds.length === 0">
                      <el-icon><PictureFilled /></el-icon>
                      <span>暂无历史背景图片</span>
                    </div>
                    
                    <div class="bg-list-container" v-else>
                      <div class="bg-list">
                        <div 
                          v-for="bg in historyBackgrounds" 
                          :key="bg.path"
                          class="bg-item"
                          :class="{ active: editorSettings.bgImage === bg.path }"
                        >
                          <div class="bg-preview" @click="selectHistoryBackground(bg.path)">
                            <img 
                              :src="bgThumbnails[bg.path] || ''" 
                              alt="历史背景" 
                              class="bg-img"
                              :class="{'loading': !bgThumbnails[bg.path]}"
                            />
                            <div class="loading-placeholder" v-if="!bgThumbnails[bg.path]">
                              <el-icon class="is-loading"><Loading /></el-icon>
                            </div>
                          </div>
                          <div class="bg-info">
                            <div class="bg-date">{{ formatTime(bg.timestamp) }}</div>
                            <div class="bg-actions">
                              <el-button 
                                type="danger" 
                                size="small" 
                                link
                                @click.stop="confirmDeleteBackground(bg)"
                              >
                                <el-icon><Delete /></el-icon>
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="上传新图片" name="upload">
                  <div class="bg-preview-wrapper">
                    <div class="bg-preview" v-if="backgroundImageData">
                      <img :src="backgroundImageData" alt="当前背景预览" />
                      <div class="preview-label">当前背景预览</div>
                    </div>
                    <div class="no-bg-selected" v-else>
                      <el-icon><Picture /></el-icon>
                      <span>未选择背景图片</span>
                    </div>
                  </div>
                 
                  <el-button type="primary" @click="selectBackground">选择本地图片</el-button>
                  <div class="upload-tip">
                    支持 JPG、PNG、GIF、WEBP 格式，图片将保存至您的背景库
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-form-item>

            <el-form-item label="背景透明度">
              <el-slider
                  v-model="editorSettings.bgOpacity"
                  :min="0"
                  :max="100"
                  :step="1"
                  show-input
              />
            </el-form-item>
          </template>
        </el-form>
      </el-dialog>

      <!-- 排序设置弹窗 -->
      <el-dialog
          v-model="showSortSettings"
          title="目录排序设置"
          width="400px"
          :modal="true"
          :close-on-click-modal="false"
          destroy-on-close
      >
        <el-form label-position="top">
          <el-form-item label="卷排序方式">
            <el-radio-group v-model="editorSettings.volumeSortOrder">
              <el-radio label="asc">从旧到新</el-radio>
              <el-radio label="desc">从新到旧</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="章节排序方式">
            <el-radio-group v-model="editorSettings.chapterSortOrder">
              <el-radio label="asc">从旧到新</el-radio>
              <el-radio label="desc">从新到旧</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showSortSettings = false">取消</el-button>
            <el-button type="primary" @click="saveSortSettings">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 右键菜单配置 -->
      <el-drawer
        v-model="showContextMenuSettings"
        title="右键菜单配置"
        direction="rtl"
        size="600px"
        :modal="true"
        :destroy-on-close="false"
        :append-to-body="true"
        custom-class="context-menu-settings-drawer"
      >
        <ContextMenuSettings
          :visible="showContextMenuSettings"
          :selectedText="selectedText"
          :editor="editor"
          :book-id="bookId"
          @close="showContextMenuSettings = false"
        />
      </el-drawer>

      <!-- TTS配置面板 -->
      <el-dialog
        v-model="showTTSSettings"
        title="TTS设置"
        width="500px"
      >
        <div class="tts-settings-panel">
          <el-form label-position="top">
            <el-form-item label="选择语音">
              <div class="voice-select-container">
                <el-select 
                  v-model="ttsConfig.voice" 
                  placeholder="请选择语音"
                  @change="updateTTSConfig('voice', $event)"
                  :loading="voicesLoading"
                  filterable
                >
                  <el-option
                    v-for="voice in voices"
                    :key="voice.ShortName"
                    :label="voice.LocalName"
                    :value="voice.ShortName"
                  />
                </el-select>
                
                <!-- 网络错误提示 -->
                <div v-if="!voicesLoading && voices.length === 0" class="empty-voices-tip">
                  <el-icon><Warning /></el-icon>
                  <span>无法加载语音列表，请检查网络连接</span>
                  <el-button type="primary" link size="small" @click="retryLoadVoices">重试</el-button>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="语速">
              <el-slider
                v-model="ttsConfig.rate"
                :min="-100"
                :max="100"
                :format-tooltip="val => val + '%'"
                @change="val => updateTTSConfig('rate', val)"
              />
            </el-form-item>

            <el-form-item label="音量">
              <el-slider
                v-model="ttsConfig.volume"
                :min="-100"
                :max="100"
                :format-tooltip="val => val + '%'"
                @change="val => updateTTSConfig('volume', val)"
              />
            </el-form-item>

            <el-form-item label="音调">
              <el-slider
                v-model="ttsConfig.pitch"
                :min="-100"
                :max="100"
                :format-tooltip="val => val + 'Hz'"
                @change="val => updateTTSConfig('pitch', val)"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>

      <!-- 新增：新建实体对话框 -->
      <div class="global-popup-container">
        <EntityCreateWindow
          :visible="showEntityCreate"
          :book-id="bookId"
          @update:visible="showEntityCreate = $event"
          @created="handleEntityCreated"
        />

        <!-- 新增：场景创建窗口 -->
        <SceneCreateWindow
          :visible="showSceneCreate"
          :book-id="bookId"
          @update:visible="showSceneCreate = $event"
          @created="handleSceneCreated"
        />

        <!-- 新增：搜索配置窗口 -->
        <SearchConfigWindow
          :visible="showSearchConfig"
          :book-id="bookId"
          @update:visible="showSearchConfig = $event"
          @config-updated="handleSearchConfigUpdated"
        />

        <AIAssistantWindow
                v-if="showAIAssistant"
                :visible="showAIAssistant"
                :initial-prompt="aiInitialPrompt"
                :model="aiModel"
                :selected-text="selectedText"
                @close="showAIAssistant = false"
                @insert-text="insertAIContentToEditor"
            />
      </div>
    </div>
  </div>
</template>

<script setup>

</script>

<style lang="scss" scoped>
@import url("@/scss/editor.scss");
</style>

