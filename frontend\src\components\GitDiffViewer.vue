<template>
  <div class="git-diff-viewer">
    <div class="file-list" v-if="changedFiles.length > 0">
      <h3>变更文件 ({{ changedFiles.length }})</h3>
      <el-scrollbar height="200px">
        <div 
          v-for="file in changedFiles" 
          :key="file.filename" 
          class="file-item"
          :class="{ active: selectedFile === file.filename }"
          @click="selectedFile = file.filename"
        >
          <el-tag 
            :type="getStatusType(file.status)"
            size="small"
          >
            {{ file.status_text }}
          </el-tag>
          <span class="filename">{{ file.filename }}</span>
        </div>
      </el-scrollbar>
    </div>
    
    <div class="diff-content">
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载差异内容...</span>
      </div>
      <div v-else-if="error" class="error-message">
        <el-icon><Warning /></el-icon>
        <span>{{ error }}</span>
      </div>
      <div v-else-if="!diffHtml" class="no-diff-message">
        <el-icon><InfoFilled /></el-icon>
        <span>没有变更内容</span>
      </div>
      <div v-else ref="diffContainer" class="diff-container"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElIcon, ElTag, ElScrollbar } from 'element-plus';
import { Loading, Warning, InfoFilled } from '@element-plus/icons-vue';
import * as Diff2Html from 'diff2html';
import 'diff2html/bundles/css/diff2html.min.css';

const props = defineProps({
  diffText: String,
  changedFiles: {
    type: Array,
    default: () => []
  },
  loading: Boolean,
  error: String
});

const emit = defineEmits(['refresh']);
const diffContainer = ref(null);
const selectedFile = ref('');
const diffHtml = ref('');

// 根据状态获取标签类型
const getStatusType = (status) => {
  const types = {
    'M': 'warning',   // 修改
    'A': 'success',   // 新增
    'D': 'danger',    // 删除
    'R': 'info',      // 重命名
    'C': 'info'       // 复制
  };
  return types[status] || 'info';
};

// 过滤差异内容，只显示选中的文件
const filteredDiff = computed(() => {
  if (!props.diffText || !selectedFile.value) return props.diffText;
  
  // 简单的过滤方法，更复杂的情况可能需要解析diff格式
  const lines = props.diffText.split('\n');
  const result = [];
  let inSelectedFile = false;
  let diffHeaderPattern = /^diff --git a\/(.*) b\/(.*)$/;
  
  for (const line of lines) {
    const match = line.match(diffHeaderPattern);
    if (match) {
      const filename = match[1];
      inSelectedFile = filename === selectedFile.value;
      
      if (inSelectedFile) {
        result.push(line);
      }
    } else if (inSelectedFile || !selectedFile.value) {
      result.push(line);
    }
  }
  
  return result.join('\n');
});

// 渲染差异内容
const renderDiff = () => {
  if (!props.diffText) {
    diffHtml.value = '';
    return;
  }
  
  try {
    const diffJson = Diff2Html.parse(filteredDiff.value);
    diffHtml.value = Diff2Html.html(diffJson, {
      drawFileList: false,
      matching: 'lines',
      outputFormat: 'side-by-side'
    });
    
    // 更新DOM
    if (diffContainer.value) {
      diffContainer.value.innerHTML = diffHtml.value;
    }
  } catch (error) {
    console.error('渲染差异失败:', error);
    diffHtml.value = '';
  }
};

// 监听差异内容变化
watch(() => props.diffText, renderDiff, { immediate: true });
watch(() => selectedFile.value, renderDiff);

// 组件挂载后初始化
onMounted(() => {
  if (props.changedFiles.length > 0) {
    selectedFile.value = props.changedFiles[0].filename;
  }
  renderDiff();
});
</script>

<style scoped>
.git-diff-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.file-list {
  flex-shrink: 0;
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 12px;
  margin-bottom: 12px;
}

.file-list h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.file-item {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 4px;
}

.file-item:hover {
  background-color: var(--el-fill-color-light);
}

.file-item.active {
  background-color: var(--el-color-primary-light-9);
}

.filename {
  margin-left: 8px;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.diff-content {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  background-color: var(--el-bg-color);
}

.loading-container,
.error-message,
.no-diff-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  color: var(--el-text-color-secondary);
}

.loading-container .el-icon,
.error-message .el-icon,
.no-diff-message .el-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.error-message {
  color: var(--el-color-danger);
}

.diff-container {
  padding: 16px;
}

:deep(.d2h-file-header) {
  background-color: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-light);
}

:deep(.d2h-file-diff) {
  border-color: var(--el-border-color-light);
}

:deep(.d2h-code-linenumber) {
  background-color: var(--el-fill-color-light);
}

:deep(.d2h-info) {
  color: var(--el-text-color-regular);
}
</style> 