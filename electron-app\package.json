{"name": "pvv-electron", "version": "1.0.0", "description": "小说创作软件 - Electron版本", "main": "src/main/main.js", "author": "PVV Team", "license": "MIT", "private": true, "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite --port 13001", "dev:electron": "cross-env NODE_ENV=development electron src/main/main.js", "dev:test": "cross-env NODE_ENV=development electron src/main/main.js", "build": "npm run build:vite && npm run build:electron", "build:vite": "vite build", "build:electron": "cross-env NODE_ENV=production electron-builder", "build:win": "cross-env NODE_ENV=production electron-builder --win", "build:mac": "cross-env NODE_ENV=production electron-builder --mac", "build:linux": "cross-env NODE_ENV=production electron-builder --linux", "build:all": "cross-env NODE_ENV=production electron-builder --win --mac --linux", "postinstall": "electron-builder install-app-deps", "clean": "<PERSON><PERSON><PERSON> dist build", "lint": "eslint src --ext .js,.ts", "test": "jest"}, "dependencies": {"axios": "^1.6.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "node-fetch": "^3.3.2", "uuid": "^11.0.3", "vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "eslint": "^8.56.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0", "sass": "^1.69.5"}, "build": {"appId": "com.pvv.novel-creator", "productName": "PVV小说创作软件", "directories": {"output": "dist", "buildResources": "build"}, "files": ["src/main/**/*", "src/services/**/*", "src/preload/**/*", "dist/**/*", "node_modules/**/*"], "extraResources": [{"from": "resources", "to": "resources", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "build/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "category": "public.app-category.productivity", "hardenedRuntime": true, "gatekeeperAssess": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}, {"target": "rpm", "arch": ["x64", "arm64"]}], "icon": "build/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "PVV小说创作软件"}, "dmg": {"title": "PVV小说创作软件", "backgroundColor": "#ffffff"}, "publish": {"provider": "generic", "url": "https://your-update-server.com/"}}}