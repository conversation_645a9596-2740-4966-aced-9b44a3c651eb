<template>
  <div class="pilot-generator glass-bg">
    <!-- 顶部导航 -->
    <div class="page-header">
      <h1 class="page-title">剧情生成器</h1>
      <el-button
          class="back-button"
          @click="goBack"
          type="primary"
          plain
      >
        <el-icon><ArrowLeft /></el-icon>
        返回写作
      </el-button>
    </div>

    <!-- 功能区 -->
    <div class="function-area glass-bg">
      <div class="selectors-group">
        <!-- 书籍选择 -->
        <el-select
            v-model="selectedBookId"
            placeholder="选择书籍"
            class="selector-item"
            @change="handleBookChange"
        >
          <el-option
              v-for="book in bookStore.bookList"
              :key="book.id"
              :label="book.title"
              :value="book.id"
          />
        </el-select>

        <!-- 实体选择 -->
        <el-select
            v-model="selectedTemplates"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :disabled="!selectedBookId"
            placeholder="选择实体模板"
            class="selector-item"
        >
          <el-option
              v-for="template in templateList"
              :key="template.id"
              :label="template.name"
              :value="template.id"
          >
            <span class="entity-option">
              <span class="option-name">{{ template.name }}</span>
              <span class="option-count">{{ getEntityCount(template) }}个实体</span>
            </span>
          </el-option>
        </el-select>

        <!-- 场景选择 -->
        <el-select
            v-model="selectedScenes"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :disabled="!selectedBookId"
            placeholder="选择场景卡池"
            class="selector-item"
        >
          <el-option
              v-for="pool in sceneList"
              :key="pool.id"
              :label="pool.name"
              :value="pool.id"
          >
            <div class="scene-option">
              <span class="option-name">{{ pool.name }}</span>
              <div class="scene-option-controls">
                <el-input-number
                    v-model="sceneCounts[pool.id]"
                
                    :min="1"
                    :max="pool.scenes?.length || 1"
                    size="small"
                    @change="handleSceneCountChange(pool.id)"
                    @click.stop
                />
                <span class="option-count">{{ pool.scenes?.length || 0 }}个场景</span>
              </div>
            </div>
          </el-option>
        </el-select>

        <!-- 生成按钮 -->
        <el-button
            type="primary"
            :disabled="!canGenerate"
            @click="generatePlot"
            class="generate-button"
        >
          生成剧情
        </el-button>
        
        <!-- 添加灵感历史按钮 -->
        <el-button
            type="info"
            plain
            :disabled="!selectedBookId"
            @click="showHistoryDialog = true"
            class="history-button"
        >
          <el-icon><Collection /></el-icon>
          灵感历史
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 空状态提示 -->
      <el-empty
          v-if="!generatedPlot"
          description="请选择书籍、实体和场景后生成剧情"
          :image-size="200"
      >
        <template #image>
          <el-icon :size="64" class="empty-icon"><Edit /></el-icon>
        </template>
      </el-empty>
    </div>

    <!-- 剧情组合弹窗 -->
    <el-dialog
        v-model="showPlotDialog"
        :fullscreen="true"
        :show-close="false"
        class="plot-dialog"
    >
      <template #header>
        <div class="dialog-header glass-morphism">
          <h2>剧情组合</h2>
          <div class="dialog-actions">
            <el-button type="primary" class="action-button" @click="regeneratePlot">
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
            <el-button type="success" class="action-button" @click="savePlot">
              <el-icon><Check /></el-icon>
              保存剧情
            </el-button>
            <el-button class="close-button" @click="showPlotDialog = false">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="generatedPlot" class="plot-content glass-morphism">
        <div class="plot-section entities-section">
          <div class="section-header glass-card">
            <el-icon><User /></el-icon>
            <span>参与角色</span>
          </div>
          <div class="entity-cards">
            <div v-for="entity in selectedPlotEntities"
                 :key="entity.id"
                 class="entity-card glass-card">
              <div class="entity-header">
                <span class="entity-name">{{ entity.name }}</span>
              </div>
              <div class="entity-details">
                <div class="dimensions-container">
                  <div v-for="dimension in getEntityDimensions(entity.dimensions)"
                       :key="dimension.key"
                       class="dimension-item glass-effect">
                    <span class="dimension-label">{{ dimension.key }}：</span>
                    <span class="dimension-value">{{ dimension.value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="plot-section scenes-section">
          <div class="section-header glass-card">
            <el-icon><Location /></el-icon>
            <span>场景组合</span>
          </div>
          <div class="scene-cards">
            <div v-for="scene in selectedPlotScenes"
                 :key="scene.id"
                 class="scene-card glass-card">
              <div class="scene-header">
                <span class="scene-name">{{ scene.title }}</span>
              </div>
              <div class="scene-content">
                <div class="scene-description glass-effect" v-if="scene.description">
                  {{ scene.description }}
                </div>
                <div class="scene-elements" v-if="scene.elements">
                  <div class="element-item" v-for="(element, index) in scene.elements" :key="index">
                    <el-tag size="small" class="element-tag glass-tag">{{ element }}</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 添加感悟输入区域 -->
          <div class="insight-section glass-card">
            <div class="insight-header">
              <el-icon><Edit /></el-icon>
              <span>创作感悟</span>
            </div>
            <el-input
              v-model="plotInsight"
              type="textarea"
              :rows="4"
              placeholder="记录下这个剧情组合给你的灵感和感悟..."
              resize="none"
              class="insight-input glass-input"
            />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加灵感历史弹窗 -->
    <el-dialog
        v-model="showHistoryDialog"
        title="灵感历史"
        :fullscreen="true"
        class="history-dialog"
        style="user-select: none;"
    >
      <div class="history-container">
        <div class="history-content">
          <div class="history-list">
            <div v-if="!plotHistory.length" class="empty-history">
              暂无灵感历史记录
            </div>
            <template v-else>
              <div
                v-for="plot in paginatedPlots"
                :key="plot.id"
                class="history-item"
                :class="{ 'is-active': selectedPlot?.id === plot.id }"
                @click="selectedPlot = plot"
              >
                <div class="item-header">
                  <span class="item-title">{{ plot.title }}</span>
                  <div class="item-actions">
                    <el-switch
                      v-model="plot.is_used"
                      @change="(val) => handleUsedStateChange(plot.id, val)"
                      size="small"
                      inline-prompt
                      :active-text="'已用'"
                      :inactive-text="'未用'"
                      @click.stop
                    />
                    <el-button
                      type="danger"
                      size="small"
                      @click.stop="deletePlot(plot.id)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <div class="item-time">{{ new Date(plot.created_at).toLocaleString() }}</div>
              </div>
            </template>
          </div>
          <div class="history-detail" v-if="selectedPlot">
            <div class="sections-container">
              <div class="detail-section">
                <h3>参与角色</h3>
                <div class="entity-cards">
                  <div v-for="entity in selectedPlot.entities"
                       :key="entity.id"
                       class="entity-card">
                    <div class="entity-header">
                      <span class="entity-name">{{ entity.name }}</span>
                    </div>
                    <div class="entity-details">
                      <div class="dimensions-container" v-if="entity.dimensions">
                        <div v-for="(value, key) in entity.dimensions"
                             :key="key"
                             class="dimension-item">
                          <span class="dimension-label">{{ key }}：</span>
                          <span class="dimension-value">{{ value }}</span>
                        </div>
                      </div>
                      <div class="entity-description" v-if="entity.description">
                        {{ entity.description }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="detail-section">
                <h3>场景组合</h3>
                <div class="scene-cards">
                  <div v-for="scene in selectedPlot.scenes"
                       :key="scene.id"
                       class="scene-card">
                    <div class="scene-header">
                      <span class="scene-name">{{ scene.title }}</span>
                    </div>
                    <div class="scene-content">
                      <div class="scene-description" v-if="scene.description">
                        {{ scene.description }}
                      </div>
                      <div class="scene-elements" v-if="scene.entities && scene.entities.length">
                        <div class="element-item" v-for="(entity, index) in scene.entities" :key="index">
                          <el-tag size="small" class="element-tag">{{ entity }}</el-tag>
                        </div>
                      </div>
                      <div class="scene-content-text" v-if="scene.content">
                        {{ scene.content }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h3>创作感悟</h3>
              <div class="insight-content" @dblclick="startEditInsight">
                <template v-if="!isEditingInsight">
                  {{ selectedPlot.insight || '暂无感悟' }}
                </template>
                <template v-else>
                  <el-input
                    v-model="editingInsight"
                    type="textarea"
                    :rows="10"
                    resize="none"
                    placeholder="记录下这个剧情组合给你的灵感和感悟..."
                    ref="insightInput"
                  />
                  <div class="insight-actions">
                    <el-button type="primary" size="small" @click.stop="saveInsight">保存修改</el-button>
                    <el-button size="small" @click.stop="cancelEditInsight">取消</el-button>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div class="pagination-container" v-if="totalPlots">
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]"
            :total="totalPlots"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 在 template 中添加标题输入对话框 -->
    <el-dialog
        v-model="showTitleDialog"
        title="保存剧情"
        width="30%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @opened="handleTitleDialogOpened"
    >
      <el-form :model="plotForm" label-position="top" @submit.prevent="confirmSavePlot">
        <el-form-item label="剧情标题" required>
          <el-input
              ref="titleInputRef"
              v-model="plotForm.title"
              placeholder="请输入剧情标题"
              maxlength="50"
              show-word-limit
              @keyup.enter="confirmSavePlot"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTitleDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmSavePlot" :disabled="!plotForm.title">
            确认保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowLeft,
  Refresh,
  Check,
  User,
  Location,
  Edit,
  Close,
  Collection
} from '@element-plus/icons-vue'
import { useBookStore } from '@/stores/book'

const router = useRouter()
const bookStore = useBookStore()

// 数据状态
const selectedBookId = ref('')
const entityList = ref([])
const templateList = ref([])
const selectedTemplates = ref([])
const sceneList = ref([])
const selectedScenes = ref([])
const sceneCounts = ref({})
const generatedPlot = ref(null)

// 生成的剧情数据
const selectedPlotEntities = ref([])
const selectedPlotScenes = ref([])
const selectedPool = ref(null)

// 弹窗控制
const showPlotDialog = ref(false)
const showHistoryDialog = ref(false)
const showTitleDialog = ref(false)

// 添加感悟状态
const plotInsight = ref('')

// 灵感历史相关
const plotHistory = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalPlots = computed(() => plotHistory.value.length)
const selectedPlot = ref(null)

// 创作感悟编辑相关
const isEditingInsight = ref(false)
const editingInsight = ref('')
const insightInput = ref(null)

// 表单相关
const plotForm = ref({
  title: ''
})

// 添加对话框打开后的处理方法
const titleInputRef = ref(null)

const handleTitleDialogOpened = () => {
  nextTick(() => {
    titleInputRef.value?.input?.focus()
  })
}

// 计算属性
const groupedEntities = computed(() => {
  const groups = {}
  entityList.value.forEach(entity => {
    if (!groups[entity.template]) {
      groups[entity.template] = {
        template: entity.template,
        entities: []
      }
    }
    groups[entity.template].entities.push(entity)
  })
  return Object.values(groups)
})

const canGenerate = computed(() => {
  return selectedBookId.value && 
         selectedTemplates.value.length > 0 && 
         selectedScenes.value.length > 0
})

// 计算当前页显示的数据
const paginatedPlots = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return plotHistory.value.slice(start, end)
})

// 监听分页变化时，重置选中项
watch([currentPage, pageSize], () => {
  // 如果当前选中的项不在当前页中，则清空选中
  if (selectedPlot.value) {
    const isInCurrentPage = paginatedPlots.value.some(plot => plot.id === selectedPlot.value.id)
    if (!isInCurrentPage) {
      selectedPlot.value = null
    }
  }
  // 如果没有选中项且当前页有数据，默认选中第一项
  if (!selectedPlot.value && paginatedPlots.value.length > 0) {
    selectedPlot.value = paginatedPlots.value[0]
  }
})

// 方法
const goBack = () => {
  router.push({
    name: 'bookWriting'
  })
}

// 获取实体数量
const getEntityCount = (template) => {
  return entityList.value.filter(entity => entity.template_id === template.id).length
}

// 获取实体的维度信息
const getEntityDimensions = (entity) => {
  return Object.entries(entity)
    .filter(([key]) => !['id', 'name', 'template', 'template_id'].includes(key))
    .map(([key, value]) => ({ key, value }))
}

// 加载实体列表
const loadEntities = async (bookId) => {
  try {
    const response = await window.pywebview.api.book_controller.get_entities(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      entityList.value = result.data || []
    } else {
      ElMessage.error(result.message || '加载实体失败')
    }
  } catch (error) {
    ElMessage.error('加载实体失败：' + error.message)
  }
}

// 加载模板列表
const loadTemplates = async (bookId) => {
  if (!bookId) return
  
  try {
    const response = await window.pywebview.api.book_controller.get_templates(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      templateList.value = result.data || []
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    ElMessage.error('加载模板失败：' + error.message)
  }
}

// 加载灵感历史
const loadPlotHistory = async () => {
  try {
    const response = await window.pywebview.api.book_controller.get_plots(selectedBookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      plotHistory.value = result.data || []
      // 按创建时间倒序排序
      plotHistory.value.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      
      // 重置分页
      currentPage.value = 1
      
      // 如果有历史记录，默认选中第一条
      if (plotHistory.value.length > 0) {
        selectedPlot.value = plotHistory.value[0]
      } else {
        selectedPlot.value = null
      }
    } else {
      throw new Error(result.message || '加载失败')
    }
  } catch (error) {
    console.error('加载灵感历史失败:', error)
    ElMessage.error('加载失败：' + error.message)
  }
}

// 监听showHistoryDialog的变化
watch(showHistoryDialog, async (newValue) => {
  if (newValue && selectedBookId.value) {
    await loadPlotHistory()
  }
})

// 修改handleBookChange，添加加载历史记录
const handleBookChange = async (bookId) => {
  try {
    // 1. 清空当前选择状态
    selectedTemplates.value = []
    selectedScenes.value = []
    sceneCounts.value = {}
    
    // 2. 重置生成的剧情数据
    generatedPlot.value = null
    selectedPlotEntities.value = []
    selectedPlotScenes.value = []
    
    // 3. 并行加载新书籍的所有必要数据
    await Promise.all([
      loadTemplates(bookId),
      loadEntities(bookId),
      loadScenes(bookId)
    ])
    
    // 4. 加载历史记录
    await loadPlotHistory()
    
    ElMessage.success('书籍数据加载完成')
  } catch (error) {
    console.error('加载书籍数据失败:', error)
    ElMessage.error('加载书籍数据失败，请重试')
  }
}

// 加载场景列表
const loadScenes = async (bookId) => {
  if (!bookId) return

  try {
    const response = await window.pywebview.api.book_controller.get_scene_events(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      // 确保数据结构完整
      const data = result.data || {}
      const pools = Array.isArray(data.pools) ? data.pools : []
      
      // 过滤掉没有场景的卡池，并确保每个场景都有必要的属性
      sceneList.value = pools.filter(pool => Array.isArray(pool.scenes) && pool.scenes.length > 0)
        .map(pool => ({
          id: pool.id,
          name: pool.name || '未命名卡池',
          scenes: pool.scenes.map(scene => ({
            id: scene.id,
            title: scene.title,
            description: scene.description,
            
          }))
        }))
      
    } else {
      console.error('加载场景失败:', result.message)
      ElMessage.error(result.message || '加载场景失败')
      sceneList.value = [] // 重置为空数组
    }
  } catch (error) {
    console.error('加载场景失败:', error)
    ElMessage.error('加载场景失败：' + error.message)
    sceneList.value = [] // 重置为空数组
  }
}

// 工具函数：从数组中随机选择指定数量的元素
const getRandomElements = (array, count) => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// 生成剧情
const generatePlot = () => {
  // 从每个选中的模板中随机选择一个实体
  const randomEntities = selectedTemplates.value.map(templateId => {
    const templateEntities = entityList.value.filter(e => e.template_id === templateId)
    return getRandomElements(templateEntities, 1)[0]
  }).filter(Boolean)
  
  // 从每个选中的卡池中随机选择指定数量的场景
  const selectedPoolScenes = selectedScenes.value.map(poolId => {
    const pool = sceneList.value.find(p => p.id === poolId)
    if (!pool) return null
    
    const count = sceneCounts.value[poolId] || 1
    const randomScenes = getRandomElements(pool.scenes, count)
    return {
      pool,
      scenes: randomScenes
    }
  }).filter(Boolean)
  
  // 使用所有选中卡池的场景
  selectedPlotEntities.value = randomEntities
  selectedPlotScenes.value = selectedPoolScenes.flatMap(ps => ps.scenes)
  selectedPool.value = null // 不再只选择一个卡池

  generatedPlot.value = {
    entities: randomEntities,
    scenes: selectedPlotScenes.value
  }
  
  // 显示弹窗
  showPlotDialog.value = true
}

// 重新生成剧情
const regeneratePlot = () => {
  generatePlot()
}

// 保存剧情
const savePlot = () => {
  // 重置表单
  plotForm.value.title = ''
  // 显示标题输入对话框
  showTitleDialog.value = true
}

// 添加确认保存的方法
const confirmSavePlot = async () => {
  try {
    if (!selectedBookId.value) {
      ElMessage.error('请先选择书籍')
      return
    }

    const plotData = {
      title: plotForm.value.title.trim(),
      entities: selectedPlotEntities.value.map(entity => ({
        id: entity.id,
        name: entity.name,
        template_id: entity.template_id,
        dimensions: entity.dimensions || {},
        description: entity.description || ''
      })),
      scenes: selectedPlotScenes.value.map(scene => ({
        id: scene.id,
        title: scene.title,
        description: scene.description || '',
        entities: scene.entities || [],
        content: scene.content || ''
      })),
      insight: plotInsight.value,
      is_used: false, // 添加使用状态字段，默认为未使用
      created_at: new Date().toISOString()
    }

    const response = await window.pywebview.api.book_controller.save_plot(selectedBookId.value, plotData)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('保存成功')
      await loadPlotHistory()
      showPlotDialog.value = false
      showTitleDialog.value = false
      // 清空感悟输入
      plotInsight.value = ''
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存剧情组合失败:', error)
    ElMessage.error('保存失败：' + error.message)
  }
}

// 删除剧情历史
const deletePlot = async (plotId) => {
  try {
    await ElMessageBox.confirm('确定要删除这条灵感记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await window.pywebview.api.book_controller.delete_plot(selectedBookId.value, plotId)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('删除成功')
      
      // 更新本地数据
      plotHistory.value = plotHistory.value.filter(plot => plot.id !== plotId)
      
      // 如果删除后当前页没有数据了，且不是第一页，则跳转到上一页
      if (paginatedPlots.value.length === 0 && currentPage.value > 1) {
        currentPage.value--
      }
      
      // 如果删除的是当前选中的剧情，选择当前页的第一项
      if (selectedPlot.value?.id === plotId) {
        selectedPlot.value = paginatedPlots.value[0] || null
      }
    } else {
      throw new Error(result.message || '删除失败')
    }
  } catch (error) {
    if (error.toString().includes('cancel')) return
    console.error('删除剧情组合失败:', error)
    ElMessage.error('删除失败：' + error.message)
  }
}

// 添加场景数量变化处理方法
const handleSceneCountChange = (poolId) => {
  const pool = sceneList.value.find(p => p.id === poolId)
  if (!pool) return
  
  // 确保数量不超过可用场景数
  if (sceneCounts.value[poolId] > pool.scenes.length) {
    sceneCounts.value[poolId] = pool.scenes.length
  }
  // 确保至少选择一个场景
  if (sceneCounts.value[poolId] < 1) {
    sceneCounts.value[poolId] = 1
  }
}

// 开始编辑感悟
const startEditInsight = () => {
  editingInsight.value = selectedPlot.value.insight || ''
  isEditingInsight.value = true
  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    insightInput.value?.input?.focus()
  })
}

// 取消编辑感悟
const cancelEditInsight = () => {
  isEditingInsight.value = false
  editingInsight.value = ''
}

// 保存感悟修改
const saveInsight = async () => {
  try {
    if (!selectedPlot.value || !selectedBookId.value) return

    const plotData = {
      ...selectedPlot.value,
      insight: editingInsight.value
    }
    console.log(plotData)
    const response = await window.pywebview.api.book_controller.update_plot(
      selectedBookId.value,
      selectedPlot.value.id,
      plotData
    )
    
    // 确保正确解析响应
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      ElMessage.success('保存成功')
      // 更新本地数据
      selectedPlot.value = { ...plotData }  // 使用完整的更新后数据
      // 更新列表中的数据
      const index = plotHistory.value.findIndex(p => p.id === selectedPlot.value.id)
      if (index !== -1) {
        plotHistory.value[index] = { ...plotData }  // 使用完整的更新后数据
      }
      // 退出编辑模式
      isEditingInsight.value = false
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存感悟失败:', error)
    ElMessage.error(error.message || '保存失败')
  }
}

// 添加处理使用状态变更的方法
const handleUsedStateChange = async (plotId, isUsed) => {
  try {
    const plot = plotHistory.value.find(p => p.id === plotId)
    if (!plot) return

    const plotData = {
      ...plot,
      is_used: isUsed
    }

    const response = await window.pywebview.api.book_controller.update_plot(
      selectedBookId.value,
      plotId,
      plotData
    )

    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success(isUsed ? '已标记为已使用' : '已标记为未使用')
      // 更新本地数据
      const index = plotHistory.value.findIndex(p => p.id === plotId)
      if (index !== -1) {
        plotHistory.value[index] = { ...plotData }
      }
      // 如果是当前选中的剧情，也更新选中的数据
      if (selectedPlot.value?.id === plotId) {
        selectedPlot.value = { ...plotData }
      }
    } else {
      throw new Error(result.message || '更新状态失败')
    }
  } catch (error) {
    console.error('更新使用状态失败:', error)
    ElMessage.error('更新状态失败：' + error.message)
    // 恢复原状态
    const plot = plotHistory.value.find(p => p.id === plotId)
    if (plot) {
      plot.is_used = !isUsed
    }
  }
}

// 添加分页处理方法
const handleSizeChange = (val) => {
  pageSize.value = val
  // 重置到第一页
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 生命周期钩子
onMounted(async () => {
  try {
    // 确保书籍列表已加载
    await bookStore.loadBooks()
    // 如果有书籍列表，默认选择第一本书
    if (bookStore.bookList?.length > 0) {
      selectedBookId.value = bookStore.bookList[0].id
      await Promise.all([
        loadTemplates(selectedBookId.value),
        loadEntities(selectedBookId.value),
        loadScenes(selectedBookId.value)
      ])
    }
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }
})
</script>

<style lang="scss" scoped>
.pilot-generator {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 100%;
  position: relative;
  background: var(--el-bg-color);

  &.glass-bg {
    background: rgba(var(--el-bg-color-rgb), 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

.page-header {
  user-select: none;
  margin-bottom: 24px;
  padding: 20px 28px;
  background: var(--el-bg-color);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;

  .page-title {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 20px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(var(--el-color-primary-rgb), 0.2);
    background: rgba(var(--el-color-primary-rgb), 0.1);
    color: var(--el-color-primary);

    &:hover {
      background: var(--el-color-primary);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
    }
  }
}

.function-area {
  padding: 16px;
  border-radius: 8px;
  background: rgba(var(--el-bg-color-overlay-rgb), 0.7);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  .selectors-group {
    display: flex;
    gap: 12px;
    align-items: center;

    .selector-item {
      min-width: 200px;
    }

    .generate-button {
      margin-left: auto;
      padding: 8px 24px;
      font-weight: 500;
      border-radius: 6px;
      background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
      border: none;
      transition: all 0.3s ease;

      &:not(:disabled):hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
      }

      &:disabled {
        background: var(--el-color-info-light-5);
        opacity: 0.7;
      }
    }

    .history-button {
      margin-left: 12px;
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 10px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s ease;
      border: 1px solid rgba(var(--el-color-primary-rgb), 0.2);
      background: rgba(var(--el-color-primary-rgb), 0.1);
      color: var(--el-color-primary);
      
      &:hover {
        background: var(--el-color-primary);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
      }
      
      .el-icon {
        font-size: 16px;
      }
    }
  }
}

.main-content {
  user-select: none;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 16px;
  background: rgba(var(--el-bg-color-overlay-rgb), 0.7);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  :deep(.el-empty) {
    padding: 40px;
    
    .empty-icon {
      color: var(--el-text-color-secondary);
    }
  }
}

.result-area {
  display: none; // 隐藏原有的结果区域
}

.plot-dialog {
  :deep(.el-dialog) {
    background: var(--el-bg-color-overlay);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin: 0 !important;
  }

  :deep(.el-dialog__header) {
    margin: 0;
    padding: 0;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0;
    margin: 0;
    overflow: hidden;
  }

  .dialog-header {
    padding: 16px 24px;
    margin: 0;
    background: rgba(var(--el-bg-color-rgb), 0.8);
    border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .dialog-actions {
      display: flex;
      gap: 12px;
      align-items: center;

      .action-button {
        height: 36px;
        padding: 0 20px;
        font-size: 15px;
        font-weight: 500;
        border-radius: 18px;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: all 0.3s ease;
        
        .el-icon {
          font-size: 16px;
        }
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
        }
      }

      .close-button {
        width: 36px;
        height: 36px;
        padding: 0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--el-color-danger-light-9);
        border-color: var(--el-color-danger-light-5);
        color: var(--el-color-danger);
        
        .el-icon {
          font-size: 18px;
        }
        
        &:hover {
          background: var(--el-color-danger);
          color: white;
          transform: rotate(90deg);
        }
      }
    }
  }

  .plot-content {
    padding: 16px;
    height: calc(100vh - 69px);
    display: flex;
    gap: 16px;
    background: rgba(var(--el-bg-color-rgb), 0.6);
    overflow: auto;

    .plot-section {
      flex: 1;
      height: 100%;
      background: rgba(var(--el-bg-color-rgb), 0.8);
      border-radius: 16px;
      padding: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .section-header {
        flex-shrink: 0;
        margin-bottom: 12px;
        padding: 10px 16px;
        background: rgba(var(--el-color-primary-rgb), 0.1);
        border-radius: 12px;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;

        .el-icon {
          font-size: 20px;
          color: var(--el-color-primary);
        }

        span {
          background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .entity-cards, .scene-cards {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 4px 4px 4px 8px;
        margin-right: -4px;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 12px;
        align-content: start;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--el-color-primary-light-5);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &:hover::-webkit-scrollbar-thumb {
          background: var(--el-color-primary-light-3);
        }
      }
    }

    .scenes-section {
      .scene-cards {
        margin-bottom: 16px;
      }

      .insight-section {
        flex-shrink: 0;
        margin-top: 0;
        padding: 16px;
        background: rgba(var(--el-bg-color-rgb), 0.8);
        border-radius: 12px;
        border: 1px solid rgba(var(--el-border-color-rgb), 0.1);

        .insight-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          padding-bottom: 10px;
          border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);

          .el-icon {
            font-size: 18px;
            color: var(--el-color-primary);
          }

          span {
            font-size: 16px;
            font-weight: 600;
            background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }

        .insight-input {
          :deep(.el-textarea__inner) {
            background: rgba(var(--el-bg-color-rgb), 0.8);
            border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
            border-radius: 10px;
            padding: 10px;
            font-size: 14px;
            line-height: 1.6;
            resize: none;
            transition: all 0.3s ease;
            height: 100px;

            &:focus {
              border-color: var(--el-color-primary);
              box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
            }

            &::placeholder {
              color: var(--el-text-color-placeholder);
            }
          }
        }
      }
    }
  }
}

.entity-option,
.scene-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px;
  gap: 8px;

  .option-name {
    flex: 1;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .scene-option-controls {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-input-number {
      width: 80px;
    }
  }

  .option-count {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
    background: var(--el-fill-color-light);
    color: var(--el-text-color-secondary);
    white-space: nowrap;
  }
}

:deep(.el-select),
:deep(.el-input) {
  .el-input__wrapper {
    background: transparent;
    box-shadow: 0 0 0 1px var(--el-border-color-light);

    &:hover {
      box-shadow: 0 0 0 1px var(--el-border-color);
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) !important;
    }
  }
}

:deep(.el-select-dropdown__item) {
  padding: 8px 0;
  
  &.selected {
    .entity-option,
    .scene-option {
      .option-name {
        color: var(--el-color-primary);
      }
      
      .option-count {
        background: rgba(var(--el-color-primary-rgb), 0.1);
        color: var(--el-color-primary);
      }
    }
  }

  &:hover {
    .entity-option,
    .scene-option {
      .option-count {
        background: var(--el-fill-color);
      }
    }
  }
}

// 修复多选标签的样式
:deep(.el-select .el-select__tags) {
  .el-tag {
    background-color: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary-light-5);
    color: var(--el-color-primary);
    border-radius: 4px;
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
    
    .el-tag__close {
      color: var(--el-color-primary);
      
      &:hover {
        background-color: var(--el-color-primary);
        color: white;
      }
    }
  }
}

.history-dialog {
  user-select: none !important;
  :deep(.el-dialog) {
   
    height: 100vh;
    margin: 0 !important;
    display: flex;
    flex-direction: column;
    overflow: hidden !important;
  }

  :deep(.el-dialog__body) {

    flex: 1;
    overflow: hidden !important;
    padding: 0;
  }
}

.history-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden !important;
}

.history-content {
  flex: 1;
  display: flex;
  gap: 12px;
  padding: 16px;
  overflow: hidden !important;
  min-height: 0;
}

.history-list {
  user-select: none;
  width: 260px;
  height: calc(80vh - 140px);  // 固定高度，减去头部和分页的高度
  overflow-y: auto;
  overflow-x: hidden;
  border-right: 1px solid var(--el-border-color-light);
  padding-right: 16px;

  .empty-history {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 3px;
  }
}

.history-detail {
  flex: 1;
  height: calc(80vh - 140px);  // 与history-list保持一致的高度
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 12px;

  h3 {
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
    font-size: 16px;
    font-weight: 600;
    user-select: none;
  }

  .sections-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 24px;
  }

  .entity-cards, .scene-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &:hover::-webkit-scrollbar-thumb {
    background-color: var(--el-color-primary-light-5);
  }
}

.history-item {
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: var(--el-fill-color-darker);
  }

  &.is-active {
    background-color: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary);
  }

  .item-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .item-title {
      font-size: 14px;
      font-weight: bold;
      color: var(--el-text-color-primary);
      flex: 1;
      margin-right: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .item-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .el-switch {
        --el-switch-on-color: var(--el-color-success);
      }

      .el-button {
        width: 0;
        padding: 0;
        margin: 0;
        border: none;
        overflow: hidden;
        transition: all 0.3s ease;
        opacity: 0;
      }
    }
  }

  &:hover {
    .item-actions {
      .el-button {
        width: auto;
        padding: 5px 12px;
        margin-left: 8px;
        border: 1px solid var(--el-button-border-color);
        opacity: 1;
      }
    }
  }

  .item-time {
    font-size: 11px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  .item-title {
    font-size: 14px;
    font-weight: bold;
    color: var(--el-text-color-primary);
    flex: 1;
    margin-right: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .item-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    // 不设置 opacity，使按钮始终可见
    
    .el-switch {
      --el-switch-on-color: var(--el-color-success);
    }
  }
}

.detail-section {
  margin-bottom: 24px;

  h3 {
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
  }
}

.entity-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.entity-card {
  height: 480px; // 设置固定高度
  background: var(--el-bg-color);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-4px);
    border-color: var(--el-color-primary);
    box-shadow: 0 8px 24px rgba(var(--el-color-primary-rgb), 0.15);
  }

  .entity-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: -24px -24px 20px -24px;
    padding: 20px 24px;
    background: var(--el-color-primary-light-9);
    border-bottom: 1px solid var(--el-border-color-light);
    position: sticky;
    top: 0;
    z-index: 1;

    .entity-name {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-color-primary);
      letter-spacing: 0.5px;
      text-align: center;
    }
  }

  .entity-details {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--el-border-color);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &:hover::-webkit-scrollbar-thumb {
      background-color: var(--el-color-primary-light-5);
    }

    .dimensions-container {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .dimension-item {
        padding: 12px 16px;
        background: var(--el-fill-color-light);
        border-radius: 10px;
        border: 1px solid var(--el-border-color-light);
        transition: all 0.2s ease;

        &:hover {
          background: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary-light-5);
          transform: translateX(4px);
        }

        .dimension-label {
          font-size: 15px;
          font-weight: 600;
          color: var(--el-color-primary);
          margin-bottom: 4px;
          display: block;
        }

        .dimension-value {
          font-size: 14px;
          color: var(--el-text-color-primary);
          line-height: 1.6;
        }
      }
    }
  }
}

.scene-cards {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  padding-right: 12px;
}

.scene-card {
  background: var(--el-bg-color);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-4px);
    border-color: var(--el-color-primary);
    box-shadow: 0 8px 24px rgba(var(--el-color-primary-rgb), 0.15);
  }

  .scene-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: -24px -24px 20px -24px;
    padding: 20px 24px;
    background: var(--el-color-primary-light-9);
    border-bottom: 1px solid var(--el-border-color-light);

    .scene-name {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }

  .scene-content {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .scene-description {
      font-size: 15px;
      color: var(--el-text-color-primary);
      line-height: 1.6;
      padding: 12px 16px;
      background: var(--el-fill-color-light);
      border-radius: 10px;
      border: 1px solid var(--el-border-color-light);
    }

    .scene-elements {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 12px 16px;
      background: var(--el-fill-color-light);
      border-radius: 10px;

      .element-tag {
        background: var(--el-color-primary-light-9);
        border: 1px solid var(--el-color-primary-light-5);
        color: var(--el-color-primary);
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-2px);
          background: var(--el-color-primary);
          color: white;
        }
      }
    }
  }
}

.insight-section {
  margin-top: 24px;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 12px;
  border: 1px solid var(--el-border-color-light);
  
  .insight-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--el-border-color-light);
    
    .el-icon {
      font-size: 18px;
      color: var(--el-color-primary);
    }
    
    span {
      font-size: 16px;
      font-weight: 600;
      background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  
  .insight-input {
    :deep(.el-textarea__inner) {
      background: var(--el-fill-color-light);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
      line-height: 1.6;
      resize: none;
      
      &:focus {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
      }
      
      &::placeholder {
        color: var(--el-text-color-placeholder);
      }
    }
  }
}

.insight-content {
  padding: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 12px;
  white-space: pre-wrap;
  font-size: 20px;
  line-height: 1.5;
  min-height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--el-fill-color);
  }

  :deep(.el-textarea__inner) {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 12px;
    padding: 12px;
    font-size: 16px;
    line-height: 1.6;
    resize: none;
    
    &:focus {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
    }
  }
}

.insight-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-light);
}
.insight-actions .el-button{
  font-size: 18px;
}

.pagination-container {
  height: 70px;
  flex-shrink: 0;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: center;
  align-items: center;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

// 添加毛玻璃效果的通用类
.glass-morphism {
  background: rgba(var(--el-bg-color-rgb), 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
}

.glass-card {
  background: rgba(var(--el-bg-color-rgb), 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.glass-effect {
  background: rgba(var(--el-bg-color-rgb), 0.8);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
}

.glass-tag {
  background: rgba(var(--el-color-primary-rgb), 0.1);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(var(--el-color-primary-rgb), 0.2);
}

.glass-input {
  :deep(.el-input__inner) {
    background: rgba(var(--el-bg-color-rgb), 0.8);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  }
}
</style>