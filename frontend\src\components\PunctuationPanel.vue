<template>
  <div 
    v-if="visible" 
    class="punctuation-panel"
    :style="panelStyle"
    @mousedown="handleMouseDown"
  >
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="panel-title">
        <el-icon><Edit /></el-icon>
        <span>标点符号</span>
      </div>
      <div class="panel-controls">
        <el-tooltip content="拖拽移动" placement="top">
          <el-icon class="drag-handle" @mousedown="initDrag"><Rank /></el-icon>
        </el-tooltip>
        <el-tooltip content="关闭面板" placement="top">
          <el-icon class="close-btn" @click="$emit('close')"><Close /></el-icon>
        </el-tooltip>
      </div>
    </div>

    <!-- 标点符号内容区域 -->
    <div class="panel-content">
      <!-- 中文常用 -->
      <div class="punctuation-category">
        <div class="category-title">中文常用</div>
        <div class="punctuation-grid">
          <button
            v-for="punct in chinesePunctuations"
            :key="punct.symbol"
            :class="['punctuation-btn', { 'pair-punctuation': punct.type === 'pair' }]"
            :title="punct.name + (punct.type === 'pair' ? ' (成对插入)' : '')"
            @click="insertPunctuation(punct)"
          >
            {{ punct.symbol }}
          </button>
        </div>
      </div>



      <!-- 功能按键 -->
      <div class="punctuation-category">
        <div class="category-title">功能按键</div>
        <div class="punctuation-grid">
          <button
            v-for="key in functionKeys"
            :key="key.symbol"
            :class="['punctuation-btn', 'function-key']"
            :title="key.name"
            @click="insertFunctionKey(key)"
          >
            {{ key.symbol }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Edit, Rank, Close } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editor: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'insert'])

// 面板位置状态
const panelPosition = ref({ x: 100, y: 100 })
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })

// 标点符号数据
const chinesePunctuations = ref([
  // 基础标点
  { symbol: '，', name: '逗号', type: 'single' },
  { symbol: '。', name: '句号', type: 'single' },
  { symbol: '？', name: '问号', type: 'single' },
  { symbol: '！', name: '感叹号', type: 'single' },
  { symbol: '；', name: '分号', type: 'single' },
  { symbol: '：', name: '冒号', type: 'single' },
  { symbol: '、', name: '顿号', type: 'single' },
  { symbol: '…', name: '省略号', type: 'single' },
  // 中文引号和括号
  { symbol: '“”', name: '中文双引号', type: 'pair', left: '“', right: '”' },
  { symbol: '\'\’', name: '中文单引号', type: 'pair', left: '\‘', right: '\’' },
  { symbol: '（）', name: '圆括号', type: 'pair', left: '（', right: '）' },
  { symbol: '【】', name: '方括号', type: 'pair', left: '【', right: '】' },
  { symbol: '《》', name: '书名号', type: 'pair', left: '《', right: '》' },
  { symbol: '〈〉', name: '单书名号', type: 'pair', left: '〈', right: '〉' },
  // 特殊符号
  { symbol: '——', name: '破折号', type: 'single' },
  { symbol: '·', name: '间隔号', type: 'single' }
])



const functionKeys = ref([
  { symbol: '删除', name: '删除键 (Backspace)', type: 'function', action: 'backspace' },
  { symbol: '回车', name: '回车键 (Enter)', type: 'function', action: 'enter' }
])

// 计算面板样式
const panelStyle = computed(() => ({
  left: `${panelPosition.value.x}px`,
  top: `${panelPosition.value.y}px`
}))

// 插入标点符号
const insertPunctuation = (punctuation) => {
  if (!props.editor || !props.editor.commands) return

  if (punctuation.type === 'pair') {
    // 成对标点符号：插入一对并将光标定位在中间
    const content = punctuation.left + punctuation.right
    props.editor.commands.insertContent(content)

    // 将光标向左移动一个字符，定位在两个符号中间
    const { state, dispatch } = props.editor.view
    const { tr } = state
    const newPos = state.selection.head - 1
    const newSelection = state.selection.constructor.near(state.doc.resolve(newPos))
    dispatch(tr.setSelection(newSelection))

    emit('insert', content)
  } else {
    // 单个标点符号：直接插入
    props.editor.commands.insertContent(punctuation.symbol)
    emit('insert', punctuation.symbol)
  }

  props.editor.commands.focus()
}

// 模拟键盘事件函数
const simulateKeyboardEvent = (keyCode) => {
  if (!props.editor || !props.editor.view) return

  const view = props.editor.view

  // 创建键盘事件，使用更完整的事件属性
  const event = new KeyboardEvent('keydown', {
    key: keyCode,
    code: keyCode === 'Backspace' ? 'Backspace' : 'Enter',
    keyCode: getKeyCodeNumber(keyCode),
    which: getKeyCodeNumber(keyCode),
    bubbles: true,
    cancelable: true,
    composed: true
  })

  // 直接通过编辑器视图处理键盘事件
  // 这样能确保编辑器的所有插件和处理器都能正确响应
  view.dom.dispatchEvent(event)

  // 如果事件没有被处理，尝试手动触发编辑器的键盘处理
  if (!event.defaultPrevented) {
    // 对于 ProseMirror 编辑器，直接调用 handleKeyDown
    if (view.someProp && typeof view.someProp === 'function') {
      view.someProp('handleKeyDown', (f) => f(view, event))
    }
  }
}

// 获取键码数字
const getKeyCodeNumber = (keyCode) => {
  const keyCodes = {
    'Backspace': 8,
    'Enter': 13
  }
  return keyCodes[keyCode] || 0
}

// 插入功能按键
const insertFunctionKey = (key) => {
  if (!props.editor || !props.editor.commands) return

  switch (key.action) {
    case 'backspace':
      // 使用编辑器原生的删除命令，完全模拟键盘行为
      const { from, empty } = props.editor.state.selection

      if (!empty) {
        // 如果有选中内容，删除选中内容
        props.editor.commands.deleteSelection()
      } else {
        // 使用 joinBackward 命令，这个命令会智能处理：
        // 1. 在段落开头时：合并到上一个段落
        // 2. 在段落中间时：删除前一个字符
        // 3. 在列表项开头时：合并到上一个列表项或退出列表
        if (!props.editor.commands.joinBackward()) {
          // 如果 joinBackward 失败（比如在文档开头），尝试删除前一个字符
          if (from > 0) {
            props.editor.commands.deleteRange({ from: from - 1, to: from })
          }
        }
      }
      break
    case 'enter':
      // 使用编辑器原生的回车命令
      props.editor.commands.splitBlock()
      break
  }

  props.editor.commands.focus()
  emit('insert', key.symbol)
}

// 拖拽功能
const initDrag = (event) => {
  event.preventDefault()
  isDragging.value = true
  
  const rect = event.currentTarget.closest('.punctuation-panel').getBoundingClientRect()
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

const handleDrag = (event) => {
  if (!isDragging.value) return
  
  panelPosition.value = {
    x: event.clientX - dragOffset.value.x,
    y: event.clientY - dragOffset.value.y
  }
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 防止面板被选中
const handleMouseDown = (event) => {
  event.preventDefault()
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
})

// 初始化面板位置
onMounted(() => {
  // 设置初始位置在屏幕中央偏右
  panelPosition.value = {
    x: window.innerWidth - 320,
    y: 150
  }
})
</script>

<style lang="scss" scoped>
.punctuation-panel {
  position: fixed;
  width: 300px;
  max-height: 520px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  z-index: 9999;
  user-select: none;
  overflow: hidden;
  animation: panelFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: contrast(1.05) brightness(0.98);

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: linear-gradient(135deg,
      var(--el-fill-color-light) 0%,
      var(--el-fill-color) 100%);
    border-bottom: 1px solid var(--el-border-color-lighter);
    cursor: move;
    backdrop-filter: blur(4px);

    .panel-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: 0.5px;

      .el-icon {
        font-size: 18px;
        color: var(--el-color-primary);
      }
    }

    .panel-controls {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        font-size: 16px;
        color: var(--el-text-color-regular);
        cursor: pointer;
        padding: 2px;
        border-radius: 4px;
        transition: all 0.2s;

        &:hover {
          color: var(--el-color-primary);
          background: var(--el-fill-color);
        }

        &.drag-handle {
          cursor: move;
        }
      }
    }
  }

  .panel-content {
    padding: 16px;
    max-height: 440px;
    overflow-y: auto;
    background: var(--el-bg-color-page);

    .punctuation-category {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-title {
        font-size: 13px;
        color: var(--el-text-color-regular);
        margin-bottom: 12px;
        font-weight: 600;
        letter-spacing: 0.3px;
        text-transform: uppercase;
        opacity: 0.8;
      }

      .punctuation-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;

        // 功能按键区域的特殊布局
        &:has(.function-key) {
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;
          justify-items: center;
        }

        .punctuation-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 46px;
          height: 34px;
          background: linear-gradient(135deg,
            var(--el-fill-color-lighter) 0%,
            var(--el-fill-color-light) 100%);
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 8px;
          font-size: 20px;
          font-weight: 600;
          color: #000000;
          cursor: pointer;
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          // 成对标点符号的特殊样式
          &.pair-punctuation {
            background: #e8f5e8;
            border-color: #b3d9b3;
            color: #000000;
            font-weight: 700;

            &::after {
              content: '成对';
              position: absolute;
              top: -6px;
              right: -6px;
              background: #67c23a;
              color: white;
              font-size: 9px;
              padding: 2px 5px;
              border-radius: 10px;
              line-height: 1;
              font-weight: 700;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              letter-spacing: 0.2px;
            }
          }

          &:hover {
            background: #e6f3ff;
            border-color: #b3d9ff;
            color: #000000;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 128, 255, 0.25);

            &.pair-punctuation {
              background: #d9f2d9;
              border-color: #99d699;
              color: #000000;
            }
          }

          &:active {
            transform: translateY(-1px);
            background: #cce6ff;
            color: #000000;
            box-shadow: 0 2px 6px rgba(64, 128, 255, 0.3);

            &.pair-punctuation {
              background: #ccebcc;
              color: #000000;
            }
          }

          // 功能按键的特殊样式
          &.function-key {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
            font-weight: 600;
            font-size: 14px;
            width: 70px;
            height: 50px;
            padding: 12px 16px;
            min-width: 70px;

            &::after {
              content: '功能';
              position: absolute;
              top: -6px;
              right: -6px;
              background: #e6a23c;
              color: white;
              font-size: 9px;
              padding: 2px 5px;
              border-radius: 10px;
              line-height: 1;
              font-weight: 700;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              letter-spacing: 0.2px;
            }

            &:hover {
              background: #ffeaa7;
              border-color: #fdcb6e;
              color: #856404;
              box-shadow: 0 6px 16px rgba(255, 193, 7, 0.3);
              transform: translateY(-2px) scale(1.05);
            }

            &:active {
              background: #fdcb6e;
              box-shadow: 0 3px 8px rgba(255, 193, 7, 0.4);
              transform: translateY(-1px) scale(1.02);
            }
          }
        }
      }
    }
  }

  // 滚动条样式
  :deep(.panel-content::-webkit-scrollbar) {
    width: 8px;
  }

  :deep(.panel-content::-webkit-scrollbar-track) {
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
    margin: 4px 0;
  }

  :deep(.panel-content::-webkit-scrollbar-thumb) {
    background: var(--el-border-color-light);
    border-radius: 4px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.2s;

    &:hover {
      background: var(--el-border-color);
    }
  }
}

@keyframes panelFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 暗色主题样式 */
html.dark .punctuation-panel {
  background: rgba(26, 26, 26, 0.95);
  border-color: #333333;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.2);
  filter: contrast(1.1) brightness(0.95) hue-rotate(-5deg);

  .panel-header {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.04) 100%);
    border-bottom-color: #2a2a2a;
  }

  .panel-content {
    background: rgba(26, 26, 26, 0.6);

    .category-title {
      color: #e0e0e0;
      opacity: 0.9;
    }

    .punctuation-btn {
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%) !important;
      border-color: #333333 !important;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
      color: #ffffff !important;

      &.pair-punctuation {
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.12) 0%,
          rgba(255, 255, 255, 0.08) 100%);
        border-color: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);

        &::after {
          background: rgba(64, 158, 255, 0.8);
          color: rgba(255, 255, 255, 0.95);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
        }
      }

      &:hover {
        background: linear-gradient(135deg,
          rgba(64, 128, 255, 0.2) 0%,
          rgba(64, 128, 255, 0.15) 100%) !important;
        box-shadow: 0 4px 12px rgba(64, 128, 255, 0.3) !important;
        color: #ffffff !important;

        &.pair-punctuation {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.18) 0%,
            rgba(255, 255, 255, 0.14) 100%);
          border-color: rgba(64, 158, 255, 0.4);
          color: rgba(255, 255, 255, 0.95);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
      }

      &:active {
        background: linear-gradient(135deg,
          rgba(64, 128, 255, 0.25) 0%,
          rgba(64, 128, 255, 0.2) 100%) !important;
        color: #ffffff !important;

        &.pair-punctuation {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.12) 100%);
          border-color: rgba(64, 158, 255, 0.3);
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
        }
      }

      &.function-key {
        background: linear-gradient(135deg,
          rgba(255, 193, 7, 0.15) 0%,
          rgba(255, 193, 7, 0.1) 100%);
        border-color: rgba(255, 193, 7, 0.3);
        color: rgba(255, 193, 7, 0.9);

        &::after {
          background: rgba(255, 193, 7, 0.8);
          color: rgba(0, 0, 0, 0.8);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
        }

        &:hover {
          background: linear-gradient(135deg,
            rgba(255, 193, 7, 0.2) 0%,
            rgba(255, 193, 7, 0.15) 100%);
          box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
        }

        &:active {
          background: linear-gradient(135deg,
            rgba(255, 193, 7, 0.25) 0%,
            rgba(255, 193, 7, 0.2) 100%);
          box-shadow: 0 2px 6px rgba(255, 193, 7, 0.25);
        }
      }
    }
  }

  :deep(.panel-content::-webkit-scrollbar-thumb) {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.05);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}
</style>
