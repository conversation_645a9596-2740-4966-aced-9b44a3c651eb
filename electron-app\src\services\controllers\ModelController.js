const axios = require('axios');
const fs = require('fs-extra');
const path = require('path');
const EventEmitter = require('events');
const { nanoid } = require('../utils/idGenerator');

class ModelController extends EventEmitter {
  constructor(config) {
    super();
    this.config = {
      apiKey: config.apiKey || '',
      baseUrl: config.baseUrl || 'https://api.openai.com/v1',
      model: config.model || 'gpt-3.5-turbo',
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 2000,
      ...config
    };
    
    this.baseDir = config.baseDir;
    this.rolesFile = path.join(this.baseDir, 'config', 'ai_roles.json');
    this.providersFile = path.join(this.baseDir, 'config', 'ai_providers.json');
    this.conversationsDir = path.join(this.baseDir, 'conversations');
    
    // 会话管理
    this.activeConversations = new Map();
    this.conversationHistory = new Map();
    
    // 请求队列和限流
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.rateLimitDelay = 1000; // 1秒间隔
    
    this.initialize();
  }

  async initialize() {
    try {
      await fs.ensureDir(path.dirname(this.rolesFile));
      await fs.ensureDir(path.dirname(this.providersFile));
      await fs.ensureDir(this.conversationsDir);
      
      // 初始化默认AI角色
      if (!await fs.pathExists(this.rolesFile)) {
        await this.initializeDefaultRoles();
      }
      
      // 初始化默认AI服务商
      if (!await fs.pathExists(this.providersFile)) {
        await this.initializeDefaultProviders();
      }
      
      console.log('ModelController 初始化完成');
    } catch (error) {
      console.error('ModelController 初始化失败:', error);
      throw error;
    }
  }

  async initializeDefaultRoles() {
    const defaultRoles = [
      {
        id: 'writing-assistant',
        name: '写作助手',
        description: '专业的小说写作助手，帮助构思情节、完善角色、优化文笔',
        systemPrompt: '你是一个专业的小说写作助手。你擅长帮助作者构思情节、塑造角色、优化文笔。请用专业而友好的语气回答问题，提供具体可行的建议。',
        temperature: 0.8,
        maxTokens: 2000,
        category: 'writing',
        isDefault: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'character-creator',
        name: '角色设计师',
        description: '专门帮助创建和完善小说角色的AI助手',
        systemPrompt: '你是一个角色设计专家。你擅长创建立体、有趣的小说角色，包括外貌、性格、背景故事、动机等。请提供详细而富有创意的角色设定。',
        temperature: 0.9,
        maxTokens: 1500,
        category: 'character',
        isDefault: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'plot-developer',
        name: '情节策划师',
        description: '帮助构思和发展故事情节的专业助手',
        systemPrompt: '你是一个故事情节专家。你擅长构思引人入胜的故事情节，包括冲突设置、转折点、高潮和结局。请提供逻辑清晰、富有张力的情节建议。',
        temperature: 0.7,
        maxTokens: 2500,
        category: 'plot',
        isDefault: true,
        createdAt: new Date().toISOString()
      }
    ];

    await fs.writeJson(this.rolesFile, defaultRoles, { spaces: 2 });
  }

  async initializeDefaultProviders() {
    const defaultProviders = [
      {
        id: 'openai',
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        isDefault: true,
        config: {
          headers: {
            'Authorization': 'Bearer {API_KEY}',
            'Content-Type': 'application/json'
          }
        }
      },
      {
        id: 'azure-openai',
        name: 'Azure OpenAI',
        baseUrl: 'https://{resource}.openai.azure.com/openai/deployments/{deployment}',
        models: ['gpt-4', 'gpt-35-turbo'],
        isDefault: false,
        config: {
          headers: {
            'api-key': '{API_KEY}',
            'Content-Type': 'application/json'
          },
          apiVersion: '2024-02-15-preview'
        }
      },
      {
        id: 'claude',
        name: 'Anthropic Claude',
        baseUrl: 'https://api.anthropic.com/v1',
        models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
        isDefault: false,
        config: {
          headers: {
            'x-api-key': '{API_KEY}',
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
          }
        }
      }
    ];

    await fs.writeJson(this.providersFile, defaultProviders, { spaces: 2 });
  }

  // ==================== AI角色管理 ====================
  async get_ai_roles() {
    try {
      if (!await fs.pathExists(this.rolesFile)) {
        return [];
      }
      const roles = await fs.readJson(this.rolesFile);
      return roles.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    } catch (error) {
      console.error('获取AI角色失败:', error);
      return [];
    }
  }

  async add_ai_role(roleData) {
    try {
      const roles = await this.get_ai_roles();
      
      const newRole = {
        id: nanoid(),
        name: roleData.name || '新角色',
        description: roleData.description || '',
        systemPrompt: roleData.systemPrompt || '',
        temperature: roleData.temperature || 0.7,
        maxTokens: roleData.maxTokens || 2000,
        category: roleData.category || 'general',
        isDefault: false,
        createdAt: new Date().toISOString(),
        ...roleData
      };

      roles.push(newRole);
      await fs.writeJson(this.rolesFile, roles, { spaces: 2 });
      
      return newRole;
    } catch (error) {
      console.error('添加AI角色失败:', error);
      throw error;
    }
  }

  async update_ai_role(roleId, updateData) {
    try {
      const roles = await this.get_ai_roles();
      const roleIndex = roles.findIndex(r => r.id === roleId);
      
      if (roleIndex === -1) {
        throw new Error('角色不存在');
      }

      roles[roleIndex] = {
        ...roles[roleIndex],
        ...updateData,
        id: roleId,
        updatedAt: new Date().toISOString()
      };

      await fs.writeJson(this.rolesFile, roles, { spaces: 2 });
      return roles[roleIndex];
    } catch (error) {
      console.error('更新AI角色失败:', error);
      throw error;
    }
  }

  async delete_ai_role(roleId) {
    try {
      const roles = await this.get_ai_roles();
      const filteredRoles = roles.filter(r => r.id !== roleId);
      
      if (filteredRoles.length === roles.length) {
        throw new Error('角色不存在');
      }

      await fs.writeJson(this.rolesFile, filteredRoles, { spaces: 2 });
      return true;
    } catch (error) {
      console.error('删除AI角色失败:', error);
      throw error;
    }
  }

  // ==================== AI服务商管理 ====================
  async get_ai_providers() {
    try {
      if (!await fs.pathExists(this.providersFile)) {
        return [];
      }
      return await fs.readJson(this.providersFile);
    } catch (error) {
      console.error('获取AI服务商失败:', error);
      return [];
    }
  }

  async save_ai_providers(providersData) {
    try {
      await fs.writeJson(this.providersFile, providersData, { spaces: 2 });
      return {
        status: 'success',
        message: 'AI服务商配置保存成功'
      };
    } catch (error) {
      console.error('保存AI服务商失败:', error);
      return {
        status: 'error',
        message: '保存AI服务商配置失败',
        error: error.message
      };
    }
  }

  // ==================== 模型测试和获取 ====================
  async test_api_key(params) {
    try {
      const { provider, apiKey, baseUrl, model } = params;
      
      const testMessage = {
        role: 'user',
        content: '你好，请回复"测试成功"'
      };

      const response = await this.makeApiRequest({
        provider,
        apiKey,
        baseUrl,
        model: model || 'gpt-3.5-turbo',
        messages: [testMessage],
        maxTokens: 50,
        temperature: 0.1
      });

      return {
        status: 'success',
        message: 'API密钥测试成功',
        data: {
          response: response.content,
          model: response.model,
          usage: response.usage
        }
      };
    } catch (error) {
      console.error('API密钥测试失败:', error);
      return {
        status: 'error',
        message: 'API密钥测试失败',
        error: error.message
      };
    }
  }

  async fetch_models(params) {
    try {
      const { provider, apiKey, baseUrl } = params;

      // 根据不同服务商获取模型列表
      let modelsUrl;
      let headers = {};

      switch (provider) {
        case 'openai':
          modelsUrl = `${baseUrl}/models`;
          headers = {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          };
          break;
        case 'azure-openai':
          // Azure OpenAI 需要特殊处理
          return {
            status: 'success',
            message: '获取模型列表成功',
            data: ['gpt-4', 'gpt-35-turbo'] // Azure 预定义模型
          };
        default:
          throw new Error('不支持的服务商');
      }

      const response = await axios.get(modelsUrl, { headers });
      const models = response.data.data.map(model => model.id);

      return {
        status: 'success',
        message: '获取模型列表成功',
        data: models
      };
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return {
        status: 'error',
        message: '获取模型列表失败',
        error: error.message
      };
    }
  }

  // ==================== 智能对话核心 ====================
  async chat_with_ai(messages, config = {}) {
    try {
      // 获取角色配置
      const roleId = config.roleId || 'writing-assistant';
      const role = await this.getRole(roleId);

      // 构建完整的消息历史
      const fullMessages = this.buildMessageHistory(messages, role, config);

      // 配置合并
      const requestConfig = {
        provider: config.provider || 'openai',
        apiKey: config.apiKey || this.config.apiKey,
        baseUrl: config.baseUrl || this.config.baseUrl,
        model: config.model || role?.model || this.config.model,
        temperature: config.temperature ?? role?.temperature ?? this.config.temperature,
        maxTokens: config.maxTokens || role?.maxTokens || this.config.maxTokens,
        messages: fullMessages
      };

      // 添加到请求队列（支持并发控制）
      return await this.queueRequest(requestConfig, config);
    } catch (error) {
      console.error('AI对话失败:', error);
      return {
        status: 'error',
        message: 'AI对话失败',
        error: error.message
      };
    }
  }

  // 构建消息历史（优化的上下文管理）
  buildMessageHistory(messages, role, config) {
    const fullMessages = [];

    // 添加系统提示
    if (role?.systemPrompt) {
      fullMessages.push({
        role: 'system',
        content: role.systemPrompt
      });
    }

    // 添加上下文信息（如果提供）
    if (config.context) {
      const contextPrompt = this.buildContextPrompt(config.context);
      if (contextPrompt) {
        fullMessages.push({
          role: 'system',
          content: contextPrompt
        });
      }
    }

    // 添加对话历史（智能截断）
    const processedMessages = this.processMessageHistory(messages, config.maxHistoryTokens || 4000);
    fullMessages.push(...processedMessages);

    return fullMessages;
  }

  // 智能消息历史处理
  processMessageHistory(messages, maxTokens) {
    // 简单的token估算（1个token约等于4个字符）
    const estimateTokens = (text) => Math.ceil(text.length / 4);

    let totalTokens = 0;
    const processedMessages = [];

    // 从最新消息开始，向前添加直到达到token限制
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      const messageTokens = estimateTokens(message.content);

      if (totalTokens + messageTokens > maxTokens && processedMessages.length > 0) {
        break;
      }

      processedMessages.unshift(message);
      totalTokens += messageTokens;
    }

    return processedMessages;
  }

  // 构建上下文提示
  buildContextPrompt(context) {
    const contextParts = [];

    if (context.bookInfo) {
      contextParts.push(`当前书籍：${context.bookInfo.title}`);
      if (context.bookInfo.genre) {
        contextParts.push(`类型：${context.bookInfo.genre}`);
      }
      if (context.bookInfo.summary) {
        contextParts.push(`简介：${context.bookInfo.summary}`);
      }
    }

    if (context.characters && context.characters.length > 0) {
      const charList = context.characters.map(char => `${char.name}: ${char.description}`).join('\n');
      contextParts.push(`主要角色：\n${charList}`);
    }

    if (context.currentChapter) {
      contextParts.push(`当前章节：${context.currentChapter.title}`);
    }

    if (context.plotOutline) {
      contextParts.push(`情节大纲：${context.plotOutline}`);
    }

    return contextParts.length > 0 ?
      `以下是当前创作的背景信息：\n${contextParts.join('\n\n')}` : '';
  }

  // 请求队列管理
  async queueRequest(requestConfig, options = {}) {
    return new Promise((resolve, reject) => {
      const request = {
        config: requestConfig,
        options,
        resolve,
        reject,
        timestamp: Date.now()
      };

      this.requestQueue.push(request);
      this.processQueue();
    });
  }

  async processQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();

      try {
        const result = await this.makeApiRequest(request.config);

        // 保存对话历史
        if (request.options.saveHistory !== false) {
          await this.saveConversationHistory(request.config.messages, result, request.options);
        }

        request.resolve({
          status: 'success',
          message: '对话成功',
          data: result
        });
      } catch (error) {
        request.reject(error);
      }

      // 限流延迟
      if (this.requestQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay));
      }
    }

    this.isProcessingQueue = false;
  }

  // 核心API请求方法
  async makeApiRequest(config) {
    const { provider, apiKey, baseUrl, model, messages, temperature, maxTokens } = config;

    try {
      let requestUrl, headers, requestBody;

      switch (provider) {
        case 'openai':
          requestUrl = `${baseUrl}/chat/completions`;
          headers = {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          };
          requestBody = {
            model,
            messages,
            temperature,
            max_tokens: maxTokens,
            stream: false
          };
          break;

        case 'azure-openai':
          requestUrl = `${baseUrl}/chat/completions?api-version=2024-02-15-preview`;
          headers = {
            'api-key': apiKey,
            'Content-Type': 'application/json'
          };
          requestBody = {
            messages,
            temperature,
            max_tokens: maxTokens
          };
          break;

        case 'claude':
          requestUrl = `${baseUrl}/messages`;
          headers = {
            'x-api-key': apiKey,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
          };

          // Claude API格式转换
          const systemMessage = messages.find(m => m.role === 'system');
          const userMessages = messages.filter(m => m.role !== 'system');

          requestBody = {
            model,
            max_tokens: maxTokens,
            temperature,
            system: systemMessage?.content || '',
            messages: userMessages
          };
          break;

        default:
          throw new Error(`不支持的AI服务商: ${provider}`);
      }

      const response = await axios.post(requestUrl, requestBody, {
        headers,
        timeout: 60000 // 60秒超时
      });

      // 统一响应格式处理
      return this.parseApiResponse(response.data, provider);
    } catch (error) {
      console.error('API请求失败:', error);

      if (error.response) {
        const errorMessage = error.response.data?.error?.message || error.response.statusText;
        throw new Error(`API请求失败 (${error.response.status}): ${errorMessage}`);
      } else if (error.request) {
        throw new Error('网络请求失败，请检查网络连接');
      } else {
        throw new Error(`请求配置错误: ${error.message}`);
      }
    }
  }

  // 解析不同服务商的响应格式
  parseApiResponse(responseData, provider) {
    switch (provider) {
      case 'openai':
      case 'azure-openai':
        return {
          content: responseData.choices[0]?.message?.content || '',
          model: responseData.model,
          usage: responseData.usage,
          finishReason: responseData.choices[0]?.finish_reason
        };

      case 'claude':
        return {
          content: responseData.content[0]?.text || '',
          model: responseData.model,
          usage: responseData.usage,
          finishReason: responseData.stop_reason
        };

      default:
        throw new Error(`不支持的响应格式: ${provider}`);
    }
  }

  // 保存对话历史
  async saveConversationHistory(messages, response, options) {
    try {
      const conversationId = options.conversationId || nanoid();
      const conversationFile = path.join(this.conversationsDir, `${conversationId}.json`);

      let conversation = {};
      if (await fs.pathExists(conversationFile)) {
        conversation = await fs.readJson(conversationFile);
      } else {
        conversation = {
          id: conversationId,
          title: options.title || '新对话',
          roleId: options.roleId || 'writing-assistant',
          createdAt: new Date().toISOString(),
          messages: []
        };
      }

      // 添加新的消息和响应
      const lastUserMessage = messages[messages.length - 1];
      if (lastUserMessage) {
        conversation.messages.push({
          ...lastUserMessage,
          timestamp: new Date().toISOString()
        });
      }

      conversation.messages.push({
        role: 'assistant',
        content: response.content,
        timestamp: new Date().toISOString(),
        model: response.model,
        usage: response.usage
      });

      conversation.updatedAt = new Date().toISOString();

      await fs.writeJson(conversationFile, conversation, { spaces: 2 });

      return conversationId;
    } catch (error) {
      console.error('保存对话历史失败:', error);
      // 不抛出错误，避免影响主要功能
    }
  }

  // 获取角色信息
  async getRole(roleId) {
    try {
      const roles = await this.get_ai_roles();
      return roles.find(r => r.id === roleId) || null;
    } catch (error) {
      console.error('获取角色信息失败:', error);
      return null;
    }
  }

  // 获取对话历史
  async getConversationHistory(conversationId) {
    try {
      const conversationFile = path.join(this.conversationsDir, `${conversationId}.json`);
      if (!await fs.pathExists(conversationFile)) {
        return null;
      }
      return await fs.readJson(conversationFile);
    } catch (error) {
      console.error('获取对话历史失败:', error);
      return null;
    }
  }

  // 获取所有对话列表
  async getConversationList() {
    try {
      const files = await fs.readdir(this.conversationsDir);
      const conversations = [];

      for (const file of files) {
        if (path.extname(file) === '.json') {
          try {
            const conversationFile = path.join(this.conversationsDir, file);
            const conversation = await fs.readJson(conversationFile);
            conversations.push({
              id: conversation.id,
              title: conversation.title,
              roleId: conversation.roleId,
              createdAt: conversation.createdAt,
              updatedAt: conversation.updatedAt,
              messageCount: conversation.messages?.length || 0
            });
          } catch (error) {
            console.error(`读取对话文件失败: ${file}`, error);
          }
        }
      }

      return conversations.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
    } catch (error) {
      console.error('获取对话列表失败:', error);
      return [];
    }
  }

  // 删除对话
  async deleteConversation(conversationId) {
    try {
      const conversationFile = path.join(this.conversationsDir, `${conversationId}.json`);
      if (await fs.pathExists(conversationFile)) {
        await fs.remove(conversationFile);
        return true;
      }
      return false;
    } catch (error) {
      console.error('删除对话失败:', error);
      throw error;
    }
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = {
      ...this.config,
      ...newConfig
    };
  }

  // 清理资源
  cleanup() {
    this.requestQueue = [];
    this.activeConversations.clear();
    this.conversationHistory.clear();
    this.removeAllListeners();
  }
}

module.exports = ModelController;
