import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export const useAIProvidersStore = defineStore('aiProviders', () => {
  // 状态定义
  const providers = ref([])
  const loading = ref(false)
  const error = ref(null)
  const isLoaded = ref(false)
  const initialized = ref(false)

  // Getters
  const allProviders = computed(() => providers.value)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)

  // 获取所有可用模型的计算属性（只返回启用的模型）
  const allAvailableModels = computed(() => {
    const models = []

    providers.value.forEach(provider => {
      if (provider.models && Array.isArray(provider.models)) {
        provider.models.forEach(model => {
          // 只包含明确启用的模型（available === true）
          if (model.available === true) {
            models.push({
              id: model.id,
              name: model.name || model.id,
              providerId: provider.id,
              providerName: provider.name,
              available: true,
              // 添加模型配置信息
              config: model.config || {
                temperature: 0.8,
                max_tokens: 8192,
                top_p: 0.8,
                frequency_penalty: 0,
                presence_penalty: 0,
                stream: true
              },
              // 添加唯一标识符（providerId + modelId）
              uniqueId: `${provider.id}:${model.id}`
            })
          }
        })
      }
    })

    // 去重（基于唯一标识符，而不是模型ID）
    const uniqueModels = []
    const seenUniqueIds = new Set()

    models.forEach(model => {
      if (!seenUniqueIds.has(model.uniqueId)) {
        seenUniqueIds.add(model.uniqueId)
        uniqueModels.push(model)
      }
    })

    console.log(`aiProvidersStore: 找到 ${uniqueModels.length} 个可用模型`)
    return uniqueModels
  })

  // 获取所有模型（包括未启用的，用于管理界面）
  const allModels = computed(() => {
    const models = []

    providers.value.forEach(provider => {
      if (provider.models && Array.isArray(provider.models)) {
        provider.models.forEach(model => {
          models.push({
            id: model.id,
            name: model.name || model.id,
            providerId: provider.id,
            providerName: provider.name,
            available: model.available !== false, // 默认为true
            config: model.config || {
              temperature: 0.8,
              max_tokens: 8192,
              top_p: 0.8,
              frequency_penalty: 0,
              presence_penalty: 0,
              stream: true
            },
            uniqueId: `${provider.id}:${model.id}`
          })
        })
      }
    })

    return models
  })

  // 获取模型选项列表（用于下拉选择）
  const modelOptions = computed(() => {
    return allAvailableModels.value.map(model => ({
      value: model.uniqueId,  // 使用唯一标识符作为值
      label: model.name === model.id
        ? `${model.id} (${model.providerName})`  // 如果别名和ID相同，显示 "ID (提供商)"
        : `${model.name}  (${model.providerName})`, // 如果不同，显示 "别名 - ID (提供商)"
      id: model.id,
      name: model.name,
      providerId: model.providerId,
      providerName: model.providerName,
      uniqueId: model.uniqueId,
      config: model.config
    }))
  })

  // Actions
  async function loadProviders(forceRefresh = false) {
    // 如果已初始化且不需要强制刷新，直接返回缓存
    if (initialized.value && !forceRefresh) return providers.value
    
    try {
      loading.value = true
      error.value = null
      
      // 尝试最多3次
      let retryCount = 0
      const maxRetries = 3
      let success = false
      
      while (!success && retryCount < maxRetries) {
        try {
          // 添加API调用超时控制
          const apiPromise = window.pywebview.api.get_ai_providers();
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('获取AI服务商列表超时')), 5000);
          });
          
          // 竞争模式执行，谁先完成用谁的结果
          const response = await Promise.race([apiPromise, timeoutPromise]);
          const result = typeof response === 'string' ? JSON.parse(response) : response
          
          if (result && result.status === 'success') {
            // 检查返回的数据是否为有效数组
            if (Array.isArray(result.data)) {
              providers.value = result.data

              isLoaded.value = true
              initialized.value = true
              success = true
              return providers.value
            } else {
              console.warn('服务器返回的数据不是数组格式:', result.data)
              throw new Error('服务器返回的数据格式不正确')
            }
          } else {
            throw new Error(result?.message || '获取AI服务商列表失败')
          }
        } catch (e) {
          retryCount++
          if (retryCount >= maxRetries) {
            throw e
          }
          console.warn(`加载AI服务商失败 (尝试 ${retryCount}/${maxRetries}):`, e)
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }
    } catch (e) {
      error.value = e.message
      console.error('加载AI服务商失败:', e)
      // 确保在出错时设置initialized为true，避免反复重试
      initialized.value = true
      // 设置默认的空数组避免界面渲染出错
      providers.value = []
      throw e
    } finally {
      loading.value = false
    }
  }

  async function saveProviders() {
    try {
      loading.value = true
      error.value = null

      // 验证数据格式
      if (!Array.isArray(providers.value)) {
        throw new Error('服务商数据必须是数组格式')
      }

      // 验证每个服务商的必要字段
      for (const provider of providers.value) {
        if (!provider || typeof provider !== 'object') {
          throw new Error('服务商必须是对象格式')
        }

        // 确保基本字段存在
        if (!provider.name) provider.name = '未命名服务商'
        if (!provider.baseUrl) provider.baseUrl = 'https://api.openai.com/v1'
        if (!Array.isArray(provider.apiKeys)) provider.apiKeys = []
        if (!Array.isArray(provider.models)) provider.models = []
      }
      
      // 调用后端API
      const response = await window.pywebview.api.save_ai_providers(providers.value)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        ElMessage.success('AI服务商配置保存成功')

        // 通知配置存储重新加载模型列表
        try {
          const { useConfigStore } = await import('./config')
          const configStore = useConfigStore()
          console.log('AI提供商配置已更新，重新加载模型列表...')
          await configStore.reloadModels()
          console.log('模型列表重新加载完成')
        } catch (reloadError) {
          console.warn('重新加载模型列表失败:', reloadError)
        }

        return true
      } else {
        throw new Error(result?.message || '保存AI服务商配置失败')
      }
    } catch (e) {
      error.value = e.message
      ElMessage.error('保存AI服务商配置失败: ' + e.message)
      console.error('保存AI服务商配置失败:', e)
      throw e
    } finally {
      loading.value = false
    }
  }

  async function addProvider(providerData) {
    const newProvider = {
      id: Date.now().toString(),
      name: providerData.name || '新服务商',
      baseUrl: providerData.baseUrl || '',
      apiKeys: providerData.apiKeys || [],
      models: providerData.models || []
    }
    
    providers.value.push(newProvider)
    return newProvider
  }

  async function updateProvider(providerId, providerData) {
    const provider = providers.value.find(p => p.id === providerId)
    if (!provider) {
      throw new Error(`未找到ID为 ${providerId} 的服务商`)
    }
    
    // 更新服务商数据
    Object.assign(provider, providerData)
    
    return provider
  }

  async function removeProvider(providerId) {
    const index = providers.value.findIndex(p => p.id === providerId)
    if (index === -1) {
      throw new Error(`未找到ID为 ${providerId} 的服务商`)
    }
    
    providers.value.splice(index, 1)
    
    return true
  }

  async function testApiKey(providerId, apiKeyId) {
    try {
      loading.value = true
      
      const provider = providers.value.find(p => p.id === providerId)
      if (!provider) {
        throw new Error(`未找到ID为 ${providerId} 的服务商`)
      }
      
      const apiKey = provider.apiKeys.find(k => k.id === apiKeyId)
      if (!apiKey) {
        throw new Error(`未找到ID为 ${apiKeyId} 的API密钥`)
      }
      
      // 选择测试模型：优先使用提供商的第一个模型，否则使用默认模型
      let testModel = 'gpt-3.5-turbo' // 默认测试模型
      if (provider.models && provider.models.length > 0) {
        testModel = provider.models[0].id // 使用提供商的第一个模型
      }
      
      const params = {
        provider_id: providerId,
        api_key: apiKey.key,
        base_url: provider.baseUrl,
        test_model: testModel // 添加测试模型参数
      }
      
      const response = await window.pywebview.api.test_api_key(params)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        apiKey.status = 'active'
        ElMessage.success('API密钥测试成功')
        return true
      } else {
        apiKey.status = 'error'
        throw new Error(result?.message || 'API密钥测试失败')
      }
    } catch (e) {
      error.value = e.message
      ElMessage.error('API密钥测试失败: ' + e.message)
      return false
    } finally {
      loading.value = false
    }
  }

  async function fetchModels(providerId) {
    try {
      loading.value = true
      
      const provider = providers.value.find(p => p.id === providerId)
      if (!provider) {
        throw new Error(`未找到ID为 ${providerId} 的服务商`)
      }
      
      if (!provider.apiKeys || provider.apiKeys.length === 0) {
        throw new Error('请先添加API密钥')
      }
      
      const params = {
        provider_id: providerId,
        api_key: provider.apiKeys[0].key,
        base_url: provider.baseUrl
      }
      
      const response = await window.pywebview.api.fetch_models(params)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 合并已有模型和新获取的模型
        const existingModels = {}
        provider.models.forEach(model => {
          existingModels[model.id] = model
        })
        
        const newModels = result.data.map(modelId => {
          if (existingModels[modelId]) {
            return existingModels[modelId]
          } else {
            return {
              id: modelId,
              name: modelId,
              available: true
            }
          }
        })
        
        provider.models = newModels
        ElMessage.success(`成功获取 ${newModels.length} 个模型`)
        return newModels
      } else {
        throw new Error(result?.message || '获取模型列表失败')
      }
    } catch (e) {
      error.value = e.message
      ElMessage.error('获取模型列表失败: ' + e.message)
      return []
    } finally {
      loading.value = false
    }
  }

  function addApiKey(providerId, keyData = {}) {
    const provider = providers.value.find(p => p.id === providerId)
    if (!provider) {
      throw new Error(`未找到ID为 ${providerId} 的服务商`)
    }
    
    const newKey = {
      id: Date.now().toString(),
      key: keyData.key || '',
      weight: keyData.weight || 1,
      status: 'active'
    }
    
    if (!provider.apiKeys) {
      provider.apiKeys = []
    }
    
    provider.apiKeys.push(newKey)
    return newKey
  }

  function removeApiKey(providerId, keyIndex) {
    const provider = providers.value.find(p => p.id === providerId)
    if (!provider || !provider.apiKeys) {
      throw new Error(`未找到服务商或API密钥列表`)
    }
    
    if (keyIndex < 0 || keyIndex >= provider.apiKeys.length) {
      throw new Error(`无效的API密钥索引: ${keyIndex}`)
    }
    
    provider.apiKeys.splice(keyIndex, 1)
    return true
  }

  function addModel(providerId, modelData = {}) {
    const provider = providers.value.find(p => p.id === providerId)
    if (!provider) {
      throw new Error(`未找到ID为 ${providerId} 的服务商`)
    }

    // 验证模型ID
    if (!modelData.id || !modelData.id.trim()) {
      throw new Error('模型ID不能为空')
    }

    // 检查模型ID是否已存在
    if (provider.models && provider.models.some(m => m.id === modelData.id)) {
      throw new Error(`模型ID "${modelData.id}" 已存在`)
    }

    const newModel = {
      id: modelData.id.trim(),
      name: modelData.name?.trim() || modelData.id.trim(), // 别名，默认使用ID
      available: modelData.available !== false, // 默认启用，除非明确设置为false
      // 添加模型参数配置
      config: {
        temperature: modelData.config?.temperature ?? 0.8,
        max_tokens: modelData.config?.max_tokens ?? 8192,
        top_p: modelData.config?.top_p ?? 0.8,
        frequency_penalty: modelData.config?.frequency_penalty ?? 0,
        presence_penalty: modelData.config?.presence_penalty ?? 0,
        stream: modelData.config?.stream ?? true
      }
    }

    if (!provider.models) {
      provider.models = []
    }

    provider.models.push(newModel)
    console.log(`添加模型: ID="${newModel.id}", 别名="${newModel.name}", 配置:`, newModel.config)
    return newModel
  }

  function removeModel(providerId, modelIndex) {
    const provider = providers.value.find(p => p.id === providerId)
    if (!provider || !provider.models) {
      throw new Error(`未找到服务商或模型列表`)
    }
    
    if (modelIndex < 0 || modelIndex >= provider.models.length) {
      throw new Error(`无效的模型索引: ${modelIndex}`)
    }
    
    provider.models.splice(modelIndex, 1)
    return true
  }

  // 根据唯一标识符获取模型配置
  function getModelConfig(uniqueId) {
    if (!uniqueId || !uniqueId.includes(':')) {
      console.warn('无效的模型唯一标识符:', uniqueId)
      return null
    }

    const [providerId, modelId] = uniqueId.split(':')
    const provider = providers.value.find(p => p.id === providerId)
    if (!provider || !provider.models) {
      console.warn(`未找到服务商: ${providerId}`)
      return null
    }

    const model = provider.models.find(m => m.id === modelId)
    if (!model) {
      console.warn(`未找到模型: ${modelId} 在服务商 ${providerId} 中`)
      return null
    }

    return {
      id: model.id,
      name: model.name || model.id,
      providerId: providerId,
      providerName: provider.name,
      uniqueId: uniqueId,
      config: model.config || {
        temperature: 0.8,
        max_tokens: 8192,
        top_p: 0.8,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: true
      }
    }
  }

  // 更新模型配置
  function updateModelConfig(uniqueId, newConfig) {
    if (!uniqueId || !uniqueId.includes(':')) {
      throw new Error('无效的模型唯一标识符')
    }

    const [providerId, modelId] = uniqueId.split(':')
    const provider = providers.value.find(p => p.id === providerId)
    if (!provider || !provider.models) {
      throw new Error(`未找到服务商: ${providerId}`)
    }

    const model = provider.models.find(m => m.id === modelId)
    if (!model) {
      throw new Error(`未找到模型: ${modelId} 在服务商 ${providerId} 中`)
    }

    // 更新模型配置
    model.config = {
      ...model.config,
      ...newConfig
    }

    console.log(`更新模型配置: ${uniqueId}`, model.config)
    return model.config
  }



  // 清除当前状态，用于重置
  function reset() {
    providers.value = []
    isLoaded.value = false
    initialized.value = false
    error.value = null
  }

  return {
    // 状态
    providers,
    loading,
    error,
    isLoaded,
    initialized,
    
    // Getters
    allProviders,
    isLoading,
    hasError,
    allAvailableModels,
    allModels,
    modelOptions,
    
    // Actions
    loadProviders,
    saveProviders,
    addProvider,
    updateProvider,
    removeProvider,
    testApiKey,
    fetchModels,
    addApiKey,
    removeApiKey,
    addModel,
    removeModel,
    getModelConfig,
    updateModelConfig,
    reset
  }
}) 