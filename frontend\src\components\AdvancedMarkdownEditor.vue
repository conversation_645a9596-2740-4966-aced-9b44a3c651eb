<template>
  <div
    class="advanced-markdown-editor"
    :class="{ dark: isDarkTheme, 'theme-dark': isDarkTheme }"
  >
    <!-- 内容区域 -->
    <div class="editor-content" :class="`mode-${currentMode}`">
      <!-- 编辑模式 -->
      <div
        v-show="currentMode === 'edit'"
        class="edit-mode"
        :style="{
          ...editorFontStyle,
          '--preview-font-size': `${props.editFontSize}px`,
        }"
      >
        <v-md-editor
          v-model="content"
          height="100%"
          @change="handleContentChange"
        />
      </div>

      <!-- 分屏模式 -->
      <div v-show="currentMode === 'split'" class="split-mode">
        <v-md-editor
          v-model="content"
          height="100%"
          @change="handleContentChange"
        />
      </div>

      <!-- AI思维导图模式 -->
      <MindmapCanvas
        ref="mindmapCanvasRef"
        v-show="currentMode === 'mindmap'"
        :mindmap-data="mindmapData"
        :font-size="props.mindmapFontSize"
        :is-dark-theme="isDarkTheme"
        :available-models="availableModels"
        :selected-model="selectedModel"
        @update:selected-model="selectedModel = $event"
        @node-click="handleNodeClick"
        @ai-generate="handleAIGenerate"
        @ai-generate-request="handleAIGenerateRequest"
        @markdown-change="handleMarkdownChange"
      />
    </div>

    <!-- AI交互对话框 -->
    <AIDialog
      :visible="aiDialogVisible"
      @update:visible="aiDialogVisible = $event"
      :current-node="currentAiNode"
      :selected-model="selectedModel"
      :book-title="props.title"
      :mindmap-data="mindmapData"
      @content-generated="handleAIContentGenerated"
    />
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watch,
  computed,
  nextTick,
} from "vue";
import { ElMessage } from "element-plus";
import { useConfigStore } from "@/stores/config";
import { useAIProvidersStore } from "@/stores/aiProviders";

// 导入子组件
import MindmapCanvas from './mindmap/MindmapCanvas.vue'
import AIDialog from './mindmap/AIDialog.vue'
import { ContentProcessor } from './mindmap/contentProcessor.js'
import { AIContentProcessor } from './mindmap/aiContentProcessor.js'

// 创建内容处理器实例
const contentProcessor = new ContentProcessor()
const aiContentProcessor = new AIContentProcessor()

// Vue Markdown Editor 组件已在 main.js 中全局注册

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "未命名文档",
  },
  mode: {
    type: String,
    default: "edit",
    validator: (value) => ["edit", "mindmap"].includes(value),
  },
  editFontSize: {
    type: Number,
    default: 14,
  },
  mindmapFontSize: {
    type: Number,
    default: 12,
  },
});

// Emits
const emit = defineEmits([
  "update:modelValue",
  "save",
  "export",
  "content-change",
]);

// Store
const configStore = useConfigStore();
const aiProvidersStore = useAIProvidersStore();

// 响应式数据
const currentMode = ref(props.mode);

// AI相关
const aiDialogVisible = ref(false);
const currentAiNode = ref(null);

// 使用store中的模型管理
const selectedModel = computed({
  get: () => configStore.selectedModel,
  set: (value) => configStore.setSelectedModel(value)
});

// 从AI提供商store获取可用模型
const availableModels = computed(() => {
  const options = aiProvidersStore.modelOptions;
  return options.map(option => ({
    id: option.value,  // 使用value作为模型ID
    name: option.label, // 使用label作为显示名称
    providerId: option.providerId,
    providerName: option.providerName
  }));
});

// 文档内容
const content = ref(props.modelValue);
const mindmapData = ref(null);

// 防止循环更新的标志
const isUpdatingFromMindmap = ref(false);

// 思维导图组件引用
const mindmapCanvasRef = ref(null);

// 计算属性
const isDarkTheme = computed(() => configStore.theme === "dark");

// 字体大小样式
const editorFontStyle = computed(() => {
  const style = { fontSize: `${props.editFontSize}px` };
  console.log("编辑器字体样式:", style);
  return style;
});

const mindmapFontStyle = computed(() => {
  const style = { fontSize: `${props.mindmapFontSize}px` };
  console.log("思维导图字体样式:", style);
  return style;
});

const toolbarConfig = computed(() => {
  return {
    bold: true,
    italic: true,
    header: true,
    underline: true,
    strikethrough: true,
    mark: true,
    superscript: true,
    subscript: true,
    quote: true,
    ol: true,
    ul: true,
    link: true,
    imagelink: true,
    code: true,
    table: true,
    fullscreen: true,
    readmodel: true,
    htmlcode: true,
    help: true,
    undo: true,
    redo: true,
    trash: true,
    save: true,
    navigation: true,
  };
});

// 监听内容变化
watch(
  () => props.modelValue,
  (newValue) => {
    content.value = newValue;
    // 只在思维导图模式下才更新数据
    if (currentMode.value === "mindmap") {
      updateMindmapData();
    }
  }
);

// 监听props.title变化，用于检测文件切换
watch(
  () => props.title,
  (newTitle, oldTitle) => {
    // 如果标题发生变化，说明切换了文件，需要清理数据
    if (oldTitle && newTitle !== oldTitle) {
      console.log('检测到文件切换，从', oldTitle, '到', newTitle);
      clearEditorData();
    }
  }
);

watch(
  () => props.mode,
  (newMode, oldMode) => {
    console.log('模式切换:', oldMode, '->', newMode);

    // 如果从思维导图模式切换出去，先同步数据到markdown
    if (oldMode === "mindmap" && mindmapData.value) {
      console.log('从思维导图模式切换出去，同步数据到markdown');
      isUpdatingFromMindmap.value = true;
      const newMarkdown = contentProcessor.convertMindmapToMarkdown(mindmapData.value);
      content.value = newMarkdown;
      emit("update:modelValue", newMarkdown);
      nextTick(() => {
        isUpdatingFromMindmap.value = false;
      });
    }

    currentMode.value = newMode;

    // 如果切换到思维导图模式，从markdown更新思维导图数据
    if (newMode === "mindmap") {
      console.log('切换到思维导图模式，从markdown更新数据');
      // 确保有内容才更新思维导图数据
      if (content.value || props.title) {
        updateMindmapData();
      }
    } else {
      // 切换出思维导图模式时，清理思维导图相关状态
      if (oldMode === "mindmap") {
        console.log('清理思维导图相关状态');
        currentAiNode.value = null;
        aiDialogVisible.value = false;
      }
    }
  }
);

watch(content, (newValue) => {
  emit("update:modelValue", newValue);
  // 只在思维导图模式下且不是从思维导图更新时才更新数据
  if (currentMode.value === "mindmap" && !isUpdatingFromMindmap.value) {
    updateMindmapData();
  }
});

// 生命周期
onMounted(async () => {
  // 确保AI提供商store已初始化
  if (!aiProvidersStore.initialized) {
    await aiProvidersStore.loadProviders();
  }

  // 移除自动初始化思维导图数据，只在切换到思维导图模式时才加载
  // if (currentMode.value === "mindmap") {
  //   updateMindmapData();
  // }
});

onBeforeUnmount(() => {
  // 清理资源
  // 移除注入的样式
  const existingStyle = document.getElementById('markdown-theme-override-dynamic');
  if (existingStyle) {
    existingStyle.remove();
  }
});

// 主题样式现在通过外部CSS文件处理，不需要动态注入

// 清理编辑器数据
const clearEditorData = () => {
  console.log('清理编辑器数据...');

  // 清理思维导图数据
  mindmapData.value = null;

  // 重置AI相关状态
  currentAiNode.value = null;
  aiDialogVisible.value = false;

  // 重置更新标志
  isUpdatingFromMindmap.value = false;

  // 如果有思维导图实例，销毁它
  if (mindmapCanvasRef.value) {
    try {
      mindmapCanvasRef.value.destroyMindmapInstance();
    } catch (error) {
      console.warn('销毁思维导图实例失败:', error);
    }
  }

  console.log('编辑器数据清理完成');
};

// 内容变化处理
const handleContentChange = (text) => {
  content.value = text;
  emit("update:modelValue", text);
  emit("content-change");
};



// 更新思维导图数据
const updateMindmapData = () => {
  try {
    if (!content.value || !props.title) {
      mindmapData.value = contentProcessor.createEmptyMindmap(props.title || '未命名文档');
      return;
    }

    const newMindmapData = contentProcessor.parseMarkdownToMindmap(content.value, props.title, { showContentAsNodes: true });

    // 验证生成的数据是否有效
    if (newMindmapData && newMindmapData.id && newMindmapData.title) {
      mindmapData.value = newMindmapData;
    } else {
      console.warn('生成的思维导图数据无效，使用空数据');
      mindmapData.value = contentProcessor.createEmptyMindmap(props.title || '未命名文档');
    }
  } catch (error) {
    console.error('更新思维导图数据失败:', error);
    mindmapData.value = contentProcessor.createEmptyMindmap(props.title || '未命名文档');
  }
};

// 新的事件处理方法
const handleNodeClick = (node) => {
  console.log('节点点击:', node);
  // 可以在这里添加节点点击的处理逻辑
};

const handleAIGenerate = (node) => {
  currentAiNode.value = node;
  aiDialogVisible.value = true;
};

const handleAIGenerateRequest = (params) => {
  console.log('=== AI生成请求调试信息 ===');
  console.log('接收到的参数:', params);
  console.log('节点信息:', params.node);
  console.log('当前mindmapData根节点:', mindmapData.value?.title);

  // 🔧 关键修复：使用params中的最新mindmapData，而不是本地的mindmapData
  if (params.mindmapData) {
    console.log('使用传递的最新mindmapData');
    mindmapData.value = params.mindmapData;
  } else {
    console.warn('参数中没有mindmapData，使用本地数据');
  }

  console.log('更新后的mindmapData根节点:', mindmapData.value?.title);
  console.log('mindmapData是否包含目标节点:', findNodeInData(mindmapData.value, params.node.id));

  // 设置当前AI节点并打开对话框
  currentAiNode.value = params.node;
  aiDialogVisible.value = true;
};

// 辅助函数：在思维导图数据中查找节点
const findNodeInData = (data, nodeId) => {
  if (!data || !nodeId) return false;

  console.log(`🔍 在数据中查找节点ID: ${nodeId} (类型: ${typeof nodeId})`);

  const findNode = (node, path = []) => {
    const currentPath = [...path, node.title];
    console.log(`  检查节点: ${node.id} (类型: ${typeof node.id}) - ${node.title}`);

    if (node.id === nodeId) {
      console.log(`  ✅ 找到匹配节点! 路径: ${currentPath.join(' → ')}`);
      return true;
    }

    if (node.children) {
      return node.children.some(child => findNode(child, currentPath));
    }
    return false;
  };

  const result = findNode(data);
  console.log(`查找结果: ${result ? '找到' : '未找到'}`);
  return result;
};

// 处理从思维导图组件传来的markdown变化
const handleMarkdownChange = (markdown) => {
  console.log('收到思维导图Markdown变化，长度:', markdown.length);
  isUpdatingFromMindmap.value = true;
  content.value = markdown;
  emit("update:modelValue", markdown);
  emit("content-change");
  // 下一个tick后重置标志
  nextTick(() => {
    isUpdatingFromMindmap.value = false;
  });
};

const handleAIContentGenerated = (params) => {
  try {
    console.log('处理AI生成内容:', params);
    console.log('当前节点ID:', params.node?.id);

    // 如果在思维导图模式，直接在思维导图中添加节点
    if (currentMode.value === 'mindmap' && mindmapCanvasRef.value) {
      // 解析AI生成的内容为节点数据
      const nodeDataList = aiContentProcessor.parseContentToNodes(params.content, params.mode);

      if (nodeDataList && nodeDataList.length > 0) {
        console.log('准备添加节点到父节点ID:', params.node.id);
        // 直接在思维导图中添加节点
        mindmapCanvasRef.value.addAIGeneratedNodes(params.node.id, nodeDataList);

        console.log(`AI生成了${nodeDataList.length}个节点，已直接添加到思维导图`);

        // 🔧 关键修复：更新mindmapData以保持数据同步
        console.log('=== 数据同步调试 ===');
        console.log('生成前的mindmapData:', JSON.stringify(mindmapData.value, null, 2));

        // 获取更新后的思维导图数据
        const updatedMindmapData = mindmapCanvasRef.value.getCurrentMindmapData();
        console.log('生成后的updatedMindmapData:', JSON.stringify(updatedMindmapData, null, 2));

        if (updatedMindmapData) {
          // 检查数据结构是否一致
          console.log('数据结构对比:');
          console.log('- 原始根节点ID:', mindmapData.value?.id, '类型:', typeof mindmapData.value?.id);
          console.log('- 更新根节点ID:', updatedMindmapData?.id, '类型:', typeof updatedMindmapData?.id);
          console.log('- 原始子节点数量:', mindmapData.value?.children?.length || 0);
          console.log('- 更新子节点数量:', updatedMindmapData?.children?.length || 0);

          mindmapData.value = updatedMindmapData;
          console.log('✅ 已同步更新mindmapData');
        } else {
          console.error('❌ 无法获取更新后的思维导图数据');
        }
      } else {
        console.warn('AI生成的内容无法解析为有效节点');
      }
    } else {
      // 如果不在思维导图模式，使用原有的数据更新方式
      const updatedMindmapData = aiContentProcessor.applyGeneratedContent(
        params,
        mindmapData.value
      );

      mindmapData.value = updatedMindmapData;

      // 同步到markdown
      isUpdatingFromMindmap.value = true;
      const newMarkdown = contentProcessor.convertMindmapToMarkdown(updatedMindmapData);
      content.value = newMarkdown;
      emit("update:modelValue", newMarkdown);

      nextTick(() => {
        isUpdatingFromMindmap.value = false;
      });
    }

    const modeMap = {
      subtopics: '生成子主题',
      children: '要点分解',
      analysis: '多角度分析',
      creative: '创意发散'
    };

    ElMessage.success(`${modeMap[params.mode] || 'AI生成'}完成`);
  } catch (error) {
    console.error('应用AI生成内容失败:', error);
    ElMessage.error('应用AI生成内容失败: ' + error.message);
  }
};

// 文档操作
const saveDocument = () => {
  emit("save", {
    title: props.title,
    content: content.value,
  });
};

const exportDocument = () => {
  emit("export", {
    title: props.title,
    content: content.value,
  });
};

// 暴露方法给父组件
defineExpose({
  saveDocument,
  exportDocument,
  getCurrentContent: () => content.value,
  setContent: (newContent) => {
    content.value = newContent;
  },
  clearEditorData, // 暴露清理方法
});
</script>

<style lang="scss" scoped>

.advanced-markdown-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--el-bg-color);
  transition: all 0.3s ease;

  &.dark {
    background: var(--el-bg-color);
  }
}

.editor-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 100%;

  &.mode-edit .edit-mode,
  &.mode-mindmap .mindmap-mode {
    height: 100%;
  }
}

.edit-mode {
  height: 100%;
  position: relative;

  /* 确保字体大小应用到编辑器内容 */
  :deep(.v-md-editor) {
    font-size: inherit !important;

    .CodeMirror {
      font-size: inherit !important;
    }

    .CodeMirror-line {
      font-size: inherit !important;
    }

    .v-md-editor-preview {
      font-size: var(--preview-font-size, inherit) !important;

      /* 确保预览内容的所有元素都继承字体大小 */
      * {
        font-size: inherit !important;
      }
    }
  }
}
.dark .pre{
  background: var(--el-bg-color) !important ;
}
.split-mode {
  height: 100%;
  position: relative;
}

.mindmap-mode {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);

  /* 思维导图字体大小控制 */
  .mindmap-container {
    font-size: inherit !important;

    /* SVG 文本元素 */
    text {
      font-size: inherit !important;
    }

    /* 节点文本 */
    .node-text {
      font-size: inherit !important;
    }
  }

  .mindmap-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color-page);
    backdrop-filter: blur(8px);

    .el-select {
      min-width: 180px;
    }
  }

  .mindmap-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: var(--el-fill-color-blank);

    svg {
      width: 100%;
      height: 100%;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }

    // 思维导图节点样式
    :deep(.node) {
      transition: all 0.3s ease;

      &:hover {
        .node-bg {
          filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15)) !important;
        }
      }
    }

    :deep(.link) {
      stroke: var(--el-border-color);
      stroke-width: 2px;
      fill: none;
      transition: all 0.3s ease;
    }

    :deep(.node-bg) {
      fill: var(--el-bg-color);
      stroke: var(--el-border-color-light);
      stroke-width: 1px;
      filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.1));
      transition: all 0.3s ease;
    }

    :deep(.node-text) {
      fill: var(--el-text-color-primary);
      font-size: 14px;
      font-weight: 500;
      pointer-events: none;
    }

    :deep(.expand-btn) {
      fill: var(--el-color-primary);
      stroke: var(--el-bg-color);
      stroke-width: 2px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        fill: var(--el-color-primary-light-3);
        transform: scale(1.1);
      }
    }

    :deep(.expand-text) {
      fill: var(--el-bg-color);
      font-size: 12px;
      font-weight: bold;
      pointer-events: none;
    }

    :deep(.ai-btn) {
      fill: var(--el-color-success);
      stroke: var(--el-bg-color);
      stroke-width: 2px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        fill: var(--el-color-success-light-3);
        transform: scale(1.1);
      }
    }

    :deep(.ai-text) {
      fill: var(--el-bg-color);
      font-size: 10px;
      font-weight: bold;
      pointer-events: none;
    }
  }
}

.ai-dialog-content {
  .node-info {
    padding: 16px;
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-7);
    border-radius: 8px;
    margin-bottom: 20px;
    color: var(--el-color-primary);
    font-weight: 500;

    strong {
      color: var(--el-color-primary-dark-2);
    }
  }

  .ai-generating {
    margin-top: 20px;

    .el-progress {
      margin-bottom: 16px;
    }

    .ai-content-preview {
      margin-top: 16px;
      padding: 16px;
      background: var(--el-fill-color-light);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 8px;
      max-height: 240px;
      overflow-y: auto;
      white-space: pre-wrap;
      font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo,
        monospace;
      font-size: 13px;
      line-height: 1.5;
      color: var(--el-text-color-primary);

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: var(--el-fill-color-lighter);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--el-border-color);
        border-radius: 3px;

        &:hover {
          background: var(--el-border-color-dark);
        }
      }
    }
  }
}

/* 深色主题适配 */
.dark {
  .edit-mode {
    background-color: var(--el-bg-color) !important;
  }

  /* 强制覆盖所有可能的背景色 */
  :deep(*) {
    &[style*="background-color: #fff"],
    &[style*="background-color: white"],
    &[style*="background: #fff"],
    &[style*="background: white"] {
      background-color: var(--el-bg-color) !important;
      background: var(--el-bg-color) !important;
    }
  }

  /* 覆盖 vue-markdown-editor 的样式 */
  :deep(.v-md-editor) {
    background-color: var(--el-bg-color) !important;
    color: var(--el-text-color-primary) !important;
    border-color: var(--el-border-color) !important;

    .v-md-editor__main {
      background-color: var(--el-bg-color) !important;
    }

    .v-md-editor__main-left {
      background-color: var(--el-bg-color) !important;
      border-right-color: var(--el-border-color) !important;
    }

    .v-md-editor__main-right {
      background-color: var(--el-bg-color) !important;
    }

    .v-md-editor__toolbar {
      background-color: var(--el-bg-color-page) !important;
      border-bottom-color: var(--el-border-color) !important;
    }

    .v-md-editor__toolbar-item {
      color: var(--el-text-color-primary) !important;

      &:hover {
        background-color: var(--el-fill-color-light) !important;
      }

      &.v-md-editor__toolbar-item--active {
        background-color: var(--el-color-primary) !important;
        color: white !important;
      }
    }

    /* 所有工具栏按钮的通用样式 */
    .v-md-editor__toolbar-item--sync-scroll,
    .v-md-editor__toolbar-item--fullscreen,
    .v-md-editor__toolbar-item--preview,
    .v-md-editor__toolbar-item--toc {
      color: var(--el-text-color-primary) !important;

      &:hover {
        background-color: var(--el-fill-color-light) !important;
      }

      &.v-md-editor__toolbar-item--active {
        background-color: var(--el-color-primary) !important;
        color: white !important;
      }
    }

    /* 工具栏分隔符 */
    .v-md-editor__toolbar-divider {
      background-color: var(--el-border-color) !important;
    }

    /* 工具栏右侧按钮组 */
    .v-md-editor__toolbar-right {
      .v-md-editor__toolbar-item {
        color: var(--el-text-color-primary) !important;

        &:hover {
          background-color: var(--el-fill-color-light) !important;
        }
      }
    }

    /* 目录导航样式 - 根据实际DOM结构 */
    .v-md-editor__left-area {
      background-color: var(--el-bg-color-page) !important;
      border-color: var(--el-border-color) !important;
      color: var(--el-text-color-primary) !important;

      .v-md-editor__left-area-title {
        background-color: var(--el-bg-color-page) !important;
        color: var(--el-text-color-primary) !important;
        border-bottom-color: var(--el-border-color) !important;
      }

      .v-md-editor__left-area-body {
        background-color: var(--el-bg-color-page) !important;
        color: var(--el-text-color-primary) !important;
      }

      .v-md-editor__toc-nav {
        background-color: var(--el-bg-color-page) !important;
        color: var(--el-text-color-primary) !important;
      }

      .v-md-editor__toc-nav-item {
        color: var(--el-text-color-regular) !important;
        background-color: transparent !important;

        &:hover {
          background-color: var(--el-fill-color-light) !important;
          color: var(--el-text-color-primary) !important;
        }

        &.active {
          background-color: var(--el-color-primary-light-9) !important;
          color: var(--el-color-primary) !important;
        }
      }

      .v-md-editor__toc-nav-title {
        color: inherit !important;
      }

      /* 滚动条样式 */
      .scrollbar {
        .scrollbar__wrap {
          background-color: var(--el-bg-color-page) !important;
        }

        .scrollbar__view {
          background-color: var(--el-bg-color-page) !important;
        }

        .scrollbar__bar {
          .scrollbar__thumb {
            background-color: var(--el-fill-color-dark) !important;

            &:hover {
              background-color: var(--el-fill-color) !important;
            }
          }
        }
      }
    }

    /* CodeMirror 编辑器样式 */
    .CodeMirror {
      background-color: var(--el-bg-color) !important;
      color: var(--el-text-color-primary) !important;

      .CodeMirror-gutters {
        background-color: var(--el-bg-color-page) !important;
        border-right-color: var(--el-border-color) !important;
      }

      .CodeMirror-linenumber {
        color: var(--el-text-color-secondary) !important;
      }

      .CodeMirror-cursor {
        border-left-color: var(--el-text-color-primary) !important;
      }

      .CodeMirror-selected {
        background-color: var(--el-color-primary-light-9) !important;
      }

      .CodeMirror-activeline-background {
        background-color: var(--el-fill-color-light) !important;
      }

      .CodeMirror-scroll {
        background-color: var(--el-bg-color) !important;
      }

      .CodeMirror-lines {
        background-color: var(--el-bg-color) !important;
      }

      .CodeMirror-line {
        background-color: transparent !important;
      }

      /* Markdown 语法高亮样式 */
      .cm-header {
        color: var(--el-color-primary) !important;
        font-weight: bold !important;
      }

      .cm-strong {
        color: var(--el-text-color-primary) !important;
        font-weight: bold !important;
      }

      .cm-em {
        color: var(--el-text-color-primary) !important;
        font-style: italic !important;
      }

      .cm-link {
        color: var(--el-color-primary-light-3) !important;
      }

      .cm-url {
        color: var(--el-color-primary-light-5) !important;
      }

      .cm-quote {
        color: var(--el-text-color-secondary) !important;
        font-style: italic !important;
      }

      .cm-keyword {
        color: var(--el-color-warning) !important;
      }

      .cm-atom {
        color: var(--el-color-success) !important;
      }

      .cm-number {
        color: var(--el-color-warning-light-3) !important;
      }

      .cm-def {
        color: var(--el-color-primary-light-3) !important;
      }

      .cm-variable {
        color: var(--el-text-color-primary) !important;
      }

      .cm-variable-2 {
        color: var(--el-color-info) !important;
      }

      .cm-variable-3 {
        color: var(--el-color-success-light-3) !important;
      }

      .cm-property {
        color: var(--el-color-primary-light-3) !important;
      }

      .cm-operator {
        color: var(--el-text-color-regular) !important;
      }

      .cm-comment {
        color: var(--el-text-color-placeholder) !important;
        font-style: italic !important;
      }

      .cm-string {
        color: var(--el-color-success-light-3) !important;
      }

      .cm-string-2 {
        color: var(--el-color-success) !important;
      }

      .cm-meta {
        color: var(--el-text-color-secondary) !important;
      }

      .cm-qualifier {
        color: var(--el-color-info-light-3) !important;
      }

      .cm-builtin {
        color: var(--el-color-warning-light-3) !important;
      }

      .cm-bracket {
        color: var(--el-text-color-regular) !important;
      }

      .cm-tag {
        color: var(--el-color-danger-light-3) !important;
      }

      .cm-attribute {
        color: var(--el-color-primary-light-5) !important;
      }

      /* 代码块样式 */
      .cm-s-default .cm-comment {
        color: var(--el-text-color-placeholder) !important;
      }

      /* 列表样式 */
      .cm-formatting-list {
        color: var(--el-color-primary) !important;
        font-weight: bold !important;
      }

      /* 代码样式 */
      .cm-formatting-code {
        color: var(--el-color-danger-light-3) !important;
      }

      /* 链接样式 */
      .cm-formatting-link {
        color: var(--el-color-primary-light-5) !important;
      }

      /* 粗体样式 */
      .cm-formatting-strong {
        color: var(--el-text-color-primary) !important;
        font-weight: bold !important;
      }

      /* 斜体样式 */
      .cm-formatting-em {
        color: var(--el-text-color-primary) !important;
        font-style: italic !important;
      }

      /* 标题样式 */
      .cm-formatting-header {
        color: var(--el-color-primary) !important;
        font-weight: bold !important;
      }

      /* 确保所有文本都有足够的对比度 */
      .CodeMirror-line > span {
        color: var(--el-text-color-primary) !important;
      }

      /* 特殊字符和符号 */
      .cm-punctuation {
        color: var(--el-text-color-regular) !important;
      }

      /* 确保普通文本清晰可见 */
      .cm-variable,
      .cm-variable-2,
      .cm-variable-3 {
        color: var(--el-text-color-primary) !important;
      }

      /* 增强对比度的文本 */
      span:not([class]) {
        color: var(--el-text-color-primary) !important;
      }
    }

    /* 编辑模式下的预览区域背景 */
    .v-md-editor__preview {
      background-color: var(--el-bg-color) !important;
      font-size: var(--preview-font-size, inherit) !important;

      /* 确保预览内容的所有元素都继承字体大小 */
      * {
        font-size: inherit !important;
      }
    }
  }
}

  /* 预览组件样式 */
  :deep(.v-md-preview) {
    background-color: var(--el-bg-color) !important;
    color: var(--el-text-color-primary) !important;

    .v-md-preview__wrapper {
      background-color: var(--el-bg-color) !important;
    }

    /* 预览内容区域 */
    .v-md-preview__content {
      background-color: var(--el-bg-color) !important;
    }

    /* 滚动条样式 */
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background-color: var(--el-fill-color-lighter) !important;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--el-fill-color-dark) !important;
      border-radius: 4px;

      &:hover {
        background-color: var(--el-fill-color) !important;
      }
    }

    /* 标题样式 */
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: var(--el-text-color-primary) !important;
      border-bottom-color: var(--el-border-color-light) !important;
    }



    /* 代码块样式 */
    code {
      background-color: var(--el-fill-color-dark) !important;
      color: var(--el-color-danger-light-3) !important;
      padding: 2px 4px !important;
      border-radius: 3px !important;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
    }

    /* pre 代码块样式 */
    pre {
      background-color: var(--el-fill-color-dark) !important;
      color: var(--el-text-color-primary) !important;
      border: 1px solid var(--el-border-color-light) !important;
      border-radius: 6px !important;
      padding: 16px !important;
      overflow-x: auto !important;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
      line-height: 1.45 !important;

      code {
        background-color: transparent !important;
        color: inherit !important;
        padding: 0 !important;
        border-radius: 0 !important;
      }
    }

    /* 引用块样式 */
    blockquote {
      background-color: var(--el-fill-color-dark) !important;
      border-left-color: var(--el-color-primary) !important;
      color: var(--el-text-color-secondary) !important;
    }

    /* 表格样式 */
    table {
      border-color: var(--el-border-color) !important;
      background-color: var(--el-bg-color) !important;

      th {
        background-color: var(--el-fill-color-dark) !important;
        color: var(--el-text-color-primary) !important;
        border-color: var(--el-border-color) !important;
      }

      td {
        border-color: var(--el-border-color) !important;
        color: var(--el-text-color-primary) !important;
      }

      tr:nth-child(even) {
        background-color: var(--el-fill-color-darker) !important;
      }

      tr:hover {
        background-color: var(--el-fill-color-dark) !important;
      }
    }

    /* 链接样式 */
    a {
      color: var(--el-color-primary) !important;
    }

    /* 分割线样式 */
    hr {
      border-color: var(--el-border-color) !important;
    }

    /* 列表样式 */
    ul,
    ol {
      color: var(--el-text-color-primary) !important;
    }
  }


/* 浅色主题优化 */
.advanced-markdown-editor:not(.dark) {
  :deep(.v-md-editor) {
    background-color: var(--el-bg-color) !important;
    color: var(--el-text-color-primary) !important;
    border-color: var(--el-border-color-light) !important;

    .v-md-editor__toolbar {
      background-color: var(--el-bg-color-page) !important;
      border-bottom-color: var(--el-border-color-light) !important;
    }

    .CodeMirror {
      background-color: var(--el-bg-color) !important;
      color: var(--el-text-color-primary) !important;

      .CodeMirror-gutters {
        background-color: var(--el-bg-color-page) !important;
        border-right-color: var(--el-border-color-light) !important;
      }

      /* 浅色主题下的语法高亮 */
      .cm-header {
        color: var(--el-color-primary-dark-2) !important;
      }

      .cm-link {
        color: var(--el-color-primary) !important;
      }

      .cm-quote {
        color: var(--el-text-color-regular) !important;
      }

      .cm-comment {
        color: var(--el-text-color-placeholder) !important;
      }

      .cm-string {
        color: var(--el-color-success-dark-2) !important;
      }

      .cm-keyword {
        color: var(--el-color-warning-dark-2) !important;
      }
    }

    /* 浅色主题下的预览区域字体大小控制 */
    .v-md-editor__preview {
      font-size: var(--preview-font-size, inherit) !important;

      /* 确保预览内容的所有元素都继承字体大小 */
      * {
        font-size: inherit !important;
      }

      /* Prism.js 语法高亮颜色 - 浅色主题 */
      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: #6a737d !important;
      }

      .token.punctuation {
        color: #24292e !important;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol,
      .token.deleted {
        color: #005cc5 !important;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin,
      .token.inserted {
        color: #032f62 !important;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string {
        color: #24292e !important;
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #d73a49 !important;
      }

      .token.function,
      .token.class-name {
        color: #6f42c1 !important;
      }

      .token.regex,
      .token.important,
      .token.variable {
        color: #e36209 !important;
      }
    }

    /* 浅色主题下的目录导航 - 根据实际DOM结构 */
    .v-md-editor__left-area {
      background-color: var(--el-bg-color-page) !important;
      border-color: var(--el-border-color-light) !important;
      color: var(--el-text-color-primary) !important;

      .v-md-editor__left-area-title {
        background-color: var(--el-bg-color-page) !important;
        color: var(--el-text-color-primary) !important;
        border-bottom-color: var(--el-border-color-light) !important;
      }

      .v-md-editor__left-area-body {
        background-color: var(--el-bg-color-page) !important;
        color: var(--el-text-color-primary) !important;
      }

      .v-md-editor__toc-nav-item {
        color: var(--el-text-color-regular) !important;

        &:hover {
          background-color: var(--el-fill-color-light) !important;
          color: var(--el-text-color-primary) !important;
        }

        &.active {
          background-color: var(--el-color-primary-light-9) !important;
          color: var(--el-color-primary-dark-2) !important;
        }
      }

      /* 浅色主题滚动条 */
      .scrollbar .scrollbar__bar .scrollbar__thumb {
        background-color: var(--el-fill-color) !important;

        &:hover {
          background-color: var(--el-fill-color-dark) !important;
        }
      }
    }
  }

  :deep(.v-md-preview) {
    background-color: var(--el-bg-color) !important;
    color: var(--el-text-color-primary) !important;

    .v-md-preview__wrapper {
      background-color: var(--el-bg-color) !important;
    }

    /* 浅色主题代码块样式 - 使用更高优先级 */
    .v-md-preview__content code,
    .vuepress-markdown-body code,
    div[class*="v-md"] code,
    code {
      background-color: #f6f8fa !important;
      color: #d73a49 !important;
      padding: 2px 4px !important;
      border-radius: 3px !important;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
      border: none !important;
    }

    /* 浅色主题 pre 代码块样式 - 使用更高优先级 */
    .v-md-preview__content pre,
    .vuepress-markdown-body pre,
    div[class*="v-md"] pre,
    pre {
      background-color: #f6f8fa !important;
      color: #24292e !important;
      border: 1px solid #e1e4e8 !important;
      border-radius: 6px !important;
      padding: 16px !important;
      overflow-x: auto !important;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
      line-height: 1.45 !important;

      code {
        background-color: transparent !important;
        color: inherit !important;
        padding: 0 !important;
        border-radius: 0 !important;
        border: none !important;
      }
    }

    /* 浅色主题表格样式 - 使用更高优先级 */
    .v-md-preview__content table,
    .vuepress-markdown-body table,
    div[class*="v-md"] table,
    table {
      border-collapse: collapse !important;
      border-spacing: 0 !important;
      background-color: var(--el-bg-color) !important;
      border: 1px solid #e1e4e8 !important;
      border-radius: 6px !important;
      overflow: hidden !important;

      th {
        background-color: #f6f8fa !important;
        color: #24292e !important;
        border: 1px solid #e1e4e8 !important;
        padding: 8px 12px !important;
        font-weight: 600 !important;
      }

      td {
        border: 1px solid #e1e4e8 !important;
        color: #24292e !important;
        padding: 8px 12px !important;
        background-color: transparent !important;
      }

      tr:nth-child(even) {
        background-color: #f8f9fa !important;

        td {
          background-color: #f8f9fa !important;
        }
      }

      tr:hover {
        background-color: #f1f3f4 !important;

        td {
          background-color: #f1f3f4 !important;
        }
      }
    }

    /* Prism.js 语法高亮颜色 - 浅色主题 */
    .token.comment,
    .token.prolog,
    .token.doctype,
    .token.cdata {
      color: #6a737d !important;
    }

    .token.punctuation {
      color: #24292e !important;
    }

    .token.property,
    .token.tag,
    .token.boolean,
    .token.number,
    .token.constant,
    .token.symbol,
    .token.deleted {
      color: #005cc5 !important;
    }

    .token.selector,
    .token.attr-name,
    .token.string,
    .token.char,
    .token.builtin,
    .token.inserted {
      color: #032f62 !important;
    }

    .token.operator,
    .token.entity,
    .token.url,
    .language-css .token.string,
    .style .token.string {
      color: #24292e !important;
    }

    .token.atrule,
    .token.attr-value,
    .token.keyword {
      color: #d73a49 !important;
    }

    .token.function,
    .token.class-name {
      color: #6f42c1 !important;
    }

    .token.regex,
    .token.important,
    .token.variable {
      color: #e36209 !important;
    }
  }
}

.dark {
  .mindmap-mode {
    .mindmap-container {
      background: var(--el-fill-color-darker);

      :deep(.node-bg) {
        fill: var(--el-bg-color-overlay);
        stroke: var(--el-border-color);
      }

      :deep(.link) {
        stroke: var(--el-border-color-dark);
      }
    }
  }

  .ai-dialog-content {
    .node-info {
      background: rgba(var(--el-color-primary-rgb), 0.1);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
    }

    .ai-content-preview {
      background: var(--el-fill-color-darker);
      border-color: var(--el-border-color);
    }
  }

  /* 响应式设计 */
  @media screen and (max-width: 768px) {
    .editor-toolbar {
      padding: 8px 12px;

      .toolbar-left {
        gap: 12px;

        .document-title {
          font-size: 12px;
          padding: 4px 8px;
        }
      }

      .toolbar-right {
        gap: 6px;
      }
    }

    .preview-mode .preview-content {
      padding: 20px 16px;
      font-size: 14px;
    }

    .mindmap-mode .mindmap-toolbar {
      padding: 8px 12px;
      flex-direction: column;
      gap: 8px;
      align-items: stretch;

      .el-select {
        min-width: unset;
      }
    }
  }

  /* 全局深色主题强制覆盖 */
  .theme-dark {
    background-color: var(--el-bg-color) !important;

    :deep(.v-md-editor),
    :deep(.v-md-preview),
    :deep(.CodeMirror),
    :deep(.v-md-editor__main),
    :deep(.v-md-editor__main-left),
    :deep(.v-md-editor__main-right),
    :deep(.v-md-editor__preview),
    :deep(.v-md-editor__preview-wrapper),
    :deep(.v-md-preview__wrapper),
    :deep(.v-md-preview__content) {
      background-color: var(--el-bg-color) !important;
      color: var(--el-text-color-primary) !important;
    }

    /* 强制覆盖预览组件的所有元素 */
    :deep(.v-md-preview) {
      * {
        background-color: var(--el-bg-color) !important;
        color: var(--el-text-color-primary) !important;
      }

      /* 保持特殊元素的颜色 */
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        color: var(--el-text-color-primary) !important;
      }

      a {
        color: var(--el-color-primary) !important;
      }


      /* Prism.js 语法高亮颜色 - 深色主题 */
      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: #6a9955 !important;
      }

      .token.punctuation {
        color: #d4d4d4 !important;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol,
      .token.deleted {
        color: #b5cea8 !important;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin,
      .token.inserted {
        color: #ce9178 !important;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string {
        color: #d4d4d4 !important;
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #569cd6 !important;
      }

      .token.function,
      .token.class-name {
        color: #dcdcaa !important;
      }

      .token.regex,
      .token.important,
      .token.variable {
        color: #d16969 !important;
      }

      blockquote {
        background-color: var(--el-fill-color-dark) !important;
        color: var(--el-text-color-secondary) !important;
      }
    }

    /* 强制覆盖内联样式 */
    :deep([style*="background"]) {
      background-color: var(--el-bg-color) !important;
    }

    :deep([style*="color"]) {
      color: var(--el-text-color-primary) !important;
    }

    /* 强制确保所有文本颜色清晰 */
    :deep(.CodeMirror) {
      /* 为没有特殊类名的文本设置默认颜色 */
      .CodeMirror-line > span:not([class*="cm-"]) {
        color: var(--el-text-color-primary) !important;
      }

      /* 确保普通文本可见 */
      .CodeMirror-line {
        color: var(--el-text-color-primary) !important;
      }
    }
  }
}

/* 预览模式专用强制样式 */
.advanced-markdown-editor.dark .preview-mode {
  background: var(--el-bg-color) !important;
  background-color: var(--el-bg-color) !important;

  .preview-wrapper {
    background: var(--el-bg-color) !important;
    background-color: var(--el-bg-color) !important;
  }

  :deep(.v-md-preview) {
    background: var(--el-bg-color) !important;
    background-color: var(--el-bg-color) !important;

    /* 强制预览组件使用主题颜色 - 根据实际DOM结构 */
    .v-md-editor-preview {
      background: var(--el-bg-color) !important;
      background-color: var(--el-bg-color) !important;
      color: var(--el-text-color-primary) !important;

      .vuepress-markdown-body {
        background: var(--el-bg-color) !important;
        background-color: var(--el-bg-color) !important;
        color: var(--el-text-color-primary) !important;

        /* 基础元素使用主题颜色，但排除代码块 */
        p,
        div:not([class*="language-"]):not(.token),
        span:not(.token),
        ul,
        ol,
        li,
        table,
        tr,
        td:not(.token),
        th:not(.token) {
          background: var(--el-bg-color) !important;
          background-color: var(--el-bg-color) !important;
          color: var(--el-text-color-primary) !important;
        }

        /* 深色主题代码块样式 - 使用更高优先级 */
        .v-md-editor-preview code,
        .vuepress-markdown-body code,
        div[class*="v-md"] code,
        code {
          background-color: var(--el-fill-color-dark) !important;
          color: var(--el-color-danger-light-3) !important;
          padding: 2px 4px !important;
          border-radius: 3px !important;
          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
          border: none !important;
        }

        /* 深色主题 pre 代码块样式 - 使用更高优先级 */
        .v-md-editor-preview pre,
        .vuepress-markdown-body pre,
        div[class*="v-md"] pre,
        pre {
          background-color: var(--el-fill-color-dark) !important;
          color: var(--el-text-color-primary) !important;
          border: 1px solid var(--el-border-color-light) !important;
          border-radius: 6px !important;
          padding: 16px !important;
          overflow-x: auto !important;
          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
          line-height: 1.45 !important;

          code {
            background-color: transparent !important;
            color: inherit !important;
            padding: 0 !important;
            border-radius: 0 !important;
            border: none !important;
          }
        }

        /* 重新设置特殊元素颜色 */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          color: var(--el-text-color-primary) !important;
          background: transparent !important;
          background-color: transparent !important;
        }

        a {
          color: var(--el-color-primary) !important;
          background: transparent !important;
          background-color: transparent !important;
        }

        /* Prism.js 语法高亮颜色 - 深色主题 */
        .token.comment,
        .token.prolog,
        .token.doctype,
        .token.cdata {
          color: #6a9955 !important;
        }

        .token.punctuation {
          color: #d4d4d4 !important;
        }

        .token.property,
        .token.tag,
        .token.boolean,
        .token.number,
        .token.constant,
        .token.symbol,
        .token.deleted {
          color: #b5cea8 !important;
        }

        .token.selector,
        .token.attr-name,
        .token.string,
        .token.char,
        .token.builtin,
        .token.inserted {
          color: #ce9178 !important;
        }

        .token.operator,
        .token.entity,
        .token.url,
        .language-css .token.string,
        .style .token.string {
          color: #d4d4d4 !important;
        }

        .token.atrule,
        .token.attr-value,
        .token.keyword {
          color: #569cd6 !important;
        }

        .token.function,
        .token.class-name {
          color: #dcdcaa !important;
        }

        .token.regex,
        .token.important,
        .token.variable {
          color: #d16969 !important;
        }

        blockquote {
          background: var(--el-fill-color-dark) !important;
          background-color: var(--el-fill-color-dark) !important;
          color: var(--el-text-color-secondary) !important;
        }

        /* 深色主题表格样式 - 使用更高优先级 */
        .v-md-editor-preview table,
        .vuepress-markdown-body table,
        div[class*="v-md"] table,
        table {
          border-collapse: collapse !important;
          border-spacing: 0 !important;
          background: var(--el-bg-color) !important;
          background-color: var(--el-bg-color) !important;
          border: 1px solid var(--el-border-color-light) !important;
          border-radius: 6px !important;
          overflow: hidden !important;

          th {
            background: var(--el-fill-color-dark) !important;
            background-color: var(--el-fill-color-dark) !important;
            color: var(--el-text-color-primary) !important;
            border: 1px solid var(--el-border-color-light) !important;
            padding: 8px 12px !important;
            font-weight: 600 !important;
          }

          td {
            background: var(--el-bg-color) !important;
            background-color: var(--el-bg-color) !important;
            color: var(--el-text-color-primary) !important;
            border: 1px solid var(--el-border-color-light) !important;
            padding: 8px 12px !important;
          }

          tr:nth-child(even) {
            background: var(--el-fill-color-darker) !important;
            background-color: var(--el-fill-color-darker) !important;

            td {
              background: var(--el-fill-color-darker) !important;
              background-color: var(--el-fill-color-darker) !important;
            }
          }

          tr:hover {
            background: var(--el-fill-color-dark) !important;
            background-color: var(--el-fill-color-dark) !important;

            td {
              background: var(--el-fill-color-dark) !important;
              background-color: var(--el-fill-color-dark) !important;
            }
          }
        }
      }
    }

    /* 目录导航强制覆盖 - 根据实际DOM结构 */
    .v-md-editor__left-area {
      background: var(--el-bg-color-page) !important;
      background-color: var(--el-bg-color-page) !important;
      border-color: var(--el-border-color) !important;
      color: var(--el-text-color-primary) !important;

      * {
        color: var(--el-text-color-primary) !important;
      }

      .v-md-editor__left-area-title {
        background: var(--el-bg-color-page) !important;
        background-color: var(--el-bg-color-page) !important;
        color: var(--el-text-color-primary) !important;
      }

      .v-md-editor__left-area-body {
        background: var(--el-bg-color-page) !important;
        background-color: var(--el-bg-color-page) !important;
        color: var(--el-text-color-primary) !important;
      }

      .v-md-editor__toc-nav {
        background: var(--el-bg-color-page) !important;
        background-color: var(--el-bg-color-page) !important;
        color: var(--el-text-color-primary) !important;
      }

      .v-md-editor__toc-nav-item {
        color: var(--el-text-color-regular) !important;
        background: transparent !important;
        background-color: transparent !important;

        &:hover {
          background: var(--el-fill-color-light) !important;
          background-color: var(--el-fill-color-light) !important;
          color: var(--el-text-color-primary) !important;
        }

        &.active {
          background: var(--el-color-primary-light-9) !important;
          background-color: var(--el-color-primary-light-9) !important;
          color: var(--el-color-primary) !important;
        }
      }

      .v-md-editor__toc-nav-title {
        color: inherit !important;
      }

      /* 滚动条强制覆盖 */
      .scrollbar {
        .scrollbar__wrap,
        .scrollbar__view {
          background: var(--el-bg-color-page) !important;
          background-color: var(--el-bg-color-page) !important;
        }

        .scrollbar__bar .scrollbar__thumb {
          background: var(--el-fill-color-dark) !important;
          background-color: var(--el-fill-color-dark) !important;

          &:hover {
            background: var(--el-fill-color) !important;
            background-color: var(--el-fill-color) !important;
          }
        }
      }
    }
  }
}

/* 浅色主题下的预览模式 - 根据实际DOM结构 */
.advanced-markdown-editor:not(.dark) .preview-mode {
  :deep(.v-md-editor-preview) {
    background: var(--el-bg-color) !important;
    background-color: var(--el-bg-color) !important;
    color: var(--el-text-color-primary) !important;

    /* Prism.js 语法高亮颜色 - 浅色主题 */
    .token.comment,
    .token.prolog,
    .token.doctype,
    .token.cdata {
      color: #6a737d !important;
    }

    .token.punctuation {
      color: #24292e !important;
    }

    .token.property,
    .token.tag,
    .token.boolean,
    .token.number,
    .token.constant,
    .token.symbol,
    .token.deleted {
      color: #005cc5 !important;
    }

    .token.selector,
    .token.attr-name,
    .token.string,
    .token.char,
    .token.builtin,
    .token.inserted {
      color: #032f62 !important;
    }

    .token.operator,
    .token.entity,
    .token.url,
    .language-css .token.string,
    .style .token.string {
      color: #24292e !important;
    }

    .token.atrule,
    .token.attr-value,
    .token.keyword {
      color: #d73a49 !important;
    }

    .token.function,
    .token.class-name {
      color: #6f42c1 !important;
    }

    .token.regex,
    .token.important,
    .token.variable {
      color: #e36209 !important;
    }

    .vuepress-markdown-body {
      background: var(--el-bg-color) !important;
      background-color: var(--el-bg-color) !important;
      color: var(--el-text-color-primary) !important;

      /* 浅色主题代码块样式 */
      code {
        background-color: #f6f8fa !important;
        color: #d73a49 !important;
        padding: 2px 4px !important;
        border-radius: 3px !important;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
      }

      /* 浅色主题 pre 代码块样式 */
      pre {
        background-color: #f6f8fa !important;
        color: #24292e !important;
        border: 1px solid #e1e4e8 !important;
        border-radius: 6px !important;
        padding: 16px !important;
        overflow-x: auto !important;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
        line-height: 1.45 !important;

        code {
          background-color: transparent !important;
          color: inherit !important;
          padding: 0 !important;
          border-radius: 0 !important;
        }
      }

      /* 浅色主题表格样式 */
      table {
        border-collapse: collapse !important;
        border-spacing: 0 !important;
        background-color: var(--el-bg-color) !important;
        border: 1px solid #e1e4e8 !important;
        border-radius: 6px !important;
        overflow: hidden !important;

        th {
          background-color: #f6f8fa !important;
          color: #24292e !important;
          border: 1px solid #e1e4e8 !important;
          padding: 8px 12px !important;
          font-weight: 600 !important;
        }

        td {
          border: 1px solid #e1e4e8 !important;
          color: #24292e !important;
          padding: 8px 12px !important;
        }

        tr:nth-child(even) {
          background-color: #f8f9fa !important;
        }

        tr:hover {
          background-color: #f1f3f4 !important;
        }
      }
    }
  }
}
</style>
