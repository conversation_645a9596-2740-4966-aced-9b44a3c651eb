<template>
  <div class="writing-page">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-select
          v-model="currentProject"
          placeholder="选择项目"
          style="width: 200px"
          @change="onProjectChange"
        >
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>

        <el-select
          v-model="currentBook"
          placeholder="选择书籍"
          style="width: 200px; margin-left: 10px"
          @change="onBookChange"
          :disabled="!currentProject"
        >
          <el-option
            v-for="book in books"
            :key="book.id"
            :label="book.title"
            :value="book.id"
          />
        </el-select>

        <el-select
          v-model="currentChapter"
          placeholder="选择章节"
          style="width: 200px; margin-left: 10px"
          @change="onChapterChange"
          :disabled="!currentBook"
        >
          <el-option
            v-for="chapter in chapters"
            :key="chapter.id"
            :label="chapter.title"
            :value="chapter.id"
          />
        </el-select>
      </div>

      <div class="toolbar-right">
        <el-button :icon="Plus" @click="showNewChapterDialog = true" :disabled="!currentBook">
          新建章节
        </el-button>
        <el-button :icon="Document" @click="saveContent" :loading="saving">
          保存
        </el-button>
        <el-button :icon="View" @click="togglePreview">
          {{ showPreview ? '编辑' : '预览' }}
        </el-button>
      </div>
    </div>

    <!-- 主要编辑区域 -->
    <div class="editor-container">
      <!-- 左侧：章节列表 -->
      <div class="chapter-sidebar">
        <div class="sidebar-header">
          <span>章节列表</span>
          <el-button text :icon="Plus" @click="showNewChapterDialog = true" :disabled="!currentBook">
            新建
          </el-button>
        </div>

        <div class="chapter-list">
          <div
            v-for="chapter in chapters"
            :key="chapter.id"
            class="chapter-item"
            :class="{ active: chapter.id === currentChapter }"
            @click="selectChapter(chapter.id)"
          >
            <div class="chapter-title">{{ chapter.title }}</div>
            <div class="chapter-info">
              <span>{{ chapter.wordCount || 0 }} 字</span>
              <span>{{ formatDate(chapter.updatedAt) }}</span>
            </div>
          </div>

          <div v-if="chapters.length === 0" class="empty-chapters">
            <el-icon size="48"><DocumentAdd /></el-icon>
            <p>还没有章节</p>
            <el-button type="primary" @click="showNewChapterDialog = true" :disabled="!currentBook">
              创建第一个章节
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧：编辑器 -->
      <div class="editor-main">
        <div v-if="!currentChapter" class="editor-placeholder">
          <el-icon size="64"><Edit /></el-icon>
          <h3>选择章节开始写作</h3>
          <p>请先选择项目、书籍和章节，然后开始您的创作之旅</p>
        </div>

        <div v-else class="editor-content">
          <!-- 章节标题 -->
          <div class="chapter-header">
            <el-input
              v-model="chapterTitle"
              placeholder="章节标题"
              class="chapter-title-input"
              @blur="updateChapterTitle"
            />
            <div class="chapter-stats">
              <span>字数: {{ wordCount }}</span>
              <span>最后保存: {{ lastSaved }}</span>
            </div>
          </div>

          <!-- 编辑器/预览区域 -->
          <div class="editor-area">
            <el-input
              v-if="!showPreview"
              v-model="content"
              type="textarea"
              placeholder="开始您的创作..."
              :rows="25"
              class="content-editor"
              @input="onContentChange"
            />

            <div v-else class="preview-area" v-html="previewContent"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建章节对话框 -->
    <el-dialog
      v-model="showNewChapterDialog"
      title="新建章节"
      width="400px"
    >
      <el-form :model="newChapter" label-width="80px">
        <el-form-item label="章节标题" required>
          <el-input v-model="newChapter.title" placeholder="请输入章节标题" />
        </el-form-item>
        <el-form-item label="章节序号">
          <el-input-number v-model="newChapter.order" :min="1" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showNewChapterDialog = false">取消</el-button>
        <el-button type="primary" @click="createChapter">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Plus, Document, View, Edit, DocumentAdd } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const projects = ref([])
const books = ref([])
const chapters = ref([])

const currentProject = ref('')
const currentBook = ref('')
const currentChapter = ref('')

const content = ref('')
const chapterTitle = ref('')
const showPreview = ref(false)
const saving = ref(false)
const lastSaved = ref('')

const showNewChapterDialog = ref(false)
const newChapter = ref({
  title: '',
  order: 1
})

// 计算属性
const wordCount = computed(() => {
  return content.value.length
})

const previewContent = computed(() => {
  // 简单的Markdown预览（可以后续集成更强大的Markdown解析器）
  return content.value
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
})

// 生命周期
onMounted(() => {
  loadProjects()
})

// 监听器
watch(content, () => {
  // 自动保存（防抖）
  clearTimeout(window.autoSaveTimer)
  window.autoSaveTimer = setTimeout(() => {
    if (currentChapter.value && content.value) {
      autoSave()
    }
  }, 5000) // 5秒后自动保存
})

// 方法
async function loadProjects() {
  try {
    const result = await window.pywebview.api.get_projects()
    if (result.status === 'success') {
      projects.value = result.data
    }
  } catch (error) {
    ElMessage.error('加载项目失败: ' + error.message)
  }
}

async function onProjectChange() {
  books.value = []
  chapters.value = []
  currentBook.value = ''
  currentChapter.value = ''
  content.value = ''

  if (currentProject.value) {
    await loadBooks()
  }
}

async function loadBooks() {
  try {
    const result = await window.pywebview.api.get_books(currentProject.value)
    if (result.status === 'success') {
      books.value = result.data
    }
  } catch (error) {
    ElMessage.error('加载书籍失败: ' + error.message)
  }
}

async function onBookChange() {
  chapters.value = []
  currentChapter.value = ''
  content.value = ''

  if (currentBook.value) {
    await loadChapters()
  }
}

async function loadChapters() {
  try {
    const result = await window.pywebview.api.get_chapters(currentBook.value)
    if (result.status === 'success') {
      chapters.value = result.data
    }
  } catch (error) {
    ElMessage.error('加载章节失败: ' + error.message)
  }
}

async function onChapterChange() {
  if (currentChapter.value) {
    await loadChapterContent()
  }
}

async function selectChapter(chapterId) {
  currentChapter.value = chapterId
  await loadChapterContent()
}

async function loadChapterContent() {
  try {
    const result = await window.pywebview.api.get_chapter_content(currentChapter.value)
    if (result.status === 'success') {
      content.value = result.data.content || ''
      chapterTitle.value = result.data.title || ''
      lastSaved.value = formatDate(result.data.updatedAt)
    }
  } catch (error) {
    ElMessage.error('加载章节内容失败: ' + error.message)
  }
}

async function saveContent() {
  if (!currentChapter.value) {
    ElMessage.warning('请先选择章节')
    return
  }

  saving.value = true
  try {
    const result = await window.pywebview.api.save_chapter_content(currentChapter.value, {
      content: content.value,
      title: chapterTitle.value,
      wordCount: wordCount.value
    })

    if (result.status === 'success') {
      ElMessage.success('保存成功')
      lastSaved.value = new Date().toLocaleString()

      // 更新章节列表中的字数
      const chapter = chapters.value.find(c => c.id === currentChapter.value)
      if (chapter) {
        chapter.wordCount = wordCount.value
        chapter.updatedAt = new Date().toISOString()
      }
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

async function autoSave() {
  if (!currentChapter.value) return

  try {
    await window.pywebview.api.save_chapter_content(currentChapter.value, {
      content: content.value,
      title: chapterTitle.value,
      wordCount: wordCount.value
    })
    lastSaved.value = new Date().toLocaleString()
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

async function createChapter() {
  if (!newChapter.value.title.trim()) {
    ElMessage.warning('请输入章节标题')
    return
  }

  try {
    const result = await window.pywebview.api.create_chapter(currentBook.value, {
      title: newChapter.value.title,
      order: newChapter.value.order,
      content: ''
    })

    if (result.status === 'success') {
      ElMessage.success('章节创建成功')
      showNewChapterDialog.value = false
      newChapter.value = { title: '', order: 1 }

      // 重新加载章节列表
      await loadChapters()

      // 自动选择新创建的章节
      currentChapter.value = result.data.id
      await loadChapterContent()
    } else {
      ElMessage.error(result.message || '创建章节失败')
    }
  } catch (error) {
    ElMessage.error('创建章节失败: ' + error.message)
  }
}

function updateChapterTitle() {
  // 当标题失去焦点时自动保存
  if (currentChapter.value && chapterTitle.value) {
    saveContent()
  }
}

function onContentChange() {
  // 内容变化时的处理
}

function togglePreview() {
  showPreview.value = !showPreview.value
}

function formatDate(dateString) {
  if (!dateString) return '未知时间'
  return new Date(dateString).toLocaleString()
}
</script>

<style scoped>
.writing-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.editor-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.chapter-sidebar {
  width: 300px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--el-border-color);
  font-weight: 500;
}

.chapter-list {
  flex: 1;
  overflow-y: auto;
}

.chapter-item {
  padding: 15px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.chapter-item:hover {
  background-color: var(--el-fill-color-light);
}

.chapter-item.active {
  background-color: var(--el-color-primary-light-9);
  border-right: 3px solid var(--el-color-primary);
}

.chapter-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 5px;
}

.chapter-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.empty-chapters {
  text-align: center;
  padding: 40px 20px;
  color: var(--el-text-color-regular);
}

.empty-chapters .el-icon {
  margin-bottom: 15px;
  color: var(--el-text-color-placeholder);
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--el-text-color-regular);
}

.editor-placeholder .el-icon {
  margin-bottom: 20px;
  color: var(--el-text-color-placeholder);
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chapter-header {
  padding: 20px;
  border-bottom: 1px solid var(--el-border-color);
}

.chapter-title-input {
  margin-bottom: 10px;
}

.chapter-stats {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.editor-area {
  flex: 1;
  padding: 20px;
}

.content-editor {
  height: 100%;
}

.content-editor :deep(.el-textarea__inner) {
  height: 100% !important;
  resize: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  line-height: 1.6;
}

.preview-area {
  height: 100%;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  overflow-y: auto;
  line-height: 1.6;
}
</style>
