<template>
  <div>
    <FloatingWindow
      ref="windowRef"
      title="新建场景"
      :visible="visible"
      :initial-position="windowConfig.position"
      :width="windowConfig.size.width"
      :height="windowConfig.size.height"
      :pinnable="true"
      class="scene-create-window"
      @close="emit('update:visible', false)"
      @move="handleWindowMove"
      @resize="handleWindowResize"
    >
      <template #title-extra>
        <div class="title-extra">
          <el-tooltip
            content="新建卡池"
            placement="bottom"
            effect="light"
          >
            <el-button
              type="success"
              size="small"
              @click="showCreatePoolDialog"
              class="pool-btn"
            >
              新建卡池
            </el-button>
          </el-tooltip>
          <el-tooltip
            content="从JSON导入场景"
            placement="bottom"
            effect="light"
          >
            <el-button
              type="success"
              size="small"
              :disabled="!sceneForm.poolId"
              @click="showImportDialog"
              class="import-btn"
            >
              导入
            </el-button>
          </el-tooltip>
          <el-tooltip
            content="Ctrl + Enter 快速创建"
            placement="bottom"
            effect="light"
          >
            <el-button
              type="primary"
              size="small"
              :disabled="!sceneForm.poolId"
              @click="saveScene"
              class="create-btn"
            >
              创建
            </el-button>
          </el-tooltip>
        </div>
      </template>

      <div class="window-content">
        <el-form :model="sceneForm" label-width="100px">
          <el-form-item label="选择卡池" required>
            <el-select
              v-model="sceneForm.poolId"
              placeholder="请选择场景卡池"
              class="pool-select"
              @change="handlePoolSelect"
            >
              <el-option
                v-for="pool in scenePools"
                :key="pool.id"
                :label="pool.name"
                :value="pool.id"
              >
                <div class="pool-option">
                  <span class="pool-name">{{ pool.name }}</span>
                  <span class="pool-info">
                    <el-tag size="small" type="success" effect="light" class="scene-count">
                      {{ pool.scenes?.length || 0 }}个场景
                    </el-tag>
                    <span class="update-time">{{ formatDate(pool.updateTime) }}</span>
                  </span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="场景标题" required>
            <el-input
              v-model="sceneForm.title"
              placeholder="输入场景标题"
              clearable
              @keyup.enter="saveScene"
            />
          </el-form-item>

          <el-form-item label="场景描述">
            <el-input
              v-model="sceneForm.description"
              type="textarea"
              :rows="4"
              placeholder="输入场景描述"
              class="description-input"
            />
          </el-form-item>

          <el-form-item label="场景标签">
            <el-select
              v-model="sceneForm.tags"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请选择或创建标签"
              class="tags-select"
            >
              <el-option
                v-for="tag in availableTags"
                :key="tag"
                :label="tag"
                :value="tag"
              >
                <span class="tag-option">{{ tag }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </FloatingWindow>

    <!-- 导入场景对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入场景卡池"
      width="600px"
      class="import-dialog"
      :append-to-body="true"
    >
      <div class="import-content">
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="format-hint-title">
                <el-icon><InfoFilled /></el-icon>
                <span>查看JSON格式示例</span>
              </div>
            </template>
            <div class="format-hint-content">
              <pre>{
  "name": "卡池名称",
  "scenes": [
    {
      "title": "场景标题1",
      "description": "场景描述1",
      "tags": ["标签1", "标签2"],
      "position": {
        "left": 100,
        "top": 100
      }
    },
    {
      "title": "场景标题2",
      "description": "场景描述2",
      "tags": ["标签3", "标签4"],
      "position": {
        "left": 300,
        "top": 200
      }
    }
  ]
}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-input
          v-model="importJsonContent"
          type="textarea"
          :rows="12"
          placeholder="请输入JSON字符串"
          class="import-input"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确认导入</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新建卡池对话框 -->
    <el-dialog
      v-model="createPoolDialogVisible"
      title="新建卡池"
      width="500px"
      class="create-pool-dialog"
      :append-to-body="true"
    >
      <div class="create-pool-content">
        <el-form :model="poolForm" label-width="80px">
          <el-form-item label="卡池名称" required>
            <el-input
              v-model="poolForm.name"
              placeholder="请输入卡池名称"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createPoolDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmCreatePool">确认创建</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useConfigStore } from '../stores/config'
import FloatingWindow from './FloatingWindow.vue'
import { InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  visible: Boolean,
  bookId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['close', 'created', 'update:visible'])

// 弹窗引用
const windowRef = ref(null)

const configStore = useConfigStore()

// 场景相关状态
const scenePools = ref([])
const sceneForm = ref({
  poolId: '',
  title: '',
  description: '',
  tags: []
})

// 可用标签
const availableTags = ref([
  '战斗', '对话', '探索', '追逐', '相遇',
  '告别', '冲突', '和解', '发现', '选择'
])

// 计算窗口配置
const windowConfig = computed(() => {
  const editorConfig = configStore.state.config.editor
  return editorConfig?.sceneWindow || {
    position: { x: 120, y: 120 },
    size: { width: 600, height: 600 }
  }
})

// 窗口事件处理
const handleWindowMove = (position) => {
  configStore.updateConfigItem('editor.sceneWindow.position', position)
}

const handleWindowResize = (size) => {
  configStore.updateConfigItem('editor.sceneWindow.size', size)
}

// 加载卡池列表
const loadScenePools = async () => {
  try {
    const response = await window.pywebview.api.book_controller.get_scene_events(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      // 过滤掉虚拟的"全部场景"卡池
      scenePools.value = (result.data?.pools || []).filter(pool => !pool.isVirtual)
    } else {
      ElMessage.error(result.message || '加载卡池失败')
    }
  } catch (error) {
    console.error('加载卡池失败:', error)
    ElMessage.error('加载卡池失败：' + error.message)
  }
}

// 处理卡池选择
const handlePoolSelect = (poolId) => {
  const selectedPool = scenePools.value.find(p => p.id === poolId)
  if (selectedPool) {
    // 可以在这里添加额外的逻辑
  }
}

// 格式化日期
const formatDate = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 保存场景
const saveScene = async () => {
  try {
    if (!sceneForm.value.poolId) {
      ElMessage.error('请选择场景卡池')
      return
    }
    if (!sceneForm.value.title) {
      ElMessage.error('请输入场景标题')
      return
    }

    // 找到目标卡池
    const targetPool = scenePools.value.find(p => p.id === sceneForm.value.poolId)
    if (!targetPool) {
      ElMessage.error('卡池不存在')
      return
    }

    // 确保卡池有scenes数组
    if (!Array.isArray(targetPool.scenes)) {
      targetPool.scenes = []
    }

    // 创建新场景对象
    const newScene = {
      id: Date.now().toString(),
      title: sceneForm.value.title,
      description: sceneForm.value.description,
      tags: sceneForm.value.tags,
      position: {
        left: Math.random() * (800 - 240),
        top: Math.random() * (600 - 280)
      },
      zIndex: 1
    }

    // 添加场景到卡池
    targetPool.scenes.push(newScene)
    targetPool.updateTime = Date.now()

    // 保存更改
    const response = await window.pywebview.api.book_controller.save_scene_events(
      props.bookId,
      { pools: scenePools.value, currentPoolId: sceneForm.value.poolId }
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      emit('created')
      ElMessage.success('场景创建成功')
      
      // 保持当前卡池选中，但重置其他表单字段
      const currentPoolId = sceneForm.value.poolId
      sceneForm.value = {
        poolId: currentPoolId,
        title: '',
        description: '',
        tags: []
      }
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存场景失败:', error)
    ElMessage.error('保存失败：' + error.message)
  }
}

// 监听可见性变化
watch(() => props.visible, async (newVal) => {
  if (newVal && scenePools.value.length === 0) {
    await loadScenePools()
  }
})

// 在 script setup 部分添加 keydown 处理
const handleKeydown = (e) => {
  // 只有在弹窗可见且焦点在弹窗内时才处理快捷键
  if (!props.visible) return

  // 检查焦点是否在弹窗内
  const activeElement = document.activeElement
  const windowElement = windowRef.value?.$el
  if (!windowElement || !windowElement.contains(activeElement)) {
    return
  }

  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
    e.preventDefault()
    e.stopPropagation()
    saveScene()
  }
}

// 监听弹窗可见性变化，只在弹窗可见时添加快捷键监听
watch(() => props.visible, (newVal) => {
  if (newVal) {
    window.addEventListener('keydown', handleKeydown)
  } else {
    window.removeEventListener('keydown', handleKeydown)
  }
})

onMounted(() => {
  // 确保配置存在
  if (!configStore.state.config.editor.sceneWindow) {
    configStore.updateConfigItem('editor.sceneWindow', {
      position: { x: 120, y: 120 },
      size: { width: 600, height: 600 }
    })
  }
  // 如果弹窗已经可见，添加键盘事件监听
  if (props.visible) {
    window.addEventListener('keydown', handleKeydown)
  }
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
})

// 导入相关状态
const importDialogVisible = ref(false)
const importJsonContent = ref('')

// 新建卡池相关状态
const createPoolDialogVisible = ref(false)
const poolForm = ref({
  name: ''
})

// 显示导入对话框
const showImportDialog = () => {
  importDialogVisible.value = true
  importJsonContent.value = ''
}

// 显示新建卡池对话框
const showCreatePoolDialog = () => {
  createPoolDialogVisible.value = true
  poolForm.value.name = ''
}

// 确认导入
const confirmImport = async () => {
  try {
    if (!importJsonContent.value.trim()) {
      ElMessage.error('请输入JSON字符串')
      return
    }

    // 解析JSON
    const jsonContent = JSON.parse(importJsonContent.value)
    
    // 验证必要字段
    if (!jsonContent.name) {
      ElMessage.error('导入失败：缺少卡池名称')
      return
    }
    if (!Array.isArray(jsonContent.scenes)) {
      ElMessage.error('导入失败：场景列表格式不正确')
      return
    }

    // 创建新卡池对象
    const newPool = {
      id: Date.now().toString(),
      name: jsonContent.name,
      scenes: jsonContent.scenes.map(scene => ({
        id: Date.now().toString() + Math.random().toString(36).slice(2),
        title: scene.title,
        description: scene.description || '',
        tags: scene.tags || [],
        position: scene.position || {
          left: Math.random() * (800 - 240),
          top: Math.random() * (600 - 280)
        },
        zIndex: 1
      })),
      updateTime: Date.now()
    }

    // 添加新卡池
    scenePools.value.push(newPool)

    // 保存更改
    const response = await window.pywebview.api.book_controller.save_scene_events(
      props.bookId,
      { pools: scenePools.value, currentPoolId: newPool.id }
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('卡池导入成功')
      importDialogVisible.value = false
      
      // 自动选择新导入的卡池
      sceneForm.value.poolId = newPool.id
      handlePoolSelect(newPool.id)
      emit('created')
    } else {
      throw new Error(result.message || '导入失败')
    }
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('导入失败：JSON格式不正确')
    } else {
      console.error('导入失败:', error)
      ElMessage.error('导入失败：' + error.message)
    }
  }
}

// 确认创建卡池
const confirmCreatePool = async () => {
  try {
    if (!poolForm.value.name.trim()) {
      ElMessage.error('请输入卡池名称')
      return
    }

    // 创建新卡池对象
    const newPool = {
      id: Date.now().toString(),
      name: poolForm.value.name,
      scenes: [],
      updateTime: Date.now()
    }

    // 添加新卡池
    scenePools.value.push(newPool)

    // 保存更改
    const response = await window.pywebview.api.book_controller.save_scene_events(
      props.bookId,
      { pools: scenePools.value, currentPoolId: newPool.id }
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('卡池创建成功')
      createPoolDialogVisible.value = false
      
      // 自动选择新创建的卡池
      sceneForm.value.poolId = newPool.id
      handlePoolSelect(newPool.id)
    } else {
      throw new Error(result.message || '创建失败')
    }
  } catch (error) {
    console.error('创建卡池失败:', error)
    ElMessage.error('创建失败：' + error.message)
  }
}
</script>

<style lang="scss" scoped>
.scene-create-window {
  min-width: 320px;
  min-height: 400px;
  
  :deep(.floating-window-content) {
    background: var(--el-bg-color-page);
  }

  :deep(.floating-window-header) {
    .window-title {
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.window-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;

  .el-form {
    height: 100%;
    
    :deep(.el-form-item__label) {
      font-size: 15px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      line-height: 32px;
      padding-right: 24px;
    }
    
    :deep(.el-input__wrapper),
    :deep(.el-textarea__inner) {
      box-shadow: none;
      border: 1px solid var(--el-border-color);
      border-radius: 8px;
      transition: all 0.3s ease;
      background: var(--el-bg-color-blank);
      font-size: 15px;
      line-height: 1.6;
      padding: 8px 16px;
      
      &:hover {
        border-color: var(--el-border-color-darker);
      }
      
      &.is-focus {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
      }

      &::placeholder {
        font-size: 14px;
        color: var(--el-text-color-placeholder);
      }
    }

    :deep(.el-select) {
      .el-input__wrapper {
        padding: 0 16px;
        height: 40px;
        line-height: 40px;
      }

      &.tags-select {
        .el-select__tags {
          padding: 4px 0;
          
          .el-tag {
            margin: 2px 4px;
            height: 28px;
            padding: 0 10px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .pool-select,
  .tags-select {
    width: 100%;
  }
}

.pool-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 12px;
}

.pool-name {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 15px;
  font-weight: 500;
}

.pool-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 12px;
  
  .scene-count {
    font-size: 13px;
    padding: 0 10px;
    height: 26px;
    line-height: 24px;
    border-radius: 4px;
  }
  
  .update-time {
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }
}

.description-input {
  :deep(.el-textarea__inner) {
    font-size: 15px;
    line-height: 1.6;
    min-height: 120px !important;
    
    &::placeholder {
      color: var(--el-text-color-placeholder);
    }
  }
}

.tag-option {
  font-size: 15px;
  color: var(--el-text-color-regular);
  padding: 8px 0;
}

.title-extra {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .create-btn, .import-btn, .pool-btn {
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    height: 36px;
    line-height: 20px;
  }

  .create-btn {
    background: rgba(var(--el-color-primary-rgb), 0.1);
    border-color: rgba(var(--el-color-primary-rgb), 0.2);
    color: var(--el-color-primary);

    &:hover:not(:disabled) {
      background: var(--el-color-primary);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
    }

    &:disabled {
      background: var(--el-fill-color-light);
      border-color: var(--el-border-color-lighter);
      color: var(--el-text-color-disabled);
      cursor: not-allowed;
    }
  }

  .import-btn, .pool-btn {
    background: rgba(var(--el-color-success-rgb), 0.1);
    border-color: rgba(var(--el-color-success-rgb), 0.2);
    color: var(--el-color-success);

    &:hover:not(:disabled) {
      background: var(--el-color-success);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--el-color-success-rgb), 0.2);
    }

    &:disabled {
      background: var(--el-fill-color-light);
      border-color: var(--el-border-color-lighter);
      color: var(--el-text-color-disabled);
      cursor: not-allowed;
    }
  }
}

.import-dialog, .create-pool-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    background: var(--el-bg-color);
    
    .el-dialog__header {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-light);
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      background: var(--el-bg-color-page);
    }
    
    .el-dialog__footer {
      margin: 0;
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
    }
  }
}

.import-content {
  .el-collapse {
    margin-bottom: 16px;
    border: none;
    
    :deep(.el-collapse-item) {
      border: 1px solid var(--el-border-color);
      border-radius: 8px;
      overflow: hidden;
      
      .el-collapse-item__header {
        padding: 12px 16px;
        font-size: 15px;
        font-weight: normal;
        color: var(--el-text-color-regular);
        background: var(--el-fill-color-blank);
        border-bottom: none;
        
        &.is-active {
          border-bottom: 1px solid var(--el-border-color);
        }
      }
      
      .el-collapse-item__content {
        padding: 16px;
        background: var(--el-bg-color);
      }
    }
  }

  .format-hint-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--el-text-color-regular);
    
    .el-icon {
      font-size: 16px;
      color: var(--el-color-info);
    }
  }

  .format-hint-content {
    pre {
      margin: 0;
      padding: 16px;
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      
      font-size: 14px;
      line-height: 1.6;
      color: var(--el-text-color-primary);
      white-space: pre-wrap;
      
      &:hover {
        border-color: var(--el-border-color);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
    }
  }

  .import-input {
    margin-top: 16px;
    
    :deep(.el-textarea__inner) {
      
      font-size: 14px;
      line-height: 1.6;
      padding: 16px;
      border-radius: 8px;
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color);
      color: var(--el-text-color-primary);
      transition: all 0.3s ease;
      min-height: 240px !important;
      
      &:hover {
        border-color: var(--el-border-color-darker);
      }
      
      &:focus {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
      }
    }
  }
}

.create-pool-content {
  .el-form {
    margin-top: 16px;
  }
}
</style> 