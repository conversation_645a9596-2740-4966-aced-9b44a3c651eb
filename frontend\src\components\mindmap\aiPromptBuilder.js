/**
 * AI提示词构建器 - 专门用于思维导图的AI生成
 */
export class AIPromptBuilder {
  /**
   * 构建子节点生成的提示词
   * @param {Object} context - 节点上下文信息
   * @param {number} count - 期望生成的子节点数量
   * @returns {string} 提示词
   */
  buildChildrenPrompt(context, count = 5) {
    const { path, currentTitle, currentContent, level, siblings, parentTitle, existingChildren } = context

    let prompt = `你是一个专业的思维导图助手，请为以下节点生成${count}个合适的子节点。

**当前节点信息：**
- 节点路径：${path}
- 当前标题：${currentTitle}
- 当前内容：${currentContent || '无'}
- 节点层级：第${level}级

**上下文信息：**
- 父节点：${parentTitle || '无'}
- 兄弟节点：${siblings.length > 0 ? siblings.join('、') : '无'}
- 已有子节点：${existingChildren.length > 0 ? existingChildren.join('、') : '无'}

**生成要求：**
1. 生成${count}个子节点，每个子节点一行
2. 子节点应该与当前节点主题相关且逻辑清晰
3. 避免与已有子节点重复
4. 使用简洁明确的标题
5. 可以使用以下格式之一：
   - 纯标题：标题名称
   - 带描述：标题：简短描述
   - 序号格式：1. 标题

**示例场景理解：**
如果当前是"都市网络小说设计"这样的创作项目，子节点应该包括：人物设定、世界背景、情节大纲、写作技巧等相关要素。

请生成子节点：`

    return prompt
  }

  /**
   * 构建节点内容扩展的提示词
   * @param {Object} context - 节点上下文信息
   * @returns {string} 提示词
   */
  buildContentPrompt(context) {
    const { path, currentTitle, currentContent, level, parentTitle } = context

    let prompt = `你是一个专业的内容创作助手，请为以下思维导图节点扩展详细内容。

**节点信息：**
- 节点路径：${path}
- 节点标题：${currentTitle}
- 当前内容：${currentContent || '无'}
- 节点层级：第${level}级
- 父节点：${parentTitle || '无'}

**扩展要求：**
1. 基于节点标题和上下文，生成详细的内容描述
2. 内容应该具体、实用、有指导意义
3. 如果是创作类项目，提供具体的创作建议和要点
4. 内容长度适中，重点突出
5. 使用Markdown格式，可以包含列表、重点标记等

请生成扩展内容：`

    return prompt
  }

  /**
   * 构建相关节点生成的提示词
   * @param {Object} context - 节点上下文信息
   * @param {number} count - 期望生成的相关节点数量
   * @returns {string} 提示词
   */
  buildRelatedPrompt(context, count = 3) {
    const { path, currentTitle, currentContent, level, siblings } = context

    let prompt = `你是一个专业的思维导图助手，请为以下节点生成${count}个相关的同级节点。

**当前节点信息：**
- 节点路径：${path}
- 当前标题：${currentTitle}
- 当前内容：${currentContent || '无'}
- 节点层级：第${level}级
- 现有兄弟节点：${siblings.length > 0 ? siblings.join('、') : '无'}

**生成要求：**
1. 生成${count}个与当前节点同级且相关的节点
2. 新节点应该与当前节点在同一个主题范围内
3. 避免与现有兄弟节点重复
4. 保持逻辑一致性和完整性
5. 每个节点一行，使用简洁的标题

请生成相关节点：`

    return prompt
  }

  /**
   * 构建整体思维导图优化的提示词
   * @param {Object} rootNode - 根节点
   * @param {string} optimizeType - 优化类型
   * @returns {string} 提示词
   */
  buildOptimizePrompt(rootNode, optimizeType = 'structure') {
    const structure = this.getTreeStructure(rootNode)

    let prompt = `你是一个专业的思维导图优化专家，请分析并优化以下思维导图结构。

**当前思维导图结构：**
${structure}

**优化类型：${optimizeType}**

**优化要求：**`

    switch (optimizeType) {
      case 'structure':
        prompt += `
1. 分析当前结构的逻辑性和完整性
2. 建议调整节点层级和分类
3. 指出缺失的重要节点
4. 提供结构优化建议`
        break
      case 'content':
        prompt += `
1. 分析各节点内容的充实程度
2. 建议需要扩展内容的节点
3. 提供内容改进建议
4. 确保内容的一致性和专业性`
        break
      case 'balance':
        prompt += `
1. 分析各分支的平衡性
2. 建议调整过于稀疏或密集的分支
3. 确保思维导图的视觉平衡
4. 提供节点分布优化建议`
        break
    }

    prompt += `

请提供具体的优化建议：`

    return prompt
  }

  /**
   * 获取树形结构的文本表示
   * @param {Object} node - 节点
   * @param {number} depth - 深度
   * @returns {string} 结构文本
   */
  getTreeStructure(node, depth = 0) {
    const indent = '  '.repeat(depth)
    let structure = `${indent}- ${node.title}`
    
    if (node.content) {
      structure += ` (${node.content.substring(0, 50)}${node.content.length > 50 ? '...' : ''})`
    }
    
    structure += '\n'

    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        structure += this.getTreeStructure(child, depth + 1)
      }
    }

    return structure
  }

  /**
   * 构建专业领域的提示词模板
   * @param {string} domain - 专业领域
   * @param {Object} context - 上下文
   * @returns {string} 专业提示词
   */
  buildDomainSpecificPrompt(domain, context) {
    const basePrompt = this.buildChildrenPrompt(context)
    
    const domainTemplates = {
      'novel': `
**小说创作专业指导：**
- 人物设定：主角、配角、反派的性格、背景、关系
- 世界观：时代背景、社会环境、规则设定
- 情节结构：开端、发展、高潮、结局
- 写作技巧：叙述方式、对话技巧、节奏控制`,
      
      'business': `
**商业项目专业指导：**
- 市场分析：目标市场、竞争对手、市场机会
- 产品策略：功能定义、差异化、价值主张
- 运营计划：团队组建、资源配置、时间规划
- 风险管理：潜在风险、应对策略、备选方案`,
      
      'education': `
**教育培训专业指导：**
- 学习目标：知识点、技能要求、能力培养
- 教学方法：理论讲解、实践操作、互动讨论
- 评估体系：考核标准、评价方式、反馈机制
- 资源配置：教材选择、工具使用、环境准备`
    }

    return basePrompt + (domainTemplates[domain] || '')
  }
}

export default new AIPromptBuilder()
