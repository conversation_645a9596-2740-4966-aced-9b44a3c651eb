// 定义提示语参数常量
export const PROMPT_PARAMS = {
    ENTITY: 'entity',
    ENTITY_ID: 'entity_id',
    SELECTED_TEXT: 'selected_text',
    CONTEXT: 'context'
}

// 定义可用参数配置
export const AVAILABLE_PARAMS = [

    {
        key: PROMPT_PARAMS.ENTITY_ID,
        description: '插入指定实体的JSON数据，会弹出选择对话框',
        previewValue: '{ "name": "具体实体", "description": "实体描述" }'
    },
    {
        key: PROMPT_PARAMS.SELECTED_TEXT,
        description: '插入选中的文本内容',
        previewValue: '这是一段被选中的示例文本，用于预览效果。'
    },
    {
        key: PROMPT_PARAMS.CONTEXT,
        description: '插入当前章节的完整内容作为上下文',
        previewValue: '这是一段章节上下文的示例内容，包含了当前章节的相关信息...'
    }
]