/* 原生应用通用样式 */
.native-app-style {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;

  /* 禁用右键菜单 */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;

  /* 禁用图片和媒体元素拖拽 */
  img, svg, canvas, video, audio {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    pointer-events: none;
  }

  /* 按钮和交互元素恢复指针事件 */
  button, .el-button, input, textarea, select, .el-input, .el-select, .el-textarea {
    pointer-events: auto;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 输入框内容可以选择 */
  input, textarea, .el-input__inner, .el-textarea__inner {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }
}

.scene-cards-container {
    padding: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
    background: var(--el-bg-color-page);

    /* 应用原生应用样式 */
    @extend .native-app-style;
  }

  .action-bar {
    display: flex;
    gap: 16px;
    padding: 16px;
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    align-items: center;
    position: relative;
    z-index: 10;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);

    /* 原生应用样式 - 禁用选择和拖拽 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;

    .book-select, .pool-select, .draw-count-select {
      :deep(.el-input__wrapper) {
        height: 40px;
        box-shadow: 0 0 0 1px var(--el-border-color-lighter) inset;
        transition: all 0.3s ease;
        background: var(--el-fill-color-blank);
        padding: 0 16px;
        
        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary-light-5) inset;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary-light-5) inset !important;
          border-color: transparent !important;
        }
      }
      
      :deep(.el-input__suffix) {
        display: flex;
        align-items: center;
      }
      
      :deep(.el-select__wrapper) {
        height: 40px;
        border: none;
        box-shadow: none !important;
      }
      
      // 移除激活状态的蓝色边框
      :deep(.el-select:hover .el-input__wrapper),
      :deep(.el-select.is-focus .el-input__wrapper) {
        box-shadow: 0 0 0 1px var(--el-color-primary-light-5) inset !important;
        border-color: transparent !important;
      }
    }

    .book-select {
      width: 200px;
    }

    .pool-select {
      width: 240px;
    }

    .draw-count-select {
      width: 140px;
    }

    // 确保所有按钮高度一致
    .el-button, .el-dropdown .el-button {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  :deep(.el-dropdown-menu__item) {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    transition: all 0.3s ease;

    .el-icon {
      font-size: 16px;
      color: var(--el-color-primary);
    }

    &:not(.is-disabled):hover {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }

    &.is-disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }

  .dropdown-item-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    
    .el-icon {
      font-size: 16px;
    }
  }
  
  .cards-grid {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: var(--el-bg-color);
    border-radius: 16px;
    min-height: 600px;
    height: calc(100vh - 200px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.8);
    backdrop-filter: blur(4px);

    background-image:
      linear-gradient(rgba(var(--el-border-color-lighter-rgb), 0.2) 1px, transparent 1px),
      linear-gradient(90deg, rgba(var(--el-border-color-lighter-rgb), 0.2) 1px, transparent 1px);
    background-size: 20px 20px;

    /* 原生应用样式 - 禁用选择和拖拽 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
  }
  
  .scene-card-wrapper {
    position: absolute;
    cursor: move;
    user-select: none;
    transform-origin: top left;
    width: 240px; /* 固定宽度 */
    transition: none !important; /* 禁用可能导致问题的过渡效果 */

    /* 原生应用样式 - 禁用图片和元素拖拽 */
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;

    /* 禁用卡片内所有图片和元素的拖拽 */
    img, svg, canvas, video {
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      pointer-events: none;
    }
  }
  
  .scene-card-wrapper .el-card {
    width: 100%;
    height: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: visible;
  }
  
  .scene-card-wrapper .dragging {
    z-index: 1000 !important;
    transform: rotate(2deg) scale(1.02) !important;
    opacity: 0.9;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
  }
  
  .ghost-card {
    opacity: 0.5;
    background: var(--el-color-primary-light-9);
  }
  
  .add-card {
    width: 240px;
    height: 280px;
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--el-text-color-secondary);
  }
  
  .add-card:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
    transform: translateY(-2px);
  }
  
  .add-card .el-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }
  
  .add-card span {
    font-size: 16px;
  }
  
  .draw-dialog :deep(.el-dialog) {
    margin: 15vh auto 0 !important;
    margin-bottom: 0 !important;
  }

  .draw-dialog :deep(.el-dialog__body) {
    padding: 0;
    overflow: hidden;
  }

  .draw-result {
    background: var(--el-bg-color-page);
    height: 100%;
    overflow: hidden;
  }

  .draw-content {
    display: flex;
    gap: 24px;
    padding: 24px;
    height: 100%;
    overflow: hidden;
  }

  .scenes-section {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .inspiration-section {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .drawn-cards {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);
  }

  .drawn-card-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    flex: 1;
    min-width: 0;
  }

  .scene-connector {
    position: absolute;
    right: -28px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--el-color-primary);
    font-size: 24px;
  }
  
  .combination-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: var(--el-text-color-regular);
    font-size: 16px;
    margin-top: 16px;
    padding: 16px;
    background: var(--el-color-primary-light-9);
    border-radius: 8px;
  }
  
  .inspiration-input {
    background: var(--el-bg-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);
  }
  
  .inspiration-input .el-textarea {
    flex: 1;
    overflow: hidden;
  }
  
  .inspiration-input .el-textarea :deep(.el-textarea__inner) {
    height: 100% !important;
    resize: none;
  }
  
  .inspiration-meta {
    margin-top: 16px;
    padding: 16px;
    background: var(--el-fill-color-blank);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex-shrink: 0;
    box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.4);
  }
  
  .inspiration-actions {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
  }
  
  .inspiration-label {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
    flex-shrink: 0;
  }
  
  .inspiration-rating {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .rating-label {
    color: var(--el-text-color-regular);
    font-size: 14px;
    white-space: nowrap;
  }
  
  .rating-slider {
    flex: 1;
    margin-right: 16px;
  }
  
  .compact-card {
    width: 100%;
  }
  
  .compact-card :deep(.el-card__body) {
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .scene-number {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    font-weight: 500;
    text-align: center;
  }

  
  .history-dialog :deep(.el-dialog) {
    position: fixed;
    top: 0;
    left: 0;
    margin: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex;
    flex-direction: column;
    border-radius: 0;
    overflow: hidden;
    backdrop-filter: blur(8px);
    border: none;
    background: var(--el-bg-color-page);
  }

  .history-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 0;
    height: 64px;
    min-height: 64px;
    flex: 0 0 64px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    backdrop-filter: blur(12px);
    background: rgba(var(--el-bg-color-rgb), 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .history-dialog :deep(.el-dialog__body) {
    margin: 0;
    padding: 0;
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .history-container {
    position: absolute;
    top: 11%;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }

  .history-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .history-list {
    user-select:none;
    width: 480px;
    border-right: 1px solid var(--el-border-color-lighter);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .history-list-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
  }

  .history-detail-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 24px 32px;
    overflow: hidden;
  }

  .history-scenes {
    user-select:none;
    flex: 0 0 240px;
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px var(--el-mask-color-extra-light);
    border: 1px solid var(--el-border-color-lighter);
    margin-bottom: 24px;
    overflow: hidden;
  }

  .history-inspiration {
    user-select:none;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px var(--el-mask-color-extra-light);
    border: 1px solid var(--el-border-color-lighter);
    overflow: hidden;
  }

  .inspiration-content {
    user-select:text;
    flex: 1;
    overflow-y: auto;
    white-space: pre-wrap;
    line-height: 1.8;
    color: var(--el-text-color-regular);
    padding: 20px;
    background: var(--el-fill-color-blank);
    border-radius: 4px;
    font-size: 14px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .history-footer {
    user-select:none;
    flex: 0 0 60px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dialog-header-content {
    user-select: none;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    background: var(--el-bg-color);
  }

  .dialog-header-content h4 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .close-btn {
    font-size: 20px;
    color: var(--el-text-color-secondary);
    border: none;
    background: transparent;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .close-btn:hover {
    color: var(--el-text-color-primary);
    transform: scale(1.1);
  }

  .history-item {
    background: var(--el-bg-color);
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px var(--el-mask-color-extra-light);
    cursor: pointer;
    transition: all 0.3s;
    padding: 16px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--el-mask-color-extra-light);
  }

  .history-item.active {
    border: 1px solid var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  .history-header {
    padding: 0;
  }

  .header-main {
    margin-bottom: 12px;
  }

  .history-meta-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .pool-tag {
    font-size: 12px;
  }

  .history-time {
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }

  .history-brief {
    font-size: 14px;
    line-height: 1.6;
    color: var(--el-text-color-regular);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 12px;
  }

  .history-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  .history-tags {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .scenes-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .scenes-title:after {
    content: "";
    flex: 1;
    height: 1px;
    background: var(--el-border-color-lighter);
  }

  .scenes-list {
    display: flex;
    flex-direction: row;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 8px;
    height: calc(100% - 40px);
  }

  .history-scene {
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 20px;
    position: relative;
    width: 320px;
    flex-shrink: 0;
    border: 1px solid var(--el-border-color-lighter);
    display: flex;
    flex-direction: column;
  }

  .inspiration-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .inspiration-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .inspiration-title:after {
    content: "";
    flex: 1;
    height: 1px;
    background: var(--el-border-color-lighter);
  }

  .pool-info {
    margin-left: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  .create-pool-btn {
    padding: 0;
    margin-right: 8px;
    transition: transform 0.2s ease, color 0.2s ease;
    
    &:hover {
      color: var(--el-color-primary);
      transform: scale(1.2);
    }
  }
  
  .draw-count-select {
    width: 140px;
  }

  .scenes-list::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scenes-list::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 3px;
  }

  .scenes-list::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }

  .history-inspiration::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .history-inspiration::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 3px;
  }

  .history-inspiration::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }

  .history-list::-webkit-scrollbar {
    width: 4px;
  }

  .history-list::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 2px;
  }

  .history-list::-webkit-scrollbar-track {
    background: transparent;
  }

  .inspiration-content::-webkit-scrollbar {
    width: 4px;
  }

  .inspiration-content::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 2px;
  }

  .inspiration-content::-webkit-scrollbar-track {
    background: transparent;
  }

  :deep(.el-overlay) {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    overflow: hidden !important;
    height: 100vh !important;
    width: 100vw !important;
  }

  .history-count {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-left: 8px;
    font-weight: normal;
  }

  .scene-count {
    margin-left: 8px;
  }

  .scene-description {
    font-size: 13px;
    color: var(--el-text-color-secondary);
    line-height: 1.6;
    margin: 8px 0 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .pool-option {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .pool-name {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .delete-pool-btn {
    opacity: 0;
    transition: opacity 0.3s, transform 0.2s;
    margin-left: 8px;
    
    &:hover {
      transform: scale(1.2);
      color: var(--el-color-danger);
    }
  }

  .pool-option:hover .delete-pool-btn {
    opacity: 1;
  }

  .el-select-dropdown__item:hover {
    background-color: var(--el-fill-color-light);
  }

  .delete-history-btn {
    opacity: 0;
    transition: opacity 0.3s;
  }

  .history-item:hover .delete-history-btn {
    opacity: 1;
  }

  .delete-history-btn:hover {
    transform: scale(1.1);
  }

  /* 场景对话框样式 */
  .scene-dialog :deep(.el-dialog) {
  
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .scene-dialog :deep(.el-dialog__header) {

    margin: 0;
    padding: 24px 32px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color);
  }

  .scene-dialog :deep(.el-dialog__title) {
    font-size: 22px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .scene-dialog :deep(.el-dialog__headerbtn) {
    top: 24px;
    right: 24px;
    font-size: 20px;
  }

  .scene-dialog :deep(.el-dialog__body) {
    padding: 32px;
    background: var(--el-bg-color-page);
  }

  .scene-form :deep(.el-form-item__label) {
    user-select: none;
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    padding-right: 24px;
  }

  .scene-title-input :deep(.el-input__wrapper) {
    padding: 8px 16px;
    font-size: 16px;
    box-shadow: none;
    border: 1px solid var(--el-border-color);
    transition: all 0.3s;
  }

  .scene-title-input :deep(.el-input__wrapper:hover),
  .scene-title-input :deep(.el-input__wrapper.is-focus) {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  .scene-description-input :deep(.el-textarea__inner) {
    padding: 16px;
    font-size: 15px;
    line-height: 1.6;
    border: 1px solid var(--el-border-color);
    box-shadow: none;
    transition: all 0.3s;
  }

  .scene-description-input :deep(.el-textarea__inner:hover),
  .scene-description-input :deep(.el-textarea__inner:focus) {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  .scene-tags-select {
    width: 100%;
  }

  .scene-tags-select :deep(.el-select__wrapper) {
    padding: 8px 16px;
    font-size: 15px;
  }

  .scene-dialog :deep(.el-dialog__footer) {
    padding: 24px 32px;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }

  .dialog-footer .el-button {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    transition: all 0.3s;
  }

  .dialog-footer .cancel-btn {
    border: 1px solid var(--el-border-color);
  }

  .dialog-footer .cancel-btn:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  .dialog-footer .save-btn {
    font-weight: 500;
  }

  .dialog-footer .save-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--el-color-primary-light-5);
  }

  /* 输入框placeholder样式 */
  .scene-dialog :deep(.el-input__inner::placeholder),
  .scene-dialog :deep(.el-textarea__inner::placeholder) {
    color: var(--el-text-color-placeholder);
    font-size: 14px;
  }

  /* 标签选择器样式 */
  .scene-tags-select :deep(.el-tag) {
    border-radius: 4px;
    padding: 4px 8px;
    margin: 2px;
  }

  .scene-tags-select :deep(.el-select__tags-text) {
    font-size: 14px;
  }

  .related-pools-tooltip {
    padding: 8px;
    min-width: 200px;
  }

  .tooltip-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #fff;
  }

  .related-pool-item {
    font-size: 13px;
    line-height: 1.6;
    color: #eee;
    padding: 4px 0;
  }

  .related-pool-item:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dropdown-item-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  :deep(.el-dropdown-menu__item) {
    padding: 5px 16px;
    line-height: 1.5;
  }

  :deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
    background-color: var(--el-fill-color-light);
  }

  :deep(.el-dropdown-menu__item.is-disabled) {
    opacity: 0.7;
  }

  :deep(.el-dropdown-menu__item .el-icon) {
    font-size: 16px;
    color: var(--el-color-primary);
  }

  /* 添加卡池拖拽相关样式 */
  .manage-pools-btn {
    margin-left: 6px;
    margin-right: 0;
    padding: 2px;
    font-size: 14px;
    transition: transform 0.2s;
    
    &:hover {
      color: var(--el-color-primary);
      transform: scale(1.1);
    }
  }

  .manage-pools-container {
    padding: 0 0 16px;
  }

  .manage-pools-hint {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-bottom: 16px;
    line-height: 1.5;
    padding: 8px 12px;
    background: var(--el-color-info-light-9);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:before {
      content: "💡";
      font-size: 16px;
    }
  }

  .pool-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: var(--el-bg-color);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.2s;
    
    &:hover {
      border-color: var(--el-color-primary-light-5);
      transform: translateY(-2px);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
    }
  }

  .ghost-pool {
    opacity: 0.5;
    background: var(--el-color-primary-light-9) !important;
    border: 1px dashed var(--el-color-primary) !important;
  }

  .drag-handle {
    cursor: move;
    font-size: 18px;
    color: var(--el-text-color-secondary);
    margin-right: 12px;
    flex-shrink: 0;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }

  .pool-item-content {
    flex: 1;
    min-width: 0;
    margin-right: 16px;
  }

  .pool-item-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .pool-item-info {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--el-text-color-secondary);
    font-size: 13px;
  }

  .pool-item-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  .select-pool-btn, .rename-pool-btn, .delete-pool-btn {
    padding: 8px;
    border-radius: 4px;
    font-size: 16px;
    
    &:hover {
      transform: scale(1.15);
    }
  }

  .select-pool-btn {
    color: var(--el-color-success);
  }

  .rename-pool-btn {
    color: var(--el-color-warning);
  }

  .delete-pool-btn {
    color: var(--el-color-danger);
  }

  :deep(.el-divider__text) {
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: var(--el-bg-color-overlay);
  }

  .pools-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .search-wrapper {
    flex: 1;
  }

  .pools-actions {
    display: flex;
    gap: 16px;
  }

  .scene-count-tag {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    background: var(--el-color-info-light-9);
    padding: 4px 8px;
    border-radius: 4px;
  }

  .manage-pools-dialog :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .manage-pools-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 24px 32px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color);
  }

  .manage-pools-dialog :deep(.el-dialog__title) {
    font-size: 22px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .manage-pools-dialog :deep(.el-dialog__headerbtn) {
    top: 24px;
    right: 24px;
    font-size: 20px;
  }

  .manage-pools-dialog :deep(.el-dialog__body) {
    padding: 32px;
    background: var(--el-bg-color-page);
  }

  .manage-pools-dialog :deep(.el-scrollbar) {
    height: 400px;
  }

  .is-searched {
    background: var(--el-color-info-light-9);
  }

  /* 卡池管理对话框样式优化 */
  .manage-pools-dialog {
    :deep(.el-dialog) {
      display: flex;
      flex-direction: column;
      margin-top: 5vh !important;
      max-height: 90vh;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    :deep(.el-dialog__header) {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
    }
    
    :deep(.el-dialog__title) {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    
    :deep(.el-dialog__body) {
      flex: 1;
      padding: 20px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    :deep(.el-dialog__footer) {
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
    }
  }

  .manage-pools-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .pools-toolbar {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;
    
    .search-wrapper {
      flex: 1;
    }
    
    .pools-actions {
      flex-shrink: 0;
    }
  }

  .pools-scrollbar {
    flex: 1;
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    padding: 4px;
    background: var(--el-bg-color);
    
    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }
    
    :deep(.el-scrollbar__bar.is-horizontal) {
      display: none;
    }
    
    :deep(.el-empty) {
      padding: 40px 0;
    }
  }

  .pool-item {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    margin-bottom: 10px;
    background: var(--el-bg-color-overlay);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.2s;
    
    &:hover {
      border-color: var(--el-color-primary-light-5);
      transform: translateY(-2px);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
    }
    
    &.is-searched {
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-5);
    }
    
    &:last-child {
      margin-bottom: 4px;
    }
  }

  .scene-count-tag {
    font-size: 12px;
    border-radius: 4px;
    padding: 0 8px;
    height: 22px;
    line-height: 22px;
    background: var(--el-fill-color);
    color: var(--el-text-color-secondary);
    border: none;
  }

  .manage-pools-hint {
    background: var(--el-fill-color-light);
    border-left: 4px solid var(--el-color-info);
    padding: 10px 16px;
    margin-bottom: 16px;
    border-radius: 4px;
    color: var(--el-text-color-regular);
    font-size: 13px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:before {
      content: "💡";
      font-size: 16px;
    }
  }

  .pool-item-actions {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
  }

  .top-pool-btn, .select-pool-btn, .rename-pool-btn, .delete-pool-btn {
    padding: 8px;
    border-radius: 4px;
    font-size: 16px;
    
    &:hover {
      transform: scale(1.15);
    }
    
    &.is-disabled {
      opacity: 0.4;
      pointer-events: none;
    }
  }

  .top-pool-btn {
    color: var(--el-color-info);
    
    &:hover {
      color: var(--el-color-info-dark-2);
    }
  }

  /* 优化管理弹窗样式 */
  .manage-pools-dialog {
    :deep(.el-overlay) {
      overflow: hidden !important;
    }
    
    :deep(.el-dialog) {
      margin: 5vh auto !important;
      position: relative;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    :deep(.el-dialog__header) {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
      position: relative;
      z-index: 10;
    }
    
    :deep(.el-dialog__body) {
      flex: 1;
      padding: 20px;
      overflow: hidden;
      position: relative;
      max-height: calc(90vh - 140px); /* 减去header和footer的高度 */
    }
    
    :deep(.el-dialog__footer) {
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
      position: relative;
      z-index: 10;
    }
  }

  .manage-pools-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    position: relative;
  }

  .pools-scrollbar {
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    padding: 4px;
    background: var(--el-bg-color);
    height: calc(100% - 80px); /* 减去工具栏和提示的高度 */
    overflow: hidden;
    
    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }
    
    :deep(.el-scrollbar__bar.is-horizontal) {
      display: none;
    }
  }

  /* 禁止对话框外部内容滚动 */
  :deep(.el-overlay-dialog) {
    overflow: hidden !important;
    
    .el-dialog__wrapper {
      overflow: hidden !important;
    }
  }

  /* 美化返回顶部按钮 */
  .scroll-top-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    height: 36px;
    padding: 0 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-primary);
    background: var(--el-bg-color-overlay);
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.5);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    /* 拟态效果 */
    box-shadow: 
      2px 2px 5px rgba(0, 0, 0, 0.05),
      -2px -2px 5px rgba(255, 255, 255, 0.2),
      inset 0 0 0 rgba(0, 0, 0, 0.05),
      inset 0 0 0 rgba(255, 255, 255, 0.2);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        135deg,
        rgba(var(--el-color-primary-rgb), 0.05) 0%,
        transparent 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .el-icon {
      font-size: 16px;
      transition: transform 0.3s ease;
    }
    
    span {
      position: relative;
    }
    
    &:hover {
      color: var(--el-color-primary-dark-2);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
      transform: translateY(-2px);
      box-shadow: 
        3px 3px 8px rgba(0, 0, 0, 0.08),
        -3px -3px 8px rgba(255, 255, 255, 0.3),
        inset 0 0 0 rgba(0, 0, 0, 0),
        inset 0 0 0 rgba(255, 255, 255, 0);
      
      &::before {
        opacity: 1;
      }
      
      .el-icon {
        transform: translateY(-2px);
      }
    }
    
    &:active {
      transform: translateY(1px);
      box-shadow: 
        1px 1px 3px rgba(0, 0, 0, 0.05),
        -1px -1px 3px rgba(255, 255, 255, 0.1),
        inset 2px 2px 5px rgba(0, 0, 0, 0.05),
        inset -2px -2px 5px rgba(255, 255, 255, 0.1);
    }
  }

  /* 调整工具栏样式，使其更协调 */
  .pools-toolbar {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    align-items: center;
    
    .search-wrapper {
      flex: 1;
      
      :deep(.el-input__wrapper) {
        box-shadow: 
          0 0 0 1px rgba(var(--el-border-color-rgb), 0.2) inset,
          2px 2px 5px rgba(0, 0, 0, 0.03),
          -2px -2px 5px rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        border-radius: 8px;
        padding-left: 12px;
        
        &:hover {
          box-shadow: 
            0 0 0 1px rgba(var(--el-color-primary-rgb), 0.2) inset,
            2px 2px 5px rgba(0, 0, 0, 0.03),
            -2px -2px 5px rgba(255, 255, 255, 0.1);
        }
        
        &.is-focus {
          box-shadow: 
            0 0 0 1px var(--el-color-primary) inset,
            2px 2px 6px rgba(var(--el-color-primary-rgb), 0.1),
            -2px -2px 6px rgba(255, 255, 255, 0.2);
        }
      }
      
      :deep(.el-input__inner) {
        height: 36px;
        font-size: 14px;
      }
      
      :deep(.el-input__icon) {
        color: var(--el-text-color-secondary);
      }
    }
    
    .pools-actions {
      flex-shrink: 0;
    }
  }

  /* 调整提示条，使整体更协调 */
  .manage-pools-hint {
    background: rgba(var(--el-color-info-light-rgb), 0.1);
    backdrop-filter: blur(5px);
    border-left: 4px solid var(--el-color-primary-light-3);
    padding: 12px 16px;
    margin-bottom: 20px;
    border-radius: 8px;
    color: var(--el-text-color-primary);
    font-size: 14px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 
      2px 2px 10px rgba(0, 0, 0, 0.03),
      -2px -2px 10px rgba(255, 255, 255, 0.05);
    
    &:before {
      content: "💡";
      font-size: 18px;
    }
  }

  /* 添加滚动时的按钮状态 */
  .scroll-top-btn {
    &.is-scrolling {
      color: var(--el-color-primary);
      background: rgba(var(--el-color-primary-rgb), 0.05);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
      box-shadow: 
        1px 1px 3px rgba(0, 0, 0, 0.05),
        -1px -1px 3px rgba(255, 255, 255, 0.1),
        inset 1px 1px 3px rgba(0, 0, 0, 0.02),
        inset -1px -1px 3px rgba(255, 255, 255, 0.05);
      
      .el-icon {
        animation: float-icon 0.6s ease-out;
      }
    }
  }

  /* 滚动时图标浮动动画 */
  @keyframes float-icon {
    0% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-6px);
    }
    100% {
      transform: translateY(0);
    }
  }

  /* 为卡池项添加缓动过渡 */
  .pool-item {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  /* 滚动条动画 */
  .pools-scrollbar {
    :deep(.el-scrollbar__bar) {
      opacity: 0;
      transition: opacity 0.3s ease, background-color 0.3s ease;
    }
    
    &:hover :deep(.el-scrollbar__bar),
    &:focus :deep(.el-scrollbar__bar) {
      opacity: 0.6;
    }
    
    &:hover :deep(.el-scrollbar__thumb),
    &:focus :deep(.el-scrollbar__thumb) {
      background-color: var(--el-color-primary-light-5);
    }
  }

  /* 重命名对话框样式 */
  .rename-pool-dialog {
    :deep(.el-dialog) {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    :deep(.el-dialog__header) {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
    }
    
    :deep(.el-dialog__body) {
      padding: 24px;
    }
    
    :deep(.el-dialog__footer) {
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
    }
    
    :deep(.el-input__wrapper) {
      box-shadow: 
        0 0 0 1px rgba(var(--el-border-color-rgb), 0.2) inset,
        2px 2px 5px rgba(0, 0, 0, 0.03),
        -2px -2px 5px rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
      border-radius: 8px;
      
      &:hover {
        box-shadow: 
          0 0 0 1px rgba(var(--el-color-primary-rgb), 0.2) inset,
          2px 2px 5px rgba(0, 0, 0, 0.03),
          -2px -2px 5px rgba(255, 255, 255, 0.1);
      }
      
      &.is-focus {
        box-shadow: 
          0 0 0 1px var(--el-color-primary) inset,
          2px 2px 6px rgba(var(--el-color-primary-rgb), 0.1),
          -2px -2px 6px rgba(255, 255, 255, 0.2);
      }
    }
  }

  /* 修复卡片重叠时的频闪问题 */
  .scene-card {
    &:hover {
      /* 移除可能导致频闪的transform或z-index变化 */
      transform: none !important;
      /* 仅添加阴影效果，不改变卡片位置 */
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
      
      /* 保留高亮效果但不影响层叠顺序 */
      border-color: rgba(var(--el-color-primary-rgb), 0.3) !important;
      
      /* 使用filter增强亮度而不是改变z-index */
      filter: brightness(1.02);
    }
    
    /* 确保激活状态不会改变z-index，仅改变视觉样式 */
    &.active {
      border-color: var(--el-color-primary) !important;
      box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2) !important;
      filter: brightness(1.05);
    }
    
    /* 为拖动状态单独设置样式，避免与hover冲突 */
    &.dragging {
      /* 拖动时才提高z-index */
      z-index: 1000 !important;
      transform: rotate(2deg) scale(1.02) !important;
      opacity: 0.9;
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
      pointer-events: none;
    }
  }

  /* 增加过渡延迟，避免鼠标快速移动时的频闪 */
  .scene-card-wrapper {
    transition: z-index 0s 0.05s !important;
  }

  /* 确保卡片内容不影响z-index层叠逻辑 */
  .scene-card-content {
    pointer-events: none; /* 防止内容元素接收鼠标事件 */
  }

  /* 修复卡片交互区域，确保能正确接收拖拽事件 */
  .scene-card-interaction-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10; /* 提高z-index，确保能接收到鼠标事件 */
    cursor: move;
    background: transparent; /* 透明背景 */
    pointer-events: auto; /* 确保可以接收鼠标事件 */

    /* 原生应用样式 - 禁用选择但允许内容交互 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 确保卡片内容仍然可见 */
  .scene-card-wrapper .el-card {
    pointer-events: none; /* 禁止卡片内容接收鼠标事件，让事件穿透到交互区域 */

    /* 卡片内容文本应该可以选择 */
    .el-card__body {
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      pointer-events: auto;
    }

    /* 卡片标题也应该可以选择 */
    .el-card__header {
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      pointer-events: auto;
    }
  }

  /* 但保留卡片内按钮的点击能力 */
  .scene-card-wrapper .el-card .el-button {
    pointer-events: auto; /* 恢复按钮的鼠标事件 */
    position: relative; /* 确保按钮在交互层之上 */
    z-index: 15; /* 高于交互区域 */
    user-select: none; /* 按钮文字不应该被选择 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 实际的无限画布容器 */
  .infinite-canvas {
    position: absolute;
    width: 10000px; /* 设置足够大的初始尺寸 */
    height: 10000px;
    transform-origin: 0 0;
    cursor: default;
    
    /* 添加平滑过渡，但仅对缩放应用，拖动保持即时 */
    transition: transform 0.1s ease-out;
    transition-property: transform;
    
    /* 禁用过渡当画布被拖动时 */
    &.dragging {
      transition: none;
    }
  }

  /* 画布控制区域 */
  .canvas-controls {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 100;
    padding: 8px;
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border: 1px solid var(--el-border-color-lighter);
    backdrop-filter: blur(8px);

    /* 原生应用样式 - 禁用选择和拖拽 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;

    .control-btn {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      background: transparent;

      &:hover {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      .el-icon {
        font-size: 18px;
      }
    }

    .zoom-display {
      padding: 8px 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 500;
      color: var(--el-text-color-regular);
      background: var(--el-fill-color-light);
      border-radius: 6px;
      min-width: 40px;
      text-align: center;
    }
  }

  /* 右键拖动提示 */
  .drag-hint {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(var(--el-bg-color-rgb), 0.8);
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(4px);

    /* 原生应用样式 - 禁用选择 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    
    &.visible {
      opacity: 1;
    }
    
    .key-hint {
      background: var(--el-bg-color);
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid var(--el-border-color-lighter);
      margin: 0 2px;
    }
  }

  /* 全屏模式下的样式 */
  .cards-grid:fullscreen {
    padding: 0;
    width: 100vw;
    height: 100vh;
    background: var(--el-bg-color);

    /* 继承原生应用样式 */
    @extend .native-app-style;

    /* 确保控制按钮在全屏模式下更加明显 */
    .canvas-controls {
      top: 50%;
      right: 30px;
      transform: translateY(-50%);
      padding: 12px;
      background: var(--el-bg-color);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);
      border: 1px solid var(--el-border-color);
      border-radius: 16px;

      .control-btn {
        width: 48px;
        height: 48px;
        border-radius: 10px;

        .el-icon {
          font-size: 22px;
        }
      }

      .zoom-display {
        font-size: 14px;
        padding: 10px 6px;
        min-width: 48px;
      }
    }

    /* 全屏模式下的拖动提示 */
    .drag-hint {
      top: 30px;
      font-size: 14px;
      padding: 8px 16px;
      background: rgba(var(--el-bg-color-rgb), 0.9);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }

  /* 支持webkit浏览器的全屏 */
  .cards-grid:-webkit-full-screen {
    padding: 0;
    width: 100vw;
    height: 100vh;
    background: var(--el-bg-color);

    /* 继承原生应用样式 */
    @extend .native-app-style;

    /* 确保控制按钮在全屏模式下更加明显 */
    .canvas-controls {
      top: 50%;
      right: 30px;
      transform: translateY(-50%);
      padding: 12px;
      background: var(--el-bg-color);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);
      border: 1px solid var(--el-border-color);
      border-radius: 16px;

      .control-btn {
        width: 48px;
        height: 48px;
        border-radius: 10px;

        .el-icon {
          font-size: 22px;
        }
      }

      .zoom-display {
        font-size: 14px;
        padding: 10px 6px;
        min-width: 48px;
      }
    }

    /* 全屏模式下的拖动提示 */
    .drag-hint {
      top: 30px;
      font-size: 14px;
      padding: 8px 16px;
      background: rgba(var(--el-bg-color-rgb), 0.9);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }

  /* 支持Mozilla浏览器的全屏 */
  .cards-grid:-moz-full-screen {
    padding: 0;
    width: 100vw;
    height: 100vh;
    background: var(--el-bg-color);

    /* 继承原生应用样式 */
    @extend .native-app-style;

    .canvas-controls {
      top: 50%;
      right: 30px;
      transform: translateY(-50%);
      padding: 12px;
      background: var(--el-bg-color);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);
      border: 1px solid var(--el-border-color);
      border-radius: 16px;

      .control-btn {
        width: 48px;
        height: 48px;
        border-radius: 10px;

        .el-icon {
          font-size: 22px;
        }
      }

      .zoom-display {
        font-size: 14px;
        padding: 10px 6px;
        min-width: 48px;
      }
    }

    .drag-hint {
      top: 30px;
      font-size: 14px;
      padding: 8px 16px;
      background: rgba(var(--el-bg-color-rgb), 0.9);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }

  /* 支持MS浏览器的全屏 */
  .cards-grid:-ms-fullscreen {
    padding: 0;
    width: 100vw;
    height: 100vh;
    background: var(--el-bg-color);

    /* 继承原生应用样式 */
    @extend .native-app-style;

    .canvas-controls {
      top: 50%;
      right: 30px;
      transform: translateY(-50%);
      padding: 12px;
      background: var(--el-bg-color);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);
      border: 1px solid var(--el-border-color);
      border-radius: 16px;

      .control-btn {
        width: 48px;
        height: 48px;
        border-radius: 10px;

        .el-icon {
          font-size: 22px;
        }
      }

      .zoom-display {
        font-size: 14px;
        padding: 10px 6px;
        min-width: 48px;
      }
    }

    .drag-hint {
      top: 30px;
      font-size: 14px;
      padding: 8px 16px;
      background: rgba(var(--el-bg-color-rgb), 0.9);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }