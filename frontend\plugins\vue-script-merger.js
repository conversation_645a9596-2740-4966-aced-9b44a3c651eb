import fs from 'fs'
import path from 'path'

/**
 * Vue Script Merger - 通过外部JS脚本映射到Vue组件的插件
 * 
 * @typedef {Object} ScriptMergerOptions
 * @property {string[]} scriptPaths - 脚本查找路径(相对或绝对路径)
 * @property {string[]} extensions - 脚本文件扩展名
 * @property {Object} aliases - 路径别名映射
 * @property {boolean} debug - 是否启用调试日志
 * @property {Function} resolveVueFiles - 自定义Vue文件解析函数
 * @property {Function} transform - 自定义内容转换函数
 * @property {string} rootDir - 项目根目录(默认为process.cwd())
 * @property {string} srcDir - 源代码目录(默认为src)
 * @property {string} injectComment - 注入脚本时的注释模板
 * @property {boolean} useSameDir - 是否优先使用同目录下的Vue文件
 * @property {string[]} vueDirs - 存放Vue文件的目录
 */

/**
 * Vue脚本合并器插件
 * @param {ScriptMergerOptions} options - 配置选项
 */
export default function vueScriptMerger(options = {}) {
  // 默认配置
  const defaultOptions = {
    scriptPaths: ['views', 'scripts', 'components'],
    extensions: ['.script.js', '.js'],
    aliases: {},
    debug: false,
    rootDir: process.cwd(),
    srcDir: 'src',
    injectComment: '// 自动导入的外部脚本: {filename}',
    useSameDir: true,
    vueDirs: ['views', 'components']
  }
  
  // 合并配置
  const config = { ...defaultOptions, ...options }
  
  // 初始化缓存
  const scriptToVueMap = new Map()
  const vueFileCache = new Map()
  const processedVueFiles = new Set()
  let allVueFiles = []
  let allScriptFiles = []
  
  /**
   * 查找目录中所有的文件
   * @param {string} dir - 搜索目录
   * @param {string[]} extensions - 匹配的扩展名
   * @param {string[]} results - 结果数组
   * @returns {string[]} 匹配的文件路径列表
   */
  function findFiles(dir, extensions, results = []) {
    if (!fs.existsSync(dir)) return results
    
    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name)
        
        if (entry.isDirectory()) {
          // 递归搜索子目录
          findFiles(fullPath, extensions, results)
        } else if (entry.isFile() && extensions.some(ext => entry.name.endsWith(ext))) {
          results.push(fullPath)
        }
      }
    } catch (err) {
      log(`读取目录 ${dir} 失败: ${err.message}`, 'error')
    }
    
    return results
  }
  
  /**
   * 查找所有Vue文件
   */
  function findAllVueFiles() {
    allVueFiles = []
    
    for (const vueDir of config.vueDirs) {
      let searchDir
      if (path.isAbsolute(vueDir)) {
        searchDir = vueDir
      } else if (vueDir.startsWith('./') || vueDir.startsWith('../')) {
        searchDir = path.join(config.rootDir, vueDir)
      } else {
        searchDir = path.join(config.rootDir, config.srcDir, vueDir)
      }
      
      // 处理别名路径
      searchDir = resolveAlias(searchDir)
      
      if (!fs.existsSync(searchDir)) {
        log(`Vue搜索目录不存在: ${searchDir}`, 'warn')
        continue
      }
      
      const files = findFiles(searchDir, ['.vue'])
      allVueFiles.push(...files)
    }
    
    log(`共找到 ${allVueFiles.length} 个Vue文件`, 'info')
    return allVueFiles
  }
  
  /**
   * 查找所有脚本文件
   */
  function findAllScriptFiles() {
    allScriptFiles = []
    
    for (const scriptPath of config.scriptPaths) {
      let searchDir
      if (path.isAbsolute(scriptPath)) {
        searchDir = scriptPath
      } else if (scriptPath.startsWith('./') || scriptPath.startsWith('../')) {
        searchDir = path.join(config.rootDir, scriptPath)
      } else {
        searchDir = path.join(config.rootDir, config.srcDir, scriptPath)
      }
      
      // 处理别名路径
      searchDir = resolveAlias(searchDir)
      
      if (!fs.existsSync(searchDir)) {
        log(`脚本搜索目录不存在: ${searchDir}`, 'warn')
        continue
      }
      
      const files = findFiles(searchDir, config.extensions)
      allScriptFiles.push(...files)
    }
    
    log(`共找到 ${allScriptFiles.length} 个脚本文件`, 'info')
    return allScriptFiles
  }
  
  /**
   * 建立脚本文件到Vue文件的映射关系
   */
  function buildScriptToVueMapping() {
    scriptToVueMap.clear()
    
    if (allVueFiles.length === 0) {
      findAllVueFiles()
    }
    
    if (allScriptFiles.length === 0) {
      findAllScriptFiles()
    }
    
    for (const scriptFile of allScriptFiles) {
      const scriptBaseName = path.basename(scriptFile, path.extname(scriptFile))
        .replace('.script', '')
        .replace('.vue', '')
      
      const matchingVueFiles = []
      
      // 1. 同目录优先匹配
      if (config.useSameDir) {
        const scriptDir = path.dirname(scriptFile)
        const sameDirVue = path.join(scriptDir, `${scriptBaseName}.vue`)
        
        if (fs.existsSync(sameDirVue)) {
          matchingVueFiles.push(sameDirVue)
          log(`为脚本 ${scriptFile} 找到同目录Vue文件: ${sameDirVue}`)
        }
      }
      
      // 2. 如果没有同目录匹配，查找所有可能匹配的Vue文件
      if (matchingVueFiles.length === 0) {
        for (const vueFile of allVueFiles) {
          const vueBaseName = path.basename(vueFile, '.vue')
          
          // 匹配规则:
          // 1. 文件名完全匹配
          // 2. 脚本在 componentName/index.js 而Vue是 componentName.vue
          // 3. Vue在 componentName/index.vue 而脚本是 componentName.js
          if (vueBaseName === scriptBaseName || 
              vueFile.includes(`/${scriptBaseName}/index.vue`) ||
              vueFile.includes(`\\${scriptBaseName}\\index.vue`) ||
              scriptFile.includes(`/${vueBaseName}/index`) ||
              scriptFile.includes(`\\${vueBaseName}\\index`)) {
            matchingVueFiles.push(vueFile)
            log(`为脚本 ${scriptFile} 找到匹配的Vue文件: ${vueFile}`)
          }
        }
      }
      
      if (matchingVueFiles.length > 0) {
        scriptToVueMap.set(scriptFile, matchingVueFiles)
      } else {
        log(`未找到与脚本 ${scriptFile} 匹配的Vue文件`, 'warn')
      }
    }
    
    log(`建立了 ${scriptToVueMap.size} 个脚本到Vue的映射关系`, 'info')
  }
  
  /**
   * 处理别名路径
   * @param {string} filepath - 文件路径
   * @returns {string} 解析后的路径
   */
  function resolveAlias(filepath) {
    for (const [alias, target] of Object.entries(config.aliases)) {
      if (filepath.startsWith(alias)) {
        return filepath.replace(alias, target)
      }
    }
    return filepath
  }
  
  /**
   * 内部日志函数
   * @param {string} message - 日志消息
   * @param {string} level - 日志级别
   */
  function log(message, level = 'debug') {
    if (!config.debug && level === 'debug') return
    
    const prefix = `[vue-script-merger] ${level.toUpperCase()}:`
    
    switch(level) {
      case 'error':
        console.error(prefix, message)
        break
      case 'warn':
        console.warn(prefix, message)
        break
      case 'info':
      case 'debug':
      default:
        if (config.debug) console.log(prefix, message)
    }
  }
  
  /**
   * 注入脚本内容到Vue文件
   * @param {string} vueContent - Vue文件内容 
   * @param {string} scriptContent - 脚本内容
   * @param {string} comment - 注入注释
   * @returns {string} 注入后的内容
   */
  function injectScript(vueContent, scriptContent, comment) {
    // 如果Vue文件有<script setup>标签，将内容注入到标签内
    if (/<script\s+setup[^>]*>/.test(vueContent)) {
      return vueContent.replace(
        /(<script\s+setup[^>]*>)([\s\S]*?)(<\/script>)/,
        (match, openTag, content, closeTag) => {
          return `${openTag}\n${comment}\n${scriptContent}\n${content}${closeTag}`
        }
      )
    } else {
      // 否则，创建新的<script setup>标签
      return vueContent.replace(
        /<\/template>/,
        `</template>\n\n<script setup>\n${comment}\n${scriptContent}\n</script>`
      )
    }
  }
  
  // Vite插件定义
  return {
    name: 'vue-script-merger',
    enforce: 'pre',
    
    configResolved(resolvedConfig) {
      // 更新配置，使用vite的别名配置
      if (resolvedConfig.resolve && resolvedConfig.resolve.alias) {
        config.viteAliases = resolvedConfig.resolve.alias
      }
      
      // 输出插件配置信息
      if (config.debug) {
        log('插件配置:', 'info')
        log(JSON.stringify(config, null, 2), 'info')
      }
      
      // 初始化时建立脚本与Vue文件的映射关系
      buildScriptToVueMapping()
    },
    
    /**
     * 转换Vue组件文件
     * @param {string} code - 源代码
     * @param {string} id - 文件ID(路径)
     */
    transform(code, id) {
      // 只处理Vue文件
      if (!id.endsWith('.vue')) return null
      
      // 避免重复处理
      if (processedVueFiles.has(id)) {
        return null
      }
      
      try {
        // 查找匹配当前Vue文件的所有脚本
        const matchingScripts = []
        
        for (const [scriptFile, vueFiles] of scriptToVueMap.entries()) {
          if (vueFiles.includes(id) || vueFiles.some(vueFile => path.normalize(vueFile) === path.normalize(id))) {
            matchingScripts.push(scriptFile)
          }
        }
        
        if (matchingScripts.length === 0) {
          return null
        }
        
        log(`为Vue文件 ${id} 找到 ${matchingScripts.length} 个匹配的脚本文件`, 'info')
        
        // 读取脚本内容并注入
        let modifiedCode = code
        for (const scriptFile of matchingScripts) {
          const scriptContent = fs.readFileSync(scriptFile, 'utf-8')
          
          // 生成注入注释
          const comment = config.injectComment.replace(
            '{filename}', 
            path.relative(path.dirname(id), scriptFile)
          )
          
          // 注入脚本内容
          if (typeof config.transform === 'function') {
            modifiedCode = config.transform(modifiedCode, scriptContent, comment, id, scriptFile)
          } else {
            modifiedCode = injectScript(modifiedCode, scriptContent, comment)
          }
        }
        
        // 标记为已处理
        processedVueFiles.add(id)
        
        return modifiedCode
      } catch (error) {
        log(`处理文件 ${id} 失败: ${error.stack || error.message}`, 'error')
        return null
      }
    },
    
    // 监听文件变化，更新映射关系
    handleHotUpdate({ file, server }) {
      if (file.endsWith('.js') && config.extensions.some(ext => file.endsWith(ext))) {
        log(`检测到脚本文件变化: ${file}`, 'info')
        // 清空缓存，重新建立映射关系
        scriptToVueMap.clear()
        allScriptFiles = []
        processedVueFiles.clear()
        buildScriptToVueMapping()
      } else if (file.endsWith('.vue')) {
        // 重置处理标记，允许再次处理此Vue文件
        processedVueFiles.delete(file)
      }
    }
  }
} 