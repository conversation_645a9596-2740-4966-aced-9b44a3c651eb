<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    required: true
  },
  width: {
    type: [Number, String],
    default: 400
  },
  height: {
    type: [Number, String],
    default: 400
  },
  initialPosition: {
    type: Object,
    default: () => ({ x: 100, y: 100 })
  },
  pinnable: {
    type: Boolean,
    default: false
  },
  onResize: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'close', 'move', 'resize'])

const windowRef = ref(null)
const isDragging = ref(false)
const isResizing = ref(false)
const isPinned = ref(true)
const position = ref({ ...props.initialPosition })
const size = ref({
  width: typeof props.width === 'number' ? props.width : parseInt(props.width),
  height: typeof props.height === 'number' ? props.height : parseInt(props.height)
})
const dragOffset = ref({ x: 0, y: 0 })
const resizeStartPos = ref({ x: 0, y: 0 })
const resizeStartSize = ref({ width: 0, height: 0 })

const handleDragStart = (event) => {
  if (event.target.closest('.window-content') || event.target.closest('.resize-handle')) return

  isDragging.value = true
  const rect = windowRef.value.getBoundingClientRect()
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }

  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('mouseup', handleDragEnd)
}

const handleDragMove = (event) => {
  if (!isDragging.value) return

  const newPosition = {
    x: event.clientX - dragOffset.value.x,
    y: event.clientY - dragOffset.value.y
  }
  
  position.value = newPosition
  emit('move', newPosition)
}

const handleDragEnd = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
}

const handleResizeStart = (event) => {
  isResizing.value = true
  resizeStartPos.value = { x: event.clientX, y: event.clientY }
  resizeStartSize.value = { ...size.value }

  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
}

const handleResizeMove = (event) => {
  if (!isResizing.value) return

  const deltaX = event.clientX - resizeStartPos.value.x
  const deltaY = event.clientY - resizeStartPos.value.y

  const newSize = {
    width: Math.max(300, resizeStartSize.value.width + deltaX),
    height: Math.max(400, resizeStartSize.value.height + deltaY)
  }

  size.value = newSize
  emit('resize', newSize)
}

const handleResizeEnd = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
}

const handleClickOutside = (event) => {
  if (windowRef.value &&
      !windowRef.value.contains(event.target) &&
      !isPinned.value) {
    emit('close')
  }
}

const togglePin = () => {
  isPinned.value = !isPinned.value
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})
</script>

<template>
  <div v-show="visible"
       ref="windowRef"
       class="floating-window"
       :class="{ 'is-pinned': isPinned }"
       :style="{
         left: `${position.x}px`,
         top: `${position.y}px`,
         width: `${size.width}px`,
         height: `${size.height}px`
       }"
       @mousedown="handleDragStart">
    <div class="window-header">
      <div class="window-title">
        <span>{{ title }}</span>
        <slot name="title-extra"></slot>
      </div>
      <div class="window-controls">
        <button v-if="pinnable" 
                class="control-btn pin-btn" 
                :class="{ active: isPinned }"
                @click.stop="togglePin">
          <svg class="pin-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
          </svg>
        </button>
        <button class="control-btn close-btn" @click.stop="emit('close')">
          <svg class="close-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
          </svg>
        </button>
      </div>
    </div>
    <div class="window-content">
      <slot></slot>
    </div>
    <div class="resize-handle" @mousedown.stop="handleResizeStart"></div>
  </div>
</template>

<style lang="scss" scoped>
.floating-window {
  position: fixed;
  background: var(--el-bg-color);
  backdrop-filter: blur(10px);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
  animation: windowAppear 0.2s ease-out;

  &:hover {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.16);
  }

  &.is-pinned {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
  }

  @keyframes windowAppear {
    from {
      opacity: 0;
      transform: scale(0.98);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}

.window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  cursor: move;
  user-select: none;
}

.window-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.window-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  color: var(--el-text-color-secondary);
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }

  &.pin-btn {
    &.active {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
  }

  svg {
    fill: currentColor;
  }
}

.window-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

.resize-handle {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 16px;
  height: 16px;
  cursor: se-resize;
  
  &::before {
    content: '';
    position: absolute;
    right: 4px;
    bottom: 4px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--el-border-color);
    border-bottom: 2px solid var(--el-border-color);
  }
}
</style>
