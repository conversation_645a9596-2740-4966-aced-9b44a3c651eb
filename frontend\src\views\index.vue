<template>
  <div>
    <!-- ... 其他内容 ... -->
    
    <!-- 时间验证信息弹窗 -->
    <el-dialog
      v-model="showTimeInfo"
      title="网络时间验证状态"
      width="400px"
    >
      <div v-if="userStore.isLoggedIn">
        <p><strong>激活状态:</strong> 有效</p>
        <p><strong>过期时间:</strong> {{ userStore.formattedExpiryDate }}</p>
        <p><strong>剩余时间:</strong> {{ userStore.formattedTimeRemaining }}</p>
        <p>
          <strong>时间来源:</strong> {{ userStore.timeSourceName || '未知来源' }}
          <span v-if="userStore.timeSource === 'local'" class="warning-text">
            (警告：使用本地时间，可能1不准确)
          </span>
        </p>
      </div>
      <div v-else>
        <p class="text-danger">{{ userStore.error || '未激活或激活已过期' }}</p>
      </div>
      <template #footer>
        <el-button @click="refreshNetworkTime" type="primary">刷新网络时间</el-button>
        <el-button @click="showTimeInfo = false">关闭</el-button>
      </template>
    </el-dialog>
    
    <!-- ... 其他内容 ... -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useUserStore } from '@/stores/user';

const userStore = useUserStore();
const showTimeInfo = ref(false);

onMounted(async () => {
  // 检查激活状态

  
  // 如果无法获取网络时间或激活无效，显示弹窗
  if (!result || userStore.error.includes('无法获取网络时间')) {
    showTimeInfo.value = true;
  }
});

// 添加刷新网络时间的方法
const refreshNetworkTime = async () => {
  try {
    const result = await window.pywebview.api.get_network_time_info();
    if (result.status === 'success') {
      // 重新检查激活状态
      userStore.startActivationTimer();
    }
  } catch (error) {
    console.error('刷新网络时间失败:', error);
  }
};
</script> 