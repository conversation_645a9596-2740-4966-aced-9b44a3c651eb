import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAIRolesStore = defineStore('aiRoles', () => {
  // 状态定义
  const roles = ref([])
  const loading = ref(false)
  const error = ref(null)
  const isLoaded = ref(false)

  // Getters
  const allRoles = computed(() => roles.value)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)

  // Actions
  async function loadRoles(forceRefresh = false) {
    // 如果已加载且不需要强制刷新，直接返回缓存
    if (isLoaded.value && !forceRefresh) return roles.value
    
    try {
      loading.value = true
      error.value = null
      
      console.log('加载AI角色列表...')
      const response = await window.pywebview.api.model_controller.get_ai_roles()
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 确保result.data是数组
        if (Array.isArray(result.data)) {
          roles.value = result.data
          console.log(`成功加载${roles.value.length}个AI角色`)
        } else {
          console.warn('API返回的角色数据不是数组:', result.data)
          roles.value = []
        }
        
        isLoaded.value = true
        return roles.value
      } else {
        const errorMsg = result?.message || '获取AI角色列表失败'
        console.error('加载角色失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (e) {
      error.value = e.message
      console.error('加载AI角色失败:', e)
      throw e
    } finally {
      loading.value = false
    }
  }

  async function addRole(roleData) {
    try {
      loading.value = true
      error.value = null
      
      // 调用后端API
      const response = await window.pywebview.api.model_controller.add_ai_role(roleData)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 获取新增的角色
        const newRole = result.data
        
        // 立即更新本地状态，而不是重新加载
        if (newRole && newRole.id) {
          // 确保角色不重复
          const existingIndex = roles.value.findIndex(r => r.id === newRole.id)
          if (existingIndex >= 0) {
            // 如果已经存在，更新
            roles.value[existingIndex] = { ...newRole }
          } else {
            // 如果不存在，添加到数组
            roles.value.push({ ...newRole })
          }
          
          // 设置加载标志
          isLoaded.value = true
        } else {
          // 如果没有获取到新角色数据，则重新加载所有角色
          await loadRoles()
        }
        
        return result
      } else {
        throw new Error(result?.message || '添加AI角色失败')
      }
    } catch (e) {
      error.value = e.message
      console.error('添加AI角色失败:', e)
      throw e
    } finally {
      loading.value = false
    }
  }

  async function updateRole(roleId, roleData) {
    try {
      loading.value = true
      error.value = null
      
      // 调用后端API
      const response = await window.pywebview.api.model_controller.update_ai_role(roleId, roleData)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 获取更新后的角色
        const updatedRole = result.data
        
        // 立即更新本地状态，而不是重新加载
        if (updatedRole && updatedRole.id) {
          const index = roles.value.findIndex(r => r.id === updatedRole.id)
          if (index !== -1) {
            // 更新现有角色
            roles.value[index] = { ...updatedRole }
            console.log('角色已更新:', updatedRole.id, updatedRole.name)
          } else {
            // 如果找不到角色，添加到列表
            roles.value.push({ ...updatedRole })
            console.log('找不到要更新的角色，已添加到列表:', updatedRole.id)
          }
        } else {
          // 如果没有获取到更新角色数据，则重新加载所有角色
          await loadRoles()
        }
        
        return result
      } else {
        throw new Error(result?.message || '更新AI角色失败')
      }
    } catch (e) {
      error.value = e.message
      console.error('更新AI角色失败:', e)
      throw e
    } finally {
      loading.value = false
    }
  }

  async function deleteRole(roleId) {
    try {
      loading.value = true
      error.value = null
      
      // 调用后端API
      const response = await window.pywebview.api.model_controller.delete_ai_role(roleId)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 立即从本地状态中移除，而不是重新加载
        const index = roles.value.findIndex(r => r.id === roleId)
        if (index !== -1) {
          console.log('从本地状态中移除角色:', roleId)
          roles.value.splice(index, 1)
        } else {
          // 如果在本地找不到要删除的角色，则重新加载所有角色
          console.log('找不到要删除的角色，重新加载列表')
          await loadRoles()
        }
        
        return { success: true, message: '角色已删除' }
      } else {
        throw new Error(result?.message || '删除AI角色失败')
      }
    } catch (e) {
      error.value = e.message
      console.error('删除AI角色失败:', e)
      throw e
    } finally {
      loading.value = false
    }
  }

  async function getRoleById(roleId) {
    // 确保角色已加载
    if (!isLoaded.value) {
      await loadRoles()
    }
    
    // 查找角色
    const role = roles.value.find(r => r.id === roleId)
    if (!role) {
      throw new Error(`未找到ID为 ${roleId} 的角色`)
    }
    
    return role
  }

  // 清除当前状态，用于重置
  function reset() {
    roles.value = []
    isLoaded.value = false
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    roles,
    loading,
    error,
    isLoaded,
    
    // Getters
    allRoles,
    isLoading,
    hasError,
    
    // Actions
    loadRoles,
    addRole,
    updateRole,
    deleteRole,
    getRoleById,
    reset
  }
}) 