<template>
  <div class="writing-manager">
    <!-- 书籍列表卡片 -->
    <el-card class="book-list">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" @click="showBookDialog">新建草稿</el-button>
            <el-button type="primary" @click="showBookDialog">新建作品</el-button>
            <el-button type="primary" @click="showBookDialog">导入草稿</el-button>
          </div>
        </div>
      </template>

      <div class="books-container">
        <div class="books-grid">
          <div v-for="book in bookStore.bookList" 
               :key="book.id" 
               class="book-card" 
          >
            <div class="book-content"
                 :class="getBookCardClass(book)"
                 :data-book-type="book.type"
                 :data-encrypted="book.encrypted">
              <div class="book-header">
                <div class="title-container">
                  <h3>{{ book.title }}</h3>
                </div>
                <el-button 
                  class="settings-btn" 
                  @click.stop="openBookSettings(book)"
                >
                  <el-icon><Setting /></el-icon>
                </el-button>
              </div>
              <div class="book-meta">
                <span class="word-count">{{ book.word_count || 0 }}字</span>
                <span class="update-time">{{ formatDate(book.updated_at) }}</span>
              </div>
              <p class="book-description">{{ book.description }}</p>
              <div class="book-stats">
                <div class="stat-item">
                  <el-icon><Document /></el-icon>
                  <span>{{ getChapterCount(book) }}章</span>
                </div>
                <div class="stat-item">
                  <el-icon><Timer /></el-icon>
                  <span>{{ getLastEditTime(book) }}</span>
                </div>
              </div>
            </div>
            <div class="book-actions">
              <div class="action-group primary">
                <el-button 
                  type="primary" 
                  class="action-btn"
                  @click="startWriting(book)"
                >
                  <el-icon><Edit /></el-icon>
                  写作
                </el-button>
              </div>
              <div class="action-group secondary">
                <el-button 
                  type="success" 
                  class="action-btn"
                  @click="openSettings(book)"
                >
                  设定
                </el-button>
                <el-button 
                  type="warning" 
                  class="action-btn icon-btn"
                  @click="openTimeline(book)"
                >
                  <el-icon><Timer /></el-icon>
                </el-button>
                <el-button 
                  type="info" 
                  class="action-btn icon-btn"
                  @click="exportBook(book)"
                >
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button 
                  type="danger" 
                  class="action-btn icon-btn"
                  @click="removeBook(book)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <div class="book-card new-book" @click="showBookDialog">
            <el-icon><Plus /></el-icon>
            <span>新建书籍</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 写作内容区域 -->
    <el-card v-if="currentBook" class="writing-area" :class="getWritingAreaClass(currentBook)">
      <template #header>
        <div class="card-header">
          <span>{{ currentBook.title }}</span>
          <div class="writing-actions">
            <el-button type="success" @click="saveContent">保存</el-button>
            <el-button type="info" @click="exportContent">导出</el-button>
          </div>
        </div>
      </template>

      <div class="editor-container">
        <el-input
          v-model="writingContent"
          type="textarea"
          :rows="20"
          placeholder="开始创作..."
          resize="none"
        />
      </div>
    </el-card>

    <!-- 书籍编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="newBook.type === 'draft' ? '📝 新建草稿' : '📚 新建作品'"
      width="700px"
      class="book-create-dialog"
      :close-on-click-modal="false"
      :destroy-on-close="false"
      append-to-body
    >
      <div class="book-create-content">
        <el-scrollbar class="create-scroll-container" max-height="500px">
          <div class="create-form-wrapper">
            <el-form ref="bookFormRef" :model="newBook" :rules="rules" label-width="0px" class="create-form">
              <!-- 基本信息 -->
              <div class="settings-section">
                <h3 class="section-title">📝 基本信息</h3>
                <div class="form-row">
                  <div class="form-item">
                    <label class="form-label">书名</label>
                    <el-input
                      v-model="newBook.title"
                      placeholder="请输入书名"
                      class="styled-input"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label class="form-label">描述</label>
                    <el-input
                      v-model="newBook.description"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入书籍描述"
                      class="styled-textarea"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label class="form-label">类型</label>
                    <el-select v-model="newBook.type" placeholder="请选择类型" class="styled-select">
                      <el-option label="草稿" value="draft" />
                      <el-option label="作品" value="work" />
                    </el-select>
                  </div>
                </div>
              </div>

              <!-- 书籍风格 -->
              <div class="settings-section">
                <h3 class="section-title">🎨 书籍风格</h3>
                <div class="style-selector">
                  <div
                    v-for="style in bookStyles"
                    :key="style.value"
                    class="style-card"
                    :class="{
                      'selected': newBook.book_style === style.value,
                      [`preview-${style.value}`]: true
                    }"
                    @click="newBook.book_style = style.value"
                  >
                    <div class="style-preview">
                      <div class="preview-header" :style="{ backgroundColor: style.color }"></div>
                      <div class="preview-content">
                        <div class="preview-title" :style="{ color: style.color }">{{ style.label }}</div>
                        <div class="preview-description">{{ style.description }}</div>
                      </div>
                    </div>
                    <div class="style-check" v-if="newBook.book_style === style.value">
                      <el-icon><Check /></el-icon>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 密码保护 -->
              <div class="settings-section">
                <h3 class="section-title">🔒 密码保护</h3>
                <div class="form-row">
                  <div class="form-item">
                    <label class="form-label">启用密码</label>
                    <el-switch v-model="enablePassword" />
                  </div>
                </div>

                <template v-if="enablePassword">
                  <div class="form-row">
                    <div class="form-item">
                      <label class="form-label">密码</label>
                      <el-input
                        v-model="newBook.password"
                        type="password"
                        placeholder="请输入密码"
                        show-password
                        class="styled-input"
                      />
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <label class="form-label">确认密码</label>
                      <el-input
                        v-model="newBook.confirmPassword"
                        type="password"
                        placeholder="请再次输入密码"
                        show-password
                        class="styled-input"
                      />
                    </div>
                  </div>
                  <div class="warning-text">
                    <el-icon><Warning /></el-icon>
                    <span>请牢记您的密码，密码丢失将无法恢复书籍内容！</span>
                  </div>
                </template>
              </div>
            </el-form>
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createBook">创建</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 书籍设置对话框 -->
    <el-dialog
      v-model="settingsVisible"
      title="📚 书籍设置"
      width="700px"
      class="book-settings-dialog"
      :close-on-click-modal="false"
      :destroy-on-close="false"
      append-to-body
    >
      <div v-if="editingBook" class="book-settings-content">
        <el-scrollbar class="settings-scroll-container" max-height="500px">
          <div class="settings-form-wrapper">
            <el-form :model="editingBook" label-width="0px" class="settings-form">
              <!-- 基本信息 -->
              <div class="settings-section">
                <h3 class="section-title">📝 基本信息</h3>
                <div class="form-row">
                  <div class="form-item">
                    <label class="form-label">书名</label>
                    <el-input
                      v-model="editingBook.title"
                      placeholder="请输入书名"
                      class="styled-input"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label class="form-label">描述</label>
                    <el-input
                      v-model="editingBook.description"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入书籍描述"
                      class="styled-textarea"
                    />
                  </div>
                </div>
              </div>

              <!-- 书籍风格 -->
              <div class="settings-section">
                <h3 class="section-title">🎨 书籍风格</h3>
                <div class="style-selector">
                  <div
                    v-for="style in bookStyles"
                    :key="style.value"
                    class="style-card"
                    :class="{
                      'selected': editingBook.book_style === style.value,
                      [`preview-${style.value}`]: true
                    }"
                    @click="selectBookStyle(style.value)"
                  >
                    <div class="style-preview">
                      <div class="preview-header" :style="{ backgroundColor: style.color }"></div>
                      <div class="preview-content">
                        <div class="preview-title" :style="{ color: style.color }">{{ style.label }}</div>
                        <div class="preview-desc">{{ style.description }}</div>
                      </div>
                    </div>
                    <div class="style-check" v-if="editingBook.book_style === style.value">
                      <el-icon><Check /></el-icon>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 密码保护 -->
              <div class="settings-section">
                <h3 class="section-title">🔒 密码保护</h3>
                <div class="password-section">
                  <div v-if="!editingBook.encrypted" class="password-setup">
                    <div class="password-info">
                      <p class="info-text">设置密码后，所有章节内容将被加密保存，访问时需要输入密码。</p>
                    </div>
                    <el-button
                      type="primary"
                      @click="showPasswordDialog"
                      class="password-btn"
                    >
                      <el-icon><Lock /></el-icon>
                      设置密码保护
                    </el-button>
                  </div>
                  <div v-else class="password-status">
                    <div class="encrypted-info">
                      <div class="encrypted-badge">
                        <el-icon class="lock-icon"><Lock /></el-icon>
                        <span>此书籍已加密</span>
                      </div>
                      <p class="info-text">书籍内容已加密保护，访问时需要输入密码。</p>
                    </div>
                    <el-button
                      type="danger"
                      @click="showRemovePasswordDialog"
                      class="password-btn danger"
                    >
                      <el-icon><Delete /></el-icon>
                      移除密码保护
                    </el-button>
                  </div>
                </div>
              </div>
            </el-form>
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="settingsVisible = false">取消</el-button>
          <el-button type="primary" @click="updateBookSettings">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加设置密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="设置密码保护"
      width="400px"
      append-to-body
      destroy-on-close
    >
      <el-form :model="passwordForm" ref="passwordFormRef" label-width="100px">
        <el-form-item label="密码" prop="password" :rules="[
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }
        ]">
          <el-input
            v-model="passwordForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" :rules="[
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ]">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <div class="warning-text">
        <el-icon><Warning /></el-icon>
        <span>请牢记您的密码，密码丢失将无法恢复书籍内容！</span>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="setBookPassword">确认设置</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 添加移除密码对话框 -->
    <el-dialog
      v-model="removePasswordDialogVisible"
      title="移除密码保护"
      width="400px"
      append-to-body
      destroy-on-close
    >
      <el-form :model="removePasswordForm" ref="removePasswordFormRef" label-width="100px">
        <el-form-item label="当前密码" prop="password" :rules="[
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ]">
          <el-input
            v-model="removePasswordForm.password"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <div class="warning-text">
        <el-icon><Warning /></el-icon>
        <span>移除密码保护后，书籍内容将以明文形式保存。</span>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="removePasswordDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="removeBookPassword">确认移除</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导出面板对话框 -->
    <el-dialog
      v-model="exportVisible"
      title="导出书籍内容"
      width="700px"
      class="export-dialog"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <div v-if="currentExportBook" class="export-panel">
        <div class="export-header">
          <h3>《{{ currentExportBook.title }}》</h3>
          <p>{{ currentExportBook.description }}</p>
          
          <div class="export-options">
            <el-radio-group v-model="exportFormat">
              <el-radio value="txt">纯文本 (TXT)</el-radio>
              <el-radio value="md" disabled>Markdown (MD)</el-radio>
            </el-radio-group>
            
            <div class="selection-controls">
              <el-button size="small" type="primary" @click="selectAllChapters">全选</el-button>
              <el-button size="small" @click="deselectAllChapters">取消全选</el-button>
            </div>
          </div>
        </div>
        
        <div class="chapters-container">
          <el-scrollbar height="300px">
            <div v-for="(volume, volumeIndex) in exportVolumes" :key="volumeIndex" class="volume-item">
              <div class="volume-header">
                <el-checkbox 
                  v-model="volume.selected" 
                  @change="(val) => handleVolumeSelect(volumeIndex, val)"
                >
                  <span class="volume-title">{{ volume.title }}</span>
                </el-checkbox>
              </div>
              
              <div class="chapter-list">
                <div v-for="(chapter, chapterIndex) in volume.chapters" :key="chapterIndex" class="chapter-item">
                  <el-checkbox 
                    v-model="chapter.selected" 
                    @change="() => handleChapterSelect(volumeIndex)"
                  >
                    <span class="chapter-title">{{ chapter.title }}</span>
                    <span class="chapter-word-count">{{ chapter.word_count || 0 }}字</span>
                  </el-checkbox>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
        
        <div class="export-summary">
          <span>已选择 {{ selectedChapterCount }} 个章节，共 {{ selectedWordCount }} 字</span>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="exportVisible = false">取消</el-button>
          <el-button type="primary" @click="executeExport" :disabled="selectedChapterCount === 0">
            导出
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Setting, Timer, Download, Delete, Edit, Document, Lock, Warning, Check } from '@element-plus/icons-vue'
import { useBookStore } from '@/stores/book'

const router = useRouter()
const bookStore = useBookStore()

// 数据
const dialogVisible = ref(false)
const settingsVisible = ref(false)
const exportVisible = ref(false)
const editingBook = ref({})
const currentBook = ref(null)
const writingContent = ref('')
const currentExportBook = ref(null)
const exportVolumes = ref([])
const exportFormat = ref('txt')

// 添加密码相关数据
const passwordDialogVisible = ref(false)
const removePasswordDialogVisible = ref(false)
const passwordForm = ref({
  password: '',
  confirmPassword: ''
})
const removePasswordForm = ref({
  password: ''
})

// 添加启用密码的标志
const enablePassword = ref(false)

// 书籍风格选项
const bookStyles = ref([
  {
    value: 'classic-blue',
    label: '经典蓝调',
    description: '专业稳重，适合商务类作品',
    color: '#4a90e2'
  },
  {
    value: 'warm-orange',
    label: '温暖橙光',
    description: '活力温馨，适合生活类作品',
    color: '#ff8c42'
  },
  {
    value: 'fresh-green',
    label: '清新绿意',
    description: '自然清新，适合治愈类作品',
    color: '#2ecc71'
  },
  {
    value: 'elegant-purple',
    label: '优雅紫韵',
    description: '神秘优雅，适合奇幻类作品',
    color: '#9b59b6'
  },
  {
    value: 'mysterious-dark',
    label: '神秘深邃',
    description: '沉稳内敛，适合悬疑类作品',
    color: '#34495e'
  },
  {
    value: 'minimal-gray',
    label: '简约灰调',
    description: '简洁现代，适合科技类作品',
    color: '#95a5a6'
  },
  {
    value: 'sakura-pink',
    label: '樱花粉韵',
    description: '浪漫温柔，适合言情类作品',
    color: '#ff6b9d'
  },
  {
    value: 'deep-ocean',
    label: '深海蓝调',
    description: '深邃宁静，适合哲学类作品',
    color: '#1e3a8a'
  },
  {
    value: 'emerald-oasis',
    label: '翡翠绿洲',
    description: '生机盎然，适合冒险类作品',
    color: '#059669'
  },
  {
    value: 'sunset-glow',
    label: '夕阳红霞',
    description: '热情奔放，适合青春类作品',
    color: '#dc2626'
  },
  {
    value: 'lavender-dream',
    label: '薰衣草紫',
    description: '梦幻唯美，适合童话类作品',
    color: '#7c3aed'
  },
  {
    value: 'amber-gold',
    label: '琥珀金辉',
    description: '典雅华贵，适合历史类作品',
    color: '#d97706'
  }
])

// 新建书籍表单
const newBook = ref({
  title: '',
  description: '',
  type: 'draft',
  book_style: 'classic-blue',  // 添加默认书籍风格
  password: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入书名', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'blur' }]
}

// 验证新建书籍密码一致性
const validateNewBookPassword = (rule, value, callback) => {
  if (enablePassword.value && value !== newBook.value.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 组件挂载时加载数据
onMounted(() => {
  bookStore.loadBooks()
})

// 创建新书籍
const createBook = async () => {
  // 验证密码
  if (enablePassword.value) {
    if (newBook.value.password !== newBook.value.confirmPassword) {
      ElMessage.error('两次输入的密码不一致')
      return
    }
    
    if (newBook.value.password.length < 6) {
      ElMessage.error('密码长度至少6个字符')
      return
    }
  }

  try {
    // 先检查是否有同名书籍
    const existingBooks = bookStore.bookList.filter(
      book => book.title === newBook.value.title
    )
    
    if (existingBooks.length > 0) {
      // 如果有同名书籍，检查是否有加密书籍
      const encryptedBooks = existingBooks.filter(book => book.encrypted)
      
      if (encryptedBooks.length > 0) {
        // 如果有同名加密书籍，提示用户并阻止创建
        await ElMessageBox.confirm(
          '已存在同名加密书籍，创建新书籍可能导致无法解密原有内容。请使用其他名称。',
          '警告',
          {
            confirmButtonText: '更改名称',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        return
      } else {
        // 如果只有非加密同名书籍，询问用户是否继续
        await ElMessageBox.confirm(
          '已存在同名书籍，继续创建将可能覆盖现有书籍。是否继续？',
          '提示',
          {
            confirmButtonText: '继续',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
      }
    }
    
    // 创建书籍
    const success = await bookStore.createBook(newBook.value)
    if (success) {
      ElMessage.success('创建成功')
      dialogVisible.value = false
      // 重置表单
      newBook.value = {
        title: '',
        description: '',
        type: 'draft',
        book_style: 'classic-blue',
        password: '',
        confirmPassword: ''
      }
      enablePassword.value = false
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建失败:', error)
      ElMessage.error('创建失败：' + (error.message || '未知错误'))
    }
  }
}

// 删除书籍
const removeBook = async (book) => {
  try {
    await ElMessageBox.confirm(
      '注意：为了安全只会删除到软件垃圾桶目录，请手动找到目录确认删除！！',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    await bookStore.removeBook(book)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 打开设定页面
const openSettings = (book) => {
  router.push({
    name: 'bookSettings',
    params: { id: book.id },
    query: { title: book.title }
  })
}

// 开始写作
const startWriting = async (book) => {
  try {
    router.push(`/book/editor/${book.id}`)
  } catch (error) {
    console.error('导航到编辑器页面失败:', error)
    ElMessage.error('导航到编辑器页面失败：' + error.message)
  }
}

// 保存内容
const saveContent = async () => {
  if (!currentBook.value) return

  try {
    const response = await bookStore.updateBook(currentBook.value.id, {
      ...currentBook.value,
      content: writingContent.value,
      word_count: writingContent.value.length
    })
    if (response) {
      ElMessage.success('保存成功')
      bookStore.loadBooks() // 刷新列表以更新字数统计
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败：' + error.message)
  }
}

// 显示新建书籍对话框
const showBookDialog = () => {
  dialogVisible.value = true
  // 重置表单
  newBook.value = {
    title: '',
    description: '',
    type: 'draft',
    book_style: 'classic-blue',
    password: '',
    confirmPassword: ''
  }
}

// 打开书籍设置
const openBookSettings = (book) => {
  editingBook.value = {
    ...book,
    // 确保有默认的书籍风格
    book_style: book.book_style || (book.theme_color ? getStyleFromLegacyColor(book.theme_color) : 'classic-blue')
  }
  settingsVisible.value = true
}

// 选择书籍风格
const selectBookStyle = (styleValue) => {
  editingBook.value.book_style = styleValue

  // 如果当前正在编辑的书籍就是当前打开的书籍，立即应用风格
  if (currentBook.value && currentBook.value.id === editingBook.value.id) {
    currentBook.value.book_style = styleValue
  }

  // 立即更新书籍列表中对应书籍的风格，以便实时预览
  const bookInList = bookStore.bookList.find(book => book.id === editingBook.value.id)
  if (bookInList) {
    bookInList.book_style = styleValue
  }
}

// 更新书籍设置
const updateBookSettings = async () => {
  try {
    console.log('开始更新书籍设置:', editingBook.value)

    if (!editingBook.value || !editingBook.value.id) {
      throw new Error('书籍信息无效')
    }

    // 创建一个干净的书籍数据副本
    const bookData = {
      id: editingBook.value.id,
      title: editingBook.value.title,
      description: editingBook.value.description,
      book_style: editingBook.value.book_style,
      type: editingBook.value.type,
      encrypted: editingBook.value.encrypted,
      // 保留其他必要字段
      created_at: editingBook.value.created_at,
      updated_at: editingBook.value.updated_at,
      word_count: editingBook.value.word_count
    }

    // 如果书籍已加密，保留加密相关字段
    if (editingBook.value.encrypted) {
      bookData.salt = editingBook.value.salt
      bookData.iv = editingBook.value.iv
      bookData.checksum = editingBook.value.checksum
    }

    console.log('准备发送的书籍数据:', bookData)

    const response = await bookStore.updateBook(editingBook.value.id, bookData)
    if (response) {
      ElMessage.success('设置更新成功')
      settingsVisible.value = false

      // 等待一小段时间确保后端缓存已清理，然后刷新书籍列表
      setTimeout(async () => {
        await bookStore.loadBooks()

        // 如果当前正在编辑的书籍就是当前打开的书籍，更新当前书籍信息
        if (currentBook.value && currentBook.value.id === editingBook.value.id) {
          const updatedBook = bookStore.bookList.find(b => b.id === editingBook.value.id)
          if (updatedBook) {
            currentBook.value = { ...updatedBook }
          }
        }
      }, 100)
    } else {
      throw new Error('更新失败，未收到成功响应')
    }
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('更新失败：' + (error.message || '未知错误'))
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 获取章节数
const getChapterCount = (book) => {
  // 这里可以根据实际数据结构获取章节数
  return book.chapter_count || 0;
};

// 获取最后编辑时间
const getLastEditTime = (book) => {
  if (!book.updated_at) return '未编辑';
  const now = new Date();
  const updateTime = new Date(book.updated_at);
  const diff = now - updateTime;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) return '今天';
  if (days === 1) return '昨天';
  if (days < 30) return `${days}天前`;
  return formatDate(book.updated_at);
};

// 生成书籍卡片样式类
const getBookCardClass = (book) => {
  const baseClass = 'book-style'

  // 优先使用新的book_style，如果没有则尝试从旧的theme_color转换
  let styleClass = 'classic-blue'
  if (book.book_style) {
    styleClass = book.book_style
  } else if (book.theme_color) {
    styleClass = getStyleFromLegacyColor(book.theme_color)
  }

  const typeClass = book.type === 'draft' ? 'draft-type' : 'work-type'
  const encryptedClass = book.encrypted ? 'encrypted-book' : ''

  return [baseClass, `style-${styleClass}`, typeClass, encryptedClass].filter(Boolean).join(' ')
}

// 兼容旧版本主题颜色的转换
const getStyleFromLegacyColor = (themeColor) => {
  if (!themeColor) return 'classic-blue'

  // 根据颜色值映射到预设风格
  const colorMap = {
    '#409EFF': 'classic-blue',
    '#67C23A': 'fresh-green',
    '#E6A23C': 'warm-orange',
    '#F56C6C': 'elegant-purple',
    '#909399': 'minimal-gray',
    '#000000': 'mysterious-dark'
  }

  return colorMap[themeColor] || 'classic-blue'
}

// 获取写作区域样式类
const getWritingAreaClass = (book) => {
  if (!book) return ''

  let styleClass = 'classic-blue'
  if (book.book_style) {
    styleClass = book.book_style
  } else if (book.theme_color) {
    styleClass = getStyleFromLegacyColor(book.theme_color)
  }

  return `writing-style-${styleClass}`
}

// 打开时间线
const openTimeline = (book) => {
  if (!book || !book.id) {
    ElMessage.error('无效的书籍信息')
    return
  }
  
  router.push({
    name: 'bookTimeline',
    params: { id: book.id },
    query: { title: book.title }
  })
}

// 导出书籍
const exportBook = async (book) => {
  try {
    // 保存当前导出书籍
    currentExportBook.value = { ...book }
    exportVisible.value = true
    
    // 加载书籍卷和章节
    ElMessage.info('正在加载书籍结构...')
    console.log('准备获取书籍结构，书籍ID:', book.id)
    
    // 确保book.id有效
    if (!book.id) {
      throw new Error('无效的书籍ID')
    }
    
    // 确保API可用
    const api = window.pywebview?.api
    if (!api) {
      console.error('API未正确初始化')
      throw new Error('系统API未就绪')
    }
    
    // 获取书籍结构
    console.log('调用get_volumes方法...')
    const response = await api.book_controller.get_volumes(book.id)
    console.log('获取书籍结构响应:', response)
    
    // 检查响应
    if (!response) {
      throw new Error('获取书籍结构时服务器无响应')
    }
    
    // 处理响应可能是字符串的情况
    let data = response
    if (typeof response === 'string') {
      try {
        data = JSON.parse(response)
      } catch (e) {
        console.error('解析响应失败:', e)
        throw new Error('解析书籍数据失败')
      }
    }
    
    // 检查响应状态
    if (data.status !== 'success') {
      throw new Error(data.message || '加载书籍结构失败')
    }
    
    // 处理卷和章节结构，添加选中状态
    const volumes = data.data || []
    if (volumes.length === 0) {
      throw new Error('此书籍没有任何卷或章节')
    }
    
    console.log('成功获取卷数量:', volumes.length)
    
    exportVolumes.value = volumes.map(volume => {
      return {
        ...volume,
        selected: true,
        chapters: (volume.chapters || []).map(chapter => {
          return {
            ...chapter,
            selected: true
          }
        })
      }
    })
    
    // 如果没有任何章节，提醒用户
    const totalChapters = exportVolumes.value.reduce(
      (total, volume) => total + volume.chapters.length, 0
    )
    
    if (totalChapters === 0) {
      ElMessage.warning('此书籍没有任何章节内容')
    } else {
      ElMessage.success(`成功加载 ${exportVolumes.value.length} 卷 ${totalChapters} 章内容`)
    }
  } catch (error) {
    console.error('准备导出失败:', error)
    ElMessage.error('准备导出失败：' + (error.message || '未知错误'))
    exportVisible.value = false
  }
}

// 选择所有章节
const selectAllChapters = () => {
  exportVolumes.value.forEach(volume => {
    volume.selected = true
    volume.chapters.forEach(chapter => {
      chapter.selected = true
    })
  })
}

// 取消选择所有章节
const deselectAllChapters = () => {
  exportVolumes.value.forEach(volume => {
    volume.selected = false
    volume.chapters.forEach(chapter => {
      chapter.selected = false
    })
  })
}

// 处理卷选择状态变化
const handleVolumeSelect = (volumeIndex, selected) => {
  const volume = exportVolumes.value[volumeIndex]
  if (volume) {
    volume.chapters.forEach(chapter => {
      chapter.selected = selected
    })
  }
}

// 处理章节选择状态变化
const handleChapterSelect = (volumeIndex) => {
  const volume = exportVolumes.value[volumeIndex]
  if (volume) {
    // 如果所有章节都被选中，则卷也被选中
    volume.selected = volume.chapters.every(chapter => chapter.selected)
  }
}

// 计算已选择的章节数量
const selectedChapterCount = computed(() => {
  return exportVolumes.value.reduce((count, volume) => {
    return count + volume.chapters.filter(chapter => chapter.selected).length
  }, 0)
})

// 计算已选择的字数
const selectedWordCount = computed(() => {
  return exportVolumes.value.reduce((count, volume) => {
    return count + volume.chapters
      .filter(chapter => chapter.selected)
      .reduce((sum, chapter) => sum + (chapter.word_count || 0), 0)
  }, 0)
})

// 执行导出
const executeExport = async () => {
  try {
    // 如果没有选择任何章节，提示用户
    if (selectedChapterCount.value === 0) {
      ElMessage.warning('请选择至少一个章节')
      return
    }
    
    ElMessage.info('正在准备导出...')
    console.log('准备导出数据，书籍ID:', currentExportBook.value.id)
    
    // 准备要导出的数据
    const exportData = {
      book_id: currentExportBook.value.id,
      format: exportFormat.value,
      volumes: exportVolumes.value.map(volume => ({
        id: volume.id,
        title: volume.title,
        chapters: volume.chapters
          .filter(chapter => chapter.selected)
          .map(chapter => ({
            id: chapter.id,
            title: chapter.title
          }))
      })).filter(volume => volume.chapters.length > 0) // 只保留有选中章节的卷
    }
    
    console.log('导出数据结构:', JSON.stringify(exportData, null, 2))
    
    // 确保API可用
    const api = window.pywebview?.api
    if (!api) {
      console.error('API未正确初始化')
      throw new Error('系统API未就绪')
    }
    
    // 调用后端API进行导出
    console.log('调用export_book方法...')
    const response = await api.book_controller.export_book(exportData)
    console.log('导出响应:', response)
    
    // 检查响应
    if (!response) {
      throw new Error('导出时服务器无响应')
    }
    
    // 处理响应可能是字符串的情况
    let data = response
    if (typeof response === 'string') {
      try {
        data = JSON.parse(response)
      } catch (e) {
        console.error('解析响应失败:', e)
        throw new Error('解析导出结果失败')
      }
    }
    
    // 检查响应状态
    if (data.status !== 'success') {
      throw new Error(data.message || '导出失败')
    }
    
    ElMessage.success(data.message || '导出成功')
    exportVisible.value = false
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败：' + (error.message || '未知错误'))
  }
}

// 导出当前内容
const exportContent = async () => {
  if (!currentBook.value) return;
  
  try {
    ElMessage.info('正在导出内容...');
    // 假设后端提供了导出内容API
    // await pywebview.api.book_controller.export_content(currentBook.value.id, writingContent.value);
    ElMessage.success('内容导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败：' + (error.message || '未知错误'));
  }
};

// 验证密码一致性
const validatePassword = (rule, value, callback) => {
  if (value !== passwordForm.value.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 显示设置密码对话框
const showPasswordDialog = () => {
  passwordDialogVisible.value = true
  passwordForm.value = {
    password: '',
    confirmPassword: ''
  }
}

// 显示移除密码对话框
const showRemovePasswordDialog = () => {
  removePasswordDialogVisible.value = true
  removePasswordForm.value = {
    password: ''
  }
}

// 设置书籍密码
const setBookPassword = async () => {
  // 验证密码
  if (passwordForm.value.password !== passwordForm.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }
  
  if (passwordForm.value.password.length < 6) {
    ElMessage.error('密码长度至少6个字符')
    return
  }
  
  // 确认操作
  try {
    await ElMessageBox.confirm(
      '设置密码后，书籍内容将被加密保存。请务必记住密码，密码丢失将无法恢复内容！',
      '重要提示',
      {
        confirmButtonText: '确认设置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.info('正在加密书籍内容，请稍候...')
    
    // 设置密码
    const result = await bookStore.setBookPassword(
      editingBook.value.id,
      passwordForm.value.password
    )
    
    if (result) {
      ElMessage.success('密码设置成功，书籍内容已加密')
      passwordDialogVisible.value = false

      // 等待一小段时间确保后端缓存已清理，然后刷新书籍列表
      setTimeout(async () => {
        await bookStore.loadBooks()
        const updatedBook = bookStore.bookList.find(b => b.id === editingBook.value.id)
        if (updatedBook) {
          editingBook.value = { ...updatedBook }
        }
      }, 100)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置密码失败:', error)
      ElMessage.error('设置密码失败：' + error.message)
    }
  }
}

// 移除书籍密码
const removeBookPassword = async () => {
  if (!removePasswordForm.value.password) {
    ElMessage.error('请输入当前密码')
    return
  }
  
  // 确认操作
  try {
    await ElMessageBox.confirm(
      '移除密码保护后，书籍内容将以明文形式保存，确定要继续吗？',
      '确认移除',
      {
        confirmButtonText: '确认移除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.info('正在解密书籍内容，请稍候...')
    
    // 移除密码
    const result = await bookStore.removeBookPassword(
      editingBook.value.id,
      removePasswordForm.value.password
    )
    
    if (result) {
      ElMessage.success('密码已移除，书籍内容已解密')
      removePasswordDialogVisible.value = false

      // 等待一小段时间确保后端缓存已清理，然后刷新书籍列表
      setTimeout(async () => {
        await bookStore.loadBooks()
        const updatedBook = bookStore.bookList.find(b => b.id === editingBook.value.id)
        if (updatedBook) {
          editingBook.value = { ...updatedBook }
        }
      }, 100)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除密码失败:', error)
      ElMessage.error('移除密码失败：' + (error.message || '密码可能错误'))
    }
  }
}
</script>

<style lang="scss" scoped>
@import url("@/scss/writing.scss");

/* 通用滚动条样式 */
@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-overlay);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 4px;

    &:hover {
      background: var(--el-border-color-dark);
    }
  }
}

.writing-manager {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  min-height: 100vh;
  box-sizing: border-box;
  position: relative;
  overflow-x: hidden; /* 防止水平滚动 */
  padding-bottom: 90px; /* 添加底部间距 */

  /* 确保容器能够正确处理子元素的动画 */
  perspective: 1000px;

  /* 原生应用样式 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;

  /* 禁用图片和媒体元素拖拽 */
  img, svg, canvas, video, audio {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    pointer-events: none;
  }

  /* 按钮和交互元素恢复指针事件 */
  button, .el-button, input, textarea, select, .el-input, .el-select, .el-textarea {
    pointer-events: auto;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 输入框内容可以选择 */
  input, textarea, .el-input__inner, .el-textarea__inner {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }

  /* 只有书籍标题可以选择 */
  .book-header h3 {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }

  /* 其他内容不可选择 */
  .book-description, .book-meta, .book-stats {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }
}

.book-list {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
  z-index: 1;
  :deep(.el-card__header) {
    flex-shrink: 0;
    position: relative;
    z-index: 2;
  }

  :deep(.el-card__body) {
    flex-grow: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0; /* 让内部的books-container控制padding */
    position: relative;
    z-index: 1;

    @include custom-scrollbar;
  }
}

.export-dialog {
  .export-panel {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-height: calc(80vh - 120px); /* 限制最大高度为视口高度的80%减去标题和按钮区域 */
    overflow: hidden; /* 防止内容溢出 */

    .export-header {
      h3 {
        margin-top: 0;
        margin-bottom: 8px;
        color: var(--el-text-color-primary);
      }

      p {
        color: var(--el-text-color-regular);
        margin-bottom: 16px;
      }

      .export-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 16px 0;
      }
    }
    
    .chapters-container {
      border: 1px solid var(--el-border-color-light);
      border-radius: 4px;
      height: 300px; /* 固定高度 */
      overflow: hidden; /* 防止内容溢出 */

      .volume-item {
        margin-bottom: 12px;

        .volume-header {
          background-color: var(--el-bg-color-page);
          padding: 8px 12px;
          border-bottom: 1px solid var(--el-border-color-light);

          .volume-title {
            font-weight: bold;
            color: var(--el-text-color-primary);
          }
        }

        .chapter-list {
          padding: 8px 12px;

          .chapter-item {
            padding: 6px 0;
            display: flex;
            align-items: center;

            .chapter-title {
              margin-right: 12px;
              color: var(--el-text-color-primary);
            }

            .chapter-word-count {
              color: var(--el-text-color-secondary);
              font-size: 0.9em;
            }
          }
        }
      }
    }
    
    .export-summary {
      text-align: right;
      color: var(--el-text-color-regular);
      font-size: 14px;
      padding: 8px 0;
    }
  }
}

/* 覆盖Element Plus的对话框样式 */
:deep(.el-dialog) {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 80vh;
  max-width: 90vw;

  /* 原生应用样式 - 对话框标题栏不能选择 */
  .el-dialog__header {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* 对话框内容可以交互 */
  .el-dialog__body {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;

    /* 但按钮不能选择 */
    button, .el-button {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
  }

  /* 对话框底部按钮区域不能选择 */
  .el-dialog__footer {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
  overflow: hidden;
}

/* 确保滚动条只出现在内容区域 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

/* 防止对话框打开时页面滚动 */
.book-settings-dialog {
  &.el-dialog__wrapper {
    overflow: hidden !important;
  }
}

.warning-text {
  color: #E6A23C;
  font-size: 13px;
  display: flex;
  align-items: center;
  margin: 15px 0;

  .el-icon {
    margin-right: 5px;
  }
}

/* 调整书籍卡片样式，添加加密标记 */
.book-card {
  .book-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none; /* 添加禁止选择文本 */
    
    .title-container {
      display: flex;
      align-items: center;
      
      /* 移除了书名前锁图标的样式 */
      
      h3 {
        user-select: text; /* 保持标题可选 */
      }
    }
  }
}

/* 书籍设置弹窗样式 */
.book-settings-dialog {
  :deep(.el-overlay) {
    overflow: hidden !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
  }

  :deep(.el-dialog) {
    border-radius: 24px;
    overflow: hidden;
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.15),
      0 10px 30px rgba(0, 0, 0, 0.1);
    max-height: 85vh;
    height: auto;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    color: white;
    padding: 20px 32px;
    border-bottom: none;
    flex-shrink: 0;

    .el-dialog__title {
      color: white;
      font-size: 18px;
      font-weight: 700;
    }

    .el-dialog__headerbtn {
      top: 20px;
      right: 32px;

      .el-dialog__close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 18px;

        &:hover {
          color: white;
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 确保flex子元素能正确收缩 */
  }

  :deep(.el-dialog__footer) {
    padding: 20px 32px;
    background: var(--el-bg-color-overlay);
    border-top: 1px solid var(--el-border-color-light);
    flex-shrink: 0;
  }
}

.book-settings-content {
  background: var(--el-bg-color);
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素能正确收缩 */
}

.settings-scroll-container {
  flex: 1;
  min-height: 0; /* 确保滚动容器能正确工作 */

  :deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
  }

  :deep(.el-scrollbar__view) {
    padding: 0;
  }
}

.settings-form-wrapper {
  padding: 24px 32px 40px 32px; /* 增加底部间距 */
}

.settings-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 24px; /* 确保最后一个区域有底部间距 */
  }
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;

  &::after {
    content: '';
    flex: 1;
    height: 2px;
    background: linear-gradient(to right, var(--el-color-primary-light-8), transparent);
    margin-left: 16px;
  }
}

.settings-form {
  .form-row {
    margin-bottom: 20px;
  }

  .form-item {
    width: 100%;
  }

  .form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }

  .styled-input,
  .styled-textarea {
    :deep(.el-input__wrapper) {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid var(--el-border-color-light);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.is-focus {
        box-shadow: 0 0 0 3px rgba(var(--el-color-primary-rgb), 0.1);
      }
    }
  }
}

/* 风格选择器样式 */
.style-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 8px;

  &.compact {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
}

.style-card {
  position: relative;
  background: var(--el-bg-color-overlay);
  border: 2px solid var(--el-border-color-light);
  border-radius: 16px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;

  &.compact {
    padding: 12px;
    border-radius: 12px;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    border-color: var(--el-color-primary-light-7);
  }

  &.selected {
    border-color: var(--el-color-primary);
    background: rgba(var(--el-color-primary-rgb), 0.05);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--el-color-primary-rgb), 0.2);
  }
}

.style-preview {
  .preview-header {
    height: 8px;
    border-radius: 4px;
    margin-bottom: 12px;
    opacity: 0.8;
  }

  .preview-content {
    .preview-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;

      .compact & {
        font-size: 14px;
        margin-bottom: 0;
      }
    }

    .preview-desc {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      line-height: 1.4;
    }
  }
}

.style-check {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: var(--el-color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  animation: checkBounce 0.3s ease;
}

@keyframes checkBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 密码保护样式 */
.password-section {
  background: var(--el-bg-color-overlay);
  border-radius: 16px;
  padding: 24px 24px 32px 24px; /* 增加底部间距 */
  border: 1px solid var(--el-border-color-light);
  margin-bottom: 16px; /* 增加与其他区域的间距 */
}

.password-setup,
.password-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.password-info,
.encrypted-info {
  .info-text {
    margin: 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
    line-height: 1.5;
  }
}

.encrypted-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(var(--el-color-warning-rgb), 0.1);
  color: var(--el-color-warning);
  padding: 8px 16px;
  border-radius: 12px;
  font-weight: 600;
  margin-bottom: 12px;
  width: fit-content;

  .lock-icon {
    font-size: 16px;
  }
}

.password-btn {
  align-self: flex-start;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;

  &.danger {
    background: linear-gradient(135deg, var(--el-color-danger), var(--el-color-danger-light));
    border: none;

    &:hover {
      background: linear-gradient(135deg, var(--el-color-danger-light), var(--el-color-danger-lighter));
    }
  }
}

/* 暗色主题适配 */
html.dark {
  .book-settings-dialog {
    :deep(.el-dialog) {
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color);
    }
  }

  .style-card {
    background: var(--el-fill-color-light);
    border-color: var(--el-border-color);

    &:hover {
      border-color: var(--el-color-primary-light-5);
    }

    &.selected {
      background: rgba(var(--el-color-primary-rgb), 0.1);
    }
  }

  .password-section {
    background: var(--el-fill-color-light);
    border-color: var(--el-border-color);
  }
}

/* 响应式调整 - 书籍设置对话框 */
@media (max-height: 800px) {
  .book-settings-dialog {
    :deep(.el-dialog) {
      max-height: 80vh;
    }

    .settings-scroll-container {
      max-height: 400px !important;
    }
  }
}

@media (max-height: 600px) {
  .book-settings-dialog {
    :deep(.el-dialog) {
      max-height: 90vh;
    }

    .settings-scroll-container {
      max-height: 300px !important;
    }

    .settings-form-wrapper {
      padding: 16px 24px 32px 24px;
    }
  }
}

/* 写作区域风格样式 */
.writing-area {
  &.writing-style-classic-blue {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #4a90e2, #6bb6ff);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #4a90e2;

      &:focus {
        border-color: #4a90e2;
        box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
      }
    }
  }

  &.writing-style-warm-orange {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #ff8c42, #ffb366);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #ff8c42;

      &:focus {
        border-color: #ff8c42;
        box-shadow: 0 0 0 2px rgba(255, 140, 66, 0.2);
      }
    }
  }

  &.writing-style-fresh-green {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #2ecc71, #58d68d);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #2ecc71;

      &:focus {
        border-color: #2ecc71;
        box-shadow: 0 0 0 2px rgba(46, 204, 113, 0.2);
      }
    }
  }

  &.writing-style-elegant-purple {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #9b59b6, #bb77d4);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #9b59b6;

      &:focus {
        border-color: #9b59b6;
        box-shadow: 0 0 0 2px rgba(155, 89, 182, 0.2);
      }
    }
  }

  &.writing-style-mysterious-dark {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #34495e, #5d6d7e);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #34495e;

      &:focus {
        border-color: #34495e;
        box-shadow: 0 0 0 2px rgba(52, 73, 94, 0.2);
      }
    }
  }

  &.writing-style-minimal-gray {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #95a5a6, #b2babb);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #95a5a6;

      &:focus {
        border-color: #95a5a6;
        box-shadow: 0 0 0 2px rgba(149, 165, 166, 0.2);
      }
    }
  }

  &.writing-style-sakura-pink {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #ff6b9d, #ff8fab);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #ff6b9d;

      &:focus {
        border-color: #ff6b9d;
        box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
      }
    }
  }

  &.writing-style-deep-ocean {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #1e3a8a, #1e40af);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #1e3a8a;

      &:focus {
        border-color: #1e3a8a;
        box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.2);
      }
    }
  }

  &.writing-style-emerald-oasis {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #059669, #047857);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #059669;

      &:focus {
        border-color: #059669;
        box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
      }
    }
  }

  &.writing-style-sunset-glow {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #dc2626, #ef4444);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #dc2626;

      &:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
      }
    }
  }

  &.writing-style-lavender-dream {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #7c3aed, #8b5cf6);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #7c3aed;

      &:focus {
        border-color: #7c3aed;
        box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
      }
    }
  }

  &.writing-style-amber-gold {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #d97706, #f59e0b);
      color: white;
    }

    :deep(.el-textarea__inner) {
      border-color: #d97706;

      &:focus {
        border-color: #d97706;
        box-shadow: 0 0 0 2px rgba(217, 119, 6, 0.2);
      }
    }
  }
}

/* 统一文本选择样式 */
.editor-container :deep(.el-textarea__inner),
.book-description,
:deep(.el-dialog__body) .el-input__inner,
:deep(.el-dialog__body) .el-textarea__inner,
.export-dialog .chapter-title,
.export-dialog .chapter-word-count,
.export-dialog .volume-title {
  user-select: text;
}
</style>