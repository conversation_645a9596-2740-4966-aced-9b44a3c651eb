import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'

export const useInspirationStore = defineStore('inspiration', () => {
  // 状态定义
  const state = reactive({
    data: {
      categories: {},
      theme: [],
      volume: [],
      keyPoint: [],
      technique: []
    },
    isLoaded: false
  })

  const loading = ref(false)
  const error = ref(null)

  // Getters
  const inspirationData = computed(() => state.data)
  const isLoaded = computed(() => state.isLoaded)
  const categories = computed(() => state.data.categories || {})
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)

  // 获取特定类别的元素
  const getElementsByCategory = (category) => {
    if (state.data && 
        state.data[category] && 
        Array.isArray(state.data[category])) {
      return state.data[category]
    }
    return []
  }

  // 加载故事灵感数据
  async function loadInspirationData() {
    try {
      loading.value = true
      error.value = null
      
      console.log('开始请求故事灵感数据...')
      
      // 检查API是否存在
      if (!window.pywebview?.api?.book_controller?.get_story_inspiration) {
        console.error('API不存在: book_controller.get_story_inspiration')
        throw new Error('API方法不存在，无法加载灵感卡池数据')
      }
      
      const result = await window.pywebview.api.book_controller.get_story_inspiration()
      console.log('API原始响应:', result)
      
      // 处理可能返回的字符串JSON，同时处理null响应
      let response = null
      if (result === null || result === undefined) {
        console.warn('API返回了null或undefined，将创建默认数据')
        // 创建默认数据结构
        response = {
          success: true,
          message: '已创建默认灵感卡池数据',
          data: createDefaultInspirationData()
        }
      } else {
        response = typeof result === 'string' ? JSON.parse(result) : result
      }
      
      console.log('处理后的响应:', response)
      
      // 检查响应是否成功并包含数据
      if (response && response.success && response.data) {
        state.data = response.data
        state.isLoaded = true
        console.log('数据加载成功:', state.data)
        return response.data
      } else if (response && response.status === 'success' && response.data) {
        // 处理不同的成功响应格式
        state.data = response.data
        state.isLoaded = true
        console.log('数据加载成功(status格式):', state.data)
        return response.data
      } else {
        // 如果没有数据，创建默认数据
        console.warn('响应没有包含有效数据，将使用默认数据')
        const defaultData = createDefaultInspirationData()
        state.data = defaultData
        state.isLoaded = true
        
        const errorMsg = response?.message || '加载灵感卡池失败: 服务器未返回有效数据，已使用默认数据'
        error.value = errorMsg
        console.error('加载灵感卡池警告:', errorMsg, response)
        
        // 尽管有警告，但我们返回默认数据而不是抛出错误
        return defaultData
      }
    } catch (err) {
      console.error('加载灵感卡池出错:', err)
      
      // 出错时，也使用默认数据
      const defaultData = createDefaultInspirationData()
      state.data = defaultData
      state.isLoaded = true
      
      error.value = err.message || '加载灵感卡池出错: 请检查网络连接'
      // 返回默认数据而不是抛出错误
      return defaultData
    } finally {
      loading.value = false
    }
  }

  // 创建默认灵感卡池数据
  function createDefaultInspirationData() {
    return {
      categories: {
        theme: {
          name: "主题层",
          description: "故事的核心主题与情感基调",
          icon: "Sunrise",
          color: "primary",
          defaultCount: 2,
          maxCount: 5
        },
        volume: {
          name: "卷级结构",
          description: "故事的大纲架构与发展脉络",
          icon: "Connection",
          color: "success",
          defaultCount: 4,
          maxCount: 8
        },
        keyPoint: {
          name: "关键点",
          description: "故事中的重要转折与关键节点",
          icon: "Key",
          color: "warning",
          defaultCount: 5,
          maxCount: 8
        },
        technique: {
          name: "技法卡",
          description: "用于优化剧情的各种写作技巧",
          icon: "TrendCharts",
          color: "danger",
          defaultCount: 3,
          maxCount: 5
        }
      },
      theme: [],
      volume: [],
      keyPoint: [],
      technique: []
    }
  }

  // 保存整个灵感卡池
  async function saveInspirationData(data) {
    try {
      loading.value = true
      error.value = null
      
      const result = await window.pywebview.api.book_controller.save_story_inspiration(data)
      // 处理可能返回的字符串JSON
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response && response.success) {
        state.data = data
        return response.data
      } else {
        const errorMsg = response?.message || '保存灵感卡池失败'
        error.value = errorMsg
        throw new Error(errorMsg)
      }
    } catch (err) {
      error.value = err.message || '保存灵感卡池失败'
      console.error('保存灵感卡池失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 保存单个类别
  async function saveInspirationCategory(categoryKey, elements) {
    try {
      loading.value = true
      error.value = null
      
      console.log(`开始保存灵感类别 ${categoryKey}，共 ${elements.length} 个元素`)
      
      const result = await window.pywebview.api.book_controller.save_inspiration_category(categoryKey, elements)
      console.log('保存灵感类别API响应:', result)
      
      // 处理可能返回的字符串JSON
      let response
      try {
        response = typeof result === 'string' ? JSON.parse(result) : result
      } catch (err) {
        console.warn('解析响应失败，使用原始响应', err)
        response = result
      }
      
      // 检查响应状态 - 注意处理多种可能的成功标识
      const isSuccess = 
        (response && response.success === true) || 
        (response && response.status === 'success') ||
        (typeof response === 'string' && response.includes('成功'))
      
      if (isSuccess) {
        // 更新本地状态
        state.data[categoryKey] = elements
        console.log(`灵感类别 ${categoryKey} 保存成功`)
        return response.data || { [categoryKey]: elements }
      } else {
        const errorMsg = response?.message || '保存灵感类别失败'
        error.value = errorMsg
        console.error('保存灵感类别失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (err) {
      error.value = err.message || `保存灵感类别 ${categoryKey} 失败`
      console.error(`保存灵感类别 ${categoryKey} 失败:`, err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 重置错误
  function clearError() {
    error.value = null
  }

  return {
    // 状态
    state,
    loading,
    error,
    
    // Getters
    inspirationData,
    isLoaded,
    categories,
    isLoading,
    hasError,
    getElementsByCategory,
    
    // Actions
    loadInspirationData,
    saveInspirationData,
    saveInspirationCategory,
    clearError
  }
}) 