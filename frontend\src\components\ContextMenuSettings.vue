<template>
  <div class="menu-settings" @contextmenu.prevent @selectstart.prevent @dragstart.prevent>
    <!-- 固定头部 -->
    <div class="menu-settings-header">
      <div class="header-title">
        <el-icon><Setting /></el-icon>
        <span>右键菜单配置</span>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="handleAddItem">
          <el-icon><Plus /></el-icon>
          添加菜单项
        </el-button>
      </div>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="menu-settings-content">
      <div class="menu-list">
        <el-table :data="menuItems" style="width: 100%" height="100%">
          <el-table-column prop="name" label="名称" width="140" show-overflow-tooltip />
          <el-table-column prop="icon" label="图标" width="80" align="center">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.icon }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="model"
            label="AI模型"
            width="120"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tag size="small" type="success" v-if="row.model">
                {{ getModelDisplayName(row.model) }}
              </el-tag>
              <span v-else class="text-placeholder">未设置</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="aiPrompt"
            label="提示语"
            min-width="200"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="prompt-preview">
                {{ row.aiPrompt ? row.aiPrompt.substring(0, 50) + (row.aiPrompt.length > 50 ? '...' : '') : '未设置' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button
                  size="small"
                  type="primary"
                  link
                  @click="handleEditItem(row)"
                >
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  link
                  @click="handleCopyPrompt(row)"
                >
                  <el-icon><DocumentCopy /></el-icon>
                  复制
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  link
                  @click="handleDeleteItem(row)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 固定底部 -->
    <div class="menu-settings-footer">
      <div class="footer-info">
        <el-icon><InfoFilled /></el-icon>
        <span>共 {{ menuItems.length }} 个菜单项</span>
      </div>
      <div class="footer-actions">
        <el-button @click="$emit('close')">关闭</el-button>
        <el-button type="primary" @click="saveSettings">保存配置</el-button>
      </div>
    </div>

    <!-- 编辑对话框 - 重新设计为全屏模式 -->
    <el-dialog
      v-model="showEditDialog"
      width="90%"
      top="5vh"
      :lock-scroll="true"
      :modal-append-to-body="false"
      destroy-on-close
      :show-close="false"
      class="menu-item-dialog native-dialog fullscreen-dialog"
      @contextmenu.prevent
      @selectstart.prevent
      @dragstart.prevent
    >
      <!-- 自定义头部 -->
      <template #header>
        <div class="dialog-header">
          <div class="dialog-title">
            <el-icon><Edit /></el-icon>
            <span>{{ editingItem?.id ? '编辑菜单项' : '新增菜单项' }}</span>
          </div>
          <div class="dialog-actions">
            <el-button size="small" @click="showEditDialog = false">
              <el-icon><Close /></el-icon>
              关闭
            </el-button>
          </div>
        </div>
      </template>

      <!-- 重新设计的对话框主体 - 左右分栏布局 -->
      <div v-if="editingItem" class="dialog-body">
        <!-- 左侧面板：基本信息和配置 -->
        <div class="left-panel">
          <el-scrollbar height="calc(80vh - 120px)">
            <div class="panel-content">
              <!-- 基本信息卡片 -->
              <div class="info-card">
                <div class="card-header">
                  <el-icon><Document /></el-icon>
                  <span>基本信息</span>
                </div>
                <div class="card-body">
                  <el-form label-width="80px">
                    <el-form-item label="名称" required>
                      <el-input v-model="editingItem.name" placeholder="请输入菜单名称" />
                    </el-form-item>

                    <el-row :gutter="16">
                      <el-col :span="12">
                        <el-form-item label="图标">
                          <el-select v-model="editingItem.icon" placeholder="请选择图标" class="w-full">
                            <el-option
                              v-for="icon in icons"
                              :key="icon.name"
                              :label="icon.label"
                              :value="icon.name"
                            >
                              <span class="icon-option">
                                <el-icon><component :is="icon.name" /></el-icon>
                                <span>{{ icon.label }}</span>
                              </span>
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="快捷键">
                          <el-input v-model="editingItem.shortcut" placeholder="例如：Ctrl+O" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="16">
                      <el-col :span="16">
                        <el-form-item label="AI模型" required>
                          <el-select v-model="editingItem.model" placeholder="请选择模型" class="w-full">
                            <el-option
                              v-for="model in models"
                              :key="model.id"
                              :label="`${model.name} (${model.providerName})`"
                              :value="model.id"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="排序">
                          <el-input-number v-model="editingItem.order" :min="0" class="w-full" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-form-item label="状态">
                      <el-switch
                        v-model="editingItem.disabled"
                        active-text="禁用"
                        inactive-text="启用"
                        :active-value="true"
                        :inactive-value="false"
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </div>

              <!-- 实体选择配置卡片 -->
              <div class="info-card">
                <div class="card-header">
                  <el-icon><Connection /></el-icon>
                  <span>实体选择配置</span>
                </div>
                <div class="card-body">
                  <el-form label-width="80px">
                    <el-form-item label="启用实体">
                      <el-switch v-model="entitySelectionEnabled" />
                    </el-form-item>

                    <template v-if="entitySelectionEnabled">
                      <el-form-item label="选择模板">
                        <el-select
                          v-model="selectedTemplateIds"
                          placeholder="请选择模板(可多选)"
                          filterable
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                          class="w-full"
                        >
                          <el-option
                            v-for="template in templates"
                            :key="template.id"
                            :label="template.name"
                            :value="template.id"
                          />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="选择实体" v-if="selectedTemplateIds.length > 0">
                        <el-select
                          v-model="selectedEntityIds"
                          placeholder="请选择实体(可多选)"
                          filterable
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                          class="w-full"
                        >
                          <el-option-group
                            v-for="template in selectedTemplatesWithEntities"
                            :key="template.id"
                            :label="template.name"
                          >
                            <el-option
                              v-for="entity in template.entities"
                              :key="entity.id"
                              :label="entity.name"
                              :value="entity.id"
                            >
                              <div class="entity-option-content">
                                <span>{{ entity.name }}</span>
                                <span class="entity-template-tag">{{ template.name }}</span>
                              </div>
                            </el-option>
                          </el-option-group>
                        </el-select>
                      </el-form-item>

                      <!-- 显示已选择的实体信息 -->
                      <el-form-item label="已选实体" v-if="selectedEntityIds.length > 0">
                        <div class="selected-entities-info">
                          <div
                            v-for="entityId in selectedEntityIds"
                            :key="entityId"
                            class="entity-item"
                          >
                            <div class="entity-name">{{ getEntityName(entityId) }}</div>
                            <div class="entity-variable">
                              可用变量: <code>${{`entity_${entityId}`}}</code>
                            </div>
                          </div>
                        </div>
                      </el-form-item>
                    </template>
                  </el-form>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>


        <!-- 右侧面板：AI提示语配置 -->
        <div class="right-panel">
          <el-scrollbar height="calc(80vh - 120px)">
            <div class="panel-content">
              <!-- AI提示语配置卡片 -->
              <div class="info-card">
                <div class="card-header">
                  <el-icon><ChatSquare /></el-icon>
                  <span>AI 提示配置</span>
                </div>
                <div class="card-body">
                  <div class="prompt-editor-container">
                    <div class="prompt-editor">
                      <div class="editor-header">
                        <div class="editor-tabs">
                          <span :class="['tab-item', { active: !showPreview }]" @click="showPreview = false">编辑</span>
                          <span :class="['tab-item', { active: showPreview }]" @click="showPreview = true">预览</span>
                        </div>
                        <div class="editor-actions">
                          <el-checkbox v-model="editingItem.includeContext" class="context-checkbox">
                            包含章节上下文
                            <el-tooltip content="启用后，会将当前章节的完整内容作为上下文添加到提示语中" placement="top">
                              <el-icon><QuestionFilled /></el-icon>
                            </el-tooltip>
                          </el-checkbox>
                        </div>
                      </div>

                      <div class="editor-content">
                        <div v-if="!showPreview" class="edit-view">
                          <el-input
                            v-model="editingItem.aiPrompt"
                            type="textarea"
                            :rows="12"
                            placeholder="请输入AI提示语..."
                            class="prompt-textarea"
                          />

                          <!-- 参数插入工具栏 -->
                          <div class="prompt-toolbar">
                            <div class="params-list">
                              <!-- 显示已选中的实体特定参数 -->
                              <template v-if="entitySelectionEnabled && selectedEntityIds.length > 0">
                                <span
                                  v-for="entityId in selectedEntityIds"
                                  :key="`entity_${entityId}`"
                                  class="param-tag entity-param-tag"
                                  @click="insertEntityParameter(entityId)"
                                >
                                  <code>${{`entity_${entityId}`}}</code>
                                  <span class="entity-name">{{ getEntityName(entityId) }}</span>
                                </span>
                              </template>

                              <!-- 常用的参数 -->
                              <span
                                v-for="param in commonParams"
                                :key="param.key"
                                class="param-tag"
                                @click="insertParameter(param.key)"
                              >
                                <code>{{ getParamDisplay(param.key) }}</code>
                              </span>
                            </div>
                            <div class="tooltip-wrapper">
                              <el-button
                                link
                                size="small"
                                @click="showParamsHelp = true"
                                class="help-button"
                              >
                                <el-icon class="help-icon"><InfoFilled /></el-icon>
                              </el-button>
                            </div>
                          </div>
                        </div>
                        <div v-else class="preview-view">
                          <div class="preview-content">
                            <div class="preview-header">
                              <el-icon><View /></el-icon>
                              <span>预览效果</span>
                            </div>
                            <div class="preview-text">{{ getPreviewText() }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>

      <!-- 固定底部操作栏 -->
      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button size="small" @click="showParamsHelp = true">
              <el-icon><QuestionFilled /></el-icon>
              参数说明
            </el-button>
          </div>
          <div class="footer-right">
            <el-button @click="showEditDialog = false">取消</el-button>
            <el-button type="primary" @click="handleSaveItem">
              <el-icon><Document /></el-icon>
              保存
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 将参数说明改为对话框展示 -->
    <el-dialog
      v-model="showParamsHelp"
      title="参数说明"
      width="400px"
      destroy-on-close
      append-to-body
    >
      <div class="params-tooltip">
        <div
          v-for="param in availableParams"
          :key="param.key"
          class="param-item"
        >
          <code>{{ getParamDisplay(param.key) }}</code>
          <span>{{ param.description }}</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showParamsHelp = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, computed } from "vue";
import { useConfigStore } from "../stores/config";
import { useAIProvidersStore } from "../stores/aiProviders";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  ArrowDown,
  QuestionFilled,
  Document,
  ChatSquare,
  Connection,
  InfoFilled,
  DocumentCopy,
  View,
  MoreFilled,
  Edit,
  Delete,
  Setting,
  Plus,
  Close
} from "@element-plus/icons-vue";
import { AVAILABLE_PARAMS, PROMPT_PARAMS } from "../constants/promptParams";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  bookId: {
    type: String,
    required: true,
  },
  editor: {
    type: Object,
    required: true,
  },
  selectedText: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["update:visible", "close"]);

const configStore = useConfigStore();
const aiProvidersStore = useAIProvidersStore();
const menuItems = ref([]);
const editingItem = ref(null);
const showEditDialog = ref(false);

// 添加模板和实体相关的状态
const templates = ref([]);
const entities = ref([]);
const selectedTemplateIds = ref([]);
const selectedEntityIds = ref([]);
const entitySelectionEnabled = ref(false);

// 添加预览相关的状态
const showPreview = ref(false);
const showParamsHelp = ref(false);

// 定义可用参数
const availableParams = AVAILABLE_PARAMS;

// 添加 models 计算属性 - 从 AI 提供商配置中获取
const models = computed(() => aiProvidersStore.allAvailableModels);

// 只保留常用参数，不再需要高级参数
const commonParams = computed(() => {
  return AVAILABLE_PARAMS.filter(param => 
    param.key === PROMPT_PARAMS.SELECTED_TEXT || 
    param.key === PROMPT_PARAMS.CONTEXT
  );
});

const defaultMenuItem = {
  id: "",
  name: "",
  icon: "",
  shortcut: "",
  aiPrompt: "",
  model: "",
  disabled: false,
  order: 0,
  includeContext: false,
  entityInfo: null,
};

const icons = [
  { name: "magic-wand", label: "魔法棒" },
  { name: "chat", label: "对话" },
  { name: "edit", label: "编辑" },
];

// 加载模板列表
const loadTemplates = async (bookId) => {
  try {
    const response = await window.pywebview.api.book_controller.get_templates(
      bookId
    );
    const result =
      typeof response === "string" ? JSON.parse(response) : response;
    if (result.status === "success") {
      templates.value = result.data || [];
    } else {
      ElMessage.error(result.message || "加载模板失败");
    }
  } catch (error) {
    ElMessage.error("加载模板失败：" + error.message);
  }
};

const handleCopyPrompt = (item) => {
  try {
    console.log("开始复制提示语:", item);
    if (item.aiPrompt) {
      let prompt = item.aiPrompt;
      const selectedText = props.editor?.state.doc.textBetween(
        props.editor.state.selection.from,
        props.editor.state.selection.to
      );

      // 使用统一的参数替换逻辑
      const context = {
        [PROMPT_PARAMS.ENTITY]: item.entityInfoList 
          ? JSON.stringify(item.entityInfoList, null, 2)
          : "",
        [PROMPT_PARAMS.SELECTED_TEXT]: selectedText || "",
        [PROMPT_PARAMS.CONTEXT]: item.includeContext
          ? props.editor?.state.doc.textBetween(
              0,
              props.editor.state.doc.content.size
            )
          : "",
      };

      // 为每个实体添加单独的占位符
      if (item.entityInfoList && item.entityInfoList.length > 0) {
        item.entityInfoList.forEach(entity => {
          if (entity && entity.id) {
            context[`entity_${entity.id}`] = JSON.stringify(entity, null, 2);
          }
        });
      }

      // 替换所有标准参数
      AVAILABLE_PARAMS.forEach((param) => {
        if (param && param.key) {
          const regex = new RegExp(`\\$\\{${param.key}\\}`, "g");
          prompt = prompt.replace(regex, (context[param.key] || ""));
        }
      });

      // 使用同样的方法处理实体特定占位符
      if (item.entityInfoList && item.entityInfoList.length > 0) {
        const allMatches = [];
        
        // 首先检查所有可能的实体占位符
        item.entityInfoList.forEach(entity => {
          if (entity && entity.id) {
            const entityId = entity.id;
            const pattern = `\\$\\{entity_${entityId}\\}`;
            const regex = new RegExp(pattern, 'g');
            
            let match;
            while ((match = regex.exec(prompt)) !== null) {
              allMatches.push({
                entityId,
                index: match.index,
                length: match[0].length,
                replacement: context[`entity_${entityId}`] || ''
              });
            }
          }
        });
        
        // 从后向前替换，避免位置偏移问题
        allMatches.sort((a, b) => b.index - a.index);
        
        allMatches.forEach(match => {
          const before = prompt.substring(0, match.index);
          const after = prompt.substring(match.index + match.length);
          prompt = before + match.replacement + after;
        });
      }

      // 在最后的复制操作之前添加日志
      console.log("处理后的提示语:", prompt);
      
      // 安全地复制到剪贴板
      window.pywebview.api.copy_to_clipboard(prompt)
        .then(() => {
          console.log("复制成功！");
          ElMessage.success("提示语已复制到剪贴板");
        })
        .catch(err => {

          ElMessage.error("复制失败，请手动复制");
        });
    }
  } catch (error) {
    console.error('处理复制提示词时出错:', error);
    ElMessage.error('处理复制时发生错误');
  }
};

// 加载实体列表
const loadEntities = async (bookId) => {
  try {
    const response = await window.pywebview.api.book_controller.get_entities(
      bookId
    );
    const result =
      typeof response === "string" ? JSON.parse(response) : response;
    if (result.status === "success") {
      entities.value = result.data || [];
    } else {
      ElMessage.error(result.message || "加载实体失败");
    }
  } catch (error) {
    ElMessage.error("加载实体失败：" + error.message);
  }
};

// 监听模板选择变化，修改为清空实体选择
watch(selectedTemplateIds, (newTemplateIds) => {
  if (newTemplateIds.length > 0 && editingItem.value) {
    // 清空实体选择
    selectedEntityIds.value = [];
    
    // 如果正在编辑项目，也同步更新
    editingItem.value.entitySelection = {
      ...editingItem.value.entitySelection,
      selectedTemplateIds: newTemplateIds,
      selectedEntityIds: []
    };
    
    // 清空实体信息列表
    editingItem.value.entityInfoList = [];
  }
});

// 添加监听实体选择变化
watch(selectedEntityIds, async (newEntityIds) => {
  if (newEntityIds.length > 0 && editingItem.value) {
    // 从已加载的实体列表中查找对应的实体
    const selectedEntities = entities.value.filter(
      (entity) => newEntityIds.includes(entity.id)
    );
    if (selectedEntities.length > 0) {
      // 将实体信息保存到菜单项的配置中
      const selectedEntitiesInfo = selectedEntities.map((entity) => ({
        id: entity.id,
        name: entity.name,
        description: entity.description,
        dimensions: entity.dimensions,
      }));
      editingItem.value.entityInfoList = selectedEntitiesInfo;
      // 更新实体选择状态
      editingItem.value.entitySelection = {
        enabled: true,
        selectedTemplateIds: selectedTemplateIds.value,
        selectedEntityIds: newEntityIds,
      };
    } else {
      ElMessage.warning("未找到选中的实体信息");
    }
  } else if (newEntityIds.length === 0 && editingItem.value) {
    // 清空实体信息
    editingItem.value.entityInfoList = [];
    editingItem.value.entitySelection = {
      enabled: false,
      selectedTemplateIds: [],
      selectedEntityIds: [],
    };
  }
});

// 确保模型列表已加载
const ensureModelsLoaded = async () => {
  try {
    // 检查AI提供商配置是否已加载
    if (!aiProvidersStore.initialized) {
      console.log('ContextMenuSettings: AI提供商配置未初始化，先加载提供商配置');
      await aiProvidersStore.loadProviders();
    }

    // 模型列表现在从 AI 提供商配置中获取，无需单独加载
    console.log('ContextMenuSettings: 可用模型数量:', models.value.length);
    if (models.value.length === 0) {
      console.warn('ContextMenuSettings: 没有可用的模型，请检查AI提供商配置');
    }
  } catch (error) {
    console.error('ContextMenuSettings: 确保模型列表加载失败:', error);
  }
};

const loadMenuItems = () => {
  menuItems.value = [...(configStore.editor.contextMenus || [])];

  // 确保每个菜单项都有正确的实体选择配置
  menuItems.value = menuItems.value.map((item) => {
    if (!item.entitySelection) {
      item.entitySelection = {
        enabled: false,
        selectedTemplateIds: [],
        selectedEntityIds: [],
      };
    }
    return item;
  });

  // 如果正在编辑某个菜单项，同步其实体选择状态
  if (editingItem.value) {
    const currentItem = menuItems.value.find(
      (item) => item.id === editingItem.value.id
    );
    if (currentItem?.entitySelection) {
      entitySelectionEnabled.value = currentItem.entitySelection.enabled;
      selectedTemplateIds.value = currentItem.entitySelection.selectedTemplateIds;
      selectedEntityIds.value = currentItem.entitySelection.selectedEntityIds;
    }
  }
};

const handleAddItem = async () => {
  // 确保模型列表已加载
  await ensureModelsLoaded();

  editingItem.value = {
    ...defaultMenuItem,
    id: `menu-${Date.now()}`,
    order: menuItems.value.length,
    model:
      configStore.selectedModel || (models.value[0]?.id) || "gpt-3.5-turbo",
  };
  showEditDialog.value = true;
};

const handleEditItem = async (item) => {
  // 确保模型列表已加载
  await ensureModelsLoaded();

  // 先加载编辑项
  editingItem.value = { ...item };

  // 如果有实体选择配置，确保模板和实体数据已加载
  if (item.entitySelection?.enabled) {
    // 确保模板和实体数据已加载
    if (props.bookId && (!templates.value.length || !entities.value.length)) {
      await Promise.all([
        loadTemplates(props.bookId),
        loadEntities(props.bookId),
      ]);
    }

    // 设置实体选择状态
    entitySelectionEnabled.value = item.entitySelection.enabled;
    
    // 支持旧版本的单模板选择或新版本的多模板选择
    if (item.entitySelection.selectedTemplateIds) {
      selectedTemplateIds.value = item.entitySelection.selectedTemplateIds;
    } else if (item.entitySelection.selectedTemplateId) {
      selectedTemplateIds.value = [item.entitySelection.selectedTemplateId];
    } else {
      selectedTemplateIds.value = [];
    }

    // 设置多实体选择
    selectedEntityIds.value = item.entitySelection.selectedEntityIds || [];

    // 如果启用了实体选择但没有实体信息列表，初始化为空数组
    if (!item.entityInfoList) {
      editingItem.value.entityInfoList = [];
    }
  } else {
    // 如果没有实体选择配置，初始化默认值
    entitySelectionEnabled.value = false;
    selectedTemplateIds.value = [];
    selectedEntityIds.value = [];
    editingItem.value.entitySelection = {
      enabled: false,
      selectedTemplateIds: [],
      selectedEntityIds: []
    };
  }

  showEditDialog.value = true;
};

const handleDeleteItem = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单项 "${item.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'native-message-box'
      }
    );

    const newItems = menuItems.value.filter((i) => i.id !== item.id);
    await configStore.updateConfigItem("editor.contextMenus", newItems);
    loadMenuItems();
    ElMessage.success("删除成功");
  } catch {
    // 用户取消删除
  }
};

// 保存所有设置
const saveSettings = async () => {
  try {
    await configStore.updateConfigItem("editor.contextMenus", menuItems.value);
    ElMessage.success("配置保存成功");
  } catch (error) {
    console.error('保存配置失败:', error);
    ElMessage.error("保存配置失败");
  }
};

// 获取模型显示名称
const getModelDisplayName = (modelId) => {
  const model = models.value.find(m => m.id === modelId);
  if (model) {
    return model.name || modelId;
  }
  return modelId || '未知模型';
};

// 获取预览文本
const getPreviewText = () => {
  if (!editingItem.value?.aiPrompt) {
    return '请先输入AI提示语...';
  }

  let preview = editingItem.value.aiPrompt;

  // 1. 替换选中文本参数
  const selectedTextRegex = /\$\{selected_text\}/g;
  const selectedTextPreview = props.selectedText || '这是选中的文本内容示例';
  preview = preview.replace(selectedTextRegex, selectedTextPreview);

  // 2. 替换章节上下文参数
  const contextRegex = /\$\{context\}/g;
  let contextPreview = '这是当前章节的上下文内容示例...';

  // 如果启用了章节上下文，尝试获取实际内容
  if (editingItem.value.includeContext && props.editor) {
    try {
      const fullContent = props.editor.state.doc.textBetween(0, props.editor.state.doc.content.size);
      if (fullContent && fullContent.length > 0) {
        // 截取前200个字符作为预览
        contextPreview = fullContent.length > 200
          ? fullContent.substring(0, 200) + '...'
          : fullContent;
      }
    } catch (error) {
      console.warn('获取章节上下文失败:', error);
    }
  }
  preview = preview.replace(contextRegex, contextPreview);

  // 3. 替换通用实体参数
  const entityRegex = /\$\{entity\}/g;
  let entityPreview = '这是实体信息的JSON数据示例';
  if (entitySelectionEnabled.value && selectedEntityIds.value.length > 0) {
    const selectedEntitiesInfo = selectedEntityIds.value.map(entityId => {
      const entity = entities.value.find(e => e.id === entityId);
      return entity ? {
        id: entity.id,
        name: entity.name,
        description: entity.description || '',
        dimensions: entity.dimensions || {}
      } : { id: entityId, name: '未知实体' };
    });
    entityPreview = JSON.stringify(selectedEntitiesInfo, null, 2);
  }
  preview = preview.replace(entityRegex, entityPreview);

  // 4. 替换特定实体参数 ${entity_xxx}
  if (entitySelectionEnabled.value && selectedEntityIds.value.length > 0) {
    selectedEntityIds.value.forEach(entityId => {
      const entitySpecificRegex = new RegExp(`\\$\\{entity_${entityId}\\}`, 'g');
      const entity = entities.value.find(e => e.id === entityId);

      let entitySpecificPreview = `{ "id": "${entityId}", "name": "未知实体" }`;
      if (entity) {
        // 构建完整的实体信息预览
        const entityData = {
          id: entity.id,
          name: entity.name,
          description: entity.description || '',
          template_id: entity.template_id || ''
        };

        // 添加维度信息
        if (entity.dimensions && Object.keys(entity.dimensions).length > 0) {
          entityData.dimensions = entity.dimensions;
        }

        // 格式化JSON，限制长度以便预览
        const jsonStr = JSON.stringify(entityData, null, 2);
        entitySpecificPreview = jsonStr.length > 300
          ? jsonStr.substring(0, 300) + '\n  ...\n}'
          : jsonStr;
      }

      preview = preview.replace(entitySpecificRegex, entitySpecificPreview);
    });
  }

  // 5. 处理其他可能的参数（保持兼容性）
  const otherParamRegex = /\$\{([^}]+)\}/g;
  preview = preview.replace(otherParamRegex, (match, paramName) => {
    // 如果是已知参数，保持原样（已经被上面的逻辑处理）
    if (['selected_text', 'context', 'entity'].includes(paramName) ||
        paramName.startsWith('entity_')) {
      return match;
    }
    // 对于未知参数，显示占位符
    return `[参数: ${paramName}]`;
  });

  return preview;
};

const handleSaveItem = async () => {
  if (!editingItem.value.name || !editingItem.value.aiPrompt) {
    ElMessage.warning("请填写必要的信息");
    return;
  }

  // 获取所有选中实体的详细信息
  const selectedEntitiesInfo = [];
  if (entitySelectionEnabled.value && selectedEntityIds.value.length > 0) {
    selectedEntityIds.value.forEach(entityId => {
      const selectedEntity = entities.value.find(
        (entity) => entity.id === entityId
      );
      if (selectedEntity) {
        // 确保实体ID被正确复制
        const { id, name, description, dimensions, template_id } = selectedEntity;
        selectedEntitiesInfo.push({ id, name, description, dimensions, template_id });
      }
    });
  }
  
  // 更新实体选择配置
  editingItem.value.entitySelection = {
    enabled: entitySelectionEnabled.value,
    selectedTemplateIds: selectedTemplateIds.value,
    selectedEntityIds: selectedEntityIds.value
  };
  
  // 确保实体信息列表有数据
  editingItem.value.entityInfoList = selectedEntitiesInfo;
  console.log("保存的实体信息列表:", editingItem.value.entityInfoList);

  const newItems = [...menuItems.value];
  const index = newItems.findIndex((item) => item.id === editingItem.value.id);

  if (index === -1) {
    newItems.push(editingItem.value);
  } else {
    newItems[index] = editingItem.value;
  }

  // 根据order排序
  newItems.sort((a, b) => a.order - b.order);

  // 保存菜单项
  await configStore.updateConfigItem("editor.contextMenus", newItems);

  loadMenuItems();
  showEditDialog.value = false;
  ElMessage.success("保存成功");
};

// 删除旧的预览文本计算属性，使用新的 getPreviewText() 方法

// 修改插入参数的方法，增加对选中实体的支持
const insertParameter = (paramKey) => {
  if (!editingItem.value) return;

  const textarea = document.querySelector(".prompt-editor textarea");
  if (!textarea) return;

  const cursorPos = textarea.selectionStart;
  const textBefore = editingItem.value.aiPrompt.substring(0, cursorPos);
  const textAfter = editingItem.value.aiPrompt.substring(textarea.selectionEnd);

  // 使用正确的参数格式
  let paramText = `\${${paramKey}}`;
  
  // 如果是特殊的entity_id参数，不直接插入
  if (paramKey === 'entity_id') {
    // 如果有选中的实体，弹出选择对话框
    if (selectedEntityIds.value.length > 0) {
      ElMessageBox.confirm(
        h('div', { class: 'entity-insert-dialog' }, [
          h('p', '请选择要插入的实体:'),
          h('div', { class: 'entity-list' }, 
            selectedEntityIds.value.map(id => {
              const entity = entities.value.find(e => e.id === id);
              return h('div', { 
                class: 'entity-option',
                onClick: () => {
                  // 插入特定实体的占位符
                  const entityParamText = `\${entity_${id}}`;
                  editingItem.value.aiPrompt = `${textBefore}${entityParamText}${textAfter}`;
                  
                  // 关闭对话框
                  ElMessageBox.close();
                  
                  // 恢复光标位置
                  nextTick(() => {
                    textarea.focus();
                    const newCursorPos = cursorPos + entityParamText.length;
                    textarea.setSelectionRange(newCursorPos, newCursorPos);
                  });
                }
              }, [
                h('span', entity?.name || `实体 ${id}`)
              ]);
            })
          )
        ]),
        '选择实体',
        {
          confirmButtonText: '取消',
          cancelButtonText: '插入全部实体',
          type: 'info',
          showCancelButton: true,
          distinguishCancelAndClose: true,
          callback: action => {
            if (action === 'cancel') {
              // 插入通用实体占位符
              editingItem.value.aiPrompt = `${textBefore}${paramText}${textAfter}`;
              
              // 恢复光标位置
              nextTick(() => {
                textarea.focus();
                const newCursorPos = cursorPos + paramText.length;
                textarea.setSelectionRange(newCursorPos, newCursorPos);
              });
            }
          }
        }
      ).catch(() => {});
      return;
    }
  }
  
  // 对于其他参数，直接插入
  editingItem.value.aiPrompt = `${textBefore}${paramText}${textAfter}`;

  // 恢复光标位置
  nextTick(() => {
    textarea.focus();
    const newCursorPos = cursorPos + paramText.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
  });
};

// 添加一个方法用于实际执行时替换参数
const replacePromptParams = (prompt, context = {}) => {
  let result = prompt;

  // 定义实际替换值的映射
  const paramValues = {
    [PROMPT_PARAMS.ENTITY]: context.entity || "",
    [PROMPT_PARAMS.SELECTED_TEXT]: context.selected || "",
    [PROMPT_PARAMS.CONTEXT]: context.context || "",
  };

  // 替换所有参数
  availableParams.forEach((param) => {
    const regex = new RegExp(`\\$\\{${param.key}\\}`, "g");
    result = result.replace(regex, paramValues[param.key] || "");
  });

  return result;
};

// 添加一个计算属性来生成参数显示格式
const getParamDisplay = (key) => `\${${key}}`;

// 根据ID获取实体名称
const getEntityName = (entityId) => {
  try {
    if (!entityId) return '未知实体';
    const entity = entities.value?.find(e => e && e.id === entityId);
    return entity ? entity.name : `实体 ${entityId.substring(0, 8)}...`;
  } catch (error) {
    console.error('获取实体名称失败:', error);
    return '未知实体';
  }
};

// 计算属性：获取所有选中模板及其包含的实体
const selectedTemplatesWithEntities = computed(() => {
  if (!selectedTemplateIds.value.length || !entities.value?.length) return [];
  
  return selectedTemplateIds.value.map(templateId => {
    const template = templates.value.find(t => t.id === templateId);
    const templateEntities = entities.value.filter(e => e.template_id === templateId);
    
    return {
      id: templateId,
      name: template?.name || '未命名模板',
      entities: templateEntities
    };
  }).filter(item => item.entities.length > 0);
});

// 修改为监听多模板选择变化
watch(selectedTemplateIds, (newTemplateIds) => {
  // 当模板选择变化时，不清空实体选择，而是过滤掉不在新选中模板中的实体
  if (newTemplateIds.length > 0 && editingItem.value) {
    // 获取所有选中模板中包含的实体ID
    const validEntityIds = entities.value
      .filter(entity => newTemplateIds.includes(entity.template_id))
      .map(entity => entity.id);
    
    // 过滤掉不在有效实体列表中的已选实体
    selectedEntityIds.value = selectedEntityIds.value.filter(id => 
      validEntityIds.includes(id)
    );
    
    // 更新编辑项的模板选择
    editingItem.value.entitySelection = {
      ...editingItem.value.entitySelection,
      selectedTemplateIds: newTemplateIds
    };
  }
});

// 修改currentTemplateEntities计算属性以适应多模板
const currentTemplateEntities = computed(() => {
  if (!selectedTemplateIds.value.length || !entities.value?.length) return [];

  // 获取所有选中模板中的实体
  return entities.value.filter(
    entity => selectedTemplateIds.value.includes(entity.template_id)
  );
});

// 添加直接插入特定实体参数的方法
const insertEntityParameter = (entityId) => {
  if (!editingItem.value) return;

  const textarea = document.querySelector(".prompt-editor textarea");
  if (!textarea) return;

  const cursorPos = textarea.selectionStart;
  const textBefore = editingItem.value.aiPrompt.substring(0, cursorPos);
  const textAfter = editingItem.value.aiPrompt.substring(textarea.selectionEnd);

  // 使用实体特定的参数格式
  const paramText = `\${entity_${entityId}}`;
  editingItem.value.aiPrompt = `${textBefore}${paramText}${textAfter}`;

  // 恢复光标位置
  nextTick(() => {
    textarea.focus();
    const newCursorPos = cursorPos + paramText.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
  });
};

onMounted(async () => {
  if (!configStore.isModelsLoaded) {
    await configStore.loadModels();
  }
  loadMenuItems();
  // 使用传入的 bookId 加载模板和实体
  if (props.bookId) {
    await Promise.all([
      loadTemplates(props.bookId),
      loadEntities(props.bookId),
    ]);
  }
});

// 添加 watch 以监听 bookId 变化
watch(
  () => props.bookId,
  async (newBookId) => {
    if (newBookId) {
      await Promise.all([loadTemplates(newBookId), loadEntities(newBookId)]);
    }
  }
);
</script>

<style lang="scss" scoped>
.menu-settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

// 固定头部
.menu-settings-header {
  flex-shrink: 0;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: linear-gradient(135deg, var(--el-bg-color), var(--el-fill-color-blank));
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  .header-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    position: relative;

    .el-icon {
      font-size: 20px;
      color: var(--el-color-primary);
      padding: 8px;
      background: var(--el-color-primary-light-9);
      border-radius: 8px;
      transition: all 0.2s ease;
    }

    span {
      background: linear-gradient(135deg, var(--el-text-color-primary), var(--el-color-primary));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: 0.5px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;

    .el-button {
      padding: 10px 16px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &.el-button--primary {
        background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
        border: none;

        &:hover {
          background: linear-gradient(135deg, var(--el-color-primary-dark-2), var(--el-color-primary));
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
        }

        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }
}

// 可滚动内容区域
.menu-settings-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
  min-height: 0; // 确保可以正确滚动
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.menu-item-form {
  .form-section {
    margin-bottom: 20px;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--el-border-color);
    background-color: var(--el-bg-color-overlay);

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px dashed var(--el-border-color);
      color: var(--el-text-color-primary);

      .el-icon {
        font-size: 18px;
        color: var(--el-color-primary);
      }
    }
  }
}

.menu-list {
  flex: 1;
  overflow: hidden;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  background: var(--el-bg-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  :deep(.el-table) {
    height: 100%;

    .el-table__inner-wrapper {
      height: 100%;
    }

    .el-table__body-wrapper {
      overflow-y: auto;
    }

    .el-table__header-wrapper {
      background: var(--el-fill-color-light);

      .el-table__header {
        th {
          background: var(--el-fill-color-light);
          color: var(--el-text-color-primary);
          font-weight: 600;
          border-bottom: 2px solid var(--el-border-color);

          .cell {
            padding: 12px 8px;
          }
        }
      }
    }

    .el-table__row {
      transition: all 0.2s ease;

      &:hover {
        background: var(--el-fill-color-light);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      td {
        border-bottom: 1px solid var(--el-border-color-lighter);

        .cell {
          padding: 12px 8px;
        }
      }
    }
  }
}

// 表格内容样式
.prompt-preview {
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.4;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-placeholder {
  color: var(--el-text-color-placeholder);
  font-style: italic;
  font-size: 12px;
}

// 表格操作按钮
.table-actions {
  display: flex;
  align-items: center;
  gap: 6px;

  .el-button {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    min-width: auto;

    .el-icon {
      margin-right: 4px;
      font-size: 14px;
    }

    &.el-button--primary {
      &:hover {
        background: var(--el-color-primary-dark-2);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(var(--el-color-primary-rgb), 0.3);
      }
    }

    &.el-button--info {
      &:hover {
        background: var(--el-color-info-dark-2);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(var(--el-color-info-rgb), 0.3);
      }
    }

    &.el-button--danger {
      &:hover {
        background: var(--el-color-danger-dark-2);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(var(--el-color-danger-rgb), 0.3);
      }
    }
  }
}

// 固定底部
.menu-settings-footer {
  flex-shrink: 0;
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: linear-gradient(to right, var(--el-bg-color), var(--el-fill-color-blank));
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  .footer-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    font-weight: 500;
    padding: 8px 12px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
    border: 1px solid var(--el-border-color-lighter);

    .el-icon {
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }

  .footer-actions {
    display: flex;
    gap: 12px;

    .el-button {
      padding: 8px 16px;
      font-weight: 500;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:not(.el-button--primary) {
        background: var(--el-fill-color-light);
        border-color: var(--el-border-color);

        &:hover {
          background: var(--el-fill-color);
          border-color: var(--el-color-primary-light-5);
          color: var(--el-color-primary);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      &.el-button--primary {
        background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
        border: none;

        &:hover {
          background: linear-gradient(135deg, var(--el-color-primary-dark-2), var(--el-color-primary));
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
        }
      }
    }
  }
}

// 全屏对话框样式
:deep(.fullscreen-dialog) {
  .el-dialog {
    margin: 0;
    border-radius: 12px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
    max-height: 90vh;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  .el-dialog__body {
    padding: 0;
    height: calc(80vh - 120px);
    overflow: hidden;
  }
}

// 对话框头部样式
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: linear-gradient(135deg, var(--el-fill-color-light), var(--el-bg-color));
  border-bottom: 1px solid var(--el-border-color-lighter);

  .dialog-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);

    .el-icon {
      font-size: 18px;
      color: var(--el-color-primary);
    }
  }

  .dialog-actions {
    display: flex;
    gap: 8px;
  }
}

// 对话框主体布局
.dialog-body {
  display: flex;
  height: 100%;
  background: var(--el-bg-color);

  .left-panel, .right-panel {
    flex: 1;
    border-right: 1px solid var(--el-border-color-lighter);

    &:last-child {
      border-right: none;
    }
  }

  .panel-content {
    padding: 20px;
  }
}

// 信息卡片样式
.info-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;

  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: var(--el-fill-color-light);
    border-bottom: 1px solid var(--el-border-color-lighter);
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    color: var(--el-text-color-primary);

    .el-icon {
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }

  .card-body {
    padding: 20px;
  }
}

// 提示语编辑器样式
.prompt-editor-container {
  .prompt-editor {
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    overflow: hidden;
    background: var(--el-bg-color);

    .editor-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-lighter);

      .editor-tabs {
        display: flex;
        gap: 16px;

        .tab-item {
          padding: 6px 12px;
          cursor: pointer;
          border-radius: 6px;
          color: var(--el-text-color-regular);
          transition: all 0.2s;
          font-size: 14px;

          &:hover {
            color: var(--el-color-primary);
            background: var(--el-fill-color);
          }

          &.active {
            color: var(--el-color-primary);
            background: var(--el-color-primary-light-9);
            font-weight: 500;
          }
        }
      }

      .context-checkbox {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 13px;

        .el-icon {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          cursor: help;
        }
      }
    }

    .editor-content {
      .edit-view {
        padding: 16px;

        .prompt-textarea {
          :deep(.el-textarea__inner) {
            border: none;
            box-shadow: none;
            background: transparent;
            resize: none;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;

            &:focus {
              box-shadow: none;
            }
          }
        }
      }

      .preview-view {
        .preview-content {
          background: var(--el-fill-color-light);

          .preview-header {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 12px 16px;
            background: var(--el-fill-color);
            border-bottom: 1px solid var(--el-border-color-lighter);
            color: var(--el-text-color-secondary);
            font-size: 13px;

            .el-icon {
              font-size: 14px;
            }
          }

          .preview-text {
            padding: 16px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.6;
            color: var(--el-text-color-regular);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            background: var(--el-bg-color-overlay);
            border-radius: 4px;

            // 美化JSON显示
            &::-webkit-scrollbar {
              width: 6px;
            }

            &::-webkit-scrollbar-thumb {
              background: var(--el-border-color-darker);
              border-radius: 3px;
            }

            &::-webkit-scrollbar-track {
              background: transparent;
            }
          }
        }
      }
    }
  }
}

// 对话框底部样式
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: linear-gradient(to right, var(--el-bg-color), var(--el-fill-color-blank));
  border-top: 1px solid var(--el-border-color-lighter);

  .footer-left {
    display: flex;
    gap: 8px;
  }

  .footer-right {
    display: flex;
    gap: 12px;

    .el-button {
      padding: 8px 16px;
      font-weight: 500;
      border-radius: 6px;

      &.el-button--primary {
        background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
        border: none;

        &:hover {
          background: linear-gradient(135deg, var(--el-color-primary-dark-2), var(--el-color-primary));
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
        }

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-light);

    .el-dialog__title {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .el-dialog__headerbtn {
      top: 16px;
      right: 16px;
      width: 32px;
      height: 32px;
      border-radius: 6px;
      transition: all 0.2s;

      &:hover {
        background: var(--el-fill-color);
      }

      .el-dialog__close {
        font-size: 16px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .el-dialog__body {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;

    // 禁用文本选择和拖拽
    * {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
    }

    // 允许输入框和文本域的文本选择
    input, textarea, .el-input__inner, .el-textarea__inner {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
    }
  }

  .menu-item-form {
    .form-section {
      padding: 20px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      &:last-child {
        border-bottom: none;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);

        .el-icon {
          font-size: 18px;
          color: var(--el-color-primary);
        }
      }
    }
  }

  .w-full {
    width: 100%;
  }

  .icon-option {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      font-size: 16px;
    }
  
}

// 原生应用样式的消息框
:deep(.native-message-box) {
  .el-message-box {
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  .el-message-box__header {
    padding: 16px 20px 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-message-box__content {
    padding: 16px 20px;
  }

  .el-message-box__btns {
    padding: 12px 20px 16px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

.prompt-editor {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background: var(--el-bg-color);

  .editor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid var(--el-border-color);
    background: var(--el-fill-color-light);
  }

  .editor-tabs {
    display: flex;
    gap: 16px;

    .tab-item {
      padding: 4px 8px;
      cursor: pointer;
      border-radius: 4px;
      color: var(--el-text-color-regular);
      transition: all 0.2s;

      &:hover {
        color: var(--el-color-primary);
        background: var(--el-fill-color);
      }

      &.active {
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
      }
    }
  }

  .editor-actions {
    .context-checkbox {
      display: flex;
      align-items: center;
      gap: 4px;

      .el-icon {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        cursor: help;
      }
    }
  }

  .edit-view,
  .preview-view {
    padding: 12px;
  }

  .edit-view {
    .el-textarea {
      .el-textarea__inner {
        line-height: 1.6;
        font-size: 14px;
        resize: vertical;
      }
    }
  }

  .preview-view {
    .preview-content {
      background: var(--el-fill-color-light);
      border-radius: 4px;
      overflow: hidden;
    }

    .preview-header {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 12px;
      background: var(--el-fill-color);
      border-bottom: 1px solid var(--el-border-color-lighter);
      color: var(--el-text-color-secondary);
      font-size: 13px;

      .el-icon {
        font-size: 14px;
      }
    }

    .preview-text {
      padding: 12px;
      min-height: 120px;
      white-space: pre-wrap;
      line-height: 1.6;
      color: var(--el-text-color-regular);
    }
  }
}

.prompt-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0 0;

  .params-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .tooltip-wrapper {
    .help-icon {
      font-size: 16px;
      color: var(--el-text-color-secondary);
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }
}

.param-tag {
  display: inline-flex;
  align-items: center;
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  padding: 3px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);

  // 暗色主题适配
  @media (prefers-color-scheme: dark) {
    background: var(--el-color-primary-dark-2);
    border-color: var(--el-color-primary-dark-1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  &:hover {
    background: var(--el-color-primary-light-8);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    
    @media (prefers-color-scheme: dark) {
      background: var(--el-color-primary-dark-1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
    }
  }

  code {
    background: none;
    padding: 0;
    color: var(--el-color-primary);

    font-weight: 500;
    
    @media (prefers-color-scheme: dark) {
      color: var(--el-color-primary-light-9);
    }
  }
}

.params-tooltip {
  max-height: 350px;
  overflow-y: auto;
  
  .param-item {
    display: flex;
    flex-direction: column;
    padding: 10px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    &:last-child {
      border-bottom: none;
    }
    
    code {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      padding: 2px 6px;
      border-radius: 4px;
      margin-bottom: 5px;
      
      @media (prefers-color-scheme: dark) {
        background: var(--el-color-primary-dark-2);
        color: var(--el-color-primary-light-8);
      }
    }
    
    span {
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }
}

.dialog-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.mt-2 {
  margin-top: 12px;
}

.mb-2 {
  margin-bottom: 12px;
}

.selected-entities-info {
  max-height: 180px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  padding: 10px;
  background: var(--el-fill-color-light);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.entity-item {
  padding: 8px 10px;
  border-radius: 4px;
  background: var(--el-bg-color);
  margin-bottom: 8px;
  transition: all 0.2s;
  border: 1px solid var(--el-border-color-lighter);
  
  &:hover {
    border-color: var(--el-color-primary-light-5);
    background: var(--el-fill-color-blank);
  }
  
  &:not(:last-child) {
    margin-bottom: 8px;
  }
  
  .entity-name {
    font-weight: 600;
    color: var(--el-color-primary);
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 6px;
    
    &:before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--el-color-success);
    }
  }
  
  .entity-variable {
    font-size: 13px;
    color: var(--el-text-color-secondary);
    background: var(--el-fill-color);
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid var(--el-color-primary-light-5);
    
    code {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary-dark-2);
      padding: 2px 5px;
      border-radius: 3px;
      font-weight: 500;
    }
  }
}

.entity-insert-dialog {
  padding: 15px 0 5px;
  
  p {
    margin-bottom: 12px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  .entity-list {
    max-height: 250px;
    overflow-y: auto;
    margin-top: 12px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    background: var(--el-bg-color-overlay);
  }
  
  .entity-option {
    padding: 10px 12px;
    cursor: pointer;
    transition: all 0.15s;
    border-left: 3px solid transparent;
    
    &:hover {
      background: var(--el-color-primary-light-9);
      border-left-color: var(--el-color-primary);
      
      @media (prefers-color-scheme: dark) {
        background: var(--el-color-primary-dark-2);
      }
    }
    
    &:not(:last-child) {
      border-bottom: 1px solid var(--el-border-color-lighter);
    }
  }
}

.entity-option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 2px 0;
  
  .entity-template-tag {
    font-size: 11px;
    padding: 1px 6px;
    border-radius: 10px;
    background: var(--el-color-info-light-8);
    color: var(--el-color-info-dark-2);
    white-space: nowrap;
    font-weight: 500;
    
    @media (prefers-color-scheme: dark) {
      background: var(--el-color-info-dark-2);
      color: var(--el-color-info-light-8);
    }
  }
}

.entity-param-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  background: var(--el-color-success-light-9);
  border-color: var(--el-color-success-light-5);
  padding: 3px 6px 3px 10px;
  
  @media (prefers-color-scheme: dark) {
    background: var(--el-color-success-dark-2);
    border-color: var(--el-color-success-dark-1);
  }
  
  .entity-name {
    font-size: 12px;
    color: var(--el-color-success-dark-2);
    max-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
    border-left: 1px solid var(--el-color-success-light-5);
    padding-left: 4px;
    margin-left: 2px;
    
    @media (prefers-color-scheme: dark) {
      color: var(--el-color-success-light-8);
      border-left-color: var(--el-color-success-light-7);
    }
  }
  
  &:hover {
    background: var(--el-color-success-light-8);
    border-color: var(--el-color-success);
    
    @media (prefers-color-scheme: dark) {
      background: var(--el-color-success-dark-1);
      border-color: var(--el-color-success-light-7);
    }
  }
}

.params-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 5px 0;
  max-width: 100%;
}

.form-section {
  padding: 24px !important;
  
  .section-title {
    margin-bottom: 25px !important;
    padding-bottom: 10px;
    border-bottom: 1px dashed var(--el-border-color);
    
    .el-icon {
      font-size: 20px !important;
    }
  }
}

.prompt-editor {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color);
  
  .editor-header {
    background: var(--el-fill-color-light);
    padding: 10px 15px;
  }
  
  .edit-view, .preview-view {
    padding: 15px;
  }
  
  textarea {
    line-height: 1.6;
    font-size: 14px;
  }
}

.preview-content {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color);
  
  .preview-header {
    padding: 10px 15px;
  }
  
  .preview-text {
    padding: 15px;
    line-height: 1.6;
    font-size: 14px;
    color: var(--el-text-color-primary);
    background: var(--el-bg-color-overlay);
  }
}

.w-full {
  width: 100%;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    font-size: 16px;
  }
}

:deep(.el-dialog) {
  z-index: 2001 !important;
  background: var(--el-bg-color) !important;
  
  .el-dialog__header {
    background: var(--el-bg-color) !important;
  }
  
  .el-dialog__body {
    background: var(--el-bg-color) !important;
  }
  
  .el-dialog__footer {
    background: var(--el-bg-color) !important;
  }
}

:deep(.el-input__wrapper),
:deep(.el-textarea__wrapper) {
  background-color: var(--el-input-bg-color, var(--el-fill-color-blank)) !important;
}

:deep(.el-select-dropdown) {
  background-color: var(--el-bg-color) !important;
  
  .el-select-dropdown__item {
    color: var(--el-text-color-regular) !important;
    
    &.selected {
      color: var(--el-color-primary) !important;
    }
    
    &.hover {
      background-color: var(--el-fill-color-light) !important;
    }
  }
}

.help-button {
  padding: 4px;
  margin-left: 5px;

  .help-icon {
    font-size: 16px;
    color: var(--el-text-color-secondary);
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .menu-settings-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .header-title {
      justify-content: center;
      font-size: 16px;
    }

    .header-actions {
      justify-content: center;
    }
  }

  .menu-settings-content {
    padding: 16px 20px;
  }

  .menu-settings-footer {
    padding: 12px 20px;
    flex-direction: column;
    gap: 12px;

    .footer-info {
      justify-content: center;
    }

    .footer-actions {
      justify-content: center;
    }
  }

  .menu-list {
    :deep(.el-table) {
      .el-table__header {
        th {
          .cell {
            padding: 8px 4px;
            font-size: 12px;
          }
        }
      }

      .el-table__row {
        td {
          .cell {
            padding: 8px 4px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;

    .el-button {
      padding: 4px 8px;
      font-size: 11px;

      .el-icon {
        margin-right: 2px;
        font-size: 12px;
      }
    }
  }
}
</style>
