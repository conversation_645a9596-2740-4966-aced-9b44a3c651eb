<template>
  <div class="app-header">
    <!-- 左侧：面包屑导航 -->
    <div class="header-left">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item 
          v-for="item in breadcrumbs" 
          :key="item.path"
          :to="item.path"
        >
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 右侧：用户信息和操作 -->
    <div class="header-right">
      <!-- 主题切换 -->
      <el-button 
        text 
        @click="toggleTheme"
        class="header-btn"
      >
        <el-icon>
          <Sunny v-if="configStore.isDark" />
          <Moon v-else />
        </el-icon>
      </el-button>

      <!-- 激活状态 -->
      <div class="activation-status">
        <el-tag 
          :type="userStore.isActivated ? 'success' : 'warning'"
          size="small"
        >
          {{ activationText }}
        </el-tag>
      </div>

      <!-- 用户菜单 -->
      <el-dropdown @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="32" :icon="UserFilled" />
          <span class="username">{{ userStore.userInfo.username || 'User' }}</span>
          <el-icon><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="activation">
              <el-icon><Key /></el-icon>
              激活管理
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item command="about">
              <el-icon><InfoFilled /></el-icon>
              关于
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 激活对话框 -->
    <el-dialog
      v-model="showActivationDialog"
      title="激活管理"
      width="500px"
    >
      <div class="activation-content">
        <el-form :model="activationForm" label-width="100px">
          <el-form-item label="激活状态">
            <el-tag 
              :type="userStore.isActivated ? 'success' : 'warning'"
              size="large"
            >
              {{ activationText }}
            </el-tag>
          </el-form-item>
          
          <el-form-item v-if="!userStore.isActivated" label="激活码">
            <el-input 
              v-model="activationForm.code"
              placeholder="请输入激活码"
              type="password"
              show-password
            />
          </el-form-item>
          
          <el-form-item label="机器码">
            <el-input 
              v-model="machineCode"
              readonly
              type="textarea"
              :rows="3"
            >
              <template #append>
                <el-button @click="copyMachineCode">复制</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showActivationDialog = false">取消</el-button>
        <el-button 
          v-if="!userStore.isActivated"
          type="primary" 
          @click="activateCode"
          :loading="activating"
        >
          激活
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useConfigStore } from '@/stores/config'
import { useUserStore } from '@/stores/user'
import { menuList } from '@/config/menuConfig.js'
import {
  Sunny, Moon, UserFilled, ArrowDown, Key, Setting, 
  InfoFilled, SwitchButton
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const configStore = useConfigStore()
const userStore = useUserStore()

// 激活相关状态
const showActivationDialog = ref(false)
const activating = ref(false)
const machineCode = ref('')
const activationForm = ref({
  code: ''
})

// 面包屑导航
const breadcrumbs = computed(() => {
  const crumbs = []
  const pathSegments = route.path.split('/').filter(Boolean)
  
  // 查找当前路径对应的菜单项
  function findMenuByPath(menus, targetPath) {
    for (const menu of menus) {
      if (menu.path === targetPath) {
        return menu
      }
      if (menu.children) {
        const found = findMenuByPath(menu.children, targetPath)
        if (found) {
          return found
        }
      }
    }
    return null
  }
  
  // 构建面包屑
  let currentPath = ''
  for (const segment of pathSegments) {
    currentPath += '/' + segment
    const menu = findMenuByPath(menuList, currentPath)
    
    if (menu) {
      crumbs.push({
        title: menu.title,
        path: menu.path
      })
    }
  }
  
  return crumbs
})

// 激活状态文本
const activationText = computed(() => {
  if (userStore.isActivated) {
    return userStore.activationType === 'permanent' ? '已激活' : '试用版'
  } else {
    return `试用剩余 ${userStore.trialDaysLeft} 天`
  }
})

// 切换主题
async function toggleTheme() {
  const newTheme = configStore.isDark ? 'light' : 'dark'
  try {
    await configStore.updateConfigItem('theme', newTheme)
    ElMessage.success(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}主题`)
  } catch (error) {
    ElMessage.error('主题切换失败')
  }
}

// 处理用户菜单命令
async function handleUserCommand(command) {
  switch (command) {
    case 'activation':
      await openActivationDialog()
      break
    case 'settings':
      router.push('/settings')
      break
    case 'about':
      showAboutDialog()
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 打开激活对话框
async function openActivationDialog() {
  showActivationDialog.value = true
  
  // 获取机器码
  try {
    const result = await userStore.getMachineCode()
    if (result.status === 'success') {
      machineCode.value = result.data
    }
  } catch (error) {
    ElMessage.error('获取机器码失败')
  }
}

// 激活码验证
async function activateCode() {
  if (!activationForm.value.code.trim()) {
    ElMessage.warning('请输入激活码')
    return
  }
  
  activating.value = true
  try {
    const result = await userStore.verifyActivationCode({
      code: activationForm.value.code,
      machineCode: machineCode.value
    })
    
    if (result.status === 'success') {
      ElMessage.success('激活成功')
      showActivationDialog.value = false
      activationForm.value.code = ''
    } else {
      ElMessage.error(result.message || '激活失败')
    }
  } catch (error) {
    ElMessage.error('激活失败: ' + error.message)
  } finally {
    activating.value = false
  }
}

// 复制机器码
function copyMachineCode() {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(machineCode.value)
    ElMessage.success('机器码已复制到剪贴板')
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = machineCode.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('机器码已复制到剪贴板')
  }
}

// 显示关于对话框
function showAboutDialog() {
  ElMessageBox.alert(
    `
    <div style="text-align: center;">
      <h3>PVV小说创作软件</h3>
      <p>版本: 1.0.0</p>
      <p>基于 Electron + Vue3 + Element Plus</p>
      <p>专业的小说创作工具</p>
    </div>
    `,
    '关于',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定'
    }
  )
}

// 退出登录
async function handleLogout() {
  try {
    await ElMessageBox.confirm(
      '确定要退出应用吗？',
      '确认退出',
      {
        confirmButtonText: '退出',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里可以调用退出API或直接关闭应用
    if (window.electronAPI) {
      window.electronAPI.invoke('app-quit')
    }
  } catch (error) {
    // 用户取消退出
  }
}

onMounted(() => {
  // 初始化用户状态
  userStore.initialize()
})
</script>

<style scoped>
.app-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-btn {
  padding: 8px;
}

.activation-status {
  margin-right: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: var(--el-fill-color-light);
}

.username {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.activation-content {
  padding: 20px 0;
}

/* 面包屑样式 */
:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: var(--el-color-primary);
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover) {
  color: var(--el-color-primary);
  cursor: default;
}
</style>
