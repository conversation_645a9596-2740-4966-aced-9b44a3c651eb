import re
import time
import platform
import subprocess

from DrissionPage import ChromiumOptions, Chromium
import os
import json
import uuid
import base64
import threading
from datetime import datetime

from html2docx import html2docx

from .Base import ResponsePacket
from .ConfigManager import ConfigManager

# CSS解密相关内容
CODE_ST = 58344
CODE_ED = 58715
charset = ['D', '在', '主', '特', '家', '军', '然', '表', '场', '4', '要', '只', 'v', '和', '?', '6', '别', '还', 'g',
           '现', '儿', '岁', '?', '?', '此', '象', '月', '3', '出', '战', '工', '相', 'o', '男', '直', '失', '世', 'F',
           '都', '平', '文', '什', 'V', 'O', '将', '真', 'T', '那', '当', '?', '会', '立', '些', 'u', '是', '十', '张',
           '学', '气', '大', '爱', '两', '命', '全', '后', '东', '性', '通', '被', '1', '它', '乐', '接', '而', '感',
           '车', '山', '公', '了', '常', '以', '何', '可', '话', '先', 'p', 'i', '叫', '轻', 'M', '士', 'w', '着', '变',
           '尔', '快', 'l', '个', '说', '少', '色', '里', '安', '花', '远', '7', '难', '师', '放', 't', '报', '认',
           '面', '道', 'S', '?', '克', '地', '度', 'I', '好', '机', 'U', '民', '写', '把', '万', '同', '水', '新', '没',
           '书', '电', '吃', '像', '斯', '5', '为', 'y', '白', '几', '日', '教', '看', '但', '第', '加', '候', '作',
           '上', '拉', '住', '有', '法', 'r', '事', '应', '位', '利', '你', '声', '身', '国', '问', '马', '女', '他',
           'Y', '比', '父', 'x', 'A', 'H', 'N', 's', 'X', '边', '美', '对', '所', '金', '活', '回', '意', '到', 'z',
           '从', 'j', '知', '又', '内', '因', '点', 'Q', '三', '定', '8', 'R', 'b', '正', '或', '夫', '向', '德', '听',
           '更', '?', '得', '告', '并', '本', 'q', '过', '记', 'L', '让', '打', 'f', '人', '就', '者', '去', '原', '满',
           '体', '做', '经', 'K', '走', '如', '孩', 'c', 'G', '给', '使', '物', '?', '最', '笑', '部', '?', '员', '等',
           '受', 'k', '行', '一', '条', '果', '动', '光', '门', '头', '见', '往', '自', '解', '成', '处', '天', '能',
           '于', '名', '其', '发', '总', '母', '的', '死', '手', '入', '路', '进', '心', '来', 'h', '时', '力', '多',
           '开', '己', '许', 'd', '至', '由', '很', '界', 'n', '小', '与', 'Z', '想', '代', '么', '分', '生', '口',
           '再', '妈', '望', '次', '西', '风', '种', '带', 'J', '?', '实', '情', '才', '这', '?', 'E', '我', '神', '格',
           '长', '觉', '间', '年', '眼', '无', '不', '亲', '关', '结', '0', '友', '信', '下', '却', '重', '己', '老',
           '2', '音', '字', 'm', '呢', '明', '之', '前', '高', 'P', 'B', '目', '太', 'e', '9', '起', '稜', '她', '也',
           'W', '用', '方', '子', '英', '每', '理', '便', '四', '数', '期', '中', 'C', '外', '样', 'a', '海', '们',
           '任']

def interpreter(cc):
    bias = cc - CODE_ST
    if charset[bias] == '?':
        return chr(cc)
    return charset[bias]

def fanqie_decode_text(text):
    decoded_text = ''
    for char in text:
        cc = ord(char)
        if cc >= CODE_ST and cc <= CODE_ED:
            decoded_text += interpreter(cc)
        else:
            decoded_text += char
    return decoded_text

class DrssionController(ResponsePacket):
    def __init__(self, config_file):
        super().__init__()
        self.config_file = config_file
        self.config_controller = ConfigManager(config_file)
        self.config = self._load_config()
        self.config_dir = os.path.dirname(config_file)
        self.rules_file = os.path.join(self.config_dir, 'novel_rules.json')
        
        # 默认规则
        self.rules = {

        }
        
        # 加载用户自定义规则
        user_rules = self._load_rules()
        # 更新规则字典
        self.rules.update(user_rules)
        
        # 存储运行中的Chrome实例
        self.running_instances = {}
        # 任务管理
        self.tasks = {}
        self.task_outputs = {}
        self.task_id_counter = 0

    def _load_config(self):
        """Load config from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"Error loading config: {e}")
            return {}

    def _load_rules(self):
        """Load novel rules from file"""
        try:
            if os.path.exists(self.rules_file):
                with open(self.rules_file, 'r', encoding='utf-8') as f:
                    user_rules = json.load(f)
                    # If a user rule has the same ID as a default rule, it overrides the default
                    self.rules.update(user_rules)
            return self.rules
        except Exception as e:
            print(f"Error loading rules: {e}")
            return self.rules

    def _save_rules(self):
        """Save novel rules to file"""
        try:
            # Save all rules including modified defaults
            with open(self.rules_file, 'w', encoding='utf-8') as f:
                json.dump(self.rules, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"Error saving rules: {e}")
            return False

    def get_novel_rules(self):
        """获取所有小说规则"""
        try:
            return {
                "status": "success",
                "rules": self.rules
            }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

    def save_novel_rule(self, rule):
        """保存小说规则"""
        try:
            # 更新规则
            self.rules[rule["id"]] = {
                "id": rule["id"],
                "name": rule["name"],
                "book_title_rule": rule["book_title_rule"],
                "directory_rule": rule["directory_rule"],
                "content_rule": rule["content_rule"],
                "description_rule": rule["description_rule"],
                "need_decrypt": rule.get("need_decrypt", False)
            }
            
            # 保存到文件
            self._save_rules()
            
            return {
                "status": "success",
                "message": "规则保存成功"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

    def delete_novel_rule(self, rule_id):
        """删除小说规则"""
        try:
            # 删除规则
            if rule_id in self.rules:
                del self.rules[rule_id]
                # 保存到文件
                self._save_rules()
                
                return {
                    "status": "success",
                    "message": "规则删除成功"
                }
            else:
                return {
                    "status": "error",
                    "message": "规则不存在"
                }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

    def test_novel_rule(self, data):
        """测试小说规则"""
        try:
            url = data.get("url")
            rule = data.get("rule")
            
            if not url or not rule:
                return self._error_response("规则或URL不能为空")

            # 初始化浏览器
            co = ChromiumOptions().auto_port()
            browser = Chromium(addr_or_opts=co)
            tab = browser.latest_tab
            
            try:
                # 访问URL
                tab.get(url)
                
                # 获取书名和简介
                title_elements = tab.eles("xpath:" + rule["book_title_rule"])
                book_title = title_elements[0].texts()[0].strip() if title_elements else "未知书籍"
                
                brief_elements = tab.eles("xpath:" + rule["description_rule"])
                book_brief = brief_elements[0].inner_html.strip() if brief_elements else "暂无简介"
                
                # 获取目录列表
                directories = tab.eles("xpath:" + rule["directory_rule"])
                chapter_list = []
                
                for chapter in directories:
                    chapter_title = chapter.attr("text").strip()
                    chapter_url = chapter.attr("href").strip()
                    chapter_list.append({
                        "title": chapter_title,
                        "url": chapter_url
                    })
                book = {"book_title": book_title, "book_brief": book_brief, "chapter_list": chapter_list}
                    
                return self._success_response("测试成功。",book)
                
            finally:
                browser.quit()
                
        except Exception as e:

            return self._error_response(str(e))

    def _config_options(self, port, user_data_dir, browser_path, load_extensions=None):
        """配置Chrome启动选项"""
        co = ChromiumOptions(read_file=False)
        co.set_user_data_path(user_data_dir)
        co.set_local_port(port)
        co.set_browser_path(browser_path)
        if load_extensions:
            co.add_extension(load_extensions)
        return co

    def _is_chrome_alive(self, chrome):
        """检查Chrome实例是否还在运行"""
        try:
            # 尝试获取标签页，如果失败说明Chrome已关闭
            chrome.latest_tab
            return True
        except Exception:
            return False

    def _cleanup_dead_instances(self):
        """清理已经关闭的Chrome实例"""
        dead_keys = []
        for key, chrome in self.running_instances.items():
            if not self._is_chrome_alive(chrome):
                dead_keys.append(key)

        # 从中移除死亡实例
        for key in dead_keys:
            del self.running_instances[key]

    def create_chrome_instance(self, user_data_dir, port, browser_path, load_extensions=None):
        """创建并启动Chrome实例"""
        try:
            # 清理已关闭的实例
            self._cleanup_dead_instances()

            # 检查是否已经有相同配置的实例在运行
            instance_key = f"{user_data_dir}_{port}"
            if instance_key in self.running_instances:
                chrome = self.running_instances[instance_key]
                if self._is_chrome_alive(chrome):
                    return {'status': 'error', 'message': '该配置的Chrome实例已在运行'}

            # 配置Chrome选项
            options = self._config_options(port, user_data_dir, browser_path, load_extensions)

            # 创建Chrome实例
            chrome = Chromium(addr_or_opts=options)
            self.running_instances[instance_key] = chrome

            return {'status': 'success', 'message': 'Chrome实例创建成功'}
        except Exception as e:
            return {'status': 'error', 'message': f'创建Chrome实例失败: {str(e)}'}

    def stop_chrome_instance(self, user_data_dir, port):
        """停止指定的Chrome实例"""
        try:
            instance_key = f"{user_data_dir}_{port}"
            if instance_key in self.running_instances:
                chrome = self.running_instances[instance_key]
                try:
                    if self._is_chrome_alive(chrome):
                        chrome.quit()  # 关闭Chrome
                except Exception:
                    pass  # 忽略关闭时的错误
                finally:
                    del self.running_instances[instance_key]  # 从管理器中移除
                return {'status': 'success', 'message': 'Chrome已停止'}
            return {'status': 'error', 'message': '未找到运行中的Chrome实例'}
        except Exception as e:
            return {'status': 'error', 'message': f'停止Chrome失败: {str(e)}'}

    def is_chrome_running(self, user_data_dir, port):
        """检查指定配置的Chrome是否在运行"""
        try:
            # 清理已关闭的实例
            self._cleanup_dead_instances()

            instance_key = f"{user_data_dir}_{port}"
            is_running = False
            if instance_key in self.running_instances:
                is_running = self._is_chrome_alive(self.running_instances[instance_key])

            return {'status': 'success', 'data': {'running': is_running}}
        except Exception as e:
            return {'status': 'error', 'message': f'检查Chrome状态失败: {str(e)}', 'data': {'running': False}}

    def get_chrome_path(self):
        """获取Chrome浏览器路径"""
        try:
            config = self.config_controller.load_config()
            return config.get('chrome', {}).get('path')
        except Exception:
            return None

    def detect_chrome_path(self):
        """自动检测 Chrome 浏览器路径"""
        try:
            system = platform.system()
            chrome_paths = []
            
            if system == "Windows":
                # Windows上的常见Chrome安装路径
                chrome_paths = [
                    os.path.join(os.environ.get('LOCALAPPDATA', ''), r"Google\Chrome\Application\chrome.exe"),
                    os.path.join(os.environ.get('PROGRAMFILES', ''), r"Google\Chrome\Application\chrome.exe"),
                    os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), r"Google\Chrome\Application\chrome.exe"),
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
                ]
            elif system == "Darwin":  # macOS
                chrome_paths = [
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                    os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
                ]
            else:  # Linux
                chrome_paths = [
                    "/usr/bin/google-chrome",
                    "/usr/bin/google-chrome-stable",
                    "/usr/bin/chromium",
                    "/usr/bin/chromium-browser",
                    "/snap/bin/chromium",
                    os.path.expanduser("~/.local/bin/chrome")
                ]
                
                # 使用which命令查找
                try:
                    chrome_path = subprocess.run(
                        ["which", "google-chrome"], 
                        capture_output=True, 
                        text=True
                    ).stdout.strip()
                    if chrome_path:
                        chrome_paths.append(chrome_path)
                except Exception:
                    pass

            # 检查每个可能的路径
            for path in chrome_paths:
                if os.path.isfile(path):
                    # 更新配置
                    config = self.config_controller.load_config()
                    if 'chrome' not in config:
                        config['chrome'] = {}
                    config['chrome']['path'] = path
                    self.config_controller.save_config(config)
                    return self._success_response("Chrome路径检测成功", {"path": path})

            return self._error_response("未找到Chrome浏览器。请手动指定路径或安装Chrome浏览器。")
        except Exception as e:
            return self._error_response(f"检测Chrome路径出错: {str(e)}")

    def select_chrome_path(self):
        """选择Chrome可执行文件路径"""
        try:
            result = self.select_directory()
            if result.get('status') == 'success':
                chrome_path = result['data']
                # 更新配置
                config = self.config_controller.load_config()
                config['chrome']['path'] = chrome_path
                self.config_controller.save_config(config)
                return self._success_response("Chrome路径设置成功", {"path": chrome_path})
            return result
        except Exception as e:
            return self._error_response(f"选择Chrome路径出错: {str(e)}")

    def start_chrome_with_profile(self, profile_data):
        """启动Chrome并执行指定的代码"""
        print(profile_data)
        try:
            path = profile_data.get("path")
            port = profile_data.get("port", "9222")
            extensions_enabled = profile_data.get("extensions_enabled", False)
            extensions_path = profile_data.get("extensions_path", "")

            # 获取Chrome路径
            browser_path = profile_data.get("browser_path", None)

            # 创建Chrome实例，如果启用了扩展则传入扩展路径
            load_extensions = extensions_path if extensions_enabled else None
            result = self.create_chrome_instance(path, port, browser_path, load_extensions)

            if result['status'] != 'success':
                return self._error_response(result['message'])

            return self._success_response("Chrome启动成功", {
                "running": True
            })
        except Exception as e:
            return self._error_response(str(e))

    def stop_chrome_profile(self, profile_data):
        """停止Chrome实例"""
        try:
            path = profile_data.get("path")
            port = profile_data.get("port", "9222")

            result = self.stop_chrome_instance(path, port)
            if result['status'] == 'success':
                return self._success_response(result['message'])
            else:
                return self._error_response(result['message'])
        except Exception as e:
            return self._error_response(str(e))

    def check_chrome_status(self, profile_data):
        """检查Chrome实例状态"""
        try:
            path = profile_data.get("path")
            port = profile_data.get("port", "9222")

            result = self.is_chrome_running(path, port)
            if result['status'] == 'success':
                return self._success_response("获取状态成功", result['data'])
            else:
                return self._error_response(result['message'], result.get('data', {}))
        except Exception as e:
            return self._error_response(str(e))

    def create_task(self, task_config):
        """创建新任务"""
        try:
            task_id = str(uuid.uuid4())
            task = {
                'id': task_id,
                'status': 'initializing',
                'config': task_config,
                'chrome_instance': None,
                'created_at': datetime.now(),
                'progress': 0
            }
            self.tasks[task_id] = task
            self.task_outputs[task_id] = []
            
            # 启动任务
            threading.Thread(target=self._run_task, args=(task_id,)).start()
            
            return {
                'status': 'success',
                'task_id': task_id
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e)
            }

    def _run_task(self, task_id):
        """运行任务的核心逻辑"""
        task = self.tasks[task_id]
        try:
            # 配置Chrome
            port = task['config'].get('port')
            user_data_dir = task['config'].get('user_data_dir')
            browser_path = task['config'].get('browser_path')
            load_extensions = task['config'].get('load_extensions')
            
            # 创建Chrome实例
            chrome = self.create_chrome_instance(user_data_dir, port, browser_path, load_extensions)
            task['chrome_instance'] = chrome
            task['status'] = 'running'
            
            # 发送状态更新到前端
            self._send_task_output(task_id, "Chrome实例创建成功")
            
            # TODO: 在这里实现具体任务逻辑
            
        except Exception as e:
            task['status'] = 'failed'
            self._send_task_output(task_id, f"Error: {str(e)}")
            
    def _send_task_output(self, task_id, content):
        """发送任务输出到前端"""
        if task_id in self.task_outputs:
            output = {
                'time': datetime.now().isoformat(),
                'content': content
            }
            self.task_outputs[task_id].append(output)
            import webview
            # 通过pywebview发送到前端
            encoded_content = base64.b64encode(json.dumps(output).encode()).decode()
            webview.Window[0].evaluate_js(f"window.receiveTaskOutput('{task_id}', '{encoded_content}')")
            
    def get_task_status(self, task_id):
        """获取任务状态"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            return {
                'status': 'success',
                'data': {
                    'status': task['status'],
                    'progress': task['progress'],
                    'created_at': task['created_at'].isoformat()
                }
            }
        return {
            'status': 'error',
            'message': '任务不存在'
        }

    def get_tasks(self):
        """获取所有任务"""

        try:
            tasks_info = []
            for task_id, task in self.tasks.items():
                tasks_info.append({
                    'id': task_id,
                    'status': task['status'],
                    'progress': task['progress'],
                    'created_at': task['created_at'],
                    'config': task['config'],
                    'book_info': task['book_info'],
                    'chapters': task['chapters'],
                    'outputs': task['outputs'],
                    'error': task['error']
                })
            return self._success_response("成功获取所有信息",tasks_info)
            
        except Exception as e:
            return self._error_response(str(e))

    def create_download_task(self, task_config):
        """创建下载任务"""
        try:
            task_id = str(self.task_id_counter)
            self.task_id_counter += 1
            
            task = {
                'id': task_id,
                'config': task_config,
                'status': 'initializing',
                'progress': 0,
                'book_info': {},
                'chapters': [],
                'outputs': [],
                'error': None,
                'created_at': datetime.now().isoformat()
            }
            
            self.tasks[task_id] = task
            
            # 启动下载任务
            threading.Thread(target=self._run_download_task, args=(task_id,)).start()
            
            return self._success_response({'task_id': task_id})
            
        except Exception as e:
            return self._error_response(str(e))
    def clean_html(self,html):
        # 移除所有非法的 XML 字符（保留换行、制表符等）
        cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', html)
        return cleaned
    def _save_book_content(self, task, book_title, content, is_final=False):
        """安全地保存书籍内容
        
        Args:
            task: 任务对象
            book_title: 书籍标题
            content: 要保存的内容
            is_final: 是否是最终保存
        """
        try:
            # 生成文件名
            filename = f"{datetime.now().strftime('%Y_%m_%d_%H_%M')}_{book_title}"
            temp_file = os.path.join(task['config']['download_path'], f"{filename}.tmp")
            final_file = os.path.join(task['config']['download_path'], f"{filename}.docx")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(final_file), exist_ok=True)
            content = self.clean_html(content)
            # 写入临时文件
            with open(temp_file, 'wb') as doc_file:
                doc_file.write(html2docx(content, book_title).getvalue())
            
            # 如果是最终保存，重命名为正式文件
            if is_final:
                if os.path.exists(final_file):
                    os.remove(final_file)
                os.rename(temp_file, final_file)
                return final_file
            return temp_file
                
        except Exception as e:
            task['outputs'].append({
                'type': 'error',
                'message': f"保存失败: {str(e)}"
            })
            raise e


    def _run_download_task(self, task_id):
        """执行下载任务"""
        task = self.tasks[task_id]
        browser = None
        try:
            # 更新状态为运行中
            task['status'] = 'running'
            task['progress'] = 0
            task['total_books'] = len(task['config']['urls'])
            task['completed_books'] = 0
            
            # 获取Chrome配置
            co = ChromiumOptions()
            co.set_user_data_path(task['config']['chrome_config']['user_data_dir'])
            co.set_local_port(task['config']['chrome_config']['port'])
            
            browser = Chromium(addr_or_opts=co)
            tab = browser.latest_tab
            
            rule = task['config']['rule']
            print(task["config"])
            # 处理每个URL
            for url in task['config']['urls']:
                if task['status'] == 'stopped':
                    break
                    
                try:
                    # 检查Chrome是否还在运行
                    if not self._is_chrome_alive(browser):
                        task['status'] = 'stopped'
                        task['outputs'].append({
                            'type': 'warning',
                            'message': "Chrome浏览器已关闭，任务自动停止"
                        })
                        return  # 直接返回，终止任务

                    # 获取小说信息，添加重试机制
                    max_retries = 3
                    retry_count = 0
                    success = False
                    
                    while retry_count < max_retries and not success:
                        try:
                            # 访问页面并等待加载
                            tab.get(url)
                            # 等待页面基本元素加载完成（最多等待5秒）
                            wait_start = time.time()
                            loaded = False
                            while time.time() - wait_start < 5:
                                try:
                                    # 尝试获取标题元素
                                    title_elements = tab.eles("xpath:" + rule["book_title_rule"])
                                    if title_elements:
                                        loaded = True
                                        break
                                    time.sleep(0.5)
                                except:
                                    pass
                            
                            if not loaded:
                                raise Exception("页面加载超时")
                            
                            # 获取标题
                            book_title = title_elements[0].texts()[0].strip()
                            if not book_title:
                                raise Exception("无法获取书籍标题")
                            
                            # 获取简介（非必需，最多等待2秒）
                            brief_elements = None
                            wait_start = time.time()
                            while time.time() - wait_start < 2:
                                try:
                                    brief_elements = tab.eles("xpath:" + rule["description_rule"])
                                    if brief_elements:
                                        break
                                    time.sleep(0.5)
                                except:
                                    pass
                            
                            book_brief = brief_elements[0].inner_html.strip() if brief_elements else "暂无简介"
                            
                            # 获取章节列表（最多等待3秒）
                            directories = None
                            wait_start = time.time()
                            while time.time() - wait_start < 3:
                                try:
                                    directories = tab.eles("xpath:" + rule["directory_rule"])
                                    if directories and len(directories) > 0:
                                        break
                                    time.sleep(0.5)
                                except:
                                    pass
                            
                            if not directories:
                                raise Exception("无法获取章节列表")
                            
                            success = True
                            task['outputs'].append({
                                'type': 'info',
                                'message': f"成功获取信息: {book_title}"
                            })
                            
                        except Exception as e:
                            retry_count += 1
                            if retry_count < max_retries:
                                task['outputs'].append({
                                    'type': 'warning',
                                    'message': f"第 {retry_count} 次重试获取信息: {str(e)}"
                                })
                                time.sleep(1)  # 重试前短暂等待
                            else:
                                task['outputs'].append({
                                    'type': 'error',
                                    'message': f"获取信息失败: {str(e)}"
                                })
                                continue  # 跳过当前URL，处理下一个
                    
                    if not success:
                        continue  # 如果重试后仍然失败，跳过当前URL
                    
                    chapters = []
                    for chapter in directories:
                        try:
                            chapter_title = chapter.attr("text").strip()
                            chapter_url = chapter.attr("href").strip()
                            if chapter_title and chapter_url:
                                chapters.append({
                                    'title': chapter_title,
                                    'url': chapter_url,
                                    'status': 'pending'
                                })
                        except Exception as e:
                            task['outputs'].append({
                                'type': 'warning',
                                'message': f"解析章节信息失败: {str(e)}"
                            })
                            continue
                    
                    if not chapters:
                        task['outputs'].append({
                            'type': 'error',
                            'message': "未能获取到任何有效章节"
                        })
                        continue
                    
                    # 如果设置了章节数量限制，则只取指定数量的章节
                    if task['config'].get('chapter_count'):
                        chapters = chapters[:task['config']['chapter_count']]
                    
                    total_chapters = len(chapters)
                    doc_content = f"<h1>{book_title}</h1>\n\n<p>{book_brief}</p>\n\n"
                    last_save_time = time.time()
                    
                    # 下载章节
                    for i, chapter in enumerate(chapters):
                        if task['status'] == 'stopped':
                            break
                            
                        try:
                            # 再次检查Chrome是否还在运行
                            if not self._is_chrome_alive(browser):
                                task['status'] = 'stopped'
                                task['outputs'].append({
                                    'type': 'warning',
                                    'message': "Chrome浏览器已被关闭，任务自动停止"
                                })
                                return  # 直接返回，终止任务

                            # 下载章节内容
                            tab.get(chapter['url'])
                            
                            content_elements = tab.eles("xpath:" + rule["content_rule"])
                            html_content = content_elements[0].inner_html if content_elements else ""
                            
                            if rule.get('need_decrypt', False):
                                html_content = fanqie_decode_text(html_content)
                                
                            doc_content += f"<h2>{chapter['title']}</h2>\n\n{html_content}\n\n"
                            
                            chapter['status'] = 'completed'
                            chapter_progress = int((i + 1) / total_chapters * 100)
                            
                            # 更新总进度
                            task['progress'] = int((task['completed_books'] * 100 + chapter_progress) / task['total_books'])
                            
                            task['outputs'].append({
                                'type': 'info',
                                'message': f"[{book_title}] 下载完成: {chapter['title']} ({i + 1}/{total_chapters})"
                            })
                            
                            # 等待指定的间隔时间
                            if task['config'].get('interval_time'):
                                time.sleep(task['config']['interval_time'])
                                
                                # 在等待期间也检查Chrome状态
                                if not self._is_chrome_alive(browser):
                                    task['status'] = 'stopped'
                                    task['outputs'].append({
                                        'type': 'warning',
                                        'message': "Chrome浏览器已被关闭，任务自动停止"
                                    })
                                    return  # 直接返回，终止任务

                        except Exception as e:
                            chapter['status'] = 'failed'
                            chapter['error'] = str(e)
                            task['outputs'].append({
                                'type': 'error',
                                'message': f"[{book_title}] 章节下载失败: {chapter['title']} - {str(e)}"
                            })
                    
                    # 每下载完一章就更新内容
                    doc_content += f"<h2>{chapter['title']}</h2>\n\n{html_content}\n\n"
                    
                    # 每5分钟或每20章保存一次临时文件
                    current_time = time.time()
                    if (current_time - last_save_time > 300) or (i > 0 and i % 20 == 0):
                        self._save_book_content(task, book_title, doc_content, is_final=False)
                        last_save_time = current_time
                        
                        task['outputs'].append({
                            'type': 'info',
                            'message': f"[{book_title}] 已自动保存当前进度"
                        })
                    
                    # 最终保存
                    if task['status'] != 'stopped':
                        final_file = self._save_book_content(task, book_title, doc_content, is_final=True)
                        task['completed_books'] += 1
                        task['outputs'].append({
                            'type': 'success',
                            'message': f"[{book_title}] 保存成功: {os.path.basename(final_file)}"
                        })
                    
                except Exception as e:
                    task['outputs'].append({
                        'type': 'error',
                        'message': f"下载失败: {url} - {str(e)}"
                    })
            
            # 更新最终状态
            if task['status'] != 'stopped':
                task['status'] = 'completed'
                task['progress'] = 100
                task['outputs'].append({
                    'type': 'success',
                    'message': f"任务完成，共下载 {task['completed_books']}/{task['total_books']} 本小说"
                })
                
        except Exception as e:
            task['status'] = 'failed'
            task['error'] = str(e)
            task['outputs'].append({
                'type': 'error',
                'message': f"任务失败: {str(e)}"
            })
            
        finally:
            if browser:
                browser.quit()

    def stop_task(self, task_id):
        """停止下载任务"""
        try:
            if task_id in self.tasks:
                self.tasks[task_id]['status'] = 'stopped'
                return self._success_response("停止任务成功。",True)
            return self._error_response('任务不在')
            
        except Exception as e:
            return self._error_response(str(e))

    def send_task_output(self, task_id, output):
        """发送任务输出到前端"""
        try:
            task = self.tasks.get(task_id)
            if task:
                task['outputs'].append(output)
            
            encoded_output = base64.b64encode(json.dumps(output).encode()).decode()
            import webview
            webview.window[0].evaluate_js(f"window.receiveTaskOutput('{task_id}', '{encoded_output}')")
            
        except Exception as e:
            print(f"发送任务输出失败: {str(e)}")
