import { ref, onMounted, onUpdated, onUnmounted, watch, computed, onBeforeUnmount, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useConfigStore } from '@/stores/config'
import { useBookStore } from '@/stores/book'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { ElMessage, ElMessageBox } from 'element-plus'
import { enableDragging } from "@/utils/draggable.js"
import { Close, Position, Top, Bottom, Microphone, VideoPlay, VideoPause, Setting, CaretRight, DataLine, Rank, Plus, Picture, PictureFilled, Loading, Check, Delete, Search, ArrowRight, ArrowLeft, FolderOpened, Document, Edit, More, CopyDocument } from '@element-plus/icons-vue'
import ContextMenu from '@/components/ContextMenu.vue'
import AIAssistantWindow from '@/components/AIAssistantWindow.vue'
import ContextMenuSettings from '@/components/ContextMenuSettings.vue'
import { debounce } from 'lodash-es'
import { PROMPT_PARAMS, AVAILABLE_PARAMS } from '@/constants/promptParams'
import EntityCreateWindow from '@/components/EntityCreateWindow.vue'
import SceneCreateWindow from '@/components/SceneCreateWindow.vue'
import FindReplacePanel from '@/components/FindReplacePanel.vue'
import { FindAndReplace } from '@/extensions/FindAndReplace'
import dayjs from 'dayjs'
import SearchPanel from '@/components/SearchPanel.vue';
import SearchConfigWindow from '@/components/SearchConfigWindow.vue';
import PunctuationPanel from '@/components/PunctuationPanel.vue';
import VirtualChapterList from '@/components/VirtualChapterList.vue';

import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { useAIProvidersStore } from '@/stores/aiProviders';

// 模型ID转换函数
const convertToUniqueModelId = (modelId) => {
  if (!modelId) return 'gpt-3.5-turbo';

  // 如果已经是新格式（包含冒号且不是模型ID本身包含的冒号），直接返回
  if (modelId.includes(':') && !modelId.startsWith('gpt-') && !modelId.startsWith('claude-')) {
    // 检查是否已经是 providerId:modelId 格式
    const parts = modelId.split(':');
    if (parts.length >= 2 && !isNaN(parts[0])) {
      return modelId; // 已经是新格式
    }
  }

  // 旧格式，需要转换
  const aiProvidersStore = useAIProvidersStore();
  const availableModels = aiProvidersStore.allAvailableModels;

  // 查找匹配的模型
  const matchingModel = availableModels.find(m => m.uniqueId && m.uniqueId.endsWith(':' + modelId));
  if (matchingModel) {
    console.log('转换模型ID:', modelId, '->', matchingModel.uniqueId);
    return matchingModel.uniqueId;
  }

  // 如果找不到匹配的模型，使用第一个可用模型
  if (availableModels.length > 0) {
    console.log('使用默认模型:', availableModels[0].uniqueId);
    return availableModels[0].uniqueId;
  }

  // 最后的备选方案
  return 'gpt-3.5-turbo';
};


// 自定义扩展，用于处理粘贴事件
const CustomPasteHandler = Extension.create({
  name: 'customPasteHandler',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('customPasteHandler'),
        props: {
          handlePaste(view, event) {
            // 获取剪贴板数据
            const clipboardData = event.clipboardData;
            
            // 仅处理纯文本粘贴
            if (clipboardData && clipboardData.types.includes('text/plain')) {
              const content = clipboardData.getData('text/plain');
              
              // 如果有内容才处理
              if (content && content.trim()) {
                // 检查内容是否包含段落分隔（如空行）
                const hasParagraphBreaks = /\n\s*\n/.test(content);
                
                // 如果不包含段落分隔，让编辑器使用默认粘贴行为
                if (!hasParagraphBreaks) {
                  return false;
                }
                
                try {
                  // 阻止默认粘贴行为
                  event.preventDefault();
                  
                  // 智能处理粘贴的文本内容
                  let paragraphs;

                  // 检查是否是单行文本（没有换行符）
                  if (!content.includes('\n')) {
                    // 单行文本直接作为一个段落
                    paragraphs = [content.trim()];
                  } else {
                    // 多行文本：按空行分割成段落
                    paragraphs = content.split(/\n\s*\n/)
                      .map(p => p.trim())
                      .filter(p => p)
                      .map(p => {
                        // 统一换行符
                        let processed = p.replace(/\r\n/g, '\n');

                        // 检查是否有段首缩进（中文全角空格或英文空格）
                        const hasIndent = /^[\s\u3000]{2,}/.test(processed);

                        // 检查是否是诗歌或特殊格式（短行且有明显的行结构）
                        const lines = processed.split('\n');
                        const isPoetryLike = lines.length > 2 && lines.every(line => line.trim().length < 50);

                        if (isPoetryLike) {
                          // 诗歌格式：保留换行结构，但用<br>标签
                          processed = lines.map(line => line.trim()).join('<br>');
                        } else if (hasIndent) {
                          // 如果已有段首缩进，保留原有格式，段落内换行转为空格
                          const firstLine = lines[0];
                          const restLines = lines.slice(1).join(' ').trim();
                          processed = restLines ? `${firstLine} ${restLines}` : firstLine;
                        } else {
                          // 普通段落：换行转为空格
                          processed = processed.replace(/\n/g, ' ').trim();
                        }

                        return processed;
                      });
                  }
                  
                  // 创建HTML内容，根据内容类型使用不同样式
                  const html = paragraphs.map((p, index) => {
                    // 段落间添加空行（除了第一个段落）
                    const spacing = index > 0 ? '<p><br></p>' : '';

                    // 检查是否包含<br>标签（诗歌格式）
                    if (p.includes('<br>')) {
                      // 诗歌或特殊格式：不使用段首缩进，使用居中对齐
                      return `${spacing}<p style="line-height: 2; margin-bottom: 1.5em; text-align: left;">${p}</p>`;
                    } else {
                      // 普通段落：使用段首缩进
                      const hasExistingIndent = /^[\s\u3000]{2,}/.test(p);
                      const style = hasExistingIndent
                        ? "line-height: 1.8; margin-bottom: 1em;"
                        : "text-indent: 2em; line-height: 1.8; margin-bottom: 1em;";
                      return `${spacing}<p style="${style}">${p}</p>`;
                    }
                  }).join('');

                  // 获取当前可见的编辑器实例
                  const editorInstance = window.editorInstance || editor.value;
                  
                  // 如果有有效的编辑器实例
                  if (editorInstance && editorInstance.commands) {
                    // 如果有选中文本，先删除
                    if (!view.state.selection.empty) {
                      editorInstance.commands.deleteSelection();
                    }
                    
                    // 插入HTML内容
                    editorInstance.commands.insertContent(html, {
                      parseOptions: {
                        preserveWhitespace: false
                      }
                    });
                    
                    return true;
                  } else {
                    // 如果没有找到编辑器实例，使用备用方法
                    if (!view.state.selection.empty) {
                      const tr = view.state.tr.deleteSelection();
                      view.dispatch(tr);
                    }
                    
                    // 基本的段落插入
                    let tr = view.state.tr;
                    let pos = tr.selection.from;
                    
                    paragraphs.forEach((p, index) => {
                      tr = tr.insertText(p, pos);
                      pos += p.length;
                      
                      if (index < paragraphs.length - 1) {
                        tr = tr.insertText('\n\n', pos);
                        pos += 2;
                      }
                    });
                    
                    view.dispatch(tr);
                    return true;
                  }
                } catch (error) {
                  console.error('粘贴处理出错:', error);
                  return false; // 出错时让编辑器默认处理
                }
              }
            }
            
            // 返回false让编辑器默认处理
            return false;
          }
        }
      })
    ];
  }
});






const ttsServiceAvailable = ref(true)

// 配置store
const configStore = useConfigStore()
const editorSettings = computed(() => configStore.editor)

// 添加新的方法来处理字体颜色变化
const handleFontColorChange = (newColor) => {
  if (!newColor) return

  // 确保recentFontColors数组存在
  if (!editorSettings.value.recentFontColors) {
    editorSettings.value.recentFontColors = []
  }

  // 如果颜色已经在列表中，先移除它
  const colorIndex = editorSettings.value.recentFontColors.indexOf(newColor)
  if (colorIndex > -1) {
    editorSettings.value.recentFontColors.splice(colorIndex, 1)
  }

  // 将新颜色添加到列表开头
  editorSettings.value.recentFontColors.unshift(newColor)

  // 保持最近颜色列表在合理长度，只保留最近的10个颜色
  if (editorSettings.value.recentFontColors.length > 10) {
    editorSettings.value.recentFontColors = editorSettings.value.recentFontColors.slice(0, 10)
  }

  // 更新配置 - 使用防抖函数
  debouncedUpdateConfig({...editorSettings.value})
}

// 背景图片数据
const backgroundImageData = ref('')
const lastLoadedBgImage = ref('') // 缓存上次加载的背景图片路径

// 加载背景图片数据
const loadBackgroundImage = async () => {
  const currentBgImage = editorSettings.value?.bgImage || ''
  const bgEnabled = editorSettings.value?.bgEnabled || false

  // 如果背景未启用，清空背景数据
  if (!bgEnabled) {
    backgroundImageData.value = ''
    lastLoadedBgImage.value = ''
    return
  }

  // 如果没有背景图片路径，清空背景数据
  if (!currentBgImage) {
    backgroundImageData.value = ''
    lastLoadedBgImage.value = ''
    return
  }

  // 如果是相同的背景图片，跳过加载
  if (currentBgImage === lastLoadedBgImage.value && backgroundImageData.value) {
    console.log('背景图片已缓存，跳过加载:', currentBgImage)
    return
  }

  console.log('加载背景图片:', currentBgImage)
  try {
    const result = await window.pywebview.api.get_background_image_data(currentBgImage)
    const response = typeof result === 'string' ? JSON.parse(result) : result
    if (response.status === 'success') {
      backgroundImageData.value = response.data  // 保存base64编码的图片数据
      lastLoadedBgImage.value = currentBgImage   // 缓存当前加载的图片路径
    } else {
      console.error('加载背景图片失败:', response.message)
      backgroundImageData.value = ''
      lastLoadedBgImage.value = ''
    }
  } catch (error) {
    console.error('加载背景图片失败:', error)
    backgroundImageData.value = ''
    lastLoadedBgImage.value = ''
  }
}

// 监听背景图片设置变化 - 使用防抖避免频繁加载
const debouncedLoadBackground = debounce(loadBackgroundImage, 500)

// 监听背景图片关键设置变化
watch(
    () => [editorSettings.value?.bgEnabled, editorSettings.value?.bgImage],
    (newVal, oldVal) => {
      // 只有在值真正发生变化时才触发加载
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        debouncedLoadBackground()
      }
    },
    { immediate: true, deep: false }
)

// 路由相关
const route = useRoute()
const router = useRouter()
const bookId = route.params.id

// 书籍信息
const bookInfo = ref({})
const volumes = ref([])
const currentVolume = ref(null)
const currentChapter = ref({})
const currentChapterId = ref('')
const totalWords = ref(0)

// 编辑器相关
const editor = ref(null)
const isFullscreen = ref(false)
const showFindReplace = ref(false)
const showPunctuationPanel = ref(false) // 控制标点符号面板显示/隐藏
const sidebarCollapsed = ref(false) // 控制左侧目录展开/收起
const autoSaveIntervalRef = ref(null) // 自动保存定时器引用

// 新建卷相关
const showNewVolumeDialog = ref(false)
const newVolumeForm = ref({
  title: ''
})

// 新建章节相关
const showNewChapterDialog = ref(false)
const newChapterForm = ref({
  volumeId: '',
  title: ''
})

// 背景设置相关
const showBackgroundSettings = ref(false)

// 排序设置相关
const showSortSettings = ref(false)

// 右键菜单配置相关
const showContextMenuSettings = ref(false)



// 对话框状态
const showFontSettings = ref(false)

// TTS相关
const ttsConfig = ref({
  voice: 'zh-CN-XiaoxiaoNeural',
  rate: 0,
  volume: 0,
  pitch: 0
})
const voices = ref([])
const showTTSSettings = ref(false)
const ttsLoading = ref(false)
const voicesLoading = ref(false)
const ttsError = ref(null)
const audioContext = ref(null)
const audioQueue = ref([])
const isPlaying = ref(false)
// 
// 新增：实体相关状态
const showEntityDialog = ref(false)
const templateList = ref([])
const entityForm = ref({
  template_id: '',  // 修改这里：templateId -> template_id
  name: '',
  description: '',
  dimensions: {}
})
const currentTemplate = ref(null)

// 新增：加载模板列表
const loadTemplates = async () => {
  try {
    const response = await window.pywebview.api.book_controller.get_templates(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      templateList.value = result.data || []
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败：' + error.message)
  }
}

// 新增：处理模板选择
const handleTemplateSelect = (templateId) => {
  currentTemplate.value = templateList.value.find(t => t.id === templateId)
  if (currentTemplate.value) {
    // 初始化维度字段
    entityForm.value.dimensions = currentTemplate.value.dimensions.reduce((acc, dim) => {
      acc[dim.name] = ''
      return acc
    }, {})
  }
}

// 新增：保存实体
const saveEntity = async () => {
  try {
    if (!entityForm.value.template_id) {  // 修改这里：templateId -> template_id
      ElMessage.error('请选择模板')
      return
    }
    if (!entityForm.value.name) {
      ElMessage.error('请输入实体名称')
      return
    }

    // 将空维度设置为"未设定"
    if (currentTemplate.value) {
      currentTemplate.value.dimensions.forEach(dim => {
        if (!entityForm.value.dimensions[dim.name] || entityForm.value.dimensions[dim.name].trim() === '') {
          entityForm.value.dimensions[dim.name] = '未设定'
        }
      })
    }

    const response = await window.pywebview.api.book_controller.save_entity({
      ...entityForm.value,
      book_id: bookId
    })
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      ElMessage.success('创建成功')
      showEntityDialog.value = false
      // 重置表单
      entityForm.value = {
        template_id: '',  // 修改这里：templateId -> template_id
        name: '',
        description: '',
        dimensions: {}
      }
      currentTemplate.value = null
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存实体失败:', error)
    ElMessage.error('保存失败：' + error.message)
  }
}

// 加载TTS配置
async function loadTTSConfig() {
  try {
    voicesLoading.value = true
    let result;
    
    try {
      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时，请检查网络连接')), 10000); // 10秒超时
      });
      
      // 创建实际的API调用Promise
      const apiPromise = window.pywebview.api.get_voices();
      
      // 使用Promise.race竞争模式，哪个先完成就返回哪个
      result = await Promise.race([apiPromise, timeoutPromise]);
    } catch (apiError) {
      console.error('获取语音列表失败:', apiError);
      // 错误信息处理
      let errorMsg = '网络连接问题';
      if (typeof apiError.message === 'string') {
        if (apiError.message.includes('timeout')) {
          errorMsg = '请求超时，请检查网络连接';
        } else if (apiError.message.includes('network')) {
          errorMsg = '网络连接问题，请检查网络后重试';
        }
      }
      throw new Error(errorMsg);
    }
    
    const response = typeof result === 'string' ? JSON.parse(result) : result
    
    if (response.status === 'success') {
      voices.value = response.data || []
      // 如果成功获取到语音列表且不为空，则服务可用
      ttsServiceAvailable.value = voices.value.length > 0
      if (voices.value.length === 0) {
        console.warn('警告：语音列表为空')
        ttsServiceAvailable.value = false
      }
      
      // 尝试获取TTS配置
      try {
        const configResult = await window.pywebview.api.get_tts_config();
        const configResponse = typeof configResult === 'string' ? JSON.parse(configResult) : configResult;
        
        if (configResponse && configResponse.status === 'success') {
          // 更新本地配置
          ttsConfig.value = configResponse.data || ttsConfig.value;
          console.log('已加载TTS配置', ttsConfig.value);
        }
      } catch (configError) {
        console.warn('获取TTS默认配置失败，使用当前配置', configError);
      }
    } else {
      console.error('语音列表加载失败:', response.message)
      ttsServiceAvailable.value = false
      ElMessage.error('加载TTS配置失败: ' + (response.message || '请检查网络连接'))
    }
  } catch (e) {
    console.error('加载TTS配置失败:', e)
    ElMessage.error('加载TTS配置失败: ' + e.message)
    // 确保即使发生错误，也会设置一个空数组
    voices.value = []
    ttsServiceAvailable.value = false
  } finally {
    voicesLoading.value = false
  }
}

// 更新TTS配置 - 已经使用单独的configStore.updateTTSConfig方法，不需要防抖
async function updateTTSConfig(key, value) {
  try {
    ttsLoading.value = true
    
    // 如果正在播放，先停止
    if (isPlaying.value) {
      await stopPlayback()
      // 短暂延迟确保资源被释放
      await new Promise(resolve => setTimeout(resolve, 200))
    }
    
    let configValue = value
    
    // 只对数值类型的配置进行转换
    if (key !== 'voice') {
      configValue = parseInt(value) || 0
    }
    
    // 通过store更新配置 - 这是一个单独的更新方法，不会与编辑器设置产生循环依赖
    await configStore.updateTTSConfig({ [key]: configValue })
    ElMessage.success('配置已更新')
  } catch (e) {
    console.error('更新TTS配置失败:', e)
    ttsError.value = e.message
    ElMessage.error('更新TTS配置失败: ' + e.message)
  } finally {
    ttsLoading.value = false
  }
}

// 监听TTS设置弹窗显示状态
watch(showTTSSettings, async (newVal) => {
  if (newVal) {
    await loadTTSConfig()
  }
})

// 初始化
onMounted(async () => {
  // 初始化音频上下文
  audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
  
  // 注册TTS回调函数
  window.ttsAudioChunkCallback = (base64Data) => {
    try {
      const audioData = base64ToArrayBuffer(base64Data)
      audioQueue.value.push(audioData)
      if (!isPlaying.value) {
        playNextChunk()
      }
    } catch (e) {
      console.error('处理音频数据失败:', e)
      ttsError.value = e.message
      ttsLoading.value = false
    }
  }

  window.ttsAudioCompleteCallback = () => {
    console.log('TTS播放完成')
    ttsLoading.value = false
    isPlaying.value = false
  }

  window.ttsAudioErrorCallback = (error) => {
    console.error('TTS错误:', error)
    ttsError.value = error
    ttsLoading.value = false
    isPlaying.value = false
    ElMessage.error(error)
  }

  // 加载初始配置
  await loadTTSConfig()

  // 在onMounted中添加加载模板的调用
  await loadTemplates()

  updateTime()
  timer = setInterval(updateTime, 1000)
})

// 组件卸载时清理
onUnmounted(() => {
  if (audioContext.value) {
    audioContext.value.close()
  }
  // 清理window对象上的回调函数
  delete window.ttsAudioChunkCallback
  delete window.ttsAudioCompleteCallback
  delete window.ttsAudioErrorCallback

  if (timer) {
    clearInterval(timer)
    timer = null
  }
})



// 播放下一个音频块
async function playNextChunk() {
  if (audioQueue.value.length === 0 || !audioContext.value) {
    isPlaying.value = false
    ttsLoading.value = false
    return
  }

  isPlaying.value = true
  const audioData = audioQueue.value.shift()

  try {
    const audioBuffer = await audioContext.value.decodeAudioData(audioData)
    const source = audioContext.value.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.value.destination)
    source.onended = () => {
      if (audioQueue.value.length > 0) {
        playNextChunk()
      } else {
        isPlaying.value = false
        ttsLoading.value = false
      }
    }
    source.start(0)
  } catch (e) {
    console.error('音频解码失败:', e)
    isPlaying.value = false
    ttsLoading.value = false
    ttsError.value = e.message
  }
}

// 停止播放
function stopPlayback() {
  console.log('停止TTS播放')
  
  // 首先设置标志，防止新的音频块被处理
  isPlaying.value = false
  ttsLoading.value = false
  
  // 清空音频队列
  audioQueue.value = []
  
  // 停止所有正在播放的音频源
  if (audioContext.value) {
    try {
      // 暂停AudioContext
      audioContext.value.suspend().catch(err => {
        console.error('暂停AudioContext失败:', err)
      })
      
      // 尝试关闭旧的AudioContext并创建新的
      try {
        audioContext.value.close().then(() => {
          // 创建新的AudioContext
          try {
            audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
          } catch (e3) {
            console.error('创建新AudioContext失败:', e3)
          }
        }).catch(err => {
          console.error('关闭AudioContext失败:', err)
          // 如果关闭失败，仍然尝试创建新的
          try {
            audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
          } catch (e2) {
            console.error('创建新的AudioContext失败:', e2)
          }
        })
      } catch (e) {
        console.error('重置AudioContext失败:', e)
        // 出错时尝试直接创建新的AudioContext
        try {
          audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
        } catch (e2) {
          console.error('创建新的AudioContext失败:', e2)
        }
      }
    } catch (e) {
      console.error('停止音频失败:', e)
    }
  }

  // 调用后端停止流，使用try-catch包装确保不会阻塞UI
  try {
    // 包装在Promise.race中，添加超时处理
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve({ status: 'timeout', message: 'TTS停止操作超时，可能已经停止' })
      }, 2000) // 2秒超时
    })
    
    const stopPromise = window.pywebview.api.stop_tts_stream()
    
    Promise.race([stopPromise, timeoutPromise])
      .then(result => {
        if (result.status === 'timeout') {
          console.warn(result.message)
        } else {
          console.log('后端TTS流已停止')
        }
      })
      .catch(err => {
        console.error('停止后端TTS流失败:', err)
      })
  } catch (err) {
    console.error('调用后端停止TTS流方法失败:', err)
  }
}

// 菜单状态控制
const activeChapterId = ref(null)
const openedVolumeIds = ref([])

// 更新菜单状态
const updateMenuState = (volumeId, chapterId) => {
  activeChapterId.value = chapterId
  if (volumeId && !openedVolumeIds.value.includes(volumeId)) {
    openedVolumeIds.value = [volumeId]
  }
}

// 加载书籍信息
const loadBookInfo = async () => {
  try {
    await ensureConfigLoaded()
    const response = await window.pywebview.api.book_controller.get_book(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      bookInfo.value = result.data
      totalWords.value = result.data.word_count
      await loadVolumes()

      // 恢复上次编辑状态
      const lastState = editorSettings.value?.lastState
      if (lastState && lastState.bookId === bookId) {
        if (lastState.volumeId && lastState.chapterId) {
          // 更新菜单状态
          updateMenuState(lastState.volumeId, lastState.chapterId)
          await selectChapter(lastState.volumeId, lastState.chapterId)
        }
      }
    }
  } catch (error) {
    console.error('加载书籍信息失败:', error)
    ElMessage.error('加载书籍信息失败')
  }
}

// 分页加载相关状态
const volumesLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(50)
const totalVolumes = ref(0)
const hasMoreVolumes = ref(true)

// 加载卷和章节列表（支持分页）
const loadVolumes = async (page = 1, append = false) => {
  try {
    volumesLoading.value = true

    const response = await window.pywebview.api.book_controller.get_volumes(
      bookId,
      page,
      pageSize.value,
      true // include_chapters
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      const data = result.data

      if (append && page > 1) {
        // 追加数据（用于无限滚动）
        volumes.value = [...volumes.value, ...data.volumes]
      } else {
        // 替换数据（用于初始加载或刷新）
        volumes.value = data.volumes || data // 兼容旧格式
      }

      // 更新分页信息
      if (data.total !== undefined) {
        totalVolumes.value = data.total
        currentPage.value = data.page
        hasMoreVolumes.value = data.page < data.total_pages
      } else {
        // 兼容旧格式
        hasMoreVolumes.value = false
      }

      // 如果当前没有选中的卷，并且有卷存在，则选中第一个卷
      if (!currentVolume.value && volumes.value.length > 0) {
        currentVolume.value = volumes.value[0]
      }
    }
  } catch (error) {
    console.error('加载卷列表失败:', error)
    ElMessage.error('加载卷列表失败')
  } finally {
    volumesLoading.value = false
  }
}

// 加载更多卷（无限滚动）
const loadMoreVolumes = async () => {
  if (!hasMoreVolumes.value || volumesLoading.value) {
    return
  }

  await loadVolumes(currentPage.value + 1, true)
}

// 确保配置已加载
const ensureConfigLoaded = async () => {
  if (!configStore.loaded) {
    await configStore.loadConfig()
  }
}

// 当僵尸

// 监听路由变化时保存状态
watch(route, async () => {
  if (currentChapter.value?.id) {
    await saveLastEditState()
  }
})

// 在组件卸载前保存状态
onBeforeUnmount(async () => {
  // 禁止再弹出密码验证窗口
  allowPasswordPrompt.value = false;
  
  // 清除密码验证状态
  passwordVerifiedForBook.value = null;
  
  // 取消所有正在进行的保存操作
  if (debouncedSave && typeof debouncedSave.cancel === 'function') {
    debouncedSave.cancel();
  }
  
  try {
    // 如果有未保存内容，尝试执行一次同步保存，但不强制密码验证
    if (
      currentChapter.value?.id &&
      editor.value &&
      editor.value.getHTML() !== lastSavedContent.value
    ) {
      // 仅当内容已修改时保存
      console.log('组件卸载前保存最终内容');
      
      // 同步保存内容，禁用密码验证弹窗
      const bookStore = useBookStore();
      if (bookStore.isBookEncrypted(bookId) && !bookStore.getBookPassword(bookId)) {
        // 如果是加密书籍但没有缓存密码，不执行保存
        console.log('加密书籍无可用密码，跳过卸载前保存');
      } else {
        // 有密码或非加密书籍，执行最后一次保存
        const finalContent = editor.value.getHTML();
        const wordCount = finalContent.replace(/<[^>]+>/g, '').length;
        
        await window.pywebview.api.book_controller.update_chapter(
          bookId,
          currentVolume.value.id,
          currentChapter.value.id,
          {
            content: finalContent,
            word_count: wordCount,
            title: currentChapter.value.title,
            save_timestamp: Date.now()
          }
        );
      }
    }
  } catch (error) {
    console.error('卸载前保存失败:', error);
    // 忽略错误，继续卸载流程
  }
  
  // 保存最后编辑状态
  if (currentChapter.value?.id) {
    await saveLastEditState();
  }
  
  // 确保在返回写作页面时重新加载书籍列表
  await useBookStore().loadBooks();
})

// 处理新建卷对话框打开事件 - 恢复自动聚焦功能
const handleNewVolumeDialogOpened = () => {
  nextTick(() => {
    volumeTitleInput.value?.input?.focus()
  })
}

// 处理新建章节对话框打开事件 - 恢复自动聚焦功能
const handleNewChapterDialogOpened = () => {
  nextTick(() => {
    chapterTitleInput.value?.input?.focus()
  })
}

// 创建新卷
const createNewVolume = () => {
  // 确保newVolumeForm是一个响应式对象
  newVolumeForm.value = {
    title: ''
  }
  showNewVolumeDialog.value = true
}

// 确认创建新卷
const confirmCreateVolume = async () => {
  if (!newVolumeForm.value.title) {
    ElMessage.warning('请输入卷标题')
    return
  }

  try {
    const response = await window.pywebview.api.book_controller.create_volume(
        bookId,
        newVolumeForm.value
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('创建成功')
      showNewVolumeDialog.value = false
      await loadVolumes()
      // 设置当前卷
      currentVolume.value = result.data
      // 展开新创建的卷
      updateMenuState(result.data.id, null)
    } else {
      ElMessage.error(result.message || '创建卷失败')
    }
  } catch (error) {
    console.error('创建卷失败:', error)
    ElMessage.error('创建卷失败')
  }
}

// 获取推荐的章节标题
const getRecommendedChapterTitle = (volumeId) => {
  const volume = volumes.value.find(v =>
      v.chapters?.find(c => c.id === currentChapter.value.id)
  )
  if (!volume || !volume.chapters) return '第一章'

  const chapters = volume.chapters
  if (chapters.length === 0) return '第一章'

  // 从现有章节标题中提取数字
  const numbers = chapters.map(chapter => {
    const match = chapter.title.match(/第([一二三四五六七八九十百千万\d]+)章/)
    if (match) {
      // 如果是中文数字，转换为阿拉伯数字
      const chineseNum = match[1]
      if (/^\d+$/.test(chineseNum)) {
        return parseInt(chineseNum)
      } else {
        // 改进的中文数字转换
        const chineseToNumber = (chinese) => {
          const numbers = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '百': 100, '千': 1000, '万': 10000
          }

          if (chinese === '十') return 10
          if (chinese.startsWith('十')) {
            return 10 + (numbers[chinese.charAt(1)] || 0)
          }
          if (chinese.includes('十')) {
            const parts = chinese.split('十')
            const before = numbers[parts[0]] || 0
            const after = numbers[parts[1]] || 0
            return before * 10 + after
          }
          return numbers[chinese] || 0
        }
        return chineseToNumber(chineseNum)
      }
    }
    return 0
  }).filter(num => num > 0)

  if (numbers.length === 0) return '第一章'

  // 找到最大的章节号
  const maxNumber = Math.max(...numbers)

  // 改进的数字转中文函数
  const numberToChinese = (num) => {
    const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
    if (num <= 10) return chineseNums[num]
    if (num < 20) return '十' + (num % 10 === 0 ? '' : chineseNums[num % 10])
    if (num < 100) {
      const tens = Math.floor(num / 10)
      const ones = num % 10
      return chineseNums[tens] + '十' + (ones === 0 ? '' : chineseNums[ones])
    }
    return num.toString() // 大于100的数字直接使用阿拉伯数字
  }

  // 返回下一章的标题
  return `第${numberToChinese(maxNumber + 1)}章`
}

// 创建新章节
const createNewChapter = () => {
  if (!currentVolume.value) {
    ElMessage.warning('请先选择一个卷')
    return
  }
  
  // 确保newChapterForm是一个响应式对象
  newChapterForm.value = {
    volumeId: currentVolume.value.id,
    title: getRecommendedChapterTitle(currentVolume.value.id)
  }
  showNewChapterDialog.value = true
}

// 确认创建新章节
const confirmCreateChapter = async () => {
  if (!newChapterForm.value.title.trim()) {
    ElMessage.warning('章节标题不能为空')
    return
  }

  try {
    const response = await window.pywebview.api.book_controller.create_chapter(
        bookId,
        newChapterForm.value.volumeId,
        {
          title: newChapterForm.value.title.trim(),
          content: ''
        }
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('创建成功')
      showNewChapterDialog.value = false
      await loadVolumes()
      // 自动打开新创建的章节
      await router.push({
        query: {
          ...route.query,
          volumeId: newChapterForm.value.volumeId,
          chapterId: result.data.id
        }
      })
    } else {
      ElMessage.error(result.message || '创建失败')
    }
  } catch (error) {
    console.error('创建失败:', error)
    ElMessage.error('创建失败')
  }
}

// 处理卷的操作
const handleVolumeCommand = async (command, volume) => {
  if (command === 'rename') {
    const { value } = await ElMessageBox.prompt('请输入新的卷标题', '重命名卷', {
      inputValue: volume.title,
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    if (value) {
      try {
        const response = await window.pywebview.api.book_controller.update_volume(
            bookId,
            volume.id,
            { ...volume, title: value }
        )
        const result = typeof response === 'string' ? JSON.parse(response) : response
        if (result.status === 'success') {
          ElMessage.success('重命名成功')
          loadVolumes()
        } else {
          ElMessage.error(result.message || '重命名失败')
        }
      } catch (error) {
        console.error('重命名失败:', error)
        ElMessage.error('重命名失败')
      }
    }
  } else if (command === 'delete') {
    if (volume.chapters && volume.chapters.length > 0) {
      ElMessage.warning('请先删除卷内的所有章节')
      return
    }

    try {
      await ElMessageBox.confirm('确定要删除这个卷吗？', '删除卷', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const response = await window.pywebview.api.book_controller.delete_volume(bookId, volume.id)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      if (result.status === 'success') {
        ElMessage.success('删除成功')
        loadVolumes()
      } else {
        ElMessage.error(result.message || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }
}

// 处理章节的操作
const handleChapterCommand = async (command, volumeId, chapter) => {
  if (command === 'rename') {
    const { value } = await ElMessageBox.prompt('请输入新的章节标题', '重命名章节', {
      inputValue: chapter.title,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValidator: (value) => {
        if (!value.trim()) {
          return '章节标题不能为空'
        }
        return true
      }
    })

    if (value) {
      try {
        const newTitle = value.trim()
        const response = await window.pywebview.api.book_controller.update_chapter(
          bookId,
          volumeId,
          chapter.id,
          { ...chapter, title: newTitle }
        )
        const result = typeof response === 'string' ? JSON.parse(response) : response
        if (result.status === 'success') {
          // 立即更新当前章节标题
          chapter.title = newTitle
          if (currentChapter.value?.id === chapter.id) {
            currentChapter.value.title = newTitle
            // 如果重命名的是当前正在编辑的章节，立即保存内容
            await saveChapter()
          }
          ElMessage.success('重命名成功')
          // 异步加载完整列表
          loadVolumes()
        } else {
          ElMessage.error(result.message || '重命名失败')
        }
      } catch (error) {
        console.error('重命名失败:', error)
        ElMessage.error('重命名失败')
      }
    }
  } else if (command === 'delete') {
    try {
      await ElMessageBox.confirm('确定要删除这个章节吗？', '删除章节', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const response = await window.pywebview.api.book_controller.delete_chapter(
          bookId,
          volumeId,
          chapter.id
      )
      const result = typeof response === 'string' ? JSON.parse(response) : response
      if (result.status === 'success') {
        // 如果删除的是当前正在编辑的章节
        if (currentChapter.value?.id === chapter.id) {
          // 先清空编辑器
          editor.value?.commands.setContent('')
          currentChapter.value = null

          // 获取当前卷的所有章节
          const volume = volumes.value.find(v => v.id === volumeId)
          if (volume && volume.chapters && volume.chapters.length > 0) {
            // 找到被删除章节的前一个或后一个章节
            const chapterIndex = volume.chapters.findIndex(c => c.id === chapter.id)
            let nextChapter
            if (chapterIndex > 0) {
              // 优先选择前一个章节
              nextChapter = volume.chapters[chapterIndex - 1]
            } else if (volume.chapters.length > 1) {
              // 如果是第一个章节，选择下一个章节
              nextChapter = volume.chapters[1]
            }

            // 如果找到了其他章节，自动打开它
            if (nextChapter) {
              await router.replace({
                query: {
                  ...route.query,
                  volumeId,
                  chapterId: nextChapter.id
                }
              })
              // 加载新章节内容
              await selectChapter(volumeId, nextChapter.id)
            } else {
              // 如果没有其他章节，清除路由参数
              await router.replace({
                query: {
                  ...route.query,
                  chapterId: undefined
                }
              })
            }
          }
        }
        await loadVolumes()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(result.message || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }
}

// 保存章节，添加showMessage参数控制是否显示提示
const saveChapter = async (showMessage = false) => {
  if (!currentChapter.value?.id || !editor.value) return;
  
  // 获取当前内容
  const content = editor.value.getHTML();
  
  // 检查内容是否与上次保存的内容相同，如果相同则不需要保存
  if (content === lastSavedContent.value && !showMessage) {
    return;
  }
  
  // 如果不允许密码验证，则在加密书籍没有密码时跳过保存
  if (!allowPasswordPrompt.value) {
    const bookStore = useBookStore();
    if (bookStore.isBookEncrypted(bookId) && !bookStore.getBookPassword(bookId)) {
      console.log('禁止密码验证，且加密书籍无可用密码，跳过保存');
      return;
    }
  }
  
  // 获取当前版本号
  const currentVersion = ++lastSaveVersion.value;
  
  // 如果有另一个保存进行中，只增加版本号，不执行保存操作
  if (isSaving.value) {
    console.log(`保存请求已排队，版本: ${currentVersion}`);
    return;
  }
  
  try {
    isSaving.value = true;
    const wordCount = content.replace(/<[^>]+>/g, '').length;
    
    console.log(`开始保存章节, 版本: ${currentVersion}`);
    
    // 获取书籍存储
    const bookStore = useBookStore();
    
    // 检查书籍是否加密
    let contentToSave = content;
    if (bookStore.isBookEncrypted(bookId)) {
      try {
        // 如果当前书籍未经过密码验证，需要先验证密码
        if (passwordVerifiedForBook.value !== bookId) {
          // 只有允许密码验证时才弹窗
          if (!allowPasswordPrompt.value) {
            throw new Error('当前状态不允许密码验证');
          }
          
          const passwordResult = await bookStore.forcePromptBookPassword(bookId);
          if (!passwordResult) {
            // 用户取消了密码输入，停止保存
            throw new Error('需要密码才能保存加密书籍');
          }
          // 验证成功，更新已验证标记
          passwordVerifiedForBook.value = bookId;
        }
        
        // 加密章节内容
        contentToSave = await bookStore.encryptChapterContent(
          bookId,
          currentVolume.value.id,
          currentChapter.value.id,
          content
        );
      } catch (encryptError) {
        // 如果加密失败，清除密码验证状态
        passwordVerifiedForBook.value = null;
        throw encryptError;
      }
    }
    
    const response = await window.pywebview.api.book_controller.update_chapter(
        bookId,
        currentVolume.value.id,
        currentChapter.value.id,
        {
          content: contentToSave,
          word_count: wordCount,
          title: currentChapter.value.title,
          save_timestamp: Date.now() // 添加时间戳作为版本标识
        }
    );
    
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    // 检查这个响应是否来自最近的保存请求
    if (currentVersion < lastSaveVersion.value) {
      console.log(`忽略旧版本(${currentVersion})的保存响应`);
      return;
    }
    
    if (result.status === 'success') {
      if (showMessage) {
        ElMessage.success('保存成功');
      }
      // 更新最后保存内容状态 - 使用原始未加密内容，以便比较
      lastSavedContent.value = content;
    } else {
      ElMessage.error(result.message || '保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    
    // 只有在允许密码验证时才显示错误信息
    if (allowPasswordPrompt.value) {
      ElMessage.error('保存失败: ' + (error.message || '未知错误'));
    }
  } finally {
    isSaving.value = false;
    
    // 如果在保存过程中版本号已更新，且允许密码验证，重新保存
    if (currentVersion < lastSaveVersion.value && allowPasswordPrompt.value) {
      console.log(`检测到更新的内容，重新保存(版本: ${lastSaveVersion.value})`);
      nextTick(() => saveChapter(false));
    }
  }
};

// 保存章节标题
const saveChapterTitle = async (volumeId, chapter) => {
  // 如果标题为空或者只包含空格，不进行保存
  const trimmedTitle = chapter.title.trim()
  if (!trimmedTitle || trimmedTitle === '未命名章节') {
    chapter.title = '未命名章节'
    chapter.isEditing = false
    return
  }

  try {
    const response = await window.pywebview.api.book_controller.update_chapter(
        bookId,
        volumeId,
        chapter.id,
        { ...chapter, title: chapter.title.trim() }
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('保存成功')
      loadVolumes()
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    chapter.isEditing = false
  }
}

// 更新章节标题
const updateChapterTitle = async () => {
  if (!currentChapter.value?.id) return

  // 果标题为空或者只包含空格，使用"未命名章节"
  if (!currentChapter.value.title?.trim()) {
    currentChapter.value.title = '未命名章节'
  }

  try {
    // 查找当前章节所属的卷
    const volume = volumes.value.find(v =>
        v.chapters?.find(c => c.id === currentChapter.value.id)
    )
    if (!volume) return

    const response = await window.pywebview.api.book_controller.update_chapter(
        bookId,
        volume.id,
        currentChapter.value.id,
        { ...currentChapter.value }
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('保存成功')
      loadVolumes()
    } else {
      ElMessage.error(result.message || '保存失败')
      // 如果保存失败，恢复为原标题
      const chapter = volumes.value
          .find(v => v.chapters?.find(c => c.id === currentChapter.value.id))
          ?.chapters?.find(c => c.id === currentChapter.value.id)
      if (chapter) {
        currentChapter.value.title = chapter.title
      }
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
    // 如果保存失败，恢复为原标题
    const chapter = volumes.value
        .find(v => v.chapters?.find(c => c.id === currentChapter.value.id))
        ?.chapters?.find(c => c.id === currentChapter.value.id)
    if (chapter) {
      currentChapter.value.title = chapter.title
    }
  }
}

// 初始化编辑器
const initEditor = async () => {
  await ensureConfigLoaded()

  // 初始化背景设置
  if (editorSettings.value.bgEnabled === undefined) {
    editorSettings.value.bgEnabled = false
  }
  if (editorSettings.value.bgOpacity === undefined) {
    editorSettings.value.bgOpacity = 100
  }
  if (editorSettings.value.bgImage === undefined) {
    editorSettings.value.bgImage = ''
  }
  // 初始化段落间距设置
  if (editorSettings.value.paragraphSpace === undefined) {
    editorSettings.value.paragraphSpace = false
  }
  // 初始化最近使用的字体颜色
  if (editorSettings.value.recentFontColors === undefined) {
    editorSettings.value.recentFontColors = []
  }

  // 注意：不在这里调用 loadBackgroundImage，让 watch 监听器处理背景图片加载

  // 初始化编辑器实例
  editor.value = new Editor({

    extensions: [
      StarterKit, 
      CustomPasteHandler,
      FindAndReplace.configure({
        findClass: 'find-result',
        findActiveClass: 'find-result-active',
      })
    ],
    content: '',
    autofocus: "start",
    editorProps: {
      attributes: {
        spellcheck: 'false'
      },
      handleDOMEvents: {
        contextmenu: (view, event) => {
          event.preventDefault()
          handleContextMenu(event)
          return true
        },
        keydown: (view, event) => {
          // Ctrl + F 快捷键
          if (event.ctrlKey && !event.shiftKey && event.key === 'f') {
            event.preventDefault()
            toggleFindReplace()
            return true
          }
          
          // Ctrl + Shift + F 快捷键
          if (event.ctrlKey && event.shiftKey && event.key === 'F') {
            event.preventDefault()
            contextMenuVisible.value = true
            return true
          }
          
          // 编辑器内部不再处理ESC键，由全局事件处理器统一处理
          // 这样可以避免事件处理的冲突
          
          return false
        }
      },
      scrollThreshold: { top: 40, bottom: window.innerHeight / 3 },
      scrollMargin: { bottom: window.innerHeight / 3 }
    },
    onUpdate: ({ editor }) => {
      // 更新基本字数统计（总是需要的，用于状态栏显示）
      updateWritingStats()
      
      // 只有在写作统计面板打开时才更新相关统计数据
      // (新的统计系统会在updateWritingStats中自动处理)
      
      // 自动保存
      debouncedSave()
    },
    // https://tiptap.dev/docs/editor/api/events

  })
}

// 滚动优化函数
const optimizeScrolling = () => {
  // 节流函数
  const throttle = (func, limit) => {
    let inThrottle
    return function() {
      const args = arguments
      const context = this
      if (!inThrottle) {
        func.apply(context, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  

  // 优化ProseMirror编辑器的滚动
  const proseMirror = document.querySelector('.ProseMirror')
  if (proseMirror) {
    // 为ProseMirror添加滚动优化
    proseMirror.style.scrollbarWidth = 'thin' // Firefox滚动条优化
  }
}

// 生命周期钩子
onMounted(async () => {
  // 先加载配置
  await ensureConfigLoaded()
  // 注意：不在这里调用 loadBackgroundImage，让 watch 监听器和 initEditor 处理

  // 再初始化其他UI
  enableDragging(".editor-toolbar .drag-area")
  // 添加全局键盘事件监听器，确保在整个页面上都能响应ESC键
  window.addEventListener('keydown', handleKeydown)
  loadBookInfo()
  loadVolumes()
  initEditor()

  // 优化滚动性能
  nextTick(() => {
    optimizeScrolling()
  })

  // 修改定时保存的代码
  const autoSaveInterval = setInterval(() => saveChapter(false), 1000 * 30)

  // 将定时器保存到外部作用域，以便在组件卸载时清理
  autoSaveIntervalRef.value = autoSaveInterval

  // 在onMounted中添加加载模板的调用
  await loadTemplates()

  // 初始检查TTS服务
  try {
    await loadTTSConfig()
  } catch (e) {
    console.error('初始TTS服务检查失败:', e)
    ttsServiceAvailable.value = false
  }
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
  if (editor.value) {
    editor.value.destroy()
  }
  // 清理自动保存定时器
  if (autoSaveIntervalRef.value) {
    clearInterval(autoSaveIntervalRef.value)
    autoSaveIntervalRef.value = null
  }
  // 确保在组件卸载时取消所有未执行的防抖保存
  if (debouncedSave && typeof debouncedSave.cancel === 'function') {
    debouncedSave.cancel()
  }
  // if (debouncedUpdateStats && typeof debouncedUpdateStats.cancel === 'function') {
  //   debouncedUpdateStats.cancel()
  // }
})

// 编辑器样式控制
const editorStyleControl = {
  // 获取编辑器样式对象
  getStyle() {
    const style = {
      '--editor-font-family': editorSettings.value.fontFamily,
      '--editor-font-size': `${editorSettings.value.fontSize}px`,
      '--editor-line-height': editorSettings.value.lineHeight,
      '--editor-content-width': `${editorSettings.value.contentWidth}%`,
      '--editor-theme': editorSettings.value.theme,
      '--editor-font-color': editorSettings.value.fontColor || '#303133', // 添加字体颜色变量
    }
    return style
  },

  // 获取编辑器类名
  getClass() {
    return {
      'auto-spacing': editorSettings.value.paragraphSpace,
      'dark-theme': editorSettings.value.theme === 'dark',
      'light-theme': editorSettings.value.theme === 'light'
    }
  },

  // 更新编辑器样式
  updateStyle() {
    const editorElement = document.querySelector('.book-editor')
    if (!editorElement) return

    const styles = this.getStyle()
    Object.entries(styles).forEach(([key, value]) => {
      editorElement.style.setProperty(key, value)
    })
  }
}

// 计算属性：编辑器样式
const editorStyle = computed(() => editorStyleControl.getStyle())
const editorClass = computed(() => editorStyleControl.getClass())

// 计算属性：背景图片样式
const backgroundStyle = computed(() => {
  const bgEnabled = editorSettings.value?.bgEnabled
  const opacity = bgEnabled ? editorSettings.value.bgOpacity / 100 : 0

  return {
    backgroundImage: bgEnabled && backgroundImageData.value 
      ? `url('${backgroundImageData.value}')` 
      : 'none',
    opacity: opacity,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    zIndex: -999 // 确保背景在底层
  }
})

// 处理背景图片开关变化
const handleBgEnabledChange = async (enabled) => {
  if (!enabled) {
    // 关闭背景图片时，清除背景图片设置
    editorSettings.value.bgImage = ''
    // 使用防抖函数进行配置保存
    debouncedUpdateConfig({...editorSettings.value})
  }
}

// 选择本地背景图片
const selectBackground = async () => {
  try {
    const result = await window.pywebview.api.select_file_path()
    const response = typeof result === 'string' ? JSON.parse(result) : result
    
    if (response.status === 'success') {
      const filePath = response.data[0]
      
      // 处理背景图片
      const processResult = await window.pywebview.api.process_background_image(filePath)
      const processResponse = typeof processResult === 'string' ? JSON.parse(processResult) : processResult
      
      if (processResponse.status === 'success') {
        // 设置为当前背景 - 直接调用updateConfig而非单独调用loadBackgroundImage
        editorSettings.value.bgImage = processResponse.data
        
        // 直接调用防抖函数进行配置保存，避免多次触发监听器
        debouncedUpdateConfig({...editorSettings.value})
        
        // 重新加载历史背景列表
        await loadHistoryBackgrounds()
        ElMessage.success('背景图片设置成功')
      } else {
        throw new Error(processResponse.message)
      }
    }
  } catch (error) {
    console.error('选择背景图片失败:', error)
    ElMessage.error('选择背景图片失败：' + error.message)
  }
}

// 监听设置变化
watch(editorSettings, () => {
  editorStyleControl.updateStyle()
}, { deep: true })

// 右键菜单相关
const showContextMenu = ref(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const selectedText = ref('')
const showAIAssistant = ref(false)
const aiInitialPrompt = ref('')
const aiModel = ref('gpt-3.5-turbo')

// 处理右键菜单
const handleContextMenu = (event) => {
  event.preventDefault()

  // 禁用浏览器默认的右键菜单行为
  event.stopPropagation()

  selectedText.value = editor.value?.state.doc.textBetween(
      editor.value.state.selection.from,
      editor.value.state.selection.to
  )

  // 确保菜单位置不会超出视窗
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const menuWidth = 200 // 预估菜单宽度
  const menuHeight = 300 // 预估菜单高度

  let x = event.clientX
  let y = event.clientY

  // 调整X坐标，避免超出右边界
  if (x + menuWidth > viewportWidth) {
    x = viewportWidth - menuWidth - 10
  }

  // 调整Y坐标，避免超出下边界
  if (y + menuHeight > viewportHeight) {
    y = viewportHeight - menuHeight - 10
  }

  contextMenuX.value = Math.max(10, x)
  contextMenuY.value = Math.max(10, y)
  showContextMenu.value = true
}

// 处理右键菜单选择 - 添加错误处理
const handleMenuSelect = async (item) => {
  try {
    if (item.aiPrompt) {
      console.log("处理菜单项:", item);
      console.log("实体信息列表:", item.entityInfoList);
      
      let prompt = item.aiPrompt;
      const selectedText = editor.value?.state.doc.textBetween(
        editor.value.state.selection.from,
        editor.value.state.selection.to
      )

      // 使用统一的参数替换逻辑
      const context = {
        [PROMPT_PARAMS.ENTITY]: item.entityInfoList ? JSON.stringify(item.entityInfoList, null, 2) : '',
        [PROMPT_PARAMS.SELECTED_TEXT]: selectedText || '',
        [PROMPT_PARAMS.CONTEXT]: item.includeContext ? editor.value?.state.doc.textBetween(0, editor.value.state.doc.content.size) : ''
      }
      
      // 为每个实体添加单独的占位符
      if (item.entityInfoList && item.entityInfoList.length > 0) {
        item.entityInfoList.forEach(entity => {
          if (entity && entity.id) {
            context[`entity_${entity.id}`] = JSON.stringify(entity, null, 2);
          }
        });
      }

      // 替换所有标准参数
      AVAILABLE_PARAMS.forEach(param => {
        if (param && param.key) {
          const regex = new RegExp(`\\$\\{${param.key}\\}`, 'g');
          prompt = prompt.replace(regex, (context[param.key] || ''));
        }
      })
      
      // 单独处理实体ID特定的占位符 (这是核心修复部分)
      if (item.entityInfoList && item.entityInfoList.length > 0) {
        const allMatches = [];
        
        // 首先检查所有可能的实体占位符
        item.entityInfoList.forEach(entity => {
          if (entity && entity.id) {
            const entityId = entity.id;
            const pattern = `\\$\\{entity_${entityId}\\}`;
            const regex = new RegExp(pattern, 'g');
            
            let match;
            while ((match = regex.exec(prompt)) !== null) {
              allMatches.push({
                entityId,
                index: match.index,
                length: match[0].length,
                replacement: context[`entity_${entityId}`] || ''
              });
            }
          }
        });
        
        // 从后向前替换，避免位置偏移问题
        allMatches.sort((a, b) => b.index - a.index);
        
        allMatches.forEach(match => {
          const before = prompt.substring(0, match.index);
          const after = prompt.substring(match.index + match.length);
          prompt = before + match.replacement + after;
        });
      }

      // 安全地设置值
      nextTick(() => {
        aiInitialPrompt.value = prompt;

        // 转换模型ID格式
        const modelId = item.model || 'gpt-3.5-turbo';
        const convertedModelId = convertToUniqueModelId(modelId);
        aiModel.value = convertedModelId;

        console.log('右键菜单设置模型:', modelId, '->', convertedModelId);
        showAIAssistant.value = true;
        showContextMenu.value = false;
      });
    } else if (item.action === 'playText') {
      await playSelectedText();
      showContextMenu.value = false;
    }
  } catch (error) {
    console.error('处理菜单选择时出错:', error);
    ElMessage.error('处理菜单项时发生错误');
    // 确保关闭上下文菜单
    showContextMenu.value = false;
  }
}

// 处理右键菜单关闭
const handleContextMenuClose = () => {
  showContextMenu.value = false
}

// 切换全屏模式
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 处理ESC键切换最大化/最小化和Ctrl+S保存
const handleKeydown = (event) => {
  // 检查是否有弹窗打开，如果有则不处理某些快捷键
  const hasModalOpen = document.querySelector('.entity-create-window, .scene-create-window, .el-dialog__wrapper')

  // 处理ESC键 - 无论在哪里按下都能触发展开/收起聊天侧边栏
  if (event.key === 'Escape') {
    // 如果查找替换面板打开，则关闭它
    if (showFindReplace.value) {
      showFindReplace.value = false;
      event.preventDefault();
      return;
    }

    // 如果有弹窗打开，不处理ESC键（让弹窗自己处理）
    if (hasModalOpen) {
      return;
    }

    // 否则切换聊天侧边栏
    toggleChatSidebar();
    event.preventDefault();
    return;
  }

  // 添加Ctrl+S快捷键保存功能
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault() // 阻止浏览器默认保存网页行为
    saveChapter(true) // 传入true参数，表示显示保存成功提示
  }

  // 添加Ctrl+F快捷键打开查找面板
  if (event.ctrlKey && event.key === 'f') {
    event.preventDefault() // 阻止浏览器默认查找功能
    toggleFindReplace() // 打开/关闭查找面板
  }
}

// 焦点转移到编辑器
const focusEditor = () => {
  editor.value?.commands.focus("end")
}

// 返回上一页
const back = () => {
  // 禁止再弹出密码验证窗口
  allowPasswordPrompt.value = false;
  
  // 清除当前书籍的密码验证状态
  passwordVerifiedForBook.value = null;
  
  // 取消所有进行中的保存操作
  if (debouncedSave && typeof debouncedSave.cancel === 'function') {
    debouncedSave.cancel();
  }
  
  // 返回上一页
  router.push('/book/writing');
}

// 最大化应用
const maximizeApp = () => {
  window.pywebview.api.toggle_fullscreen()
}

// 保存最后编辑状态
const saveLastEditState = async () => {
  try {
  await configStore.updateConfigItem('editor.lastState', {
    bookId,
    volumeId: currentVolume.value?.id,
    chapterId: currentChapter.value?.id
  })
  } catch (error) {
    console.error('保存最后编辑状态失败:', error)
  }
}

// 监听设置变化并保存 - 使用防抖避免频繁更新
const debouncedUpdateConfig = debounce(async (newSettings) => {
  try {
  await configStore.updateConfigItem('editor', newSettings)
  } catch (error) {
    console.error('保存编辑器设置失败:', error)
  }
}, 300)

watch(editorSettings, (newSettings, oldSettings) => {
  // 避免在初始化时触发保存
  if (oldSettings) {
    debouncedUpdateConfig(newSettings)
  }
}, { deep: true })

// 排序后的卷列表
const sortedVolumes = computed(() => {
  if (!volumes.value) return []

  // 克隆数组以避免修改原数据
  const sorted = [...volumes.value]

  // 根据配置的排序方式排序
  sorted.sort((a, b) => {
    const order = editorSettings.value.volumeSortOrder === 'asc' ? 1 : -1
    return (a.created_at > b.created_at ? 1 : -1) * order
  })

  // 对每个卷的章节进行排序
  sorted.forEach(volume => {
    if (volume.chapters) {
      volume.chapters.sort((a, b) => {
        const order = editorSettings.value.chapterSortOrder === 'asc' ? 1 : -1
        return (a.created_at > b.created_at ? 1 : -1) * order
      })
    }
  })

  return sorted
})

// 获取排序后的章节列表
const getSortedChapters = (chapters) => {
  if (!chapters) return []
  return [...chapters].sort((a, b) => {
    const order = editorSettings.value.chapterSortOrder === 'asc' ? 1 : -1
    return (a.created_at > b.created_at ? 1 : -1) * order
  })
}

// 保存排序设置
const saveSortSettings = async () => {
  // 使用防抖函数保存配置
  debouncedUpdateConfig({...editorSettings.value})
  showSortSettings.value = false
  ElMessage.success('排序设置已保存')
}

// Base64转ArrayBuffer
const base64ToArrayBuffer = (base64) => {
  const binaryString = window.atob(base64)
  const length = binaryString.length
  const bytes = new Uint8Array(length)
  
  for (let i = 0; i < length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  
  return bytes.buffer
}

// 播放选中文本或从光标处开始播放
async function playSelectedText() {
  try {
    let text = ''
    
    // 使用editor的selection来获取选中的文本或光标位置
    if (editor.value) {
      const { from, to, empty } = editor.value.state.selection
      
      if (!empty) {
        // 有选中文本的情况
        text = editor.value.state.doc.textBetween(from, to).trim()
      } else {
        // 从光标位置到文档末尾
        const docEnd = editor.value.state.doc.content.size
        text = editor.value.state.doc.textBetween(from, docEnd).trim()
      }
    }

    if (!text) {
      ElMessage.warning('无法获取文本内容')
      return
    }
    
    // 停止之前的播放
    stopPlayback()
    
    ttsLoading.value = true
    audioQueue.value = []

    // 获取TTS配置并格式化参数
    const config = ttsConfig.value
    const rate = parseInt(config.rate) || 0
    const volume = parseInt(config.volume) || 0
    const pitch = parseInt(config.pitch) || 0
    
    // 开始流式播放，确保参数带有正负号
    const result = await window.pywebview.api.stream_speak_sync(
      text,
      config.voice,
      `${rate >= 0 ? '+' : ''}${rate}%`,
      `${volume >= 0 ? '+' : ''}${volume}%`,
      `${pitch >= 0 ? '+' : ''}${pitch}Hz`
    )
    const response = typeof result === 'string' ? JSON.parse(result) : response

    if (response.status !== 'success') {
      throw new Error(response.message)
    }
  } catch (e) {
    console.error('TTS播放失败:', e)
    ElMessage.error('TTS播放失败: ' + e.message)
    ttsLoading.value = false
  }
}

// 播放全文
async function playFullText() {
  try {
    let text = ''
    
    if (editor.value) {
      // 获取文档全部内容
      text = editor.value.state.doc.textBetween(0, editor.value.state.doc.content.size).trim()
    }

    if (!text) {
      ElMessage.warning('文档内容为空')
      return
    }
    
    // 停止之前的播放
    stopPlayback()
    
    ttsLoading.value = true
    audioQueue.value = []

    // 获取TTS配置并格式化参数
    const config = ttsConfig.value
    const rate = parseInt(config.rate) || 0
    const volume = parseInt(config.volume) || 0
    const pitch = parseInt(config.pitch) || 0
    
    // 开始流式播放，确保参数带有正负号
    const result = await window.pywebview.api.stream_speak_sync(
      text,
      config.voice,
      `${rate >= 0 ? '+' : ''}${rate}%`,
      `${volume >= 0 ? '+' : ''}${volume}%`,
      `${pitch >= 0 ? '+' : ''}${pitch}Hz`
    )
    const response = typeof result === 'string' ? JSON.parse(result) : response

    if (response.status !== 'success') {
      throw new Error(response.message)
    }
  } catch (e) {
    console.error('TTS播放失败:', e)
    ElMessage.error('TTS播放失败: ' + e.message)
    ttsLoading.value = false
  }
}

// 写作统计相关的状态
const showWritingStats = ref(false)
const chapterWords = ref(0)         // 当前章节字数
const punctuationCount = ref(0)     // 标点符号数
const panelPosition = ref({ x: 0, y: 0 })
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })

// 计算右下角位置的函数
const calculateBottomRightPosition = () => {
  const panelWidth = 180  // 面板实际宽度
  const panelHeight = 220 // 面板高度（进一步增加余量）
  const margin = 30       // 边距

  // 获取可视区域高度（考虑浏览器界面）
  const viewportHeight = window.innerHeight
  const viewportWidth = window.innerWidth

  // 确保面板完全在可视区域内
  const maxX = Math.max(margin, viewportWidth - panelWidth - margin)
  const maxY = Math.max(margin, viewportHeight - panelHeight - margin)

  return {
    x: maxX,
    y: maxY
  }
}

// 新的写作统计系统
const sessionStartTime = ref(null)  // 会话开始时间
const initialWordCount = ref(0)     // 进入编辑器时的初始字数
const minWordCount = ref(0)         // 会话期间的最低字数
const activeTypingTime = ref(0)     // 实际码字时间（毫秒）
const lastActivityTime = ref(null)  // 上次活动时间
const isCurrentlyTyping = ref(false) // 当前是否在输入状态
const typingTimeoutThreshold = 3000 // 3秒无操作视为停止输入
const statsUpdateTimer = ref(null)  // 统计更新定时器
// 计算当前时间
const currentTime = ref(new Date().toLocaleTimeString('zh-CN', {
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  hour12: false
}))

let timer = null

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})



// 格式化时间显示，显示时分秒
const formatTimeDetailed = (milliseconds) => {
  if (milliseconds <= 0) return '00:00:00'
  
  const hours = Math.floor(milliseconds / (1000 * 60 * 60))
  const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000)
  
  // 格式化为 HH:MM:SS
  const formattedHours = hours.toString().padStart(2, '0')
  const formattedMinutes = minutes.toString().padStart(2, '0')
  const formattedSeconds = seconds.toString().padStart(2, '0')
  
  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
}

// 计算本次码字（当前字数 - 最低字数）
const formattedSessionWords = computed(() => {
  if (!sessionStartTime.value) return '0'
  const currentTotal = chapterWords.value + punctuationCount.value
  const sessionWords = Math.max(0, currentTotal - minWordCount.value)
  return sessionWords.toString()
})

// 计算码字速率（字/小时）
const formattedTypingSpeed = computed(() => {
  if (!sessionStartTime.value || activeTypingTime.value === 0) return '0 字/小时'

  // 使用本次码字数计算速率
  const currentTotal = chapterWords.value + punctuationCount.value
  const sessionWords = Math.max(0, currentTotal - minWordCount.value)

  // 使用实际码字时间计算速率
  const activeTimeInHours = activeTypingTime.value / (1000 * 60 * 60)
  if (activeTimeInHours === 0) return '0 字/小时'

  const wordsPerHour = Math.round(sessionWords / activeTimeInHours)
  return `${wordsPerHour} 字/小时`
})

// 实时时间显示（用于强制更新）
const currentTimestamp = ref(Date.now())

// 计算码字时间（实时显示）
const formattedActiveTime = computed(() => {
  if (!sessionStartTime.value) return '00:00:00'

  // 计算当前的码字时间
  let currentActiveTime = activeTypingTime.value

  // 如果当前正在输入，需要加上从上次记录到现在的时间
  if (isCurrentlyTyping.value && lastActivityTime.value) {
    const timeSinceLastActivity = currentTimestamp.value - lastActivityTime.value
    if (timeSinceLastActivity < typingTimeoutThreshold) {
      currentActiveTime += timeSinceLastActivity
    }
  }

  return formatTimeDetailed(currentActiveTime)
})

// 计算空闲时间（实时显示）
const formattedIdleTime = computed(() => {
  if (!sessionStartTime.value) return '00:00:00'

  const totalTime = currentTimestamp.value - sessionStartTime.value

  // 计算当前的码字时间
  let currentActiveTime = activeTypingTime.value
  if (isCurrentlyTyping.value && lastActivityTime.value) {
    const timeSinceLastActivity = currentTimestamp.value - lastActivityTime.value
    if (timeSinceLastActivity < typingTimeoutThreshold) {
      currentActiveTime += timeSinceLastActivity
    }
  }

  const idleTime = Math.max(0, totalTime - currentActiveTime)
  return formatTimeDetailed(idleTime)
})

// 计算进度条
const typingTrendWidth = computed(() => {
  if (!sessionStartTime.value) return 0
  
  // 计算当前速率
  const currentWords = chapterWords.value + punctuationCount.value
  const wordsDelta = Math.max(0, currentWords - initialWordCount.value)
  
  // 使用码字时间计算速率
  const activeHours = activeTypingTime.value / (1000 * 60 * 60)
  const currentSpeed = activeHours > 0 ? Math.round(wordsDelta / activeHours) : 0
  
  // 使用3000字/小时作为参考速度
  const referenceSpeed = 3000
  return Math.min((currentSpeed / referenceSpeed) * 100, 100)
})



// 更新写作统计的方法
const updateWritingStats = (isInitial = false) => {
  if (!editor.value || !currentChapter.value) return

  // 获取当前内容
  const content = editor.value.getHTML()
  const text = content.replace(/<[^>]+>/g, '')

  // 计算字数（包括标点，但不包括空格和换行）
  const cleanText = text.replace(/\s/g, '')
  const currentWordCount = cleanText.length

  // 计算标点符号数
  const punctuationRegex = /[，。！？；：""''（）【】《》、]/g
  const punctuationMatches = cleanText.match(punctuationRegex)
  punctuationCount.value = punctuationMatches ? punctuationMatches.length : 0

  // 更新章节字数
  chapterWords.value = currentWordCount

  // 如果是初始化调用或者没有开始统计会话，不进行其他统计
  if (isInitial || !sessionStartTime.value) return

  // 更新最低字数（用于计算本次码字）
  const currentTotalWords = currentWordCount + punctuationCount.value
  if (currentTotalWords < minWordCount.value) {
    minWordCount.value = currentTotalWords
  }

  // 记录活动时间
  const now = Date.now()
  lastActivityTime.value = now

  // 如果当前不在输入状态，开始输入
  if (!isCurrentlyTyping.value) {
    isCurrentlyTyping.value = true
    startTypingSession()
  }
}

// 开始输入会话
const startTypingSession = () => {
  if (statsUpdateTimer.value) {
    clearTimeout(statsUpdateTimer.value)
  }

  // 设置定时器，在停止输入后一定时间停止计时
  statsUpdateTimer.value = setTimeout(() => {
    isCurrentlyTyping.value = false
  }, typingTimeoutThreshold)
}

// 更新活动时间
const updateActiveTime = () => {
  if (!sessionStartTime.value || !isCurrentlyTyping.value) return

  const now = Date.now()
  if (lastActivityTime.value) {
    const timeDiff = now - lastActivityTime.value
    // 只有在合理的时间间隔内才累加活动时间
    if (timeDiff < typingTimeoutThreshold) {
      activeTypingTime.value += timeDiff
    }
  }
  lastActivityTime.value = now
}

// 启动活动时间更新定时器
const startActiveTimeUpdater = () => {
  return setInterval(() => {
    updateActiveTime()
  }, 1000) // 每秒更新一次
}

// 启动实时时间更新定时器
const startRealtimeUpdater = () => {
  return setInterval(() => {
    currentTimestamp.value = Date.now()
  }, 1000) // 每秒更新一次时间戳，让计算属性重新计算
}

// 初始化统计
const initializeStats = () => {
  if (!editor.value || !currentChapter.value) return

  console.log('初始化写作统计...')

  // 设置面板默认位置为右下角
  panelPosition.value = calculateBottomRightPosition()

  // 重置所有计时器和计数
  const now = Date.now()
  sessionStartTime.value = now
  lastActivityTime.value = null
  activeTypingTime.value = 0
  isCurrentlyTyping.value = false

  // 清理旧的定时器
  if (statsUpdateTimer.value) {
    clearTimeout(statsUpdateTimer.value)
    statsUpdateTimer.value = null
  }

  // 初始化时统计当前章节字数
  updateWritingStats(true)

  // 记录初始字数和最低字数
  initialWordCount.value = chapterWords.value + punctuationCount.value
  minWordCount.value = initialWordCount.value

  // 启动活动时间更新定时器
  if (window.activeTimeUpdateInterval) {
    clearInterval(window.activeTimeUpdateInterval)
  }
  window.activeTimeUpdateInterval = startActiveTimeUpdater()

  // 启动实时时间更新定时器
  if (window.realtimeUpdateInterval) {
    clearInterval(window.realtimeUpdateInterval)
  }
  window.realtimeUpdateInterval = startRealtimeUpdater()

  // 添加窗口大小变化监听器
  const handleResize = () => {
    if (!isDragging.value) {
      // 只有在没有拖拽时才自动调整位置
      const newPosition = calculateBottomRightPosition()
      panelPosition.value = newPosition
    }
  }
  window.addEventListener('resize', handleResize)

  // 保存监听器引用以便清理
  window.writingStatsResizeHandler = handleResize
}

// 统计相关方法
const toggleWritingStats = () => {
  showWritingStats.value = !showWritingStats.value
  if (showWritingStats.value) {
    // 显示面板时初始化统计
    console.log('打开写作统计面板')
    initializeStats()
  } else {
    console.log('关闭写作统计面板')

    // 清理所有定时器
    if (statsUpdateTimer.value) {
      clearTimeout(statsUpdateTimer.value)
      statsUpdateTimer.value = null
    }

    if (window.activeTimeUpdateInterval) {
      clearInterval(window.activeTimeUpdateInterval)
      window.activeTimeUpdateInterval = null
    }

    if (window.realtimeUpdateInterval) {
      clearInterval(window.realtimeUpdateInterval)
      window.realtimeUpdateInterval = null
    }

    // 清理窗口大小变化监听器
    if (window.writingStatsResizeHandler) {
      window.removeEventListener('resize', window.writingStatsResizeHandler)
      window.writingStatsResizeHandler = null
    }

    // 重置统计数据
    sessionStartTime.value = null
    lastActivityTime.value = null
    activeTypingTime.value = 0
    isCurrentlyTyping.value = false

    // 保留总字数和标点符号数，但重置初始字数和最低字数
    initialWordCount.value = chapterWords.value + punctuationCount.value
    minWordCount.value = initialWordCount.value
  }
}

// 在组件卸载时清理统计数据和定时器
onUnmounted(() => {
  // 清理写作统计相关定时器
  
  // 清理定时更新的定时器
  if (window.writingStatsUpdateInterval) {
    clearInterval(window.writingStatsUpdateInterval)
    window.writingStatsUpdateInterval = null
  }
  
  // 重置所有统计数据
  sessionStartTime.value = null
  lastActivityTime.value = null
  activeTypingTime.value = 0
  isCurrentlyTyping.value = false
  minWordCount.value = 0
  
  // 清理选择监听器和更新监听器
  if (editor.value) {
    editor.value.off('selectionUpdate')
    editor.value.off('update')
  }
})

// 添加一个标记来跟踪当前会话中已验证密码的书籍
const passwordVerifiedForBook = ref(null);

// 修改selectChapter方法以支持解密
const selectChapter = async (volumeId, chapterId) => {
  try {
    // 先设置当前卷
    const volume = volumes.value.find(v => v.id === volumeId)
    if (!volume) {
      ElMessage.error('找不到指定的卷')
      return
    }
    currentVolume.value = volume

    const response = await window.pywebview.api.book_controller.get_chapter(bookId, volumeId, chapterId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      currentChapter.value = result.data
      
      // 获取书籍存储
      const bookStore = useBookStore();
      
      // 检查是否需要解密内容
      if (bookStore.isBookEncrypted(bookId) && currentChapter.value.content) {
        try {
          // 判断是否需要强制密码验证
          const needForceVerify = passwordVerifiedForBook.value !== bookId;
          
          // 解密章节内容 - 仅在首次进入书籍时强制验证
          const decryptedContent = await bookStore.decryptChapterContent(
            bookId,
            volumeId,
            chapterId,
            currentChapter.value.content,
            needForceVerify // 仅在首次加载时强制验证
          );
          
          // 如果成功解密，标记该书籍已验证密码
          passwordVerifiedForBook.value = bookId;
          
          // 使用解密后的内容
          currentChapter.value.content = decryptedContent;
        } catch (decryptError) {
          console.error('解密章节失败:', decryptError);
          ElMessage.error('解密章节失败: ' + (decryptError.message || '密码错误'));
          
          // 如果解密失败，清除会话中的密码和验证标记
          bookStore.clearBookPassword(bookId);
          passwordVerifiedForBook.value = null;
          
          // 显示空内容
          currentChapter.value.content = '<p>加密内容，请输入密码查看</p>';
          
          // 解密失败，退出加载流程
          return;
        }
      }
      
      // 将章节内容加载到编辑器
      editor.value?.commands.setContent(currentChapter.value.content || '')
      
      // 更新菜单状态
      updateMenuState(volumeId, chapterId)
      // 保存最后编辑状态
      await saveLastEditState()
      
      // 无论是否开启写作统计，都计算当前章节字数
      updateWritingStats(true)
      
      // 重置并初始化统计
      if (showWritingStats.value) {
        sessionStartTime.value = null
        initializeStats()
      }
      
      // 更新最后保存内容状态，用于后续比较
      if (editor.value) {
        lastSavedContent.value = editor.value.getHTML();
      }
    }
  } catch (error) {
    console.error('加载章节失败:', error)
    ElMessage.error('加载章节失败: ' + (error.message || '未知错误'))
  }
}

// 在组件卸载时清理统计数据
onUnmounted(() => {
  // 清理写作统计相关定时器
  if (statsUpdateTimer.value) {
    clearTimeout(statsUpdateTimer.value)
    statsUpdateTimer.value = null
  }

  if (window.activeTimeUpdateInterval) {
    clearInterval(window.activeTimeUpdateInterval)
    window.activeTimeUpdateInterval = null
  }

  if (window.realtimeUpdateInterval) {
    clearInterval(window.realtimeUpdateInterval)
    window.realtimeUpdateInterval = null
  }

  // 清理窗口大小变化监听器
  if (window.writingStatsResizeHandler) {
    window.removeEventListener('resize', window.writingStatsResizeHandler)
    window.writingStatsResizeHandler = null
  }

  // 重置统计数据
  sessionStartTime.value = null
  lastActivityTime.value = null
  activeTypingTime.value = 0
  isCurrentlyTyping.value = false
  minWordCount.value = 0
})

// 写作统计面板样式
const writingStatsPanelStyle = computed(() => ({
  transform: `translate(${panelPosition.value.x}px, ${panelPosition.value.y}px)`
}))

// 面板拖拽相关方法
const initDrag = (e) => {
  isDragging.value = true
  dragOffset.value = {
    x: e.clientX - panelPosition.value.x,
    y: e.clientY - panelPosition.value.y
  }
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

const handleDrag = (e) => {
  if (!isDragging.value) return
  panelPosition.value = {
    x: e.clientX - dragOffset.value.x,
    y: e.clientY - dragOffset.value.y
  }
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 计算字数统计
const formattedChapterWords = computed(() => {
  // 包含标点符号的总字数
  const total = chapterWords.value + punctuationCount.value
  return `${total} 字`
})



// 新增：实体创建相关
const showEntityCreate = ref(false)

// 添加处理方法
const handleEntityCreated = () => {
  // 移除了 showEntityCreate.value = false
  // 可以在这里添加其他需要的处理逻辑，比如提示创建成功等
  
}

// 新增：场景创建相关
const showSceneCreate = ref(false)

// 添加处理方法
const handleSceneCreated = () => {
  // 可以在这里添加其他需要的处理逻辑，比如提示创建成功等
}



// 添加选中文本字数计算
const selectedTextCount = ref(0)
// 添加当前段落字数计算
const currentParagraphCount = ref(0)

// 监听编辑器选择变化和光标位置变化
const setupSelectionListener = () => {
  if (!editor.value) return
  
  // 处理选择变化
  editor.value.on('selectionUpdate', ({ editor }) => {
    const { from, to } = editor.state.selection
    
    // 计算选中文本的字数
    if (from === to) {
      // 没有选中文本
      selectedTextCount.value = 0
      selectedText.value = '' // 清空选中文本
    } else {
      // 获取选中的文本
      const selectedTextContent = editor.state.doc.textBetween(from, to, ' ')
      // 更新选中文本内容  
      selectedText.value = selectedTextContent
      // 计算字数（不包括空格和换行）
      const cleanText = selectedTextContent.replace(/\s/g, '')
      // 处理连续标点符号：将连续的相同标点符号替换为单个标点符号
      const processedText = cleanText.replace(/([，。！？；：、,.!?;:])\1+/g, '$1')
      selectedTextCount.value = processedText.length
    }
    
    // 计算当前段落的字数
    updateCurrentParagraphCount(editor, from)
  })
  
  // 处理光标位置变化（即使没有选中文本）
  editor.value.on('update', ({ editor }) => {
    const { from } = editor.state.selection
    updateCurrentParagraphCount(editor, from)
  })
}

// 计算当前段落的字数
const updateCurrentParagraphCount = (editor, pos) => {
  if (!editor) return
  
  // 获取当前位置所在的节点
  const $pos = editor.state.doc.resolve(pos)
  
  // 获取当前段落节点
  const paragraph = $pos.parent
  
  // 如果不是段落节点，则返回
  if (paragraph.type.name !== 'paragraph') {
    currentParagraphCount.value = 0
    return
  }
  
  // 获取段落文本内容
  const paragraphText = paragraph.textContent
  
  // 计算字数（不包括空格和换行）
  const cleanText = paragraphText.replace(/\s/g, '')
  // 处理连续标点符号：将连续的相同标点符号替换为单个标点符号
  const processedText = cleanText.replace(/([，。！？；：、,.!?;:])\1+/g, '$1')
  currentParagraphCount.value = processedText.length
}

// 在编辑器初始化后设置选择监听器
watch(() => editor.value, (newEditor) => {
  if (newEditor) {
    setupSelectionListener()
  }
}, { immediate: true })

// 历史背景相关
const bgSelectionTab = ref('history')
const historyBackgrounds = ref([])
const loadingBackgrounds = ref(false)
const bgThumbnails = ref({}) // 用于存储缩略图数据

// 加载历史背景图片
const loadHistoryBackgrounds = async () => {
  try {
    loadingBackgrounds.value = true
    const result = await window.pywebview.api.get_background_images()
    const response = typeof result === 'string' ? JSON.parse(result) : result
    
    if (response.status === 'success') {
      historyBackgrounds.value = response.data || []
      // 为每张图片加载缩略图
      historyBackgrounds.value.forEach(bg => {
        loadBgThumbnail(bg.path)
      })
    } else {
      throw new Error(response.message)
    }
  } catch (error) {
    console.error('加载历史背景失败:', error)
    ElMessage.error('加载历史背景失败：' + error.message)
  } finally {
    loadingBackgrounds.value = false
  }
}

// 选择历史背景图片
const selectHistoryBackground = async (imagePath) => {
  editorSettings.value.bgImage = imagePath
  // 使用防抖函数进行配置保存，避免多次触发更新
  debouncedUpdateConfig({...editorSettings.value})
  ElMessage.success('背景图片设置成功')
}

// 格式化时间
const formatTime = (timestamp) => {
  return dayjs(timestamp * 1000).format('YYYY-MM-DD')
}

// 监听背景设置弹窗的打开状态
watch(showBackgroundSettings, async (newVal) => {
  if (newVal) {
    // 当弹窗打开时，加载历史背景
    await loadHistoryBackgrounds()
  }
})

// 加载背景图片缩略图
const loadBgThumbnail = async (path) => {
  if (bgThumbnails.value[path]) return // 已经加载过则跳过
  
  try {
    const result = await window.pywebview.api.get_background_image_data(path, true) // 传入true获取缩略图
    const response = typeof result === 'string' ? JSON.parse(result) : result
    if (response.status === 'success') {
      bgThumbnails.value[path] = response.data
    }
  } catch (error) {
    console.error('加载图片缩略图失败:', error, path)
  }
}

// 删除背景图片
const confirmDeleteBackground = async (bg) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张背景图片吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const result = await window.pywebview.api.delete_background_image(bg.path)
    const response = typeof result === 'string' ? JSON.parse(result) : result
    
    if (response.status === 'success') {
      ElMessage.success('背景图片删除成功')
      // 重新加载背景图片列表
      await loadHistoryBackgrounds()
    } else {
      ElMessage.error(response.message || '删除背景图片失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除背景图片失败:', error)
      ElMessage.error('删除背景图片失败')
    }
  }
}

// 在脚本中添加处理方法
// 插入AI内容到编辑器，这个不是自定义扩展中，直接拿到编辑器实例，然后插入内容
const insertAIContentToEditor = (content) => {
  if (!editor.value) return
  
  // 获取编辑器当前状态
  const { state, view } = editor.value
  
  // 如果有选中文本，则替换选中文本
  if (!state.selection.empty) {
    // 替换选中的文本
    editor.value.commands.deleteSelection()
  }
  
  // 处理AI返回的内容
  let processedContent
  
  // 检查内容是否包含HTML标签
  const containsHTML = /<[a-z][\s\S]*>/i.test(content)
  
  if (containsHTML) {
    // 如果包含HTML标签，直接使用
    processedContent = content
  } else {
    // 处理纯文本：按段落分割
    const paragraphs = content.split(/\n\s*\n/)
    
    // 将每个段落转换为带有文本缩进的HTML段落
    processedContent = paragraphs.map(p => {
      // 忽略空段落
      if (!p.trim()) return ''
      
      // 添加文本缩进样式
      return `<p >${p.trim()}</p>`
    }).join('')
  }
  
  // 在当前位置插入处理后的内容
  editor.value.commands.insertContent(processedContent, {
    parseOptions: {
      preserveWhitespace: false, // 不保留多余的空白字符
    }
  })
  
  // 聚焦编辑器
  editor.value.commands.focus()
  
  // 提示用户
  ElMessage.success('AI内容已插入到编辑器')
}



// // 添加自动保存状态指示变量（在响应式变量区域）
// const autoSaveIndicator = ref(false)

// 添加重试加载语音列表的方法
const retryLoadVoices = async () => {
  try {
    ElMessage.info('正在重新加载语音列表...')
    await loadTTSConfig()
    if (voices.value.length > 0) {
      ElMessage.success('语音列表加载成功')
      ttsServiceAvailable.value = true
    }
  } catch (e) {
    ElMessage.error('重新加载失败: ' + e.message)
    ttsServiceAvailable.value = false
  }
}

// 确保在 setup 中返回这个函数
// 在返回对象中添加 retryLoadVoices

// 配置创建卷和章节的对话框选项
const dialogOptions = {
  appendToBody: true, // 添加到body元素
  lockScroll: true,   // 锁定背景滚动
  closeOnClickModal: false // 点击外部不关闭
}

// 可以在组件setup中设置，或者直接在模板中使用
const createVolumeDialogProps = {
  appendToBody: true,
  destroyOnClose: true
}




// 如果想使用新名称，添加以下变量并与原变量同步
const showCreateVolumeDialog = ref(false)
const showCreateChapterDialog = ref(false)

// 同步原有变量和新变量
watch(showNewVolumeDialog, (val) => {
  showCreateVolumeDialog.value = val
})
watch(showCreateVolumeDialog, (val) => {
  showNewVolumeDialog.value = val
})
watch(showNewChapterDialog, (val) => {
  showCreateChapterDialog.value = val
})
watch(showCreateChapterDialog, (val) => {
  showNewChapterDialog.value = val
})

// 搜索相关状态
const searchQuery = ref('')
const searchResults = ref([])
const showSearchResults = ref(false)
const searchLoading = ref(false)
const searchPage = ref(1)
const searchPageSize = ref(20)
const hasMoreSearchResults = ref(true)

// 防抖搜索
const debouncedSearch = debounce(async (query) => {
  if (!query.trim()) {
    searchResults.value = []
    showSearchResults.value = false
    return
  }

  try {
    searchLoading.value = true
    searchPage.value = 1

    const response = await window.pywebview.api.book_controller.search_chapters(
      bookId,
      query,
      searchPage.value,
      searchPageSize.value
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      const data = result.data
      searchResults.value = data.results || []
      hasMoreSearchResults.value = data.page < data.total_pages
      showSearchResults.value = true
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败')
  } finally {
    searchLoading.value = false
  }
}, 300)

// 搜索处理
const handleSearch = () => {
  debouncedSearch(searchQuery.value)
}

// 加载更多搜索结果
const loadMoreSearchResults = async () => {
  if (!hasMoreSearchResults.value || searchLoading.value) {
    return
  }

  try {
    searchLoading.value = true
    searchPage.value += 1

    const response = await window.pywebview.api.book_controller.search_chapters(
      bookId,
      searchQuery.value,
      searchPage.value,
      searchPageSize.value
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      const data = result.data
      searchResults.value = [...searchResults.value, ...data.results]
      hasMoreSearchResults.value = data.page < data.total_pages
    }
  } catch (error) {
    console.error('加载更多搜索结果失败:', error)
  } finally {
    searchLoading.value = false
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  searchResults.value = []
  showSearchResults.value = false
}

// 确保在setup中返回这些新变量和方法

// 添加双击章节处理函数
const handleChapterDblClick = async (volumeId, chapter) => {
  try {
    const { value } = await ElMessageBox.prompt('请输入新的章节标题', '重命名章节', {
      inputValue: chapter.title,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValidator: (value) => {
        if (!value.trim()) {
          return '章节标题不能为空'
        }
        return true
      },
      beforeClose: (action, instance, done) => {
        if (action === 'confirm' && instance.inputValue.trim()) {
          done()
        } else if (action === 'cancel') {
          done()
        }
      }
    })

    if (value) {
      try {
        const newTitle = value.trim()
        const response = await window.pywebview.api.book_controller.update_chapter(
          bookId,
          volumeId,
          chapter.id,
          { ...chapter, title: newTitle }
        )
        const result = typeof response === 'string' ? JSON.parse(response) : response
        if (result.status === 'success') {
          // 立即更新当前章节标题
          chapter.title = newTitle
          if (currentChapter.value?.id === chapter.id) {
            currentChapter.value.title = newTitle
            // 如果重命名的是当前正在编辑的章节，立即保存内容
            await saveChapter()
          }
          ElMessage.success('重命名成功')
          // 异步加载完整列表
          loadVolumes()
        } else {
          ElMessage.error(result.message || '重命名失败')
        }
      } catch (error) {
        console.error('重命名失败:', error)
        ElMessage.error('重命名失败')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重命名失败:', error)
      ElMessage.error('重命名失败')
    }
  }
}

// 在现有代码中添加聊天侧边栏相关状态和方法

// 聊天侧边栏状态
const showChatSidebar = ref(false)

// 切换聊天侧边栏显示状态
const toggleChatSidebar = () => {
  showChatSidebar.value = !showChatSidebar.value
}

// 切换左侧目录显示/隐藏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 插入聊天内容到编辑器
const insertChatContentToEditor = (content) => {
  if (!content || !editor.value) return
  
  try {
    // 获取当前选区
    const { from, to } = editor.value.state.selection
    
    // 创建事务并插入内容
    editor.value.chain().focus().insertContentAt(from, content).run()
    
    // 提示用户
    ElMessage.success('已插入聊天内容到编辑器')
  } catch (error) {
    console.error('插入聊天内容失败:', error)
    ElMessage.error('插入聊天内容失败')
  }
}

// 确保在组件卸载时关闭聊天侧边栏
onUnmounted(() => {
  // 现有的卸载代码
  window.removeEventListener('keydown', handleKeydown)
  if (editor.value) {
    editor.value.destroy()
  }
  // 确保在组件卸载时取消所有未执行的防抖保存
  if (debouncedSave && typeof debouncedSave.cancel === 'function') {
    debouncedSave.cancel()
  }
  
  // 移除可能存在的全局样式
  document.documentElement.style.removeProperty('--chat-sidebar-width')
})

// 添加查找替换功能开关
const toggleFindReplace = () => {
  showFindReplace.value = !showFindReplace.value

  // 如果是打开面板，检查是否有选中文本
  if (showFindReplace.value && editor.value) {
    const { from, to, empty } = editor.value.state.selection

    // 如果有选中文本，将其传递给FindReplacePanel组件
    if (!empty) {
      const selectedText = editor.value.state.doc.textBetween(from, to)
      // 在下一个渲染周期中设置搜索词，确保面板已经初始化
      setTimeout(() => {
        if (editor.value?.commands) {
          editor.value.commands.setFindTerm(selectedText)
        }
      }, 10)
    }
  }
}

// 切换标点符号面板
const togglePunctuationPanel = () => {
  showPunctuationPanel.value = !showPunctuationPanel.value
}

// 处理标点符号插入
const handlePunctuationInsert = (symbol) => {
  // 标点符号已经在组件内部插入，这里只做额外的处理（如果需要）
  // 比如记录统计、触发自动保存等
  console.log('标点符号已插入:', symbol)
}

// 在文件顶部响应式变量定义区域添加这些变量
// 添加在其他ref变量附近
const isSaving = ref(false); // 保存锁定标志
const lastSaveVersion = ref(0); // 保存版本号
const lastSavedContent = ref(''); // 上次保存的内容

// 修改防抖保存函数
const debouncedSave = debounce(() => {
  // 如果没有正在进行的保存，则执行保存
  if (!isSaving.value) {
    saveChapter(false);
  } else {
    // 如果有保存进行中，只增加版本号，当前保存完成后会自动触发新的保存
    lastSaveVersion.value++;
    console.log(`内容已更改，版本增加到: ${lastSaveVersion.value}`);
  }
}, 1500); // 减少延迟时间，确保更及时保存

// 添加手动保存按钮处理函数
const manualSave = () => {
  // 取消任何待处理的防抖保存
  if (debouncedSave && typeof debouncedSave.cancel === 'function') {
    debouncedSave.cancel();
  }
  
  // 立即执行保存并显示消息
  saveChapter(true);
};

// 添加一个变量控制是否允许加密验证
const allowPasswordPrompt = ref(true);

// 搜索面板相关
const showSearchPanel = ref(false);
const showSearchConfig = ref(false);

// 控制搜索面板显示/隐藏
const toggleSearchPanel = () => {
  showSearchPanel.value = !showSearchPanel.value;
  if (showSearchPanel.value) {
    showChatSidebar.value = false; // 关闭聊天侧边栏
  }
};

// 处理搜索配置更新
const handleSearchConfigUpdated = () => {
  ElMessage.success('搜索配置已更新');
};

// 新的目录功能相关状态
const expandedVolumeIds = ref(new Set())
const draggingChapter = ref(null)

// 检查卷是否展开
const isVolumeExpanded = (volumeId) => {
  return expandedVolumeIds.value.has(volumeId)
}

// 切换卷的展开/收起状态
const toggleVolume = (volumeId) => {
  if (expandedVolumeIds.value.has(volumeId)) {
    expandedVolumeIds.value.delete(volumeId)
  } else {
    expandedVolumeIds.value.add(volumeId)
  }
  // 触发响应式更新
  expandedVolumeIds.value = new Set(expandedVolumeIds.value)
}

// 处理卷的右键菜单
const handleVolumeContextMenu = (event, volume) => {
  // 可以在这里添加卷的右键菜单逻辑
  console.log('卷右键菜单:', volume)
}

// 处理章节的右键菜单
const handleChapterContextMenu = (event, volumeId, chapter) => {
  // 可以在这里添加章节的右键菜单逻辑
  console.log('章节右键菜单:', chapter)
}

// 格式化字数显示
const formatWordCount = (count) => {
  if (!count) return '0'
  if (count < 1000) return count.toString()
  if (count < 10000) return (count / 1000).toFixed(1) + 'k'
  return (count / 10000).toFixed(1) + '万'
}

// 在指定卷中创建章节
const createChapterInVolume = (volumeId) => {
  const volume = volumes.value.find(v => v.id === volumeId)
  if (!volume) return

  currentVolume.value = volume
  newChapterForm.value = {
    volumeId: volumeId,
    title: getRecommendedChapterTitle(volumeId)
  }
  showNewChapterDialog.value = true
}

// 章节拖拽相关方法
const handleChapterDragStart = (event, chapter) => {
  draggingChapter.value = chapter.id
  event.dataTransfer.setData('text/plain', JSON.stringify({
    type: 'chapter',
    chapterId: chapter.id,
    sourceVolumeId: currentVolume.value?.id
  }))
}

const handleChapterDragOver = (event) => {
  event.preventDefault()
}

const handleChapterDrop = async (event, targetVolumeId, targetChapter) => {
  event.preventDefault()

  try {
    const data = JSON.parse(event.dataTransfer.getData('text/plain'))
    if (data.type !== 'chapter') return

    const { chapterId, sourceVolumeId } = data

    // 如果是同一个章节，不处理
    if (chapterId === targetChapter.id) return

    // 这里可以添加章节移动的逻辑
    console.log('移动章节:', {
      chapterId,
      sourceVolumeId,
      targetVolumeId,
      targetChapter: targetChapter.id
    })

    ElMessage.info('章节拖拽移动功能开发中...')
  } catch (error) {
    console.error('处理章节拖拽失败:', error)
  } finally {
    draggingChapter.value = null
  }
}

// 扩展handleVolumeCommand方法以支持新的命令
const handleVolumeCommandExtended = async (command, volume) => {
  if (command === 'add-chapter') {
    createChapterInVolume(volume.id)
    return
  }

  // 调用原有的处理方法
  await handleVolumeCommand(command, volume)
}

// 扩展handleChapterCommand方法以支持新的命令
const handleChapterCommandExtended = async (command, volumeId, chapter) => {
  if (command === 'duplicate') {
    try {
      // 复制章节的逻辑
      const response = await window.pywebview.api.book_controller.create_chapter(
        bookId,
        volumeId,
        {
          title: chapter.title + ' (副本)',
          content: chapter.content || ''
        }
      )
      const result = typeof response === 'string' ? JSON.parse(response) : response
      if (result.status === 'success') {
        ElMessage.success('章节复制成功')
        await loadVolumes()
      } else {
        ElMessage.error(result.message || '复制失败')
      }
    } catch (error) {
      console.error('复制章节失败:', error)
      ElMessage.error('复制章节失败')
    }
    return
  }

  if (command === 'move') {
    ElMessage.info('章节移动功能开发中...')
    return
  }

  // 调用原有的处理方法
  await handleChapterCommand(command, volumeId, chapter)
}

// 初始化时展开包含当前章节的卷
const initializeExpandedVolumes = () => {
  if (activeChapterId.value) {
    const volume = volumes.value.find(v =>
      v.chapters?.some(c => c.id === activeChapterId.value)
    )
    if (volume) {
      expandedVolumeIds.value.add(volume.id)
    }
  }
}

// 监听activeChapterId变化，自动展开对应的卷
watch(activeChapterId, (newChapterId) => {
  if (newChapterId) {
    const volume = volumes.value.find(v =>
      v.chapters?.some(c => c.id === newChapterId)
    )
    if (volume) {
      expandedVolumeIds.value.add(volume.id)
      expandedVolumeIds.value = new Set(expandedVolumeIds.value)
    }
  }
})

// 监听volumes变化，初始化展开状态
watch(volumes, () => {
  initializeExpandedVolumes()
}, { immediate: true })


