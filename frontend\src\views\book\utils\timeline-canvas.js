/**
 * 时间线Canvas管理类
 */
export default class TimelineCanvas {
  constructor(canvas, config = {}) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    
    // 默认配置
    this.config = {
      scale: 1,
      offsetX: 0,
      offsetY: 0,
      mainTrunkX: 80,
      branchSpacing: 200,
      lineWidth: 2,
      yearSpacing: 100,
      mainTrunkColor: '#409EFF',
      ...config
    };
    
    // 数据
    this.mainTrunkEvents = [];
    this.branchEvents = [];
    this.branches = [];
    
    // 时间刻度
    this.timeScale = {
      startYear: 2020,
      endYear: 2040,
      years: []
    };
    
    // 拖拽状态
    this.dragInfo = {
      isDragging: false,
      isPanning: false,
      currentNode: null,
      nodeType: null,
      startX: 0,
      startY: 0,
      offsetX: 0,
      offsetY: 0,
      originalX: 0,
      originalY: 0
    };
    
    // 鼠标悬停状态
    this.hoverInfo = {
      isHovering: false,
      node: null,
      nodeType: null
    };
    
    // 标记已初始化
    this.initialized = false;
    
    // 绑定事件处理函数以确保this指向正确
    this.handleMouseDown = this.handleMouseDown.bind(this);
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleMouseUp = this.handleMouseUp.bind(this);
    this.handleWheel = this.handleWheel.bind(this);
  }
  
  /**
   * 初始化Canvas
   */
  init() {
    if (this.initialized) return this;
    
    // 设置Canvas尺寸
    this.resizeCanvas();
    
    // 生成时间刻度
    this.generateTimeScale();
    
    // 绘制时间线
    this.draw();
    
    // 绑定事件监听器
    this.canvas.addEventListener('mousedown', this.handleMouseDown);
    this.canvas.addEventListener('mousemove', this.handleMouseMove);
    this.canvas.addEventListener('mouseup', this.handleMouseUp);
    this.canvas.addEventListener('mouseleave', this.handleMouseUp);
    this.canvas.addEventListener('wheel', this.handleWheel, { passive: false });
    
    // 初始化完成标记
    this.initialized = true;
    return this;
  }
  
  /**
   * 清理资源，移除事件监听器
   */
  destroy() {
    if (!this.initialized) return;
    
    // 移除事件监听器
    this.canvas.removeEventListener('mousedown', this.handleMouseDown);
    this.canvas.removeEventListener('mousemove', this.handleMouseMove);
    this.canvas.removeEventListener('mouseup', this.handleMouseUp);
    this.canvas.removeEventListener('mouseleave', this.handleMouseUp);
    this.canvas.removeEventListener('wheel', this.handleWheel);
    
    this.initialized = false;
  }
  
  /**
   * 调整Canvas尺寸
   */
  resizeCanvas() {
    if (!this.canvas) return;
    
    const parent = this.canvas.parentElement;
    if (!parent) return;
    
    const { width, height } = parent.getBoundingClientRect();
    this.canvas.width = width;
    
    // 根据时间范围设置高度
    const timeSpan = (this.timeScale.endYear - this.timeScale.startYear + 1);
    const minHeight = Math.max(
      height,
      timeSpan * this.config.yearSpacing + 200 // 额外空间用于滚动
    );
    
    this.canvas.height = minHeight;
    
    // 重新绘制
    this.draw();
  }
  
  /**
   * 生成时间刻度
   */
  generateTimeScale() {
    const { timeRange } = this.config;
    const startYear = timeRange?.start || this.timeScale.startYear;
    const endYear = timeRange?.end || this.timeScale.endYear;
    
    // 更新时间刻度范围
    this.timeScale.startYear = startYear;
    this.timeScale.endYear = endYear;
    
    const years = [];
    for (let i = startYear; i <= endYear; i++) {
      years.push({
        value: i,
        label: `${i}年`,
        position: this.calculateYPosition({ year: i, month: 1, day: 1 })
      });
    }
    this.timeScale.years = years;
    return years;
  }
  
  /**
   * 设置事件数据
   * @param {Object} data 包含mainTrunkEvents、branchEvents和branches的对象
   */
  setData(data) {
    if (data.mainTrunkEvents) this.mainTrunkEvents = data.mainTrunkEvents;
    if (data.branchEvents) this.branchEvents = data.branchEvents;
    if (data.branches) this.branches = data.branches;
    this.draw();
    return this;
  }
  
  /**
   * 设置配置项
   * @param {Object} config 配置对象
   */
  setConfig(config) {
    this.config = {
      ...this.config,
      ...config
    };
    this.draw();
    return this;
  }
  
  /**
   * 设置时间范围
   * @param {Object} timeScale 时间范围对象
   */
  setTimeScale(timeScale) {
    this.timeScale = {
      ...this.timeScale,
      ...timeScale
    };
    this.generateTimeScale();
    this.draw();
    return this;
  }
  
  /**
   * 绘制时间线
   */
  draw() {
    if (!this.ctx) return this;
    
    const { scale, offsetX, offsetY, lineWidth, mainTrunkX } = this.config;
    
    // 清除画布
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // 应用缩放和平移
    this.ctx.save();
    this.ctx.translate(offsetX, offsetY);
    this.ctx.scale(scale, scale);
    
    // 绘制年份标记（在主干线上）
    this.drawYearMarkers();
    
    // 绘制主干线
    this.ctx.beginPath();
    this.ctx.moveTo(mainTrunkX, 0);
    this.ctx.lineTo(mainTrunkX, this.canvas.height / scale);
    this.ctx.strokeStyle = this.config.mainTrunkColor;
    this.ctx.lineWidth = lineWidth;
    this.ctx.stroke();
    
    // 绘制树枝连接
    this.drawTreeBranches();
    
    // 绘制事件节点
    this.drawEvents();
    
    // 如果启用了调试模式，显示一些调试信息
    if (this.config.debug) {
      this.drawDebugInfo();
    }
    
    // 恢复上下文
    this.ctx.restore();
    
    return this;
  }
  
  /**
   * 绘制年份标记
   */
  drawYearMarkers() {
    if (!this.ctx) return this;
    
    const { mainTrunkX } = this.config;
    
    this.timeScale.years.forEach(year => {
      const y = year.position;
      
      // 绘制年份文字
      this.ctx.fillStyle = '#666';
      this.ctx.font = '14px Arial';
      this.ctx.textAlign = 'right';
      this.ctx.fillText(year.label, mainTrunkX - 10, y + 5);
      
      // 绘制小圆点标记
      this.ctx.beginPath();
      this.ctx.arc(mainTrunkX, y, 3, 0, Math.PI * 2);
      this.ctx.fillStyle = this.config.mainTrunkColor;
      this.ctx.fill();
      
      // 简单的横线标记年份
      this.ctx.beginPath();
      this.ctx.moveTo(mainTrunkX - 5, y);
      this.ctx.lineTo(mainTrunkX + 5, y);
      this.ctx.strokeStyle = this.config.mainTrunkColor;
      this.ctx.lineWidth = 1;
      this.ctx.stroke();
    });
    
    return this;
  }
  
  /**
   * 绘制树枝连接
   */
  drawTreeBranches() {
    if (!this.ctx) return this;
    
    const { mainTrunkX } = this.config;
    const branchSpacing = 180; // 调整分支间距
    
    // 先按照年份对分支事件进行分组，这样可以避免同一年的事件堆叠
    const eventsByYear = {};
    this.branchEvents.forEach(event => {
      const year = event.year || 2020;
      if (!eventsByYear[year]) {
        eventsByYear[year] = [];
      }
      eventsByYear[year].push(event);
    });
    
    // 为每个分支事件绘制树枝式连接线
    let eventIndex = 0;
    Object.keys(eventsByYear).forEach(year => {
      const eventsInYear = eventsByYear[year];
      
      // 在同一年中，平均分布事件
      eventsInYear.forEach((event, indexInYear) => {
        // 计算事件时间对应的Y轴位置
        const eventY = this.calculateYPosition({
          year: event.year,
          month: event.month || 1,
          day: event.day || 1
        });
        
        // 确定分支事件X坐标 - 交替放在主干左右两侧
        // 使用全局索引来决定左右，确保更好的分布
        const isLeft = eventIndex % 2 === 0;
        
        // 根据分支类型调整颜色和X位置
        const branchX = isLeft ? 
          mainTrunkX - branchSpacing : // 左侧
          mainTrunkX + branchSpacing;  // 右侧
        
        // 绘制连接线(树枝)
        this.ctx.beginPath();
        this.ctx.moveTo(mainTrunkX, eventY); // 从主干开始
        
        // 获取分支颜色
        const branchColor = this.getBranchColor(event.branchId);
        
        // 使用贝塞尔曲线绘制树枝 - 调整参数使其更像截图中的弧线
        const xDiff = branchX - mainTrunkX;
        
        // 控制点的位置调整
        // 对于不同的分支使用不同的控制点策略，创造多样化的曲线
        let controlPointX1, controlPointX2, controlPointY1, controlPointY2;
        
        if (Math.random() > 0.5) {
          // 向上弯曲的曲线
          controlPointX1 = mainTrunkX + (xDiff * 0.3);
          controlPointX2 = mainTrunkX + (xDiff * 0.7);
          controlPointY1 = eventY - Math.abs(xDiff * 0.4);
          controlPointY2 = eventY - Math.abs(xDiff * 0.2);
        } else {
          // 向下弯曲的曲线
          controlPointX1 = mainTrunkX + (xDiff * 0.3);
          controlPointX2 = mainTrunkX + (xDiff * 0.7);
          controlPointY1 = eventY + Math.abs(xDiff * 0.4);
          controlPointY2 = eventY + Math.abs(xDiff * 0.2);
        }
        
        // 绘制贝塞尔曲线
        this.ctx.bezierCurveTo(
          controlPointX1, controlPointY1, // 第一个控制点
          controlPointX2, controlPointY2, // 第二个控制点
          branchX, eventY              // 终点(分支事件位置)
        );
        
        // 设置线条样式
        this.ctx.strokeStyle = branchColor;
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // 在主干线上的连接点
        this.ctx.beginPath();
        this.ctx.arc(mainTrunkX, eventY, 4, 0, Math.PI * 2);
        this.ctx.fillStyle = branchColor;
        this.ctx.fill();
        
        // 保存分支事件的位置信息，供后续绘制事件节点使用
        event.renderPosition = { 
          x: branchX, 
          y: eventY,
          isLeft,
          // 保存曲线控制点，用于后续拖动时更新连接线
          controlPoints: {
            x1: controlPointX1,
            y1: controlPointY1,
            x2: controlPointX2,
            y2: controlPointY2
          }
        };
        
        eventIndex++;
      });
    });
    
    return this;
  }
  
  /**
   * 获取分支颜色
   * @param {string} branchId 分支ID
   * @returns {string} 颜色代码
   */
  getBranchColor(branchId) {
    const branch = this.branches.find(b => b.id === branchId);
    return branch ? branch.color : '#67C23A';
  }
  
  /**
   * 绘制事件节点
   */
  drawEvents() {
    if (!this.ctx) return this;
    
    // 绘制主干事件
    this.mainTrunkEvents.forEach(event => {
      const x = this.config.mainTrunkX;
      const y = this.calculateYPosition({
        year: event.year,
        month: event.month || 1,
        day: event.day || 1
      });
      
      // 绘制节点
      this.ctx.beginPath();
      this.ctx.arc(x, y, 10, 0, Math.PI * 2);
      this.ctx.fillStyle = this.config.mainTrunkColor;
      this.ctx.fill();
      
      // 绘制文字标签
      this.ctx.fillStyle = 'black';
      this.ctx.font = '14px Arial';
      this.ctx.textAlign = 'left';
      
      // 添加标题
      this.ctx.fillText(event.title, x + 15, y);
      
      // 添加日期（小一号字体）
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = '#666'; 
      this.ctx.fillText(`${event.year}年${event.month}月${event.day}日`, x + 15, y + 20);
    });
    
    // 绘制分支事件
    this.branchEvents.forEach(event => {
      if (!event.renderPosition) return; // 确保位置已计算
      
      const { x, y, isLeft } = event.renderPosition;
      
      // 绘制节点
      this.ctx.beginPath();
      this.ctx.arc(x, y, 8, 0, Math.PI * 2);
      this.ctx.fillStyle = this.getBranchColor(event.branchId);
      this.ctx.fill();
      
      // 绘制文字标签
      this.ctx.fillStyle = 'black';
      this.ctx.font = '14px Arial';
      
      // 根据位置确定文本对齐方式
      this.ctx.textAlign = isLeft ? 'right' : 'left';
      const textX = isLeft ? x - 12 : x + 12;
      
      // 添加标题
      this.ctx.fillText(event.title, textX, y);
      
      // 添加日期（小一号字体）
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = '#666';
      this.ctx.fillText(`${event.year}年${event.month}月${event.day}日`, textX, y + 20);
    });
    
    return this;
  }
  
  /**
   * 绘制调试信息
   */
  drawDebugInfo() {
    // 显示主干事件信息
    this.ctx.fillStyle = 'rgba(0,0,0,0.7)';
    this.ctx.fillRect(10, 10, 300, 130);
    this.ctx.fillStyle = 'white';
    this.ctx.font = '12px monospace';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`主干事件: ${this.mainTrunkEvents.length}个`, 20, 30);
    this.ctx.fillText(`分支: ${this.branches.length}个`, 20, 50);
    this.ctx.fillText(`分支事件: ${this.branchEvents.length}个`, 20, 70);
    this.ctx.fillText(`缩放比例: ${this.config.scale.toFixed(2)}`, 20, 90);
    this.ctx.fillText(`偏移: (${this.config.offsetX.toFixed(0)}, ${this.config.offsetY.toFixed(0)})`, 20, 110);
  }
  
  /**
   * 处理鼠标按下事件
   * @param {MouseEvent} e 鼠标事件
   */
  handleMouseDown(e) {
    if (e.button !== 0) return; // 仅处理左键点击
    
    const rect = this.canvas.getBoundingClientRect();
    const mouseX = (e.clientX - rect.left - this.config.offsetX) / this.config.scale;
    const mouseY = (e.clientY - rect.top - this.config.offsetY) / this.config.scale;
    
    // 检查是否点击了主干事件
    const mainTrunkEvent = this.findEventAtPosition(mouseX, mouseY, 'mainTrunk');
    if (mainTrunkEvent) {
      this.dragInfo.isDragging = true;
      this.dragInfo.currentNode = mainTrunkEvent;
      this.dragInfo.nodeType = 'mainTrunk';
      this.dragInfo.startX = e.clientX;
      this.dragInfo.startY = e.clientY;
      this.canvas.style.cursor = 'grabbing';
      return;
    }
    
    // 检查是否点击了分支事件
    const branchEvent = this.findEventAtPosition(mouseX, mouseY, 'branch');
    if (branchEvent) {
      this.dragInfo.isDragging = true;
      this.dragInfo.currentNode = branchEvent;
      this.dragInfo.nodeType = 'branch';
      this.dragInfo.startX = e.clientX;
      this.dragInfo.startY = e.clientY;
      this.dragInfo.originalX = branchEvent.renderPosition?.x || mouseX;
      this.dragInfo.originalY = branchEvent.renderPosition?.y || mouseY;
      this.canvas.style.cursor = 'grabbing';
      return;
    }
    
    // 如果没有点击任何事件节点，则进入平移模式
    this.dragInfo.isPanning = true;
    this.dragInfo.startX = e.clientX - this.config.offsetX;
    this.dragInfo.startY = e.clientY - this.config.offsetY;
    this.canvas.style.cursor = 'grabbing';
  }
  
  /**
   * 处理鼠标移动事件
   * @param {MouseEvent} e 鼠标事件
   */
  handleMouseMove(e) {
    // 获取鼠标在Canvas中的位置
    const rect = this.canvas.getBoundingClientRect();
    const mouseX = (e.clientX - rect.left - this.config.offsetX) / this.config.scale;
    const mouseY = (e.clientY - rect.top - this.config.offsetY) / this.config.scale;
    
    // 检测鼠标是否悬停在事件节点上，更新光标样式
    const isOverEvent = this.findEventAtPosition(mouseX, mouseY);
    if (isOverEvent && !this.dragInfo.isDragging && !this.dragInfo.isPanning) {
      this.canvas.style.cursor = 'pointer';
    } else if (!this.dragInfo.isDragging && !this.dragInfo.isPanning) {
      this.canvas.style.cursor = 'default';
    }
    
    if (this.dragInfo.isPanning) {
      // 平移画布
      this.config.offsetX = e.clientX - this.dragInfo.startX;
      this.config.offsetY = e.clientY - this.dragInfo.startY;
      this.draw();
    } else if (this.dragInfo.isDragging && this.dragInfo.currentNode) {
      // 计算拖动偏移量
      const dx = (e.clientX - this.dragInfo.startX) / this.config.scale;
      const dy = (e.clientY - this.dragInfo.startY) / this.config.scale;
      
      if (this.dragInfo.nodeType === 'branch') {
        // 分支事件只能水平拖动
        const node = this.dragInfo.currentNode;
        if (node.renderPosition) {
          node.renderPosition.x = this.dragInfo.originalX + dx;
          // Y位置保持不变，因为它是由时间（年月日）决定的
        }
      } else if (this.dragInfo.nodeType === 'mainTrunk') {
        // 主干事件只能垂直拖动（调整时间）
        const node = this.dragInfo.currentNode;
        // 根据拖动计算新的时间（年月日）
        // 这里简化处理，仅调整月份
        const newMonth = Math.max(1, Math.min(12, node.month + Math.round(dy / 10)));
        if (newMonth !== node.month) {
          node.month = newMonth;
        }
      }
      
      // 更新起始位置
      this.dragInfo.startX = e.clientX;
      this.dragInfo.startY = e.clientY;
      
      // 重绘时间线
      this.draw();
    }
  }
  
  /**
   * 处理鼠标释放事件
   * @param {MouseEvent} e 鼠标事件
   */
  handleMouseUp(e) {
    if (this.dragInfo.isPanning) {
      this.dragInfo.isPanning = false;
      this.canvas.style.cursor = 'default';
    }
    
    if (this.dragInfo.isDragging) {
      this.dragInfo.isDragging = false;
      this.dragInfo.currentNode = null;
      this.canvas.style.cursor = 'default';
    }
  }
  
  /**
   * 处理鼠标滚轮事件
   * @param {WheelEvent} e 滚轮事件
   */
  handleWheel(e) {
    e.preventDefault();
    
    const delta = e.deltaY < 0 ? 0.1 : -0.1;
    const newScale = Math.max(0.5, Math.min(2, this.config.scale + delta));
    
    // 计算鼠标位置相对于Canvas的坐标
    const rect = this.canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    // 根据鼠标位置调整偏移
    const scaleChange = newScale / this.config.scale;
    this.config.offsetX = mouseX - (mouseX - this.config.offsetX) * scaleChange;
    this.config.offsetY = mouseY - (mouseY - this.config.offsetY) * scaleChange;
    
    this.config.scale = newScale;
    this.generateTimeScale();
    this.draw();
  }
  
  /**
   * 计算事件在Y轴的位置
   * @param {Object} time 时间对象，包含year, month, day
   * @returns {number} Y轴位置
   */
  calculateYPosition(time) {
    if (!time || !time.year) return 100;
    
    // 基础年份差距
    const yearDiff = time.year - this.timeScale.startYear;
    
    // 基础位置 = 年份 * 年间距
    let position = yearDiff * this.config.yearSpacing;
    
    // 考虑月份 (每个月占年间距的 1/12)
    const monthPart = ((time.month || 1) - 1) / 12;
    position += monthPart * this.config.yearSpacing;
    
    // 考虑日期 (每天占月间距的 1/30，近似值)
    const dayPart = ((time.day || 1) - 1) / 30;
    position += dayPart * (this.config.yearSpacing / 12);
    
    return position;
  }
  
  /**
   * 设置节点拖拽模式
   * @param {Object} node 节点
   * @param {string} nodeType 节点类型
   * @param {number} startX 起始X坐标
   * @param {number} startY 起始Y坐标
   */
  setNodeDragging(node, nodeType, startX, startY) {
    this.dragInfo.isDragging = true;
    this.dragInfo.currentNode = node;
    this.dragInfo.nodeType = nodeType;
    this.dragInfo.startX = startX;
    this.dragInfo.startY = startY;
    return this;
  }
  
  /**
   * 在指定位置查找事件节点
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @param {string} type 可选，指定查找的事件类型：'mainTrunk'或'branch'
   * @returns {Object|null} 找到的事件对象，没找到则返回null
   */
  findEventAtPosition(x, y, type) {
    const hitRadius = 10; // 点击检测半径
    
    // 查找主干事件
    if (!type || type === 'mainTrunk') {
      for (const event of this.mainTrunkEvents) {
        const eventX = this.config.mainTrunkX;
        const eventY = this.calculateYPosition({
          year: event.year,
          month: event.month || 1,
          day: event.day || 1
        });
        
        // 计算距离
        const distance = Math.sqrt(Math.pow(x - eventX, 2) + Math.pow(y - eventY, 2));
        if (distance <= hitRadius) {
          return event;
        }
      }
    }
    
    // 查找分支事件
    if (!type || type === 'branch') {
      for (const event of this.branchEvents) {
        if (!event.renderPosition) continue;
        
        const { x: eventX, y: eventY } = event.renderPosition;
        // 计算距离
        const distance = Math.sqrt(Math.pow(x - eventX, 2) + Math.pow(y - eventY, 2));
        if (distance <= hitRadius) {
          return event;
        }
      }
    }
    
    return null;
  }
} 