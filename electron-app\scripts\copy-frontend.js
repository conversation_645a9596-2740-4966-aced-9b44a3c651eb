const fs = require('fs-extra');
const path = require('path');

async function copyFrontend() {
  try {
    console.log('开始复制前端文件...');
    
    const sourceDir = path.join(__dirname, '../../frontend');
    const targetDir = path.join(__dirname, '../renderer');
    
    // 检查源目录是否存在
    if (!await fs.pathExists(sourceDir)) {
      console.error('前端源目录不存在:', sourceDir);
      process.exit(1);
    }
    
    // 清理目标目录
    if (await fs.pathExists(targetDir)) {
      await fs.remove(targetDir);
    }
    
    // 复制前端文件
    await fs.copy(sourceDir, targetDir, {
      filter: (src, dest) => {
        // 排除 node_modules 和其他不需要的文件
        const relativePath = path.relative(sourceDir, src);
        
        if (relativePath.includes('node_modules')) return false;
        if (relativePath.includes('.git')) return false;
        if (relativePath.includes('dist')) return false;
        if (relativePath.includes('.vite')) return false;
        if (relativePath.endsWith('.log')) return false;
        
        return true;
      }
    });
    
    console.log('前端文件复制完成!');
    console.log('源目录:', sourceDir);
    console.log('目标目录:', targetDir);
    
    // 修改 package.json 中的脚本
    const packageJsonPath = path.join(targetDir, 'package.json');
    if (await fs.pathExists(packageJsonPath)) {
      const packageJson = await fs.readJson(packageJsonPath);
      
      // 修改开发服务器端口，避免冲突
      if (packageJson.scripts && packageJson.scripts.dev) {
        packageJson.scripts.dev = packageJson.scripts.dev.replace('vite', 'vite --port 13001');
      }
      
      await fs.writeJson(packageJsonPath, packageJson, { spaces: 2 });
      console.log('已更新 package.json 配置');
    }
    
  } catch (error) {
    console.error('复制前端文件失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  copyFrontend();
}

module.exports = copyFrontend;
