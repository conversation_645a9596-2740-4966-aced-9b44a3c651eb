<template>
  <div class="app-layout">
    <!-- 侧边菜单 -->
    <AppMenu />
    
    <!-- 主要内容区域 -->
    <div class="app-main">
      <!-- 顶部标题栏 -->
      <AppHeader />
      
      <!-- 页面内容 -->
      <div class="app-content">
        <router-view v-slot="{ Component, route }">
          <keep-alive :include="keepAliveComponents">
            <component :is="Component" :key="route.fullPath" />
          </keep-alive>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppMenu from './AppMenu.vue'
import AppHeader from './AppHeader.vue'

const route = useRoute()

// 需要缓存的组件
const keepAliveComponents = computed(() => {
  const components = []
  
  // 根据路由meta信息决定是否缓存
  if (route.meta?.keepAlive) {
    components.push(route.name)
  }
  
  // 默认缓存的组件
  const defaultKeepAlive = [
    'Writing', // 写作页面
    'Settings', // 设定页面
    'Chat' // 聊天页面
  ]
  
  return [...new Set([...components, ...defaultKeepAlive])]
})
</script>

<style scoped>
.app-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.app-content {
  flex: 1;
  overflow: hidden;
  background: var(--el-bg-color-page);
}

/* 确保内容区域可以滚动 */
.app-content > * {
  height: 100%;
  overflow-y: auto;
}
</style>
