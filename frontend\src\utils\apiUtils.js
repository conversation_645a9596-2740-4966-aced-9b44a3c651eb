/**
 * API工具类 - 统一处理API响应和错误
 */

import { ElMessage } from 'element-plus'

/**
 * 解析API响应
 * @param {string|object} response - API响应
 * @returns {object} 解析后的响应对象
 */
export const parseApiResponse = (response) => {
  if (typeof response === 'string') {
    try {
      return JSON.parse(response)
    } catch (error) {
      console.error('解析API响应失败:', error)
      throw new Error('API响应格式错误')
    }
  }
  return response
}

/**
 * 检查API响应是否成功
 * @param {object} response - 解析后的响应对象
 * @returns {boolean} 是否成功
 */
export const isApiSuccess = (response) => {
  return response && response.status === 'success'
}

/**
 * 获取API响应数据
 * @param {object} response - 解析后的响应对象
 * @returns {any} 响应数据
 */
export const getApiData = (response) => {
  return response?.data || null
}

/**
 * 获取API错误消息
 * @param {object} response - 解析后的响应对象
 * @param {string} defaultMessage - 默认错误消息
 * @returns {string} 错误消息
 */
export const getApiError = (response, defaultMessage = '操作失败') => {
  return response?.message || defaultMessage
}

/**
 * 统一的API调用处理器
 * @param {Function} apiCall - API调用函数
 * @param {object} options - 选项
 * @param {string} options.successMessage - 成功消息
 * @param {string} options.errorMessage - 错误消息
 * @param {boolean} options.showSuccess - 是否显示成功消息
 * @param {boolean} options.showError - 是否显示错误消息
 * @returns {Promise<any>} API调用结果
 */
export const handleApiCall = async (apiCall, options = {}) => {
  const {
    successMessage,
    errorMessage = '操作失败',
    showSuccess = false,
    showError = true
  } = options

  try {
    const response = await apiCall()
    const parsedResponse = parseApiResponse(response)
    
    if (isApiSuccess(parsedResponse)) {
      if (showSuccess && successMessage) {
        ElMessage.success(successMessage)
      }
      return getApiData(parsedResponse)
    } else {
      const error = getApiError(parsedResponse, errorMessage)
      if (showError) {
        ElMessage.error(error)
      }
      throw new Error(error)
    }
  } catch (error) {
    if (showError && !error.message.includes('操作失败')) {
      ElMessage.error(error.message || errorMessage)
    }
    throw error
  }
}

/**
 * 批量API调用处理器
 * @param {Array<Function>} apiCalls - API调用函数数组
 * @param {object} options - 选项
 * @returns {Promise<Array>} 批量调用结果
 */
export const handleBatchApiCalls = async (apiCalls, options = {}) => {
  const {
    concurrent = false,
    stopOnError = false,
    showError = true
  } = options

  const results = []
  const errors = []

  if (concurrent) {
    // 并发执行
    const promises = apiCalls.map(async (apiCall, index) => {
      try {
        const result = await handleApiCall(apiCall, { showError: false })
        return { index, result, error: null }
      } catch (error) {
        return { index, result: null, error }
      }
    })

    const responses = await Promise.all(promises)
    
    responses.forEach(({ index, result, error }) => {
      results[index] = result
      if (error) {
        errors[index] = error
      }
    })
  } else {
    // 串行执行
    for (let i = 0; i < apiCalls.length; i++) {
      try {
        const result = await handleApiCall(apiCalls[i], { showError: false })
        results[i] = result
      } catch (error) {
        errors[i] = error
        if (stopOnError) {
          break
        }
      }
    }
  }

  // 显示错误消息
  if (showError && errors.length > 0) {
    const errorCount = errors.filter(e => e).length
    if (errorCount > 0) {
      ElMessage.error(`${errorCount} 个操作失败`)
    }
  }

  return { results, errors }
}

/**
 * 创建带重试的API调用
 * @param {Function} apiCall - API调用函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟（毫秒）
 * @returns {Function} 带重试的API调用函数
 */
export const createRetryableApiCall = (apiCall, maxRetries = 3, delay = 1000) => {
  return async (...args) => {
    let lastError
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await apiCall(...args)
      } catch (error) {
        lastError = error
        
        if (i < maxRetries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    }
    
    throw lastError
  }
}

/**
 * 创建缓存的API调用
 * @param {Function} apiCall - API调用函数
 * @param {number} ttl - 缓存时间（毫秒）
 * @returns {Function} 带缓存的API调用函数
 */
export const createCachedApiCall = (apiCall, ttl = 300000) => { // 默认5分钟
  const cache = new Map()
  
  return async (...args) => {
    const key = JSON.stringify(args)
    const cached = cache.get(key)
    
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data
    }
    
    const result = await apiCall(...args)
    cache.set(key, {
      data: result,
      timestamp: Date.now()
    })
    
    return result
  }
}

/**
 * 防抖API调用
 * @param {Function} apiCall - API调用函数
 * @param {number} delay - 防抖延迟（毫秒）
 * @returns {Function} 防抖的API调用函数
 */
export const debounceApiCall = (apiCall, delay = 300) => {
  let timeoutId
  
  return (...args) => {
    return new Promise((resolve, reject) => {
      clearTimeout(timeoutId)
      
      timeoutId = setTimeout(async () => {
        try {
          const result = await apiCall(...args)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }, delay)
    })
  }
}

/**
 * 节流API调用
 * @param {Function} apiCall - API调用函数
 * @param {number} delay - 节流延迟（毫秒）
 * @returns {Function} 节流的API调用函数
 */
export const throttleApiCall = (apiCall, delay = 1000) => {
  let lastCall = 0
  let timeoutId
  
  return (...args) => {
    return new Promise((resolve, reject) => {
      const now = Date.now()
      const timeSinceLastCall = now - lastCall
      
      if (timeSinceLastCall >= delay) {
        lastCall = now
        apiCall(...args).then(resolve).catch(reject)
      } else {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => {
          lastCall = Date.now()
          apiCall(...args).then(resolve).catch(reject)
        }, delay - timeSinceLastCall)
      }
    })
  }
}
