<template>
  <el-dialog
      v-model="dialogVisible"
      title="关系图谱"
      class="fullscreen-graph-dialog"
      fullscreen
      destroy-on-close
      :show-close="false"
      :lock-scroll="true"
      append-to-body
  >
    <div class="dialog-content-wrapper">
      <!-- 顶部工具栏 -->
      <div class="graph-toolbar">
        <!-- 标题和布局区域 -->
        <div class="toolbar-section title-section">
          <h2 class="dialog-title">人物关系图谱</h2>
        </div>
        
        <!-- 视图控制区域 - 主要功能区 -->
        <div class="toolbar-section view-section">
          <!-- 布局选择器 -->
          <div class="toolbar-item layout-controls">
            <span class="control-label">布局:</span>
            <el-select v-model="graphLayout" size="small" @change="updateGraphLayout">
              <el-option label="力导向布局" value="force" />
              <el-option label="环形布局" value="circular" />
              <el-option label="网格布局" value="grid" />
            </el-select>
          </div>
          
          <!-- 查看模式 -->
          <div class="toolbar-item mode-controls">
            <span class="control-label">查看模式:</span>
            <el-radio-group v-model="viewMode" size="small" @change="handleModeChange">
              <el-radio-button label="relations">关系实体</el-radio-button>
              <el-radio-button label="templates">模板筛选</el-radio-button>
            </el-radio-group>
          </div>
          
          <!-- 根据模式显示不同控件 -->
          <div class="toolbar-item dynamic-controls">
            <!-- 始终显示两个选择器，但根据模式启用/禁用 -->
            <!-- 模板筛选控件 -->
            <el-select 
              v-model="selectedTemplateIds"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="选择角色模板"
              size="small"
              class="template-selector"
              @change="handleTemplateChange"
              :disabled="viewMode !== 'templates'"
            >
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              />
            </el-select>
            
            <!-- 中心节点选择 -->
            <el-select 
              v-model="centerNodeId"
              placeholder="选择中心角色"
              filterable
              clearable
              size="small"
              class="center-node-selector"
              @change="handleCenterNodeChange"
              :disabled="viewMode !== 'relations'"
            >
              <el-option-group label="主要角色">
                <el-option
                  v-for="entity in filteredMainCharacters"
                  :key="entity.id"
                  :label="entity.name"
                  :value="entity.id"
                >
                  <div class="entity-option">
                    <span class="entity-color-dot" :style="{ background: getEntityColor(entity.name) }"></span>
                    <span>{{ entity.name }}</span>
                  </div>
                </el-option>
              </el-option-group>
              
              <el-option-group label="所有角色">
                <el-option
                  v-for="entity in filteredEntityList"
                  :key="entity.id"
                  :label="entity.name"
                  :value="entity.id"
                >
                  <div class="entity-option">
                    <span class="entity-color-dot" :style="{ background: getEntityColor(entity.name) }"></span>
                    <span>{{ entity.name }}</span>
                  </div>
                </el-option>
              </el-option-group>
            </el-select>
            
            <!-- 当选择了中心节点时显示取消中心按钮 -->
            <el-button 
              v-if="centerNodeId && viewMode === 'relations'"
              size="small" 
              @click="clearCenterNode"
              class="clear-center-btn"
            >
              取消中心
            </el-button>
          </div>
        </div>
        
        <!-- 操作区域 - 右侧 -->
        <div class="toolbar-section action-section">
          <el-button 
            @click="addNewRelation" 
            type="primary" 
            size="small" 
            class="add-relation-btn"
            :disabled="viewMode === 'templates' && selectedTemplateIds.length === 0"
          >
            <el-icon><Plus /></el-icon> 添加关系
          </el-button>
          
          <el-button-group class="view-actions">
            <el-tooltip content="重置视图">
              <el-button @click="resetGraph" type="default" size="small">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="自适应">
              <el-button @click="fitView" type="default" size="small">
                <el-icon><Position /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="导出图片">
              <el-button @click="exportGraph" type="default" size="small" :loading="exporting">
                <el-icon><Download /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="查看图例">
              <el-button @click="showLegend" type="default" size="small">
                <el-icon><InfoFilled /></el-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>
          
          <el-button @click="closeDialog" class="close-btn" type="primary" size="small">
            <el-icon><Back /></el-icon> 返回
          </el-button>
        </div>
      </div>

      <!-- 图谱容器 -->
      <div class="fullscreen-graph-container">
        <div ref="graphContainer" class="graph-content"></div>
        
        <!-- 没有数据时的提示 -->
        <div v-if="!hasRelations" class="empty-graph">
          <el-icon class="empty-icon"><Connection /></el-icon>
          <div class="empty-text">暂无关系数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 图例说明对话框 -->
    <el-dialog
      v-model="legendDialogVisible"
      title="图谱图例说明"
      width="500px"
      align-center
    >
      <div class="graph-legend-dialog">
        <div class="legend-section">
          <h4 class="legend-section-title">关系类型</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="connection-line friendly"></div>
              <span>友好关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line hostile"></div>
              <span>敌对关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line family"></div>
              <span>血缘关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line romance"></div>
              <span>恋爱关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line other"></div>
              <span>其他关系</span>
            </div>
          </div>
        </div>
        
        <div class="legend-section">
          <h4 class="legend-section-title">关系方向</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="connection-line unidirectional">
                <div class="arrow-end"></div>
              </div>
              <span>单向关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line bidirectional">
                <div class="arrow-start"></div>
                <div class="arrow-end"></div>
              </div>
              <span>双向关系</span>
            </div>
          </div>
        </div>
        
        <!-- 模板模式下的节点图例 -->
        <div v-if="viewMode === 'templates' && selectedTemplateIds.length > 0" class="legend-section">
          <h4 class="legend-section-title">模板筛选</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="node-sample selected"></div>
              <span>选中模板的实体</span>
            </div>
            <div class="legend-item">
              <div class="node-sample related"></div>
              <span>关联实体</span>
            </div>
          </div>
        </div>
        
        <!-- 添加中心节点说明到图例 -->
        <div v-if="centerNodeId" class="legend-section">
          <h4 class="legend-section-title">中心角色</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="node-sample center-node"></div>
              <span>中心角色</span>
            </div>
            <div class="legend-item">
              <div class="node-sample connected-node"></div>
              <span>关联角色</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加导出设置对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出设置"
      width="460px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="export-settings">
        <div class="setting-item">
          <span class="setting-label">导出范围</span>
          <el-radio-group v-model="exportSettings.exportMode">
            <el-radio label="visible">可视区域</el-radio>
            <el-radio label="all">全部内容</el-radio>
          </el-radio-group>
        </div>
      
        <div class="setting-item">
          <span class="setting-label">背景颜色</span>
          <el-color-picker v-model="exportSettings.backgroundColor" show-alpha />
        </div>
        
        <div class="setting-item">
          <span class="setting-label">尺寸预设</span>
          <el-select v-model="exportSettings.sizePreset" @change="handleSizePresetChange">
            <el-option label="原始尺寸" value="original" />
            <el-option label="A4 纸张 (横向)" value="a4-landscape" />
            <el-option label="A4 纸张 (纵向)" value="a4-portrait" />
            <el-option label="16:9 屏幕" value="16:9" />
            <el-option label="方形 (1:1)" value="square" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </div>
        
        <div class="setting-item" v-if="exportSettings.sizePreset === 'custom'">
          <div class="custom-size">
            <div class="size-input">
              <span>宽度 (px)</span>
              <el-input-number v-model="exportSettings.width" :min="100" :max="3000" size="small" />
            </div>
            <div class="size-input">
              <span>高度 (px)</span>
              <el-input-number v-model="exportSettings.height" :min="100" :max="3000" size="small" />
            </div>
          </div>
        </div>
        
        <div class="setting-item">
          <span class="setting-label">图片质量</span>
          <el-select v-model="exportSettings.quality">
            <el-option label="标准" value="1" />
            <el-option label="高清" value="2" />
            <el-option label="超清" value="3" />
          </el-select>
        </div>
        
        <div class="setting-item">
          <el-checkbox v-model="exportSettings.fitAllNodes">自动适应所有节点</el-checkbox>
        </div>
        
        <div class="setting-item">
          <span class="setting-label">节点大小</span>
          <div class="slider-with-value">
            <el-slider 
              v-model="exportSettings.nodeScale" 
              :min="0.5" 
              :max="3" 
              :step="0.1" 
              :format-tooltip="val => `${Math.round(val * 100)}%`"
              class="node-scale-slider"
            />
            <div class="scale-value">{{ Math.round(exportSettings.nodeScale * 100) }}%</div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="doExportGraph" :loading="exporting">
            导出图片
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, onUnmounted } from 'vue';
import { useBookStore } from '@/stores/book';
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
import html2canvas from 'html2canvas';
import { 
  Refresh, 
  Connection, 
  Loading, 
  Position, 
  Back,
  Plus,
  Download,
  InfoFilled
} from '@element-plus/icons-vue';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  relations: {
    type: Array,
    default: () => []
  },
  entityList: {
    type: Array,
    default: () => []
  },
  bookId: {
    type: String,
    default: ''
  },
  templates: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:visible', 'node-click', 'edge-click', 'add-relation']);

// 本地状态
const dialogVisible = ref(props.visible);
const graphContainer = ref(null);
const chart = ref(null);
const loading = ref(false);
const viewMode = ref('relations'); // 默认为关系实体模式
const selectedTemplateIds = ref([]); // 选中的模板IDs
const centerNodeId = ref(null);
const connectedNodeIds = ref(new Set());
const exporting = ref(false);
const exportDialogVisible = ref(false);
const exportSettings = ref({
  backgroundColor: 'rgba(0, 0, 0, 0.9)', // 默认背景色
  sizePreset: 'original',
  width: 1920,
  height: 1080,
  quality: '2',
  fitAllNodes: true,
  nodeScale: 1.5, // 默认节点放大到150%
  exportMode: 'visible'
});
const graphLayout = ref('force'); // 默认为力导向布局
const legendDialogVisible = ref(false);

// Store
const bookStore = useBookStore();

// 计算属性
const hasRelations = computed(() => props.relations && props.relations.length > 0);
const bookTitle = computed(() => {
  const book = bookStore.bookList.find(b => b.id === props.bookId);
  return book ? book.title : '';
});

const filteredEntityList = computed(() => {
  if (viewMode.value === 'relations') {
    // 关系模式：只显示有关系连接的实体
    const entityIdsWithRelations = new Set();
    props.relations.forEach(relation => {
      entityIdsWithRelations.add(relation.source);
      entityIdsWithRelations.add(relation.target);
    });
    
    return props.entityList.filter(entity => 
      entityIdsWithRelations.has(entity.id)
    );
  } else {
    // 模板模式：显示选中模板的实体以及与它们有关系的实体
    if (selectedTemplateIds.value.length === 0) {
      return []; // 如果没有选择模板，不显示任何实体
    }
    
    // 先找出所有选中模板的实体
    const selectedTemplateEntities = props.entityList.filter(entity => 
      selectedTemplateIds.value.includes(entity.template_id)
    );
    
    // 如果没有选中模板的实体，直接返回空数组
    if (selectedTemplateEntities.length === 0) {
      return [];
    }
    
    // 构建选中模板实体ID集合
    const selectedEntityIds = new Set(selectedTemplateEntities.map(e => e.id));
    
    // 找出所有与选中实体有关联的实体ID
    const connectedEntityIds = new Set(selectedEntityIds);
    
    props.relations.forEach(relation => {
      // 如果源或目标是选中的实体，将另一端也添加到集合中
      if (selectedEntityIds.has(relation.source)) {
        connectedEntityIds.add(relation.target);
      }
      if (selectedEntityIds.has(relation.target)) {
        connectedEntityIds.add(relation.source);
      }
    });
    
    // 返回所有选中和关联的实体
    return props.entityList.filter(entity => connectedEntityIds.has(entity.id));
  }
});

const filteredRelations = computed(() => {
  if (viewMode.value === 'relations') {
    // 关系模式下显示所有关系
    return props.relations;
  } else {
    // 模板模式：显示至少有一端连接到选中模板实体的所有关系
    if (selectedTemplateIds.value.length === 0) {
      return [];
    }
    
    // 获取选中模板的实体ID
    const selectedTemplateEntityIds = new Set(
      props.entityList
        .filter(entity => selectedTemplateIds.value.includes(entity.template_id))
        .map(entity => entity.id)
    );
    
    // 过滤关系，只保留至少有一端是选中模板的实体
    return props.relations.filter(relation =>
      selectedTemplateEntityIds.has(relation.source) ||
      selectedTemplateEntityIds.has(relation.target)
    );
  }
});

// 识别主要角色（关系数量最多的前5个）
const filteredMainCharacters = computed(() => {
  // 统计每个实体的关系数量
  const entityRelationCounts = {};
  filteredRelations.value.forEach(relation => {
    entityRelationCounts[relation.source] = (entityRelationCounts[relation.source] || 0) + 1;
    entityRelationCounts[relation.target] = (entityRelationCounts[relation.target] || 0) + 1;
  });
  
  // 将实体按关系数量排序并取前5个
  return filteredEntityList.value
    .filter(entity => entityRelationCounts[entity.id] > 0)
    .sort((a, b) => (entityRelationCounts[b.id] || 0) - (entityRelationCounts[a.id] || 0))
    .slice(0, 5);
});

// 简化监听visible变化的逻辑，避免多个监听器相互干扰
watch(
  () => props.visible,
  (newVal) => {
    console.log('父组件visible变化:', newVal);
    dialogVisible.value = newVal;
    if (newVal) {
      // 仅在弹窗打开时初始化图表
      nextTick(() => {
        setTimeout(() => {
          initGraph();
        }, 300);
      });
    }
  }
);

// 简化监听dialogVisible的逻辑，只保留一个监听器
watch(
  () => dialogVisible.value,
  (newVal) => {
    console.log('dialogVisible变化:', newVal);
    // 确保将状态同步回父组件
    emit('update:visible', newVal);
    
    if (!newVal) {
      // 弹窗关闭时清理资源
      if (chart.value) {
        chart.value.dispose();
        chart.value = null;
      }
      window.removeEventListener('resize', handleResize);
    }
  }
);

// 修改初始化图表的方法
const initGraph = () => {
  loading.value = true;
  console.log('开始初始化图表');
  
  // 先检查数据是否有效
  if (!props.relations || props.relations.length === 0) {
    console.warn('没有关系数据，无法初始化图表');
    loading.value = false;
    return;
  }

  if (!props.entityList || props.entityList.length === 0) {
    console.warn('没有角色数据，无法初始化图表');
    loading.value = false;
    return;
  }
  
  // 延迟初始化，确保DOM已经完全渲染
  setTimeout(() => {
    if (!graphContainer.value) {
      console.error('图表容器不存在，无法初始化图表');
      loading.value = false;
      return;
    }
    
    // 如果已存在图表，先销毁
    if (chart.value) {
      chart.value.dispose();
      chart.value = null;
    }
    
    // 确保容器有宽高
    const containerWidth = graphContainer.value.clientWidth;
    const containerHeight = graphContainer.value.clientHeight;
    
    if (containerWidth === 0 || containerHeight === 0) {
      console.warn('图表容器尺寸为0，设置最小尺寸');
      // 设置最小尺寸
      graphContainer.value.style.width = '100%';
      graphContainer.value.style.height = '600px';
    }
    
    // 初始化图表
    try {
      console.log('创建图表实例，容器尺寸:', {
        width: containerWidth || graphContainer.value.clientWidth,
        height: containerHeight || graphContainer.value.clientHeight
      });
      
      chart.value = echarts.init(graphContainer.value);
      
      // 设置图表配置
      updateGraphData();
      
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize);
      
      // 添加额外的事件监听
      chart.value.on('rendered', () => {
        // console.log('图表渲染完成');
      });
      
      loading.value = false;
    } catch (error) {
      console.error('初始化图表时出错:', error);
      loading.value = false;
    }
  }, 500); // 延长延迟时间，确保DOM渲染完成
};

// 处理窗口大小变化
const handleResize = () => {
  if (chart.value) {
    chart.value.resize();
  }
};

// 修改关闭对话框的方法，确保状态正确更新
const closeDialog = () => {
  console.log('关闭全屏弹窗按钮点击');
  // 先销毁图表实例释放资源
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
  
  // 移除事件监听
  window.removeEventListener('resize', handleResize);
  
  // 最后设置状态为false
  dialogVisible.value = false;
};

// 统计实体关系数量
const countEntityRelations = (entityId) => {
  return props.relations.filter(
    relation => relation.source === entityId || relation.target === entityId
  ).length;
};

// 根据实体名称生成颜色
const getEntityColor = (name) => {
  if (!name) return '#409EFF';
  
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const hue = Math.abs(hash) % 360;
  return `hsl(${hue}, 70%, 60%)`;
};

// 处理模式变更
const handleModeChange = () => {
  if (viewMode.value === 'relations') {
    // 关系模式直接重新初始化图谱
    initGraph();
  } else {
    // 模板模式下，如果已经选择了模板，则重新初始化
    if (selectedTemplateIds.value.length > 0) {
      initGraph();
    }
  }
};

// 处理模板选择变更
const handleTemplateChange = () => {
  // 当选择的模板变化时，重新初始化图谱
  initGraph();
};

// 添加关系的方法
const addNewRelation = () => {
  emit('add-relation');
};

// 处理中心节点变化
const handleCenterNodeChange = (nodeId) => {
  centerNodeId.value = nodeId;
  
  if (nodeId) {
    // 找出与中心节点直接相连的所有节点
    connectedNodeIds.value = new Set();
    filteredRelations.value.forEach(relation => {
      if (relation.source === nodeId) {
        connectedNodeIds.value.add(relation.target);
      }
      if (relation.target === nodeId) {
        connectedNodeIds.value.add(relation.source);
      }
    });
    
    // 重新初始化图表以反映中心节点
    initGraph();
  } else {
    connectedNodeIds.value = new Set();
  }
};

// 清除中心节点
const clearCenterNode = () => {
  centerNodeId.value = null;
  connectedNodeIds.value = new Set();
  initGraph();
};

// 在组件挂载时初始化
onMounted(() => {
  if (dialogVisible.value) {
    // 延迟初始化，确保DOM已渲染
    setTimeout(() => {
      initGraph();
    }, 500);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  console.log('全屏弹窗组件卸载');
  window.removeEventListener('resize', handleResize);
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
});

// 添加回缺失的updateGraphData函数及其依赖
const updateGraphData = () => {
  // 验证数据是否存在
  if (!chart.value) {
    console.error('图表实例不存在，无法更新数据');
    loading.value = false;
    return;
  }
  
  // 检查是否有关系数据
  if (!hasRelations.value) {
    console.warn('没有关系数据可显示');
    loading.value = false;
    return;
  }
  
  // 检查是否有实体数据
  if (!filteredEntityList.value || filteredEntityList.value.length === 0) {
    console.warn('没有角色数据可显示');
    loading.value = false;
    return;
  }
  
  console.log('准备更新图表数据:', {
    实体数量: filteredEntityList.value.length,
    关系数量: filteredRelations.value.length
  });
  
  try {
    // 准备图表数据
    const nodes = prepareNodes();
    const edges = prepareEdges();
    
    // 检查处理后的数据是否有效
    if (!nodes.length || !edges.length) {
      console.warn('处理后的节点或边数据为空，无法绘制图表');
      loading.value = false;
      return;
    }
    
    // 设置图表选项
    const option = {
      backgroundColor: 'transparent', // 透明背景
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          if (params.dataType === 'edge') {
            return formatEdgeTooltip(params.data);
          } else {
            return formatNodeTooltip(params.data);
          }
        },
        backgroundColor: 'rgba(50, 50, 50, 0.8)',
        borderRadius: 8,
        padding: 10,
        textStyle: {
          color: '#fff'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasingUpdate: 'quinticInOut',
      series: [
        {
          type: 'graph',
          layout: graphLayout.value,
          draggable: true,
          roam: true,
          zoom: 1.2, // 初始放大一点
          nodeScaleRatio: 0.6, // 节点缩放比例
          focusNodeAdjacency: true, // 鼠标移到节点上时高亮相邻节点和边
          itemStyle: {
            borderWidth: 2,
            borderColor: 'rgba(255, 255, 255, 0.3)',
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 10
          },
          data: nodes,
          links: edges,
          edgeSymbolSize: [6, 10],
          categories: [],
          force: {
            repulsion: centerNodeId.value ? 2000 : 1500, // 有中心节点时增加斥力
            gravity: centerNodeId.value ? 0.1 : 0.2,    // 减小引力使中心节点更突出
            edgeLength: centerNodeId.value ? 300 : 250, // 增加边长
            friction: 0.15,
            layoutAnimation: true
          },
          lineStyle: {
            color: 'source',
            curveness: 0.25, // 柔和的曲线
            width: 3,        // 稍粗的线条
            shadowColor: 'rgba(0, 0, 0, 0.2)',
            shadowBlur: 5,
            cap: 'round',
            join: 'round'
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 6,
              shadowBlur: 10
            },
            itemStyle: {
              borderWidth: 3,
              shadowBlur: 15
            },
            label: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          label: {
            show: true,
            position: 'right',
            distance: 5,
            formatter: '{b}',
            fontSize: 14,
            fontWeight: 'bold',
            color: '#fff',
            backgroundColor: 'rgba(40, 40, 40, 0.7)',
            borderRadius: 4,
            padding: [4, 8],
            distance: 5,
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 3,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1
          },
          edgeLabel: {
            show: true,
            fontSize: 14,
            formatter: '{c}',
            position: 'middle',
            backgroundColor: 'rgba(40, 40, 40, 0.85)',
            padding: [6, 10],
            borderRadius: 8,
            color: '#fff',
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 5
          },
          // 添加节点全局样式
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        }
      ]
    };
    
    // 设置图表选项
    chart.value.setOption(option);
    
    // 修改事件监听，同时处理节点和边的点击
    chart.value.on('click', (params) => {
      if (params.dataType === 'node') {
        // 处理节点点击
        const entityId = params.data.id;
        const entity = props.entityList.find(e => e.id === entityId);
        if (entity) {
          emit('node-click', entity);
        }
      } else if (params.dataType === 'edge') {
        // 处理边（关系）点击
        const relationId = params.data.relationId;
        const relation = props.relations.find(r => r.id === relationId);
        if (relation) {
          emit('edge-click', relation);
        }
      }
    });
    
    console.log('图表数据更新成功');
    
    // 如果有中心节点，进行额外处理使其居中
    if (centerNodeId.value) {
      // 延迟处理以确保布局已经初始化
      setTimeout(() => {
        // 找到中心节点的图表实例
        const centerNodeIndex = chart.value.getModel().getSeriesByIndex(0).getData().indexOfName(
          props.entityList.find(e => e.id === centerNodeId.value)?.name
        );
        
        if (centerNodeIndex >= 0) {
          const centerNode = chart.value.getModel().getSeriesByIndex(0).getData().getItemGraphicEl(centerNodeIndex);
          
          if (centerNode) {
            // 使用dispatchAction将视图居中到中心节点
            chart.value.dispatchAction({
              type: 'graphRoam',
              zoom: 1.2,
              originX: centerNode.position[0],
              originY: centerNode.position[1]
            });
          }
        }
      }, 1000); // 延迟1秒执行，给力导向图布局足够的时间
    }
  } catch (error) {
    console.error('更新图表数据时发生错误:', error);
    loading.value = false;
  }
};

// 添加回缺失的prepareNodes函数
const prepareNodes = () => {
  return filteredEntityList.value.map(entity => {
    // 计算节点大小 - 基于关系数量
    const relCount = countEntityRelations(entity.id);
    const symbolSize = Math.max(50, Math.min(80, 50 + relCount * 5));
    
    // 检查是否是选中模板的实体
    const isSelectedTemplateEntity = viewMode.value === 'templates' && 
      selectedTemplateIds.value.includes(entity.template_id);
    
    // 添加中心节点和相连节点的判断逻辑
    const isCenterNode = entity.id === centerNodeId.value;
    const isConnectedNode = connectedNodeIds.value.has(entity.id);
    
    // 根据实体类型/名称获取颜色
    const baseColor = getEntityColor(entity.name);
    
    // 调整不同节点类型的尺寸
    let finalSize = symbolSize;
    if (isCenterNode) {
      finalSize = symbolSize * 1.5; // 中心节点更大
    } else if (isConnectedNode) {
      finalSize = symbolSize * 1.2; // 连接节点稍大
    } else if (centerNodeId.value && !isConnectedNode) {
      finalSize = symbolSize * 0.8; // 非连接节点稍小
    }
    
    return {
      id: entity.id,
      name: entity.name,
      value: relCount,
      itemStyle: {
        // 调整颜色和边框样式基于节点类型
        color: {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            {
              offset: 0,
              color: isCenterNode ? '#FFF9C4' : (isConnectedNode ? lightenColor(baseColor, 30) : lightenColor(baseColor, 20))
            },
            {
              offset: 0.7,
              color: isCenterNode ? '#FFC107' : (isConnectedNode ? baseColor : baseColor)
            },
            {
              offset: 1,
              color: isCenterNode ? '#FF8F00' : (isConnectedNode ? darkenColor(baseColor, 15) : darkenColor(baseColor, 20))
            }
          ]
        },
        borderWidth: isCenterNode ? 4 : (isConnectedNode ? 3 : 2),
        borderColor: isCenterNode ? 'rgba(255, 215, 0, 0.9)' : (isConnectedNode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(255, 255, 255, 0.6)'),
        shadowColor: isCenterNode ? 'rgba(255, 193, 7, 0.6)' : 'rgba(0, 0, 0, 0.3)',
        shadowBlur: isCenterNode ? 20 : 15,
        shadowOffsetX: 2,
        shadowOffsetY: 2
      },
      // 调整节点大小
      symbolSize: finalSize,
      emphasis: {
        itemStyle: {
          borderWidth: isCenterNode ? 5 : (isConnectedNode ? 4 : 3),
          shadowBlur: isCenterNode ? 25 : 20
        },
        scale: true
      },
      entity: entity,
      isSelectedTemplate: isSelectedTemplateEntity,
      isCenterNode: isCenterNode,
      isConnectedNode: isConnectedNode
    };
  });
};

// 添加回缺失的prepareEdges函数
const prepareEdges = () => {
  return filteredRelations.value.map(relation => {
    // 检查该关系是否连接到中心节点
    const isConnectedToCenterNode = centerNodeId.value && 
      (relation.source === centerNodeId.value || relation.target === centerNodeId.value);
    
    // 根据关系类型确定线条样式
    let lineStyle = {
      width: Math.max(2, relation.strength || 1),
      // 对于双向关系增加曲率，避免两个箭头重叠
      curveness: relation.bidirectional ? 0.3 : 0.1, 
      type: relation.bidirectional ? 'solid' : 'dashed',
      opacity: 1
    };
    
    // 确定关系类型颜色
    let relationColor = '#909399';
    switch(relation.type) {
      case '友好':
        relationColor = '#67C23A';
        lineStyle.color = relationColor;
        lineStyle.type = 'solid';
        break;
      case '敌对':
        relationColor = '#F56C6C';
        lineStyle.color = relationColor;
        lineStyle.type = relation.bidirectional ? 'solid' : 'dashed';
        break;
      case '血缘':
        relationColor = '#E6A23C';
        lineStyle.color = relationColor;
        lineStyle.width += 1;
        break;
      case '恋爱':
        relationColor = '#FF88A0';
        lineStyle.color = relationColor;
        lineStyle.type = 'solid';
        break;
      case '师徒':
        relationColor = '#409EFF';
        lineStyle.color = relationColor;
        lineStyle.type = 'solid';
        break;
      default:
        lineStyle.color = relationColor;
    }
    
    // 设置线条颜色
    lineStyle.color = relationColor;
    
    // 根据是否连接到中心节点调整线条样式
    if (isConnectedToCenterNode) {
      lineStyle.width += 2;
      lineStyle.shadowBlur = 10;
      lineStyle.shadowColor = relationColor;
      lineStyle.opacity = 1;
    } else if (centerNodeId.value) {
      lineStyle.opacity = 0.4;
    }
    
    return {
      source: relation.source,
      target: relation.target,
      relationId: relation.id,
      value: relation.strength || 1,
      // 增大线条宽度
      lineStyle: lineStyle,
      // 增强箭头显示
      symbol: relation.bidirectional ? ['arrow', 'arrow'] : ['none', 'arrow'],
      // 增大箭头尺寸
      symbolSize: relation.bidirectional ? [10, 15] : [6, 10], 
      // 确保箭头颜色与线条匹配
      itemStyle: {
        color: relationColor
      },
      label: {
        show: true,
        formatter: relation.type || '未知关系',
        fontSize: 14,
        fontWeight: 'bold',
        backgroundColor: `rgba(40, 40, 40, 0.85)`,
        borderColor: relationColor,
        borderWidth: 2,
        borderRadius: 8,
        padding: [6, 10],
        color: '#fff',
        distance: relation.bidirectional ? 10 : 5, // 双向关系标签距离增加
        align: 'center',
        position: relation.bidirectional ? 'middle' : 'middle',
      },
      emphasis: {
        lineStyle: {
          width: lineStyle.width + 2,
          shadowBlur: isConnectedToCenterNode ? 15 : 10,
          shadowColor: relationColor,
          opacity: 1
        },
        label: {
          fontSize: 16,
          backgroundColor: `rgba(40, 40, 40, 0.95)`,
          borderWidth: 2.5,
          padding: [7, 12],
          shadowBlur: 8
        }
      },
      bidirectional: relation.bidirectional,
      isConnectedToCenterNode: isConnectedToCenterNode,
      tooltip: {
        formatter: formatEdgeTooltip(relation)
      }
    };
  });
};

// 格式化节点tooltip
const formatNodeTooltip = (data) => {
  const entity = props.entityList.find(e => e.id === data.id);
  if (!entity) return '';
  
  let html = `<div class="graph-tooltip">
    <div class="tooltip-title">${entity.name}</div>
    <div class="tooltip-content">
      <p>关系数: ${data.value}</p>`;
  
  if (entity.description) {
    html += `<p class="tooltip-desc">${entity.description}</p>`;
  }
  
  html += `</div></div>`;
  return html;
};

// 格式化边tooltip
const formatEdgeTooltip = (relation) => {
  const sourceEntity = props.entityList.find(e => e.id === relation.source);
  const targetEntity = props.entityList.find(e => e.id === relation.target);
  
  if (!sourceEntity || !targetEntity) return '';
  
  let html = `<div class="graph-tooltip">
    <div class="tooltip-title">${relation.type}关系</div>
    <div class="tooltip-content">
      <p>${sourceEntity.name} → ${targetEntity.name}</p>`;
  
  if (relation.bidirectional) {
    html += `<p class="tooltip-badge">双向关系</p>`;
  }
  
  if (relation.strength) {
    html += `<p>关系强度: ${relation.strength}</p>`;
  }
  
  if (relation.description) {
    html += `<p class="tooltip-desc">${relation.description}</p>`;
  }
  
  if (relation.tags && relation.tags.length > 0) {
    html += `<div class="tooltip-tags">
      ${relation.tags.map(tag => `<span class="tooltip-tag">${tag}</span>`).join('')}
    </div>`;
  }
  
  html += `</div></div>`;
  return html;
};

// 添加颜色处理助手函数
const lightenColor = (color, percent) => {
  // 简单实现：返回更亮的颜色
  return color; // 占位，图表会自动处理
};

const darkenColor = (color, percent) => {
  // 简单实现：返回更暗的颜色
  return color; // 占位，图表会自动处理
};

// 添加此方法
const handleAddRelation = (nodeId) => {
  if (nodeId) {
    emit('add-relation', nodeId); // 传递节点ID给父组件
  }
};

// 添加图表重置方法
const resetGraph = () => {
  if (chart.value) {
    // 销毁并重新初始化图表
    chart.value.dispose();
    chart.value = null;
    
    // 清除中心节点选择
    centerNodeId.value = null;
    connectedNodeIds.value = new Set();
    
    // 延迟重新初始化以确保DOM更新
    setTimeout(() => {
      initGraph();
    }, 300);
  }
};

// 添加自适应视图方法
const fitView = () => {
  if (chart.value) {
    chart.value.dispatchAction({
      type: 'graphRoam',
      zoom: 1, // 重置缩放级别
      originX: 0,
      originY: 0
    });
    
    // 自动适应内容
    setTimeout(() => {
      const option = chart.value.getOption();
      if (option && option.series && option.series[0]) {
        // 保存当前的布局设置
        const currentLayout = option.series[0].layout;
        const currentForce = option.series[0].force;
        
        // 临时更改为居中布局
        chart.value.setOption({
          series: [{
            roam: true,
            zoom: 1.2,
            layout: 'force',
            force: {
              ...currentForce,
              layoutAnimation: true
            }
          }]
        });
      }
    }, 100);
  }
};

// 处理尺寸预设变化
const handleSizePresetChange = () => {
  const preset = exportSettings.value.sizePreset;
  
  switch (preset) {
    case 'a4-landscape':
      exportSettings.value.width = 2480; // A4 横向 (300dpi)
      exportSettings.value.height = 1754;
      break;
    case 'a4-portrait':
      exportSettings.value.width = 1754; // A4 纵向 (300dpi)
      exportSettings.value.height = 2480;
      break;
    case '16:9':
      exportSettings.value.width = 1920;
      exportSettings.value.height = 1080;
      break;
    case 'square':
      exportSettings.value.width = 1500;
      exportSettings.value.height = 1500;
      break;
  }
};

// 简化 doExportGraph 方法，修复nodeScale未定义的错误
const doExportGraph = async () => {
  if (!chart.value) {
    ElMessage.warning('没有可导出的图谱');
    return;
  }
  
  try {
    exporting.value = true;
    
    // 存储原始状态
    const originalSize = {
      width: graphContainer.value.style.width,
      height: graphContainer.value.style.height
    };
    
    // 提前定义nodeScale，避免未定义错误
    const nodeScale = exportSettings.value.nodeScale;
    
    let imageData;
    
    if (exportSettings.value.exportMode === 'all') {
      console.log('使用全部节点导出模式');
      
      // 创建临时容器
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '-9999px';
      tempContainer.style.width = `${exportSettings.value.width}px`;
      tempContainer.style.height = `${exportSettings.value.height}px`;
      document.body.appendChild(tempContainer);
      
      // 初始化临时图表
      const tempChart = echarts.init(tempContainer, null, {
        renderer: 'canvas',
        width: exportSettings.value.width,
        height: exportSettings.value.height
      });
      
      // 复制当前图表配置
      const option = JSON.parse(JSON.stringify(chart.value.getOption()));
      
      // 调整配置以确保所有节点可见
      if (option.series && option.series.length > 0) {
        // 禁用动画以加快渲染
        option.series[0].animation = false;
        option.series[0].animationDurationUpdate = 0;
        
        // 应用节点大小缩放
        const originalSymbolSize = option.series[0].symbolSize;
        
        if (typeof originalSymbolSize === 'function') {
          // 如果symbolSize是函数，则包装它
          const originalFunc = originalSymbolSize;
          option.series[0].symbolSize = (value, params) => {
            const size = originalFunc(value, params);
            return typeof size === 'number' ? size * nodeScale : size;
          };
        } else if (typeof originalSymbolSize === 'number') {
          // 如果是数字，直接缩放
          option.series[0].symbolSize = originalSymbolSize * nodeScale;
        } else {
          // 如果未定义，设置一个默认值
          option.series[0].symbolSize = 30 * nodeScale;
        }
        
        // 根据节点大小调整力导向图参数
        if (option.series[0].force) {
          option.series[0].force.layoutAnimation = false;
          // 使用二次方公式增加斥力，更好地分开大节点
          option.series[0].force.repulsion = 400 * Math.pow(nodeScale, 2);
          // 使用1.5次方增加边长，提供足够间距
          option.series[0].force.edgeLength = 120 * Math.pow(nodeScale, 1.5);
          // 减小引力，防止节点聚集（节点越大，引力越小）
          option.series[0].force.gravity = 0.1 / Math.max(1, nodeScale);
        }
        
        // 禁用交互和调整缩放
        option.series[0].roam = false;
        option.series[0].zoom = 0.5; // 适当缩小以显示更多内容
      }
      
      // 设置图表选项
      tempChart.setOption(option);
      
      // 等待力导向图布局稳定
      const stabilizationTime = Math.round(600 * Math.max(1, nodeScale / 1.5));
      console.log(`等待布局稳定: ${stabilizationTime}ms，节点缩放: ${nodeScale}`);
      await new Promise(resolve => setTimeout(resolve, stabilizationTime));
      
      // 直接获取图表图像数据
      imageData = tempChart.getDataURL({
        type: 'png',
        pixelRatio: parseInt(exportSettings.value.quality, 10),
        backgroundColor: exportSettings.value.backgroundColor
      });
      
      // 清理临时资源
      tempChart.dispose();
      document.body.removeChild(tempContainer);
      
    } else {
      // 可视区域模式
      console.log('使用可视区域导出模式');
      
      // 在导出前调整节点大小
      const originalOption = chart.value.getOption();
      const tempOption = JSON.parse(JSON.stringify(originalOption));
      
      // 应用节点大小缩放
      if (tempOption.series && tempOption.series.length > 0) {
        const originalSymbolSize = tempOption.series[0].symbolSize;
        
        if (typeof originalSymbolSize === 'function') {
          const originalFunc = originalSymbolSize;
          tempOption.series[0].symbolSize = (value, params) => {
            const size = originalFunc(value, params);
            return typeof size === 'number' ? size * nodeScale : size;
          };
        } else if (typeof originalSymbolSize === 'number') {
          tempOption.series[0].symbolSize = originalSymbolSize * nodeScale;
        } else {
          tempOption.series[0].symbolSize = 30 * nodeScale;
        }
      }
      
      // 应用临时配置
      chart.value.setOption(tempOption);
      
      // 调整容器大小（如果需要）
      if (exportSettings.value.sizePreset !== 'original') {
        graphContainer.value.style.width = `${exportSettings.value.width}px`;
        graphContainer.value.style.height = `${exportSettings.value.height}px`;
        chart.value.resize();
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      // 直接从当前图表获取图像
      imageData = chart.value.getDataURL({
        type: 'png',
        pixelRatio: parseInt(exportSettings.value.quality, 10),
        backgroundColor: exportSettings.value.backgroundColor
      });
      
      // 恢复原始配置
      chart.value.setOption(originalOption);
      
      // 恢复原始容器大小
      if (exportSettings.value.sizePreset !== 'original') {
        graphContainer.value.style.width = originalSize.width;
        graphContainer.value.style.height = originalSize.height;
        chart.value.resize();
      }
    }
    
    // 关闭对话框
    exportDialogVisible.value = false;
    
    // 保存图像
    await saveImageToFile(imageData);
    
  } catch (error) {
    console.error('导出图谱时出错:', error);
    ElMessage.error('导出失败：' + (error.message || '未知错误'));
  } finally {
    exporting.value = false;
  }
};

// 提取保存文件的逻辑到单独方法
const saveImageToFile = async (imageData) => {
  // 获取文件名（使用书名+角色关系图）
  const fileName = `${bookTitle.value || '未知书籍'}_关系图谱.png`;
  
  // 调用后端API获取保存目录
  const response = await window.pywebview.api.select_directory();
  
  const result = typeof response === 'string' ? JSON.parse(response) : response;
  
  // 如果用户取消，则退出
  if (!result || result.status !== 'success' || !result.data) {
    ElMessage.info('已取消导出');
    return;
  }
  
  // 构建完整的文件路径
  const directory = result.data;
  const filePath = `${directory}/${fileName}`;
  
  // 使用与实体卡片相同的保存方法
  const saveResponse = await window.pywebview.api.save_entity_card({
    file_path: filePath,
    image_data: imageData
  });
  
  // 处理后端返回的JSON字符串
  const saveResult = typeof saveResponse === 'string' ? JSON.parse(saveResponse) : saveResponse;
  
  if (saveResult && saveResult.status === 'success') {
    ElMessage.success(`关系图谱已成功导出至: ${filePath}`);
    
    // 可选：打开文件所在位置
    try {
      await window.pywebview.api.open_directory(directory);
    } catch (error) {
      console.warn('无法打开文件目录:', error);
    }
  } else {
    ElMessage.error('导出失败：' + (saveResult?.message || '未知错误'));
  }
};

// 添加缺失的 exportGraph 函数，用于打开导出设置对话框
const exportGraph = () => {
  if (!chart.value) {
    ElMessage.warning('没有可导出的图谱');
    return;
  }
  
  // 显示导出设置对话框
  exportDialogVisible.value = true;
};

// 更新图表布局
const updateGraphLayout = () => {
  if (!chart.value) return;
  
  loading.value = true;
  
  try {
    // 获取当前图表配置
    const option = chart.value.getOption();
    
    // 根据选择的布局类型更新配置
    switch (graphLayout.value) {
      case 'circular':
        // 环形布局
        option.series[0].layout = 'circular';
        option.series[0].circular = {
          rotateLabel: true
        };
        // 禁用力导向相关配置
        option.series[0].force = null;
        break;
        
      case 'grid':
        // 网格布局
        option.series[0].layout = 'none'; // 先设为none以清除其他布局
        
        // 计算行列数: 尝试接近正方形网格
        const nodeCount = option.series[0].data.length;
        const cols = Math.ceil(Math.sqrt(nodeCount));
        const rows = Math.ceil(nodeCount / cols);
        
        // 设置网格位置
        const gridWidth = graphContainer.value.clientWidth * 0.8;
        const gridHeight = graphContainer.value.clientHeight * 0.8;
        const cellWidth = gridWidth / (cols - 1 || 1);
        const cellHeight = gridHeight / (rows - 1 || 1);
        const startX = (graphContainer.value.clientWidth - gridWidth) / 2;
        const startY = (graphContainer.value.clientHeight - gridHeight) / 2;
        
        // 为每个节点设置固定位置
        option.series[0].data.forEach((node, index) => {
          const row = Math.floor(index / cols);
          const col = index % cols;
          node.x = startX + col * cellWidth;
          node.y = startY + row * cellHeight;
          node.fixed = true; // 固定节点位置
        });
        
        // 处理边，确保它们有正确的布局信息
        if (option.series[0].links && option.series[0].links.length > 0) {
          // 关闭布局动画，防止布局计算错误
          option.series[0].animation = false;
          option.series[0].animationDurationUpdate = 0;
          
          // 处理边的布局
          option.series[0].links.forEach(link => {
            // 确保边有基本属性
            if (!link.lineStyle) {
              link.lineStyle = {};
            }
            
            // 添加曲度以避免边重叠
            link.lineStyle.curveness = 0.1;
            
            // 移除可能导致问题的布局相关属性
            if (link.force) delete link.force;
          });
          
          // 为安全起见，强制使用直线布局而非力导向布局
          option.series[0].edgeLayout = 'none';
        }
        
        // 禁用力导向相关配置
        option.series[0].force = null;
        break;
        
      case 'force':
      default:
        // 力导向布局 (默认)
        option.series[0].layout = 'force';
        option.series[0].force = {
          repulsion: 300,
          gravity: 0.1,
          edgeLength: 100,
          layoutAnimation: true
        };
        
        // 解除固定位置
        option.series[0].data.forEach(node => {
          node.fixed = false;
        });
        break;
    }
    
    // 应用新配置
    chart.value.setOption(option);
  } catch (err) {
    console.error('更新布局时出错:', err);
    ElMessage.error('更新布局失败，请尝试其他布局方式');
    
    // 回退到力导向布局
    graphLayout.value = 'force';
    setTimeout(() => {
      const fallbackOption = {
        series: [{
          layout: 'force',
          force: {
            repulsion: 300,
            gravity: 0.1,
            edgeLength: 100,
            layoutAnimation: true
          }
        }]
      };
      chart.value.setOption(fallbackOption);
    }, 100);
  }
  
  // 添加短暂延迟以让布局动画完成
  setTimeout(() => {
    loading.value = false;
  }, 800);
};

// 显示图例对话框
const showLegend = () => {
  legendDialogVisible.value = true;
};

// 专门用于处理网格布局的函数
const applyGridLayout = (graphData) => {
  if (!chart.value || !graphData.nodes || graphData.nodes.length === 0) return;
  
  // 首先确保没有任何布局算法在运行
  chart.value.setOption({
    series: [{
      layout: 'none',
      animation: false,
      animationDurationUpdate: 0,
      force: null
    }]
  });
  
  // 暂停一下，确保之前的配置已应用
  setTimeout(() => {
    // 计算网格布局
    const nodeCount = graphData.nodes.length;
    const cols = Math.ceil(Math.sqrt(nodeCount));
    const rows = Math.ceil(nodeCount / cols);
    
    // 设置网格位置
    const width = graphContainer.value.clientWidth;
    const height = graphContainer.value.clientHeight;
    const gridWidth = width * 0.8;
    const gridHeight = height * 0.8;
    const cellWidth = gridWidth / (cols > 1 ? cols - 1 : 1);
    const cellHeight = gridHeight / (rows > 1 ? rows - 1 : 1);
    const startX = (width - gridWidth) / 2;
    const startY = (height - gridHeight) / 2;
    
    // 创建节点数据，设置固定位置
    const nodes = graphData.nodes.map((node, index) => {
      // 处理每个节点
      let nodeData = {
        id: node.id,
        name: node.name,
        itemStyle: getEntityStyle(node.name),  // 应用原有的样式
        symbolSize: node.symbolSize || 36,
      };
      
      // 网格位置计算
      const row = Math.floor(index / cols);
      const col = index % cols;
      nodeData.x = startX + col * cellWidth;
      nodeData.y = startY + row * cellHeight;
      nodeData.fixed = true;  // 固定位置
      
      return {
        ...nodeData
      };
    });
    
    // 创建边数据
    // 过滤和处理边数据
    const links = [];
    graphData.links.forEach(link => {
      // 确保源节点和目标节点都存在
      const sourceExists = nodes.some(node => node.id === link.source);
      const targetExists = nodes.some(node => node.id === link.target);
      
      if (sourceExists && targetExists) {
        // 创建边配置
        const linkStyle = getRelationStyle(link.type);
        links.push({
          source: link.source,
          target: link.target,
          relation: link.name,
          type: link.type,
          lineStyle: {
            ...linkStyle,
            curveness: 0.1,  // 添加弯曲度
          },
          label: {
            show: true,
            formatter: link.name
          }
        });
      }
    });
    
    // 更新图表数据，使用安全的方式
    chart.value.setOption({
      series: [{
        data: nodes,
        links: links
      }]
    });
  }, 100);
};

// 获取实体节点样式
const getEntityStyle = (entityName) => {
  // 根据实体名称生成固定颜色
  const color = getEntityColor(entityName);
  return {
    color: color,
    borderColor: '#fff',
    borderWidth: 2
  };
};

// 获取关系样式
const getRelationStyle = (relationType) => {
  switch (relationType) {
    case 'friendly':
      return { color: '#67C23A', width: 3 };
    case 'hostile':
      return { color: '#F56C6C', width: 3, type: 'dashed' };
    case 'family':
      return { color: '#E6A23C', width: 3 };
    case 'romance':
      return { color: '#FF88A0', width: 3 };
    default:
      return { color: '#909399', width: 2 };
  }
};

// 绑定图表事件
const bindGraphEvents = () => {
  if (!chart.value) return;
  
  // 添加必要的事件监听
  chart.value.on('click', (params) => {
    // 节点点击事件
    if (params.dataType === 'node') {
      handleNodeClick(params);
    } else if (params.dataType === 'edge') {
      handleEdgeClick(params);
    }
  });
  
  // 添加缩放完成事件
  chart.value.on('restored', () => {
    fitView();
  });
};
</script>

<style lang="scss" scoped>
.fullscreen-graph-dialog {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  :deep(.el-dialog) {
    margin: 0 !important;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  :deep(.el-dialog__header) {
    display: none;
  }
  
  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0;
    height: 100vh;
    overflow: hidden;
  }
}

.dialog-content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-gradient);
}

.graph-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
  height: 60px;
  flex-wrap: nowrap;
}

.toolbar-section {
  display: flex;
  align-items: center;
  height: 100%;
}

.title-section {
  flex: 0 0 auto;
  margin-right: 20px;
}

.view-section {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.action-section {
  flex: 0 0 auto;
  margin-left: auto;
  justify-content: flex-end;
}

.toolbar-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  white-space: nowrap;
}

.dynamic-controls {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.control-label {
  margin-right: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
}

.template-selector,
.center-node-selector {
  min-width: 120px;
  max-width: 200px;
  margin-right: 10px;
}

.view-actions {
  margin: 0 16px;
}

.fullscreen-graph-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  
  .graph-content {
    flex: 1;
    width: 100%;
    height: 100%;
  }
  
  .empty-graph {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .empty-icon {
      font-size: 64px;
      color: var(--text-color-secondary);
      margin-bottom: 16px;
      opacity: 0.4;
    }
    
    .empty-text {
      font-size: 16px;
      color: var(--text-color-secondary);
    }
  }
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 5;
    
    .loading-icon {
      font-size: 32px;
      color: var(--primary-color);
      animation: rotate 1.5s linear infinite;
      margin-bottom: 12px;
    }
  }
}

.graph-legend-dialog {
  padding: 10px 0;
  
  .legend-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .legend-section-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 16px 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    min-width: 120px;
    
    .connection-line {
      width: 36px;
      height: 3px;
      border-radius: 3px;
      position: relative;
      
      &.friendly {
        background: #67C23A;
      }
      
      &.hostile {
        background: #F56C6C;
        border-top: 1px dashed #F56C6C;
      }
      
      &.family {
        background: #E6A23C;
      }
      
      &.romance {
        background: #FF88A0;
      }
      
      &.other {
        background: #909399;
      }
      
      &.unidirectional, &.bidirectional {
        background: #409EFF;
      }
    }
    
    .node-sample {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      
      &.selected {
        background: #409EFF;
      }
      
      &.related {
        background: #B3D8FF;
      }
      
      &.center-node {
        background: #F56C6C;
        border: 2px solid #fff;
        box-shadow: 0 0 0 1px #F56C6C;
      }
      
      &.connected-node {
        background: #FFB6C1;
      }
    }
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 暗黑模式适配 */
html.dark {
  .fullscreen-graph-dialog .loading-overlay {
    background: rgba(0, 0, 0, 0.6);
  }
}

/* Tooltip样式 */
:deep(.graph-tooltip) {
  .tooltip-title {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .tooltip-content {
    font-size: 12px;
    
    p {
      margin: 4px 0;
    }
    
    .tooltip-desc {
      font-style: italic;
      opacity: 0.8;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .tooltip-badge {
      display: inline-block;
      background: rgba(64, 158, 255, 0.1);
      color: #409EFF;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
    }
  }
  
  .tooltip-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
    
    .tooltip-tag {
      font-size: 10px;
      background: rgba(0, 0, 0, 0.05);
      padding: 1px 5px;
      border-radius: 8px;
    }
  }
}

/* 添加节点样式图例 */
.legend-separator {
  width: 1px;
  height: 20px;
  background: var(--border-color);
  margin: 0 8px;
}

.node-sample {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  
  &.selected {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border: 2px solid rgba(255, 255, 255, 0.9);
  }
  
  &.related {
    background: linear-gradient(135deg, #757575, #424242);
    border: 2px solid rgba(255, 255, 255, 0.6);
  }
  
  &.center-node {
    background: linear-gradient(135deg, #FFC107, #FF8F00);
    border: 3px solid rgba(255, 215, 0, 0.9);
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
  }
  
  &.connected-node {
    background: linear-gradient(135deg, #64B5F6, #1976D2);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}

/* 关系线标签样式 */
.connection-line {
  width: 36px;
  height: 3px;
  border-radius: 3px;
  position: relative;
  
  &.unidirectional, &.bidirectional {
    background: #909399;
    display: flex;
    align-items: center;
  }
  
  .arrow-end {
    position: absolute;
    right: -2px;
    width: 0; 
    height: 0; 
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 6px solid #909399;
  }
  
  .arrow-start {
    position: absolute;
    left: -2px;
    width: 0; 
    height: 0; 
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-right: 6px solid #909399;
  }
}

/* 关系标签悬停样式 */
:deep(.el-tooltip__popper) {
  border-radius: 8px !important;
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 添加"添加关系"按钮样式 */
.add-relation-btn {
  margin-right: 16px;
}

/* 中心节点选择器样式 */
.center-node-selector {
  min-width: 180px;
  margin-left: 16px;
  margin-right: 8px;
}

.clear-center-btn {
  padding: 5px 10px;
  font-size: 12px;
}

/* 添加实体选项样式 */
.entity-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.entity-color-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

/* 优化中心节点选择器样式 */
.center-node-selector {
  min-width: 180px;
  margin-left: 16px;
  margin-right: 8px;
}

:deep(.el-select-dropdown__item) {
  padding: 0 12px;
}

:deep(.el-select-group__title) {
  font-size: 12px;
  font-weight: bold;
  padding-top: 8px;
  color: var(--el-text-color-secondary);
}

/* 优化双向关系箭头样式 */
.connection-line {
  &.bidirectional {
    background: #409EFF;
    position: relative;
    
    .arrow-start, .arrow-end {
      border-color: transparent;
      border-width: 5px;
    }
    
    .arrow-start {
      border-right-color: #409EFF;
      left: -6px;
    }
    
    .arrow-end {
      border-left-color: #409EFF;
      right: -6px;
    }
  }
}

/* 导出设置样式 */
.export-settings {
  padding: 10px 0;
  
  .setting-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    
    .setting-label {
      width: 80px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    .el-select, .el-color-picker {
      width: 220px;
    }
  }
  
  .custom-size {
    display: flex;
    flex: 1;
    gap: 16px;
    
    .size-input {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      span {
        font-size: 13px;
        color: var(--el-text-color-secondary);
      }
      
      .el-input-number {
        width: 100%;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.slider-with-value {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  
  .node-scale-slider {
    flex: 1;
  }
  
  .scale-value {
    min-width: 45px;
    text-align: right;
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }
}

/* 布局选择器样式 */
.layout-selector {
  display: flex;
  align-items: center;
  margin-right: 16px;
  
  span {
    font-size: 14px;
    margin-right: 8px;
    color: var(--el-text-color-regular);
  }
  
  .el-select {
    width: 130px;
  }
}

/* 添加禁用状态的样式 */
.template-selector:disabled,
.center-node-selector:disabled {
  opacity: 0.7;
}
</style> 