<template>
  <div class="universal-selector" :class="{ 'is-open': isOpen }" ref="selectorRef">
    <div class="selector-trigger" @click="toggleDropdown">
      <span class="selected-text">{{ displayText }}</span>
      <el-icon class="dropdown-icon" :class="{ 'is-active': isOpen }">
        <ArrowDown />
      </el-icon>
    </div>

    <div class="selector-dropdown" v-if="isOpen" :style="dropdownStyle">
      <!-- 头部 -->
      <div class="dropdown-header" v-if="showHeader">
        <span class="header-title">{{ headerTitle }}</span>
        <button 
          v-if="multiple && selectedValues.length > 0" 
          class="clear-btn" 
          @click="clearAll"
          title="清空选择"
        >
          <el-icon><Close /></el-icon>
        </button>
      </div>

      <!-- 搜索框 -->
      <div class="search-box" v-if="searchable">
        <el-input
          v-model="searchQuery"
          placeholder="搜索..."
          size="small"
          clearable
          @click.stop
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 选项列表 -->
      <div class="options-container" ref="optionsContainer">
        <div
          v-for="option in filteredOptions"
          :key="option.value"
          class="option-item"
          :class="{ 
            'is-selected': isSelected(option.value),
            'is-disabled': option.disabled 
          }"
          @click="selectOption(option)"
        >
          <!-- 多选复选框 -->
          <div v-if="multiple" class="option-checkbox">
            <el-icon v-if="isSelected(option.value)" class="check-icon">
              <Check />
            </el-icon>
          </div>

          <!-- 选项内容 -->
          <div class="option-content">
            <div class="option-label">{{ option.label }}</div>
            <div v-if="option.description" class="option-description">
              {{ option.description }}
            </div>
            <div v-if="option.provider" class="option-provider">
              {{ option.provider }}
            </div>
          </div>

          <!-- 单选选中标识 -->
          <div v-if="!multiple && isSelected(option.value)" class="selected-indicator">
            <el-icon><Check /></el-icon>
          </div>
        </div>

        <!-- 无选项提示 -->
        <div v-if="filteredOptions.length === 0" class="no-options">
          {{ searchQuery ? '未找到匹配项' : '暂无选项' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ArrowDown, Close, Search, Check } from '@element-plus/icons-vue'

const props = defineProps({
  // 基础属性
  modelValue: {
    type: [String, Array],
    default: () => []
  },
  options: {
    type: Array,
    default: () => []
  },
  multiple: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  
  // 显示属性
  showHeader: {
    type: Boolean,
    default: true
  },
  headerTitle: {
    type: String,
    default: '请选择'
  },
  searchable: {
    type: Boolean,
    default: false
  },
  
  // 样式属性
  maxHeight: {
    type: String,
    default: '300px'
  },
  width: {
    type: String,
    default: '100%'
  },
  
  // 功能属性
  clearable: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'clear'])

// 响应式数据
const isOpen = ref(false)
const searchQuery = ref('')
const optionsContainer = ref(null)

// 计算属性
const selectedValues = computed(() => {
  if (props.multiple) {
    return Array.isArray(props.modelValue) ? props.modelValue : []
  }
  return props.modelValue ? [props.modelValue] : []
})

const filteredOptions = computed(() => {
  if (!searchQuery.value) return props.options
  
  const query = searchQuery.value.toLowerCase()
  return props.options.filter(option => 
    option.label.toLowerCase().includes(query) ||
    (option.description && option.description.toLowerCase().includes(query)) ||
    (option.provider && option.provider.toLowerCase().includes(query))
  )
})

const displayText = computed(() => {
  if (selectedValues.value.length === 0) {
    return props.placeholder
  }
  
  if (props.multiple) {
    if (selectedValues.value.length === 1) {
      const option = props.options.find(opt => opt.value === selectedValues.value[0])
      return option ? option.label : selectedValues.value[0]
    }
    return `已选择 ${selectedValues.value.length} 项`
  } else {
    const option = props.options.find(opt => opt.value === props.modelValue)
    return option ? option.label : props.modelValue
  }
})

const dropdownStyle = computed(() => ({
  maxHeight: props.maxHeight,
  width: props.width
}))

// 方法
const toggleDropdown = () => {
  if (props.disabled) return
  isOpen.value = !isOpen.value
  
  if (isOpen.value) {
    nextTick(() => {
      // 滚动到选中项
      scrollToSelected()
    })
  }
}

const selectOption = (option) => {
  if (option.disabled) return
  
  if (props.multiple) {
    const newValues = [...selectedValues.value]
    const index = newValues.indexOf(option.value)
    
    if (index > -1) {
      newValues.splice(index, 1)
    } else {
      newValues.push(option.value)
    }
    
    emit('update:modelValue', newValues)
    emit('change', newValues, option)
  } else {
    emit('update:modelValue', option.value)
    emit('change', option.value, option)
    isOpen.value = false
  }
}

const isSelected = (value) => {
  return selectedValues.value.includes(value)
}

const clearAll = () => {
  const newValue = props.multiple ? [] : ''
  emit('update:modelValue', newValue)
  emit('clear')
  emit('change', newValue)
}

const scrollToSelected = () => {
  if (!optionsContainer.value || selectedValues.value.length === 0) return
  
  const selectedElement = optionsContainer.value.querySelector('.option-item.is-selected')
  if (selectedElement) {
    selectedElement.scrollIntoView({ block: 'nearest' })
  }
}

// 点击外部关闭
const selectorRef = ref(null)

const handleClickOutside = (event) => {
  if (selectorRef.value && !selectorRef.value.contains(event.target)) {
    isOpen.value = false
    searchQuery.value = ''
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 监听搜索查询变化
watch(searchQuery, () => {
  nextTick(() => {
    scrollToSelected()
  })
})
</script>

<style lang="scss" scoped>
.universal-selector {
  position: relative;
  width: 100%;
  user-select: none;

  .selector-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px;

    &:hover {
      border-color: var(--el-color-primary-light-5);
    }

    .selected-text {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
    }

    .dropdown-icon {
      margin-left: 8px;
      transition: transform 0.2s ease;
      flex-shrink: 0;

      &.is-active {
        transform: rotate(180deg);
      }
    }
  }

  &.is-open .selector-trigger {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
  }

  .selector-dropdown {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    right: 0;
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999; /* 使用更高的z-index确保在最顶层 */
    overflow: hidden;
    animation: dropdown-fade 0.2s ease-out;

    .dropdown-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background-color: var(--el-fill-color-light);

      .header-title {
        font-size: 12px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }

      .clear-btn {
        background: none;
        border: none;
        cursor: pointer;
        color: var(--el-text-color-secondary);
        padding: 2px;
        border-radius: 2px;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--el-fill-color);
          color: var(--el-color-danger);
        }
      }
    }

    .search-box {
      padding: 8px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    .options-container {
      max-height: inherit;
      overflow-y: auto;
      
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--el-border-color);
        border-radius: 2px;
      }

      .option-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover:not(.is-disabled) {
          background-color: var(--el-fill-color-light);
        }

        &.is-selected {
          background-color: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
        }

        &.is-disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .option-checkbox {
          width: 16px;
          height: 16px;
          border: 1px solid var(--el-border-color);
          border-radius: 2px;
          margin-right: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .check-icon {
            color: var(--el-color-primary);
            font-size: 12px;
          }
        }

        .option-content {
          flex: 1;
          min-width: 0;

          .option-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .option-description {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            margin-top: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .option-provider {
            font-size: 11px;
            color: var(--el-text-color-placeholder);
            margin-top: 1px;
          }
        }

        .selected-indicator {
          margin-left: 8px;
          color: var(--el-color-primary);
          flex-shrink: 0;
        }
      }

      .no-options {
        padding: 16px;
        text-align: center;
        color: var(--el-text-color-placeholder);
        font-size: 14px;
      }
    }
  }
}

@keyframes dropdown-fade {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
