// Vue Markdown Editor 全局配置

// 基础编辑器
import VueMarkdownEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'

// 预览组件
import VMdPreview from '@kangc/v-md-editor/lib/preview'
import '@kangc/v-md-editor/lib/style/preview.css'

// CodeMirror 编辑器（进阶版）
import VMdEditor from '@kangc/v-md-editor/lib/codemirror-editor'
import '@kangc/v-md-editor/lib/style/codemirror-editor.css'

// 语法高亮
import Prism from 'prismjs'

// CodeMirror 相关导入
import Codemirror from 'codemirror'
import 'codemirror/mode/markdown/markdown'
import 'codemirror/mode/javascript/javascript'
import 'codemirror/mode/css/css'
import 'codemirror/mode/htmlmixed/htmlmixed'
import 'codemirror/mode/vue/vue'
import 'codemirror/addon/edit/closebrackets'
import 'codemirror/addon/edit/closetag'
import 'codemirror/addon/edit/matchbrackets'
import 'codemirror/addon/display/placeholder'
import 'codemirror/addon/selection/active-line'
import 'codemirror/addon/scroll/simplescrollbars'
import 'codemirror/addon/scroll/simplescrollbars.css'
import 'codemirror/lib/codemirror.css'

// 配置标志，避免重复配置
let isConfigured = false

function configureEditors() {
  if (isConfigured) return

  try {
    // 配置基础编辑器（使用 vuepress 主题）
    VueMarkdownEditor.use(vuepressTheme, {
      Prism,
    })

    // 配置预览组件（使用 vuepress 主题）
    VMdPreview.use(vuepressTheme, {
      Prism,
    })

    // 配置 CodeMirror 编辑器
    VMdEditor.Codemirror = Codemirror
    VMdEditor.use(vuepressTheme, {
      Prism,
    })

    isConfigured = true
  } catch (error) {
    console.error('Vue Markdown Editor 配置失败:', error)
  }
}

// 立即配置
configureEditors()

export { VueMarkdownEditor, VMdEditor, VMdPreview }
