<template>
  <div class="custom-pool-card-exporter">
    <div class="exporter-container">
      <!-- 左侧控制面板 -->
      <div class="control-panel">
        <!-- 固定头部 -->
        <div class="panel-header">
          <h3 class="panel-title">卡片导出设置</h3>
          <div class="export-info">
            <span class="card-count">{{ cards.length }} 张卡片</span>
          </div>
        </div>

        <!-- 固定的主题选择标题 -->
        <div class="theme-section-header">
          <div class="section-title">选择主题风格</div>
        </div>

        <!-- 可滚动的主题列表 -->
        <div class="theme-list-container">
          <div class="theme-list">
            <div
              v-for="theme in availableThemes"
              :key="theme.id"
              class="theme-item"
              :class="{ active: currentTheme === theme.id }"
              @click="currentTheme = theme.id"
            >
              <div class="theme-preview"
                   :style="{
                     background: theme.colors.background,
                     borderColor: currentTheme === theme.id ? theme.colors.accent : 'transparent'
                   }">
                <div class="theme-card" :style="{ background: theme.colors.cardBackground }"></div>
                <div class="theme-accent" :style="{ background: theme.colors.accent }"></div>
              </div>
              <div class="theme-name">{{ theme.name }}</div>
            </div>
          </div>
        </div>

        <!-- 固定的导出选项 -->
        <div class="export-options-section">
          <div class="section-title">导出选项</div>
          <div class="option-list">
            <label class="option-item">
              <input type="checkbox" v-model="includePoolInfo" />
              <span class="option-text">包含卡池信息</span>
            </label>
            <label class="option-item">
              <input type="checkbox" v-model="includeDimensions" />
              <span class="option-text">包含属性维度</span>
            </label>
            <div class="option-item">
              <span class="option-label">图片质量:</span>
              <select v-model="imageQuality" class="quality-select">
                <option value="1">标准</option>
                <option value="2">高清</option>
                <option value="3">超清</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 固定底部按钮 -->
        <div class="panel-footer">
          <button
            class="export-btn"
            :class="{ loading: exporting }"
            :disabled="cards.length === 0 || exporting"
            @click="exportAllCards"
          >
            {{ exporting ? '导出中...' : `导出全部 ${cards.length} 张卡片` }}
          </button>
        </div>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-panel">
        <!-- 固定头部 -->
        <div class="preview-header">
          <h3 class="panel-title">卡片预览</h3>
          <div class="preview-controls">
            <el-button-group size="small">
              <el-button 
                :type="previewIndex > 0 ? 'primary' : 'default'"
                :disabled="previewIndex <= 0"
                @click="previewIndex--"
              >
                <el-icon><ArrowLeft /></el-icon>
              </el-button>
              <el-button disabled>
                {{ previewIndex + 1 }} / {{ cards.length }}
              </el-button>
              <el-button 
                :type="previewIndex < cards.length - 1 ? 'primary' : 'default'"
                :disabled="previewIndex >= cards.length - 1"
                @click="previewIndex++"
              >
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 可滚动预览内容 -->
        <div class="preview-content" ref="previewContent">
          <div v-if="currentPreviewCard" class="preview-container">
            <!-- 固定的卡片信息区域 -->
            <div class="card-info">
              <h4>{{ getNumberPrefix(previewIndex + 1, cards.length) }}. {{ currentPreviewCard.title }}</h4>
              <span class="pool-name">来自: {{ poolName }}</span>
              <span class="stat-item">
                <el-icon><Document /></el-icon>
                {{ Object.keys(currentPreviewCard.properties || {}).length }} 个属性
              </span>
              <span class="stat-item">
                <el-icon><Star /></el-icon>
                {{ currentTheme }} 主题
              </span>
            </div>

            <!-- 可滚动的预览图片区域 -->
            <div class="preview-image-container">
              <img v-if="previewUrl" :src="previewUrl" alt="卡片预览" class="preview-image" />
              <div v-else class="generating-preview">
                <div class="loading-spinner"></div>
                <p>生成预览中...</p>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <div class="empty-icon">📄</div>
            <p class="empty-text">没有可预览的卡片</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage, ElButton, ElButtonGroup, ElIcon, ElLoading } from 'element-plus';
import { ArrowLeft, ArrowRight, Document, Star } from '@element-plus/icons-vue';
import html2canvas from 'html2canvas';

import { useConfigStore } from '@/stores/config';

const props = defineProps({
  cards: {
    type: Array,
    required: true,
    default: () => []
  },
  poolName: {
    type: String,
    default: '未知卡池'
  },
  poolInfo: {
    type: Object,
    default: () => ({})
  },
  dimensions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['export-success', 'close']);

// 使用配置store
const configStore = useConfigStore();

// 状态变量
const currentTheme = ref('classic');
const previewIndex = ref(0);
const previewUrl = ref('');
const exporting = ref(false);
const exportProgress = ref(0);
const currentExportingCard = ref('');
const exportCancelled = ref(false);
let loadingInstance = null;

// 导出选项
const includePoolInfo = ref(true);
const includeDimensions = ref(true);
const imageQuality = ref('3');

// 当前预览的卡片
const currentPreviewCard = computed(() => {
  return props.cards[previewIndex.value] || null;
});

// 定义增强的内置主题（与EntityCardExporter保持一致）
const builtinThemes = [
  {
    id: 'classic',
    name: '经典',
    colors: {
      background: 'linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%)',
      textPrimary: '#333333',
      textSecondary: '#666666',
      textTertiary: '#999999',
      cardBackground: '#ffffff',
      accent: '#409EFF',
      accentLight: '#ecf5ff',
      avatarBackground: '#4a93ff',
      border: '#eaeaea',
      borderLight: '#f0f0f0',
      sectionBackground: '#f9f9f9',
      tagBackground: '#f0f9ff',
      tagText: '#1890ff'
    },
    fonts: {
      primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '24px',
      sectionGap: '20px',
      itemGap: '12px'
    },
    borderRadius: '12px',
    shadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
    cardShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
    backgroundPattern: {
      type: 'geometric',
      opacity: 0.08,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="classicPattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
            <circle cx="30" cy="30" r="20" fill="none" stroke="#409EFF" stroke-width="0.5" opacity="0.3"/>
            <circle cx="30" cy="30" r="10" fill="none" stroke="#409EFF" stroke-width="0.3" opacity="0.5"/>
            <path d="M15,15 L45,45 M45,15 L15,45" stroke="#409EFF" stroke-width="0.2" opacity="0.2"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#classicPattern)"/>
      </svg>`
    }
  },
  {
    id: 'dark',
    name: '暗黑',
    colors: {
      background: 'linear-gradient(135deg, #2d3748 0%, #1a202c 100%)',
      textPrimary: '#e2e8f0',
      textSecondary: '#a0aec0',
      textTertiary: '#718096',
      cardBackground: '#2d3748',
      accent: '#63b3ed',
      accentLight: '#2a4a6b',
      avatarBackground: '#4299e1',
      border: '#4a5568',
      borderLight: '#3a4a5c',
      sectionBackground: '#283141',
      tagBackground: '#2a4a6b',
      tagText: '#90cdf4'
    },
    fonts: {
      primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '24px',
      sectionGap: '20px',
      itemGap: '12px'
    },
    borderRadius: '12px',
    shadow: '0 8px 24px rgba(0, 0, 0, 0.3)',
    cardShadow: '0 2px 12px rgba(0, 0, 0, 0.2)',
    backgroundPattern: {
      type: 'circuit',
      opacity: 0.05,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="darkPattern" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
            <rect x="20" y="20" width="40" height="40" fill="none" stroke="#63b3ed" stroke-width="0.5" opacity="0.3"/>
            <circle cx="40" cy="40" r="15" fill="none" stroke="#63b3ed" stroke-width="0.3" opacity="0.4"/>
            <line x1="0" y1="40" x2="80" y2="40" stroke="#63b3ed" stroke-width="0.2" opacity="0.2"/>
            <line x1="40" y1="0" x2="40" y2="80" stroke="#63b3ed" stroke-width="0.2" opacity="0.2"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#darkPattern)"/>
      </svg>`
    }
  },
  {
    id: 'creative',
    name: '灵感',
    colors: {
      background: 'linear-gradient(135deg, #fdf6fd 0%, #f3e7ff 100%)',
      textPrimary: '#4a2a5d',
      textSecondary: '#7b5a8c',
      textTertiary: '#a78bba',
      cardBackground: '#fcf8ff',
      accent: '#9c6bdf',
      accentLight: '#f3e7ff',
      avatarBackground: '#7956b3',
      border: '#e6d8f8',
      borderLight: '#f0e8ff',
      sectionBackground: '#f8f0ff',
      tagBackground: '#f3e7ff',
      tagText: '#8b5cf6'
    },
    fonts: {
      primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '24px',
      sectionGap: '20px',
      itemGap: '12px'
    },
    borderRadius: '12px',
    shadow: '0 8px 24px rgba(156, 107, 223, 0.15)',
    cardShadow: '0 2px 12px rgba(156, 107, 223, 0.1)',
    backgroundPattern: {
      type: 'artistic',
      opacity: 0.06,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="creativePattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
            <path d="M50,10 Q70,30 50,50 Q30,70 50,90 Q70,70 90,50 Q70,30 50,10" fill="none" stroke="#9c6bdf" stroke-width="0.5" opacity="0.3"/>
            <circle cx="25" cy="25" r="8" fill="none" stroke="#9c6bdf" stroke-width="0.3" opacity="0.4"/>
            <circle cx="75" cy="75" r="8" fill="none" stroke="#9c6bdf" stroke-width="0.3" opacity="0.4"/>
            <path d="M10,50 Q30,30 50,50 Q70,70 90,50" fill="none" stroke="#9c6bdf" stroke-width="0.2" opacity="0.2"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#creativePattern)"/>
      </svg>`
    }
  }
];

// 获取自定义主题
const customThemes = computed(() => {
  const themes = configStore.state.config.customThemes || [];
  return themes;
});

// 合并内置主题和自定义主题
const availableThemes = computed(() => {
  const available = [...customThemes.value, ...builtinThemes];
  return available;
});

// 默认主题字段
const defaultThemeFields = {
  spacing: {
    cardPadding: '24px',
    sectionGap: '20px',
    itemGap: '12px'
  },
  fonts: {
    primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
    title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
  },
  borderRadius: '12px',
  shadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
  cardShadow: '0 2px 12px rgba(0, 0, 0, 0.08)'
};

// 获取当前主题配置，确保所有字段都存在
const theme = computed(() => {
  const baseTheme = availableThemes.value.find(t => t.id === currentTheme.value) || availableThemes.value[0];

  // 合并默认字段，确保不会有undefined
  return {
    ...defaultThemeFields,
    ...baseTheme,
    spacing: {
      ...defaultThemeFields.spacing,
      ...(baseTheme.spacing || {})
    },
    fonts: {
      ...defaultThemeFields.fonts,
      ...(baseTheme.fonts || {})
    },
    colors: {
      background: 'linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%)',
      textPrimary: '#333333',
      textSecondary: '#666666',
      textTertiary: '#999999',
      cardBackground: '#ffffff',
      accent: '#409EFF',
      accentLight: '#ecf5ff',
      avatarBackground: '#4a93ff',
      border: '#eaeaea',
      borderLight: '#f0f0f0',
      sectionBackground: '#f9f9f9',
      tagBackground: '#f0f9ff',
      tagText: '#1890ff',
      ...(baseTheme.colors || {})
    }
  };
});

// 根据维度ID获取维度名称
const getDimensionName = (dimensionId) => {
  const dimension = props.dimensions.find(d => d.id === dimensionId);
  return dimension ? dimension.name : dimensionId;
};

// 根据维度ID获取维度描述
const getDimensionDescription = (dimensionId) => {
  const dimension = props.dimensions.find(d => d.id === dimensionId);
  return dimension ? dimension.description : '';
};

// 格式化属性值显示
const formatPropertyValue = (value, dimensionId) => {
  const dimension = props.dimensions.find(d => d.id === dimensionId);

  if (!dimension) return value;

  // 根据维度类型格式化显示
  switch (dimension.type) {
    case 'array':
      if (Array.isArray(value)) {
        if (value.length === 0) return '暂无';
        // 为数组项添加美化显示，使用当前主题色彩
        return value.map((item, index) =>
          `<span style="
            display: inline-block;
            background: ${theme.value.colors.tagBackground};
            color: ${theme.value.colors.tagText};
            padding: 3px 10px;
            margin: 2px 4px 2px 0;
            border-radius: 12px;
            font: ${theme.value.fonts.caption};
            font-weight: 500;
            border: 1px solid ${theme.value.colors.accentLight};
          ">${escapeHtml(item)}</span>`
        ).join('');
      }
      return escapeHtml(value);
    case 'boolean':
      return value ? '是' : '否';
    case 'text':
      // 处理多行文本，保留换行
      if (typeof value === 'string' && value.includes('\n')) {
        return escapeHtml(value).replace(/\n/g, '<br>');
      }
      return escapeHtml(value || '暂无');
    case 'select':
    case 'number':
    case 'date':
    case 'color':
    default:
      return escapeHtml(value || '暂无');
  }
};

// HTML转义函数
const escapeHtml = (text) => {
  if (!text) return '';
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
};

// 缓存DOM容器以提高性能
let cachedCardContainer = null;
let cachedThemeId = null; // 缓存当前主题ID

// 生成单张卡片预览 - 优化版本
const generateCardPreview = async (card, cardIndex = null, totalCards = null) => {
  if (!card) return '';

  try {
    // 检查主题是否变化，如果变化则清理缓存
    if (cachedThemeId !== currentTheme.value) {
      cleanupCachedContainer();
      cachedThemeId = currentTheme.value;
    }

    // 复用DOM容器，避免重复创建
    if (!cachedCardContainer) {
      cachedCardContainer = document.createElement('div');
      cachedCardContainer.className = 'custom-pool-card-export';
      document.body.appendChild(cachedCardContainer);
    }

    // 每次都更新样式，确保主题变化能生效
    let containerStyle = `
      width: 400px;
      padding: ${theme.value.spacing.cardPadding};
      background: ${theme.value.colors.background};
      border-radius: ${theme.value.borderRadius};
      box-shadow: ${theme.value.shadow};
      position: fixed;
      top: -9999px;
      left: -9999px;
      color: ${theme.value.colors.textPrimary};
      z-index: -1;
      font-family: ${theme.value.fonts.primary};
    `;

    cachedCardContainer.style.cssText = containerStyle;

    // 添加背景图案（如果主题支持）
    let backgroundPatternHtml = '';
    if (theme.value.backgroundPattern && theme.value.backgroundPattern.svg) {
      backgroundPatternHtml = `
        <div style="
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          opacity: ${theme.value.backgroundPattern.opacity || 0.05};
          pointer-events: none;
          border-radius: ${theme.value.borderRadius};
          overflow: hidden;
        ">
          ${theme.value.backgroundPattern.svg}
        </div>
      `;
    }

    // 卡片内容
    cachedCardContainer.innerHTML = `
      ${backgroundPatternHtml}
      <div style="position: relative; z-index: 1;">
        <div style="text-align: center; margin-bottom: ${theme.value.spacing.sectionGap};">
          <h2 style="
            margin: 0;
            font: ${theme.value.fonts.title};
            color: ${theme.value.colors.textPrimary};
          ">${cardIndex !== null ? getNumberPrefix(cardIndex + 1, totalCards || props.cards.length) + '. ' : ''}${escapeHtml(card.title || '未命名卡片')}</h2>
          ${includePoolInfo.value ? `
            <p style="
              margin: 8px 0 0 0;
              font: ${theme.value.fonts.body};
              color: ${theme.value.colors.textSecondary};
            ">来自: ${escapeHtml(props.poolName)}</p>
          ` : ''}
        </div>

        ${includeDimensions.value && card.properties && Object.keys(card.properties).length > 0 ? `
          <div style="
            background: ${theme.value.colors.cardBackground};
            border-radius: ${theme.value.borderRadius};
            padding: ${theme.value.spacing.itemGap};
            border: 1px solid ${theme.value.colors.border};
            box-shadow: ${theme.value.cardShadow};
          ">
            <h3 style="
              margin: 0 0 ${theme.value.spacing.itemGap} 0;
              font: ${theme.value.fonts.subtitle};
              color: ${theme.value.colors.textPrimary};
              border-bottom: 2px solid ${theme.value.colors.accent};
              padding-bottom: 6px;
            ">属性信息</h3>
            ${Object.entries(card.properties).map(([dimensionId, value]) => {
              const dimensionName = getDimensionName(dimensionId);
              const formattedValue = formatPropertyValue(value, dimensionId);
              const dimension = props.dimensions.find(d => d.id === dimensionId);

              return `
                <div style="margin-bottom: ${theme.value.spacing.itemGap};">
                  <div style="
                    font: ${theme.value.fonts.dimension};
                    color: ${theme.value.colors.textPrimary};
                    margin-bottom: 4px;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  ">
                    <span style="
                      background: ${theme.value.colors.accent};
                      color: white;
                      padding: 3px 8px;
                      border-radius: 12px;
                      font: ${theme.value.fonts.caption};
                      font-weight: 500;
                    ">${escapeHtml(dimensionName)}</span>
                    ${dimension && dimension.description ? `
                      <span style="
                        font: ${theme.value.fonts.caption};
                        color: ${theme.value.colors.textTertiary};
                        font-weight: normal;
                      ">${escapeHtml(dimension.description)}</span>
                    ` : ''}
                  </div>
                  <div style="
                    font: ${theme.value.fonts.body};
                    color: ${theme.value.colors.textSecondary};
                    background: ${theme.value.colors.sectionBackground};
                    padding: 10px 12px;
                    border-radius: 8px;
                    word-break: break-word;
                    line-height: 1.6;
                    border-left: 3px solid ${theme.value.colors.accent};
                    min-height: 20px;
                  ">${formattedValue}</div>
                </div>
              `;
            }).join('')}
          </div>
        ` : ''}
      </div>

        <div style="
          text-align: center;
          margin-top: ${theme.value.spacing.sectionGap};
          font: ${theme.value.fonts.caption};
          color: ${theme.value.colors.textTertiary};
          opacity: 0.8;
        ">
          ✨ Powered By PVV • ${new Date().toLocaleDateString('zh-CN')}
        </div>
    `;

    // 减少DOM更新等待时间
    await new Promise(resolve => setTimeout(resolve, 50));

    // 根据质量选项设置缩放系数
    const scaleFactors = { '1': 1.5, '2': 2, '3': 3 };
    const scaleFactor = scaleFactors[imageQuality.value] || 2;

    // 使用html2canvas生成图片
    const canvas = await html2canvas(cachedCardContainer, {
      scale: scaleFactor,
      useCORS: true,
      backgroundColor: null,
      allowTaint: true,
      logging: false // 禁用日志以提高性能
    });

    // 转换为base64
    const imageData = canvas.toDataURL('image/png');

    return imageData;
  } catch (error) {
    console.error('生成卡片预览失败:', error);
    return '';
  }
};

// 清理缓存的DOM容器
const cleanupCachedContainer = () => {
  if (cachedCardContainer && cachedCardContainer.parentNode) {
    document.body.removeChild(cachedCardContainer);
    cachedCardContainer = null;
  }
  cachedThemeId = null; // 同时清理主题缓存
};

// 生成统一的编号格式
const getNumberPrefix = (number, totalCount = 999) => {
  // 根据总数确定位数，最少3位
  const digits = Math.max(3, totalCount.toString().length);

  // 生成补零的编号，如 001, 002, 010, 100
  return number.toString().padStart(digits, '0');
};

// 更新预览
const updatePreview = async () => {
  if (currentPreviewCard.value) {
    previewUrl.value = '';
    previewUrl.value = await generateCardPreview(currentPreviewCard.value, previewIndex.value, props.cards.length);
  }
};

// 取消导出
const cancelExport = () => {
  exportCancelled.value = true;
  if (loadingInstance) {
    loadingInstance.setText('正在取消导出...');
  }
  ElMessage.info('正在取消导出...');
};

// 导出所有卡片
const exportAllCards = async () => {
  if (props.cards.length === 0) {
    ElMessage.warning('没有卡片可供导出');
    return;
  }

  try {
    exporting.value = true;
    exportProgress.value = 0;
    currentExportingCard.value = '';
    exportCancelled.value = false;

    // 选择保存目录
    const response = await window.pywebview.api.select_directory();
    const result = typeof response === 'string' ? JSON.parse(response) : response;

    if (!result || result.status !== 'success' || !result.data) {
      exporting.value = false;
      currentExportingCard.value = '';
      ElMessage.error("取消导出");
      return;
    }

    // 启动全局loading
    loadingInstance = ElLoading.service({
      lock: true,
      text: '正在导出卡片...',
      background: 'rgba(0, 0, 0, 0.7)',
      customClass: 'export-loading'
    });

    const directory = result.data;
    const successCount = [];
    const failedCards = [];

    // 修改为顺序处理，避免并发导致的编号和标题混乱
    for (let cardIndex = 0; cardIndex < props.cards.length; cardIndex++) {
      // 检查是否被取消
      if (exportCancelled.value) {
        ElMessage.warning(`导出已取消，已成功导出 ${successCount.length} 张卡片`);
        break;
      }

      const card = props.cards[cardIndex];
      currentExportingCard.value = card.title || `卡片${cardIndex + 1}`;

      // 更新loading文本
      if (loadingInstance) {
        loadingInstance.setText(`正在导出第 ${cardIndex + 1}/${props.cards.length} 张卡片: ${currentExportingCard.value}`);
      }

      try {
        // 生成卡片图片，传递正确的索引和总数用于编号
        const imageData = await generateCardPreview(card, cardIndex, props.cards.length);
        if (!imageData) {
          console.warn(`跳过卡片 ${card.title}: 生成预览失败`);
          failedCards.push(card.title || '未命名卡片');
          continue;
        }

        // 构建文件名（处理特殊字符），添加编号
        const cardNumber = cardIndex + 1;
        const numberPrefix = getNumberPrefix(cardNumber, props.cards.length);
        const safeName = (card.title || `卡片${cardNumber}`).replace(/[<>:"/\\|?*]/g, '_');
        const fileName = `${props.poolName}_${numberPrefix}_${safeName}.png`;
        const filePath = `${directory}/${fileName}`;

        // 保存卡片
        const saveResponse = await window.pywebview.api.save_entity_card({
          file_path: filePath,
          image_data: imageData
        });

        const saveResult = typeof saveResponse === 'string' ? JSON.parse(saveResponse) : saveResponse;

        if (saveResult && saveResult.status === 'success') {
          successCount.push(fileName);
        } else {
          console.warn(`保存卡片 ${card.title} 失败:`, saveResult?.message);
          failedCards.push(card.title || '未命名卡片');
        }
      } catch (error) {
        console.error(`导出卡片 ${card.title} 时出错:`, error);
        failedCards.push(card.title || '未命名卡片');
      }

      // 更新进度
      exportProgress.value = cardIndex + 1;

      // 每张卡片间的短暂延迟，避免过度占用资源
      if (cardIndex < props.cards.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // 显示导出结果
    if (successCount.length > 0) {
      let message = `成功导出 ${successCount.length} 张卡片`;
      if (failedCards.length > 0) {
        message += `，${failedCards.length} 张失败`;
      }
      message += `\n保存位置: ${directory}`;

      ElMessage.success(message);
      emit('export-success', {
        directory,
        count: successCount.length,
        failed: failedCards.length,
        failedCards
      });

      // 打开文件所在位置
      try {
        await window.pywebview.api.open_directory(directory);
      } catch (error) {
        console.warn('无法打开文件目录:', error);
      }
    } else {
      ElMessage.error(`导出失败，所有 ${props.cards.length} 张卡片都未能成功导出`);
    }
  } catch (error) {
    console.error('导出卡片时出错:', error);
    ElMessage.error('导出失败：' + (error.message || '未知错误'));
  } finally {
    // 清理缓存的DOM容器
    cleanupCachedContainer();

    // 关闭loading
    if (loadingInstance) {
      loadingInstance.close();
      loadingInstance = null;
    }

    exporting.value = false;
    exportProgress.value = 0;
    currentExportingCard.value = '';
    exportCancelled.value = false;
  }
};

// 监听预览索引变化
watch(previewIndex, updatePreview);

// 监听主题变化
watch(currentTheme, () => {
  // 主题变化时立即清理缓存，确保新主题能完全应用
  cleanupCachedContainer();
  updatePreview();
});

// 监听导出选项变化
watch([includePoolInfo, includeDimensions, imageQuality], updatePreview);

// 组件挂载时加载配置和生成初始预览
onMounted(async () => {
  // 加载自定义主题配置
  if (!configStore.state.config.loaded) {
    try {
      await configStore.loadConfig();
    } catch (error) {
      console.error('加载配置失败:', error);
      ElMessage.error('加载自定义主题失败');
    }
  }

  // 生成初始预览
  if (props.cards.length > 0) {
    nextTick(updatePreview);
  }
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理缓存的DOM容器
  cleanupCachedContainer();

  // 取消正在进行的导出
  if (exportCancelled.value === false) {
    exportCancelled.value = true;
  }
});
</script>

<style lang="scss" scoped>
.custom-pool-card-exporter {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  user-select: none;

  .exporter-container {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
    min-height: 0;
  }
}

// 左侧控制面板
.control-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--el-border-color);
  min-height: 0;

  .panel-header {
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color);
    flex-shrink: 0;

    .panel-title {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .export-info {
      .card-count {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        background: var(--el-fill-color-light);
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
  }

  .theme-section-header {
    padding: 20px 20px 16px 20px;
    flex-shrink: 0;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }

  .theme-list-container {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    padding: 0 20px;

    .theme-list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
      padding: 4px;

      .theme-item {
        cursor: pointer;
        text-align: center;
        transition: all 0.2s;
        border-radius: 6px;
        padding: 6px;
        border: 1px solid transparent;

        &:hover {
          transform: translateY(-1px);
          background: var(--el-fill-color-light);
        }

        &.active {
          border-color: var(--el-color-primary);

          .theme-preview {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
        }

        .theme-preview {
          width: 100%;
          height: 50px;
          border-radius: 4px;
          border: 1px solid var(--el-border-color-light);
          position: relative;
          overflow: hidden;
          margin-bottom: 6px;
          transition: all 0.2s;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          .theme-card {
            position: absolute;
            top: 4px;
            left: 4px;
            width: 30px;
            height: 20px;
            border-radius: 3px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }

          .theme-accent {
            position: absolute;
            bottom: 4px;
            right: 4px;
            width: 16px;
            height: 8px;
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }
        }

        .theme-name {
          font-size: 11px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          transition: color 0.2s;
          line-height: 1.2;
        }
      }
    }
  }

  .export-options-section {
    padding: 16px 20px 20px 20px;
    flex-shrink: 0;
    border-top: 1px solid var(--el-border-color);

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin: 0 0 16px 0;
      color: var(--el-text-color-primary);
    }

    .option-list {
      .option-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        cursor: pointer;

        input[type="checkbox"] {
          margin-right: 8px;
          width: 16px;
          height: 16px;
        }

        .option-text {
          font-size: 14px;
          flex: 1;
          color: var(--el-text-color-primary);
        }

        .option-label {
          font-size: 14px;
          margin-right: 12px;
          color: var(--el-text-color-primary);
        }

        .quality-select {
          padding: 4px 8px;
          border-radius: 4px;
          border: 1px solid var(--el-border-color);
          font-size: 14px;
          min-width: 80px;
          background: var(--el-bg-color);
          color: var(--el-text-color-primary);
        }
      }
    }
  }

  .panel-footer {
    padding: 20px;
    border-top: 1px solid var(--el-border-color);
    flex-shrink: 0;

    .export-btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      background: var(--el-color-success);
      color: white;

      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
        background: var(--el-color-info-light-5);
      }

      &.loading {
        cursor: wait;
      }

      &:not(:disabled):hover {
        transform: translateY(-1px);
        background: var(--el-color-success-dark-2);
      }
    }


  }
}

// 右侧预览区域
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  min-height: 0;

  .preview-header {
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .panel-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .preview-controls {
      display: flex;
      align-items: center;
    }
  }

  .preview-content {
    flex: 1;
    overflow: auto;
    padding: 20px;
    min-height: 0;
    position: relative;



    .preview-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      min-height: 0;

      .card-info {
        flex-shrink: 0;
        padding: 12px 0;
        border-bottom: 1px solid var(--el-border-color-light);
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
        }

        .pool-name {
          margin: 0;
          font-size: 14px;
          color: var(--el-text-color-secondary);
          white-space: nowrap;
        }

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
          background: var(--el-fill-color-light);
          padding: 4px 8px;
          border-radius: 4px;
          white-space: nowrap;

          .el-icon {
            font-size: 14px;
            color: var(--el-color-primary);
          }
        }
      }

      .preview-image-container {
        flex: 1;
        overflow: auto;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        min-height: 0;
        padding: 16px 0;

        .preview-image {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          display: block;
        }

        .generating-preview {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px;
          color: var(--el-text-color-secondary);

          .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid var(--el-border-color);
            border-top: 3px solid var(--el-color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 12px;
          }

          p {
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 300px;
      color: var(--el-text-color-secondary);

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.6;
      }

      .empty-text {
        margin: 0;
        font-size: 16px;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 自定义导出loading样式 */
:deep(.export-loading) {
  .el-loading-text {
    font-size: 16px;
    font-weight: 500;
    margin-top: 16px;
  }

  .el-loading-spinner {
    .circular {
      width: 50px;
      height: 50px;
    }

    .path {
      stroke: var(--el-color-primary);
      stroke-width: 3;
    }
  }
}
</style>
