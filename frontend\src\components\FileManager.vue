<template>
  <div class="file-manager" :class="{ 'dark': isDarkTheme }">
    <!-- 工具栏 -->
    <div class="file-toolbar">
      <div class="toolbar-actions">
        <el-button
          @click="selectDirectory"
          :icon="FolderOpened"
          size="small"
          text
          class="toolbar-btn"
        />

        <el-button
          @click="refreshDirectory"
          :icon="Refresh"
          size="small"
          text
          :loading="loading"
          :disabled="!currentDirectory"
          class="toolbar-btn"
        />
      </div>

      <div class="toolbar-search">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索文件..."
          :prefix-icon="Search"
          size="small"
          clearable
          class="search-input"
        />
      </div>
    </div>

    <!-- 当前目录路径 -->
    <div v-if="currentDirectory" class="current-path">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          <el-icon><Folder /></el-icon>
          {{ getDirectoryName(currentDirectory) }}
        </el-breadcrumb-item>
      </el-breadcrumb>
      <span class="path-text">{{ currentDirectory }}</span>
    </div>

    <!-- 文件列表 -->
    <div class="file-list" v-loading="loading">
      <div v-if="!currentDirectory" class="empty-state">
        <el-empty description="请选择一个包含Markdown文件的目录">
          <el-button type="primary" @click="selectDirectory">选择目录</el-button>
        </el-empty>
      </div>
      
      <div v-else-if="filteredFiles.length === 0" class="empty-state">
        <el-empty description="当前目录没有找到Markdown文件">
          <el-button @click="refreshDirectory">刷新目录</el-button>
        </el-empty>
      </div>
      
      <div v-else class="file-items">
        <div
          v-for="file in filteredFiles"
          :key="file.path"
          class="file-item"
          :class="{ 'selected': selectedFile?.path === file.path }"
          @click="selectFile(file)"
          @dblclick="openFile(file)"
        >
          <div class="file-icon">
            <el-icon>
              <Document v-if="!file.is_directory" />
              <Folder v-else />
            </el-icon>
          </div>
          
          <div class="file-info">
            <div class="file-name" :title="file.name">{{ file.name }}</div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
              <span class="file-time">{{ formatTime(file.modified_time) }}</span>
            </div>
          </div>
          
          <div class="file-actions">
            
              <el-button
                @click.stop="openInExplorer(file.path)"
                :icon="FolderOpened"
                size="small"
                text
                class="file-action-btn"
              />
            
          </div>
        </div>
      </div>
    </div>

    <!-- 文件统计 -->
    <div v-if="currentDirectory" class="file-stats">
      <span>共 {{ markdownFiles.length }} 个Markdown文件</span>
      <span v-if="searchKeyword">，显示 {{ filteredFiles.length }} 个</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  FolderOpened,
  Refresh,
  Search,
  Folder,
  Document,
  Plus
} from '@element-plus/icons-vue'
import { useConfigStore } from '@/stores/config'

// Props
const props = defineProps({
  selectedFile: {
    type: Object,
    default: null
  },
  lastSelectedDirectory: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['file-selected', 'file-opened', 'create-document', 'directory-selected'])

// Store
const configStore = useConfigStore()

// 响应式数据
const currentDirectory = ref('')
const files = ref([])
const loading = ref(false)
const searchKeyword = ref('')

// 计算属性
const isDarkTheme = computed(() => configStore.theme === 'dark')

const markdownFiles = computed(() => {
  return files.value.filter(file =>
    !file.is_directory &&
    file.name.toLowerCase().endsWith('.md')
  )
})

const filteredFiles = computed(() => {
  if (!searchKeyword.value) {
    return markdownFiles.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return markdownFiles.value.filter(file =>
    file.name.toLowerCase().includes(keyword)
  )
})

// 监听搜索关键词
watch(searchKeyword, () => {
  // 如果有搜索结果且当前选中的文件不在结果中，清除选择
  if (filteredFiles.value.length > 0 && props.selectedFile) {
    const isSelectedInResults = filteredFiles.value.some(
      file => file.path === props.selectedFile.path
    )
    if (!isSelectedInResults) {
      emit('file-selected', null)
    }
  }
})

// 监听上次选择的目录
watch(() => props.lastSelectedDirectory, async (newDirectory) => {
  if (newDirectory && newDirectory !== currentDirectory.value) {
    currentDirectory.value = newDirectory
    await loadDirectoryFiles()
  }
}, { immediate: true })

// 方法
const selectDirectory = async () => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      currentDirectory.value = result.data
      await loadDirectoryFiles()
      // 触发目录选择事件
      emit('directory-selected', result.data)
    } else {
      ElMessage.error('选择目录失败: ' + result.message)
    }
  } catch (error) {
    console.error('选择目录失败:', error)
    ElMessage.error('选择目录失败: ' + error.message)
  }
}

const loadDirectoryFiles = async () => {
  if (!currentDirectory.value) return
  
  loading.value = true
  try {
    const response = await window.pywebview.api.list_directory(currentDirectory.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      files.value = result.data || []

      // 调试信息
      console.log('加载的文件列表:', files.value)
      console.log('Markdown文件数量:', markdownFiles.value.length)
      console.log('Markdown文件:', markdownFiles.value)

      // 如果有Markdown文件，自动选择第一个
      if (markdownFiles.value.length > 0) {
        selectFile(markdownFiles.value[0])
      }
    } else {
      ElMessage.error('加载目录失败: ' + result.message)
      files.value = []
    }
  } catch (error) {
    console.error('加载目录失败:', error)
    ElMessage.error('加载目录失败: ' + error.message)
    files.value = []
  } finally {
    loading.value = false
  }
}

const refreshDirectory = async () => {
  await loadDirectoryFiles()
}

const selectFile = (file) => {
  emit('file-selected', file)
}

const openFile = async (file) => {
  if (!file.is_directory && file.name.toLowerCase().endsWith('.md')) {
    emit('file-opened', file)
  }
}

const openInExplorer = async (filePath) => {
  try {
    // 获取文件的父目录
    const parentDir = filePath.substring(0, filePath.lastIndexOf('/')) ||
                     filePath.substring(0, filePath.lastIndexOf('\\'))

    // 如果无法获取父目录，使用当前目录
    const dirToOpen = parentDir || currentDirectory.value

    await window.pywebview.api.open_directory(dirToOpen)
  } catch (error) {
    console.error('打开文件管理器失败:', error)
    ElMessage.error('打开文件管理器失败')
  }
}

const createNewDocument = () => {
  emit('create-document')
}

const getDirectoryName = (path) => {
  return path.split(/[/\\]/).pop() || path
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp * 1000)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

// 暴露方法给父组件
defineExpose({
  selectDirectory,
  refreshDirectory,
  currentDirectory: computed(() => currentDirectory.value),
  markdownFiles: computed(() => markdownFiles.value)
})
</script>

<style lang="scss" scoped>
.file-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 0;
  border-radius: 8px;
}

.file-toolbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: linear-gradient(135deg, var(--el-fill-color-extra-light) 0%, transparent 100%);
  flex-shrink: 0;
  border-radius: 8px 8px 0 0;

  // 深色主题优化
  .dark & {
    background: linear-gradient(135deg, var(--el-fill-color-dark) 0%, transparent 100%);
  }

  .toolbar-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .toolbar-btn {
      width: 36px;
      height: 36px;
      padding: 0;
      color: var(--el-text-color-regular);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      background: linear-gradient(135deg, transparent 0%, var(--el-fill-color-light) 100%);
      border: 1px solid transparent;
      position: relative;
      overflow: hidden;

      &:hover {
        color: var(--el-color-primary);
        background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
        border-color: var(--el-color-primary-light-7);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }

      &:disabled {
        color: var(--el-text-color-disabled);
        background: transparent;
        transform: none;
        box-shadow: none;
      }

      &:active {
        transform: translateY(0);
      }

      .el-icon {
        font-size: 18px;
      }
    }
  }

  .toolbar-search {
    flex: 1;
    max-width: 200px;

    .search-input {
      width: 100%;

      :deep(.el-input__wrapper) {
        border-radius: 6px;
        box-shadow: 0 0 0 1px var(--el-border-color-lighter);

        &:hover {
          box-shadow: 0 0 0 1px var(--el-border-color);
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        }
      }

      :deep(.el-input__inner) {
        font-size: 13px;
        height: 32px;
        line-height: 32px;
      }

      :deep(.el-input__prefix) {
        color: var(--el-text-color-placeholder);
      }
    }
  }
}

.current-path {
  padding: 8px 16px;
  background: var(--el-fill-color-blank);
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-size: 12px;
  
  .el-breadcrumb {
    margin-bottom: 4px;
  }
  
  .path-text {
    color: var(--el-text-color-secondary);
    word-break: break-all;
  }
}

.file-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding: 8px;

  .empty-state {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-placeholder);
    font-style: italic;
    background: linear-gradient(135deg, var(--el-fill-color-extra-light) 0%, transparent 100%);
    border-radius: 8px;
    padding: 32px;
    text-align: center;

    // 深色主题优化
    .dark & {
      background: linear-gradient(135deg, var(--el-fill-color-dark) 0%, transparent 100%);
    }
  }
}

.file-items {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  min-height: 48px;
  background: linear-gradient(135deg, transparent 0%, var(--el-fill-color-extra-light) 100%);
  border: 1px solid transparent;

  &:hover {
    background: linear-gradient(135deg, var(--el-fill-color-light) 0%, var(--el-fill-color) 100%);
    border-color: var(--el-border-color-light);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .file-actions {
      opacity: 1;
      transform: translateX(0);
    }
  }

  &.selected {
    background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
    border-color: var(--el-color-primary-light-7);
    transform: translateX(6px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 28px;
      background: linear-gradient(180deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
      border-radius: 2px;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
    }

    .file-name {
      color: var(--el-color-primary);
      font-weight: 600;
    }

    .file-actions {
      opacity: 1;
    }
  }
}

.file-icon {
  margin-right: 12px;
  color: var(--el-color-primary);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
  transition: all 0.3s ease;

  .el-icon {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(64, 158, 255, 0.2));
  }
}

.file-info {
  flex: 1;
  min-width: 0;
  margin-right: 12px;

  .file-name {
    font-size: 15px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
    letter-spacing: 0.2px;
  }

  .file-meta {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    line-height: 1.2;

    .file-size,
    .file-time {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .file-size {
      max-width: 60px;
    }

    .file-time {
      max-width: 100px;
    }
  }
}

.file-actions {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  flex-shrink: 0;
  margin-left: 12px;
  transform: translateX(8px);

  .file-action-btn {
    width: 32px;
    height: 32px;
    padding: 0;
    color: var(--el-text-color-placeholder);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(135deg, transparent 0%, var(--el-fill-color-light) 100%);
    border: 1px solid transparent;

    &:hover {
      color: var(--el-color-primary);
      background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
      border-color: var(--el-color-primary-light-7);
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
    }

    &:active {
      transform: scale(0.95);
    }

    .el-icon {
      font-size: 16px;
    }
  }
}

.file-stats {
  padding: 8px 16px;
  background: var(--el-bg-color-page);
  border-top: 1px solid var(--el-border-color-lighter);
  font-size: 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

// 深色主题适配
.dark {
  .file-item {
    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
    
    &.selected {
      background: rgba(var(--el-color-primary-rgb), 0.15);
      border-color: var(--el-color-primary);
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .file-toolbar {
    padding: 8px 12px;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .toolbar-actions {
      justify-content: center;
      flex-wrap: wrap;
    }

    .toolbar-search {
      max-width: none;

      .search-input {
        width: 100% !important;
      }
    }
  }

  .current-path {
    padding: 6px 12px;
    font-size: 11px;

    .path-text {
      word-break: break-all;
      font-size: 10px;
    }
  }

  .file-item {
    padding: 8px;

    .file-info {
      .file-name {
        font-size: 13px;
      }

      .file-meta {
        font-size: 11px;
        flex-direction: column;
        gap: 2px;
        align-items: flex-start;
      }
    }
  }

  .file-stats {
    padding: 6px 12px;
    font-size: 11px;
  }
}

@media screen and (max-width: 480px) {
  .file-toolbar {
    .toolbar-left {
      .el-button {
        font-size: 12px;
        padding: 6px 12px;
      }
    }
  }

  .file-item {
    padding: 6px;

    .file-icon {
      margin-right: 8px;

      .el-icon {
        font-size: 16px;
      }
    }

    .file-info {
      .file-name {
        font-size: 12px;
      }

      .file-meta {
        font-size: 10px;
      }
    }
  }
}

// 滚动条样式
.file-list::-webkit-scrollbar {
  width: 6px;
}

.file-list::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

.file-list::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;

  &:hover {
    background: var(--el-border-color-dark);
  }
}
</style>
