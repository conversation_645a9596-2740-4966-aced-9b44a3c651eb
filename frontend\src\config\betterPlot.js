// 剧情元素替换系统
export const plotElementSwapper = {
  // 基础配置
  config: {
    version: '1.0.0',
    description: '通过替换不同剧情元素来优化故事效果',
    commonCheckPoints: [
      '替换是否符合故事发展？',
      '替换是否能带来更好的效果？',
      '替换是否符合修真世界设定？',
      '替换是否为后续发展埋下伏笔？'
    ]
  },

  // 视角替换系统，叙述距离的选择
  perspectiveSwap: {
    description: '通过切换不同视角来优化剧情效果',
    elements: [
      {
        type: '叙事视角',
        options: [
          {
            name: '第一人称',
            description: '从"我"的视角讲述故事',
            effect: '增加代入感，让读者更容易感同身受',
            examples: ['我感受到体内灵力涌动，每一缕灵气都在经脉中欢快地流转']
          },
          {
            name: '第三人称全知',
            description: '上帝视角，可以看到所有角色的想法',
            effect: '可以展示更多信息，增加剧情的复杂性',
            examples: ['李青山暗自盘算，而对面的王掌门却早已洞悉了他的心思']
          },
          {
            name: '第三人称限知',
            description: '跟随特定角色，只知道该角色所知道的信息',
            effect: '制造悬疑感，增加故事的不确定性',
            examples: ['他看着对方高深莫测的表情，猜不透对方究竟在想什么']
          }
        ]
      },
      {
        type: '人物视角',
        options: [
          {
            name: '主角视角',
            description: '从主角角度展现故事',
            effect: '让读者更容易理解主角的想法和感受',
            examples: ['修炼到瓶颈时的那种无助感，只有经历过的人才能理解']
          },
          {
            name: '配角视角',
            description: '从配角的角度观察主角',
            effect: '展现主角的另一面，增加人物的立体感',
            examples: ['作为师兄，我看着这个天赋异禀的小师弟一步步成长']
          },
          {
            name: '对手视角',
            description: '从对手的角度看待冲突',
            effect: '增加对立面的可信度，使冲突更有说服力',
            examples: ['我必须承认，这个年轻人的修炼速度远超我的预期']
          }
        ]
      }
    ],
    checkPoints: [
      '视角转换是否自然？',
      '新视角是否带来新信息？',
      '是否保持叙事连贯性？',
      '是否控制好信息量？',
      '是否有效推进了主线？',
      '是否埋下了后续伏笔？',
      '是否展现了新的信息？',
      '是否深化了人物性格？',
      '是否展示了人物关系？',
      '是否有人物成长？',
      '是否营造了特定氛围？',
      '氛围是否配合剧情？',
      '是否有画面感？',
      '与上一场景是否有合理过渡？',
      '是否为下一场景做好铺垫？',
      '场景切换是否流畅？'
    ]
  },

  // 场景替换系统
  sceneSwap: {
    description: '通过替换场景要素来强化剧情效果',
    elements: [
      {
        type: '时间要素',
        options: [
          {
            name: '黄昏',
            description: '日落时分，阴阳交替之际',
            effect: '营造神秘感，增加紧张气氛',
          },
          {
            name: '破晓',
            description: '黎明时分，新的开始',
            effect: '象征希望，展现新生',
          }
        ],
      },
      {
        type: '空间要素',
        options: [
          {
            name: '古战场',
            description: '充满历史感的古代战场',
            effect: '增加历史感，展现世界观',
          },
          {
            name: '密室',
            description: '隐秘的修炼密室',
            effect: '制造神秘感，增加紧张气氛',
          }
        ],
      },
      {
        type: '氛围要素',
        options: [
          {
            name: '黄昏',
            description: '日落时分，阴阳交替之际',
            effect: '营造神秘感，增加紧张气氛',
          },
          {
            name: '破晓',
            description: '黎明时分，新的开始',
            effect: '象征希望，展现新生',
          }
        ],
      },
      {
        type: '场景目的',
        options: [
          {
            name: '黄昏',
            description: '日落时分，阴阳交替之际',
            effect: '营造神秘感，增加紧张气氛',
          },
          {
            name: '破晓',
            description: '黎明时分，新的开始',
            effect: '象征希望，展现新生',
          }
        ],
      },
      {
        type: '场景卖点',
        options: [
          {
            name: '期待感',
            description: '场景卖点',
            effect: '增加期待感，吸引读者',
          },
          {
            name: '悬念',
            description: '场景卖点',
            effect: '制造悬念，增加紧张气氛',
          }
        ],
      },
    ],
    checkPoints: [
      '场景替换是否合理？',
      '新场景是否能更好地服务剧情？',
      '是否与人物行为相协调？',
      '是否增强了故事氛围？'
    ]
  },

  // 冲突替换系统
  conflictSwap: {
    description: '通过替换冲突类型来深化剧情',
    elements: [
      {
        type: '内心冲突',
        options: [
          {
            name: '道德困境',
            description: '在两个都正确的选择之间做决定',
            effect: '增加人物的深度，展现价值观的碰撞',
            examples: ['为了变强而使用禁术，还是坚持正道修炼？']
          },
          {
            name: '理想与现实',
            description: '理想与现实的差距带来的挣扎',
            effect: '展现人物的成长过程',
            examples: ['修仙之路与凡人亲情的抉择']
          },
          {
            name: '欲望与责任',
            description: '个人欲望与责任义务的矛盾',
            effect: '展现人物的选择与成长',
            examples: ['是追求个人实力，还是履行宗门职责？']
          }
        ]
      },
      {
        type: '外部冲突',
        options: [
          {
            name: '势力之争',
            description: '不同修仙势力之间的对抗',
            effect: '展现世界观，推动故事发展',
            examples: ['正道与魔道的千年对立']
          },
          {
            name: '资源争夺',
            description: '为了有限的修炼资源而产生的冲突',
            effect: '突出修仙世界的残酷性',
            examples: ['多个宗门为了一处上古遗迹而争斗']
          },
          {
            name: '理念之争',
            description: '不同修炼理念之间的冲突',
            effect: '深化世界观，展现不同立场',
            examples: ['守护凡人还是追求长生？']
          }
        ]
      }
    ],
    checkPoints: [
      '冲突升级是否合理？',
      '新冲突是否更有深度？',
      '是否符合人物性格？',
      '是否为后续埋下伏笔？'
    ]
  },

  // 行动替换系统
  actionSwap: {
    description: '通过替换行动要素来强化剧情效果',
    elements: [
      {
        type: '行动要素',
        options: [
          {
            name: '行动要素1',
            description: '描述行动要素1',
            effect: '增加紧张感，推动剧情发展',
          },
          {
            name: '行动要素2',
            description: '描述行动要素2',
            effect: '展现人物的决心和勇气',
          },
          {
            name: '行动要素3',
            description: '描述行动要素3',
            effect: '制造悬念，增加期待感',
          }
        ],
      },
      {
        type: '身份转变',
        options: [
          {
            name: '坏变好',
            description: '描述行动要素1',
            effect: '增加人物的深度，展现成长',
          }
        ],
      }
    ],
    checkPoints: [
      '行动替换是否合理？',
      '新行动是否更有魅力？',
      '是否符合人物性格？',
      '是否为后续埋下伏笔？'
    ]
  },

  // 事件影响替换系统
  eventSwap: {
    description: '通过替换事件要素来强化剧情效果',
    elements: [
      {
        type: '事件要素',
        options: [
          {
            name: '事件要素1',
            description: '描述事件要素1',
            effect: '增加紧张感，推动剧情发展',
          },
          {
            name: '事件要素2',
            description: '描述事件要素2',
            effect: '展现人物的决心和勇气',
          },
          {
            name: '事件要素3',
            description: '描述事件要素3',
            effect: '制造悬念，增加期待感',
          }
        ],
      }
    ],
    checkPoints: [
      '事件替换是否合理？',
      '新事件是否更有魅力？',
      '是否符合人物性格？',
      '是否为后续埋下伏笔？'
    ]
  },

  // 人物属性替换系统
  characterSwap: {
    description: '通过替换人物要素来丰富角色',
    elements: [
      {
        type: '人物动机',
        options: [
          {
            name: '追求长生',
            description: '渴望突破生命限制',
            effect: '体现修仙者的终极追求',
            examples: ['他立志要超越凡人的界限，追寻永恒的生命']
          },
          {
            name: '复仇',
            description: '为了报仇而修炼',
            effect: '增加人物的动力和戏剧性',
            examples: ['为了给家族报仇，他忍受着修炼的痛苦']
          },
          {
            name: '守护',
            description: '为了保护重要的人或物而变强',
            effect: '增加人物的情感深度',
            examples: ['只有变得更强，才能保护身边的人']
          }
        ]
      },
      {
        type: '性格特征',
        options: [
          {
            name: '谨慎稳重',
            description: '做事深思熟虑',
            effect: '增加可信度，适合智谋型角色',
            examples: ['每一步都要算计清楚，这是他的修炼之道']
          },
          {
            name: '狂傲自信',
            description: '对自己充满信心',
            effect: '增加戏剧性，适合天才型角色',
            examples: ['在修炼一道，他从不相信有人能超越自己']
          },
          {
            name: '温和谦逊',
            description: '待人温和，不骄不躁',
            effect: '增加反差感，适合隐藏实力的角色',
            examples: ['表面上是个普通的杂役弟子，实则暗藏惊人的天赋']
          }
        ]
      }
    ],
    checkPoints: [
      '人物改变是否合理？',
      '新特征是否更有魅力？',
      '是否符合人物成长？',
      '是否为后续留下空间？'
    ]
  },

  // 关系替换系统
  relationshipSwap: {
    description: '通过替换人物关系来制造反转',
    elements: [
      {
        type: '正派位',
        options: [
          {
            name: '姐妹',
            description: '通过普通关系来展现人物关系',
            effect: '增加情感深度，展现人物关系',
          },
          {
            name: '特殊关系',
            description: '通过特殊关系来展现人物关系',
            effect: '增加戏剧性，展现人物关系',
          },
          {
            name: '宿命关系',
            description: '通过宿命关系来展现人物关系',
            effect: '增加宿命感，展现人物关系',
          }
        ]
      },
      {
        type: '反派位',
        options: [
          {
            name: '无名反派',
            description: '通过当前关系来展现人物关系',
            effect: '增加反差感，展现人物关系',
          },
          {
            name: '小反派',
            description: '通过宿命关系来展现人物关系',
            effect: '增加宿命感，展现人物关系',
          },
          {
            name: '明星反派',
            description: '通过宿世关系来展现人物关系',
            effect: '增加宿命感，展现人物关系',
          }
        ]
      }
    ],
    checkPoints: [
      '关系转变是否合理？',
      '新关系是否更有深度？',
      '是否符合人物背景？',
      '是否为后续留下伏笔？'
    ]
  }
}