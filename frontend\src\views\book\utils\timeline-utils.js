/**
 * 时间线工具函数
 */

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

/**
 * 创建新的时间线事件
 * @param {Object} data 事件数据
 * @returns {Object} 事件对象
 */
export function createTimelineEvent(data = {}) {
  const currentYear = new Date().getFullYear();
  return {
    id: generateUniqueId(),
    title: data.title || '新事件',
    description: data.description || '',
    startTime: data.startTime || { year: currentYear, month: 1, day: 1 },
    endTime: data.endTime || { year: currentYear, month: 1, day: 1 },
    type: data.type || 'primary',
    position: data.position || 300,
    width: data.width || 200,
    ...data
  };
}

/**
 * 计算事件在Y轴的位置
 * @param {Object} time 时间对象 (包含year属性)
 * @param {number} startYear 起始年份
 * @param {number} yearSpacing 每年的像素高度
 * @returns {number} Y轴位置
 */
export function calculateYPosition(time, startYear, yearSpacing) {
  if (!time || !time.year) return 100;
  
  const yearDiff = time.year - startYear;
  return yearDiff * yearSpacing;
}

/**
 * 将Canvas格式的时间线数据转换为API格式
 * @param {Array} mainTrunkEvents 主干事件
 * @param {Array} branchEvents 分支事件
 * @param {Array} branches 分支信息
 * @returns {Object} API格式数据
 */
export function convertToApiFormat(mainTrunkEvents, branchEvents, branches) {
  const apiData = {
    tracks: []
  };
  
  // 创建主干轨道
  const mainTrack = {
    id: 'main',
    name: '主线',
    type: 'main',
    events: mainTrunkEvents.map(event => ({
      id: event.id,
      title: event.title,
      description: event.description,
      startTime: event.time,
      endTime: event.time,
      type: event.type,
      position: event.renderPosition.y
    }))
  };
  
  apiData.tracks.push(mainTrack);
  
  // 创建分支轨道
  branches.forEach(branch => {
    const branchEventsForBranch = branchEvents.filter(e => e.branchId === branch.id);
    
    const trackData = {
      id: branch.id,
      name: branch.name,
      color: branch.color,
      type: 'branch',
      events: branchEventsForBranch.map(event => {
        const apiEvent = {
          id: event.id,
          title: event.title,
          description: event.description,
          startTime: event.time,
          endTime: event.time,
          type: event.type,
          position: event.renderPosition.y
        };
        
        // 添加连接信息
        if (event.connectionPoint) {
          apiEvent.connectTo = {
            time: {
              year: event.connectionYear
            }
          };
        }
        
        return apiEvent;
      })
    };
    
    apiData.tracks.push(trackData);
  });
  
  return apiData;
}

/**
 * 获取随机颜色
 * @returns {string} 颜色代码
 */
export function getRandomColor() {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'];
  return colors[Math.floor(Math.random() * colors.length)];
}

/**
 * 格式化年份显示
 * @param {Object} timeObj 时间对象
 * @returns {string} 格式化的时间字符串
 */
export function formatYearText(timeObj) {
  if (!timeObj) return '';
  return `${timeObj.year}年`;
} 