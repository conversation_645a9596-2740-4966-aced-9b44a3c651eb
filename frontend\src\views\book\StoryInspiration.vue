<template>
  <div class="story-inspiration">
    <!-- 添加数据加载指示器 -->
    <div v-if="!dataReady && inspirationStore.isLoading" class="data-loading-overlay">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <p>正在加载灵感卡池数据...</p>
    </div>
    
    <!-- 添加数据错误提示 -->
    <div v-else-if="inspirationStore.hasError" class="data-error-overlay">
      <el-icon class="error-icon"><WarningFilled /></el-icon>
      <p>加载灵感卡池数据失败</p>
      <p class="error-message">{{ inspirationStore.error }}</p>
      <div class="error-actions">
        <el-button type="primary" @click="retryLoading">重试</el-button>
        <el-button type="info" @click="showDebugInfo">诊断信息</el-button>
        <el-button type="danger" @click="resetInspirationData">重置数据</el-button>
      </div>
    </div>
    
    <!-- 现有内容包裹在这里 -->
    <el-tabs v-else v-model="activeTab" class="inspiration-tabs">
      <!-- 原型组合标签页 -->
      <el-tab-pane label="原型组合" name="archetype">
        <div class="archetype-container">
          <!-- 选择区域 - 上半部分 -->
          <div class="selection-section">
            <div class="selection-area">
              <!-- 动态生成分类选择 -->
              <div 
                v-for="(config, categoryKey) in categories" 
                :key="categoryKey"
                class="selection-column">
                <div class="column-header">
                  <div class="header-content">
                    <span>{{ config.name }}</span>
                  </div>
                  <el-select
                    v-model="selectionCounts[categoryKey]"
                    class="count-select"
                    size="small"
                    placeholder="选择个数">
                    <el-option
                      v-for="n in config.maxCount"
                      :key="n"
                      :label="n + '个'"
                      :value="n">
                    </el-option>
                  </el-select>
                  <el-button
                    class="edit-button"
                    size="small"
                    type="primary"
                    @click="showElementEditor(categoryKey)"
                    icon="Edit"
                    circle
                  />
                </div>
                <el-scrollbar height="calc(100% - 42px)">
                  <el-checkbox-group v-model="selectedElements[categoryKey]" class="checkbox-list">
                    <el-checkbox
                      v-for="element in getElementsByCategory(categoryKey)"
                      :key="element.title"
                      :value="element.title">
                      {{ element.title }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-scrollbar>
              </div>
            </div>

            <div class="control-area">
              <!-- 操作按钮 -->
              <div class="action-buttons">
                <div class="fixed-elements-indicator" v-if="hasFixedElements">
                  <span class="fixed-count">已固定: {{ totalFixedElements }}个</span>
                  <el-tooltip content="点击元素可固定/解除固定，固定的元素在随机时将被保留">
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <div class="button-group">
                  <el-button type="primary" size="default" @click="generateCombination" class="action-button generate-button">
                    <el-icon class="button-icon"><CopyDocument /></el-icon>
                    生成组合
                  </el-button>
                </div>
                <div class="button-group">
                  <el-button type="success" size="default" @click="randomSelect" class="action-button random-button">
                    <el-icon class="button-icon"><Refresh /></el-icon>
                    随机灵感
                  </el-button>
                </div>
                <el-button type="info" size="default" @click="resetSelection" class="action-button reset-button">
                  <el-icon class="button-icon"><Delete /></el-icon>
                  重置选择
                </el-button>
              </div>
            </div>
          </div>

          <!-- 结果区域 - 下半部分 -->
          <div class="result-section">
            <template v-if="combinationResult">
            <div class="result-header">
              <div class="result-header-left">
                <el-icon class="result-icon"><Star /></el-icon>
                <h2>灵感组合结果</h2>
              </div>
              <div class="interaction-tips">
                <el-tooltip placement="top" effect="light">
                  <template #content>
                    <div style="text-align: left;">
                      <div><el-icon><Mouse /></el-icon> <b>单击</b>: 固定/解除固定元素</div>
                      <div><el-icon><DCaret /></el-icon> <b>双击</b>: 查看元素详情</div>
                    </div>
                  </template>
                  <el-icon class="tips-icon" :size="20"><InfoFilled /></el-icon>
                </el-tooltip>
              </div>
            </div>
            <div class="result-content">
              <div class="result-grid">
                  <!-- 动态生成结果展示 -->
                  <div 
                    v-for="(config, categoryKey) in categories" 
                    :key="categoryKey"
                    class="result-section-card">
                  <div class="section-header">
                      <el-icon><component :is="config.icon" /></el-icon>
                      <span>{{ config.name }}</span>
                      <div class="element-count">{{ combinationResult[categoryKey].length }}个元素</div>
                  </div>
                  <div class="tag-group">
                    <el-tag
                        v-for="element in combinationResult[categoryKey]"
                        :key="element"
                        class="result-tag"
                        :class="{ 'fixed-element': fixedElements[categoryKey][element] }"
                        effect="light"
                        :type="config.color"
                        @click="toggleElementFixed(categoryKey, element)"
                        @dblclick="showElementDetail(categoryKey, element)">
                        <el-icon v-if="fixedElements[categoryKey][element]" class="fixed-icon"><Lock /></el-icon>
                        {{ element }}
                    </el-tag>
                  </div>
                </div>
                  </div>
              <!-- 添加科技感悬浮元素 -->
              <div class="tech-floater">
                <div class="tech-floater-content">
                  <div class="tech-circle"></div>
                  <div class="tech-lines"></div>
                  <div class="tech-dots"></div>
                </div>
              </div>
                  </div>
            </template>
            <template v-else>
              <!-- 提示用户生成结果的占位区域 -->
              <div class="empty-result">
                <el-icon class="empty-icon"><CopyDocument /></el-icon>
                <p>请从上方选择元素并点击"生成组合"或"随机灵感"按钮</p>
                </div>
            </template>
          </div>
        </div>
      </el-tab-pane>

      <!-- 剧情优化标签页 -->
      <el-tab-pane label="剧情优化" name="optimize">
        <div class="optimize-container">
          <el-select v-model="activeSystem" placeholder="选择替换系统" class="system-select">
            <el-option
              v-for="(system, key) in plotElementSwapper"
              :key="key"
              :label="system.description"
              :value="key">
            </el-option>
          </el-select>

          <div v-if="currentSystem" class="system-content">
            <el-collapse v-model="activeElements">
              <el-collapse-item
                v-for="element in currentSystem.elements"
                :key="element.type"
                :title="element.type"
                :name="element.type">
                <div class="options-grid">
                  <el-card
                    v-for="option in element.options"
                    :key="option.name"
                    class="option-card"
                    :class="{ 'selected': isOptionSelected(element.type, option) }"
                    @click="selectOption(element.type, option)">
                    <template #header>
                      <div class="option-header">{{ option.name }}</div>
                    </template>
                    <div class="option-content">
                      <p>{{ option.description }}</p>
                      <p v-if="option.effect" class="effect">效果: {{ option.effect }}</p>
                    </div>
                  </el-card>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 元素编辑对话框 -->
    <el-dialog
      v-model="elementEditorVisible"
      :title="getEditorTitle()"
      width="70%"
      class="element-editor-dialog"
      destroy-on-close="false"
      :append-to-body="true">
      
      <div class="editor-container">
        <div class="editor-toolbar">
          <el-button type="primary" size="default" @click="addNewElement">
            <el-icon><Plus /></el-icon> 添加新元素
          </el-button>
          <el-button type="success" size="default" @click="saveCustomElements">
            <el-icon><Check /></el-icon> 保存修改
          </el-button>
          <el-button size="default" @click="exportElements">
            <el-icon><Download /></el-icon> 导出配置
          </el-button>
          <el-button type="warning" size="default" @click="importElements">
            <el-icon><Upload /></el-icon> 导入配置
          </el-button>
  </div>
        
        <el-table :data="customElements" style="width: 100%" max-height="450px" border>
          <el-table-column label="标题" width="180">
            <template #default="{ row }">
              <el-input v-model="row.title" placeholder="输入标题" size="default" />
            </template>
          </el-table-column>
          
          <el-table-column label="描述">
            <template #default="{ row }">
              <el-input v-model="row.description" type="textarea" placeholder="输入描述" :rows="2" size="default" />
            </template>
          </el-table-column>
          
          <el-table-column label="情感走向" width="120">
            <template #default="{ row }">
              <el-select v-model="row.emotion" placeholder="选择走向" size="default">
                <el-option label="上升 ↑" value="↑" />
                <el-option label="下降 ↓" value="↓" />
                <el-option label="波动 ↓|↑" value="↓ | ↑" />
              </el-select>
            </template>
          </el-table-column>
          
          <el-table-column label="示例" width="140">
            <template #default="{ row }">
              <el-popover
                placement="right"
                :width="350"
                trigger="click">
                <template #reference>
                  <el-button size="default">编辑 ({{ row.examples?.length || 0 }})</el-button>
                </template>
                <div class="examples-editor">
                  <div v-for="(example, index) in row.examples || []" :key="index" class="example-item">
                    <el-input v-model="row.examples[index]" placeholder="输入示例" size="default" />
                    <el-button type="danger" @click="removeExample(row, index)" size="small" circle>
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                  <el-button type="primary" @click="addExample(row)" size="default">
                    <el-icon><Plus /></el-icon> 添加示例
                  </el-button>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" @click="removeElement($index)" size="default" circle>
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 导入配置对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入配置"
      width="800px"
      class="import-config-dialog"
      :close-on-click-modal="false"
      :show-close="true"
      :append-to-body="true"
      :top="'5vh'">
      <div class="import-content">
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="format-hint-title">
                <el-icon><InfoFilled /></el-icon>
                <span>查看JSON格式示例</span>
              </div>
            </template>
            <div class="format-hint-content">
              <pre>[
  {
    "title": "元素标题",
    "description": "元素描述",
    "emotion": "↑",
    "examples": ["示例1", "示例2"]
  },
  {
    "title": "另一个元素",
    "description": "另一个描述",
    "emotion": "↓ | ↑",
    "examples": ["示例1"]
  }
]</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
        
        <div class="import-options">
          <el-input
            v-model="importJsonContent"
            type="textarea"
            :rows="12"
            placeholder="请粘贴有效的JSON数据"
            class="import-input"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确认导入</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 元素详情卡片对话框 -->
    <el-dialog
      v-model="elementDetailVisible"
      width="550px"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :append-to-body="true"
      :center="true"
      :lock-scroll="true"
      class="element-detail-dialog tech-card"
      destroy-on-close>
      <template #header>
        <div class="detail-header">
          <div class="tech-lines"></div>
          <div class="detail-title">
            <span class="detail-category-badge" :class="currentDetailCategory">
              {{ categories[currentDetailCategory]?.name || '元素' }}
            </span>
            <h3>{{ currentDetailElement?.title }}</h3>
          </div>
          <el-tag 
            v-if="currentDetailElement?.emotion"
            class="emotion-tag"
            :type="getEmotionType(currentDetailElement.emotion)">
            {{ getEmotionLabel(currentDetailElement.emotion) }}
          </el-tag>
        </div>
      </template>
      
      <div class="detail-content-wrapper" v-if="currentDetailElement">
        <el-scrollbar height="auto" max-height="450px" class="detail-scrollbar">
          <div class="detail-content">
            <div class="detail-section">
              <h4 class="detail-section-title">
                <span class="icon-container">
                  <svg viewBox="0 0 24 24" class="tech-icon">
                    <path d="M4 5h16v2H4zm0 6h16v2H4zm0 6h16v2H4z"/>
                  </svg>
                </span>
                描述
              </h4>
              <p class="detail-description">{{ currentDetailElement.description }}</p>
            </div>
            
            <div class="detail-section" v-if="currentDetailElement.examples && currentDetailElement.examples.length > 0">
              <h4 class="detail-section-title">
                <span class="icon-container">
                  <svg viewBox="0 0 24 24" class="tech-icon">
                    <path d="M13 10h5l-6 6-6-6h5V4h2v6z"/>
                  </svg>
                </span>
                示例
              </h4>
              <ul class="detail-examples-list">
                <li v-for="(example, index) in currentDetailElement.examples" :key="index">
                  <div class="example-bullet"></div>
                  <span>{{ example }}</span>
                </li>
              </ul>
            </div>
            
            <div class="tech-decoration">
              <svg viewBox="0 0 100 100" class="corner-decoration top-left">
                <path d="M0 0 L40 0 L40 5 L5 5 L5 40 L0 40 Z" />
              </svg>
              <svg viewBox="0 0 100 100" class="corner-decoration top-right">
                <path d="M100 0 L60 0 L60 5 L95 5 L95 40 L100 40 Z" />
              </svg>
              <svg viewBox="0 0 100 100" class="corner-decoration bottom-left">
                <path d="M0 100 L40 100 L40 95 L5 95 L5 60 L0 60 Z" />
              </svg>
              <svg viewBox="0 0 100 100" class="corner-decoration bottom-right">
                <path d="M100 100 L60 100 L60 95 L95 95 L95 60 L100 60 Z" />
              </svg>
            </div>
          </div>
        </el-scrollbar>
      </div>
      
      <template #footer>
        <div class="detail-footer">
          <div class="tech-pulse"></div>
          <el-button 
            @click="elementDetailVisible = false" 
            class="tech-button"
            round>
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive, nextTick, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CopyDocument, Refresh, Delete, Star, Sunrise, Connection, Key, TrendCharts, Plus, Check, Download, Upload, Edit, InfoFilled, Lock, Mouse, DCaret, Loading, WarningFilled, Moon, Sunny } from '@element-plus/icons-vue'

import { storyInspirationConfig } from '@/config/storyInspiration'
import { plotElementSwapper } from '@/config/betterPlot'
import { useConfigStore } from '@/stores/config'
import { useLoading } from '@/composables/useLoading'
import { useInspirationStore } from '@/stores/inspiration'

// 获取全局配置Store
const configStore = useConfigStore()

// 创建加载状态处理器
const { startLoading, stopLoading } = useLoading()

// 标签页控制
const activeTab = ref('archetype')

// 使用新的存储
const inspirationStore = useInspirationStore()

// 初始化卡池数据
const inspirationData = ref({
  categories: {},
  theme: [],
  volume: [],
  keyPoint: [],
  technique: []
})

// 替换原来的初始化配置方法
const loadInspirationData = async () => {
  try {
    startLoading('正在加载灵感卡池...')
    // 调用后端API获取故事灵感数据
    const response = await window.pywebview.api.get_story_inspiration()
    if (response.success && response.data) {
      inspirationData.value = response.data
    } else {
      console.error('加载灵感卡池失败:', response.message)
      ElMessage.error('加载灵感卡池失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载灵感卡池出错:', error)
    ElMessage.error('加载灵感卡池出错')
  } finally {
    stopLoading()
  }
}

// 替换原来的从配置中获取分类的computed
const categories = computed(() => {
  return inspirationStore.categories
})

// 选择状态
const selectedElements = reactive({
  theme: [],
  volume: [],
  keyPoint: [],
  technique: []
})

// 固定元素状态
const fixedElements = reactive({
  theme: {},
  volume: {},
  keyPoint: {},
  technique: {}
})

const combinationResult = ref(null)

// 监听selectedElements变化，自动更新结果区域
watch(selectedElements, (newValue) => {
  // 只有当至少有一个分类有选中元素时才更新结果
  const hasSelectedElements = Object.values(newValue).some(category => category.length > 0)
  if (hasSelectedElements) {
    // 更新结果区域，避免直接引用同一对象
    combinationResult.value = JSON.parse(JSON.stringify(newValue))
  }
}, { deep: true })

// 修改选择个数配置的初始化方式
// 将初始值设为固定数字，避免未加载数据时的错误
const selectionCounts = reactive({
  theme: 2,
  volume: 4,
  keyPoint: 5,
  technique: 3
})

// 移除第二个重复声明的 selectionCounts
// 保留 watch 监听器以在数据加载后更新这些值
watch(() => inspirationStore.categories, (newCategories) => {
  if (newCategories) {
    // 使用可选链操作符避免undefined错误
    if (newCategories.theme?.defaultCount) selectionCounts.theme = newCategories.theme.defaultCount
    if (newCategories.volume?.defaultCount) selectionCounts.volume = newCategories.volume.defaultCount
    if (newCategories.keyPoint?.defaultCount) selectionCounts.keyPoint = newCategories.keyPoint.defaultCount
    if (newCategories.technique?.defaultCount) selectionCounts.technique = newCategories.technique.defaultCount
  }
}, { immediate: true })

// 替换getElementsByCategory方法
const getElementsByCategory = (category) => {
  return inspirationStore.getElementsByCategory(category)
}

// 获取问题标题
const getQuestionTitle = (type) => {
  return categories.value[type]?.name + '相关问题' || type
}

// 随机选择
const randomSelect = () => {
  // 检查数据是否已加载
  if (!inspirationStore.isLoaded) {
    ElMessage.warning('数据正在加载中，请稍候...')
    return
  }
  
  // 遍历所有分类
  Object.keys(categories.value).forEach(category => {
    // 获取该分类的元素
    const elements = getElementsByCategory(category)
    
    // 保留已固定的元素
    const fixedTitles = Object.keys(fixedElements[category]).filter(title => fixedElements[category][title])
    
    // 计算需要随机选择的数量
    const remainingCount = selectionCounts[category] - fixedTitles.length
    
    if (remainingCount <= 0) {
      // 如果固定的元素已经达到或超过总数，只保留固定元素（最多不超过总数）
      selectedElements[category] = fixedTitles.slice(0, selectionCounts[category])
    } else {
      // 过滤掉已固定的元素，从剩余元素中随机选择
      const availableElements = elements.filter(element => !fixedElements[category][element.title])
      const randomElements = getRandomElements(availableElements, remainingCount)
      
      // 合并固定元素和随机元素
      selectedElements[category] = [
        ...fixedTitles,
        ...randomElements.map(element => element.title)
      ]
    }
  })
  
  generateCombination()
}

// 获取随机元素
const getRandomElements = (array, count) => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// 生成组合
const generateCombination = () => {
  // 检查数据是否已加载
  if (!inspirationStore.isLoaded) {
    ElMessage.warning('数据正在加载中，请稍候...')
    return
  }
  
  // 验证是否每个类型都至少选择了一个元素
  const isValid = Object.keys(categories.value).every(category => 
    selectedElements[category].length > 0
  )
  
  if (!isValid) {
    ElMessage.warning('请确保每个类型都至少选择了一个元素')
    return
  }

  // 生成组合结果
  const formattedResult = Object.keys(categories.value).flatMap(category => [
    `【${categories.value[category].name}】`,
    ...formatElements(selectedElements[category], categories.value[category].name),
    ''
  ]).join('\n')

  // 复制到剪贴板
  window.pywebview.api.copy_to_clipboard(formattedResult)
    .then(() => {
      ElMessage.success({
        message: '组合已生成并复制到剪贴板',
        duration: 2000
      })
    })
    .catch(() => {
      ElMessage.warning({
        message: '复制到剪贴板失败，请手动复制',
        duration: 2000
      })
    })

  // 强制刷新结果区域
  combinationResult.value = JSON.parse(JSON.stringify(selectedElements))
  console.log('组合结果已生成:', combinationResult.value)
}

// 格式化元素
const formatElements = (elements, type) => {
  return elements.map((element, index) => {
    return `${index + 1}. ${element}`
  })
}

// 重置选择
const resetSelection = () => {
  Object.keys(categories.value).forEach(category => {
    selectedElements[category] = []
    // 同时清除固定状态
    fixedElements[category] = {}
  })
  // 清空结果区域
  combinationResult.value = null
}

// 添加点击计时器跟踪变量
const clickTimer = ref(null)
const pendingClick = ref(null)

// 切换元素固定状态 - 使用点击延迟解决单双击冲突
const toggleElementFixed = (category, elementTitle) => {
  // 清除之前的点击计时器（如果存在）
  if (clickTimer.value !== null) {
    clearTimeout(clickTimer.value)
    clickTimer.value = null
    
    // 如果当前点击与之前相同，说明是双击，不执行切换操作
    if (pendingClick.value && 
        pendingClick.value.category === category && 
        pendingClick.value.elementTitle === elementTitle) {
      pendingClick.value = null
      return
    }
  }
  
  // 保存当前点击信息并设置延迟
  pendingClick.value = { category, elementTitle }
  
  // 设置300ms延迟，等待是否有双击发生
  clickTimer.value = setTimeout(() => {
    // 延迟后执行切换操作（如果没有触发双击）
    if (pendingClick.value) {
      if (fixedElements[category][elementTitle]) {
        // 取消固定
        fixedElements[category][elementTitle] = false
      } else {
        // 固定元素
        fixedElements[category][elementTitle] = true
      }
      
      // 清除点击状态
      pendingClick.value = null
      clickTimer.value = null
    }
  }, 300) // 300ms是一个合理的双击时间窗口
}

// 剧情优化相关
const activeSystem = ref('')
const activeElements = ref([])
const selectedOptions = reactive({})

// 计算当前系统
const currentSystem = computed(() => 
  activeSystem.value ? plotElementSwapper[activeSystem.value] : null
)

// 选择选项
const selectOption = (elementType, option) => {
  selectedOptions[elementType] = option
  ElMessage.success(`已选择: ${option.name}`)
}

// 判断选项是否被选中
const isOptionSelected = (elementType, option) => {
  return selectedOptions[elementType]?.name === option.name
}

// 元素编辑功能
const elementEditorVisible = ref(false)
const currentEditType = ref(null) // 当前正在编辑的类别类型
const customElements = ref([])
const localStorageKey = 'pvv_story_inspiration_custom'

// 添加缓存系统
const editorCache = reactive({
  theme: null,
  volume: null,
  keyPoint: null,
  technique: null
})

// 编辑对话框标题
const getEditorTitle = () => {
  return categories.value[currentEditType.value]?.name ? 
    `编辑${categories.value[currentEditType.value].name}元素` : 
    '编辑元素'
}

// 优化后的打开编辑对话框函数
const showElementEditor = (type) => {
  // 先设置当前编辑类型
  currentEditType.value = type
  
  // 显示对话框（先让用户看到UI反馈）
  elementEditorVisible.value = true
  
  // 创建加载指示器
  const loadingInstance = ElMessage({
    message: '正在准备编辑器数据...',
    type: 'info',
    duration: 0
  })
  
  // 使用缓存或异步加载数据
  nextTick(() => {
    try {
      // 检查是否有缓存
      if (editorCache[type]) {
        console.log('使用缓存数据')
        customElements.value = editorCache[type]
        loadingInstance.close()
        return
      }
      
      // 无缓存时异步处理数据
      setTimeout(() => {
        let sourceData = getElementsByCategory(type)
        
        // 优化复制方式
        if (Array.isArray(sourceData)) {
          customElements.value = sourceData.map(item => ({...item}))
          
          // 保存到缓存
          editorCache[type] = [...customElements.value]
        } else {
          customElements.value = []
        }
        
        loadingInstance.close()
      }, 100)
    } catch (error) {
      console.error('加载编辑器数据失败:', error)
      loadingInstance.close()
      ElMessage.error('加载数据失败，请重试')
    }
  })
}

// 保存自定义元素方法
const saveCustomElements = async () => {
  try {
    startLoading('正在保存灵感元素...')
    const category = currentEditType.value // 使用现有的类别变量
    
    if (!category) {
      ElMessage.warning('没有选择灵感类别')
      return
    }
    
    console.log(`保存${categories.value[category]?.name || category}元素`, customElements.value)
    
    // 保存到存储
    await inspirationStore.saveInspirationCategory(category, customElements.value)
    
    // 成功提示
    ElMessage({
      type: 'success',
      message: `灵感类别 ${category} 保存成功`,
      duration: 2000
    })
    
    // 更新缓存
    editorCache[category] = [...customElements.value]
    
    // 关闭编辑器
    elementEditorVisible.value = false
  } catch (error) {
    // 错误处理
    console.error('保存自定义元素失败:', error)
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    stopLoading()
  }
}

// 添加新元素
const addNewElement = () => {
  customElements.value.push({
    title: '新元素',
    description: '请输入描述',
    emotion: '↑',
    examples: ['请添加示例']
  })
}

// 移除元素
const removeElement = (index) => {
  ElMessageBox.confirm('确定要删除这个元素吗？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    customElements.value.splice(index, 1)
    ElMessage.success('元素已删除')
  }).catch(() => {})
}

// 添加示例
const addExample = (row) => {
  if (!row.examples) {
    row.examples = []
  }
  row.examples.push('')
}

// 移除示例
const removeExample = (row, index) => {
  row.examples.splice(index, 1)
}

// 导入配置相关
const importDialogVisible = ref(false)
const importJsonContent = ref('')

// 显示导入对话框
const importElements = () => {
  importDialogVisible.value = true
  importJsonContent.value = ''
}

// 确认导入
const confirmImport = async () => {
  try {
    // 验证数据
    if (!importJsonContent.value.trim()) {
      ElMessage.warning('请输入JSON数据')
      return
    }
    
    // 解析JSON
    let jsonData
    try {
      jsonData = JSON.parse(importJsonContent.value)
    } catch (error) {
      ElMessage.error('JSON格式无效，请检查您的输入')
      return
    }
    
    // 验证数据格式
    if (!Array.isArray(jsonData)) {
      ElMessage.error('导入失败：数据必须是数组格式')
      return
    }
    
    // 验证每个元素是否有必要的字段
    for (const item of jsonData) {
      if (!item.title || !item.description) {
        ElMessage.error('导入失败：数据中有元素缺少标题或描述字段')
        return
      }
    }
    
    // 更新数据
    customElements.value = jsonData
    
    // 更新到缓存
    if (currentEditType.value) {
      editorCache[currentEditType.value] = [...jsonData]
    }
    
    // 关闭对话框并提示成功
    importDialogVisible.value = false
    ElMessage.success(`成功导入 ${jsonData.length} 个元素`)
    
    console.log('导入的元素数量:', jsonData.length)
  } catch (error) {
    console.error('导入失败', error)
    ElMessage.error('导入失败：' + error.message)
  }
}

// 导出配置
const exportElements = () => {
  try {
    // 美化JSON格式，使用2个空格缩进
    const dataStr = JSON.stringify(customElements.value, null, 2)
    
    // 复制到剪贴板
    window.pywebview.api.copy_to_clipboard(dataStr)
      .then(() => {
        ElMessage.success(`已复制 ${customElements.value.length} 个${categories.value[currentEditType.value]?.name || ''}元素到剪贴板`)
      })
      .catch((error) => {
        console.error('复制到剪贴板失败:', error)
        ElMessage.error('复制到剪贴板失败，请检查浏览器权限')
        
        // 备用复制方法
        try {
          const textarea = document.createElement('textarea')
          textarea.value = dataStr
          textarea.style.position = 'fixed'
          textarea.style.opacity = '0'
          document.body.appendChild(textarea)
          textarea.select()
          const success = document.execCommand('copy')
          document.body.removeChild(textarea)
          
          if (success) {
            ElMessage.success('使用备用方法复制成功')
          } else {
            ElMessage.error('复制失败，请手动复制')
          }
        } catch (e) {
          console.error('备用复制方法失败:', e)
          ElMessage.error('复制失败，请手动复制')
        }
      })
    
    console.log('导出的元素数量:', customElements.value.length)
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

// 页面加载时预加载常用分类数据
onMounted(async () => {
  try {
    startLoading('正在加载灵感卡池...')
    await inspirationStore.loadInspirationData()
    dataReady.value = true
    
    // 数据加载成功后初始化选择计数
    const cats = inspirationStore.categories
    if (cats) {
      if (cats.theme?.defaultCount) selectionCounts.theme = cats.theme.defaultCount
      if (cats.volume?.defaultCount) selectionCounts.volume = cats.volume.defaultCount
      if (cats.keyPoint?.defaultCount) selectionCounts.keyPoint = cats.keyPoint.defaultCount
      if (cats.technique?.defaultCount) selectionCounts.technique = cats.technique.defaultCount
    }
  } catch (error) {
    console.error('加载灵感卡池失败:', error)
    ElMessage.error('加载灵感卡池出错，请刷新页面重试')
  } finally {
    stopLoading()
  }
  
  // 后台预加载各分类数据到缓存
  setTimeout(() => {
    ['theme', 'volume', 'keyPoint', 'technique'].forEach(type => {
      if (!editorCache[type]) {
        let data = inspirationStore.getElementsByCategory(type)
        if (Array.isArray(data)) {
          editorCache[type] = data.map(item => ({...item}))
        }
      }
    })
    console.log('编辑器数据预加载完成')
  }, 1000)
})

// 元素详情卡片对话框
const elementDetailVisible = ref(false)
const currentDetailCategory = ref('')
const currentDetailElement = ref(null)

// 获取情感类型
const getEmotionType = (emotion) => {
  switch (emotion) {
    case '↑':
      return 'success'
    case '↓':
      return 'danger'
    case '↓ | ↑':
      return 'info'
    default:
      return 'info'
  }
}

// 获取情感标签
const getEmotionLabel = (emotion) => {
  switch (emotion) {
    case '↑':
      return '上升'
    case '↓':
      return '下降'
    case '↓ | ↑':
      return '波动'
    default:
      return '未知'
  }
}

// 显示元素详情
const showElementDetail = (category, elementTitle) => {
  // 清除任何挂起的单击操作
  if (clickTimer.value !== null) {
    clearTimeout(clickTimer.value)
    clickTimer.value = null
    pendingClick.value = null
  }
  
  // 设置当前类别
  currentDetailCategory.value = category
  
  // 根据标题查找完整的元素对象
  const allElements = getElementsByCategory(category)
  const foundElement = allElements.find(item => item.title === elementTitle)
  
  if (foundElement) {
    currentDetailElement.value = foundElement
    elementDetailVisible.value = true
  } else {
    // 如果找不到完整数据，只显示标题
    currentDetailElement.value = { title: elementTitle }
    elementDetailVisible.value = true
    console.warn(`未找到元素完整信息: ${elementTitle}`)
  }
}

// 计算已固定的元素数量
const hasFixedElements = computed(() => {
  return Object.values(fixedElements).some(category => Object.values(category).some(Boolean))
})

// 计算所有已固定的元素数量
const totalFixedElements = computed(() => {
  return Object.values(fixedElements).reduce((total, category) => total + Object.values(category).filter(Boolean).length, 0)
})

// 添加数据就绪标志
const dataReady = ref(false)

// 添加重试加载功能
const retryLoading = async () => {
  try {
    startLoading('正在重新加载灵感卡池...')
    await inspirationStore.loadInspirationData()
    dataReady.value = true
    
    // 更新选择计数
    const cats = inspirationStore.categories
    if (cats) {
      if (cats.theme?.defaultCount) selectionCounts.theme = cats.theme.defaultCount
      if (cats.volume?.defaultCount) selectionCounts.volume = cats.volume.defaultCount
      if (cats.keyPoint?.defaultCount) selectionCounts.keyPoint = cats.keyPoint.defaultCount
      if (cats.technique?.defaultCount) selectionCounts.technique = cats.technique.defaultCount
    }
    
    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('重新加载灵感卡池失败:', error)
    ElMessage.error('重新加载失败，请检查网络连接')
  } finally {
    stopLoading()
  }
}

// 在脚本部分添加
const showDebugInfo = () => {
  ElMessageBox.alert(
    `错误信息: ${inspirationStore.error}\n
    API状态: ${window.pywebview ? '可用' : '不可用'}\n
    图书控制器: ${window.pywebview?.api?.book_controller ? '可用' : '不可用'}`,
    '诊断信息',
    { type: 'warning' }
  )
}

// 添加重置数据功能
const resetInspirationData = async () => {
  try {
    if (!confirm('确定要重置灵感卡池数据吗？这将删除所有自定义内容。')) {
      return
    }
    
    startLoading('正在重置灵感卡池数据...')
    
    // 从备份/配置中读取初始数据
    const defaultData = {
      categories: {
        theme: {
          name: "主题层",
          description: "故事的核心主题与情感基调",
          icon: "Sunrise", 
          color: "primary",
          defaultCount: 2,
          maxCount: 5
        },
        volume: {
          name: "卷级结构",
          description: "故事的大纲架构与发展脉络",
          icon: "Connection",
          color: "success",
          defaultCount: 4,
          maxCount: 8
        },
        keyPoint: {
          name: "关键点",
          description: "故事中的重要转折与关键节点",
          icon: "Key",
          color: "warning", 
          defaultCount: 5,
          maxCount: 8
        },
        technique: {
          name: "技法卡",
          description: "用于优化剧情的各种写作技巧",
          icon: "TrendCharts",
          color: "danger",
          defaultCount: 3,
          maxCount: 5
        }
      },
      theme: [],
      volume: [],
      keyPoint: [],
      technique: []
    }
    
    // 保存默认数据
    await inspirationStore.saveInspirationData(defaultData)
    
    // 重新加载
    await inspirationStore.loadInspirationData()
    dataReady.value = true
    
    ElMessage.success('灵感卡池数据已重置')
  } catch (error) {
    console.error('重置数据失败:', error)
    ElMessage.error('重置数据失败: ' + error.message)
  } finally {
    stopLoading()
  }
}

</script>

<style scoped>
.story-inspiration {
  width: 100%;
  height: 100%;
  min-height: 600px; /* 确保最小高度 */
  display: flex;
  flex-direction: column;
  background: var(--inspiration-bg, linear-gradient(135deg, rgba(18, 18, 32, 0.95), rgba(27, 30, 54, 0.95)));
  position: relative;
  overflow: hidden;
  user-select: none; /* 添加禁止选择 */
  /* 定义主题颜色变量 */
  --primary-color: #4a6cf7;
  --primary-light: rgba(74, 108, 247, 0.3);
  --primary-lighter: rgba(74, 108, 247, 0.15);
  --primary-dark: #3a5cd7;
  --bg-card: rgba(30, 32, 50, 0.6);
  --bg-card-hover: rgba(35, 38, 60, 0.7);
  --text-primary: rgba(255, 255, 255, 0.9);
  --text-secondary: rgba(255, 255, 255, 0.7);
  --border-color: rgba(255, 255, 255, 0.08);
  --shadow-color: rgba(0, 0, 0, 0.2);
  --highlight-color: rgba(255, 255, 255, 0.05);
}

/* 明亮主题 */
html:not(.dark) .story-inspiration {
  --inspiration-bg: linear-gradient(135deg, rgba(245, 247, 250, 0.95), rgba(235, 240, 255, 0.95));
  --primary-color: #2a4cc7;
  --primary-light: rgba(42, 76, 199, 0.3);
  --primary-lighter: rgba(42, 76, 199, 0.15);
  --primary-dark: #1a3cb7;
  --bg-card: rgba(255, 255, 255, 0.9);
  --bg-card-hover: rgba(255, 255, 255, 1);
  --text-primary: rgba(20, 20, 35, 1);
  --text-secondary: rgba(40, 40, 60, 0.8);
  --border-color: rgba(20, 20, 35, 0.15);
  --shadow-color: rgba(0, 0, 0, 0.15);
  --highlight-color: rgba(42, 76, 199, 0.05);
}

/* 更新背景样式 */
.story-inspiration::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, 
    var(--primary-lighter) 0%,
    transparent 70%);
  opacity: 0.6;
  pointer-events: none;
  z-index: 0;
}

/* 增加微妙的纹理背景 */
.story-inspiration::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(var(--highlight-color) 1px, transparent 1px),
    linear-gradient(90deg, var(--highlight-color) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
  pointer-events: none;
  z-index: 0;
}

.inspiration-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  overflow: visible; /* 允许内容溢出显示 */
  padding: 10px;
}

.inspiration-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 10px 15px 0;
  border: none;
  position: relative;
  flex-shrink: 0;
  user-select: none; /* 添加禁止选择 */
}

.inspiration-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.inspiration-tabs :deep(.el-tabs__nav) {
  border: none;
  background: var(--highlight-color);
  border-radius: 16px;
  padding: 4px;
  box-shadow: 0 4px 16px var(--shadow-color);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border: 1px solid var(--border-color);
}

.inspiration-tabs :deep(.el-tabs__item) {
  padding: 0 24px;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 12px;
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
  line-height: 1;
}

.inspiration-tabs :deep(.el-tabs__item.is-active) {
  color: var(--text-primary);
  background: var(--primary-lighter);
  box-shadow: 0 0 15px var(--primary-lighter);
  text-shadow: 0 0 10px var(--primary-lighter);
}

.inspiration-tabs :deep(.el-tabs__nav-wrap) {
  display: flex;
  justify-content: center;
  margin-bottom: 0;
  padding: 0;
}

.inspiration-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: visible; /* 允许内容溢出显示 */
  padding: 10px;
  display: flex;
  flex-direction: column;
  min-height: 500px; /* 确保有足够的最小高度 */
}

.archetype-container {
  width: 100%;
  min-height: 550px; /* 确保最小高度 */
  display: flex;
  flex-direction: column;
  overflow: visible; /* 允许内容溢出显示 */
  position: relative; /* 相对定位 */
}

/* 上半部分选择区域 */
.selection-section {
  display: flex;
  gap: 15px;
  height: 280px; /* 增加卡池高度 */
  max-height: 40vh; /* 增加最大高度比例 */
  flex-shrink: 0;
  margin-bottom: 20px; /* 增加底部间距 */
  border-bottom: 2px dashed rgba(255, 255, 255, 0.1); /* 添加底部边框作为视觉分隔 */
  padding-bottom: 15px; /* 边框内边距 */
}

.selection-area {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  overflow: auto;
  height: 100%;
}

@media (min-width: 1200px) {
  .selection-area {
    grid-template-columns: repeat(4, 1fr);
  }
}

.selection-column {
  display: flex;
  flex-direction: column;
  background: var(--highlight-color);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all 0.3s;
  /* 精致的阴影效果 */
  box-shadow: 0 8px 32px var(--shadow-color);
  height: 100%;
  transform: translateZ(0);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.selection-column:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: var(--primary-light);
  transform: translateY(-2px);
}

.selection-column .el-scrollbar {
  flex: 1;
  overflow: hidden;
}

.control-area {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 180px;
  flex-shrink: 0;
}

/* 下半部分结果区域 */
.result-section {
  flex: 1;
  min-height: 320px;
  flex-grow: 1;
  flex-basis: 50%;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 14px;
  padding: 18px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  overflow: auto;
  margin-top: 10px;
  z-index: 5;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.result-section-card {
  background: rgba(250, 252, 255, 0.95);
  border-radius: 14px;
  padding: 18px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(42, 76, 199, 0.2);
  position: relative;
  overflow: hidden;
  margin-bottom: 5px;
}

.result-section-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
  border-color: rgba(74, 108, 247, 0.3);
  transform: translateY(-2px);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 15px 20px;
  border-bottom: 2px solid rgba(42, 76, 199, 0.3);
  position: sticky;
  top: 0;
  background: linear-gradient(to right, rgba(235, 240, 255, 0.95), rgba(225, 232, 255, 0.95));
  z-index: 2;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  flex-shrink: 0;
  justify-content: space-between;
  user-select: none; /* 添加禁止选择 */
  overflow: hidden; /* 添加溢出隐藏 */
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.result-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right,
    rgba(74, 108, 247, 0),
    rgba(74, 108, 247, 1) 50%,
    rgba(74, 108, 247, 0)
  );
  box-shadow: 0 0 15px 2px rgba(74, 108, 247, 0.7);
  animation: headerLineScan 3s infinite ease-in-out alternate;
  opacity: 0.9;
}

@keyframes headerLineScan {
  0% { transform: translateX(-100%); opacity: 0.7; }
  50% { transform: translateX(0%); opacity: 1; }
  100% { transform: translateX(100%); opacity: 0.7; }
}

.result-header-left {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  z-index: 2;
}

.result-header-left::after {
  content: '';
  position: absolute;
  width: 150px;
  height: 40px;
  background: radial-gradient(circle, rgba(74, 108, 247, 0.2) 0%, transparent 70%);
  filter: blur(10px);
  z-index: -1;
}

.result-header h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  background: linear-gradient(120deg, #4a6cf7, #6e8fff, #4a6cf7);
  background-size: 200% auto;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
  animation: textShine 3s linear infinite;
  text-shadow: 0 2px 10px rgba(74, 108, 247, 0.3);
}

@keyframes textShine {
  to { background-position: 200% center; }
}

.result-icon {
  font-size: 28px;
  color: #f59e0b;
  animation: starRotate 4s linear infinite;
  filter: drop-shadow(0 0 10px rgba(245, 158, 11, 0.5));
  position: relative;
  z-index: 2;
}

.result-icon::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, rgba(245, 158, 11, 0.5) 0%, transparent 70%);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  filter: blur(5px);
  animation: starGlow 2s ease-in-out infinite alternate;
}

@keyframes starGlow {
  0% { opacity: 0.3; width: 15px; height: 15px; }
  100% { opacity: 0.8; width: 25px; height: 25px; }
}

.result-header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.interaction-tips {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  position: relative;
}

.interaction-tips::before {
  content: '';
  position: absolute;
  top: -80px;
  right: -80px;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(74, 108, 247, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulseEffect 4s ease-in-out infinite;
  z-index: 0;
  opacity: 0.7;
}

.interaction-tips::after {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 120px;
  height: 120px;
  border: 2px solid rgba(74, 108, 247, 0.6);
  border-radius: 50%;
  animation: expandRing 4s ease-in-out infinite;
  z-index: 0;
}

@keyframes pulseEffect {
  0% { transform: scale(0.8); opacity: 0.4; }
  50% { transform: scale(1.3); opacity: 0.7; }
  100% { transform: scale(0.8); opacity: 0.4; }
}

@keyframes expandRing {
  0% { transform: scale(0.6); opacity: 0.9; }
  100% { transform: scale(1.8); opacity: 0; }
}

.tips-icon {
  font-size: 20px;
  color: rgba(74, 108, 247, 0.7);
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  z-index: 5;
}

.tips-icon:hover {
  color: #4a6cf7;
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 0 8px rgba(74, 108, 247, 0.6));
  animation: iconFloat 1.5s ease-in-out infinite alternate;
}

@keyframes iconFloat {
  0% { transform: translateY(0) scale(1.1); }
  100% { transform: translateY(-3px) scale(1.15); }
}

.column-header {
  padding: 12px 14px;
  font-size: 15px;
  font-weight: 600;
  color: rgba(20, 20, 35, 1);
  background: linear-gradient(to right, 
    rgba(42, 76, 199, 0.2) 0%, 
    rgba(42, 76, 199, 0.1) 100%);
  border-bottom: 1px solid rgba(42, 76, 199, 0.2);
  display: flex !important;
  align-items: center !important;
  min-height: 42px;
  flex-shrink: 0;
  user-select: none; /* 添加禁止选择 */
  flex-direction: row !important;
  justify-content: space-between !important;
  width: 100%;
}

.column-header .header-content {
  display: flex !important;
  align-items: center !important;
  flex-direction: row !important;
  flex: 1;
  justify-content: flex-start !important;
  gap: 8px;
}

.column-header .count-select {
  width: 70px;
  margin: 0 4px;
  background: rgba(30, 32, 50, 0.7) !important;
  border-radius: 4px;
}

.column-header .count-select :deep(.el-input__wrapper) {
  background: rgba(30, 32, 50, 0.7) !important;
  box-shadow: 0 0 0 1px rgba(74, 108, 247, 0.3) !important;
  border-radius: 4px;
}

.column-header .count-select :deep(.el-input__inner) {
  color: rgba(255, 255, 255, 0.9) !important;
}

.column-header .count-select :deep(.el-select__caret) {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 下拉菜单样式 */
:deep(.el-select__popper) {
  background: rgba(30, 32, 50, 0.95) !important;
  border: 1px solid rgba(74, 108, 247, 0.3) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

:deep(.el-select-dropdown__item) {
  color: rgba(255, 255, 255, 0.9) !important;
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background: rgba(74, 108, 247, 0.2) !important;
}

:deep(.el-select-dropdown__item.selected) {
  background: rgba(74, 108, 247, 0.3) !important;
  color: #fff !important;
  font-weight: bold;
}

/* 添加小型下拉框的美化样式 */
:deep(.el-select--small .el-select__wrapper) {
  background: rgba(30, 32, 50, 0.8) !important;
  box-shadow: 0 0 0 1px rgba(74, 108, 247, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  border-radius: 6px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: none;
  padding: 2px 8px;
}

:deep(.el-select--small .el-select__wrapper:hover) {
  box-shadow: 0 0 0 1px rgba(74, 108, 247, 0.5), 0 4px 16px rgba(0, 0, 0, 0.3) !important;
  background: rgba(35, 38, 60, 0.85) !important;
}

:deep(.el-select--small .el-select__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.5), 0 4px 16px rgba(0, 0, 0, 0.3) !important;
  background: rgba(40, 44, 70, 0.9) !important;
}

:deep(.el-select--small .el-input__inner) {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 13px;
}

:deep(.el-select--small .el-select__caret) {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 12px;
  transition: all 0.3s ease;
}

:deep(.el-select--small:hover .el-select__caret) {
  color: rgba(255, 255, 255, 0.9) !important;
}

.edit-button {
  margin-left: 5px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
}

.edit-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  transform: rotate(90deg);
}

/* 美化复选框区域 */
.checkbox-list {
  padding: 8px;
}

.checkbox-list :deep(.el-checkbox) {
  margin-right: 0;
  margin-bottom: 8px;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(42, 76, 199, 0.15);
}

.checkbox-list :deep(.el-checkbox:hover) {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(42, 76, 199, 0.3);
}

.checkbox-list :deep(.el-checkbox.is-checked) {
  background: rgba(42, 76, 199, 0.1);
  border-color: rgba(42, 76, 199, 0.4);
}

.checkbox-list :deep(.el-checkbox__label) {
  font-size: 14px;
  line-height: 1.4;
  white-space: normal;
  word-break: break-word;
  color: rgba(20, 20, 35, 0.9);
}

.checkbox-list :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #4a6cf7;
  border-color: #4a6cf7;
  box-shadow: 0 0 8px rgba(74, 108, 247, 0.5);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  height: 100%;
  user-select: none; /* 添加禁止选择 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* 优化按钮样式 */
.generate-button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
  color: white;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 15px var(--primary-lighter);
  border-radius: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.generate-button:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--primary-light);
}

.random-button {
  background: linear-gradient(135deg, #10b981, #34d399);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  border: none;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.random-button:hover {
  background: linear-gradient(135deg, #34d399, #10b981);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.reset-button {
  background: linear-gradient(135deg, #6b7280, #9ca3af);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
  border: none;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.reset-button:hover {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.button-group .action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.button-icon {
  margin-right: 8px;
  font-size: 18px;
  transition: all 0.3s ease;
}

.generate-button:hover .button-icon {
  transform: scale(1.2);
}

.random-button:hover .button-icon {
  transform: rotate(180deg);
}

.reset-button:hover .button-icon {
  transform: scale(0.8);
}

.section-header {
  display: flex !important;
  align-items: center !important;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
  user-select: none;
  flex-direction: row !important;
}

.section-header span {
  font-size: 17px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-header .el-icon {
  font-size: 20px;
  color: #4a6cf7;
  filter: drop-shadow(0 0 8px rgba(74, 108, 247, 0.5));
}

.result-tag {
  font-size: 15px;
  padding: 8px 14px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  position: relative;
  overflow: hidden;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: white; /* 纯白色文本 */
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4); /* 增强阴影效果 */
  letter-spacing: 0.5px;
  opacity: 0.95; /* 轻微调整不透明度 */
  cursor: pointer; /* 添加指针样式表示可点击 */
  user-select: none; /* 添加禁止选择 */
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); /* 添加默认背景渐变色 */
}

/* 固定元素样式 */
.fixed-element {
  box-shadow: 
    0 0 0 2px var(--primary-light), 
    0 4px 15px var(--shadow-color);
  opacity: 1;
  transform: translateY(-2px);
  position: relative;
  z-index: 1;
  filter: brightness(1.1);
}

.fixed-element::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.15);
  border-radius: inherit;
  pointer-events: none;
}

.fixed-icon {
  margin-right: 6px;
  font-size: 14px;
  animation: lockPulse 2s ease-in-out infinite;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
}

@keyframes lockPulse {
  0% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
  100% { opacity: 0.7; transform: scale(1); }
}

/* 针对不同类型的标签应用自定义样式 */
.result-tag.el-tag--primary {
  background: linear-gradient(135deg, #409eff, #2b85e4) !important;
}

.result-tag.el-tag--success {
  background: linear-gradient(135deg, #67c23a, #4caf50) !important;
}

.result-tag.el-tag--warning {
  background: linear-gradient(135deg, #e6a23c, #f57c00) !important;
}

.result-tag.el-tag--danger {
  background: linear-gradient(135deg, #f56c6c, #e53935) !important;
}

.result-tag.el-tag--info {
  background: linear-gradient(135deg, #909399, #607d8b) !important;
}

.result-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  filter: brightness(1.1);
  opacity: 1;
}

.element-count {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 13px;
  color: rgba(20, 20, 35, 0.9);
  background: rgba(42, 76, 199, 0.1);
  padding: 4px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(40, 40, 60, 0.7);
  text-align: center;
  padding: 20px;
  user-select: none; /* 添加禁止选择 */
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: rgba(42, 76, 199, 0.3);
  filter: drop-shadow(0 0 10px rgba(74, 108, 247, 0.3));
}

.empty-result p {
  font-size: 16px;
  margin: 0;
}

.fixed-elements-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  color: rgba(20, 20, 35, 0.9);
  background: rgba(42, 76, 199, 0.1);
  border-radius: 10px;
  padding: 10px 15px;
  position: relative;
  border-left: 3px solid #2a4cc7;
  user-select: none; /* 添加禁止选择 */
}

.fixed-count {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.fixed-count::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #4a6cf7;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulseBullet 2s ease-in-out infinite;
}

.info-icon {
  font-size: 18px;
  color: #4a6cf7;
  cursor: pointer;
  filter: drop-shadow(0 0 5px rgba(74, 108, 247, 0.5));
}

/* 添加新样式 */
.data-loading-overlay, .data-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--bg-card);
  z-index: 10;
  user-select: none; /* 添加禁止选择 */
  color: var(--text-primary);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.loading-icon {
  animation: rotate 1.5s linear infinite;
  color: var(--primary-color);
  filter: drop-shadow(0 0 10px var(--primary-lighter));
}

.error-icon {
  color: #f56c6c;
  filter: drop-shadow(0 0 10px rgba(245, 108, 108, 0.5));
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 添加科技感光效 */
.story-inspiration::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(74, 108, 247, 0.03) 0%, transparent 80%);
  animation: rotate 30s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
}
}

/* 允许在输入框和文本区域中选择文本 */
.el-input, .el-textarea, .el-select, .import-input, .format-hint-content pre {
  user-select: text;
}

/* 允许在可编辑区域选择文本 */
.editor-container .el-table {
  user-select: text;
}

.editor-container .el-input__wrapper,
.editor-container .el-textarea__wrapper {
  user-select: text;
}

/* 表格内容可选择 */
.el-table__body {
  user-select: text;
}

/* 详情描述可选择 */
.detail-description {
  user-select: text;
}

/* 示例内容可选择 */
.detail-examples-list li {
  user-select: text;
}



/* 组合结果区域，可能需要复制内容 */
.tag-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  padding: 15px 10px;
  align-items: center;
}

.tag-group .result-tag {
  user-select: all; /* 点击时全选 */
  margin-bottom: 5px;
  margin-right: 5px;
}

/* 允许复制示例JSON */
.format-hint-content {
  user-select: all;
}

/* 使用深度选择器确保样式能穿透到子组件 */
.story-inspiration :deep(.el-button),
.story-inspiration :deep(.el-tabs__item),
.story-inspiration :deep(.el-tag),
.story-inspiration :deep(.el-select-dropdown__item),
.story-inspiration :deep(.el-checkbox__label),
.story-inspiration :deep(.el-dialog__title),
.story-inspiration :deep(.el-dialog__headerbtn),
.story-inspiration :deep(.el-popover__title) {
  user-select: none;
}

/* 确保输入区域可选择文本 */
.story-inspiration :deep(.el-input__inner),
.story-inspiration :deep(.el-textarea__inner),
.story-inspiration :deep(.el-select-dropdown__item),
.story-inspiration :deep(.el-table .cell) {
  user-select: text;
}

/* 应用到弹窗 */
:deep(.el-dialog__header),
:deep(.el-dialog__footer),
:deep(.el-message-box__header),
:deep(.el-message-box__btns) {
  user-select: none;
}

/* 元素详情卡片对话框 - 科技风格 */
.element-detail-dialog {
  max-width: 90vw;
}

.tech-card :deep(.el-dialog) {
  border-radius: 16px;
  background: rgba(30, 32, 50, 0.95);
  overflow: hidden;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 10px 30px rgba(74, 108, 247, 0.2),
    0 0 0 1px rgba(74, 108, 247, 0.15);
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, opacity;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.05);
  height: auto;
  max-height: 600px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
}

.result-grid {
  display: grid !important;
  grid-template-columns: repeat(1, 1fr) !important;
  gap: 18px;
  margin-top: 15px;
}

@media (min-width: 768px) {
  .result-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (min-width: 1200px) {
  .result-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

.detail-content-wrapper {
  position: relative;
  background: rgba(30, 32, 50, 0.9);
  overflow: hidden;
  height: auto;
  min-height: 100px;
  max-height: 450px;
  color: rgba(255, 255, 255, 0.9);
}

.detail-scrollbar {
  height: auto;
  max-height: 450px;
}

.detail-scrollbar :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

.detail-scrollbar :deep(.el-scrollbar__bar.is-horizontal) {
  display: none;
}

.detail-scrollbar :deep(.el-scrollbar__bar.is-vertical) {
  width: 6px;
}

.detail-scrollbar :deep(.el-scrollbar__thumb) {
  background-color: rgba(74, 108, 247, 0.3);
  border-radius: 3px;
}

.detail-scrollbar :deep(.el-scrollbar__thumb:hover) {
  background-color: rgba(74, 108, 247, 0.5);
}

.detail-content {
  padding: 20px;
  background: rgba(30, 32, 50, 0.6);
  position: relative;
  overflow: visible;
  color: rgba(255, 255, 255, 0.9);
}

.detail-section {
  margin-bottom: 30px;
  animation: fadeInUp 0.5s ease-out;
  position: relative;
  z-index: 2;
}

.detail-section:last-child {
  margin-bottom: 10px;
}

.detail-section-title {
  margin: 0 0 16px;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  user-select: none; /* 添加禁止选择 */
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--primary-lighter);
  border: 1px solid var(--primary-light);
  position: relative;
  overflow: hidden;
}

.tech-icon {
  width: 18px;
  height: 18px;
  fill: var(--primary-color);
}

.detail-description {
  margin: 0;
  line-height: 1.7;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.9);
  padding: 16px 20px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(74, 108, 247, 0.1);
  position: relative;
  overflow: hidden;
}

.detail-examples-list {
  padding-left: 0;
  margin: 10px 0 0;
  list-style: none;
}

.detail-examples-list li {
  margin-bottom: 10px;
  padding: 12px 16px 12px 14px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  border-left: 2px solid rgba(74, 108, 247, 0.5);
  position: relative;
  overflow: hidden;
  color: rgba(255, 255, 255, 0.9);
}

.example-bullet {
  width: 8px;
  height: 8px;
  background: rgba(74, 108, 247, 0.7);
  border-radius: 50%;
  margin-top: 6px;
  position: relative;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 22px 28px;
  background: linear-gradient(135deg, 
    var(--primary-lighter) 0%, 
    var(--primary-lighter) 100%);
  border-bottom: 1px solid var(--primary-lighter);
  position: relative;
  overflow: hidden;
  user-select: none; /* 添加禁止选择 */
  color: var(--text-primary);
}

.tech-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(90deg, rgba(74, 108, 247, 0.07) 1px, transparent 1px),
    linear-gradient(0deg, rgba(74, 108, 247, 0.07) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  opacity: 0.6;
  z-index: 0;
}

.detail-title {
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
  z-index: 1;
}

.detail-title h3 {
  margin: 5px 0 0;
  font-size: 26px;
  font-weight: 600;
  color: var(--text-primary);
  text-shadow: 0 1px 3px var(--shadow-color);
  position: relative;
}

.detail-category-badge {
  display: inline-block;
  padding: 4px 14px 4px 10px;
  border-radius: 4px 12px 12px 4px;
  background: var(--primary-lighter);
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 5px var(--shadow-color);
  border-left: 3px solid var(--primary-color);
  text-shadow: 0 1px 2px var(--shadow-color);
  align-self: flex-start;
  position: relative;
  overflow: hidden;
}

.emotion-tag {
  position: relative;
  z-index: 1;
  font-size: 14px;
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.detail-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20px 30px;
  background: rgba(74, 108, 247, 0.05);
  border-top: 1px solid rgba(74, 108, 247, 0.1);
  position: relative;
  user-select: none; /* 添加禁止选择 */
}

.tech-pulse {
  position: absolute;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: #4a6cf7;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.tech-button {
  position: relative;
  overflow: hidden;
  padding: 10px 24px;
  border-radius: 22px;
  font-weight: 500;
  transition: all 0.3s;
  background: linear-gradient(135deg, #4a6cf7, #6e8fff);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(74, 108, 247, 0.3);
}

.tech-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(74, 108, 247, 0.4);
  background: linear-gradient(135deg, #6e8fff, #4a6cf7);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.tech-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.corner-decoration {
  position: absolute;
  width: 20px;
  height: 20px;
  fill: var(--primary-color);
  opacity: 0.4;
}

.top-left {
  top: 0;
  left: 0;
}

.top-right {
  top: 0;
  right: 0;
  transform: rotate(90deg);
}

.bottom-left {
  bottom: 0;
  left: 0;
  transform: rotate(270deg);
}

.bottom-right {
  bottom: 0;
  right: 0;
  transform: rotate(180deg);
}

:deep(.el-select--small:hover .el-select__caret) {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 美化下拉菜单内部组件 */
:deep(.el-select-dropdown.is-multiple .el-select-dropdown__item.selected) {
  background: rgba(74, 108, 247, 0.2) !important;
  color: #fff !important;
}

:deep(.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover) {
  background: rgba(74, 108, 247, 0.3) !important;
}

:deep(.el-select-dropdown__empty) {
  color: rgba(255, 255, 255, 0.6) !important;
  padding: 10px;
  text-align: center;
}

:deep(.el-scrollbar__bar) {
  background-color: rgba(255, 255, 255, 0.05);
}

:deep(.el-select-dropdown .el-scrollbar__thumb) {
  background-color: rgba(74, 108, 247, 0.5);
  border-radius: 10px;
}

:deep(.el-select-dropdown .el-scrollbar__thumb:hover) {
  background-color: rgba(74, 108, 247, 0.7);
}

:deep(.el-select-dropdown__list) {
  padding: 6px;
}

:deep(.el-select-dropdown__item) {
  border-radius: 4px;
  margin: 2px 0;
  padding: 0 12px;
  height: 32px;
  line-height: 32px;
  transition: all 0.2s ease;
}

:deep(.el-popper__arrow) {
  display: none !important;
}

:deep(.el-popper) {
  border-radius: 8px !important;
}

/* 修复下拉框在不同状态下的样式 */
:deep(.el-select .el-input.is-disabled .el-input__wrapper) {
  background-color: rgba(30, 32, 50, 0.5) !important;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.5) !important;
}

:deep(.el-select .el-input.is-disabled .el-input__inner) {
  color: rgba(255, 255, 255, 0.5) !important;
}

:deep(.el-select-dropdown__item.is-disabled) {
  color: rgba(255, 255, 255, 0.4) !important;
  cursor: not-allowed;
}

:deep(.el-select-dropdown__wrap) {
  max-height: 274px;
}

:deep(.el-select-dropdown .el-scrollbar .el-select-dropdown__list) {
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
  background: rgba(25, 27, 42, 0.7);
  border-radius: 6px;
  margin: 4px;
  padding: 4px;
}

/* 添加下拉框激活和悬停状态的动画效果 */
:deep(.el-select--small .el-select__wrapper) {
  position: relative;
  overflow: hidden;
}

:deep(.el-select--small .el-select__wrapper::after) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(74, 108, 247, 0.5), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

:deep(.el-select--small .el-select__wrapper:hover::after),
:deep(.el-select--small .el-select__wrapper.is-focus::after) {
  transform: translateX(100%);
}

:deep(.el-select--small .el-select__wrapper.is-focus) {
  animation: subtle-glow 2s infinite alternate;
}

@keyframes subtle-glow {
  0% {
    box-shadow: 0 0 0 1px rgba(74, 108, 247, 0.5), 0 4px 16px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.7), 0 4px 18px rgba(0, 0, 0, 0.4);
  }
}

/* 美化下拉菜单弹出动画 */
:deep(.el-popper) {
  transform-origin: center top;
  animation: dropdown-in 0.15s ease-out forwards;
}

@keyframes dropdown-in {
  from {
    opacity: 0;
    transform: scaleY(0.8);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

/* 最终润色 - 改进选中项和整体视觉效果 */
:deep(.el-select-dropdown__item.selected::after) {
  content: '';
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(74, 108, 247, 0.8);
  box-shadow: 0 0 8px rgba(74, 108, 247, 0.5);
}

:deep(.el-select__tags) {
  background: transparent !important;
}

:deep(.el-select__tags-text) {
  color: rgba(255, 255, 255, 0.9) !important;
}

:deep(.el-tag) {
  background: rgba(74, 108, 247, 0.2) !important;
  border-color: rgba(74, 108, 247, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

:deep(.el-tag .el-tag__close) {
  color: rgba(255, 255, 255, 0.7) !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-tag .el-tag__close:hover) {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
}

:deep(.el-select-dropdown) {
  border: 1px solid rgba(74, 108, 247, 0.2) !important;
  box-shadow: 
    0 6px 16px rgba(0, 0, 0, 0.3),
    0 3px 6px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(74, 108, 247, 0.1) !important;
}

/* 添加星星旋转动画定义 */
@keyframes starRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.result-content {
  position: relative;
}

.result-content:hover .tech-floater {
  opacity: 1;
  transform: translateY(0);
}

.tech-floater {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 180px;
  height: 90px;
  background: linear-gradient(135deg, rgba(74, 108, 247, 0.3), rgba(74, 108, 247, 0.1));
  border: 1px solid rgba(74, 108, 247, 0.5);
  border-radius: 12px;
  box-shadow: 0 0 30px rgba(74, 108, 247, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 10;
  pointer-events: none;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  overflow: hidden;
}

.tech-floater-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.tech-circle {
  position: absolute;
  width: 40px;
  height: 40px;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  border: 2px solid rgba(74, 108, 247, 0.6);
  border-radius: 50%;
  animation: techPulse 3s infinite ease-in-out;
}

.tech-circle::before {
  content: '';
  position: absolute;
  width: 15px;
  height: 15px;
  background: rgba(74, 108, 247, 0.8);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px rgba(74, 108, 247, 0.8);
}

.tech-lines {
  position: absolute;
  left: 20px;
  top: 20px;
  width: 80px;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tech-lines::before, .tech-lines::after {
  content: '';
  height: 2px;
  background: linear-gradient(to right, transparent, rgba(74, 108, 247, 0.8) 50%, transparent);
  animation: lineScan 3s infinite ease-in-out alternate;
}

.tech-dots {
  position: absolute;
  bottom: 15px;
  left: 25px;
  right: 25px;
  height: 2px;
  display: flex;
  justify-content: space-between;
}

.tech-dots::before, .tech-dots::after, .tech-dots::before {
  content: '';
  width: 4px;
  height: 4px;
  background: rgba(74, 108, 247, 0.8);
  border-radius: 50%;
  animation: dotBlink 1.5s infinite alternate;
}

@keyframes techPulse {
  0% { transform: translateY(-50%) scale(0.95); opacity: 0.7; }
  50% { transform: translateY(-50%) scale(1.05); opacity: 1; }
  100% { transform: translateY(-50%) scale(0.95); opacity: 0.7; }
}

@keyframes lineScan {
  0% { width: 30%; opacity: 0.3; }
  100% { width: 100%; opacity: 0.8; }
}

@keyframes dotBlink {
  0% { opacity: 0.2; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1.2); }
}

/* 更新表格样式 */
.editor-container .el-table {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.editor-container :deep(.el-table__header) {
  background-color: var(--bg-card-hover) !important;
  color: var(--text-primary) !important;
}

.editor-container :deep(.el-table__row) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

.editor-container :deep(.el-table__row:hover) {
  background-color: var(--bg-card-hover) !important;
}

.editor-container :deep(.el-table th.el-table__cell) {
  background-color: var(--bg-card-hover) !important;
  color: var(--text-primary) !important;
  border-bottom-color: var(--border-color) !important;
}

.editor-container :deep(.el-table td.el-table__cell) {
  border-bottom-color: var(--border-color) !important;
}

.editor-container :deep(.el-input__wrapper),
.editor-container :deep(.el-textarea__wrapper) {
  background-color: var(--bg-card) !important;
  box-shadow: 0 0 0 1px var(--border-color) !important;
}

.editor-container :deep(.el-input__inner),
.editor-container :deep(.el-textarea__inner) {
  color: var(--text-primary) !important;
}

/* 更新弹窗样式 */
:deep(.el-dialog) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 20px 60px var(--shadow-color);
}

:deep(.el-dialog__header) {
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-color);
}

:deep(.el-dialog__title) {
  color: var(--text-primary) !important;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: var(--text-secondary) !important;
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: var(--primary-color) !important;
}

:deep(.el-dialog__body) {
  color: var(--text-primary) !important;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid var(--border-color);
}

/* 更新按钮样式 */
:deep(.el-button) {
  border-color: var(--border-color);
}

:deep(.el-button--default) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

:deep(.el-button--default:hover) {
  background-color: var(--bg-card-hover) !important;
  border-color: var(--primary-lighter) !important;
}

:deep(.el-button--primary) {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

:deep(.el-button--primary:hover) {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
}

/* 更新消息提示框样式 */
:deep(.el-message-box) {
  background-color: var(--bg-card) !important;
  border-color: var(--border-color) !important;
}

:deep(.el-message-box__title) {
  color: var(--text-primary) !important;
}

:deep(.el-message-box__content) {
  color: var(--text-primary) !important;
}

:deep(.el-message-box__container) {
  color: var(--text-primary) !important;
}

/* 更新折叠面板样式 */
:deep(.el-collapse) {
  border-color: var(--border-color) !important;
  background-color: var(--bg-card) !important;
}

:deep(.el-collapse-item__header) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-bottom-color: var(--border-color) !important;
}

:deep(.el-collapse-item__content) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-bottom-color: var(--border-color) !important;
}

/* 更新卡片样式 */
:deep(.el-card) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

:deep(.el-card__header) {
  border-bottom-color: var(--border-color) !important;
}

/* 更新Tooltip样式 */
:deep(.el-tooltip__popper) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

:deep(.el-tooltip__popper[x-placement^=top] .el-popper__arrow) {
  border-top-color: var(--border-color) !important;
}

:deep(.el-tooltip__popper[x-placement^=bottom] .el-popper__arrow) {
  border-bottom-color: var(--border-color) !important;
}

/* 更新Popover样式 */
:deep(.el-popover) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.examples-editor {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

.example-item {
  border-bottom: 1px solid var(--border-color);
}

/* 更新JSON格式示例样式 */
.format-hint-content pre {
  background-color: var(--bg-card-hover) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color);
}

/* 更新导入文本框样式 */
.import-input {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.import-input :deep(.el-textarea__inner) {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

/* 更新滚动条样式 */
:deep(.el-scrollbar__bar) {
  background-color: var(--highlight-color);
}

:deep(.el-scrollbar__thumb) {
  background-color: var(--primary-lighter);
  border-radius: 10px;
}

:deep(.el-scrollbar__thumb:hover) {
  background-color: var(--primary-light);
}

/* 更新加载动画样式 */
@keyframes pulseBullet {
  0% { background-color: var(--primary-color); opacity: 0.5; }
  50% { background-color: var(--primary-dark); opacity: 1; }
  100% { background-color: var(--primary-color); opacity: 0.5; }
}

/* 主题切换按钮样式 */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 100;
}

.theme-toggle-button {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  box-shadow: 0 4px 12px var(--shadow-color);
  transition: all 0.3s ease;
}

.theme-toggle-button:hover {
  transform: rotate(30deg);
  background: var(--primary-lighter);
  color: var(--primary-color);
}

/* 明亮主题下的标题栏样式 */
html:not(.dark) .column-header {
  background: linear-gradient(to right, 
    rgba(42, 76, 199, 0.2) 0%, 
    rgba(42, 76, 199, 0.1) 100%);
  color: rgba(20, 20, 35, 1);
  border-bottom: 1px solid rgba(42, 76, 199, 0.2);
}

/* 明亮主题下的结果头部样式 */
html:not(.dark) .result-header {
  background: linear-gradient(to right, 
    rgba(235, 240, 255, 0.95), 
    rgba(225, 232, 255, 0.95));
  border-bottom: 2px solid rgba(42, 76, 199, 0.3);
}

/* 明亮主题下的结果头部边框流动效果 */
html:not(.dark) .result-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right,
    rgba(42, 76, 199, 0),
    rgba(42, 76, 199, 1),
    rgba(65, 105, 225, 1),
    rgba(42, 76, 199, 1),
    rgba(42, 76, 199, 0));
  background-size: 200% 100%;
  animation: flowLight 3s ease-in-out infinite alternate;
  box-shadow: 0 0 15px rgba(42, 76, 199, 0.5);
  opacity: 0.8;
}

/* 明亮主题下的结果卡片样式 */
html:not(.dark) .result-section-card {
  background: rgba(250, 252, 255, 0.95);
  border: 1px solid rgba(42, 76, 199, 0.2);
}

/* 明亮主题下的元素计数样式 */
html:not(.dark) .element-count {
  color: rgba(20, 20, 35, 0.9);
  background: rgba(42, 76, 199, 0.1);
}

/* 明亮主题下的标签样式 */
html:not(.dark) .result-tag {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 明亮主题下的空结果状态 */
html:not(.dark) .empty-result {
  color: rgba(40, 40, 60, 0.7);
}

html:not(.dark) .empty-icon {
  color: rgba(42, 76, 199, 0.3);
}

/* 明亮主题下的固定元素指示器 */
html:not(.dark) .fixed-elements-indicator {
  color: rgba(20, 20, 35, 0.9);
  background: rgba(42, 76, 199, 0.1);
  border-left: 3px solid #2a4cc7;
}

/* 明亮主题下的复选框样式 */
html:not(.dark) .checkbox-list :deep(.el-checkbox__label) {
  color: rgba(20, 20, 35, 0.9);
}

html:not(.dark) .checkbox-list :deep(.el-checkbox) {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(42, 76, 199, 0.15);
}

html:not(.dark) .checkbox-list :deep(.el-checkbox:hover) {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(42, 76, 199, 0.3);
}

html:not(.dark) .checkbox-list :deep(.el-checkbox.is-checked) {
  background: rgba(42, 76, 199, 0.1);
  border-color: rgba(42, 76, 199, 0.4);
}

/* 明亮主题下的选择区域 */
html:not(.dark) .selection-column {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(42, 76, 199, 0.15);
}

/* 明亮主题下的结果区域 */
html:not(.dark) .result-section {
  background: rgba(245, 247, 250, 0.8);
  border: 1px solid rgba(42, 76, 199, 0.15);
}

/* 明亮主题下的分隔线 */
html:not(.dark) .selection-section {
  border-bottom: 2px dashed rgba(42, 76, 199, 0.15);
}

/* 明亮主题下的按钮样式 */
html:not(.dark) .action-buttons {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(42, 76, 199, 0.15);
}

/* 明亮主题下的标题文本 */
html:not(.dark) .result-header h2 {
  background: linear-gradient(120deg, #2a4cc7, #3a5cd7, #2a4cc7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: none;
}

/* 明亮主题下的结果图标 */
html:not(.dark) .result-icon {
  color: #f59e0b;
}

/* 明亮主题下的部分标题 */
html:not(.dark) .section-header span {
  color: rgba(20, 20, 35, 1);
  text-shadow: none;
  font-weight: 600;
}

html:not(.dark) .section-header .el-icon {
  color: #2a4cc7;
}

/* 明亮主题下的表格样式 */
html:not(.dark) .editor-container :deep(.el-table th.el-table__cell) {
  background-color: rgba(235, 240, 255, 0.95) !important;
  color: rgba(20, 20, 35, 1) !important;
}

html:not(.dark) .editor-container :deep(.el-table__row) {
  background-color: rgba(255, 255, 255, 0.95) !important;
}

/* 明亮主题下的下拉框样式 */
html:not(.dark) :deep(.el-select--small .el-select__wrapper) {
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 0 0 1px rgba(42, 76, 199, 0.2), 0 4px 12px rgba(0, 0, 0, 0.05) !important;
}

html:not(.dark) :deep(.el-select--small .el-input__inner) {
  color: rgba(20, 20, 35, 1) !important;
}

/* 明亮主题下的弹窗样式 */
html:not(.dark) .tech-card :deep(.el-dialog) {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 10px 30px rgba(42, 76, 199, 0.1),
    0 0 0 1px rgba(42, 76, 199, 0.1);
}

html:not(.dark) .detail-header {
  background: linear-gradient(135deg, 
    rgba(235, 240, 255, 0.95), 
    rgba(225, 232, 255, 0.95));
  border-bottom: 1px solid rgba(42, 76, 199, 0.2);
}

html:not(.dark) .detail-content {
  background: rgba(255, 255, 255, 0.95);
}

html:not(.dark) .detail-section-title {
  color: #2a4cc7;
}

html:not(.dark) .detail-footer {
  background: rgba(245, 247, 250, 0.8);
  border-top: 1px solid rgba(42, 76, 199, 0.1);
}

html:not(.dark) .tech-button {
  background: linear-gradient(135deg, #2a4cc7, #3a5cd7);
}

html:not(.dark) .tech-button:hover {
  background: linear-gradient(135deg, #3a5cd7, #2a4cc7);
}

/* 明亮主题下的标签页样式 */
html:not(.dark) .inspiration-tabs :deep(.el-tabs__nav) {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(42, 76, 199, 0.15);
}

html:not(.dark) .inspiration-tabs :deep(.el-tabs__item) {
  color: rgba(40, 40, 60, 0.7);
}

html:not(.dark) .inspiration-tabs :deep(.el-tabs__item.is-active) {
  color: #2a4cc7;
  background: rgba(235, 240, 255, 0.95);
  box-shadow: 0 0 15px rgba(42, 76, 199, 0.2);
  text-shadow: none;
}

/* 明亮主题下的详情描述样式 */
html:not(.dark) .detail-description {
  color: rgba(20, 20, 35, 0.9);
  background: rgba(245, 247, 250, 0.8);
  border: 1px solid rgba(42, 76, 199, 0.1);
}

html:not(.dark) .detail-examples-list li {
  background: rgba(245, 247, 250, 0.8);
  border-left: 2px solid rgba(42, 76, 199, 0.3);
  color: rgba(20, 20, 35, 0.9);
}

/* 明亮主题下的下拉菜单样式 */
html:not(.dark) :deep(.el-select__popper) {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid rgba(42, 76, 199, 0.15) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
}

html:not(.dark) :deep(.el-select-dropdown__item) {
  color: rgba(20, 20, 35, 0.9) !important;
}

html:not(.dark) :deep(.el-select-dropdown__item.hover),
html:not(.dark) :deep(.el-select-dropdown__item:hover) {
  background: rgba(235, 240, 255, 0.95) !important;
}

html:not(.dark) :deep(.el-select-dropdown__item.selected) {
  background: rgba(42, 76, 199, 0.1) !important;
  color: #2a4cc7 !important;
}

/* 明亮主题下的装饰元素 */
html:not(.dark) .corner-decoration {
  fill: #2a4cc7;
}

html:not(.dark) .tech-icon {
  fill: #2a4cc7;
}

/* 明亮主题下的滚动条 */
html:not(.dark) :deep(.el-scrollbar__thumb) {
  background-color: rgba(42, 76, 199, 0.3);
}

html:not(.dark) :deep(.el-scrollbar__thumb:hover) {
  background-color: rgba(42, 76, 199, 0.5);
}

/* 明亮主题下的边框流动动画 */
@keyframes flowLight {
  0% {
    background-position: 0% 50%;
    opacity: 0.7;
  }
  50% {
    background-position: 100% 50%;
    opacity: 1;
  }
  100% {
    background-position: 0% 50%;
    opacity: 0.7;
  }
}

/* 为黑色主题明确添加样式，避免受到明亮主题的影响 */
html.dark .column-header {
  background: linear-gradient(to right, 
    rgba(74, 108, 247, 0.2) 0%, 
    rgba(74, 108, 247, 0.1) 100%);
  color: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(74, 108, 247, 0.2);
}

html.dark .count-select {
  background: rgba(30, 32, 50, 0.7) !important;
}

html.dark .count-select :deep(.el-input__wrapper) {
  background: rgba(30, 32, 50, 0.7) !important;
  box-shadow: 0 0 0 1px rgba(74, 108, 247, 0.3) !important;
}

html.dark .count-select :deep(.el-input__inner) {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .count-select :deep(.el-select__caret) {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 黑色主题下拉菜单样式 */
html.dark :deep(.el-select__popper) {
  background: rgba(30, 32, 50, 0.95) !important;
  border: 1px solid rgba(74, 108, 247, 0.3) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

html.dark :deep(.el-select-dropdown__item) {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark :deep(.el-select-dropdown__item.hover),
html.dark :deep(.el-select-dropdown__item:hover) {
  background: rgba(74, 108, 247, 0.2) !important;
}

html.dark :deep(.el-select-dropdown__item.selected) {
  background: rgba(74, 108, 247, 0.3) !important;
  color: #fff !important;
  font-weight: bold;
}

/* 黑色主题小型下拉框样式 */
html.dark :deep(.el-select--small .el-select__wrapper) {
  background: rgba(30, 32, 50, 0.8) !important;
  box-shadow: 0 0 0 1px rgba(74, 108, 247, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

html.dark :deep(.el-select--small .el-select__wrapper:hover) {
  box-shadow: 0 0 0 1px rgba(74, 108, 247, 0.5), 0 4px 16px rgba(0, 0, 0, 0.3) !important;
  background: rgba(35, 38, 60, 0.85) !important;
}

html.dark :deep(.el-select--small .el-select__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.5), 0 4px 16px rgba(0, 0, 0, 0.3) !important;
  background: rgba(40, 44, 70, 0.9) !important;
}

html.dark :deep(.el-select--small .el-input__inner) {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark :deep(.el-select--small .el-select__caret) {
  color: rgba(255, 255, 255, 0.7) !important;
}

html.dark :deep(.el-select--small:hover .el-select__caret) {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 黑色主题编辑按钮样式 */
html.dark .edit-button {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

html.dark .edit-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

/* 黑色主题复选框样式 */
html.dark .checkbox-list :deep(.el-checkbox) {
  background: rgba(30, 32, 50, 0.5);
  border: 1px solid rgba(74, 108, 247, 0.2);
}

html.dark .checkbox-list :deep(.el-checkbox:hover) {
  background: rgba(35, 38, 60, 0.6);
  border-color: rgba(74, 108, 247, 0.3);
}

html.dark .checkbox-list :deep(.el-checkbox.is-checked) {
  background: rgba(74, 108, 247, 0.2);
  border-color: rgba(74, 108, 247, 0.4);
}

html.dark .checkbox-list :deep(.el-checkbox__label) {
  color: rgba(255, 255, 255, 0.9);
}

/* 黑色主题操作按钮区域 */
html.dark .action-buttons {
  background: rgba(30, 32, 50, 0.5);
  border: 1px solid rgba(74, 108, 247, 0.2);
}

/* 黑色主题结果区域 */
html.dark .result-section {
  background: rgba(30, 32, 50, 0.5);
  border: 1px solid rgba(74, 108, 247, 0.2);
}

/* 黑色主题结果头部 */
html.dark .result-header {
  background: linear-gradient(to right, rgba(30, 32, 50, 0.85), rgba(40, 44, 70, 0.85));
  border-bottom: 2px solid rgba(74, 108, 247, 0.2);
}

/* 黑色主题结果头部流动边框 */
html.dark .result-header::before {
  background: linear-gradient(to right,
    rgba(74, 108, 247, 0),
    rgba(74, 108, 247, 1),
    rgba(100, 130, 255, 1),
    rgba(74, 108, 247, 1),
    rgba(74, 108, 247, 0));
  box-shadow: 0 0 15px rgba(74, 108, 247, 0.5);
}

/* 黑色主题结果区域卡片 */
html.dark .result-section-card {
  background: rgba(35, 38, 60, 0.6);
  border: 1px solid rgba(74, 108, 247, 0.2);
}

/* 黑色主题元素计数样式 */
html.dark .element-count {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(74, 108, 247, 0.2);
}

/* 黑色主题标题文本 */
html.dark .result-header h2 {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 10px rgba(74, 108, 247, 0.5);
}

/* 黑色主题部分标题 */
html.dark .section-header span {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 10px rgba(74, 108, 247, 0.3);
}

html.dark .section-header .el-icon {
  color: rgba(74, 108, 247, 0.9);
}

/* 黑色主题分隔线 */
html.dark .selection-section {
  border-bottom: 2px dashed rgba(255, 255, 255, 0.1);
}

/* 黑色主题选择区域 */
html.dark .selection-column {
  background: rgba(30, 32, 50, 0.5);
  border: 1px solid rgba(74, 108, 247, 0.2);
}

/* 黑色主题标签页 */
html.dark .inspiration-tabs :deep(.el-tabs__nav) {
  background: rgba(30, 32, 50, 0.5);
  border: 1px solid rgba(74, 108, 247, 0.2);
}

html.dark .inspiration-tabs :deep(.el-tabs__item) {
  color: rgba(255, 255, 255, 0.7);
}

html.dark .inspiration-tabs :deep(.el-tabs__item.is-active) {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(74, 108, 247, 0.2);
  box-shadow: 0 0 15px rgba(74, 108, 247, 0.3);
  text-shadow: 0 0 10px rgba(74, 108, 247, 0.5);
}
</style>