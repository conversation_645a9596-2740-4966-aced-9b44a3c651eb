<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书籍设置弹窗演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            text-align: center;
        }

        .open-dialog-btn {
            background: linear-gradient(135deg, #4a90e2, #5dade2);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(74, 144, 226, 0.3);
        }

        .open-dialog-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.4);
        }

        /* 弹窗遮罩 */
        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .dialog-overlay.show {
            display: flex;
        }

        /* 弹窗主体 */
        .dialog {
            background: white;
            border-radius: 24px;
            width: 700px;
            max-width: 90vw;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .dialog-overlay.show .dialog {
            transform: scale(1);
        }

        .dialog-header {
            background: linear-gradient(135deg, #4a90e2, #5dade2);
            color: white;
            padding: 24px 32px;
            position: relative;
        }

        .dialog-title {
            font-size: 20px;
            font-weight: 700;
            margin: 0;
        }

        .dialog-close {
            position: absolute;
            top: 24px;
            right: 32px;
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.8);
            font-size: 20px;
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .dialog-close:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .dialog-body {
            padding: 32px;
        }

        .settings-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin: 0 0 20px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title::after {
            content: '';
            flex: 1;
            height: 2px;
            background: linear-gradient(to right, rgba(74, 144, 226, 0.3), transparent);
            margin-left: 16px;
        }

        .form-item {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-input,
        .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #4a90e2;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* 风格选择器 */
        .style-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .style-card {
            position: relative;
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 16px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            overflow: hidden;
        }

        .style-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            border-color: #4a90e2;
        }

        .style-card.selected {
            border-color: #4a90e2;
            background: rgba(74, 144, 226, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 144, 226, 0.2);
        }

        .style-preview .preview-header {
            height: 8px;
            border-radius: 4px;
            margin-bottom: 12px;
            opacity: 0.8;
        }

        .style-preview .preview-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .style-preview .preview-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .style-check {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background: #4a90e2;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            animation: checkBounce 0.3s ease;
        }

        @keyframes checkBounce {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* 密码保护样式 */
        .password-section {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 24px;
            border: 1px solid #e0e0e0;
        }

        .password-info .info-text {
            margin: 0 0 16px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .password-btn {
            background: linear-gradient(135deg, #4a90e2, #5dade2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .password-btn:hover {
            background: linear-gradient(135deg, #5dade2, #74c0fc);
        }

        .dialog-footer {
            padding: 24px 32px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-cancel {
            background: #f0f0f0;
            color: #666;
        }

        .btn-cancel:hover {
            background: #e0e0e0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4a90e2, #5dade2);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5dade2, #74c0fc);
        }

        /* 风格颜色定义 */
        .preview-classic-blue .preview-header { background-color: #4a90e2; }
        .preview-classic-blue .preview-title { color: #4a90e2; }

        .preview-warm-orange .preview-header { background-color: #ff8c42; }
        .preview-warm-orange .preview-title { color: #ff8c42; }

        .preview-fresh-green .preview-header { background-color: #2ecc71; }
        .preview-fresh-green .preview-title { color: #2ecc71; }

        .preview-elegant-purple .preview-header { background-color: #9b59b6; }
        .preview-elegant-purple .preview-title { color: #9b59b6; }

        .preview-mysterious-dark .preview-header { background-color: #34495e; }
        .preview-mysterious-dark .preview-title { color: #34495e; }

        .preview-minimal-gray .preview-header { background-color: #95a5a6; }
        .preview-minimal-gray .preview-title { color: #95a5a6; }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="margin-bottom: 20px; color: #333;">📚 书籍设置弹窗演示</h1>
        <button class="open-dialog-btn" onclick="openDialog()">打开设置弹窗</button>
    </div>

    <!-- 设置弹窗 -->
    <div class="dialog-overlay" id="dialogOverlay" onclick="closeDialog(event)">
        <div class="dialog" onclick="event.stopPropagation()">
            <div class="dialog-header">
                <h2 class="dialog-title">📚 书籍设置</h2>
                <button class="dialog-close" onclick="closeDialog()">×</button>
            </div>
            
            <div class="dialog-body">
                <!-- 基本信息 -->
                <div class="settings-section">
                    <h3 class="section-title">📝 基本信息</h3>
                    <div class="form-item">
                        <label class="form-label">书名</label>
                        <input type="text" class="form-input" value="高武？我什么不知道" placeholder="请输入书名">
                    </div>
                    <div class="form-item">
                        <label class="form-label">描述</label>
                        <textarea class="form-textarea" placeholder="请输入书籍描述">这是一个关于高武世界的精彩故事，主人公在这个充满未知的世界中探索着各种奥秘...</textarea>
                    </div>
                </div>

                <!-- 书籍风格 -->
                <div class="settings-section">
                    <h3 class="section-title">🎨 书籍风格</h3>
                    <div class="style-selector">
                        <div class="style-card preview-classic-blue selected" onclick="selectStyle(this, 'classic-blue')">
                            <div class="style-preview">
                                <div class="preview-header"></div>
                                <div class="preview-content">
                                    <div class="preview-title">经典蓝调</div>
                                    <div class="preview-desc">专业稳重，适合商务类作品</div>
                                </div>
                            </div>
                            <div class="style-check">✓</div>
                        </div>
                        
                        <div class="style-card preview-warm-orange" onclick="selectStyle(this, 'warm-orange')">
                            <div class="style-preview">
                                <div class="preview-header"></div>
                                <div class="preview-content">
                                    <div class="preview-title">温暖橙光</div>
                                    <div class="preview-desc">活力温馨，适合生活类作品</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="style-card preview-fresh-green" onclick="selectStyle(this, 'fresh-green')">
                            <div class="style-preview">
                                <div class="preview-header"></div>
                                <div class="preview-content">
                                    <div class="preview-title">清新绿意</div>
                                    <div class="preview-desc">自然清新，适合治愈类作品</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="style-card preview-elegant-purple" onclick="selectStyle(this, 'elegant-purple')">
                            <div class="style-preview">
                                <div class="preview-header"></div>
                                <div class="preview-content">
                                    <div class="preview-title">优雅紫韵</div>
                                    <div class="preview-desc">神秘优雅，适合奇幻类作品</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="style-card preview-mysterious-dark" onclick="selectStyle(this, 'mysterious-dark')">
                            <div class="style-preview">
                                <div class="preview-header"></div>
                                <div class="preview-content">
                                    <div class="preview-title">神秘深邃</div>
                                    <div class="preview-desc">沉稳内敛，适合悬疑类作品</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="style-card preview-minimal-gray" onclick="selectStyle(this, 'minimal-gray')">
                            <div class="style-preview">
                                <div class="preview-header"></div>
                                <div class="preview-content">
                                    <div class="preview-title">简约灰调</div>
                                    <div class="preview-desc">简洁现代，适合科技类作品</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 密码保护 -->
                <div class="settings-section">
                    <h3 class="section-title">🔒 密码保护</h3>
                    <div class="password-section">
                        <div class="password-info">
                            <p class="info-text">设置密码后，所有章节内容将被加密保存，访问时需要输入密码。</p>
                        </div>
                        <button class="password-btn">
                            🔒 设置密码保护
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="dialog-footer">
                <button class="btn btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn btn-primary" onclick="saveSettings()">保存</button>
            </div>
        </div>
    </div>

    <script>
        function openDialog() {
            document.getElementById('dialogOverlay').classList.add('show');
        }

        function closeDialog(event) {
            if (event && event.target !== event.currentTarget) return;
            document.getElementById('dialogOverlay').classList.remove('show');
        }

        function selectStyle(element, style) {
            // 移除所有选中状态
            document.querySelectorAll('.style-card').forEach(card => {
                card.classList.remove('selected');
                const check = card.querySelector('.style-check');
                if (check) check.remove();
            });
            
            // 添加选中状态
            element.classList.add('selected');
            const check = document.createElement('div');
            check.className = 'style-check';
            check.textContent = '✓';
            element.appendChild(check);
        }

        function saveSettings() {
            alert('设置已保存！');
            closeDialog();
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeDialog();
            }
        });
    </script>
</body>
</html>
