(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a75fc256"],{"01b4":function(t,e){var r=function(){this.head=null,this.tail=null};r.prototype={add:function(t){var e={item:t,next:null};this.head?this.tail.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return this.head=t.next,this.tail===t&&(this.tail=null),t.item}},t.exports=r},"0d73":function(t,e,r){"use strict";r.r(e),function(t){r.d(e,"AElement",(function(){return Wt})),r.d(e,"AnimateColorElement",(function(){return Ft})),r.d(e,"AnimateElement",(function(){return zt})),r.d(e,"AnimateTransformElement",(function(){return Ht})),r.d(e,"BoundingBox",(function(){return yt})),r.d(e,"CB1",(function(){return D})),r.d(e,"CB2",(function(){return B})),r.d(e,"CB3",(function(){return U})),r.d(e,"CB4",(function(){return z})),r.d(e,"Canvg",(function(){return Ee})),r.d(e,"CircleElement",(function(){return Et})),r.d(e,"ClipPathElement",(function(){return ce})),r.d(e,"DefsElement",(function(){return kt})),r.d(e,"DescElement",(function(){return ye})),r.d(e,"Document",(function(){return Ae})),r.d(e,"Element",(function(){return ct})),r.d(e,"EllipseElement",(function(){return Pt})),r.d(e,"FeColorMatrixElement",(function(){return oe})),r.d(e,"FeCompositeElement",(function(){return ge})),r.d(e,"FeDropShadowElement",(function(){return fe})),r.d(e,"FeGaussianBlurElement",(function(){return pe})),r.d(e,"FeMorphologyElement",(function(){return de})),r.d(e,"FilterElement",(function(){return le})),r.d(e,"Font",(function(){return vt})),r.d(e,"FontElement",(function(){return Xt})),r.d(e,"FontFaceElement",(function(){return jt})),r.d(e,"GElement",(function(){return It})),r.d(e,"GlyphElement",(function(){return wt})),r.d(e,"GradientElement",(function(){return Lt})),r.d(e,"ImageElement",(function(){return Kt})),r.d(e,"LineElement",(function(){return Mt})),r.d(e,"LinearGradientElement",(function(){return Dt})),r.d(e,"MarkerElement",(function(){return _t})),r.d(e,"MaskElement",(function(){return ue})),r.d(e,"Matrix",(function(){return it})),r.d(e,"MissingGlyphElement",(function(){return Yt})),r.d(e,"Mouse",(function(){return W})),r.d(e,"PSEUDO_ZERO",(function(){return _})),r.d(e,"Parser",(function(){return J})),r.d(e,"PathElement",(function(){return bt})),r.d(e,"PathParser",(function(){return mt})),r.d(e,"PatternElement",(function(){return Vt})),r.d(e,"Point",(function(){return q})),r.d(e,"PolygonElement",(function(){return Rt})),r.d(e,"PolylineElement",(function(){return Nt})),r.d(e,"Property",(function(){return j})),r.d(e,"QB1",(function(){return F})),r.d(e,"QB2",(function(){return H})),r.d(e,"QB3",(function(){return X})),r.d(e,"RadialGradientElement",(function(){return Bt})),r.d(e,"RectElement",(function(){return Ot})),r.d(e,"RenderedElement",(function(){return xt})),r.d(e,"Rotate",(function(){return et})),r.d(e,"SVGElement",(function(){return Ct})),r.d(e,"SVGFontLoader",(function(){return te})),r.d(e,"Scale",(function(){return rt})),r.d(e,"Screen",(function(){return $})),r.d(e,"Skew",(function(){return nt})),r.d(e,"SkewX",(function(){return at})),r.d(e,"SkewY",(function(){return st})),r.d(e,"StopElement",(function(){return Ut})),r.d(e,"StyleElement",(function(){return ee})),r.d(e,"SymbolElement",(function(){return Jt})),r.d(e,"TRefElement",(function(){return qt})),r.d(e,"TSpanElement",(function(){return Tt})),r.d(e,"TextElement",(function(){return St})),r.d(e,"TextPathElement",(function(){return $t})),r.d(e,"TitleElement",(function(){return ve})),r.d(e,"Transform",(function(){return ht})),r.d(e,"Translate",(function(){return tt})),r.d(e,"UnknownElement",(function(){return lt})),r.d(e,"UseElement",(function(){return re})),r.d(e,"ViewPort",(function(){return Y})),r.d(e,"compressSpaces",(function(){return v})),r.d(e,"default",(function(){return Ee})),r.d(e,"getSelectorSpecificity",(function(){return V})),r.d(e,"normalizeAttributeName",(function(){return w})),r.d(e,"normalizeColor",(function(){return T})),r.d(e,"parseExternalUrl",(function(){return S})),r.d(e,"presets",(function(){return p})),r.d(e,"toNumbers",(function(){return x})),r.d(e,"trimLeft",(function(){return y})),r.d(e,"trimRight",(function(){return m})),r.d(e,"vectorMagnitude",(function(){return k})),r.d(e,"vectorsAngle",(function(){return L})),r.d(e,"vectorsRatio",(function(){return I}));r("e6cf");var i=r("c973"),n=r.n(i),a=(r("466d"),r("5319"),r("2ca0"),r("e260"),r("ddb0"),r("9523")),s=r.n(a),o=(r("13d5"),r("8a79"),r("1276"),r("c449")),u=r.n(o),h=(r("498a"),r("58e1")),c=r.n(h),l=(r("c975"),r("2532"),r("26e9"),r("d01f")),f=(r("25f0"),r("5e9e"));function d(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas(t,e){return new OffscreenCanvas(t,e)},createImage(t){return n()((function*(){var e=yield fetch(t),r=yield e.blob(),i=yield createImageBitmap(r);return i}))()}};return"undefined"===typeof DOMParser&&"undefined"!==typeof t||Reflect.deleteProperty(e,"DOMParser"),e}function g(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}var p=Object.freeze({__proto__:null,offscreen:d,node:g});function v(t){return t.replace(/(?!\u3000)\s+/gm," ")}function y(t){return t.replace(/^[\n \t]+/,"")}function m(t){return t.replace(/[\n \t]+$/,"")}function x(t){var e=(t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[];return e.map(parseFloat)}var b=/^[A-Z-]+$/;function w(t){return b.test(t)?t.toLowerCase():t}function S(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function T(t){if(!t.startsWith("rgb"))return t;var e=3,r=t.replace(/\d+(\.\d+)?/g,(t,r)=>e--&&r?String(Math.round(parseFloat(t))):t);return r}var A=/(\[[^\]]+\])/g,C=/(#[^\s+>~.[:]+)/g,O=/(\.[^\s+>~.[:]+)/g,E=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,P=/(:[\w-]+\([^)]*\))/gi,M=/(:[^\s+>~.[:]+)/g,N=/([^\s+>~.[:]+)/g;function R(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function V(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=R(r,A),e[1]+=i,[r,i]=R(r,C),e[0]+=i,[r,i]=R(r,O),e[1]+=i,[r,i]=R(r,E),e[2]+=i,[r,i]=R(r,P),e[1]+=i,[r,i]=R(r,M),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=R(r,N),e[2]+=i,e.join("")}var _=1e-8;function k(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function I(t,e){return(t[0]*e[0]+t[1]*e[1])/(k(t)*k(e))}function L(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(I(t,e))}function D(t){return t*t*t}function B(t){return 3*t*t*(1-t)}function U(t){return 3*t*(1-t)*(1-t)}function z(t){return(1-t)*(1-t)*(1-t)}function F(t){return t*t}function H(t){return 2*t*(1-t)}function X(t){return(1-t)*(1-t)}class j{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new j(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return v(this.getString()).trim().split(t).map(t=>new j(e,r,t))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&"undefined"!==typeof e}isString(t){var{value:e}=this,r="string"===typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return"undefined"===typeof t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return"undefined"===typeof t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return"undefined"===typeof t||this.hasValue()?"undefined"===typeof this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=T(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"===typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:var a=this.getNumber();return e&&a<1?a*n.computeSize(r):a}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"===typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"===typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?j.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,n=0;n<r;n++)if(","===e[n]&&i++,3===i)break;if(t.hasValue()&&this.isString()&&3!==i){var a=new c.a(e);a.ok&&(a.alpha=t.getNumber(),e=a.toRGBA())}return new j(this.document,this.name,e)}}j.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class Y{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"===typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class q{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=x(t);return new q(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=x(t);return new q(r,i)}static parsePath(t){for(var e=x(t),r=e.length,i=[],n=0;n<r;n+=2)i.push(new q(e[n],e[n+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],n=e*t[1]+r*t[3]+t[5];this.x=i,this.y=n}}class W{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach((t,e)=>{var{run:i}=t,n=r[e];while(n)i(n),n=n.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:a,y:s}=r;!i[n]&&e.isPointInPath&&e.isPointInPath(a,s)&&(i[n]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:a,y:s}=r;!i[n]&&e.isPointInBox(a,s)&&(i[n]=t)})}}mapXY(t,e){var{window:r,ctx:i}=this.screen,n=new q(t,e),a=i.canvas;while(a)n.x-=a.offsetLeft,n.y-=a.offsetTop,a=a.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var G="undefined"!==typeof window?window:null,Q="undefined"!==typeof fetch?fetch.bind(void 0):null;class ${constructor(t){var{fetch:e=Q,window:r=G}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new Y,this.mouse=new W(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every(t=>t());return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:n,desiredWidth:a,height:s,desiredHeight:o,minX:u=0,minY:h=0,refX:c,refY:l,clip:f=!1,clipX:d=0,clipY:g=0}=t,p=v(i).replace(/^defer\s/,""),[y,m]=p.split(" "),x=y||"xMidYMid",b=m||"meet",w=n/a,S=s/o,T=Math.min(w,S),A=Math.max(w,S),C=a,O=o;"meet"===b&&(C*=T,O*=T),"slice"===b&&(C*=A,O*=A);var E=new j(e,"refX",c),P=new j(e,"refY",l),M=E.hasValue()&&P.hasValue();if(M&&r.translate(-T*E.getPixels("x"),-T*P.getPixels("y")),f){var N=T*d,R=T*g;r.beginPath(),r.moveTo(N,R),r.lineTo(n,R),r.lineTo(n,s),r.lineTo(N,s),r.closePath(),r.clip()}if(!M){var V="meet"===b&&T===S,_="slice"===b&&A===S,k="meet"===b&&T===w,I="slice"===b&&A===w;x.startsWith("xMid")&&(V||_)&&r.translate(n/2-C/2,0),x.endsWith("YMid")&&(k||I)&&r.translate(0,s/2-O/2),x.startsWith("xMax")&&(V||_)&&r.translate(n-C,0),x.endsWith("YMax")&&(k||I)&&r.translate(0,s-O)}switch(!0){case"none"===x:r.scale(w,S);break;case"meet"===b:r.scale(T,T);break;case"slice"===b:r.scale(A,A);break}r.translate(-u,-h)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:a=!1,forceRedraw:s,scaleWidth:o,scaleHeight:h,offsetX:c,offsetY:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:f,mouse:d}=this,g=1e3/f;if(this.frameDuration=g,this.readyPromise=new Promise(t=>{this.resolveReady=t}),this.isReady()&&this.render(t,n,a,o,h,c,l),e){var p=Date.now(),v=p,y=0,m=()=>{p=Date.now(),y=p-v,y>=g&&(v=p-y%g,this.shouldUpdate(i,s)&&(this.render(t,n,a,o,h,c,l),d.runEvents())),this.intervalId=u()(m)};r||d.start(),this.intervalId=u()(m)}}stop(){this.intervalId&&(u.a.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this,i=this.animations.reduce((t,e)=>e.update(r)||t,!1);if(i)return!0}return!("function"!==typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,r,i,n,a,s){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:u,viewPort:h,ctx:c,isFirstRender:l}=this,f=c.canvas;h.clear(),f.width&&f.height?h.setCurrent(f.width,f.height):h.setCurrent(o,u);var d=t.getStyle("width"),g=t.getStyle("height");!e&&(l||"number"!==typeof i&&"number"!==typeof n)&&(d.hasValue()&&(f.width=d.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),g.hasValue()&&(f.height=g.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var p=f.clientWidth||f.width,v=f.clientHeight||f.height;if(e&&d.hasValue()&&g.hasValue()&&(p=d.getPixels("x"),v=g.getPixels("y")),h.setCurrent(p,v),"number"===typeof a&&t.getAttribute("x",!0).setValue(a),"number"===typeof s&&t.getAttribute("y",!0).setValue(s),"number"===typeof i||"number"===typeof n){var y=x(t.getAttribute("viewBox").getString()),m=0,b=0;if("number"===typeof i){var w=t.getStyle("width");w.hasValue()?m=w.getPixels("x")/i:isNaN(y[2])||(m=y[2]/i)}if("number"===typeof n){var S=t.getStyle("height");S.hasValue()?b=S.getPixels("y")/n:isNaN(y[3])||(b=y[3]/n)}m||(m=b),b||(b=m),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(n);var T=t.getStyle("transform",!0,!0);T.setValue("".concat(T.getString()," scale(").concat(1/m,", ").concat(1/b,")"))}r||c.clearRect(0,0,p,v),t.render(c),l&&(this.isFirstRender=!1)}}$.defaultWindow=G,$.defaultFetch=Q;var{defaultFetch:Z}=$,K="undefined"!==typeof DOMParser?DOMParser:null;class J{constructor(){var{fetch:t=Z,DOMParser:e=K}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return n()((function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)}))()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return n()((function*(){var r=yield e.fetch(t),i=yield r.text();return e.parseFromString(i)}))()}}class tt{constructor(t,e){this.type="translate",this.point=null,this.point=q.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class et{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=x(e);this.angle=new j(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:n,angle:a}=this,s=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(s,o),t.rotate(a.getRadians()),t.translate(-s,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:n,angle:a}=this,s=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(s,o),t.rotate(-1*a.getRadians()),t.translate(-s,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,n=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class rt{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=q.parseScale(e);0!==i.x&&0!==i.y||(i.x=_,i.y=_),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,a=i.getPixels("x"),s=n.getPixels("y");t.translate(a,s),t.scale(e,r||e),t.translate(-a,-s)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,a=i.getPixels("x"),s=n.getPixels("y");t.translate(a,s),t.scale(1/e,1/r||e),t.translate(-a,-s)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class it{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=x(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,n=e.getPixels("x"),a=r.getPixels("y");t.translate(n,a),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-n,-a)}unapply(t){var{originX:e,originY:r,matrix:i}=this,n=i[0],a=i[2],s=i[4],o=i[1],u=i[3],h=i[5],c=0,l=0,f=1,d=1/(n*(u*f-h*l)-a*(o*f-h*c)+s*(o*l-u*c)),g=e.getPixels("x"),p=r.getPixels("y");t.translate(g,p),t.transform(d*(u*f-h*l),d*(h*c-o*f),d*(s*l-a*f),d*(n*f-s*c),d*(a*h-s*u),d*(s*o-n*h)),t.translate(-g,-p)}applyToPoint(t){t.applyTransform(this.matrix)}}class nt extends it{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new j(t,"angle",e)}}class at extends nt{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class st extends nt{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}function ot(t){return v(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}function ut(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}class ht{constructor(t,e,r){this.document=t,this.transforms=[];var i=ot(e);i.forEach(t=>{if("none"!==t){var[e,i]=ut(t),n=ht.transformTypes[e];"undefined"!==typeof n&&this.transforms.push(new n(this.document,i,r))}})}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,n=i]=e.getStyle("transform-origin",!1,!0).split(),a=[i,n];return r.hasValue()?new ht(t,r.getString(),a):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length,i=r-1;i>=0;i--)e[i].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}ht.transformTypes={translate:tt,rotate:et,scale:rt,matrix:it,skewX:at,skewY:st};class ct{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes={},this.styles={},this.stylesSpecificity={},this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach(e=>{var r=w(e.nodeName);this.attributes[r]=new j(t,r,e.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){var i=this.getAttribute("style").getString().split(";").map(t=>t.trim());i.forEach(e=>{if(e){var[r,i]=e.split(":").map(t=>t.trim());this.styles[r]=new j(t,r,i)}})}var{definitions:n}=t,a=this.getAttribute("id");a.hasValue()&&(n[a.getString()]||(n[a.getString()]=this)),Array.from(e.childNodes).forEach(e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}})}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new j(this.document,t,"");return this.attributes[t]=i,i}return r||j.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return j.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var n=this.getAttribute(t);if(null!==n&&void 0!==n&&n.hasValue())return this.styles[t]=n,n;if(!r){var{parent:a}=this;if(a){var s=a.getStyle(t);if(null!==s&&void 0!==s&&s.hasValue())return s}}if(e){var o=new j(this.document,t,"");return this.styles[t]=o,o}return i||j.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=ht.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach(e=>{e.render(t)})}addChild(t){var e=t instanceof ct?t:this.document.createElement(t);e.parent=this,ct.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"===typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!i||""===i)&&i.split(" ").some(e=>".".concat(e)===t)}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],n=e[r];if(i)for(var a in i){var s=this.stylesSpecificity[a];"undefined"===typeof s&&(s="000"),n>=s&&(this.styles[a]=i[a],this.stylesSpecificity[a]=n)}}}removeStyles(t,e){var r=e.reduce((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var n=i.getString();return i.setValue(""),[...e,[r,n]]},[]);return r}restoreStyles(t,e){e.forEach(e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)})}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}ct.ignoreChildTypes=["title"];class lt extends ct{constructor(t,e,r){super(t,e,r)}}function ft(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function dt(e){return"undefined"===typeof t?e:e.trim().split(",").map(ft).join(",")}function gt(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function pt(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class vt{constructor(t,e,r,i,n,a){var s=a?"string"===typeof a?vt.parse(a):a:{};this.fontFamily=n||s.fontFamily,this.fontSize=i||s.fontSize,this.fontStyle=t||s.fontStyle,this.fontWeight=r||s.fontWeight,this.fontVariant=e||s.fontVariant}static parse(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0,r="",i="",n="",a="",s="",o=v(t).trim().split(" "),u={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return o.forEach(t=>{switch(!0){case!u.fontStyle&&vt.styles.includes(t):"inherit"!==t&&(r=t),u.fontStyle=!0;break;case!u.fontVariant&&vt.variants.includes(t):"inherit"!==t&&(i=t),u.fontStyle=!0,u.fontVariant=!0;break;case!u.fontWeight&&vt.weights.includes(t):"inherit"!==t&&(n=t),u.fontStyle=!0,u.fontVariant=!0,u.fontWeight=!0;break;case!u.fontSize:"inherit"!==t&&([a]=t.split("/")),u.fontStyle=!0,u.fontVariant=!0,u.fontWeight=!0,u.fontSize=!0;break;default:"inherit"!==t&&(s+=t)}}),new vt(r,i,n,a,s,e)}toString(){return[gt(this.fontStyle),this.fontVariant,pt(this.fontWeight),this.fontSize,dt(this.fontFamily)].join(" ").trim()}}vt.styles="normal|italic|oblique|inherit",vt.variants="normal|small-caps|inherit",vt.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class yt{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){"undefined"!==typeof t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),"undefined"!==typeof e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:n}=t;this.addPoint(e,r),this.addPoint(i,n)}}sumCubic(t,e,r,i,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*n}bezierCurveAdd(t,e,r,i,n){var a=6*e-12*r+6*i,s=-3*e+9*r-9*i+3*n,o=3*r-3*e;if(0!==s){var u=Math.pow(a,2)-4*o*s;if(!(u<0)){var h=(-a+Math.sqrt(u))/(2*s);0<h&&h<1&&(t?this.addX(this.sumCubic(h,e,r,i,n)):this.addY(this.sumCubic(h,e,r,i,n)));var c=(-a-Math.sqrt(u))/(2*s);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,n)):this.addY(this.sumCubic(c,e,r,i,n)))}}else{if(0===a)return;var l=-o/a;0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,n)):this.addY(this.sumCubic(l,e,r,i,n)))}}addBezierCurve(t,e,r,i,n,a,s,o){this.addPoint(t,e),this.addPoint(s,o),this.bezierCurveAdd(!0,t,r,n,s),this.bezierCurveAdd(!1,e,i,a,o)}addQuadraticCurve(t,e,r,i,n,a){var s=t+2/3*(r-t),o=e+2/3*(i-e),u=s+1/3*(n-t),h=o+1/3*(a-e);this.addBezierCurve(t,e,s,u,o,h,n,a)}isPointInBox(t,e){var{x1:r,y1:i,x2:n,y2:a}=this;return r<=t&&t<=n&&i<=e&&e<=a}}class mt extends l["a"]{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new q(0,0),this.control=new q(0,0),this.current=new q(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new q(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==l["a"].CURVE_TO&&t!==l["a"].SMOOTH_CURVE_TO&&t!==l["a"].QUAD_TO&&t!==l["a"].SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:n}}=this,a=new q(2*e-i,2*r-n);return a}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}return t}}class xt extends ct{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){var t=1,e=this;while(e){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),a=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,i);s&&(t.fillStyle=s)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var u=new j(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=u}if(n.isUrlDefinition()){var h=n.getFillStyleDefinition(this,a);h&&(t.strokeStyle=h)}else if(n.hasValue()){"currentColor"===n.getString()&&n.setValue(this.getStyle("color").getColor());var c=n.getString();"inherit"!==c&&(t.strokeStyle="none"===c?"rgba(0,0,0,0)":c)}if(a.hasValue()){var l=new j(this.document,"stroke",t.strokeStyle).addOpacity(a).getString();t.strokeStyle=l}var f=this.getStyle("stroke-width");if(f.hasValue()){var d=f.getPixels();t.lineWidth=d||_}var g=this.getStyle("stroke-linecap"),p=this.getStyle("stroke-linejoin"),v=this.getStyle("stroke-miterlimit"),y=this.getStyle("stroke-dasharray"),m=this.getStyle("stroke-dashoffset");if(g.hasValue()&&(t.lineCap=g.getString()),p.hasValue()&&(t.lineJoin=p.getString()),v.hasValue()&&(t.miterLimit=v.getNumber()),y.hasValue()&&"none"!==y.getString()){var b=x(y.getString());"undefined"!==typeof t.setLineDash?t.setLineDash(b):"undefined"!==typeof t.webkitLineDash?t.webkitLineDash=b:"undefined"===typeof t.mozDash||1===b.length&&0===b[0]||(t.mozDash=b);var w=m.getPixels();"undefined"!==typeof t.lineDashOffset?t.lineDashOffset=w:"undefined"!==typeof t.webkitLineDashOffset?t.webkitLineDashOffset=w:"undefined"!==typeof t.mozDashOffset&&(t.mozDashOffset=w)}}if(this.modifiedEmSizeStack=!1,"undefined"!==typeof t.font){var S=this.getStyle("font"),T=this.getStyle("font-style"),A=this.getStyle("font-variant"),C=this.getStyle("font-weight"),O=this.getStyle("font-size"),E=this.getStyle("font-family"),P=new vt(T.getString(),A.getString(),C.getString(),O.hasValue()?"".concat(O.getPixels(!0),"px"):"",E.getString(),vt.parse(S.getString(),t.font));T.setValue(P.fontStyle),A.setValue(P.fontVariant),C.setValue(P.fontWeight),O.setValue(P.fontSize),E.setValue(P.fontFamily),t.font=P.toString(),O.isPixels()&&(this.document.emSize=O.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class bt extends xt{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new mt(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new yt;e.reset(),t&&t.beginPath();while(!e.isEnd())switch(e.next().type){case mt.MOVE_TO:this.pathM(t,r);break;case mt.LINE_TO:this.pathL(t,r);break;case mt.HORIZ_LINE_TO:this.pathH(t,r);break;case mt.VERT_LINE_TO:this.pathV(t,r);break;case mt.CURVE_TO:this.pathC(t,r);break;case mt.SMOOTH_CURVE_TO:this.pathS(t,r);break;case mt.QUAD_TO:this.pathQ(t,r);break;case mt.SMOOTH_QUAD_TO:this.pathT(t,r);break;case mt.ARC:this.pathA(t,r);break;case mt.CLOSE_PATH:this.pathZ(t,r);break}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles(),i=e.map((t,e)=>[t,r[e]]);return i}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),a=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(n.isUrlDefinition()){var o=n.getDefinition(),[u,h]=r[0];o.render(t,u,h)}if(a.isUrlDefinition())for(var c=a.getDefinition(),l=1;l<i;l++){var[f,d]=r[l];c.render(t,f,d)}if(s.isUrlDefinition()){var g=s.getDefinition(),[p,v]=r[i];g.render(t,p,v)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=bt.pathM(r),{x:n,y:a}=i;r.addMarker(i),e.addPoint(n,a),t&&t.moveTo(n,a)}static pathL(t){var{current:e}=t,r=t.getAsCurrentPoint();return{current:e,point:r}}pathL(t,e){var{pathParser:r}=this,{current:i,point:n}=bt.pathL(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathH(t){var{current:e,command:r}=t,i=new q((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:n}=bt.pathH(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathV(t){var{current:e,command:r}=t,i=new q(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:n}=bt.pathV(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathC(t){var{current:e}=t,r=t.getPoint("x1","y1"),i=t.getAsControlPoint("x2","y2"),n=t.getAsCurrentPoint();return{current:e,point:r,controlPoint:i,currentPoint:n}}pathC(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:a,currentPoint:s}=bt.pathC(r);r.addMarker(s,a,n),e.addBezierCurve(i.x,i.y,n.x,n.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(n.x,n.y,a.x,a.y,s.x,s.y)}static pathS(t){var{current:e}=t,r=t.getReflectedControlPoint(),i=t.getAsControlPoint("x2","y2"),n=t.getAsCurrentPoint();return{current:e,point:r,controlPoint:i,currentPoint:n}}pathS(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:a,currentPoint:s}=bt.pathS(r);r.addMarker(s,a,n),e.addBezierCurve(i.x,i.y,n.x,n.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(n.x,n.y,a.x,a.y,s.x,s.y)}static pathQ(t){var{current:e}=t,r=t.getAsControlPoint("x1","y1"),i=t.getAsCurrentPoint();return{current:e,controlPoint:r,currentPoint:i}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:a}=bt.pathQ(r);r.addMarker(a,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,a.x,a.y),t&&t.quadraticCurveTo(n.x,n.y,a.x,a.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();t.control=r;var i=t.getAsCurrentPoint();return{current:e,controlPoint:r,currentPoint:i}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:a}=bt.pathT(r);r.addMarker(a,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,a.x,a.y),t&&t.quadraticCurveTo(n.x,n.y,a.x,a.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:n,xRot:a,lArcFlag:s,sweepFlag:o}=r,u=a*(Math.PI/180),h=t.getAsCurrentPoint(),c=new q(Math.cos(u)*(e.x-h.x)/2+Math.sin(u)*(e.y-h.y)/2,-Math.sin(u)*(e.x-h.x)/2+Math.cos(u)*(e.y-h.y)/2),l=Math.pow(c.x,2)/Math.pow(i,2)+Math.pow(c.y,2)/Math.pow(n,2);l>1&&(i*=Math.sqrt(l),n*=Math.sqrt(l));var f=(s===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(c.y,2)-Math.pow(n,2)*Math.pow(c.x,2))/(Math.pow(i,2)*Math.pow(c.y,2)+Math.pow(n,2)*Math.pow(c.x,2)));isNaN(f)&&(f=0);var d=new q(f*i*c.y/n,f*-n*c.x/i),g=new q((e.x+h.x)/2+Math.cos(u)*d.x-Math.sin(u)*d.y,(e.y+h.y)/2+Math.sin(u)*d.x+Math.cos(u)*d.y),p=L([1,0],[(c.x-d.x)/i,(c.y-d.y)/n]),v=[(c.x-d.x)/i,(c.y-d.y)/n],y=[(-c.x-d.x)/i,(-c.y-d.y)/n],m=L(v,y);return I(v,y)<=-1&&(m=Math.PI),I(v,y)>=1&&(m=0),{currentPoint:h,rX:i,rY:n,sweepFlag:o,xAxisRotation:u,centp:g,a1:p,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:a,sweepFlag:s,xAxisRotation:o,centp:u,a1:h,ad:c}=bt.pathA(r),l=1-s?1:-1,f=h+l*(c/2),d=new q(u.x+n*Math.cos(f),u.y+a*Math.sin(f));if(r.addMarkerAngle(d,f-l*Math.PI/2),r.addMarkerAngle(i,f-l*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(h)&&!isNaN(c)){var g=n>a?n:a,p=n>a?1:n/a,v=n>a?a/n:1;t.translate(u.x,u.y),t.rotate(o),t.scale(p,v),t.arc(0,0,g,h,h+c,Boolean(1-s)),t.scale(1/p,1/v),t.rotate(-o),t.translate(-u.x,-u.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){bt.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class wt extends bt{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class St extends xt{constructor(t,e,r){super(t,e,new.target===St||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,i)=>{var n=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(n):e=n}),e}getFontSize(){var{document:t,parent:e}=this,r=vt.parse(t.ctx.font).fontSize,i=e.getStyle("font-size").getNumber(r);return i}getTElementBoundingBox(t){var e=this.getFontSize();return new yt(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],n=null;if(t.isArabic){var a=e.length,s=e[r-1],o=e[r+1],u="isolated";if((0===r||" "===s)&&r<a-1&&" "!==o&&(u="terminal"),r>0&&" "!==s&&r<a-1&&" "!==o&&(u="medial"),r>0&&" "!==s&&(r===a-1||" "===o)&&(u="initial"),"undefined"!==typeof t.glyphs[i]){var h=t.glyphs[i];n=h instanceof wt?h:h[u]}}else n=t.glyphs[i];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),n=r.length-1,a=v(e.textContent||"");return 0===i&&(a=y(a)),i===n&&(a=m(a)),a}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((e,r)=>{this.renderChild(t,this,this,r)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n)for(var{unitsPerEm:a}=n.fontFace,s=vt.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(s.fontSize),u=r.getStyle("font-style").getString(s.fontStyle),h=o/a,c=n.isRTL?i.split("").reverse().join(""):i,l=x(r.getAttribute("dx").getString()),f=c.length,d=0;d<f;d++){var g=this.getGlyph(n,c,d);t.translate(this.x,this.y),t.scale(h,-h);var p=t.lineWidth;t.lineWidth=t.lineWidth*a/o,"italic"===u&&t.transform(1,0,.4,1,0,0),g.render(t),"italic"===u&&t.transform(1,0,-.4,1,0,0),t.lineWidth=p,t.scale(1/h,-1/h),t.translate(-this.x,-this.y),this.x+=o*(g.horizAdvX||n.horizAdvX)/a,"undefined"===typeof l[d]||isNaN(l[d])||(this.x+=l[d])}else{var{x:v,y:y}=this;t.fillStyle&&t.fillText(i,v,y),t.strokeStyle&&t.strokeText(i,v,y)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=!1,i=0;i="start"===e&&!r||"end"===e&&r?t.x-this.minX:"end"===e&&!r||"start"===e&&r?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var n=this.textChunkStart;n<this.leafTexts.length;n++)this.leafTexts[n].x+=i;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var n=r.children[i];n.children.length>0?n.children.forEach((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,i)}):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var n=r.children[i];if("function"!==typeof n.measureText)return n;t.save(),n.setContext(t,!0);var a=n.getAttribute("x"),s=n.getAttribute("y"),o=n.getAttribute("dx"),u=n.getAttribute("dy"),h=n.getStyle("font-family").getDefinition(),c=Boolean(h)&&h.isRTL;0===i&&(a.hasValue()||a.setValue(n.getInheritedAttribute("x")),s.hasValue()||s.setValue(n.getInheritedAttribute("y")),o.hasValue()||o.setValue(n.getInheritedAttribute("dx")),u.hasValue()||u.setValue(n.getInheritedAttribute("dy")));var l=n.measureText(t);return c&&(e.x-=l),a.hasValue()?(e.applyAnchoring(),n.x=a.getPixels("x"),o.hasValue()&&(n.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),n.x=e.x),e.x=n.x,c||(e.x+=l),s.hasValue()?(n.y=s.getPixels("y"),u.hasValue()&&(n.y+=u.getPixels("y"))):(u.hasValue()&&(e.y+=u.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+l),e.maxX=Math.max(e.maxX,n.x,n.x+l),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,r,i){var n=r.children[i];if("function"!==typeof n.getBoundingBox)return null;var a=n.getBoundingBox(t);return a?(n.children.forEach((r,i)=>{var s=e.getChildBoundingBox(t,e,n,i);a.addBoundingBox(s)}),a):null}renderChild(t,e,r,i){var n=r.children[i];n.render(t),n.children.forEach((r,i)=>{e.renderChild(t,e,n,i)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),a=i.isRTL?e.split("").reverse().join(""):e,s=x(r.getAttribute("dx").getString()),o=a.length,u=0,h=0;h<o;h++){var c=this.getGlyph(i,a,h);u+=(c.horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,"undefined"===typeof s[h]||isNaN(s[h])||(u+=s[h])}return u}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:l}=t.measureText(e);return this.clearContext(t),t.restore(),l}getInheritedAttribute(t){var e=this;while(e instanceof St&&e.isFirstChild()){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class Tt extends St{constructor(t,e,r){super(t,e,new.target===Tt||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class At extends Tt{constructor(){super(...arguments),this.type="textNode"}}class Ct extends xt{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:n}=r,a=t.canvas;if(i.setDefaults(t),a.style&&"undefined"!==typeof t.font&&n&&"undefined"!==typeof n.getComputedStyle){t.font=n.getComputedStyle(a).getPropertyValue("font");var s=new j(r,"fontSize",vt.parse(t.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:u}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),c=this.getAttribute("refY"),l=this.getAttribute("viewBox"),f=l.hasValue()?x(l.getString()):null,d=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),g=0,p=0,v=0,y=0;f&&(g=f[0],p=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y"),"marker"===this.type&&(v=g,y=p,g=0,p=0)),i.viewPort.setCurrent(o,u),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],u=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:u,minX:g,minY:p,refX:h.getValue(),refY:c.getValue(),clip:d,clipX:v,clipY:y}),f&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,u))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),a=this.getAttribute("viewBox"),s=this.getAttribute("style"),o=i.getNumber(0),u=n.getNumber(0);if(r)if("string"===typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(t),n.setValue(e),a.hasValue()||a.setValue("0 0 ".concat(o||t," ").concat(u||e)),s.hasValue()){var c=this.getStyle("width"),l=this.getStyle("height");c.hasValue()&&c.setValue("".concat(t,"px")),l.hasValue()&&l.setValue("".concat(e,"px"))}}}class Ot extends bt{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),a=this.getAttribute("rx"),s=this.getAttribute("ry"),o=a.getPixels("x"),u=s.getPixels("y");if(a.hasValue()&&!s.hasValue()&&(u=o),s.hasValue()&&!a.hasValue()&&(o=u),o=Math.min(o,i/2),u=Math.min(u,n/2),t){var h=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+h*o,r,e+i,r+u-h*u,e+i,r+u),t.lineTo(e+i,r+n-u),t.bezierCurveTo(e+i,r+n-u+h*u,e+i-o+h*o,r+n,e+i-o,r+n),t.lineTo(e+o,r+n),t.bezierCurveTo(e+o-h*o,r+n,e,r+n-u+h*u,e,r+n-u),t.lineTo(e,r+u),t.bezierCurveTo(e,r+u-h*u,e+o-h*o,r,e+o,r),t.closePath())}return new yt(e,r,e+i,r+n)}getMarkers(){return null}}class Et extends bt{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new yt(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class Pt extends bt{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),a=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(n+r,a),t.bezierCurveTo(n+r,a+e*i,n+e*r,a+i,n,a+i),t.bezierCurveTo(n-e*r,a+i,n-r,a+e*i,n-r,a),t.bezierCurveTo(n-r,a-e*i,n-e*r,a-i,n,a-i),t.bezierCurveTo(n+e*r,a-i,n+r,a-e*i,n+r,a),t.closePath()),new yt(n-r,a-i,n+r,a+i)}getMarkers(){return null}}class Mt extends bt{constructor(){super(...arguments),this.type="line"}getPoints(){return[new q(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new q(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,n)),new yt(e,r,i,n)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class Nt extends bt{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=q.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,n=new yt(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach(e=>{var{x:r,y:i}=e;n.addPoint(r,i),t&&t.lineTo(r,i)}),n}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((i,n)=>{n!==e&&r.push([i,i.angleTo(t[n+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class Rt extends Nt{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class Vt extends ct{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),a=new Ct(this.document,null);a.attributes.viewBox=new j(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.width=new j(this.document,"width","".concat(i,"px")),a.attributes.height=new j(this.document,"height","".concat(n,"px")),a.attributes.transform=new j(this.document,"transform",this.getAttribute("patternTransform").getValue()),a.children=this.children;var s=this.document.createCanvas(i,n),o=s.getContext("2d"),u=this.getAttribute("x"),h=this.getAttribute("y");u.hasValue()&&h.hasValue()&&o.translate(u.getPixels("x",!0),h.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var c=-1;c<=1;c++)for(var l=-1;l<=1;l++)o.save(),a.attributes.x=new j(this.document,"x",c*s.width),a.attributes.y=new j(this.document,"y",l*s.height),a.render(o),o.restore();var f=t.createPattern(s,"repeat");return f}}class _t extends ct{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:n}=e,a=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,n),"auto"===a&&t.rotate(r),"strokeWidth"===s&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new Ct(this.document,null);o.type=this.type,o.attributes.viewBox=new j(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new j(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new j(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new j(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new j(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new j(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new j(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new j(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===s&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===a&&t.rotate(-r),t.translate(-i,-n)}}}class kt extends ct{constructor(){super(...arguments),this.type="defs"}render(){}}class It extends xt{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new yt;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}class Lt extends ct{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach(t=>{"stop"===t.type&&i.push(t)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach(t=>{a.addColorStop(t.offset,this.addParentOpacity(r,t.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:u}=s.screen,[h]=u.viewPorts,c=new Ot(s,null);c.attributes.x=new j(s,"x",-o/3),c.attributes.y=new j(s,"y",-o/3),c.attributes.width=new j(s,"width",o),c.attributes.height=new j(s,"height",o);var l=new It(s,null);l.attributes.transform=new j(s,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[c];var f=new Ct(s,null);f.attributes.x=new j(s,"x",0),f.attributes.y=new j(s,"y",0),f.attributes.width=new j(s,"width",h.width),f.attributes.height=new j(s,"height",h.height),f.children=[l];var d=s.createCanvas(h.width,h.height),g=d.getContext("2d");return g.fillStyle=a,f.render(g),g.createPattern(d,"no-repeat")}return a}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){if(t.hasValue()){var r=new j(this.document,"color",e);return r.addOpacity(t).getColor()}return e}}class Dt extends Lt{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),a=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===s&&a===o?null:t.createLinearGradient(n,a,s,o)}}class Bt extends Lt{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),a=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=n,o=a;this.getAttribute("fx").hasValue()&&(s=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var u=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return t.createRadialGradient(s,o,h,n,a,u)}}class Ut extends ct{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),a=this.getStyle("stop-color",!0);""===a.getString()&&a.setValue("#000"),n.hasValue()&&(a=a.addOpacity(n)),this.offset=i,this.color=a.getColor()}}class zt extends ct{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new j(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var n=!1;if(this.begin<this.duration){var a=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var o=s.getString();a="".concat(o,"(").concat(a,")")}r.setValue(a),n=!0}return n}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),n=Math.floor(i),a=Math.ceil(i);r.from=new j(t,"from",parseFloat(e.getValue()[n])),r.to=new j(t,"to",parseFloat(e.getValue()[a])),r.progress=(i-n)/(a-n)}else r.from=this.from,r.to=this.to;return r}}class Ft extends zt{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new c.a(e.getColor()),n=new c.a(r.getColor());if(i.ok&&n.ok){var a=i.r+(n.r-i.r)*t,s=i.g+(n.g-i.g)*t,o=i.b+(n.b-i.b)*t;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(s),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class Ht extends zt{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=x(e.getString()),n=x(r.getString()),a=i.map((e,r)=>{var i=n[r];return e+(i-e)*t}).join(" ");return a}}class Xt extends ct{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs={},this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:n}=this;for(var a of n)switch(a.type){case"font-face":this.fontFace=a;var s=a.getStyle("font-family");s.hasValue()&&(i[s.getString()]=this);break;case"missing-glyph":this.missingGlyph=a;break;case"glyph":var o=a;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,"undefined"===typeof this.glyphs[o.unicode]&&(this.glyphs[o.unicode]={}),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o;break}}render(){}}class jt extends ct{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class Yt extends bt{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class qt extends St{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class Wt extends St{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,n=i[0],a=i.length>0&&Array.from(i).every(t=>3===t.nodeType);this.hasText=a,this.text=a?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:n}=e.screen,a=new j(e,"fontSize",vt.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new yt(r,i-a.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var s=new It(this.document,null);s.children=this.children,s.parent=this,s.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){var t=this.document.ctx;t.canvas.style.cursor="pointer"}}function Gt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function Qt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Gt(Object(r),!0).forEach((function(e){s()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Gt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}class $t extends St{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(e=>{var{type:r,points:i}=e;switch(r){case mt.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case mt.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case mt.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case mt.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case mt.ARC:var[n,a,s,o,u,h,c,l]=i,f=s>o?s:o,d=s>o?1:s/o,g=s>o?o/s:1;t&&(t.translate(n,a),t.rotate(c),t.scale(d,g),t.arc(0,0,f,u,u+h,Boolean(1-l)),t.scale(1/d,1/g),t.rotate(-c),t.translate(-n,-a));break;case mt.CLOSE_PATH:t&&t.closePath();break}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach((i,n)=>{var{p0:a,p1:s,rotation:o,text:u}=i;t.save(),t.translate(a.x,a.y),t.rotate(o),t.fillStyle&&t.fillText(u,0,0),t.strokeStyle&&t.strokeText(u,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(a.x,a.y+r/8),t.lineTo(s.x,s.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,n,a,s,o,u){var h=a,c=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(c+=(i-r)/n),u>-1&&(h+=this.getLetterSpacingAt(u));var l=this.textHeight/20,f=this.getEquidistantPointOnPath(h,l,0),d=this.getEquidistantPointOnPath(h+c,l,0),g={p0:f,p1:d},p=f&&d?Math.atan2(d.y-f.y,d.x-f.x):0;if(s){var v=Math.cos(Math.PI/2+p)*s,y=Math.cos(-p)*s;g.p0=Qt(Qt({},f),{},{x:f.x+v,y:f.y+y}),g.p1=Qt(Qt({},d),{},{x:d.x+v,y:d.y+y})}return h+=c,{offset:h,segment:g,rotation:p}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(t,i);return r.set(i,n),n}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map(t=>t.getPixels("x")),a=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),u=this.parent.getStyle("letter-spacing"),h=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(h=o.getPixels()):h=u.getPixels();var c=[],l=e.length;this.letterSpacingCache=c;for(var f=0;f<l;f++)c.push("undefined"!==typeof n[f]?n[f]:h);var d=c.reduce((t,e,r)=>0===r?0:t+e||0,0),g=this.measureText(t),p=Math.max(g+d,0);this.textWidth=g,this.textHeight=this.getFontSize(),this.glyphInfo=[];var v=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*v,m=0;"middle"!==s&&"center"!==s||(m=-p/2),"end"!==s&&"right"!==s||(m=-p),m+=y,r.forEach((e,n)=>{var{offset:o,segment:u,rotation:h}=this.findSegmentToFitChar(t,s,p,v,i,m,a,e,n);m=o,u.p0&&u.p1&&this.glyphInfo.push({text:r[n],p0:u.p0,p1:u.p1,rotation:h})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;r.reset();while(!r.isEnd()){var{current:i}=r,n=i?i.x:0,a=i?i.y:0,s=r.next(),o=s.type,u=[];switch(s.type){case mt.MOVE_TO:this.pathM(r,u);break;case mt.LINE_TO:o=this.pathL(r,u);break;case mt.HORIZ_LINE_TO:o=this.pathH(r,u);break;case mt.VERT_LINE_TO:o=this.pathV(r,u);break;case mt.CURVE_TO:this.pathC(r,u);break;case mt.SMOOTH_CURVE_TO:o=this.pathS(r,u);break;case mt.QUAD_TO:this.pathQ(r,u);break;case mt.SMOOTH_QUAD_TO:o=this.pathT(r,u);break;case mt.ARC:u=this.pathA(r);break;case mt.CLOSE_PATH:bt.pathZ(r);break}s.type!==mt.CLOSE_PATH?e.push({type:o,points:u,start:{x:n,y:a},pathLength:this.calcLength(n,a,o,u)}):e.push({type:mt.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=bt.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=bt.pathL(t).point;return e.push(r,i),mt.LINE_TO}pathH(t,e){var{x:r,y:i}=bt.pathH(t).point;return e.push(r,i),mt.LINE_TO}pathV(t,e){var{x:r,y:i}=bt.pathV(t).point;return e.push(r,i),mt.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:n}=bt.pathC(t);e.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:n}=bt.pathS(t);return e.push(r.x,r.y,i.x,i.y,n.x,n.y),mt.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=bt.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=bt.pathT(t);return e.push(r.x,r.y,i.x,i.y),mt.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:n,centp:a,a1:s,ad:o}=bt.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[a.x,a.y,e,r,s,o,n,i]}calcLength(t,e,r,i){var n=0,a=null,s=null,o=0;switch(r){case mt.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case mt.CURVE_TO:for(n=0,a=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),o=.01;o<=1;o+=.01)s=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return n;case mt.QUAD_TO:for(n=0,a=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]),o=.01;o<=1;o+=.01)s=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return n;case mt.ARC:n=0;var u=i[4],h=i[5],c=i[4]+h,l=Math.PI/180;if(Math.abs(u-c)<l&&(l=Math.abs(u-c)),a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),h<0)for(o=u-l;o>c;o-=l)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;else for(o=u+l;o<c;o+=l)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],c,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),n}return 0}getPointOnLine(t,e,r,i,n){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(n-r)/(i-e+_),u=Math.sqrt(t*t/(1+o*o));i<e&&(u*=-1);var h=o*u,c=null;if(i===e)c={x:a,y:s+h};else if((s-r)/(a-e+_)===o)c={x:a+u,y:s+h};else{var l=0,f=0,d=this.getLineLength(e,r,i,n);if(d<_)return null;var g=(a-e)*(i-e)+(s-r)*(n-r);g/=d*d,l=e+g*(i-e),f=r+g*(n-r);var p=this.getLineLength(a,s,l,f),v=Math.sqrt(t*t-p*p);u=Math.sqrt(v*v/(1+o*o)),i<e&&(u*=-1),h=o*u,c={x:l+u,y:f+h}}return c}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:n}=this;for(var a of n){if(!a||!(a.pathLength<5e-5||r+a.pathLength+5e-5<t)){var s=t-r,o=0;switch(a.type){case mt.LINE_TO:i=this.getPointOnLine(s,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case mt.ARC:var u=a.points[4],h=a.points[5],c=a.points[4]+h;if(o=u+s/a.pathLength*h,h<0&&o<c||h>=0&&o>c)break;i=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],o,a.points[6]);break;case mt.CURVE_TO:o=s/a.pathLength,o>1&&(o=1),i=this.getPointOnCubicBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case mt.QUAD_TO:o=s/a.pathLength,o>1&&(o=1),i=this.getPointOnQuadraticBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3]);break}if(i)return i;break}r+=a.pathLength}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,n,a,s,o,u){var h=o*D(t)+a*B(t)+i*U(t)+e*z(t),c=u*D(t)+s*B(t)+n*U(t)+r*z(t);return{x:h,y:c}}getPointOnQuadraticBezier(t,e,r,i,n,a,s){var o=a*F(t)+i*H(t)+e*X(t),u=s*F(t)+n*H(t)+r*X(t);return{x:o,y:u}}getPointOnEllipticalArc(t,e,r,i,n,a){var s=Math.cos(a),o=Math.sin(a),u={x:r*Math.cos(n),y:i*Math.sin(n)};return{x:t+(u.x*s-u.y*o),y:e+(u.x*o+u.y*s)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,n=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var a=0,s=0;s<=r;s+=i){var o=this.getPointOnPath(s),u=this.getPointOnPath(s+i);o&&u&&(a+=this.getLineLength(o.x,o.y,u.x,u.y),a>=n&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:s}),a-=n))}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var Zt=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class Kt extends xt{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(t){var e=this;return n()((function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(i){console.error('Error while loading image "'.concat(t,'":'),i)}e.loaded=!0}))()}loadSvg(t){var e=this;return n()((function*(){var r=Zt.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var n=yield e.document.fetch(t),a=yield n.text();e.image=a}catch(s){console.error('Error while loading image "'.concat(t,'":'),s)}e.loaded=!0}))()}renderChildren(t){var{document:e,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&s&&o){if(t.save(),t.translate(n,a),this.isSvg){var u=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:o});u.document.documentElement.parent=this,u.render()}else{var h=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:h.width,height:o,desiredHeight:h.height}),this.loaded&&("undefined"===typeof h.complete||h.complete)&&t.drawImage(h,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new yt(t,e,t+r,e+i)}}class Jt extends xt{constructor(){super(...arguments),this.type="symbol"}render(t){}}class te{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return n()((function*(){try{var{document:i}=r,n=yield i.canvg.parser.load(e),a=n.getElementsByTagName("font");Array.from(a).forEach(e=>{var r=i.createElement(e);i.definitions[t]=r})}catch(s){console.error('Error while loading font "'.concat(e,'":'),s)}r.loaded=!0}))()}}class ee extends ct{constructor(t,e,r){super(t,e,r),this.type="style";var i=v(Array.from(e.childNodes).map(t=>t.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")),n=i.split("}");n.forEach(e=>{var r=e.trim();if(r){var i=r.split("{"),n=i[0].split(","),a=i[1].split(";");n.forEach(e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(a.forEach(e=>{var r=e.indexOf(":"),n=e.substr(0,r).trim(),a=e.substr(r+1,e.length-r).trim();n&&a&&(i[n]=new j(t,n,a))}),t.styles[r]=i,t.stylesSpecificity[r]=V(r),"@font-face"===r){var n=i["font-family"].getString().replace(/"|'/g,""),s=i.src.getString().split(",");s.forEach(e=>{if(e.indexOf('format("svg")')>0){var r=S(e);r&&new te(t).load(n,r)}})}}})}})}}ee.parseExternalUrl=S;class re extends xt{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&(i=new Ct(e,null),i.attributes.viewBox=new j(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new j(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new j(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new j(e,"opacity",this.calculateOpacity())),"svg"===i.type){var n=this.getStyle("width",!1,!0),a=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new j(e,"width",n.getString())),a.hasValue()&&(i.attributes.height=new j(e,"height",a.getString()))}var s=i.parent;i.parent=this,i.render(t),i.parent=s}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return ht.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function ie(t,e,r,i,n,a){return t[r*i*4+4*e+a]}function ne(t,e,r,i,n,a,s){t[r*i*4+4*e+a]=s}function ae(t,e,r){var i=t[e];return i*r}function se(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class oe extends ct{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=x(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var a=i[0]*Math.PI/180;i=[se(a,.213,.787,-.213),se(a,.715,-.715,-.715),se(a,.072,-.072,.928),0,0,se(a,.213,-.213,.143),se(a,.715,.285,.14),se(a,.072,-.072,-.283),0,0,se(a,.213,-.213,-.787),se(a,.715,-.715,.715),se(a,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1];break}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,n){for(var{includeOpacity:a,matrix:s}=this,o=t.getImageData(0,0,i,n),u=0;u<n;u++)for(var h=0;h<i;h++){var c=ie(o.data,h,u,i,n,0),l=ie(o.data,h,u,i,n,1),f=ie(o.data,h,u,i,n,2),d=ie(o.data,h,u,i,n,3),g=ae(s,0,c)+ae(s,1,l)+ae(s,2,f)+ae(s,3,d)+ae(s,4,1),p=ae(s,5,c)+ae(s,6,l)+ae(s,7,f)+ae(s,8,d)+ae(s,9,1),v=ae(s,10,c)+ae(s,11,l)+ae(s,12,f)+ae(s,13,d)+ae(s,14,1),y=ae(s,15,c)+ae(s,16,l)+ae(s,17,f)+ae(s,18,d)+ae(s,19,1);a&&(g=0,p=0,v=0,y*=d/255),ne(o.data,h,u,i,n,0,g),ne(o.data,h,u,i,n,1,p),ne(o.data,h,u,i,n,2,v),ne(o.data,h,u,i,n,3,y)}t.clearRect(0,0,i,n),t.putImageData(o,0,0)}}class ue extends ct{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");if(!a&&!s){var o=new yt;this.children.forEach(e=>{o.addBoundingBox(e.getBoundingBox(t))}),i=Math.floor(o.x1),n=Math.floor(o.y1),a=Math.floor(o.width),s=Math.floor(o.height)}var u=this.removeStyles(e,ue.ignoreStyles),h=r.createCanvas(i+a,n+s),c=h.getContext("2d");r.screen.setDefaults(c),this.renderChildren(c),new oe(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(c,0,0,i+a,n+s);var l=r.createCanvas(i+a,n+s),f=l.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=c.createPattern(h,"no-repeat"),f.fillRect(0,0,i+a,n+s),t.fillStyle=f.createPattern(l,"no-repeat"),t.fillRect(0,0,i+a,n+s),this.restoreStyles(e,u)}render(t){}}ue.ignoreStyles=["mask","transform","clip-path"];var he=()=>{};class ce extends ct{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:n}=t;r&&(r.beginPath=he,r.closePath=he),Reflect.apply(i,t,[]),this.children.forEach(i=>{if("undefined"!==typeof i.path){var a="undefined"!==typeof i.elementTransform?i.elementTransform():null;a||(a=ht.fromElement(e,i)),a&&a.apply(t),i.path(t),r&&(r.closePath=n),a&&a.unapply(t)}}),Reflect.apply(n,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=n)}render(t){}}class le extends ct{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,n=e.getBoundingBox(t);if(n){var a=0,s=0;i.forEach(t=>{var e=t.extraFilterDistance||0;a=Math.max(a,e),s=Math.max(s,e)});var o=Math.floor(n.width),u=Math.floor(n.height),h=o+2*a,c=u+2*s;if(!(h<1||c<1)){var l=Math.floor(n.x),f=Math.floor(n.y),d=this.removeStyles(e,le.ignoreStyles),g=r.createCanvas(h,c),p=g.getContext("2d");r.screen.setDefaults(p),p.translate(-l+a,-f+s),e.render(p),i.forEach(t=>{"function"===typeof t.apply&&t.apply(p,0,0,h,c)}),t.drawImage(g,0,0,h,c,l-a,f-s,h,c),this.restoreStyles(e,d)}}}render(t){}}le.ignoreStyles=["filter","transform","clip-path"];class fe extends ct{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,n){}}class de extends ct{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,n){}}class ge extends ct{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,n){}}class pe extends ct{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,i,n){var{document:a,blurRadius:s}=this,o=a.window?a.window.document.body:null,u=t.canvas;u.id=a.getUniqueId(),o&&(u.style.display="none",o.appendChild(u)),Object(f["a"])(u,e,r,i,n,s),o&&o.removeChild(u)}}class ve extends ct{constructor(){super(...arguments),this.type="title"}}class ye extends ct{constructor(){super(...arguments),this.type="desc"}}var me={svg:Ct,rect:Ot,circle:Et,ellipse:Pt,line:Mt,polyline:Nt,polygon:Rt,path:bt,pattern:Vt,marker:_t,defs:kt,linearGradient:Dt,radialGradient:Bt,stop:Ut,animate:zt,animateColor:Ft,animateTransform:Ht,font:Xt,"font-face":jt,"missing-glyph":Yt,glyph:wt,text:St,tspan:Tt,tref:qt,a:Wt,textPath:$t,image:Kt,g:It,symbol:Jt,style:ee,use:re,mask:ue,clipPath:ce,filter:le,feDropShadow:fe,feMorphology:de,feComposite:ge,feColorMatrix:oe,feGaussianBlur:pe,title:ve,desc:ye};function xe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function be(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xe(Object(r),!0).forEach((function(e){s()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function we(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r}function Se(t){return Te.apply(this,arguments)}function Te(){return Te=n()((function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((e,i)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,n,a)=>{i(a)},r.src=t})})),Te.apply(this,arguments)}class Ae{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=Ae.createCanvas,createImage:n=Ae.createImage,anonymousCrossOrigin:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions={},this.styles={},this.stylesSpecificity={},this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,a),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"===typeof e?(r,i)=>t(r,"boolean"===typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(t=>t.loaded)}isFontsLoaded(){return this.fonts.every(t=>t.loaded)}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=Ae.elementTypes[e];return"undefined"!==typeof r?new r(this,t):new lt(this,t)}createTextNode(t){return new At(this,t)}setViewBox(t){this.screen.setViewBox(be({document:this},t))}}function Ce(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function Oe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ce(Object(r),!0).forEach((function(e){s()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ce(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}Ae.createCanvas=we,Ae.createImage=Se,Ae.elementTypes=me;class Ee{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new J(r),this.screen=new $(t,r),this.options=r;var i=new Ae(this,r),n=i.createDocumentElement(e);this.document=i,this.documentElement=n}static from(t,e){var r=arguments;return n()((function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},n=new J(i),a=yield n.parse(e);return new Ee(t,a,i)}))()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new J(r),n=i.parseFromString(e);return new Ee(t,n,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Ee.from(t,e,Oe(Oe({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Ee.fromString(t,e,Oe(Oe({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return n()((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(Oe({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()}))()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,Oe(Oe({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}}.call(this,r("4362"))},"107c":function(t,e,r){var i=r("d039"),n=r("da84"),a=n.RegExp;t.exports=i((function(){var t=a("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},1276:function(t,e,r){"use strict";var i=r("2ba4"),n=r("c65b"),a=r("e330"),s=r("d784"),o=r("825a"),u=r("7234"),h=r("44e7"),c=r("1d80"),l=r("4840"),f=r("8aa5"),d=r("50c4"),g=r("577e"),p=r("dc4a"),v=r("4dae"),y=r("14c3"),m=r("9263"),x=r("9f7f"),b=r("d039"),w=x.UNSUPPORTED_Y,S=4294967295,T=Math.min,A=[].push,C=a(/./.exec),O=a(A),E=a("".slice),P=!b((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]}));s("split",(function(t,e,r){var a;return a="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,r){var a=g(c(this)),s=void 0===r?S:r>>>0;if(0===s)return[];if(void 0===t)return[a];if(!h(t))return n(e,a,t,s);var o,u,l,f=[],d=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,y=new RegExp(t.source,d+"g");while(o=n(m,y,a)){if(u=y.lastIndex,u>p&&(O(f,E(a,p,o.index)),o.length>1&&o.index<a.length&&i(A,f,v(o,1)),l=o[0].length,p=u,f.length>=s))break;y.lastIndex===o.index&&y.lastIndex++}return p===a.length?!l&&C(y,"")||O(f,""):O(f,E(a,p)),f.length>s?v(f,0,s):f}:"0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:n(e,this,t,r)}:e,[function(e,r){var i=c(this),s=u(e)?void 0:p(e,t);return s?n(s,e,i,r):n(a,g(i),e,r)},function(t,i){var n=o(this),s=g(t),u=r(a,n,s,i,a!==e);if(u.done)return u.value;var h=l(n,RegExp),c=n.unicode,p=(n.ignoreCase?"i":"")+(n.multiline?"m":"")+(n.unicode?"u":"")+(w?"g":"y"),v=new h(w?"^(?:"+n.source+")":n,p),m=void 0===i?S:i>>>0;if(0===m)return[];if(0===s.length)return null===y(v,s)?[s]:[];var x=0,b=0,A=[];while(b<s.length){v.lastIndex=w?0:b;var C,P=y(v,w?E(s,b):s);if(null===P||(C=T(d(v.lastIndex+(w?b:0)),s.length))===x)b=f(s,b,c);else{if(O(A,E(s,x,b)),A.length===m)return A;for(var M=1;M<=P.length-1;M++)if(O(A,P[M]),A.length===m)return A;b=x=C}}return O(A,E(s,x)),A}]}),!P,w)},"14c3":function(t,e,r){var i=r("c65b"),n=r("825a"),a=r("1626"),s=r("c6b6"),o=r("9263"),u=TypeError;t.exports=function(t,e){var r=t.exec;if(a(r)){var h=i(r,t,e);return null!==h&&n(h),h}if("RegExp"===s(t))return i(o,t,e);throw u("RegExp#exec called on incompatible receiver")}},"14e5":function(t,e,r){"use strict";var i=r("23e7"),n=r("c65b"),a=r("59ed"),s=r("f069"),o=r("e667"),u=r("2266"),h=r("5eed");i({target:"Promise",stat:!0,forced:h},{all:function(t){var e=this,r=s.f(e),i=r.resolve,h=r.reject,c=o((function(){var r=a(e.resolve),s=[],o=0,c=1;u(t,(function(t){var a=o++,u=!1;c++,n(r,e,t).then((function(t){u||(u=!0,s[a]=t,--c||i(s))}),h)})),--c||i(s)}));return c.error&&h(c.value),r.promise}})},"19aa":function(t,e,r){var i=r("3a9b"),n=TypeError;t.exports=function(t,e){if(i(e,t))return t;throw n("Incorrect invocation")}},"1be4":function(t,e,r){var i=r("d066");t.exports=i("document","documentElement")},"1c7e":function(t,e,r){var i=r("b622"),n=i("iterator"),a=!1;try{var s=0,o={next:function(){return{done:!!s++}},return:function(){a=!0}};o[n]=function(){return this},Array.from(o,(function(){throw 2}))}catch(u){}t.exports=function(t,e){if(!e&&!a)return!1;var r=!1;try{var i={};i[n]=function(){return{next:function(){return{done:r=!0}}}},t(i)}catch(u){}return r}},"1cdc":function(t,e,r){var i=r("342f");t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(i)},2266:function(t,e,r){var i=r("0366"),n=r("c65b"),a=r("825a"),s=r("0d51"),o=r("e95a"),u=r("07fa"),h=r("3a9b"),c=r("9a1f"),l=r("35a1"),f=r("2a62"),d=TypeError,g=function(t,e){this.stopped=t,this.result=e},p=g.prototype;t.exports=function(t,e,r){var v,y,m,x,b,w,S,T=r&&r.that,A=!(!r||!r.AS_ENTRIES),C=!(!r||!r.IS_RECORD),O=!(!r||!r.IS_ITERATOR),E=!(!r||!r.INTERRUPTED),P=i(e,T),M=function(t){return v&&f(v,"normal",t),new g(!0,t)},N=function(t){return A?(a(t),E?P(t[0],t[1],M):P(t[0],t[1])):E?P(t,M):P(t)};if(C)v=t.iterator;else if(O)v=t;else{if(y=l(t),!y)throw d(s(t)+" is not iterable");if(o(y)){for(m=0,x=u(t);x>m;m++)if(b=N(t[m]),b&&h(p,b))return b;return new g(!1)}v=c(t,y)}w=C?t.next:v.next;while(!(S=n(w,v)).done){try{b=N(S.value)}catch(R){f(v,"throw",R)}if("object"==typeof b&&b&&h(p,b))return b}return new g(!1)}},2532:function(t,e,r){"use strict";var i=r("23e7"),n=r("e330"),a=r("5a34"),s=r("1d80"),o=r("577e"),u=r("ab13"),h=n("".indexOf);i({target:"String",proto:!0,forced:!u("includes")},{includes:function(t){return!!~h(o(s(this)),o(a(t)),arguments.length>1?arguments[1]:void 0)}})},"25f0":function(t,e,r){"use strict";var i=r("5e77").PROPER,n=r("cb2d"),a=r("825a"),s=r("577e"),o=r("d039"),u=r("90d8"),h="toString",c=RegExp.prototype,l=c[h],f=o((function(){return"/a/b"!=l.call({source:"a",flags:"b"})})),d=i&&l.name!=h;(f||d)&&n(RegExp.prototype,h,(function(){var t=a(this),e=s(t.source),r=s(u(t));return"/"+e+"/"+r}),{unsafe:!0})},2626:function(t,e,r){"use strict";var i=r("d066"),n=r("9bf2"),a=r("b622"),s=r("83ab"),o=a("species");t.exports=function(t){var e=i(t),r=n.f;s&&e&&!e[o]&&r(e,o,{configurable:!0,get:function(){return this}})}},"26e9":function(t,e,r){"use strict";var i=r("23e7"),n=r("e330"),a=r("e8b5"),s=n([].reverse),o=[1,2];i({target:"Array",proto:!0,forced:String(o)===String(o.reverse())},{reverse:function(){return a(this)&&(this.length=this.length),s(this)}})},"2a62":function(t,e,r){var i=r("c65b"),n=r("825a"),a=r("dc4a");t.exports=function(t,e,r){var s,o;n(t);try{if(s=a(t,"return"),!s){if("throw"===e)throw r;return r}s=i(s,t)}catch(u){o=!0,s=u}if("throw"===e)throw r;if(o)throw s;return n(s),r}},"2ca0":function(t,e,r){"use strict";var i=r("23e7"),n=r("e330"),a=r("06cf").f,s=r("50c4"),o=r("577e"),u=r("5a34"),h=r("1d80"),c=r("ab13"),l=r("c430"),f=n("".startsWith),d=n("".slice),g=Math.min,p=c("startsWith"),v=!l&&!p&&!!function(){var t=a(String.prototype,"startsWith");return t&&!t.writable}();i({target:"String",proto:!0,forced:!v&&!p},{startsWith:function(t){var e=o(h(this));u(t);var r=s(g(arguments.length>1?arguments[1]:void 0,e.length)),i=o(t);return f?f(e,i,r):d(e,r,r+i.length)===i}})},"2cf4":function(t,e,r){var i,n,a,s,o=r("da84"),u=r("2ba4"),h=r("0366"),c=r("1626"),l=r("1a2d"),f=r("d039"),d=r("1be4"),g=r("f36a"),p=r("cc12"),v=r("d6d6"),y=r("1cdc"),m=r("605d"),x=o.setImmediate,b=o.clearImmediate,w=o.process,S=o.Dispatch,T=o.Function,A=o.MessageChannel,C=o.String,O=0,E={},P="onreadystatechange";try{i=o.location}catch(_){}var M=function(t){if(l(E,t)){var e=E[t];delete E[t],e()}},N=function(t){return function(){M(t)}},R=function(t){M(t.data)},V=function(t){o.postMessage(C(t),i.protocol+"//"+i.host)};x&&b||(x=function(t){v(arguments.length,1);var e=c(t)?t:T(t),r=g(arguments,1);return E[++O]=function(){u(e,void 0,r)},n(O),O},b=function(t){delete E[t]},m?n=function(t){w.nextTick(N(t))}:S&&S.now?n=function(t){S.now(N(t))}:A&&!y?(a=new A,s=a.port2,a.port1.onmessage=R,n=h(s.postMessage,s)):o.addEventListener&&c(o.postMessage)&&!o.importScripts&&i&&"file:"!==i.protocol&&!f(V)?(n=V,o.addEventListener("message",R,!1)):n=P in p("script")?function(t){d.appendChild(p("script"))[P]=function(){d.removeChild(this),M(t)}}:function(t){setTimeout(N(t),0)}),t.exports={set:x,clear:b}},3529:function(t,e,r){"use strict";var i=r("23e7"),n=r("c65b"),a=r("59ed"),s=r("f069"),o=r("e667"),u=r("2266"),h=r("5eed");i({target:"Promise",stat:!0,forced:h},{race:function(t){var e=this,r=s.f(e),i=r.reject,h=o((function(){var s=a(e.resolve);u(t,(function(t){n(s,e,t).then(r.resolve,i)}))}));return h.error&&i(h.value),r.promise}})},"35a1":function(t,e,r){var i=r("f5df"),n=r("dc4a"),a=r("7234"),s=r("3f8c"),o=r("b622"),u=o("iterator");t.exports=function(t){if(!a(t))return n(t,u)||n(t,"@@iterator")||s[i(t)]}},"37e8":function(t,e,r){var i=r("83ab"),n=r("aed9"),a=r("9bf2"),s=r("825a"),o=r("fc6a"),u=r("df75");e.f=i&&!n?Object.defineProperties:function(t,e){s(t);var r,i=o(e),n=u(e),h=n.length,c=0;while(h>c)a.f(t,r=n[c++],i[r]);return t}},"3f8c":function(t,e){t.exports={}},"44d2":function(t,e,r){var i=r("b622"),n=r("7c73"),a=r("9bf2").f,s=i("unscopables"),o=Array.prototype;void 0==o[s]&&a(o,s,{configurable:!0,value:n(null)}),t.exports=function(t){o[s][t]=!0}},"44de":function(t,e,r){var i=r("da84");t.exports=function(t,e){var r=i.console;r&&r.error&&(1==arguments.length?r.error(t):r.error(t,e))}},"466d":function(t,e,r){"use strict";var i=r("c65b"),n=r("d784"),a=r("825a"),s=r("7234"),o=r("50c4"),u=r("577e"),h=r("1d80"),c=r("dc4a"),l=r("8aa5"),f=r("14c3");n("match",(function(t,e,r){return[function(e){var r=h(this),n=s(e)?void 0:c(e,t);return n?i(n,e,r):new RegExp(e)[t](u(r))},function(t){var i=a(this),n=u(t),s=r(e,i,n);if(s.done)return s.value;if(!i.global)return f(i,n);var h=i.unicode;i.lastIndex=0;var c,d=[],g=0;while(null!==(c=f(i,n))){var p=u(c[0]);d[g]=p,""===p&&(i.lastIndex=l(n,o(i.lastIndex),h)),g++}return 0===g?null:d}]}))},4738:function(t,e,r){var i=r("da84"),n=r("d256"),a=r("1626"),s=r("94ca"),o=r("8925"),u=r("b622"),h=r("6069"),c=r("6c59"),l=r("c430"),f=r("2d00"),d=n&&n.prototype,g=u("species"),p=!1,v=a(i.PromiseRejectionEvent),y=s("Promise",(function(){var t=o(n),e=t!==String(n);if(!e&&66===f)return!0;if(l&&(!d["catch"]||!d["finally"]))return!0;if(!f||f<51||!/native code/.test(t)){var r=new n((function(t){t(1)})),i=function(t){t((function(){}),(function(){}))},a=r.constructor={};if(a[g]=i,p=r.then((function(){}))instanceof i,!p)return!0}return!e&&(h||c)&&!v}));t.exports={CONSTRUCTOR:y,REJECTION_EVENT:v,SUBCLASSING:p}},4840:function(t,e,r){var i=r("825a"),n=r("5087"),a=r("7234"),s=r("b622"),o=s("species");t.exports=function(t,e){var r,s=i(t).constructor;return void 0===s||a(r=i(s)[o])?e:n(r)}},"498a":function(t,e,r){"use strict";var i=r("23e7"),n=r("58a8").trim,a=r("c8d2");i({target:"String",proto:!0,forced:a("trim")},{trim:function(){return n(this)}})},"4dae":function(t,e,r){var i=r("23cb"),n=r("07fa"),a=r("8418"),s=Array,o=Math.max;t.exports=function(t,e,r){for(var u=n(t),h=i(e,u),c=i(void 0===r?u:r,u),l=s(o(c-h,0)),f=0;h<c;h++,f++)a(l,f,t[h]);return l.length=f,l}},5087:function(t,e,r){var i=r("68ee"),n=r("0d51"),a=TypeError;t.exports=function(t){if(i(t))return t;throw a(n(t)+" is not a constructor")}},5319:function(t,e,r){"use strict";var i=r("2ba4"),n=r("c65b"),a=r("e330"),s=r("d784"),o=r("d039"),u=r("825a"),h=r("1626"),c=r("7234"),l=r("5926"),f=r("50c4"),d=r("577e"),g=r("1d80"),p=r("8aa5"),v=r("dc4a"),y=r("0cb2"),m=r("14c3"),x=r("b622"),b=x("replace"),w=Math.max,S=Math.min,T=a([].concat),A=a([].push),C=a("".indexOf),O=a("".slice),E=function(t){return void 0===t?t:String(t)},P=function(){return"$0"==="a".replace(/./,"$0")}(),M=function(){return!!/./[b]&&""===/./[b]("a","$0")}(),N=!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}));s("replace",(function(t,e,r){var a=M?"$":"$0";return[function(t,r){var i=g(this),a=c(t)?void 0:v(t,b);return a?n(a,t,i,r):n(e,d(i),t,r)},function(t,n){var s=u(this),o=d(t);if("string"==typeof n&&-1===C(n,a)&&-1===C(n,"$<")){var c=r(e,s,o,n);if(c.done)return c.value}var g=h(n);g||(n=d(n));var v=s.global;if(v){var x=s.unicode;s.lastIndex=0}var b=[];while(1){var P=m(s,o);if(null===P)break;if(A(b,P),!v)break;var M=d(P[0]);""===M&&(s.lastIndex=p(o,f(s.lastIndex),x))}for(var N="",R=0,V=0;V<b.length;V++){P=b[V];for(var _=d(P[0]),k=w(S(l(P.index),o.length),0),I=[],L=1;L<P.length;L++)A(I,E(P[L]));var D=P.groups;if(g){var B=T([_],I,k,o);void 0!==D&&A(B,D);var U=d(i(n,void 0,B))}else U=y(_,o,k,I,D,n);k>=R&&(N+=O(o,R,k)+U,R=k+_.length)}return N+O(o,R)}]}),!N||!P||M)},5899:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},"58a8":function(t,e,r){var i=r("e330"),n=r("1d80"),a=r("577e"),s=r("5899"),o=i("".replace),u="["+s+"]",h=RegExp("^"+u+u+"*"),c=RegExp(u+u+"*$"),l=function(t){return function(e){var r=a(n(e));return 1&t&&(r=o(r,h,"")),2&t&&(r=o(r,c,"")),r}};t.exports={start:l(1),end:l(2),trim:l(3)}},"58e1":function(t,e){t.exports=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=t.replace(/ /g,""),t=t.toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<r.length;i++){var n=r[i].re,a=r[i].process,s=n.exec(t);if(s){var o=a(s);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,i=0;i<r.length;i++)for(var n=r[i].example,a=0;a<n.length;a++)t[t.length]=n[a];for(var s in e)t[t.length]=s;var o=document.createElement("ul");o.setAttribute("id","rgbcolor-examples");for(i=0;i<t.length;i++)try{var u=document.createElement("li"),h=new RGBColor(t[i]),c=document.createElement("div");c.style.cssText="margin: 3px; border: 1px solid black; background:"+h.toHex()+"; color:"+h.toHex(),c.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+t[i]+" -> "+h.toRGB()+" -> "+h.toHex());u.appendChild(c),u.appendChild(l),o.appendChild(u)}catch(f){}return o}}},"5a34":function(t,e,r){var i=r("44e7"),n=TypeError;t.exports=function(t){if(i(t))throw n("The method doesn't accept regular expressions");return t}},"5e7e":function(t,e,r){"use strict";var i,n,a,s,o=r("23e7"),u=r("c430"),h=r("605d"),c=r("da84"),l=r("c65b"),f=r("cb2d"),d=r("d2bb"),g=r("d44e"),p=r("2626"),v=r("59ed"),y=r("1626"),m=r("861d"),x=r("19aa"),b=r("4840"),w=r("2cf4").set,S=r("b575"),T=r("44de"),A=r("e667"),C=r("01b4"),O=r("69f3"),E=r("d256"),P=r("4738"),M=r("f069"),N="Promise",R=P.CONSTRUCTOR,V=P.REJECTION_EVENT,_=P.SUBCLASSING,k=O.getterFor(N),I=O.set,L=E&&E.prototype,D=E,B=L,U=c.TypeError,z=c.document,F=c.process,H=M.f,X=H,j=!!(z&&z.createEvent&&c.dispatchEvent),Y="unhandledrejection",q="rejectionhandled",W=0,G=1,Q=2,$=1,Z=2,K=function(t){var e;return!(!m(t)||!y(e=t.then))&&e},J=function(t,e){var r,i,n,a=e.value,s=e.state==G,o=s?t.ok:t.fail,u=t.resolve,h=t.reject,c=t.domain;try{o?(s||(e.rejection===Z&&nt(e),e.rejection=$),!0===o?r=a:(c&&c.enter(),r=o(a),c&&(c.exit(),n=!0)),r===t.promise?h(U("Promise-chain cycle")):(i=K(r))?l(i,r,u,h):u(r)):h(a)}catch(f){c&&!n&&c.exit(),h(f)}},tt=function(t,e){t.notified||(t.notified=!0,S((function(){var r,i=t.reactions;while(r=i.get())J(r,t);t.notified=!1,e&&!t.rejection&&rt(t)})))},et=function(t,e,r){var i,n;j?(i=z.createEvent("Event"),i.promise=e,i.reason=r,i.initEvent(t,!1,!0),c.dispatchEvent(i)):i={promise:e,reason:r},!V&&(n=c["on"+t])?n(i):t===Y&&T("Unhandled promise rejection",r)},rt=function(t){l(w,c,(function(){var e,r=t.facade,i=t.value,n=it(t);if(n&&(e=A((function(){h?F.emit("unhandledRejection",i,r):et(Y,r,i)})),t.rejection=h||it(t)?Z:$,e.error))throw e.value}))},it=function(t){return t.rejection!==$&&!t.parent},nt=function(t){l(w,c,(function(){var e=t.facade;h?F.emit("rejectionHandled",e):et(q,e,t.value)}))},at=function(t,e,r){return function(i){t(e,i,r)}},st=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=Q,tt(t,!0))},ot=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw U("Promise can't be resolved itself");var i=K(e);i?S((function(){var r={done:!1};try{l(i,e,at(ot,r,t),at(st,r,t))}catch(n){st(r,n,t)}})):(t.value=e,t.state=G,tt(t,!1))}catch(n){st({done:!1},n,t)}}};if(R&&(D=function(t){x(this,B),v(t),l(i,this);var e=k(this);try{t(at(ot,e),at(st,e))}catch(r){st(e,r)}},B=D.prototype,i=function(t){I(this,{type:N,done:!1,notified:!1,parent:!1,reactions:new C,rejection:!1,state:W,value:void 0})},i.prototype=f(B,"then",(function(t,e){var r=k(this),i=H(b(this,D));return r.parent=!0,i.ok=!y(t)||t,i.fail=y(e)&&e,i.domain=h?F.domain:void 0,r.state==W?r.reactions.add(i):S((function(){J(i,r)})),i.promise})),n=function(){var t=new i,e=k(t);this.promise=t,this.resolve=at(ot,e),this.reject=at(st,e)},M.f=H=function(t){return t===D||t===a?new n(t):X(t)},!u&&y(E)&&L!==Object.prototype)){s=L.then,_||f(L,"then",(function(t,e){var r=this;return new D((function(t,e){l(s,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete L.constructor}catch(ut){}d&&d(L,B)}o({global:!0,constructor:!0,wrap:!0,forced:R},{Promise:D}),g(D,N,!1,!0),p(N)},"5e9e":function(t,e,r){"use strict";function i(t){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}r.d(e,"a",(function(){return u}));var a=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],s=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function o(t,e,r,n,a){if("string"===typeof t&&(t=document.getElementById(t)),!t||"object"!==i(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var s=t.getContext("2d");try{return s.getImageData(e,r,n,a)}catch(o){throw new Error("unable to access image data: "+o)}}function u(t,e,r,i,n,a){if(!(isNaN(a)||a<1)){a|=0;var s=o(t,e,r,i,n);s=h(s,e,r,i,n,a),t.getContext("2d").putImageData(s,e,r)}}function h(t,e,r,i,n,o){for(var u,h=t.data,l=2*o+1,f=i-1,d=n-1,g=o+1,p=g*(g+1)/2,v=new c,y=v,m=1;m<l;m++)y=y.next=new c,m===g&&(u=y);y.next=v;for(var x=null,b=null,w=0,S=0,T=a[o],A=s[o],C=0;C<n;C++){y=v;for(var O=h[S],E=h[S+1],P=h[S+2],M=h[S+3],N=0;N<g;N++)y.r=O,y.g=E,y.b=P,y.a=M,y=y.next;for(var R=0,V=0,_=0,k=0,I=g*O,L=g*E,D=g*P,B=g*M,U=p*O,z=p*E,F=p*P,H=p*M,X=1;X<g;X++){var j=S+((f<X?f:X)<<2),Y=h[j],q=h[j+1],W=h[j+2],G=h[j+3],Q=g-X;U+=(y.r=Y)*Q,z+=(y.g=q)*Q,F+=(y.b=W)*Q,H+=(y.a=G)*Q,R+=Y,V+=q,_+=W,k+=G,y=y.next}x=v,b=u;for(var $=0;$<i;$++){var Z=H*T>>A;if(h[S+3]=Z,0!==Z){var K=255/Z;h[S]=(U*T>>A)*K,h[S+1]=(z*T>>A)*K,h[S+2]=(F*T>>A)*K}else h[S]=h[S+1]=h[S+2]=0;U-=I,z-=L,F-=D,H-=B,I-=x.r,L-=x.g,D-=x.b,B-=x.a;var J=$+o+1;J=w+(J<f?J:f)<<2,R+=x.r=h[J],V+=x.g=h[J+1],_+=x.b=h[J+2],k+=x.a=h[J+3],U+=R,z+=V,F+=_,H+=k,x=x.next;var tt=b,et=tt.r,rt=tt.g,it=tt.b,nt=tt.a;I+=et,L+=rt,D+=it,B+=nt,R-=et,V-=rt,_-=it,k-=nt,b=b.next,S+=4}w+=i}for(var at=0;at<i;at++){S=at<<2;var st=h[S],ot=h[S+1],ut=h[S+2],ht=h[S+3],ct=g*st,lt=g*ot,ft=g*ut,dt=g*ht,gt=p*st,pt=p*ot,vt=p*ut,yt=p*ht;y=v;for(var mt=0;mt<g;mt++)y.r=st,y.g=ot,y.b=ut,y.a=ht,y=y.next;for(var xt=i,bt=0,wt=0,St=0,Tt=0,At=1;At<=o;At++){S=xt+at<<2;var Ct=g-At;gt+=(y.r=st=h[S])*Ct,pt+=(y.g=ot=h[S+1])*Ct,vt+=(y.b=ut=h[S+2])*Ct,yt+=(y.a=ht=h[S+3])*Ct,Tt+=st,bt+=ot,wt+=ut,St+=ht,y=y.next,At<d&&(xt+=i)}S=at,x=v,b=u;for(var Ot=0;Ot<n;Ot++){var Et=S<<2;h[Et+3]=ht=yt*T>>A,ht>0?(ht=255/ht,h[Et]=(gt*T>>A)*ht,h[Et+1]=(pt*T>>A)*ht,h[Et+2]=(vt*T>>A)*ht):h[Et]=h[Et+1]=h[Et+2]=0,gt-=ct,pt-=lt,vt-=ft,yt-=dt,ct-=x.r,lt-=x.g,ft-=x.b,dt-=x.a,Et=at+((Et=Ot+g)<d?Et:d)*i<<2,gt+=Tt+=x.r=h[Et],pt+=bt+=x.g=h[Et+1],vt+=wt+=x.b=h[Et+2],yt+=St+=x.a=h[Et+3],x=x.next,ct+=st=b.r,lt+=ot=b.g,ft+=ut=b.b,dt+=ht=b.a,Tt-=st,bt-=ot,wt-=ut,St-=ht,b=b.next,S+=i}}return t}var c=function t(){n(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}},"5eed":function(t,e,r){var i=r("d256"),n=r("1c7e"),a=r("4738").CONSTRUCTOR;t.exports=a||!n((function(t){i.all(t).then(void 0,(function(){}))}))},6069:function(t,e,r){var i=r("6c59"),n=r("605d");t.exports=!i&&!n&&"object"==typeof window&&"object"==typeof document},6547:function(t,e,r){var i=r("e330"),n=r("5926"),a=r("577e"),s=r("1d80"),o=i("".charAt),u=i("".charCodeAt),h=i("".slice),c=function(t){return function(e,r){var i,c,l=a(s(e)),f=n(r),d=l.length;return f<0||f>=d?t?"":void 0:(i=u(l,f),i<55296||i>56319||f+1===d||(c=u(l,f+1))<56320||c>57343?t?o(l,f):i:t?h(l,f,f+2):c-56320+(i-55296<<10)+65536)}};t.exports={codeAt:c(!1),charAt:c(!0)}},"68ee":function(t,e,r){var i=r("e330"),n=r("d039"),a=r("1626"),s=r("f5df"),o=r("d066"),u=r("8925"),h=function(){},c=[],l=o("Reflect","construct"),f=/^\s*(?:class|function)\b/,d=i(f.exec),g=!f.exec(h),p=function(t){if(!a(t))return!1;try{return l(h,c,t),!0}catch(e){return!1}},v=function(t){if(!a(t))return!1;switch(s(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return g||!!d(f,u(t))}catch(e){return!0}};v.sham=!0,t.exports=!l||n((function(){var t;return p(p.call)||!p(Object)||!p((function(){t=!0}))||t}))?v:p},"6c59":function(t,e){t.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},"6d08":function(t,e,r){(function(e){(function(){var r,i,n,a,s,o;"undefined"!==typeof performance&&null!==performance&&performance.now?t.exports=function(){return performance.now()}:"undefined"!==typeof e&&null!==e&&e.hrtime?(t.exports=function(){return(r()-s)/1e6},i=e.hrtime,r=function(){var t;return t=i(),1e9*t[0]+t[1]},a=r(),o=1e9*e.uptime(),s=a-o):Date.now?(t.exports=function(){return Date.now()-n},n=Date.now()):(t.exports=function(){return(new Date).getTime()-n},n=(new Date).getTime())}).call(this)}).call(this,r("4362"))},7149:function(t,e,r){"use strict";var i=r("23e7"),n=r("d066"),a=r("c430"),s=r("d256"),o=r("4738").CONSTRUCTOR,u=r("cdf9"),h=n("Promise"),c=a&&!o;i({target:"Promise",stat:!0,forced:a||o},{resolve:function(t){return u(c&&this===h?s:this,t)}})},"785a":function(t,e,r){var i=r("cc12"),n=i("span").classList,a=n&&n.constructor&&n.constructor.prototype;t.exports=a===Object.prototype?void 0:a},"7c73":function(t,e,r){var i,n=r("825a"),a=r("37e8"),s=r("7839"),o=r("d012"),u=r("1be4"),h=r("cc12"),c=r("f772"),l=">",f="<",d="prototype",g="script",p=c("IE_PROTO"),v=function(){},y=function(t){return f+g+l+t+f+"/"+g+l},m=function(t){t.write(y("")),t.close();var e=t.parentWindow.Object;return t=null,e},x=function(){var t,e=h("iframe"),r="java"+g+":";return e.style.display="none",u.appendChild(e),e.src=String(r),t=e.contentWindow.document,t.open(),t.write(y("document.F=Object")),t.close(),t.F},b=function(){try{i=new ActiveXObject("htmlfile")}catch(e){}b="undefined"!=typeof document?document.domain&&i?m(i):x():m(i);var t=s.length;while(t--)delete b[d][s[t]];return b()};o[p]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(v[d]=n(t),r=new v,v[d]=null,r[p]=t):r=b(),void 0===e?r:a.f(r,e)}},8418:function(t,e,r){"use strict";var i=r("a04b"),n=r("9bf2"),a=r("5c6c");t.exports=function(t,e,r){var s=i(e);s in t?n.f(t,s,a(0,r)):t[s]=r}},"8a79":function(t,e,r){"use strict";var i=r("23e7"),n=r("e330"),a=r("06cf").f,s=r("50c4"),o=r("577e"),u=r("5a34"),h=r("1d80"),c=r("ab13"),l=r("c430"),f=n("".endsWith),d=n("".slice),g=Math.min,p=c("endsWith"),v=!l&&!p&&!!function(){var t=a(String.prototype,"endsWith");return t&&!t.writable}();i({target:"String",proto:!0,forced:!v&&!p},{endsWith:function(t){var e=o(h(this));u(t);var r=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===r?i:g(s(r),i),a=o(t);return f?f(e,a,n):d(e,n-a.length,n)===a}})},"8aa5":function(t,e,r){"use strict";var i=r("6547").charAt;t.exports=function(t,e,r){return e+(r?i(t,e).length:1)}},9263:function(t,e,r){"use strict";var i=r("c65b"),n=r("e330"),a=r("577e"),s=r("ad6d"),o=r("9f7f"),u=r("5692"),h=r("7c73"),c=r("69f3").get,l=r("fce3"),f=r("107c"),d=u("native-string-replace",String.prototype.replace),g=RegExp.prototype.exec,p=g,v=n("".charAt),y=n("".indexOf),m=n("".replace),x=n("".slice),b=function(){var t=/a/,e=/b*/g;return i(g,t,"a"),i(g,e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),w=o.BROKEN_CARET,S=void 0!==/()??/.exec("")[1],T=b||S||w||l||f;T&&(p=function(t){var e,r,n,o,u,l,f,T=this,A=c(T),C=a(t),O=A.raw;if(O)return O.lastIndex=T.lastIndex,e=i(p,O,C),T.lastIndex=O.lastIndex,e;var E=A.groups,P=w&&T.sticky,M=i(s,T),N=T.source,R=0,V=C;if(P&&(M=m(M,"y",""),-1===y(M,"g")&&(M+="g"),V=x(C,T.lastIndex),T.lastIndex>0&&(!T.multiline||T.multiline&&"\n"!==v(C,T.lastIndex-1))&&(N="(?: "+N+")",V=" "+V,R++),r=new RegExp("^(?:"+N+")",M)),S&&(r=new RegExp("^"+N+"$(?!\\s)",M)),b&&(n=T.lastIndex),o=i(g,P?r:T,V),P?o?(o.input=x(o.input,R),o[0]=x(o[0],R),o.index=T.lastIndex,T.lastIndex+=o[0].length):T.lastIndex=0:b&&o&&(T.lastIndex=T.global?o.index+o[0].length:n),S&&o&&o.length>1&&i(d,o[0],r,(function(){for(u=1;u<arguments.length-2;u++)void 0===arguments[u]&&(o[u]=void 0)})),o&&E)for(o.groups=l=h(null),u=0;u<E.length;u++)f=E[u],l[f[0]]=o[f[1]];return o}),t.exports=p},9523:function(t,e){function r(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}t.exports=r,t.exports.__esModule=!0,t.exports["default"]=t.exports},"9a1f":function(t,e,r){var i=r("c65b"),n=r("59ed"),a=r("825a"),s=r("0d51"),o=r("35a1"),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?o(t):e;if(n(r))return a(i(r,t));throw u(s(t)+" is not iterable")}},"9f7f":function(t,e,r){var i=r("d039"),n=r("da84"),a=n.RegExp,s=i((function(){var t=a("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),o=s||i((function(){return!a("a","y").sticky})),u=s||i((function(){var t=a("^r","gy");return t.lastIndex=2,null!=t.exec("str")}));t.exports={BROKEN_CARET:u,MISSED_STICKY:o,UNSUPPORTED_Y:s}},a4b4:function(t,e,r){var i=r("342f");t.exports=/web0s(?!.*chrome)/i.test(i)},ab13:function(t,e,r){var i=r("b622"),n=i("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[n]=!1,"/./"[t](e)}catch(i){}}return!1}},ac1f:function(t,e,r){"use strict";var i=r("23e7"),n=r("9263");i({target:"RegExp",proto:!0,forced:/./.exec!==n},{exec:n})},ae93:function(t,e,r){"use strict";var i,n,a,s=r("d039"),o=r("1626"),u=r("861d"),h=r("7c73"),c=r("e163"),l=r("cb2d"),f=r("b622"),d=r("c430"),g=f("iterator"),p=!1;[].keys&&(a=[].keys(),"next"in a?(n=c(c(a)),n!==Object.prototype&&(i=n)):p=!0);var v=!u(i)||s((function(){var t={};return i[g].call(t)!==t}));v?i={}:d&&(i=h(i)),o(i[g])||l(i,g,(function(){return this})),t.exports={IteratorPrototype:i,BUGGY_SAFARI_ITERATORS:p}},b575:function(t,e,r){var i,n,a,s,o,u,h,c,l=r("da84"),f=r("0366"),d=r("06cf").f,g=r("2cf4").set,p=r("1cdc"),v=r("d4c3"),y=r("a4b4"),m=r("605d"),x=l.MutationObserver||l.WebKitMutationObserver,b=l.document,w=l.process,S=l.Promise,T=d(l,"queueMicrotask"),A=T&&T.value;A||(i=function(){var t,e;m&&(t=w.domain)&&t.exit();while(n){e=n.fn,n=n.next;try{e()}catch(r){throw n?s():a=void 0,r}}a=void 0,t&&t.enter()},p||m||y||!x||!b?!v&&S&&S.resolve?(h=S.resolve(void 0),h.constructor=S,c=f(h.then,h),s=function(){c(i)}):m?s=function(){w.nextTick(i)}:(g=f(g,l),s=function(){g(i)}):(o=!0,u=b.createTextNode(""),new x(i).observe(u,{characterData:!0}),s=function(){u.data=o=!o})),t.exports=A||function(t){var e={fn:t,next:void 0};a&&(a.next=e),n||(n=e,s()),a=e}},c449:function(t,e,r){(function(e){for(var i=r("6d08"),n="undefined"===typeof window?e:window,a=["moz","webkit"],s="AnimationFrame",o=n["request"+s],u=n["cancel"+s]||n["cancelRequest"+s],h=0;!o&&h<a.length;h++)o=n[a[h]+"Request"+s],u=n[a[h]+"Cancel"+s]||n[a[h]+"CancelRequest"+s];if(!o||!u){var c=0,l=0,f=[],d=1e3/60;o=function(t){if(0===f.length){var e=i(),r=Math.max(0,d-(e-c));c=r+e,setTimeout((function(){var t=f.slice(0);f.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(c)}catch(r){setTimeout((function(){throw r}),0)}}),Math.round(r))}return f.push({handle:++l,callback:t,cancelled:!1}),l},u=function(t){for(var e=0;e<f.length;e++)f[e].handle===t&&(f[e].cancelled=!0)}}t.exports=function(t){return o.call(n,t)},t.exports.cancel=function(){u.apply(n,arguments)},t.exports.polyfill=function(t){t||(t=n),t.requestAnimationFrame=o,t.cancelAnimationFrame=u}}).call(this,r("c8ba"))},c6d2:function(t,e,r){"use strict";var i=r("23e7"),n=r("c65b"),a=r("c430"),s=r("5e77"),o=r("1626"),u=r("dcc3"),h=r("e163"),c=r("d2bb"),l=r("d44e"),f=r("9112"),d=r("cb2d"),g=r("b622"),p=r("3f8c"),v=r("ae93"),y=s.PROPER,m=s.CONFIGURABLE,x=v.IteratorPrototype,b=v.BUGGY_SAFARI_ITERATORS,w=g("iterator"),S="keys",T="values",A="entries",C=function(){return this};t.exports=function(t,e,r,s,g,v,O){u(r,e,s);var E,P,M,N=function(t){if(t===g&&I)return I;if(!b&&t in _)return _[t];switch(t){case S:return function(){return new r(this,t)};case T:return function(){return new r(this,t)};case A:return function(){return new r(this,t)}}return function(){return new r(this)}},R=e+" Iterator",V=!1,_=t.prototype,k=_[w]||_["@@iterator"]||g&&_[g],I=!b&&k||N(g),L="Array"==e&&_.entries||k;if(L&&(E=h(L.call(new t)),E!==Object.prototype&&E.next&&(a||h(E)===x||(c?c(E,x):o(E[w])||d(E,w,C)),l(E,R,!0,!0),a&&(p[R]=C))),y&&g==T&&k&&k.name!==T&&(!a&&m?f(_,"name",T):(V=!0,I=function(){return n(k,this)})),g)if(P={values:N(T),keys:v?I:N(S),entries:N(A)},O)for(M in P)(b||V||!(M in _))&&d(_,M,P[M]);else i({target:e,proto:!0,forced:b||V},P);return a&&!O||_[w]===I||d(_,w,I,{name:g}),p[e]=I,P}},c8d2:function(t,e,r){var i=r("5e77").PROPER,n=r("d039"),a=r("5899"),s="​᠎";t.exports=function(t){return n((function(){return!!a[t]()||s[t]()!==s||i&&a[t].name!==t}))}},c973:function(t,e){function r(t,e,r,i,n,a,s){try{var o=t[a](s),u=o.value}catch(h){return void r(h)}o.done?e(u):Promise.resolve(u).then(i,n)}function i(t){return function(){var e=this,i=arguments;return new Promise((function(n,a){var s=t.apply(e,i);function o(t){r(s,n,a,o,u,"next",t)}function u(t){r(s,n,a,o,u,"throw",t)}o(void 0)}))}}t.exports=i,t.exports.__esModule=!0,t.exports["default"]=t.exports},c975:function(t,e,r){"use strict";var i=r("23e7"),n=r("e330"),a=r("4d64").indexOf,s=r("a640"),o=n([].indexOf),u=!!o&&1/o([1],1,-0)<0,h=s("indexOf");i({target:"Array",proto:!0,forced:u||!h},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return u?o(this,t,e)||0:a(this,t,e)}})},cc98:function(t,e,r){"use strict";var i=r("23e7"),n=r("c430"),a=r("4738").CONSTRUCTOR,s=r("d256"),o=r("d066"),u=r("1626"),h=r("cb2d"),c=s&&s.prototype;if(i({target:"Promise",proto:!0,forced:a,real:!0},{catch:function(t){return this.then(void 0,t)}}),!n&&u(s)){var l=o("Promise").prototype["catch"];c["catch"]!==l&&h(c,"catch",l,{unsafe:!0})}},cdf9:function(t,e,r){var i=r("825a"),n=r("861d"),a=r("f069");t.exports=function(t,e){if(i(t),n(e)&&e.constructor===t)return e;var r=a.f(t),s=r.resolve;return s(e),r.promise}},d01f:function(t,e,r){"use strict";r.d(e,"a",(function(){return S}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function n(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function a(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var i=t[r];if(i.type===S.CLOSE_PATH)e+="z";else if(i.type===S.HORIZ_LINE_TO)e+=(i.relative?"h":"H")+i.x;else if(i.type===S.VERT_LINE_TO)e+=(i.relative?"v":"V")+i.y;else if(i.type===S.MOVE_TO)e+=(i.relative?"m":"M")+i.x+" "+i.y;else if(i.type===S.LINE_TO)e+=(i.relative?"l":"L")+i.x+" "+i.y;else if(i.type===S.CURVE_TO)e+=(i.relative?"c":"C")+i.x1+" "+i.y1+" "+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===S.SMOOTH_CURVE_TO)e+=(i.relative?"s":"S")+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===S.QUAD_TO)e+=(i.relative?"q":"Q")+i.x1+" "+i.y1+" "+i.x+" "+i.y;else if(i.type===S.SMOOTH_QUAD_TO)e+=(i.relative?"t":"T")+i.x+" "+i.y;else{if(i.type!==S.ARC)throw new Error('Unexpected command type "'+i.type+'" at index '+r+".");e+=(i.relative?"a":"A")+i.rX+" "+i.rY+" "+i.xRot+" "+ +i.lArcFlag+" "+ +i.sweepFlag+" "+i.x+" "+i.y}}return e}function s(t,e){var r=t[0],i=t[1];return[r*Math.cos(e)-i*Math.sin(e),r*Math.sin(e)+i*Math.cos(e)]}function o(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var u=Math.PI;function h(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var i=t.rX,n=t.rY,a=t.x,o=t.y;i=Math.abs(t.rX),n=Math.abs(t.rY);var h=s([(e-a)/2,(r-o)/2],-t.xRot/180*u),c=h[0],l=h[1],f=Math.pow(c,2)/Math.pow(i,2)+Math.pow(l,2)/Math.pow(n,2);1<f&&(i*=Math.sqrt(f),n*=Math.sqrt(f)),t.rX=i,t.rY=n;var d=Math.pow(i,2)*Math.pow(l,2)+Math.pow(n,2)*Math.pow(c,2),g=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(i,2)*Math.pow(n,2)-d)/d)),p=i*l/n*g,v=-n*c/i*g,y=s([p,v],t.xRot/180*u);t.cX=y[0]+(e+a)/2,t.cY=y[1]+(r+o)/2,t.phi1=Math.atan2((l-v)/n,(c-p)/i),t.phi2=Math.atan2((-l-v)/n,(-c-p)/i),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*u),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*u),t.phi1*=180/u,t.phi2*=180/u}function c(t,e,r){o(t,e,r);var i=t*t+e*e-r*r;if(0>i)return[];if(0===i)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var n=Math.sqrt(i);return[[(t*r+e*n)/(t*t+e*e),(e*r-t*n)/(t*t+e*e)],[(t*r-e*n)/(t*t+e*e),(e*r+t*n)/(t*t+e*e)]]}var l,f=Math.PI/180;function d(t,e,r){return(1-r)*t+r*e}function g(t,e,r,i){return t+Math.cos(i/180*u)*e+Math.sin(i/180*u)*r}function p(t,e,r,i){var n=1e-6,a=e-t,s=r-e,o=3*a+3*(i-r)-6*s,u=6*(s-a),h=3*a;return Math.abs(o)<n?[-h/u]:function(t,e,r){void 0===r&&(r=1e-6);var i=t*t/4-e;if(i<-r)return[];if(i<=r)return[-t/2];var n=Math.sqrt(i);return[-t/2-n,-t/2+n]}(u/o,h/o,n)}function v(t,e,r,i,n){var a=1-n;return t*(a*a*a)+e*(3*a*a*n)+r*(3*a*n*n)+i*(n*n*n)}!function(t){function e(){return n((function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t}))}function r(){var t=NaN,e=NaN,r=NaN,i=NaN;return n((function(n,a,s){return n.type&S.SMOOTH_CURVE_TO&&(n.type=S.CURVE_TO,t=isNaN(t)?a:t,e=isNaN(e)?s:e,n.x1=n.relative?a-t:2*a-t,n.y1=n.relative?s-e:2*s-e),n.type&S.CURVE_TO?(t=n.relative?a+n.x2:n.x2,e=n.relative?s+n.y2:n.y2):(t=NaN,e=NaN),n.type&S.SMOOTH_QUAD_TO&&(n.type=S.QUAD_TO,r=isNaN(r)?a:r,i=isNaN(i)?s:i,n.x1=n.relative?a-r:2*a-r,n.y1=n.relative?s-i:2*s-i),n.type&S.QUAD_TO?(r=n.relative?a+n.x1:n.x1,i=n.relative?s+n.y1:n.y1):(r=NaN,i=NaN),n}))}function i(){var t=NaN,e=NaN;return n((function(r,i,n){if(r.type&S.SMOOTH_QUAD_TO&&(r.type=S.QUAD_TO,t=isNaN(t)?i:t,e=isNaN(e)?n:e,r.x1=r.relative?i-t:2*i-t,r.y1=r.relative?n-e:2*n-e),r.type&S.QUAD_TO){t=r.relative?i+r.x1:r.x1,e=r.relative?n+r.y1:r.y1;var a=r.x1,s=r.y1;r.type=S.CURVE_TO,r.x1=((r.relative?0:i)+2*a)/3,r.y1=((r.relative?0:n)+2*s)/3,r.x2=(r.x+2*a)/3,r.y2=(r.y+2*s)/3}else t=NaN,e=NaN;return r}))}function n(t){var e=0,r=0,i=NaN,n=NaN;return function(a){if(isNaN(i)&&!(a.type&S.MOVE_TO))throw new Error("path must start with moveto");var s=t(a,e,r,i,n);return a.type&S.CLOSE_PATH&&(e=i,r=n),void 0!==a.x&&(e=a.relative?e+a.x:a.x),void 0!==a.y&&(r=a.relative?r+a.y:a.y),a.type&S.MOVE_TO&&(i=e,n=r),s}}function a(t,e,r,i,a,s){return o(t,e,r,i,a,s),n((function(n,o,u,h){var c=n.x1,l=n.x2,f=n.relative&&!isNaN(h),d=void 0!==n.x?n.x:f?0:o,g=void 0!==n.y?n.y:f?0:u;function p(t){return t*t}n.type&S.HORIZ_LINE_TO&&0!==e&&(n.type=S.LINE_TO,n.y=n.relative?0:u),n.type&S.VERT_LINE_TO&&0!==r&&(n.type=S.LINE_TO,n.x=n.relative?0:o),void 0!==n.x&&(n.x=n.x*t+g*r+(f?0:a)),void 0!==n.y&&(n.y=d*e+n.y*i+(f?0:s)),void 0!==n.x1&&(n.x1=n.x1*t+n.y1*r+(f?0:a)),void 0!==n.y1&&(n.y1=c*e+n.y1*i+(f?0:s)),void 0!==n.x2&&(n.x2=n.x2*t+n.y2*r+(f?0:a)),void 0!==n.y2&&(n.y2=l*e+n.y2*i+(f?0:s));var v=t*i-e*r;if(void 0!==n.xRot&&(1!==t||0!==e||0!==r||1!==i))if(0===v)delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag,n.type=S.LINE_TO;else{var y=n.xRot*Math.PI/180,m=Math.sin(y),x=Math.cos(y),b=1/p(n.rX),w=1/p(n.rY),T=p(x)*b+p(m)*w,A=2*m*x*(b-w),C=p(m)*b+p(x)*w,O=T*i*i-A*e*i+C*e*e,E=A*(t*i+e*r)-2*(T*r*i+C*t*e),P=T*r*r-A*t*r+C*t*t,M=(Math.atan2(E,O-P)+Math.PI)%Math.PI/2,N=Math.sin(M),R=Math.cos(M);n.rX=Math.abs(v)/Math.sqrt(O*p(R)+E*N*R+P*p(N)),n.rY=Math.abs(v)/Math.sqrt(O*p(N)-E*N*R+P*p(R)),n.xRot=180*M/Math.PI}return void 0!==n.sweepFlag&&0>v&&(n.sweepFlag=+!n.sweepFlag),n}))}function u(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),o(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return n((function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t}))},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),n((function(i,n,a,s,o){if(isNaN(s)&&!(i.type&S.MOVE_TO))throw new Error("path must start with moveto");return e&&i.type&S.HORIZ_LINE_TO&&(i.type=S.LINE_TO,i.y=i.relative?0:a),r&&i.type&S.VERT_LINE_TO&&(i.type=S.LINE_TO,i.x=i.relative?0:n),t&&i.type&S.CLOSE_PATH&&(i.type=S.LINE_TO,i.x=i.relative?s-n:s,i.y=i.relative?o-a:o),i.type&S.ARC&&(0===i.rX||0===i.rY)&&(i.type=S.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i}))},t.NORMALIZE_ST=r,t.QT_TO_C=i,t.INFO=n,t.SANITIZE=function(t){void 0===t&&(t=0),o(t);var e=NaN,r=NaN,i=NaN,a=NaN;return n((function(n,s,o,u,h){var c=Math.abs,l=!1,f=0,d=0;if(n.type&S.SMOOTH_CURVE_TO&&(f=isNaN(e)?0:s-e,d=isNaN(r)?0:o-r),n.type&(S.CURVE_TO|S.SMOOTH_CURVE_TO)?(e=n.relative?s+n.x2:n.x2,r=n.relative?o+n.y2:n.y2):(e=NaN,r=NaN),n.type&S.SMOOTH_QUAD_TO?(i=isNaN(i)?s:2*s-i,a=isNaN(a)?o:2*o-a):n.type&S.QUAD_TO?(i=n.relative?s+n.x1:n.x1,a=n.relative?o+n.y1:n.y2):(i=NaN,a=NaN),n.type&S.LINE_COMMANDS||n.type&S.ARC&&(0===n.rX||0===n.rY||!n.lArcFlag)||n.type&S.CURVE_TO||n.type&S.SMOOTH_CURVE_TO||n.type&S.QUAD_TO||n.type&S.SMOOTH_QUAD_TO){var g=void 0===n.x?0:n.relative?n.x:n.x-s,p=void 0===n.y?0:n.relative?n.y:n.y-o;f=isNaN(i)?void 0===n.x1?f:n.relative?n.x:n.x1-s:i-s,d=isNaN(a)?void 0===n.y1?d:n.relative?n.y:n.y1-o:a-o;var v=void 0===n.x2?0:n.relative?n.x:n.x2-s,y=void 0===n.y2?0:n.relative?n.y:n.y2-o;c(g)<=t&&c(p)<=t&&c(f)<=t&&c(d)<=t&&c(v)<=t&&c(y)<=t&&(l=!0)}return n.type&S.CLOSE_PATH&&c(s-u)<=t&&c(o-h)<=t&&(l=!0),l?[]:n}))},t.MATRIX=a,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),o(t,e,r);var i=Math.sin(t),n=Math.cos(t);return a(n,i,-i,n,e-e*n+r*i,r-e*i-r*n)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),o(t,e),a(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),o(t,e),a(t,0,0,e,0,0)},t.SKEW_X=function(t){return o(t),a(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return o(t),a(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),o(t),a(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),o(t),a(1,0,0,-1,0,t)},t.A_TO_C=function(){return n((function(t,e,r){return S.ARC===t.type?function(t,e,r){var i,n,a,o;t.cX||h(t,e,r);for(var u=Math.min(t.phi1,t.phi2),c=Math.max(t.phi1,t.phi2)-u,l=Math.ceil(c/90),g=new Array(l),p=e,v=r,y=0;y<l;y++){var m=d(t.phi1,t.phi2,y/l),x=d(t.phi1,t.phi2,(y+1)/l),b=x-m,w=4/3*Math.tan(b*f/4),T=[Math.cos(m*f)-w*Math.sin(m*f),Math.sin(m*f)+w*Math.cos(m*f)],A=T[0],C=T[1],O=[Math.cos(x*f),Math.sin(x*f)],E=O[0],P=O[1],M=[E+w*Math.sin(x*f),P-w*Math.cos(x*f)],N=M[0],R=M[1];g[y]={relative:t.relative,type:S.CURVE_TO};var V=function(e,r){var i=s([e*t.rX,r*t.rY],t.xRot),n=i[0],a=i[1];return[t.cX+n,t.cY+a]};i=V(A,C),g[y].x1=i[0],g[y].y1=i[1],n=V(N,R),g[y].x2=n[0],g[y].y2=n[1],a=V(E,P),g[y].x=a[0],g[y].y=a[1],t.relative&&(g[y].x1-=p,g[y].y1-=v,g[y].x2-=p,g[y].y2-=v,g[y].x-=p,g[y].y-=v),p=(o=[g[y].x,g[y].y])[0],v=o[1]}return g}(t,t.relative?0:e,t.relative?0:r):t}))},t.ANNOTATE_ARCS=function(){return n((function(t,e,r){return t.relative&&(e=0,r=0),S.ARC===t.type&&h(t,e,r),t}))},t.CLONE=u,t.CALCULATE_BOUNDS=function(){var t=function(t){var e={};for(var r in t)e[r]=t[r];return e},a=e(),s=i(),o=r(),u=n((function(e,r,i){var n=o(s(a(t(e))));function l(t){t>u.maxX&&(u.maxX=t),t<u.minX&&(u.minX=t)}function f(t){t>u.maxY&&(u.maxY=t),t<u.minY&&(u.minY=t)}if(n.type&S.DRAWING_COMMANDS&&(l(r),f(i)),n.type&S.HORIZ_LINE_TO&&l(n.x),n.type&S.VERT_LINE_TO&&f(n.y),n.type&S.LINE_TO&&(l(n.x),f(n.y)),n.type&S.CURVE_TO){l(n.x),f(n.y);for(var d=0,y=p(r,n.x1,n.x2,n.x);d<y.length;d++)0<(k=y[d])&&1>k&&l(v(r,n.x1,n.x2,n.x,k));for(var m=0,x=p(i,n.y1,n.y2,n.y);m<x.length;m++)0<(k=x[m])&&1>k&&f(v(i,n.y1,n.y2,n.y,k))}if(n.type&S.ARC){l(n.x),f(n.y),h(n,r,i);for(var b=n.xRot/180*Math.PI,w=Math.cos(b)*n.rX,T=Math.sin(b)*n.rX,A=-Math.sin(b)*n.rY,C=Math.cos(b)*n.rY,O=n.phi1<n.phi2?[n.phi1,n.phi2]:-180>n.phi2?[n.phi2+360,n.phi1+360]:[n.phi2,n.phi1],E=O[0],P=O[1],M=function(t){var e=t[0],r=t[1],i=180*Math.atan2(r,e)/Math.PI;return i<E?i+360:i},N=0,R=c(A,-w,0).map(M);N<R.length;N++)(k=R[N])>E&&k<P&&l(g(n.cX,w,A,k));for(var V=0,_=c(C,-T,0).map(M);V<_.length;V++){var k;(k=_[V])>E&&k<P&&f(g(n.cY,T,C,k))}}return e}));return u.minX=1/0,u.maxX=-1/0,u.minY=1/0,u.maxY=-1/0,u}}(l||(l={}));var y,m=function(){function t(){}return t.prototype.round=function(t){return this.transform(l.ROUND(t))},t.prototype.toAbs=function(){return this.transform(l.TO_ABS())},t.prototype.toRel=function(){return this.transform(l.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(l.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(l.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(l.QT_TO_C())},t.prototype.aToC=function(){return this.transform(l.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(l.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(l.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(l.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(l.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,i,n,a){return this.transform(l.MATRIX(t,e,r,i,n,a))},t.prototype.skewX=function(t){return this.transform(l.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(l.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(l.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(l.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(l.ANNOTATE_ARCS())},t}(),x=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},b=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},w=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return n(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var i=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},n=0;n<t.length;n++){var a=t[n],s=!(this.curCommandType!==S.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=b(a)&&("0"===this.curNumber&&"0"===a||s);if(!b(a)||o)if("e"!==a&&"E"!==a)if("-"!==a&&"+"!==a||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==a||this.curNumberHasExp||this.curNumberHasDecimal||s){if(this.curNumber&&-1!==this.curCommandType){var u=Number(this.curNumber);if(isNaN(u))throw new SyntaxError("Invalid number ending at "+n);if(this.curCommandType===S.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>u)throw new SyntaxError('Expected positive number, got "'+u+'" at index "'+n+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+n+'"');this.curArgs.push(u),this.curArgs.length===T[this.curCommandType]&&(S.HORIZ_LINE_TO===this.curCommandType?i({type:S.HORIZ_LINE_TO,relative:this.curCommandRelative,x:u}):S.VERT_LINE_TO===this.curCommandType?i({type:S.VERT_LINE_TO,relative:this.curCommandRelative,y:u}):this.curCommandType===S.MOVE_TO||this.curCommandType===S.LINE_TO||this.curCommandType===S.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),S.MOVE_TO===this.curCommandType&&(this.curCommandType=S.LINE_TO)):this.curCommandType===S.CURVE_TO?i({type:S.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===S.SMOOTH_CURVE_TO?i({type:S.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===S.QUAD_TO?i({type:S.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===S.ARC&&i({type:S.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!x(a))if(","===a&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==a&&"-"!==a&&"."!==a)if(o)this.curNumber=a,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+n+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+a+'" at index '+n+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==a&&"Z"!==a)if("h"===a||"H"===a)this.curCommandType=S.HORIZ_LINE_TO,this.curCommandRelative="h"===a;else if("v"===a||"V"===a)this.curCommandType=S.VERT_LINE_TO,this.curCommandRelative="v"===a;else if("m"===a||"M"===a)this.curCommandType=S.MOVE_TO,this.curCommandRelative="m"===a;else if("l"===a||"L"===a)this.curCommandType=S.LINE_TO,this.curCommandRelative="l"===a;else if("c"===a||"C"===a)this.curCommandType=S.CURVE_TO,this.curCommandRelative="c"===a;else if("s"===a||"S"===a)this.curCommandType=S.SMOOTH_CURVE_TO,this.curCommandRelative="s"===a;else if("q"===a||"Q"===a)this.curCommandType=S.QUAD_TO,this.curCommandRelative="q"===a;else if("t"===a||"T"===a)this.curCommandType=S.SMOOTH_QUAD_TO,this.curCommandRelative="t"===a;else{if("a"!==a&&"A"!==a)throw new SyntaxError('Unexpected character "'+a+'" at index '+n+".");this.curCommandType=S.ARC,this.curCommandRelative="a"===a}else e.push({type:S.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=a,this.curNumberHasDecimal="."===a}else this.curNumber+=a,this.curNumberHasDecimal=!0;else this.curNumber+=a;else this.curNumber+=a,this.curNumberHasExp=!0;else this.curNumber+=a,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var i=0,n=Object.getPrototypeOf(this).parse.call(this,e);i<n.length;i++){var a=n[i],s=t(a);Array.isArray(s)?r.push.apply(r,s):r.push(s)}return r}}})},e}(m),S=function(t){function e(r){var i=t.call(this)||this;return i.commands="string"==typeof r?e.parse(r):r,i}return n(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=l.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,i=this.commands;r<i.length;r++){var n=t(i[r]);Array.isArray(n)?e.push.apply(e,n):e.push(n)}return this.commands=e,this},e.encode=function(t){return a(t)},e.parse=function(t){var e=new w,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(m),T=((y={})[S.MOVE_TO]=2,y[S.LINE_TO]=2,y[S.HORIZ_LINE_TO]=1,y[S.VERT_LINE_TO]=1,y[S.CLOSE_PATH]=0,y[S.QUAD_TO]=4,y[S.SMOOTH_QUAD_TO]=2,y[S.CURVE_TO]=6,y[S.SMOOTH_CURVE_TO]=4,y[S.ARC]=7,y)},d256:function(t,e,r){var i=r("da84");t.exports=i.Promise},d44e:function(t,e,r){var i=r("9bf2").f,n=r("1a2d"),a=r("b622"),s=a("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!n(t,s)&&i(t,s,{configurable:!0,value:e})}},d4c3:function(t,e,r){var i=r("342f"),n=r("da84");t.exports=/ipad|iphone|ipod/i.test(i)&&void 0!==n.Pebble},d6d6:function(t,e){var r=TypeError;t.exports=function(t,e){if(t<e)throw r("Not enough arguments");return t}},d784:function(t,e,r){"use strict";r("ac1f");var i=r("e330"),n=r("cb2d"),a=r("9263"),s=r("d039"),o=r("b622"),u=r("9112"),h=o("species"),c=RegExp.prototype;t.exports=function(t,e,r,l){var f=o(t),d=!s((function(){var e={};return e[f]=function(){return 7},7!=""[t](e)})),g=d&&!s((function(){var e=!1,r=/a/;return"split"===t&&(r={},r.constructor={},r.constructor[h]=function(){return r},r.flags="",r[f]=/./[f]),r.exec=function(){return e=!0,null},r[f](""),!e}));if(!d||!g||r){var p=i(/./[f]),v=e(f,""[t],(function(t,e,r,n,s){var o=i(t),u=e.exec;return u===a||u===c.exec?d&&!s?{done:!0,value:p(e,r,n)}:{done:!0,value:o(r,e,n)}:{done:!1}}));n(String.prototype,t,v[0]),n(c,f,v[1])}l&&u(c[f],"sham",!0)}},dcc3:function(t,e,r){"use strict";var i=r("ae93").IteratorPrototype,n=r("7c73"),a=r("5c6c"),s=r("d44e"),o=r("3f8c"),u=function(){return this};t.exports=function(t,e,r,h){var c=e+" Iterator";return t.prototype=n(i,{next:a(+!h,r)}),s(t,c,!1,!0),o[c]=u,t}},ddb0:function(t,e,r){var i=r("da84"),n=r("fdbc"),a=r("785a"),s=r("e260"),o=r("9112"),u=r("b622"),h=u("iterator"),c=u("toStringTag"),l=s.values,f=function(t,e){if(t){if(t[h]!==l)try{o(t,h,l)}catch(i){t[h]=l}if(t[c]||o(t,c,e),n[e])for(var r in s)if(t[r]!==s[r])try{o(t,r,s[r])}catch(i){t[r]=s[r]}}};for(var d in n)f(i[d]&&i[d].prototype,d);f(a,"DOMTokenList")},df75:function(t,e,r){var i=r("ca84"),n=r("7839");t.exports=Object.keys||function(t){return i(t,n)}},e260:function(t,e,r){"use strict";var i=r("fc6a"),n=r("44d2"),a=r("3f8c"),s=r("69f3"),o=r("9bf2").f,u=r("c6d2"),h=r("c430"),c=r("83ab"),l="Array Iterator",f=s.set,d=s.getterFor(l);t.exports=u(Array,"Array",(function(t,e){f(this,{type:l,target:i(t),index:0,kind:e})}),(function(){var t=d(this),e=t.target,r=t.kind,i=t.index++;return!e||i>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:i,done:!1}:"values"==r?{value:e[i],done:!1}:{value:[i,e[i]],done:!1}}),"values");var g=a.Arguments=a.Array;if(n("keys"),n("values"),n("entries"),!h&&c&&"values"!==g.name)try{o(g,"name",{value:"values"})}catch(p){}},e667:function(t,e){t.exports=function(t){try{return{error:!1,value:t()}}catch(e){return{error:!0,value:e}}}},e6cf:function(t,e,r){r("5e7e"),r("14e5"),r("cc98"),r("3529"),r("f22b"),r("7149")},e8b5:function(t,e,r){var i=r("c6b6");t.exports=Array.isArray||function(t){return"Array"==i(t)}},e95a:function(t,e,r){var i=r("b622"),n=r("3f8c"),a=i("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||s[a]===t)}},f069:function(t,e,r){"use strict";var i=r("59ed"),n=TypeError,a=function(t){var e,r;this.promise=new t((function(t,i){if(void 0!==e||void 0!==r)throw n("Bad Promise constructor");e=t,r=i})),this.resolve=i(e),this.reject=i(r)};t.exports.f=function(t){return new a(t)}},f22b:function(t,e,r){"use strict";var i=r("23e7"),n=r("c65b"),a=r("f069"),s=r("4738").CONSTRUCTOR;i({target:"Promise",stat:!0,forced:s},{reject:function(t){var e=a.f(this);return n(e.reject,void 0,t),e.promise}})},f36a:function(t,e,r){var i=r("e330");t.exports=i([].slice)},fce3:function(t,e,r){var i=r("d039"),n=r("da84"),a=n.RegExp;t.exports=i((function(){var t=a(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)}))},fdbc:function(t,e){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}}}]);