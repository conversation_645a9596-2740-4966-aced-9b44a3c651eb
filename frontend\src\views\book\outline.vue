<template>
  <div class="outline-creator" :class="{'fullscreen': isFullscreen}" v-loading="isLoading" element-loading-text="加载中..." element-loading-background="rgba(0, 0, 0, 0.7)">
    <!-- 头部导航栏 -->
    <div class="outline-header">
      <div class="left-section">
        <h1>{{ bookTitle }}</h1>
        <!-- 优化大纲选择器 -->
        <div class="outline-selector-wrapper">
          <el-popover
            placement="bottom-start"
            width="360"
            trigger="click"
            popper-class="outline-history-popover"
          >
            <template #reference>
              <div class="outline-selector">
                <div class="current-outline-info">
                  <span class="current-outline-title">{{ outlineData.volumeTitle || '未命名大纲' }}</span>
                  <el-tag size="small" effect="plain" class="current-tag" v-if="outlineData.id">
                    已保存
                  </el-tag>
                  <el-tag size="small" type="danger" effect="plain" class="current-tag" v-else>
                    未保存
                  </el-tag>
                </div>
                <el-icon class="selector-icon"><arrow-down /></el-icon>
              </div>
            </template>
            
            <div class="outline-history-panel">
              <div class="panel-header">
                <span class="panel-title">大纲历史</span>
                <el-button type="primary" size="small" plain @click="createNewOutline">
                  <el-icon><Plus /></el-icon> 新建大纲
                </el-button>
              </div>
              
              <div class="history-search">
                <el-input 
                  v-model="outlineSearchQuery" 
                  placeholder="搜索大纲..." 
                  prefix-icon="Search"
                  clearable
                />
              </div>
              
              <div class="history-list-container">
                <div v-if="filteredOutlines.length === 0" class="empty-history">
                  <el-empty description="暂无大纲历史" :image-size="60" />
                </div>
                
                <div v-else class="history-list">
                  <div 
                    v-for="outline in filteredOutlines" 
                    :key="outline.id" 
                    class="history-item"
                    :class="{'active': outlineData.id === outline.id}"
                    @click="loadOutlineData(outline.id)"
                  >
                    <div class="item-content">
                      <div class="item-title">{{ outline.volumeTitle || '未命名大纲' }}</div>
                      <div class="item-meta">
                        <div class="item-timestamp">
                          <el-icon><Calendar /></el-icon>
                          <span>{{ formatDate(outline.timestamp) }}</span>
                        </div>
                        <div class="item-info">
                          <el-tag size="small" effect="plain">
                            {{ outline.dynamicSegments?.length || 0 }} 段
                          </el-tag>
                        </div>
                      </div>
                    </div>
                    <div class="item-actions">
                      <el-button 
                        type="danger" 
                        size="small" 
                        circle 
                        plain
                        @click.stop="confirmDeleteOutline(outline.id)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-popover>
        </div>
      </div>
      <div class="right-section">
        <el-button-group class="action-buttons">
          <el-button type="success" @click="saveOutline" :loading="saving" :icon="Check">
            保存大纲
          </el-button>
          <el-button type="primary" @click="createNewOutline" :icon="Plus">
            新建大纲
          </el-button>
          <el-button @click="exportOutline" :icon="Download">
            导出
          </el-button>
          <el-button @click="importOutline" :icon="Upload">
            导入
          </el-button>
          <el-button type="danger" @click="confirmDeleteOutline" :icon="Delete" v-if="outlineData.id">
            删除
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主体内容区 -->
    <div class="outline-main-content">
      <!-- 专业模式 -->
      <div class="outline-content-layout">
        <!-- 左侧导航栏 -->
        <div class="outline-nav-sidebar">
          <div class="sidebar-section">
            <h3 class="sidebar-title">卷级设置</h3>
            <ul class="sidebar-menu">
              <li :class="{ active: activeExpertSection === 'basic' }" @click="setActiveExpertSection('basic')">
                <el-icon><Document /></el-icon> 基本信息
              </li>
              <li :class="{ active: activeExpertSection === 'structure' }" @click="setActiveExpertSection('structure')">
                <el-icon><Connection /></el-icon> 结构设计
              </li>
            </ul>
          </div>
          
          <div class="sidebar-section">
            <h3 class="sidebar-title">伏笔系统</h3>
            <ul class="sidebar-menu">
              <li :class="{ active: activeExpertSection === 'foreshadow' }" @click="setActiveExpertSection('foreshadow')">
                <el-icon><Link /></el-icon> 伏笔管理
              </li>
            </ul>
          </div>
          
          <div class="sidebar-section">
            <h3 class="sidebar-title">情绪曲线</h3>
            <ul class="sidebar-menu">
              <li :class="{ active: activeExpertSection === 'emotion' }" @click="setActiveExpertSection('emotion')">
                <el-icon><TrendCharts /></el-icon> 情绪分析
              </li>
            </ul>
          </div>
          
          <div class="sidebar-section">
            <h3 class="sidebar-title">输出</h3>
            <ul class="sidebar-menu">
              <li :class="{ active: activeExpertSection === 'preview' }" @click="setActiveExpertSection('preview')">
                <el-icon><View /></el-icon> 预览大纲
              </li>
            </ul>
          </div>
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="outline-content-area">
          <!-- 删除expert-content-scrollable的嵌套滚动容器 -->
          
          <!-- 基本信息 -->
          <div v-if="activeExpertSection === 'basic'" class="expert-section">
            <div class="section-header">
              <h3>基本信息</h3>
            </div>
            <el-card class="box-card basic-info-card" shadow="never">
            <el-form label-position="top">
              <el-form-item label="卷标题">
                <el-input v-model="outlineData.volumeTitle" placeholder="例：帝都的阴谋" />
              </el-form-item>
                
              <el-form-item label="卷主题">
                <el-input v-model="outlineData.volumeTheme" placeholder="例：信任与背叛" />
              </el-form-item>
              
              <el-form-item label="核心冲突">
                  <el-input type="textarea" v-model="outlineData.coreConflict" :rows="3" placeholder="描述本卷的核心矛盾和挑战" />
              </el-form-item>
              
              <el-form-item label="卷在整体故事中的作用">
                  <el-select v-model="outlineData.volumeFunction" placeholder="选择卷的功能定位" style="width: 100%;">
                  <el-option label="开篇引入" value="introduction" />
                  <el-option label="冲突升级" value="escalation" />
                  <el-option label="高潮转折" value="climax" />
                  <el-option label="结局收束" value="resolution" />
                  <el-option label="过渡铺垫" value="transition" />
                </el-select>
              </el-form-item>
            </el-form>
            </el-card>
          </div>
          
          <!-- 结构设计 -->
          <div v-else-if="activeExpertSection === 'structure'" class="expert-section structure-section">
            <!-- 结构设计标题和添加桥段按钮同一行 -->
            <div class="section-header">
              <h3>结构设计</h3>
              <el-button type="primary" @click="addDynamicSegment()">
                <el-icon><Plus /></el-icon> 添加桥段
              </el-button>
            </div>
            
            <!-- 动态桥段系统UI -->
            <div class="dynamic-bridge-system">
              <div class="dynamic-segments-container">
                <!-- 空状态 -->
                <div v-if="outlineData.dynamicSegments.length === 0" class="empty-segments">
                  <el-empty description="暂无桥段，请点击'添加桥段'开始创建">
                    <el-button type="primary" @click="addDynamicSegment()">添加第一个桥段</el-button>
                  </el-empty>
                </div>
                
                <!-- 桥段列表 - 使用标签页形式展示 -->
                <div v-else class="dynamic-segments-tabs">
                  <!-- 标签页组件 -->
                  <div class="tabs-header-fixed">
                    <el-tabs v-model="activeSegmentTab" class="segment-tabs" closable @tab-remove="removeDynamicSegment">
                      <!-- 为每个桥段创建标签页 -->
                      <el-tab-pane 
                        v-for="(segment, index) in outlineData.dynamicSegments" 
                        :key="segment.id"
                        :label="segment.title || '未命名桥段'"
                        :name="segment.id"
                      >
                        <template #label>
                          <div class="segment-tab-label" :class="{
                            'segment-type-normal': segment.type === 'normal',
                            'segment-type-pivot': segment.type === 'pivot',
                            'segment-type-climax': segment.type === 'climax'
                          }">
                            <span class="segment-tab-index">{{ index + 1 }}</span>
                            <span class="segment-title">{{ segment.title || '未命名桥段' }}</span>
                            <span class="segment-type-badge">{{ getBridgeTypeLabel(segment.type) }}</span>
                          </div>
                        </template>
                      </el-tab-pane>
                    </el-tabs>
                    
                  </div>
                  
                  <!-- 桥段内容 -->
                  <div class="segment-contents-container">
                    <div v-for="(segment, index) in outlineData.dynamicSegments" 
                        :key="segment.id" 
                        v-show="activeSegmentTab === segment.id"
                        class="segment-content">

                      
                      <div class="segment-main-info">
                        <el-form>
                          <div class="segment-row">
                            <el-form-item label="桥段类型">
                              <el-select v-model="segment.type" placeholder="选择桥段类型">
                                <el-option 
                                  v-for="type in bridgeTypes"
                                  :key="type.value"
                                  :label="type.label"
                                  :value="type.value"
                                />
                              </el-select>
                            </el-form-item>
                          </div>
                          
                          <div class="segment-row">
                            <el-form-item label="桥段标题">
                              <el-input 
                                v-model="segment.title" 
                                placeholder="桥段标题" 
                              />
                            </el-form-item>
                          </div>
                          
                          <div class="segment-row">
                            <el-form-item label="桥段描述">
                              <el-input
                                type="textarea"
                                v-model="segment.description"
                                :rows="2"
                                placeholder="描述这个桥段的主要内容和作用"
                              />
                            </el-form-item>
                          </div>
                          
                          <div class="segment-row">
                            <el-form-item label="角色状态">
                              <el-input
                                type="textarea"
                                v-model="segment.characterStatus"
                                :rows="2"
                                placeholder="描述角色在这个阶段的状态和变化"
                              />
                            </el-form-item>
                          </div>
                          
                          
                          
                          <!-- 直接显示详细信息，不使用折叠面板 -->
                          <div class="segment-row">
                            <el-form-item label="剧情目标">
                              <el-input
                                type="textarea"
                                v-model="segment.goal"
                                :rows="2"
                                placeholder="本桥段想要达成的目标"
                              />
                            </el-form-item>
                          </div>
                              
                          <div class="segment-row">
                            <el-form-item label="剧情冲突">
                              <el-input
                                type="textarea"
                                v-model="segment.conflict"
                                :rows="2"
                                placeholder="主要矛盾点是什么？"
                              />
                            </el-form-item>
                          </div>
                              
                          <div class="segment-row">
                            <el-form-item label="剧情结果">
                              <el-input
                                type="textarea"
                                v-model="segment.result"
                                :rows="2"
                                placeholder="本桥段最终达成的结果"
                              />
                            </el-form-item>
                          </div>
                              
                          <div class="segment-row">
                            <el-form-item label="输入状态">
                              <el-input
                                type="textarea"
                                v-model="segment.input"
                                :rows="2"
                                placeholder="本桥段的起始状态和前置条件"
                              />
                            </el-form-item>
                          </div>
                              
                          <div class="segment-row">
                            <el-form-item label="输出状态">
                              <el-input
                                type="textarea"
                                v-model="segment.output"
                                :rows="2"
                                placeholder="本桥段结束后的状态和成果"
                              />
                            </el-form-item>
                          </div>
                              
                          <div class="segment-row">
                            <el-form-item label="伏笔/铺垫">
                              <el-input
                                type="textarea"
                                v-model="segment.foreshadowing"
                                :rows="2"
                                placeholder="为后续埋下的伏笔"
                              />
                            </el-form-item>
                          </div>
                              
                          <div class="segment-row">
                            <el-form-item label="节奏设计">
                              <el-select v-model="segment.rhythm" placeholder="选择节奏类型">
                                <el-option label="舒缓" value="slow" />
                                <el-option label="平稳" value="steady" />
                                <el-option label="紧凑" value="tight" />
                                <el-option label="急促" value="fast" />
                                <el-option label="高潮" value="climax" />
                              </el-select>
                              <div class="form-helper-text">节奏设计影响读者阅读时的紧张程度</div>
                            </el-form-item>
                          </div>
                          <div class="segment-compact-row">
                            <el-form-item label="情绪基调">
                              <el-slider 
                                v-model="segment.emotionValue" 
                                :min="0" 
                                :max="100"
                                :marks="{0:'绝望', 30:'低落', 50:'中性', 75:'振奋', 100:'狂喜'}"
                              ></el-slider>
                            </el-form-item>
                            <el-form-item label="锁定程度">
                              <el-rate v-model="segment.lockLevel" :max="3"></el-rate>
                            </el-form-item>
                          </div>
                        </el-form>
                      </div>
                      
                      <!-- 子桥段 -->
                      <el-divider content-position="center">子桥段</el-divider>
                      
                      <div class="sub-segments-section">
                        <div class="segments-header">
                          <h4>子桥段列表</h4>
                          <el-button type="primary" size="small" @click="addDynamicSubSegment(segment.id)">
                            <el-icon><Plus /></el-icon> 添加子桥段
                          </el-button>
                        </div>
                        
                        <!-- 子桥段列表 -->
                        <TransitionGroup name="list" tag="div" class="sub-segments-list">
                          <div 
                            v-for="(subSegment, subIndex) in segment.subSegments" 
                            :key="subSegment.id"
                            class="sub-segment-card" 
                          >
                            <div class="segment-card-header">
                              <span class="segment-index">{{ subIndex + 1 }}</span>
                              <div class="segment-actions">
                                <el-button 
                                  type="danger" 
                                  size="small" 
                                  text 
                                  @click="removeDynamicSubSegment(segment.id, subSegment.id)"
                                >
                                  <el-icon><Delete /></el-icon>
                                </el-button>
                              </div>
                            </div>
                            
                            <el-form>
                              <el-form-item label="子桥段标题">
                                <el-input v-model="subSegment.title" placeholder="子桥段标题"></el-input>
                              </el-form-item>
                              
                              <div class="segment-compact-row">
                                <el-form-item label="情绪">
                                  <el-slider 
                                    v-model="subSegment.emotionValue" 
                                    :min="0" 
                                    :max="100"
                                    :marks="{0:'绝望', 30:'低落', 50:'中性', 75:'振奋', 100:'狂喜'}"
                                  ></el-slider>
                                </el-form-item>
                                <el-form-item label="锁定度">
                                  <el-rate v-model="subSegment.lockLevel" :max="3"></el-rate>
                                </el-form-item>
                              </div>
                              
                              <!-- 同样对子桥段也直接显示详细信息 -->
                              <el-form-item label="剧情目标">
                                <el-input
                                  type="textarea"
                                  v-model="subSegment.goal"
                                  :rows="2"
                                  placeholder="本桥段想要达成的目标"
                                />
                              </el-form-item>
                              
                              <el-form-item label="剧情冲突">
                                <el-input
                                  type="textarea"
                                  v-model="subSegment.conflict"
                                  :rows="2"
                                  placeholder="主要矛盾点是什么？"
                                />
                              </el-form-item>
                              
                              <el-form-item label="剧情结果">
                                <el-input
                                  type="textarea"
                                  v-model="subSegment.result"
                                  :rows="2"
                                  placeholder="本桥段最终达成的结果"
                                />
                              </el-form-item>
                              
                              <el-form-item label="输入状态">
                                <el-input
                                  type="textarea"
                                  v-model="subSegment.input"
                                  :rows="2"
                                  placeholder="本桥段的起始状态和前置条件"
                                />
                              </el-form-item>
                              
                              <el-form-item label="输出状态">
                                <el-input
                                  type="textarea"
                                  v-model="subSegment.output"
                                  :rows="2"
                                  placeholder="本桥段结束后的状态和成果"
                                />
                              </el-form-item>
                              
                              <div class="segment-row">
                                <el-form-item label="伏笔/铺垫">
                                  <el-input
                                    type="textarea"
                                    v-model="subSegment.foreshadowing"
                                    :rows="2"
                                    placeholder="为后续埋下的伏笔"
                                  />
                                </el-form-item>
                              </div>
                              
                              <!-- 显示与此桥段关联的伏笔 -->
                              <div class="segment-row" v-if="getRelatedForeshadows(`DYN${index}`).length > 0">
                                <el-form-item label="关联伏笔">
                                  <div class="related-foreshadows">
                                    <!-- 植入的伏笔 -->
                                    <div class="foreshadow-group" v-if="getPlantedForeshadows(`DYN${index}`).length > 0">
                                      <div class="foreshadow-group-title">
                                        <el-icon><Top /></el-icon> 植入的伏笔:
                                      </div>
                                      <el-space wrap>
                                        <el-tag 
                                          v-for="fs in getPlantedForeshadows(`DYN${index}`)" 
                                          :key="`plant-${fs.id}`"
                                          size="small"
                                          class="foreshadow-tag plant-tag"
                                          @click="scrollToForeshadow(fs.id)"
                                        >
                                          <el-icon><TopRight /></el-icon>
                                          {{ fs.title || '未命名伏笔' }}
                                        </el-tag>
                                      </el-space>
                                    </div>
                                    
                                    <!-- 回收的伏笔 -->
                                    <div class="foreshadow-group" v-if="getPayoffForeshadows(`DYN${index}`).length > 0">
                                      <div class="foreshadow-group-title">
                                        <el-icon><Bottom /></el-icon> 回收的伏笔:
                                      </div>
                                      <el-space wrap>
                                        <el-tag 
                                          v-for="fs in getPayoffForeshadows(`DYN${index}`)" 
                                          :key="`payoff-${fs.id}`"
                                          size="small"
                                          type="success"
                                          class="foreshadow-tag payoff-tag"
                                          @click="scrollToForeshadow(fs.id)"
                                        >
                                          <el-icon><Check /></el-icon>
                                          {{ fs.title || '未命名伏笔' }}
                                        </el-tag>
                                      </el-space>
                                    </div>
                                  </div>
                                </el-form-item>
                              </div>
                              
                              <el-form-item label="节奏设计">
                                <el-select v-model="subSegment.rhythm" placeholder="选择节奏类型">
                                  <el-option label="舒缓" value="slow" />
                                  <el-option label="平稳" value="steady" />
                                  <el-option label="紧凑" value="tight" />
                                  <el-option label="急促" value="fast" />
                                  <el-option label="高潮" value="climax" />
                                </el-select>
                                <div class="form-helper-text">节奏设计影响读者阅读时的紧张程度</div>
                              </el-form-item>
                            </el-form>
                          </div>
                        </TransitionGroup>
                        
                        <div v-if="segment.subSegments.length === 0" class="empty-segments">
                          <el-empty description="暂无子桥段" :image-size="60">
                            <el-button @click="addDynamicSubSegment(segment.id)">添加第一个子桥段</el-button>
                          </el-empty>
                        </div>
                      </div>
                      
                      <!-- 插入新桥段按钮 -->
                      <div class="insert-segment-actions">
                        <el-button
                          type="default"
                          size="small"
                          @click="addDynamicSegment(index + 1)"
                          class="insert-segment-button"
                          plain
                        >
                          <el-icon><Plus /></el-icon> 在此后插入新桥段
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>              
          </div>
          
          <!-- 伏笔管理 -->
          <div v-else-if="activeExpertSection === 'foreshadow'" class="expert-section">
            <div class="section-header">
              <h3>伏笔系统</h3>
              <el-button type="primary" size="small" @click="addForeshadow">
                <el-icon><Plus /></el-icon> 添加伏笔
              </el-button>
            </div>
            
            <!-- 添加一个可滚动的容器专门用于伏笔管理 -->
            <div class="foreshadow-scrollable-container">
              <div class="foreshadow-list expert-foreshadow-list">
                <div v-if="safeForeshadows.length === 0" class="empty-foreshadows">
                  <el-empty description="暂无伏笔记录" :image-size="60">
                    <el-button @click="addForeshadow">添加第一个伏笔</el-button>
                  </el-empty>
                </div>
                
                <TransitionGroup name="list" tag="div">
                  <div v-for="foreshadow in safeForeshadows"
                       :key="foreshadow.id" 
                       class="foreshadow-card"
                       :id="`foreshadow-${foreshadow.id}`"
                       :data-type="foreshadow.type"
                       :data-importance="foreshadow.importance">
                    <div class="foreshadow-card-header">
                      <div class="foreshadow-title">
                        <el-input v-model="foreshadow.title" placeholder="伏笔标题" />
                      </div>
                      <div class="delete-button-wrapper">
                        <el-button 
                          type="danger" 
                          size="default" 
                          @click="removeForeshadow(foreshadow.id)" 
                          class="delete-foreshadow-btn"
                        >
                          <el-icon :size="18"><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>

                    <div class="foreshadow-details-grid">
                      <div class="foreshadow-detail-item">
                        <el-form-item label="伏笔类型">
                          <el-select v-model="foreshadow.type" placeholder="选择伏笔类型" style="width: 100%;">
                            <el-option label="物品类" value="physical" />
                            <el-option label="情报/信息类" value="information" />
                            <el-option label="能力缺陷类" value="ability" />
                            <el-option label="人际关系类" value="relationship" />
                            <el-option label="环境规则类" value="environment" />
                          </el-select>
                        </el-form-item>
                      </div>
                      
                      <div class="foreshadow-detail-item">
                        <el-form-item label="重要程度">
                          <el-rate
                            v-model="foreshadow.importance"
                            :max="3"
                            @change="(val) => {
                              if (typeof foreshadow === 'object' && foreshadow !== null) {
                                foreshadow.importance = Number(val);
                              }
                            }"
                          />
                        </el-form-item>
                      </div>
                      
                      <div class="foreshadow-detail-item">
                        <el-form-item label="埋设位置">
                          <el-select v-model="foreshadow.plantSegment" placeholder="选择埋设位置" filterable style="width: 100%;">
                            <el-option-group label="动态桥段">
                              <el-option
                                v-for="(seg, idx) in outlineData.dynamicSegments"
                                :key="`plant-main-${seg.id}`"
                                :value="`DYN${idx}`"
                                :label="`${idx + 1}: ${seg.title || '未命名'} (${getBridgeTypeLabel(seg.type)})`"
                              >
                                <div class="foreshadow-segment-option" :class="`segment-type-${seg.type}`">
                                  <div class="segment-option-index">{{ idx + 1 }}</div>
                                  <div class="segment-option-title">{{ seg.title || '未命名桥段' }}</div>
                                  <div class="segment-option-badge">{{ getBridgeTypeLabel(seg.type) }}</div>
                                </div>
                              </el-option>
                            </el-option-group>
                            
                            <el-option-group 
                              v-for="(seg, idx) in outlineData.dynamicSegments.filter(s => s.subSegments && s.subSegments.length > 0)"
                              :key="`plant-sub-group-${seg.id}`"
                              :label="`${idx + 1}: ${seg.title || '未命名'} 的子桥段`"
                            >
                              <el-option
                                v-for="(subSeg, subIdx) in seg.subSegments"
                                :key="`plant-sub-${seg.id}-${subSeg.id}`"
                                :value="`DYN${idx}-${subIdx}`"
                                :label="`${idx + 1}.${subIdx + 1}: ${subSeg.title || '未命名子桥段'}`"
                              >
                                <div class="foreshadow-segment-option sub-segment">
                                  <div class="segment-option-index">{{ idx + 1 }}.{{ subIdx + 1 }}</div>
                                  <div class="segment-option-title">{{ subSeg.title || '未命名子桥段' }}</div>
                                </div>
                              </el-option>
                            </el-option-group>
                            
                            <el-option-group label="其他选项">
                              <el-option value="next_volume" label="后续卷回收" />
                            </el-option-group>
                          </el-select>
                          <div class="form-helper-text">选择在哪个剧情桥段中埋下这个伏笔</div>
                        </el-form-item>
                      </div>
                      
                      <div class="foreshadow-detail-item">
                        <el-form-item label="回收位置">
                          <el-select v-model="foreshadow.payoffSegment" placeholder="选择回收位置" filterable style="width: 100%;">
                            <el-option-group label="动态桥段">
                              <el-option
                                v-for="(seg, idx) in outlineData.dynamicSegments"
                                :key="`payoff-main-${seg.id}`"
                                :value="`DYN${idx}`"
                                :label="`${idx + 1}: ${seg.title || '未命名'} (${getBridgeTypeLabel(seg.type)})`"
                              >
                                <div class="foreshadow-segment-option" :class="`segment-type-${seg.type}`">
                                  <div class="segment-option-index">{{ idx + 1 }}</div>
                                  <div class="segment-option-title">{{ seg.title || '未命名桥段' }}</div>
                                  <div class="segment-option-badge">{{ getBridgeTypeLabel(seg.type) }}</div>
                                </div>
                              </el-option>
                            </el-option-group>
                            
                            <el-option-group 
                              v-for="(seg, idx) in outlineData.dynamicSegments.filter(s => s.subSegments && s.subSegments.length > 0)"
                              :key="`payoff-sub-group-${seg.id}`"
                              :label="`${idx + 1}: ${seg.title || '未命名'} 的子桥段`"
                            >
                              <el-option
                                v-for="(subSeg, subIdx) in seg.subSegments"
                                :key="`payoff-sub-${seg.id}-${subSeg.id}`"
                                :value="`DYN${idx}-${subIdx}`"
                                :label="`${idx + 1}.${subIdx + 1}: ${subSeg.title || '未命名子桥段'}`"
                              >
                                <div class="foreshadow-segment-option sub-segment">
                                  <div class="segment-option-index">{{ idx + 1 }}.{{ subIdx + 1 }}</div>
                                  <div class="segment-option-title">{{ subSeg.title || '未命名子桥段' }}</div>
                                </div>
                              </el-option>
                            </el-option-group>
                            
                            <el-option-group label="其他选项">
                              <el-option value="next_volume" label="后续卷回收" />
                            </el-option-group>
                          </el-select>
                          <div class="form-helper-text">选择在哪个剧情桥段中回收这个伏笔</div>
                        </el-form-item>
                      </div>
                      
                      <div class="foreshadow-description">
                        <el-form-item label="伏笔描述">
                          <el-input
                            type="textarea"
                            v-model="foreshadow.description"
                            :rows="3"
                            placeholder="详细描述这个伏笔的内容、表现形式和作用"
                          />
                        </el-form-item>
                      </div>
                      
                      <div class="foreshadow-description">
                        <el-form-item label="备选处理方案">
                          <el-input
                            type="textarea"
                            v-model="foreshadow.alternativePlan"
                            :rows="2"
                            placeholder="如果无法在计划位置回收，有什么备选方案？"
                          />
                        </el-form-item>
                      </div>
                      
                      <!-- 添加伏笔与桥段的关联信息显示 -->
                      <div class="foreshadow-segment-links" v-if="foreshadow.plantSegment || foreshadow.payoffSegment">
                        <div class="foreshadow-links-header">
                          <el-icon><Link /></el-icon> 剧情关联
                        </div>
                        
                        <div class="foreshadow-links-content">
                          <!-- 埋设位置关联 -->
                          <div class="foreshadow-link-item" v-if="foreshadow.plantSegment">
                            <div class="link-title">伏笔埋设：</div>
                            <div class="link-content">
                              <el-tag size="small" :type="getSegmentTagType(foreshadow.plantSegment)">
                                {{ getSegmentTitleByValue(foreshadow.plantSegment) }}
                              </el-tag>
                            </div>
                          </div>
                          
                          <!-- 回收位置关联 -->
                          <div class="foreshadow-link-item" v-if="foreshadow.payoffSegment">
                            <div class="link-title">伏笔回收：</div>
                            <div class="link-content">
                              <el-tag size="small" :type="getSegmentTagType(foreshadow.payoffSegment)">
                                {{ getSegmentTitleByValue(foreshadow.payoffSegment) }}
                              </el-tag>
                            </div>
                          </div>
                          
                          <!-- 埋设到回收的跨度 -->
                          <div class="foreshadow-link-item" v-if="foreshadow.plantSegment && foreshadow.payoffSegment && foreshadow.plantSegment !== 'next_volume' && foreshadow.payoffSegment !== 'next_volume'">
                            <div class="link-title">跨度：</div>
                            <div class="link-content">
                              {{ getSegmentSpan(foreshadow.plantSegment, foreshadow.payoffSegment) }} 个桥段
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TransitionGroup>
              </div>
            </div>
          </div>
                    
          <!-- 情绪曲线分析 -->
          <div v-else-if="activeExpertSection === 'emotion'" class="expert-section">
            <div class="section-header">
              <h3>情绪曲线分析</h3>
              <div class="emotion-controls">
                <el-checkbox v-model="showMicroEmotions">显示桥段内部情绪</el-checkbox>
                <el-button type="primary" size="small" plain @click="updateEmotionChart">
                  <el-icon><Refresh /></el-icon> 刷新图表
                </el-button>
              </div>
            </div>
            
            <el-card class="box-card emotion-curve-card" shadow="hover">
              <template #header>
                <div class="emotion-card-header">
                  <span>情绪曲线可视化</span>
                  <el-tag size="small" effect="plain" type="info">根据桥段情绪值生成</el-tag>
                </div>
              </template>
              <div class="emotion-curve-container">
                <div class="emotion-chart-wrapper">
                  <div ref="emotionChartRef" class="emotion-chart"></div>
                </div>
                <div class="emotion-legend">
                  <div class="legend-item">
                    <div class="legend-color" style="background: var(--el-color-primary)"></div>
                    <span>主桥段情绪</span>
                  </div>
                  <div class="legend-item" v-if="showMicroEmotions">
                    <div class="legend-color legend-color-sub"></div>
                    <span>子桥段情绪</span>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
          
          <!-- 预览 -->
          <div v-else-if="activeExpertSection === 'preview'" class="expert-section">
            <div class="section-header user-select-none">
              <h3>大纲预览</h3>
              <div class="preview-actions">
                <el-switch
                  v-model="previewType"
                  inline-prompt
                  active-text="专业格式"
                  inactive-text="普通格式"
                  class="preview-switch"
                />
                <el-button type="primary" size="small" plain @click="exportPreviewAsTxt">
                  <el-icon><Document /></el-icon> 复制并导出
                </el-button>
              </div>
            </div>
            
            <el-card class="box-card outline-preview-card" shadow="hover">
              <div class="preview-content" v-loading="generatingPreview">
                <!-- 专业格式预览 -->
                <div v-if="previewType" class="professional-preview">
                  <div class="preview-volume-info">
                    <h2 class="preview-title">{{ outlineData.volumeTitle || '未命名卷' }}</h2>
                    <div class="preview-meta">
                      <div class="meta-item">
                        <span class="meta-label user-select-none">卷主题：</span>
                        <span>{{ outlineData.volumeTheme || '无' }}</span>
                      </div>
                      <div class="meta-item">
                        <span class="meta-label user-select-none">核心冲突：</span>
                        <span>{{ outlineData.coreConflict || '无' }}</span>
                      </div>
                      <div class="meta-item">
                        <span class="meta-label user-select-none">功能定位：</span>
                        <span>{{ getVolumeFunctionLabel(outlineData.volumeFunction) }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="preview-segments">
                    <h3 class="preview-section-title user-select-none">桥段结构</h3>
                    <div v-if="outlineData.dynamicSegments.length === 0" class="empty-preview">
                      尚未创建桥段结构，请在"结构设计"中添加桥段。
                    </div>
                    <template v-else>
                      <div v-for="(segment, index) in outlineData.dynamicSegments" :key="segment.id" class="preview-segment">
                        <div class="segment-header user-select-none">
                          <div class="segment-header-top">
                            <span class="segment-index">{{ index + 1 }}</span>
                            <span class="segment-type-badge" :class="`segment-type-${segment.type}`">
                              {{ getBridgeTypeLabel(segment.type) }}
                            </span>
                          </div>
                          <h4 class="segment-title">{{ segment.title || `未命名桥段${index + 1}` }}</h4>
                        </div>
                        
                        <div class="segment-body">
                          <div class="segment-property" v-if="segment.description">
                            <div class="property-label user-select-none">描述：</div>
                            <div class="property-value">{{ segment.description }}</div>
                          </div>
                          <div class="segment-property" v-if="segment.goal">
                            <div class="property-label user-select-none">目标：</div>
                            <div class="property-value">{{ segment.goal }}</div>
                          </div>
                          <div class="segment-property" v-if="segment.conflict">
                            <div class="property-label user-select-none">冲突：</div>
                            <div class="property-value">{{ segment.conflict }}</div>
                          </div>
                          <div class="segment-property" v-if="segment.result">
                            <div class="property-label user-select-none">结果：</div>
                            <div class="property-value">{{ segment.result }}</div>
                          </div>
                          <div class="segment-property" v-if="segment.input">
                            <div class="property-label user-select-none">输入：</div>
                            <div class="property-value">{{ segment.input }}</div>
                          </div>
                          <div class="segment-property" v-if="segment.output">
                            <div class="property-label user-select-none">输出：</div>
                            <div class="property-value">{{ segment.output }}</div>
                          </div>
                          <div class="segment-property" v-if="segment.foreshadowing">
                            <div class="property-label user-select-none">伏笔：</div>
                            <div class="property-value">{{ segment.foreshadowing }}</div>
                          </div>
                          <div class="segment-property" v-if="segment.characterStatus">
                            <div class="property-label user-select-none">人物：</div>
                            <div class="property-value">{{ segment.characterStatus }}</div>
                          </div>
                          
                          <!-- 子桥段预览 -->
                          <div v-if="segment.subSegments && segment.subSegments.length > 0" class="segment-subsegments">
                            <h5 class="subsegments-title user-select-none">子桥段：</h5>
                            <div v-for="(subSeg, subIndex) in segment.subSegments" :key="subSeg.id" class="subsegment-item">
                              <div class="subsegment-header user-select-none">
                                <span class="subsegment-index">{{ index + 1 }}.{{ subIndex + 1 }}</span>
                                <span class="subsegment-title">{{ subSeg.title || `未命名子桥段${subIndex + 1}` }}</span>
                              </div>
                              <div class="subsegment-body">
                                <div v-if="subSeg.goal" class="subsegment-property">
                                  <span class="subsegment-property-label">目标：</span>{{ subSeg.goal }}
                                </div>
                                <div v-if="subSeg.conflict" class="subsegment-property">
                                  <span class="subsegment-property-label">冲突：</span>{{ subSeg.conflict }}
                                </div>
                                <div v-if="subSeg.result" class="subsegment-property">
                                  <span class="subsegment-property-label">结果：</span>{{ subSeg.result }}
                                </div>
                                <div v-if="subSeg.input" class="subsegment-property">
                                  <span class="subsegment-property-label">输入：</span>{{ subSeg.input }}
                                </div>
                                <div v-if="subSeg.output" class="subsegment-property">
                                  <span class="subsegment-property-label">输出：</span>{{ subSeg.output }}
                                </div>
                                <div v-if="subSeg.foreshadowing" class="subsegment-property">
                                  <span class="subsegment-property-label">伏笔：</span>{{ subSeg.foreshadowing }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                  
                  <!-- 伏笔系统预览 -->
                  <div class="preview-foreshadows">
                    <h3 class="preview-section-title user-select-none">伏笔系统</h3>
                    <div v-if="safeForeshadows.length === 0" class="empty-preview">
                      暂无伏笔记录，请在"伏笔管理"中添加伏笔。
                    </div>
                    <div v-else class="foreshadows-list">
                      <div v-for="(foreshadow, index) in safeForeshadows" :key="foreshadow.id" class="preview-foreshadow">
                        <div class="foreshadow-header user-select-none">
                          <span class="foreshadow-index">{{ index + 1 }}</span>
                          <span class="foreshadow-title">{{ foreshadow.title || '未命名伏笔' }}</span>
                          <span class="foreshadow-type">{{ getForeshadowTypeName(foreshadow.type) }}</span>
                          <span class="foreshadow-importance">
                            <el-rate :model-value="foreshadow.importance" disabled :max="3" />
                          </span>
                        </div>
                        <div class="foreshadow-body">
                          <div class="foreshadow-property" v-if="foreshadow.description">
                            <div class="foreshadow-property-label user-select-none">描述：</div>
                            <div class="foreshadow-property-value">{{ foreshadow.description }}</div>
                          </div>
                          <div class="foreshadow-property" v-if="foreshadow.alternativePlan">
                            <div class="foreshadow-property-label user-select-none">备选方案：</div>
                            <div class="foreshadow-property-value">{{ foreshadow.alternativePlan }}</div>
                          </div>
                          <div class="foreshadow-flow user-select-none" v-if="foreshadow.plantSegment || foreshadow.payoffSegment">
                            <span class="flow-label">埋设：</span>{{ getSegmentTitleByValue(foreshadow.plantSegment) || '未指定' }} 
                            → <span class="flow-label">回收：</span>{{ getSegmentTitleByValue(foreshadow.payoffSegment) || '未指定' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 底部结束标识 -->
                  <div class="preview-end-indicator">
                    <el-divider>预览结束</el-divider>
                  </div>
                </div>
                
                <!-- 普通格式预览（给作家使用的简洁格式） -->
                <div v-else class="simple-preview">
                  <h2 class="preview-title">{{ outlineData.volumeTitle || '未命名卷' }}</h2>
                  <div class="preview-description" v-if="outlineData.volumeTheme || outlineData.coreConflict">
                    <p>{{ outlineData.volumeTheme ? `【主题】${outlineData.volumeTheme}` : '' }}</p>
                    <p>{{ outlineData.coreConflict ? `【核心】${outlineData.coreConflict}` : '' }}</p>
                  </div>
                  
                  <div class="preview-segments-simple">
                    <div v-if="outlineData.dynamicSegments.length === 0" class="empty-preview">
                      尚未创建桥段结构，请在"结构设计"中添加桥段。
                    </div>
                    <template v-else>
                      <div v-for="(segment, index) in outlineData.dynamicSegments" :key="segment.id" class="preview-segment-simple">
                        <h4 class="segment-title-simple">
                          {{ index + 1 }}. {{ segment.title || `未命名桥段${index + 1}` }}
                          <span class="segment-type-badge-simple" :class="`segment-type-${segment.type}`">{{ getBridgeTypeLabel(segment.type) }}</span>
                        </h4>
                        
                        <div class="segment-content-simple" v-if="segment.description">
                          <strong class="simple-property-label">描述：</strong>{{ segment.description }}
                        </div>
                        <div class="segment-content-simple" v-if="segment.goal">
                          <strong class="simple-property-label">目标：</strong>{{ segment.goal }}
                        </div>
                        <div class="segment-content-simple" v-if="segment.conflict">
                          <strong class="simple-property-label">冲突：</strong>{{ segment.conflict }}
                        </div>
                        <div class="segment-content-simple" v-if="segment.result">
                          <strong class="simple-property-label">结果：</strong>{{ segment.result }}
                        </div>
                        <div class="segment-content-simple" v-if="segment.input">
                          <strong class="simple-property-label">输入：</strong>{{ segment.input }}
                        </div>
                        <div class="segment-content-simple" v-if="segment.output">
                          <strong class="simple-property-label">输出：</strong>{{ segment.output }}
                        </div>
                        <div class="segment-content-simple" v-if="segment.foreshadowing">
                          <strong class="simple-property-label">伏笔：</strong>{{ segment.foreshadowing }}
                        </div>
                        <div class="segment-content-simple" v-if="segment.characterStatus">
                          <strong class="simple-property-label">人物状态：</strong>{{ segment.characterStatus }}
                        </div>
                        
                        <!-- 简化的子桥段 -->
                        <div v-if="segment.subSegments && segment.subSegments.length > 0" class="subsegments-simple">
                          <div v-for="(subSeg, subIndex) in segment.subSegments" :key="subSeg.id" class="subsegment-item-simple">
                            <strong class="user-select-none">{{ index + 1 }}.{{ subIndex + 1 }} {{ subSeg.title || `子桥段${subIndex + 1}` }}</strong>
                            <div v-if="subSeg.goal" class="subsegment-simple-property">
                              <strong>目标：</strong>{{ subSeg.goal }}
                            </div>
                            <div v-if="subSeg.conflict" class="subsegment-simple-property">
                              <strong>冲突：</strong>{{ subSeg.conflict }}
                            </div>
                            <div v-if="subSeg.result" class="subsegment-simple-property">
                              <strong>结果：</strong>{{ subSeg.result }}
                            </div>
                            <div v-if="subSeg.input" class="subsegment-simple-property">
                              <strong>输入：</strong>{{ subSeg.input }}
                            </div>
                            <div v-if="subSeg.output" class="subsegment-simple-property">
                              <strong>输出：</strong>{{ subSeg.output }}
                            </div>
                            <div v-if="subSeg.foreshadowing" class="subsegment-simple-property">
                              <strong>伏笔：</strong>{{ subSeg.foreshadowing }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                  
                  <!-- 简单伏笔列表 -->
                  <div class="foreshadows-simple">
                    <h4 class="foreshadows-title-simple user-select-none">伏笔列表</h4>
                    <div v-if="safeForeshadows.length === 0" class="empty-preview">
                      暂无伏笔记录，请在"伏笔管理"中添加伏笔。
                    </div>
                    <ul v-else class="foreshadows-list-simple">
                      <li v-for="foreshadow in safeForeshadows" :key="foreshadow.id">
                        <strong>{{ foreshadow.title || '未命名伏笔' }}</strong>
                        <div class="foreshadow-simple-property" v-if="foreshadow.description">
                          <strong>描述：</strong>{{ foreshadow.description }}
                        </div>
                        <div class="foreshadow-simple-property" v-if="foreshadow.alternativePlan">
                          <strong>备选方案：</strong>{{ foreshadow.alternativePlan }}
                        </div>
                        <div class="foreshadow-simple-property" v-if="foreshadow.plantSegment || foreshadow.payoffSegment">
                          <strong>埋设：</strong>{{ getSegmentTitleByValue(foreshadow.plantSegment) || '未指定' }} 
                          → <strong>回收：</strong>{{ getSegmentTitleByValue(foreshadow.payoffSegment) || '未指定' }}
                        </div>
                      </li>
                    </ul>
                  </div>
                  
                  <!-- 底部结束标识 -->
                  <div class="preview-end-indicator">
                    <el-divider>预览结束</el-divider>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted, onUnmounted, watch } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';
import { v4 as uuidv4 } from 'uuid';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  MarkLineComponent,
  MarkPointComponent
} from 'echarts/components';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import {
  Check,
  Download,
  Upload,
  Back,
  Aim,
  TopRight,
  SwitchButton,
  Connection,
  Link,
  TrendCharts,
  ArrowRight,
  ArrowLeft,
  Guide,
  Operation,
  Document,
  View,
  Delete,
  Plus,
  ArrowUp,
  ArrowDown,
  DataAnalysis,
  Top,
  Bottom,
  Calendar,
  Refresh,
  InfoFilled
} from '@element-plus/icons-vue';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  LineChart,
  MarkLineComponent,
  MarkPointComponent,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer
]);



// 基础状态
const isFullscreen = ref(false);
const bookTitle = ref('卷设计'); // 修改默认标题
const saving = ref(false);
const showMicroEmotions = ref(true);
let emotionChart = null;
const isLoading = ref(false); // 添加加载状态标志

// 专家模式状态
const activeExpertSection = ref('basic');
const activeStructureTab = ref('start');
const activeStoryFlowItems = ref([]);

// 伏笔系统状态
const inputVisible = ref(false);
const outputVisible = ref(false);
const inputValue = ref('');
const outputValue = ref('');
const inputRef = ref(null);
const outputRef = ref(null);

// 预览状态
const previewType = ref(false);

// 图表引用
const emotionChartRef = ref(null);
const structureVisualizationRef = ref(null);

// 大纲数据
const outlineData = reactive({
  id: '', // 添加ID字段
  timestamp: 0, // 添加时间戳字段
  volumeTitle: '',
  volumeTheme: '',
  coreConflict: '',
  volumeFunction: 'escalation',
  
  // 伏笔系统
  foreshadows: [],
  
  // 专业模式结构流
  storyFlow: [],

  // 动态桥段系统
  dynamicSegments: [],
});

// 新增: 控制动态桥段系统的变量
const showDynamicBridgeSystem = ref(true); // 使用动态桥段系统
const bridgeTypes = [
  { value: 'normal', label: '通用桥段' },
  { value: 'pivot', label: '转折桥段' },
  { value: 'climax', label: '高潮桥段' }
];

// 新增: 添加动态桥段方法
const addDynamicSegment = (position = null) => {
  const newSegment = {
    id: uuidv4(),
    type: 'normal', // 默认为通用桥段
    title: '',
    description: '',
    characterStatus: '',
    emotionValue: 50,
    lockLevel: 1,
    goal: '',
    conflict: '',
    result: '',
    input: '',
    output: '',
    foreshadowing: '',
    rhythm: 'steady',
    subSegments: [],
    position: position // 插入的位置，如果为null则添加到末尾
  };

  if (position === null) {
    outlineData.dynamicSegments.push(newSegment);
  } else {
    outlineData.dynamicSegments.splice(position, 0, newSegment);
    // 更新后面所有桥段的position
    for (let i = position + 1; i < outlineData.dynamicSegments.length; i++) {
      outlineData.dynamicSegments[i].position = i;
    }
  }
  
  return newSegment;
};

// 新增: 删除动态桥段方法
const removeDynamicSegment = (id) => {
  const index = outlineData.dynamicSegments.findIndex(seg => seg.id === id);
  if (index !== -1) {
    ElMessage({
      message: `已删除桥段"${outlineData.dynamicSegments[index].title || '未命名桥段'}"`,
      type: 'success'
    });
    
    outlineData.dynamicSegments.splice(index, 1);
    // 更新后面所有桥段的position
    for (let i = index; i < outlineData.dynamicSegments.length; i++) {
      outlineData.dynamicSegments[i].position = i;
    }
    
    // 自动选择下一个可用标签页
    if (outlineData.dynamicSegments.length > 0) {
      nextTick(() => {
        // 如果删除的是当前活跃的标签页
        activeSegmentTab.value = outlineData.dynamicSegments[Math.min(index, outlineData.dynamicSegments.length - 1)].id;
      });
    }
  }
};

// 新增: 移动动态桥段方法
const moveDynamicSegment = (index, direction) => {
  const newIndex = index + direction;
  if (newIndex < 0 || newIndex >= outlineData.dynamicSegments.length) return;
  
  const temp = outlineData.dynamicSegments[index];
  outlineData.dynamicSegments[index] = outlineData.dynamicSegments[newIndex];
  outlineData.dynamicSegments[newIndex] = temp;
  
  // 更新移动后的桥段position
  outlineData.dynamicSegments[index].position = index;
  outlineData.dynamicSegments[newIndex].position = newIndex;
};

// 新增: 动态桥段子段添加方法
const addDynamicSubSegment = (parentId) => {
  const parentIndex = outlineData.dynamicSegments.findIndex(seg => seg.id === parentId);
  if (parentIndex === -1) return;
  
  const newSubSegment = {
    id: uuidv4(),
    title: '',
    emotionValue: 50,
    lockLevel: 1,
    goal: '',
    conflict: '',
    result: '',
    input: '',
    output: '',
    foreshadowing: '',
    rhythm: 'steady'
  };
  
  outlineData.dynamicSegments[parentIndex].subSegments.push(newSubSegment);
  return newSubSegment;
};

// 新增: 删除动态桥段子段方法
const removeDynamicSubSegment = (parentId, subId) => {
  const parentIndex = outlineData.dynamicSegments.findIndex(seg => seg.id === parentId);
  if (parentIndex === -1) return;
  
  const subIndex = outlineData.dynamicSegments[parentIndex].subSegments.findIndex(
    subSeg => subSeg.id === subId
  );
  if (subIndex === -1) return;
  
  outlineData.dynamicSegments[parentIndex].subSegments.splice(subIndex, 1);
};

// === 计算属性 ===

// 安全的伏笔数组，确保所有元素都是有效对象
const safeForeshadows = computed(() => {
  if (!outlineData.foreshadows || !Array.isArray(outlineData.foreshadows)) {
    return [];
  }

  return outlineData.foreshadows.filter(foreshadow => {
    return typeof foreshadow === 'object' && foreshadow !== null && !Array.isArray(foreshadow) && foreshadow.id;
  });
});

// 伏笔选项
const foreshadowPlantOptions = computed(() => {
  const options = [];
  
  // 为每个动态桥段创建选项
  const dynamicOptions = outlineData.dynamicSegments.map((seg, idx) => ({
    value: `DYN${idx}`,
    label: `${idx + 1}: ${seg.title || '未命名'} (${getBridgeTypeLabel(seg.type)})`
  }));
  
  options.push({
    label: '动态桥段',
    options: dynamicOptions
  });
  
  // 为每个动态桥段的子桥段创建选项
  outlineData.dynamicSegments.forEach((seg, idx) => {
    if (seg.subSegments && seg.subSegments.length > 0) {
      const segSubOptions = seg.subSegments.map((subSeg, subIdx) => ({
        value: `DYN${idx}-${subIdx}`,
        label: `${idx + 1}.${subIdx + 1}: ${subSeg.title || '未命名子桥段'}`
      }));
      
      options.push({
        label: `${idx + 1}: ${seg.title || '未命名'} 的子桥段`,
        options: segSubOptions
      });
    }
  });
  
  // 添加后续卷选项
  options.push({ value: 'next_volume', label: '后续卷回收' });
  
  return options;
});

const foreshadowPayoffOptions = computed(() => {
  const options = [];
  
  // 为每个动态桥段创建选项
  const dynamicOptions = outlineData.dynamicSegments.map((seg, idx) => ({
    value: `DYN${idx}`,
    label: `${idx + 1}: ${seg.title || '未命名'} (${getBridgeTypeLabel(seg.type)})`
  }));
  
  options.push({
    label: '动态桥段',
    options: dynamicOptions
  });
  
  // 为每个动态桥段的子桥段创建选项
  outlineData.dynamicSegments.forEach((seg, idx) => {
    if (seg.subSegments && seg.subSegments.length > 0) {
      const segSubOptions = seg.subSegments.map((subSeg, subIdx) => ({
        value: `DYN${idx}-${subIdx}`,
        label: `${idx + 1}.${subIdx + 1}: ${subSeg.title || '未命名子桥段'}`
      }));
      
      options.push({
        label: `${idx + 1}: ${seg.title || '未命名'} 的子桥段`,
        options: segSubOptions
      });
    }
  });
  
  // 添加后续卷选项
  options.push({ value: 'next_volume', label: '后续卷回收' });
  
  return options;
});

// === 方法 ===

// 桥段管理
// (移除旧的addSubSegment, removeSegment, moveSegment方法)

// 接口管理
const showInputBox = (type) => {
  if (type === 'input') {
    inputVisible.value = true;
    nextTick(() => {
      inputRef.value.focus();
    });
  } else {
    outputVisible.value = true;
    nextTick(() => {
      outputRef.value.focus();
    });
  }
};

const addInterface = (type) => {
  // 使用动态桥段系统的接口管理
  // 这里可以实现动态桥段之间的接口连接
};

const removeInterface = (type, index) => {
  // 同样使用动态桥段系统的接口管理
};

// 伏笔系统
const addForeshadow = () => {
  const newForeshadow = {
    id: uuidv4(),
    title: '',
    type: 'physical',  // 默认选择物品类
    importance: 2,
    plantSegment: '',
    payoffSegment: '',
    description: '',
    alternativePlan: '',
    connectionTypes: []
  };
  
  outlineData.foreshadows.push(newForeshadow);
};

const removeForeshadow = (id) => {
  const index = outlineData.foreshadows.findIndex(f => f.id === id);
  if (index !== -1) {
    outlineData.foreshadows.splice(index, 1);
  }
};

const getForeshadowTypeName = (type) => {
  const typeMap = {
    'physical': '物品类',
    'information': '情报/信息类',
    'ability': '能力缺陷类',
    'relationship': '人际关系类',
    'environment': '环境规则类'
  };
  return typeMap[type] || type;
};

// 情绪曲线分析
const getEmotionAnalysis = () => {
  // 根据数据生成分析文本
  const segments = outlineData.dynamicSegments;
  if (segments.length === 0) {
    return '暂无桥段数据，无法分析情绪曲线。';
  }
  
  // 获取情绪值
  const emotions = segments.map(seg => seg.emotionValue);
  const maxEmotion = Math.max(...emotions);
  const minEmotion = Math.min(...emotions);
  const emotionRange = maxEmotion - minEmotion;
  
  let analysis = '';
  
  // 检查整体波动
  if (emotionRange < 30) {
    analysis += '情绪波动偏小，可能导致情节缺乏张力。';
  } else if (emotionRange > 60) {
    analysis += '情绪波动较大，有明显的高低起伏。';
  } else {
    analysis += '情绪波动适中，整体节奏平稳。';
  }
  
  // 检查高潮桥段
  const climaxSegments = segments.filter(seg => seg.type === 'climax');
  if (climaxSegments.length > 0) {
    const climaxEmotions = climaxSegments.map(seg => seg.emotionValue);
    const avgClimaxEmotion = climaxEmotions.reduce((a, b) => a + b, 0) / climaxEmotions.length;
    
    if (avgClimaxEmotion < 70) {
      analysis += ' 高潮点情绪强度略显不足，可考虑增加幅度。';
    } else if (avgClimaxEmotion > 90) {
      analysis += ' 高潮点情绪强度充足，有望给读者留下深刻印象。';
    }
  }
  
  // 检查转折点设计
  const pivotSegments = segments.filter(seg => seg.type === 'pivot');
  if (pivotSegments.length > 0) {
    // 获取转折点的平均情绪值
    const pivotEmotions = pivotSegments.map(seg => seg.emotionValue);
    const avgPivotEmotion = pivotEmotions.reduce((a, b) => a + b, 0) / pivotEmotions.length;
    
    // 比较转折点与整体情绪
    const avgEmotion = emotions.reduce((a, b) => a + b, 0) / emotions.length;
    
    if (avgPivotEmotion > avgEmotion) {
      analysis += ' 转折点情绪高于平均值，注意不要影响后续高潮的情绪效果。';
    } else if (avgPivotEmotion < avgEmotion) {
      analysis += ' 转折点为情绪低谷，有助于塑造"绝境求生"的故事模式。';
    }
  }
  
  return analysis;
};

const emotionTips = [
  '理想的情绪曲线应该有起有伏，避免平坦无波或过度跳跃',
  '转折点C一般是情绪的突变点，通常从高点跌落或从低谷回升',
  '高潮点E的情绪强度应当是整个卷中最强的，避免过早达到情绪巅峰',
  '子桥段间的情绪可以有小幅波动，但整体应符合大桥段的情绪走向',
  '伏笔点的情绪值可以作为写作时的参考，帮助打造特定氛围'
];

// 初始化情绪曲线图表
const initEmotionChart = () => {
  if (!emotionChartRef.value) return;
  
  // 销毁已有图表
  if (emotionChart) {
    emotionChart.dispose();
  }
  
  // 初始化图表
  emotionChart = echarts.init(emotionChartRef.value);
  updateEmotionChart();
};

// 更新情绪曲线图表
const updateEmotionChart = () => {
  if (!emotionChart) {
    initEmotionChart();
  } else {
    // 准备数据
    let emotionData = [];
    
    // 动态桥段系统数据
    emotionData = outlineData.dynamicSegments.map((seg, idx) => {
      return { 
        name: `${idx + 1}`, 
        value: seg.emotionValue,
        segmentType: seg.type
      };
    });
    
    // 更新图表配置
    const option = {
      title: {
        text: '动态桥段情绪曲线',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const dataPoint = params[0];
          const name = dataPoint.name;
          const value = dataPoint.value;
          const segType = dataPoint.data.segmentType;
          
          let result = `${name}: ${value}`;
          if (segType) {
            result += ` (${getBridgeTypeLabel(segType)})`;
          }
          
          return result;
        }
      },
      legend: {
        data: ['动态桥段情绪'],
        bottom: '5%'
      },
      xAxis: {
        type: 'category',
        data: emotionData.map(item => item.name)
      },
      yAxis: {
        type: 'value',
        name: '情绪强度',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}'
        },
        axisLine: { show: true }
      },
      series: [
        {
          name: '动态桥段情绪',
          type: 'line',
          data: emotionData.map(item => ({
            value: item.value,
            segmentType: item.segmentType
          })),
          smooth: true,
          lineStyle: {
            width: 4
          },
          symbolSize: 10,
          markPoint: {
            data: [
              { type: 'max', name: '最高点' },
              { type: 'min', name: '最低点' }
            ]
          }
        }
      ]
    };
    
    // 添加动态桥段子桥段情绪线
    if (showMicroEmotions.value) {
      outlineData.dynamicSegments.forEach((segment, idx) => {
        if (segment.subSegments && segment.subSegments.length > 0) {
          const subSegmentEmotions = segment.subSegments.map((subSeg, subIdx) => ({
            name: `${idx+1}.${subIdx+1}`,
            value: subSeg.emotionValue
          }));
          
          if (subSegmentEmotions.length > 0) {
            option.series.push({
              name: `桥段${idx+1}的子桥段`,
              type: 'line',
              data: [
                { value: segment.emotionValue }, // 起始点是桥段本身的情绪值
                ...subSegmentEmotions.map(item => item.value),
                { value: segment.emotionValue } // 结束点也是桥段本身的情绪值
              ],
              smooth: true,
              lineStyle: {
                width: 2,
                type: 'dashed'
              },
              symbolSize: 6,
              itemStyle: {
                color: getColorForSegment(idx)
              }
            });
          }
        }
      });
    }
    
    // 渲染图表
    emotionChart.setOption(option);
  }
};

// 为不同桥段获取不同的颜色
const getColorForSegment = (index) => {
  const colors = ['#67C23A', '#E6A23C', '#409EFF', '#F56C6C', '#909399', '#9B59B6', '#1ABC9C'];
  return colors[index % colors.length];
};

// 专家模式
const getExpertSectionTitle = () => {
  switch (activeExpertSection.value) {
    case 'basic': return '卷级基本信息';
    case 'structure': return '结构设计';
    case 'foreshadow': return '伏笔系统管理';
    case 'emotion': return '情绪曲线分析';
    case 'preview': return '大纲预览';
    default: return '';
  }
};

const setActiveExpertSection = (section) => {
  activeExpertSection.value = section;
  
  nextTick(() => {
    if (section === 'emotion' && emotionChartRef.value) {
      initEmotionChart();
    }
  });
};

const getBridgeTypeClass = (bridge) => {
  if (bridge.fromNode === 'A' && bridge.toNode === 'C') return 'bridge-type-ac';
  if (bridge.fromNode === 'C' && bridge.toNode === 'E') return 'bridge-type-ce';
  if (bridge.fromNode === 'A' && bridge.toNode === 'E') return 'bridge-type-ae';
  return 'bridge-type-custom';
};

const addCustomBridge = () => {
  const newBridge = {
    id: uuidv4(),
    title: '新建桥段',
    description: '',
    fromNode: 'A',
    toNode: 'C',
    startEmotion: 50,
    endEmotion: 50,
    subSegments: []
  };
  
  outlineData.storyFlow.push(newBridge);
  activeStoryFlowItems.value.push(newBridge.id);
};

const removeCustomBridge = (id) => {
  const index = outlineData.storyFlow.findIndex(b => b.id === id);
  if (index !== -1) {
    outlineData.storyFlow.splice(index, 1);
    activeStoryFlowItems.value = activeStoryFlowItems.value.filter(item => item !== id);
  }
};

const addSubBridge = (parentId) => {
  const parent = outlineData.storyFlow.find(b => b.id === parentId);
  if (!parent) return;
  
  const newSubBridge = {
    id: uuidv4(),
    title: '',
    emotionValue: 50,
    lockLevel: 1,
    conflict: '',
    input: '',
    output: '',
    foreshadowing: ''
  };
  
  if (!parent.subSegments) {
    parent.subSegments = [];
  }
  
  parent.subSegments.push(newSubBridge);
};

const removeSubBridge = (parentId, subId) => {
  const parent = outlineData.storyFlow.find(b => b.id === parentId);
  if (!parent || !parent.subSegments) return;
  
  const index = parent.subSegments.findIndex(s => s.id === subId);
  if (index !== -1) {
    parent.subSegments.splice(index, 1);
  }
};

const moveSubBridge = (parentId, index, direction) => {
  const parent = outlineData.storyFlow.find(b => b.id === parentId);
  if (!parent || !parent.subSegments || parent.subSegments.length < 2) return;
  
  const newIndex = index + direction;
  if (newIndex < 0 || newIndex >= parent.subSegments.length) return;
  
  const temp = parent.subSegments[index];
  parent.subSegments[index] = parent.subSegments[newIndex];
  parent.subSegments[newIndex] = temp;
};

const getAvailableFromNodes = (currentBridge) => {
  return outlineData.storyFlow.filter(b => b.id !== currentBridge.id);
};

const getAvailableToNodes = (currentBridge) => {
  return outlineData.storyFlow.filter(b => b.id !== currentBridge.id);
};

// 桥段工具方法
const getBridgeSegments = (type) => {
  if (type === 'B') {
    return outlineData.novice_B_segments;
  } else if (type === 'D') {
    return outlineData.novice_D_segments;
  }
  return [];
};

// 预览功能
const generateStorySummary = () => {
  return `在${outlineData.volumeTitle}中，主角团队初到敌国首都，开始了艰险的证据收集旅程。
  在转折点，主角被诬陷为刺客，全城通缉，被迫转入地下。
  最终，主角在皇宫广场揭露了宰相的阴谋，引发全城暴动，成功扭转了局势。`;
};


// 保存导出导入功能
const saveOutline = async () => {
  saving.value = true;
  
  // 保存当前活动的标签页ID
  const currentActiveTab = activeSegmentTab.value;
  
  try {
    // 确保数据有最新时间戳
    outlineData.timestamp = Date.now();
    
    // 调用后端API保存大纲
    const responseStr = await window.pywebview.api.book_controller.save_outline(outlineData);
    // 解析JSON字符串响应
    const response = JSON.parse(responseStr);
    
    if (response.status === "success") {
      ElMessage({
        message: '大纲保存成功！',
        type: 'success'
      });
      
      // 如果返回了ID，更新本地ID
      if (response.data && response.data.id) {
        outlineData.id = response.data.id;
      }
      
      // 保存成功后刷新大纲列表
      await loadOutlineData(outlineData.id);
      
      // 恢复到之前的活动标签页（如果该标签页仍然存在）
      nextTick(() => {
        if (currentActiveTab && outlineData.dynamicSegments.some(seg => seg.id === currentActiveTab)) {
          activeSegmentTab.value = currentActiveTab;
        }
      });
    } else {
      ElMessage.error('保存失败: ' + (response.message || '未知错误'));
    }
  } catch (error) {
    console.error('保存大纲失败:', error);
    ElMessage.error('保存失败: ' + error.message);
  } finally {
    saving.value = false;
  }
};

const exportOutline = async () => {
  try {
    const dataStr = JSON.stringify(outlineData, null, 2);
    
    // 复制到剪贴板
    await window.pywebview.api.copy_to_clipboard(dataStr);
    
    ElMessage({
      message: '大纲已复制到剪贴板',
      type: 'success'
    });
  } catch (error) {
    console.error('导出大纲失败:', error);
    ElMessage.error('复制到剪贴板失败，请检查浏览器权限');
  }
};

const importOutline = () => {
  ElMessageBox.prompt('请粘贴大纲JSON数据:', '导入大纲', {
    confirmButtonText: '导入',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '粘贴JSON格式的大纲数据...'
  }).then(({ value }) => {
    if (!value || value.trim() === '') {
      ElMessage.warning('导入数据不能为空');
      return;
    }
    
    try {
      // 解析JSON数据
      const importData = JSON.parse(value);
      
      // 确认导入
      ElMessageBox.confirm(
        '确定要导入此大纲数据吗？当前大纲数据将被覆盖。',
        '确认导入',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 导入数据
        Object.assign(outlineData, importData);
        
        // 如果有动态段落，选择第一个作为活动标签
        if (outlineData.dynamicSegments && outlineData.dynamicSegments.length > 0) {
          activeSegmentTab.value = outlineData.dynamicSegments[0].id;
        }
        
        // 更新图表
        if (activeExpertSection.value === 'emotion') {
          nextTick(() => {
            updateEmotionChart();
          });
        }
        
        ElMessage.success('大纲数据导入成功');
      }).catch(() => {
        // 用户取消导入
      });
    } catch (error) {
      console.error('解析导入数据失败:', error);
      ElMessage.error('导入失败：JSON数据格式不正确');
    }
  }).catch(() => {
    // 用户取消输入
  });
};

// 添加键盘事件处理函数
const handleKeyDown = (e) => {
  // Ctrl+S 或 Command+S (Mac)
  if ((e.ctrlKey || e.metaKey) && e.key === 's') {
    // 阻止默认的保存页面行为
    e.preventDefault();
    // 触发保存大纲
    saveOutline();
  }
};

// 初始化
onMounted(() => {
  // 加载大纲数据
  loadOutlineData();
  
  // 添加窗口大小变化监听，以便重新渲染图表
  window.addEventListener('resize', handleResize);
  
  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeyDown);
  
  // 当激活概览选项卡时，初始化概览图表
  watch(activeBridgeTab, (newValue) => {
    if (newValue === 'overview') {
      nextTick(() => {
        initOverviewChart();
      });
    }
  });
  
  // 监视动态桥段数组变化，自动选择第一个桥段
  watch(() => outlineData.dynamicSegments.length, (newLength) => {
    if (newLength > 0 && !outlineData.dynamicSegments.find(seg => seg.id === activeSegmentTab.value)) {
      nextTick(() => {
        activeSegmentTab.value = outlineData.dynamicSegments[0].id;
      });
    }
  }, { immediate: true });
});

// 加载大纲数据
const loadOutlineData = async (outlineId = null) => {
  try {
    isLoading.value = true;
    
    // 调用后端API加载所有大纲数据
    const responseStr = await window.pywebview.api.book_controller.get_outlines();
    // 解析JSON字符串响应
    const response = JSON.parse(responseStr);
    
    // 处理返回数据格式
    if (response.status === "success" && response.data) {
      // 获取列表中的大纲
      outlinesList.value = response.data || [];
      
      if (outlinesList.value.length > 0) {
        if (outlineId) {
          // 加载指定ID的大纲
          const targetOutline = outlinesList.value.find(o => o.id === outlineId);
          if (targetOutline) {
            Object.assign(outlineData, targetOutline);
            // 确保伏笔数据的结构和类型正确
            if (outlineData.foreshadows) {
              // 过滤掉非对象类型的元素，并确保每个伏笔对象的结构正确
              outlineData.foreshadows = outlineData.foreshadows.filter(foreshadow => {
                return typeof foreshadow === 'object' && foreshadow !== null && !Array.isArray(foreshadow);
              }).map(foreshadow => {
                // 确保每个伏笔对象都有必要的属性
                return {
                  id: foreshadow.id || uuidv4(),
                  title: foreshadow.title || '',
                  type: foreshadow.type || 'physical',
                  importance: typeof foreshadow.importance === 'number' ? foreshadow.importance : (parseInt(foreshadow.importance) || 2),
                  plantSegment: foreshadow.plantSegment || '',
                  payoffSegment: foreshadow.payoffSegment || '',
                  description: foreshadow.description || '',
                  alternativePlan: foreshadow.alternativePlan || '',
                  connectionTypes: Array.isArray(foreshadow.connectionTypes) ? foreshadow.connectionTypes : []
                };
              });
            } else {
              outlineData.foreshadows = [];
            }
            ElMessage.success('已切换到所选大纲');
          } else {
            // 如果找不到指定ID的大纲，加载最新的大纲
            Object.assign(outlineData, outlinesList.value[0]);
            // 确保伏笔数据的结构和类型正确
            if (outlineData.foreshadows) {
              // 过滤掉非对象类型的元素，并确保每个伏笔对象的结构正确
              outlineData.foreshadows = outlineData.foreshadows.filter(foreshadow => {
                return typeof foreshadow === 'object' && foreshadow !== null && !Array.isArray(foreshadow);
              }).map(foreshadow => {
                // 确保每个伏笔对象都有必要的属性
                return {
                  id: foreshadow.id || uuidv4(),
                  title: foreshadow.title || '',
                  type: foreshadow.type || 'physical',
                  importance: typeof foreshadow.importance === 'number' ? foreshadow.importance : (parseInt(foreshadow.importance) || 2),
                  plantSegment: foreshadow.plantSegment || '',
                  payoffSegment: foreshadow.payoffSegment || '',
                  description: foreshadow.description || '',
                  alternativePlan: foreshadow.alternativePlan || '',
                  connectionTypes: Array.isArray(foreshadow.connectionTypes) ? foreshadow.connectionTypes : []
                };
              });
            } else {
              outlineData.foreshadows = [];
            }
            ElMessage.warning('未找到指定大纲，已加载最新大纲');
          }
        } else {
          // 使用最新的大纲数据
          Object.assign(outlineData, outlinesList.value[0]);
          // 确保伏笔数据的结构和类型正确
          if (outlineData.foreshadows) {
            // 过滤掉非对象类型的元素，并确保每个伏笔对象的结构正确
            outlineData.foreshadows = outlineData.foreshadows.filter(foreshadow => {
              return typeof foreshadow === 'object' && foreshadow !== null && !Array.isArray(foreshadow);
            }).map(foreshadow => {
              // 确保每个伏笔对象都有必要的属性
              return {
                id: foreshadow.id || uuidv4(),
                title: foreshadow.title || '',
                type: foreshadow.type || 'physical',
                importance: typeof foreshadow.importance === 'number' ? foreshadow.importance : (parseInt(foreshadow.importance) || 2),
                plantSegment: foreshadow.plantSegment || '',
                payoffSegment: foreshadow.payoffSegment || '',
                description: foreshadow.description || '',
                alternativePlan: foreshadow.alternativePlan || '',
                connectionTypes: Array.isArray(foreshadow.connectionTypes) ? foreshadow.connectionTypes : []
              };
            });
          } else {
            outlineData.foreshadows = [];
          }
          ElMessage.success('大纲数据加载成功');
        }
      } else {
        ElMessage.info('未找到现有大纲，使用新大纲');
      }
    } else {
      ElMessage.warning('加载大纲数据失败，使用空白大纲');
    }
    
    // 如果动态标签页为空且存在动态段落，选择第一个作为活动标签
    if (outlineData.dynamicSegments.length > 0) {
      // 仅在没有当前选中的标签页或当前标签页不存在时，才设置为第一个标签页
      if (!activeSegmentTab.value || !outlineData.dynamicSegments.some(seg => seg.id === activeSegmentTab.value)) {
        activeSegmentTab.value = outlineData.dynamicSegments[0].id;
      }
    }
    
    // 初始化图表
    if (activeExpertSection.value === 'emotion') {
      nextTick(() => {
        initEmotionChart();
      });
    }
  } catch (error) {
    console.error('加载大纲数据失败:', error);
    ElMessage.error('加载大纲数据失败: ' + error.message);
  } finally {
    isLoading.value = false;
  }
};

// 处理大纲选择
const handleOutlineSelect = (command) => {
  if (command === 'new') {
    createNewOutline();
  } else {
    loadOutlineData(command);
  }
};

// 创建新大纲
const createNewOutline = () => {
  ElMessageBox.confirm(
    '确定要创建新大纲吗？未保存的更改将丢失。',
    '创建新大纲',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 重置大纲数据
    Object.assign(outlineData, {
      id: '',
      timestamp: Date.now(),
      volumeTitle: '',
      volumeTheme: '',
      coreConflict: '',
      volumeFunction: 'escalation',
      // 只保留必要的字段
      foreshadows: [],
      storyFlow: [],
      dynamicSegments: []
    });
    
    ElMessage.success('已创建新大纲');
  }).catch(() => {
    // 用户取消操作
  });
};

// 确认删除大纲
const confirmDeleteOutline = (id = null) => {
  // 使用传递的ID或当前大纲ID
  const outlineId = id || outlineData.id;
  
  if (!outlineId) {
    ElMessage.warning('没有选择大纲或大纲尚未保存');
    return;
  }
  
  ElMessageBox.confirm(
    '确定要删除当前大纲吗？此操作不可恢复。',
    '删除大纲',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'danger'
    }
  ).then(async () => {
    try {
      isLoading.value = true;
      const responseStr = await window.pywebview.api.book_controller.delete_outline(outlineId);
      // 解析JSON字符串响应
      const response = JSON.parse(responseStr);
      
      if (response.status === "success") {
        ElMessage.success('大纲已删除');
        
        // 重新加载大纲列表并选择最新的一个
        await loadOutlineData();
      } else {
        ElMessage.error('删除失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('删除大纲失败:', error);
      ElMessage.error('删除大纲失败: ' + error.message);
    } finally {
      isLoading.value = false;
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};

// 添加组件销毁时的清理函数
const handleResize = () => {
  if (emotionChart) {
    emotionChart.resize();
  }
};

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  
  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeyDown);
  
  // 销毁图表实例
  if (emotionChart) {
    emotionChart.dispose();
    emotionChart = null;
  }
  
  if (overviewChart) {
    overviewChart.dispose();
    overviewChart = null;
  }
});

// 添加桥段计算属性
const bridgesFromA = computed(() => {
  return outlineData.storyFlow.filter(bridge => bridge.fromNode === 'A');
});

const bridgesFromC = computed(() => {
  return outlineData.storyFlow.filter(bridge => bridge.fromNode === 'C');
});

// 添加切换桥段激活状态的方法
const toggleBridgeActive = (bridgeId) => {
  const index = activeStoryFlowItems.value.indexOf(bridgeId);
  if (index === -1) {
    activeStoryFlowItems.value.push(bridgeId);
  } else {
    activeStoryFlowItems.value.splice(index, 1);
  }
};

// 添加桥段标签页状态
const activeBridgeTab = ref('frontBridge');

// 获取桥段类型的显示标签
const getBridgeTypeLabel = (type) => {
  const typeMap = {
    'normal': '通用桥段',
    'pivot': '转折桥段',
    'climax': '高潮桥段'
  };
  return typeMap[type] || '未知类型';
};

// 概览图表初始化
const overviewChartRef = ref(null);
let overviewChart = null;

const initOverviewChart = () => {
  if (!overviewChartRef.value) return;
  
  if (overviewChart) {
    overviewChart.dispose();
  }
  
  overviewChart = echarts.init(overviewChartRef.value);
  
  // 准备概览数据
  const emotionData = outlineData.dynamicSegments.map((seg, idx) => ({
    name: seg.title || `桥段 ${idx + 1}`,
    value: seg.emotionValue
  }));
  
  // 配置概览图表
  const option = {
    grid: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 40,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: emotionData.map(item => item.name)
    },
    yAxis: {
      type: 'value',
      name: '情绪值',
      min: 0,
      max: 100
    },
    series: [
      {
        name: '情绪曲线',
        type: 'line',
        data: emotionData.map(item => item.value),
        smooth: true,
        lineStyle: {
          width: 3
        },
        itemStyle: {
          color: '#409EFF'
        },
        symbolSize: 8
      }
    ]
  };
  
  overviewChart.setOption(option);
};

// 标签页激活状态
const activeSegmentTab = ref('');

// 新增辅助方法

// 根据伏笔位置获取对应的桥段标题
const getSegmentTitleByValue = (value) => {
  if (!value) return '';
  if (value === 'next_volume') return '后续卷回收';
  
  if (value.startsWith('DYN')) {
    // 提取桥段索引
    if (value.includes('-')) {
      // 子桥段格式：DYN0-1
      const [mainIndex, subIndex] = value.replace('DYN', '').split('-').map(Number);
      if (outlineData.dynamicSegments[mainIndex] && 
          outlineData.dynamicSegments[mainIndex].subSegments &&
          outlineData.dynamicSegments[mainIndex].subSegments[subIndex]) {
        const mainSeg = outlineData.dynamicSegments[mainIndex];
        const subSeg = mainSeg.subSegments[subIndex];
        return `${mainIndex + 1}.${subIndex + 1}: ${subSeg.title || '未命名子桥段'}`;
      }
    } else {
      // 主桥段格式：DYN0
      const index = Number(value.replace('DYN', ''));
      if (outlineData.dynamicSegments[index]) {
        const seg = outlineData.dynamicSegments[index];
        return `${index + 1}: ${seg.title || '未命名'} (${getBridgeTypeLabel(seg.type)})`;
      }
    }
  }
  
  return '未知位置';
};

// 获取桥段类型对应的标签类型
const getSegmentTagType = (value) => {
  if (!value || value === 'next_volume') return '';
  
  if (value.startsWith('DYN')) {
    if (value.includes('-')) {
      return 'success';  // 子桥段使用成功色
    } else {
      const index = Number(value.replace('DYN', ''));
      if (outlineData.dynamicSegments[index]) {
        const segType = outlineData.dynamicSegments[index].type;
        if (segType === 'pivot') return 'warning';
        if (segType === 'climax') return 'danger';
        return 'info';
      }
    }
  }
  
  return '';
};

// 计算两个桥段之间的跨度
const getSegmentSpan = (plantValue, payoffValue) => {
  if (!plantValue || !payoffValue || 
      plantValue === 'next_volume' || payoffValue === 'next_volume' ||
      !plantValue.startsWith('DYN') || !payoffValue.startsWith('DYN')) {
    return '未知';
  }
  
  let plantIndex = 0;
  let payoffIndex = 0;
  
  // 解析埋设位置索引
  if (plantValue.includes('-')) {
    plantIndex = Number(plantValue.replace('DYN', '').split('-')[0]);
  } else {
    plantIndex = Number(plantValue.replace('DYN', ''));
  }
  
  // 解析回收位置索引
  if (payoffValue.includes('-')) {
    payoffIndex = Number(payoffValue.replace('DYN', '').split('-')[0]);
  } else {
    payoffIndex = Number(payoffValue.replace('DYN', ''));
  }
  
  // 计算跨度
  return Math.abs(payoffIndex - plantIndex);
};

// 获取与此桥段关联的伏笔
const getRelatedForeshadows = (segmentId) => {
  return [
    ...getPlantedForeshadows(segmentId),
    ...getPayoffForeshadows(segmentId)
  ].filter((fs, index, self) => 
    index === self.findIndex(t => t.id === fs.id)
  );
};

// 获取与此桥段关联的植入伏笔
const getPlantedForeshadows = (segmentId) => {
  return outlineData.foreshadows.filter(fs => fs.plantSegment === segmentId);
};

// 获取与此桥段关联的回收伏笔
const getPayoffForeshadows = (segmentId) => {
  return outlineData.foreshadows.filter(fs => fs.payoffSegment === segmentId);
};

// 滚动到特定伏笔
const scrollToForeshadow = (id) => {
  // 先切换到伏笔管理标签
  activeExpertSection.value = 'foreshadow';
  
  // 延迟一下再滚动，确保DOM已更新
  nextTick(() => {
    const foreshadowElement = document.getElementById(`foreshadow-${id}`);
    if (foreshadowElement) {
      foreshadowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      
      // 添加一个临时高亮效果
      foreshadowElement.classList.add('foreshadow-highlight');
      setTimeout(() => {
        foreshadowElement.classList.remove('foreshadow-highlight');
      }, 2000);
    }
  });
};

// 已保存的大纲列表
const outlinesList = ref([]);
// 添加搜索功能
const outlineSearchQuery = ref('');

// 过滤后的大纲列表
const filteredOutlines = computed(() => {
  if (!outlineSearchQuery.value) {
    return outlinesList.value;
  }
  
  const query = outlineSearchQuery.value.toLowerCase();
  return outlinesList.value.filter(outline => {
    const title = (outline.volumeTitle || '未命名大纲').toLowerCase();
    return title.includes(query);
  });
});

// 情绪曲线分析相关计算方法
const getEmotionRange = () => {
  if (outlineData.dynamicSegments.length === 0) {
    return 0;
  }
  
  const emotions = outlineData.dynamicSegments.map(seg => seg.emotionValue);
  return Math.max(...emotions) - Math.min(...emotions);
};

const getEmotionRangePercentage = () => {
  return Math.round((getEmotionRange() / 100) * 100);
};

const getEmotionRangeColor = () => {
  const range = getEmotionRangePercentage();
  if (range < 30) return '#FF4500';
  if (range < 60) return '#FFD700';
  return '#32CD32';
};

const getClimaticPointStrength = () => {
  const climaxSegments = outlineData.dynamicSegments.filter(seg => seg.type === 'climax');
  if (climaxSegments.length === 0) return 0;
  
  const climaxEmotions = climaxSegments.map(seg => seg.emotionValue);
  return Math.round(climaxEmotions.reduce((a, b) => a + b, 0) / climaxEmotions.length);
};

const getClimaticPointColor = () => {
  const strength = getClimaticPointStrength();
  if (strength < 70) return '#FF4500';
  if (strength < 90) return '#FFD700';
  return '#32CD32';
};

const getPivotPointIntensity = () => {
  const pivotSegments = outlineData.dynamicSegments.filter(seg => seg.type === 'pivot');
  if (pivotSegments.length === 0) return 0;
  
  const normalSegments = outlineData.dynamicSegments.filter(seg => seg.type === 'normal');
  if (normalSegments.length === 0) return 0;
  
  const pivotEmotions = pivotSegments.map(seg => seg.emotionValue);
  const normalEmotions = normalSegments.map(seg => seg.emotionValue);
  
  const avgPivotEmotion = pivotEmotions.reduce((a, b) => a + b, 0) / pivotEmotions.length;
  const avgNormalEmotion = normalEmotions.reduce((a, b) => a + b, 0) / normalEmotions.length;
  
  return Math.round(Math.abs(avgPivotEmotion - avgNormalEmotion));
};

const getPivotPointColor = () => {
  const intensity = getPivotPointIntensity();
  if (intensity < 30) return '#FF4500';
  if (intensity < 60) return '#FFD700';
  return '#32CD32';
};

// 添加预览相关的新状态
const generatingPreview = ref(false);

// 导出预览为文本文件
const exportPreviewAsTxt = async () => {
  try {
    generatingPreview.value = true;
    
    let content = '';
    const title = outlineData.volumeTitle || '未命名卷';
    
    // 创建文本内容
    if (previewType.value) {
      // 专业格式
      content = generateProfessionalPreviewText();
    } else {
      // 普通格式
      content = generateSimplePreviewText();
    }
    
    // 先复制到剪贴板
    try {
      await window.pywebview.api.copy_to_clipboard(content);
      ElMessage.success('已复制到剪贴板');
    } catch (clipboardError) {
      console.error('复制到剪贴板失败:', clipboardError);
      ElMessage.warning('无法复制到剪贴板，将尝试下载文件');
    }
    
    // 创建Blob并下载
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = `${title}_大纲.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    ElMessage.success('大纲文本导出成功');
  } catch (error) {
    console.error('导出预览失败:', error);
    ElMessage.error('导出失败: ' + error.message);
  } finally {
    generatingPreview.value = false;
  }
};

// 生成专业格式预览文本
const generateProfessionalPreviewText = () => {
  let text = '';
  
  // 卷信息
  text += `【${outlineData.volumeTitle || '未命名卷'}】\n`;
  text += `====================\n`;
  text += `卷主题：${outlineData.volumeTheme || '无'}\n`;
  text += `核心冲突：${outlineData.coreConflict || '无'}\n`;
  text += `功能定位：${getVolumeFunctionLabel(outlineData.volumeFunction)}\n\n`;
  
  // 桥段结构
  text += `【桥段结构】\n`;
  text += `====================\n`;
  
  if (outlineData.dynamicSegments.length === 0) {
    text += `尚未创建桥段结构。\n\n`;
  } else {
    outlineData.dynamicSegments.forEach((segment, index) => {
      text += `${index + 1}. ${segment.title || `未命名桥段${index + 1}`} (${getBridgeTypeLabel(segment.type)})\n`;
      text += `-----------------\n`;
      if (segment.description) text += `描述：${segment.description}\n`;
      if (segment.goal) text += `目标：${segment.goal}\n`;
      if (segment.conflict) text += `冲突：${segment.conflict}\n`;
      if (segment.result) text += `结果：${segment.result}\n`;
      if (segment.input) text += `输入状态：${segment.input}\n`;
      if (segment.output) text += `输出状态：${segment.output}\n`;
      if (segment.foreshadowing) text += `伏笔/铺垫：${segment.foreshadowing}\n`;
      if (segment.characterStatus) text += `角色状态：${segment.characterStatus}\n`;
      if (segment.rhythm) text += `节奏设计：${segment.rhythm}\n`;
      
      // 子桥段
      if (segment.subSegments && segment.subSegments.length > 0) {
        text += `\n子桥段：\n`;
        segment.subSegments.forEach((subSeg, subIndex) => {
          text += `  ${index + 1}.${subIndex + 1} ${subSeg.title || `未命名子桥段${subIndex + 1}`}\n`;
          if (subSeg.goal) text += `    目标：${subSeg.goal}\n`;
          if (subSeg.conflict) text += `    冲突：${subSeg.conflict}\n`;
          if (subSeg.result) text += `    结果：${subSeg.result}\n`;
          if (subSeg.input) text += `    输入状态：${subSeg.input}\n`;
          if (subSeg.output) text += `    输出状态：${subSeg.output}\n`;
          if (subSeg.foreshadowing) text += `    伏笔/铺垫：${subSeg.foreshadowing}\n`;
          if (subSeg.rhythm) text += `    节奏设计：${subSeg.rhythm}\n`;
        });
      }
      
      text += `\n`;
    });
  }
  
  // 伏笔系统
  if (outlineData.foreshadows.length > 0) {
    text += `【伏笔系统】\n`;
    text += `====================\n`;
    
    outlineData.foreshadows.forEach((foreshadow, index) => {
      text += `${index + 1}. ${foreshadow.title || '未命名伏笔'} (${getForeshadowTypeName(foreshadow.type)})\n`;
      text += `-----------------\n`;
      if (foreshadow.description) text += `描述：${foreshadow.description}\n`;
      if (foreshadow.alternativePlan) text += `备选方案：${foreshadow.alternativePlan}\n`;
      
      const plantSegment = foreshadow.plantSegment ? getSegmentTitleByValue(foreshadow.plantSegment) : '未指定';
      const payoffSegment = foreshadow.payoffSegment ? getSegmentTitleByValue(foreshadow.payoffSegment) : '未指定';
      
      text += `埋设：${plantSegment} → 回收：${payoffSegment}\n\n`;
    });
  }
  
  return text;
};

// 生成普通格式预览文本
const generateSimplePreviewText = () => {
  let text = '';
  
  // 卷标题和基本信息
  text += `《${outlineData.volumeTitle || '未命名卷'}》\n\n`;
  
  if (outlineData.volumeTheme) text += `【主题】${outlineData.volumeTheme}\n`;
  if (outlineData.coreConflict) text += `【核心】${outlineData.coreConflict}\n\n`;
  
  // 简化的桥段结构
  if (outlineData.dynamicSegments.length === 0) {
    text += `尚未创建桥段结构。\n\n`;
  } else {
    outlineData.dynamicSegments.forEach((segment, index) => {
      text += `${index + 1}. ${segment.title || `未命名桥段${index + 1}`} (${getBridgeTypeLabel(segment.type)})\n`;
      
      if (segment.description) text += `   ${segment.description}\n`;
      if (segment.goal) text += `   目标：${segment.goal}\n`;
      if (segment.conflict) text += `   冲突：${segment.conflict}\n`;
      if (segment.result) text += `   结果：${segment.result}\n`;
      if (segment.input) text += `   输入状态：${segment.input}\n`;
      if (segment.output) text += `   输出状态：${segment.output}\n`;
      if (segment.foreshadowing) text += `   伏笔/铺垫：${segment.foreshadowing}\n`;
      if (segment.characterStatus) text += `   角色状态：${segment.characterStatus}\n`;
      
      // 简化的子桥段
      if (segment.subSegments && segment.subSegments.length > 0) {
        segment.subSegments.forEach((subSeg, subIndex) => {
          text += `   ${index + 1}.${subIndex + 1} ${subSeg.title || `子桥段${subIndex + 1}`}:\n`;
          if (subSeg.goal) text += `     目标：${subSeg.goal}\n`;
          if (subSeg.conflict) text += `     冲突：${subSeg.conflict}\n`;
          if (subSeg.result) text += `     结果：${subSeg.result}\n`;
          if (subSeg.input) text += `     输入状态：${subSeg.input}\n`;
          if (subSeg.output) text += `     输出状态：${subSeg.output}\n`;
          if (subSeg.foreshadowing) text += `     伏笔/铺垫：${subSeg.foreshadowing}\n`;
        });
      }
      
      text += '\n';
    });
  }
  
  // 简化的伏笔列表
  if (outlineData.foreshadows.length > 0) {
    text += `【伏笔列表】\n`;
    outlineData.foreshadows.forEach((foreshadow, index) => {
      text += `${index + 1}. ${foreshadow.title || '未命名伏笔'}: ${foreshadow.description || '无描述'}\n`;
      if (foreshadow.alternativePlan) text += `   备选方案：${foreshadow.alternativePlan}\n`;
      
      const plantSegment = foreshadow.plantSegment ? getSegmentTitleByValue(foreshadow.plantSegment) : '未指定';
      const payoffSegment = foreshadow.payoffSegment ? getSegmentTitleByValue(foreshadow.payoffSegment) : '未指定';
      
      text += `   埋设位置：${plantSegment} → 回收位置：${payoffSegment}\n\n`;
    });
  }
  
  return text;
};

// 获取卷功能的标签文本
const getVolumeFunctionLabel = (function_key) => {
  const functionMap = {
    'introduction': '开篇引入',
    'escalation': '冲突升级',
    'climax': '高潮转折',
    'resolution': '结局收束',
    'transition': '过渡铺垫'
  };
  return functionMap[function_key] || function_key;
};
</script>

<style scoped>
.history-search :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  transition: all 0.3s;
}

.history-search :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary-light-5) inset;
}

.history-search :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}
.outline-creator {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: var(--el-bg-color);
}

.outline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 64px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  z-index: 10;
  user-select: none !important;
}

.outline-header .left-section h1 {
  font-size: 1.5rem;
  margin: 0;
}

.outline-header .right-section {
  display: flex;
  gap: 10px;
}

.outline-main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.outline-content-layout {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.outline-nav-sidebar {
  width: 220px;
  height: 100%;
  border-right: 1px solid var(--el-border-color-light);
  padding: 20px 0;
  background-color: var(--el-bg-color-light);
  overflow-y: auto;
  flex-shrink: 0;
}

.outline-content-area {
  flex: 1;
  padding: 20px;
  position: relative;
  height: 100%;
  overflow: hidden;
}

/* 删除expert-content-scrollable，不再需要 */

/* 各个专家模式部分的样式 */
.expert-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保内部滚动正常工作 */
}

/* 结构设计部分特殊处理 */
.structure-section {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 动态桥段系统样式调整 */
.dynamic-bridge-system {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dynamic-segments-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dynamic-segments-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.segment-contents-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

/* 伏笔管理滚动容器 */
.foreshadow-scrollable-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 5px;
  margin: 0 -5px;
}

/* 侧边栏样式 */
.sidebar-section {
  margin-bottom: 24px;
}

.sidebar-title {
  padding: 0 16px;
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
  user-select: none !important;
}

.sidebar-menu li:hover {
  background-color: var(--el-fill-color-light);
}

.sidebar-menu li.active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border-left-color: var(--el-color-primary);
}

/* 内容区头部 */
.section-header {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--el-bg-color);
  padding-top: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-light);
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

/* 基本信息卡片样式 */
.basic-info-card {
  padding: 10px;
}

.basic-info-card .el-form-item {
  margin-bottom: 20px;
}

.basic-info-card .el-form-item__label {
  font-weight: 500;
  padding-bottom: 6px;
}

/* 标签页组件样式 */
.tabs-header-fixed {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  background-color: var(--el-bg-color);
  z-index: 11;
  padding-bottom: 16px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 桥段类型样式 */
.segment-type-normal {
  border-left: 4px solid var(--el-color-info);
}

.segment-type-pivot {
  border-left: 4px solid var(--el-color-warning);
}

.segment-type-climax {
  border-left: 4px solid var(--el-color-danger);
}

/* 桥段内容样式 */
.segment-content {
  padding: 15px;
  background-color: var(--el-fill-color-light);
}

.segment-main-info {
  padding: 16px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.segment-row {
  margin-bottom: 18px;
}

.segment-row:last-child {
  margin-bottom: 0;
}

.segment-row .el-form-item {
  margin-bottom: 0;
}

.segment-row .el-form-item__label {
  padding-bottom: 4px;
  line-height: 1.3;
  font-weight: 500;
}

.segment-compact-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: center;
  margin-bottom: 18px;
}

.segment-compact-row .el-form-item {
  margin-bottom: 0;
}

.segment-main-info .el-select,
.segment-main-info .el-input {
  width: 100%;
}

/* 标签页样式 */
.segment-tabs {
  width: 100%;
  --el-tabs-header-height: auto;
}

.segment-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
  border-bottom: none;
}

.segment-tabs :deep(.el-tabs__nav-wrap) {
  margin-bottom: 0 !important;
}

.segment-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.segment-tabs :deep(.el-tabs__nav) {
  border: none !important;
  border-radius: 8px;
  background: transparent;
  padding: 0;
  display: flex;
}

.segment-tabs :deep(.el-tabs__item) {
  border: 1px solid var(--el-border-color) !important;
  border-radius: 6px !important;
  height: auto;
  line-height: 1.4;
  padding: 8px 12px !important;
  transition: all 0.3s ease;
  margin: 0 4px 0 0 !important;
  color: var(--el-text-color-regular);
  background: var(--el-bg-color-page);
  box-shadow: 0 1px 3px rgba(0,0,0,0.04);
  position: relative;
  overflow: hidden;
}

/* 根据桥段类型设置标签页背景颜色 */
.segment-tabs :deep(.el-tabs__item .segment-tab-label.segment-type-normal) {
  
  position: relative;
  border-left: none;
}

.segment-tabs :deep(.el-tabs__item .segment-tab-label.segment-type-pivot) {
  background: linear-gradient(45deg, var(--el-color-warning-light-9), var(--el-bg-color-page));
  position: relative;
  border-left: none;
}

.segment-tabs :deep(.el-tabs__item .segment-tab-label.segment-type-climax) {
  background: linear-gradient(45deg, var(--el-color-danger-light-9), var(--el-bg-color-page));
  position: relative;
  border-left: none;
}

/* 标签页左侧边框样式 */
.segment-tabs :deep(.el-tabs__item .segment-tab-label.segment-type-normal)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--el-color-info);
}

.segment-tabs :deep(.el-tabs__item .segment-tab-label.segment-type-pivot)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--el-color-warning);
}

.segment-tabs :deep(.el-tabs__item .segment-tab-label.segment-type-climax)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--el-color-danger);
}

.segment-tabs :deep(.el-tabs__item.is-active) {
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary-dark-2);
  font-weight: 500;
  border-color: var(--el-color-primary-light-3) !important;
  box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.2);
}

/* 活动标签页特殊样式 */
.segment-tabs :deep(.el-tabs__item.is-active .segment-tab-label.segment-type-normal) {
  background: var(--el-color-primary);
  color: #e0e0e0;
}

.segment-tabs :deep(.el-tabs__item.is-active .segment-tab-label.segment-type-pivot) {
  background: linear-gradient(45deg, var(--el-color-warning-light-8), var(--el-color-warning-light-9));
  color: var(--el-color-warning-dark-2);
}

.segment-tabs :deep(.el-tabs__item.is-active .segment-tab-label.segment-type-climax) {
  background: linear-gradient(45deg, var(--el-color-danger-light-8), var(--el-color-danger-light-9));
  color: var(--el-color-danger-dark-2);
}

/* 优化删除按钮 */
.segment-tabs :deep(.el-tabs__item .el-icon-close) {
  width: 18px;
  height: 18px;
  font-size: 14px;
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  vertical-align: middle;
  transition: all 0.2s ease;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -1px;
}

.segment-tabs :deep(.el-tabs__item .el-icon-close:hover) {
  background-color: var(--el-color-danger-light-7);
  color: white;
  transform: scale(1.1);
}

.segment-tabs :deep(.el-tabs__item.is-active .el-icon-close) {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--el-text-color-primary);
}

.segment-tabs :deep(.el-tabs__item.is-active .el-icon-close:hover) {
  background-color: var(--el-color-danger);
  color: white;
}

/* 标签内容样式 */
.segment-tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 6px;
  line-height: 1.4;
  border-radius: 4px;
}

.segment-tab-index {
  font-weight: 600;
  font-size: 13px;
  padding: 3px 7px;
  border-radius: 4px;
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
  min-width: 22px;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.segment-tabs :deep(.el-tabs__item.is-active) .segment-tab-index {
  background-color: var(--el-color-primary-light-5);
  color: var(--el-color-primary-dark-2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 为不同类型的桥段标签设置不同颜色的序号样式 */
.segment-type-normal .segment-tab-index {
  background-color: rgba(var(--el-color-info-rgb), 0.1);
  color: var(--el-color-info-dark-2);
}

.segment-type-pivot .segment-tab-index {
  background-color: rgba(var(--el-color-warning-rgb), 0.15);
  color: var(--el-color-warning-dark-2);
}

.segment-type-climax .segment-tab-index {
  background-color: rgba(var(--el-color-danger-rgb), 0.15);
  color: var(--el-color-danger-dark-2);
}

.segment-title {
  margin: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  font-weight: 500;
}

.segment-type-badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: normal;
  white-space: nowrap;
}

.segment-tabs :deep(.el-tabs__item.is-active) .segment-type-badge {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

.segment-tabs :deep(.el-tabs__item.is-active .segment-tab-label.segment-type-normal .segment-type-badge) {
  background-color: var(--el-color-success) !important;
  color: #ffffff !important;
}

/* 桥段徽章颜色 */
.segment-type-normal .segment-type-badge {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.segment-type-pivot .segment-type-badge {
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.segment-type-climax .segment-type-badge {
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

/* 子桥段部分 */
.sub-segments-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.segments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.segments-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.sub-segments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sub-segment-card {
  background-color: var(--el-bg-color);
  border-radius: 6px;
  padding: 18px;
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.3s ease;
}

.sub-segment-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.segment-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.segment-card-header .segment-index {
  font-size: 1.1rem;
}

.segment-index {
  font-weight: 600;
  color: var(--el-color-primary);
  font-size: 16px;
}

.sub-segment-card .el-form-item__label {
  font-weight: 500;
  padding-bottom: 4px;
}

.sub-segment-card .el-select,
.sub-segment-card .el-input {
  width: 100%;
}

/* 空状态 */
.empty-segments {
  padding: 20px;
  text-align: center;
  background-color: var(--el-fill-color-lightest);
  border-radius: 6px;
  margin-top: 10px;
}

.empty-segments .el-empty__description {
  margin-top: 10px;
}

/* 插入桥段按钮 */
.insert-segment-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px dashed var(--el-border-color-light);
}

.insert-segment-button {
  width: auto;
  padding: 8px 20px;
  border-style: dashed;
  opacity: 0.8;
  color: var(--el-text-color-secondary);
  border-color: var(--el-border-color-base);
}

.insert-segment-button:hover {
  opacity: 1;
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* 伏笔管理样式 */
.foreshadow-card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 22px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.foreshadow-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  border-color: var(--el-color-primary-light-5);
  transform: translateY(-2px);
}

.foreshadow-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: var(--el-color-primary-light-5);
  transition: all 0.3s ease;
}

.foreshadow-card:hover::before {
  background: var(--el-color-primary);
}

.foreshadow-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 14px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.foreshadow-title {
  flex: 1;
  margin-right: 16px;
}

.foreshadow-title .el-input .el-input__inner {
  font-size: 1.1rem;
  font-weight: 600;
  height: 42px;
  border-radius: 6px;
  transition: all 0.3s;
}

.foreshadow-title .el-input .el-input__inner:focus {
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.foreshadow-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 22px;
}

.foreshadow-detail-item .el-form-item__label {
  font-weight: 500;
  padding-bottom: 6px;
  color: var(--el-text-color-primary);
  font-size: 0.95rem;
}

.foreshadow-detail-item .el-select {
  width: 100%;
}

.foreshadow-detail-item {
  margin-bottom: 16px;
}

.foreshadow-description {
  grid-column: 1 / -1;
  margin-top: 8px;
  background-color: var(--el-fill-color-lighter);
  padding: 14px;
  border-radius: 6px;
  border-left: 4px solid var(--el-color-info-light-5);
  transition: all 0.3s ease;
}

.foreshadow-description:hover {
  background-color: var(--el-fill-color-light);
  border-left-color: var(--el-color-info);
}

.foreshadow-description .el-form-item {
  margin-bottom: 0;
}

.foreshadow-description .el-form-item__label {
  font-weight: 600;
  padding-bottom: 6px;
  color: var(--el-text-color-primary);
}

.foreshadow-description .el-textarea__inner {
  background-color: var(--el-bg-color);
  transition: all 0.3s ease;
  border-color: var(--el-border-color);
  font-size: 0.95rem;
  padding: 10px 12px;
  border-radius: 4px;
  resize: vertical;
  min-height: 90px;
}

.foreshadow-description .el-textarea__inner:focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.foreshadow-description:nth-child(2n) {
  border-left-color: var(--el-color-success-light-5);
}

.foreshadow-description:nth-child(2n):hover {
  border-left-color: var(--el-color-success);
}

/* 高亮动画 */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--el-color-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0);
  }
}

.foreshadow-highlight {
  animation: highlight-pulse 2s ease-in-out;
  border-color: var(--el-color-primary) !important;
  background-color: var(--el-color-primary-light-9) !important;
}

.foreshadow-description {
  margin-top: 8px;
}

.foreshadow-description .el-form-item__label {
  font-weight: 500;
  padding-bottom: 4px;
}

.expert-foreshadow-list {
  max-height: none;
  width: 100%;
  overflow: visible;
}

.foreshadow-list {
  max-height: none;
  width: 100%;
  overflow: visible;
}

/* 情绪曲线图表 */
.emotion-curve-card {
  padding: 10px;
}

.emotion-curve-container {
  margin-top: 0;
}

.emotion-chart-wrapper {
  width: 100%;
  height: 380px;
  position: relative;
  margin-bottom: 20px;
}

.emotion-chart {
  width: 100%;
  height: 100%;
}

/* 大纲预览卡片 */
.outline-preview-card {
  padding: 10px;
}

/* 辅助样式 */
.form-helper-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

/* 列表过渡动画 */
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .outline-header {
    flex-direction: column;
    height: auto;
    padding: 10px;
    gap: 10px;
  }
  
  .outline-header .right-section {
    width: 100%;
    justify-content: space-between;
  }
  
  .action-buttons {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .outline-content-layout {
    flex-direction: column;
  }
  
  .outline-nav-sidebar {
    width: 100%;
    height: auto;
    max-height: 180px;
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-light);
    padding: 10px;
  }
  
  .outline-content-area {
    padding: 10px;
  }
  
  .emotion-chart-wrapper {
    height: 250px;
  }
  
  .foreshadow-details-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-height: 600px) {
  .outline-header {
    padding: 5px 10px;
  }
  
  .outline-header .left-section h1 {
    font-size: 1.2rem;
  }
  
  .emotion-chart-wrapper {
    height: 200px;
  }
}

/* 伏笔选项样式 */
.foreshadow-segment-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 0;
}

.foreshadow-segment-option.segment-type-normal {
  border-left: 3px solid var(--el-color-info);
  padding-left: 8px;
}

.foreshadow-segment-option.segment-type-pivot {
  border-left: 3px solid var(--el-color-warning);
  padding-left: 8px;
}

.foreshadow-segment-option.segment-type-climax {
  border-left: 3px solid var(--el-color-danger);
  padding-left: 8px;
}

.foreshadow-segment-option.sub-segment {
  border-left: 3px solid var(--el-color-success);
  padding-left: 8px;
  font-size: 0.95em;
}

.segment-option-index {
  font-weight: bold;
  background-color: var(--el-fill-color-light);
  border-radius: 3px;
  min-width: 24px;
  text-align: center;
  padding: 0 4px;
}

.segment-option-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.segment-option-badge {
  font-size: 11px;
  padding: 1px 6px;
  border-radius: 10px;
  color: #fff;
  background-color: var(--el-color-info);
}

.segment-type-normal .segment-option-badge {
  background-color: var(--el-color-info);
}

.segment-type-pivot .segment-option-badge {
  background-color: var(--el-color-warning);
}

.segment-type-climax .segment-option-badge {
  background-color: var(--el-color-danger);
}

/* 伏笔和桥段关联提示 */
.foreshadow-bridge-relation {
  margin-top: 5px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  display: flex;
  align-items: center;
}

.foreshadow-bridge-relation .el-icon {
  margin-right: 4px;
  color: var(--el-color-primary);
}

/* 伏笔与桥段关联样式 */
.foreshadow-segment-links {
  margin-top: 20px;
  padding: 16px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--el-border-color-light);
}

.foreshadow-links-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 14px;
  color: var(--el-color-primary);
  font-size: 1rem;
}

.foreshadow-links-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.foreshadow-link-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed var(--el-border-color-lighter);
}

.foreshadow-link-item:last-child {
  border-bottom: none;
}

.link-title {
  width: 90px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.link-content {
  flex: 1;
}

/* 伏笔标签样式优化 */
.foreshadow-tag {
  margin-right: 8px;
  margin-bottom: 5px;
  cursor: pointer;
  border-radius: 4px;
  padding: 0 8px;
  height: 28px;
  line-height: 26px;
  font-size: 13px;
  transition: all 0.3s ease;
}

.foreshadow-tag.plant-tag {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success-light-5);
  color: var(--el-color-success-dark-2);
}

.foreshadow-tag.plant-tag:hover {
  background-color: var(--el-color-success-light-8);
  border-color: var(--el-color-success-light-3);
  transform: translateY(-1px);
}

.foreshadow-tag.payoff-tag {
  background-color: var(--el-color-warning-light-9);
  border-color: var(--el-color-warning-light-5);
  color: var(--el-color-warning-dark-2);
}

.foreshadow-tag.payoff-tag:hover {
  background-color: var(--el-color-warning-light-8);
  border-color: var(--el-color-warning-light-3);
  transform: translateY(-1px);
}

/* 伏笔关联样式 */
.related-foreshadows {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.foreshadow-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
}

.foreshadow-group-title {
  font-weight: 500;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.foreshadow-group-title .el-icon {
  font-size: 16px;
  color: var(--el-color-primary);
}

/* 伏笔植入和回收的标题样式区分 */
.foreshadow-group:first-child .foreshadow-group-title .el-icon {
  color: var(--el-color-success);
}

.foreshadow-group:last-child .foreshadow-group-title .el-icon {
  color: var(--el-color-warning);
}

/* 备选方案特殊样式 */
.foreshadow-details-grid .foreshadow-description:last-child {
  border-left-color: var(--el-color-warning-light-5);
  background-color: var(--el-fill-color-light);
}

.foreshadow-details-grid .foreshadow-description:last-child:hover {
  border-left-color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}

.foreshadow-details-grid .foreshadow-description:last-child .el-form-item__label {
  color: var(--el-color-warning-dark-2);
}

/* 高亮动画 */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--el-color-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0);
  }
}

.foreshadow-highlight {
  animation: highlight-pulse 2s ease-in-out;
  border-color: var(--el-color-primary) !important;
  background-color: var(--el-color-primary-light-9) !important;
}

/* 添加卡片入场动画 */
@keyframes card-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.foreshadow-card {
  animation: card-fade-in 0.4s ease-out;
}

/* 为伏笔卡片添加渐变背景效果 */
.foreshadow-card {
  background-image: linear-gradient(
    to bottom, 
    var(--el-bg-color-overlay), 
    var(--el-bg-color)
  );
}

/* 增强伏笔标签交互效果 */
.foreshadow-tag {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.foreshadow-tag:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* 主题颜色支持 - 添加以下CSS */
/* 暗色主题支持 */
html.dark .foreshadow-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

html.dark .foreshadow-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

html.dark .foreshadow-description {
  background-color: rgba(0, 0, 0, 0.15);
}

html.dark .foreshadow-description:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

html.dark .foreshadow-segment-links {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 伏笔类型颜色差异化 */
.foreshadow-card[data-type="physical"]::before {
  background: var(--el-color-primary-light-5);
}

.foreshadow-card[data-type="information"]::before {
  background: var(--el-color-success-light-5);
}

.foreshadow-card[data-type="ability"]::before {
  background: var(--el-color-warning-light-5);
}

.foreshadow-card[data-type="relationship"]::before {
  background: var(--el-color-danger-light-5);
}

.foreshadow-card[data-type="environment"]::before {
  background: var(--el-color-info-light-5);
}

.foreshadow-card[data-type="physical"]:hover::before {
  background: var(--el-color-primary);
}

.foreshadow-card[data-type="information"]:hover::before {
  background: var(--el-color-success);
}

.foreshadow-card[data-type="ability"]:hover::before {
  background: var(--el-color-warning);
}

.foreshadow-card[data-type="relationship"]:hover::before {
  background: var(--el-color-danger);
}

.foreshadow-card[data-type="environment"]:hover::before {
  background: var(--el-color-info);
}

/* 根据伏笔重要程度调整样式 */
.foreshadow-card[data-importance="1"] {
  border-width: 1px;
}

.foreshadow-card[data-importance="2"] {
  border-width: 1px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.06);
}

.foreshadow-card[data-importance="3"] {
  border-width: 2px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
}

/* 输入框获焦样式增强 */
.foreshadow-description .el-textarea:focus-within {
  position: relative;
}

.foreshadow-description .el-textarea:focus-within::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  bottom: -2px;
  left: -2px;
  border-radius: 5px;
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
  pointer-events: none;
  z-index: 2;
}

/* 空状态 */
.empty-foreshadows {
  padding: 40px 20px;
  text-align: center;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  border: 1px dashed var(--el-border-color);
  margin-top: 20px;
  transition: all 0.3s;
}

.empty-foreshadows:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-color-primary-light-7);
}

.empty-foreshadows .el-button {
  margin-top: 16px;
}

/* 表单项提示样式 */
.form-helper-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 6px;
  line-height: 1.4;
}

/* 添加到CSS部分的样式 */
.delete-button-wrapper {
  display: flex;
  align-items: center;
}

.delete-foreshadow-btn {
  border-radius: 6px;
  height: 40px;
  width: 40px;
  padding: 8px;
  transition: all 0.2s ease;
  margin-left: 12px;
  border: 1px solid var(--el-color-danger-light-5);
}

.delete-foreshadow-btn:hover {
  background-color: var(--el-color-danger) !important;
  color: white !important;
  transform: scale(1.05);
  box-shadow: 0 2px 6px rgba(var(--el-color-danger-rgb), 0.3);
}

.delete-foreshadow-btn .el-icon {
  font-weight: bold;
}

.segment-tabs :deep(.el-tabs__item:last-child) {
  margin-right: 0 !important;
}

/* 增强非活动标签页的hover效果 */
.segment-tabs :deep(.el-tabs__item:not(.is-active):hover) {
  border-color: var(--el-color-primary-light-3);
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

/* 增强标签页中的标题在hover时的效果 */
.segment-tabs :deep(.el-tabs__item:hover) .segment-title {
  color: var(--el-color-primary);
}

/* 大纲头部样式 */
.outline-header .left-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 大纲选择器样式 */
.outline-selector-wrapper {
  position: relative;
  margin-left: 16px;
}

.outline-selector {
  cursor: pointer;
  font-size: 15px;
  background: var(--el-fill-color-light);
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 200px;
  border: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.outline-selector:hover {
  background: var(--el-fill-color);
  border-color: var(--el-border-color);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.current-outline-info {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
}

.current-outline-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  max-width: 200px;
}

.current-tag {
  flex-shrink: 0;
}

.selector-icon {
  margin-left: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  transition: transform 0.3s;
}

/* 大纲历史面板样式 */
.outline-history-panel {
  padding: 12px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.panel-title {
  font-weight: 600;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.history-search {
  margin-bottom: 16px;
}

.history-list-container {
  max-height: 350px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color) transparent;
}

.history-list-container::-webkit-scrollbar {
  width: 4px;
}

.history-list-container::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color);
  border-radius: 2px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  padding: 14px 16px;
  border-radius: 8px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.history-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: transparent;
  transition: all 0.25s;
}

.history-item:hover {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-5);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.history-item:hover::before {
  background-color: var(--el-color-primary);
}

.history-item.active {
  background: var(--el-color-primary-light-8);
  border-color: var(--el-color-primary-light-5);
  box-shadow: 0 4px 10px rgba(var(--el-color-primary-rgb), 0.15);
}

.history-item.active::before {
  background-color: var(--el-color-primary);
}

.item-content {
  flex: 1;
  overflow: hidden;
  padding-right: 16px;
}

.item-title {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  font-size: 15px;
  line-height: 1.4;
}

.item-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.item-timestamp {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.item-timestamp .el-icon {
  font-size: 14px;
  color: var(--el-color-info);
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}

.item-info .el-tag {
  font-size: 11px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color-light);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  transition: all 0.3s;
}

.history-item:hover .item-info .el-tag {
  background-color: var(--el-color-primary-light-8);
  border-color: var(--el-color-primary-light-5);
  color: var(--el-color-primary-dark-2);
}

.item-actions {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  align-self: flex-start;
}

.item-actions .el-button {
  border-radius: 50%;
  width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.history-item:hover .item-actions {
  opacity: 1;
}

.history-item .item-actions .el-button:hover {
  transform: scale(1.1);
  background-color: var(--el-color-danger);
  color: white;
  border-color: var(--el-color-danger);
}

.empty-history {
  padding: 30px 0;
  text-align: center;
}

/* 使用deep修改el-popover样式 */
:deep(.outline-history-popover) {
  padding: 0;
  width: 360px !important; /* 强制固定宽度 */
  border-radius: 10px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.16);
  border: 1px solid var(--el-border-color);
  overflow: hidden;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  :deep(.outline-history-popover) {
    width: 300px !important;
  }
  
  .outline-header .left-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .outline-header h1 {
    font-size: 1.3rem;
  }
  
  .outline-selector {
    font-size: 14px;
    padding: 4px 12px;
    width: 100%;
  }
  
  .outline-selector-wrapper {
    width: 100%;
    margin-left: 0;
  }
  
  .current-outline-title {
    max-width: calc(100% - 100px);
  }

  .preview-content {
    max-height: calc(100vh - 180px);
    padding: 15px;
  }

  .preview-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
  }
}

/* 情绪曲线分析 */
.emotion-analysis-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

/* 左侧图表区域 */
.emotion-chart-area {
  flex: 1;
}

/* 右侧分析区域 */
.emotion-analysis-area {
  flex: 1;
}

/* 情绪分析卡片 */
.emotion-analysis-card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 情绪曲线可视化 */
.emotion-curve-card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 情绪曲线可视化头部 */
.emotion-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 情绪曲线可视化标题 */
.emotion-card-header span {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 情绪曲线可视化副标题 */
.emotion-card-header .el-tag {
  font-size: 0.9rem;
  padding: 4px 8px;
  background-color: var(--el-color-info);
  color: var(--el-color-info-contrast-text);
}

/* 情绪曲线图表 */
.emotion-curve-container {
  margin-top: 20px;
}

/* 情绪曲线图表 */
.emotion-chart-wrapper {
  width: 100%;
  height: 300px;
  position: relative;
  margin-bottom: 20px;
}

/* 情绪曲线图表 */
.emotion-chart {
  width: 100%;
  height: 100%;
}

/* 情绪曲线图表 */
.emotion-legend {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

/* 情绪曲线图例项 */
.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 情绪曲线图例颜色 */
.legend-color {
  width: 20px;
  height: 10px;
  border-radius: 4px;
}

/* 情绪曲线图例标签 */
.legend-item span {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 情绪分析卡片 */
.emotion-analysis-card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 情绪分析卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 情绪分析卡片标题 */
.card-header span {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 情绪分析卡片副标题 */
.card-header .el-tag {
  font-size: 0.9rem;
  padding: 4px 8px;
  background-color: var(--el-color-success);
  color: var(--el-color-success-contrast-text);
}

/* 情绪分析内容 */
.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 情绪分析文本 */
.analysis-text {
  font-size: 1.1rem;
  color: var(--el-text-color-primary);
}

/* 情绪分析指标 */
.emotion-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

/* 情绪分析指标项 */
.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 情绪分析指标标签 */
.metric-label {
  font-weight: 500;
  color: var(--el-text-color-secondary);
}

/* 情绪分析进度条 */
.el-progress {
  width: 100%;
}

/* 情绪曲线贴士 */
.emotion-tips-card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 情绪曲线贴士内容 */
.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 情绪贴士项 */
.emotion-tip-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 情绪贴士图标 */
.emotion-tip-item .el-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

/* 情绪贴士文本 */
.emotion-tip-item p {
  font-size: 1.1rem;
  color: var(--el-text-color-primary);
}

/* 情绪标签释义 */
.emotion-labels-card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 情绪标签释义内容 */
.emotion-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

/* 情绪标签项 */
.emotion-label-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 情绪标签范围 */
.emotion-label-range {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 情绪标签描述 */
.emotion-label-desc {
  font-size: 1.1rem;
  color: var(--el-text-color-secondary);
}

/* 情绪标签范围颜色 */
.emotion-label-range[data-range="very-low"] {
  color: var(--el-color-danger);
}

.emotion-label-range[data-range="low"] {
  color: var(--el-color-warning);
}

.emotion-label-range[data-range="medium"] {
  color: var(--el-color-info);
}

.emotion-label-range[data-range="high"] {
  color: var(--el-color-success);
}

.emotion-label-range[data-range="very-high"] {
  color: var(--el-color-success);
}

/* 子桥段情绪曲线颜色 */
.legend-color-sub {
  background: linear-gradient(45deg, #67C23A, #409EFF);
  border-radius: 4px;
}

/* 情绪控制按钮组 */
.emotion-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 响应式布局 */
@media (max-width: 992px) {
  .emotion-analysis-container {
    flex-direction: column;
  }
  
  .emotion-chart-area, 
  .emotion-analysis-area {
    width: 100%;
  }
  
  .emotion-chart-wrapper {
    height: 250px;
  }
}

/* 情绪曲线分析 */
.emotion-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 情绪曲线卡片 */
.emotion-curve-card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

/* 情绪曲线卡片头部 */
.emotion-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 情绪曲线卡片标题 */
.emotion-card-header span {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 情绪曲线图表容器 */
.emotion-curve-container {
  padding: 20px;
}

/* 情绪曲线图表包装器 */
.emotion-chart-wrapper {
  width: 100%;
  height: 360px;
  position: relative;
  margin-bottom: 20px;
}

/* 情绪曲线图表 */
.emotion-chart {
  width: 100%;
  height: 100%;
}

/* 情绪曲线图例 */
.emotion-legend {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  margin-top: 16px;
}

/* 图例项 */
.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 图例颜色块 */
.legend-color {
  width: 20px;
  height: 10px;
  border-radius: 3px;
}

/* 子桥段图例颜色 */
.legend-color-sub {
  background: linear-gradient(45deg, #67C23A, #409EFF);
  border-radius: 3px;
}

/* 图例文本 */
.legend-item span {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 大纲预览样式 */
.outline-preview-card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;

}

.preview-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-card-header span {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.preview-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 专业预览样式 */
.preview-content {
  padding: 20px;
  padding-bottom: 40px; /* 增加底部内边距，确保最底部内容可见 */
  max-height: calc(100vh - 230px); /* 增加减去的高度，留出更多底部空间 */
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color) transparent;
}

.preview-content::-webkit-scrollbar {
  width: 6px;
}

.preview-content::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color);
  border-radius: 3px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--el-border-color-darker);
}

.professional-preview {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-bottom: 50px; /* 添加底部外边距 */
}

.preview-volume-info {
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 20px;
}

.preview-title {
  font-size: 1.8rem;
  margin: 0 0 20px 0;
  color: var(--el-text-color-primary);
  text-align: center;
}

.preview-meta {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.meta-item {
  display: flex;
  gap: 10px;
}

.meta-label {
  font-weight: 600;
  color: var(--el-text-color-secondary);
  width: 100px;
  flex-shrink: 0;
}

.preview-section-title {
  font-size: 1.4rem;
  margin: 0 0 20px 0;
  color: var(--el-color-primary);
  border-bottom: 2px solid var(--el-color-primary-light-7);
  padding-bottom: 10px;
}

.preview-segments {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.preview-segment {
  background-color: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid var(--el-color-info);
  margin-bottom: 16px;
}

.preview-segment[class*="segment-type-pivot"] {
  border-left-color: var(--el-color-warning);
}

.preview-segment[class*="segment-type-climax"] {
  border-left-color: var(--el-color-danger);
}

.segment-header {
  margin-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 10px;
}

.segment-header-top {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.segment-index {
  background-color: var(--el-color-info-light-8);
  color: var(--el-color-info);
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.segment-type-badge {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 4px;
  color: #fff;
}

.segment-type-normal .segment-type-badge {
  background-color: var(--el-color-info);
}

.segment-type-pivot .segment-type-badge {
  background-color: var(--el-color-warning);
}

.segment-type-climax .segment-type-badge {
  background-color: var(--el-color-danger);
}

.segment-title {
  margin: 0;
  font-size: 1.2rem;
}

.segment-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.segment-property {
  display: flex;
  gap: 8px;
}

.property-label {
  font-weight: 600;
  color: var(--el-text-color-secondary);
  width: 60px;
  flex-shrink: 0;
}

.property-value {
  flex: 1;
  color: var(--el-text-color-primary);
}

.segment-subsegments {
  margin-top: 16px;
  border-top: 1px dashed var(--el-border-color);
  padding-top: 16px;
}

.subsegments-title {
  font-size: 1rem;
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.subsegment-item {
  background-color: var(--el-fill-color-light);
  border-radius: 6px;
  padding: 10px 16px;
  margin-bottom: 10px;
}

.subsegment-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 6px;
}

.subsegment-index {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--el-color-success);
}

.subsegment-body {
  color: var(--el-text-color-regular);
  font-size: 0.9rem;
}

/* 伏笔预览样式 */
.preview-foreshadows {
  margin-top: 20px;
}

.foreshadows-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-foreshadow {
  background-color: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid var(--el-color-primary-light-5);
}

.foreshadow-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 10px;
}

.foreshadow-index {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.foreshadow-title {
  flex: 1;
  font-weight: 600;
  font-size: 1.1rem;
}

.foreshadow-type {
  color: var(--el-text-color-secondary);
  font-size: 0.9rem;
  background-color: var(--el-fill-color);
  padding: 2px 8px;
  border-radius: 4px;
}

.foreshadow-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.foreshadow-desc {
  color: var(--el-text-color-primary);
}

.foreshadow-flow {
  font-size: 0.9rem;
  color: var(--el-text-color-secondary);
}

/* 简单预览样式 */
.simple-preview {
  color: var(--el-text-color-primary);
  margin-bottom: 80px; /* 为简单预览也添加底部外边距 */
}

.preview-description {
  color: var(--el-text-color-regular);
  margin-bottom: 20px;
  line-height: 1.6;
}

.preview-segments-simple {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-segment-simple {
  margin-bottom: 16px;
}

.segment-title-simple {
  font-size: 1.2rem;
  margin: 0 0 10px 0;
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.segment-type-badge-simple {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 4px;
  color: var(--el-text-color-secondary);
  background-color: var(--el-fill-color-light);
}

.segment-content-simple {
  margin: 10px 0;
  line-height: 1.6;
}

.segment-flow-simple {
  color: var(--el-text-color-secondary);
  font-style: italic;
  margin: 10px 0;
}

.subsegments-simple {
  margin: 16px 0;
  padding-left: 20px;
}

.subsegment-item-simple {
  margin-bottom: 10px;
  line-height: 1.4;
}

.foreshadows-simple {
  margin-top: 30px;
  border-top: 1px solid var(--el-border-color-light);
  padding-top: 20px;
}

.foreshadows-title-simple {
  margin: 0 0 16px 0;
  font-size: 1.2rem;
}

.foreshadows-list-simple {
  padding-left: 20px;
}

.foreshadows-list-simple li {
  margin-bottom: 10px;
  line-height: 1.6;
}

/* 空状态 */
.empty-preview {
  text-align: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);
  font-style: italic;
  background-color: var(--el-fill-color-light);
  border-radius: 6px;
}

/* 为其他区域添加不可选中文本样式 */
.section-header h3,
.outline-nav-sidebar .sidebar-title,
.tabs-header-fixed,
.segment-tabs :deep(.el-tabs__item),
.legend-item span,
.emotion-card-header {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 指定不可选择文本的样式 */
.user-select-none {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 底部结束标识 */
.preview-end-indicator {
  margin-top: 30px;
  margin-bottom: 20px;
  color: var(--el-text-color-secondary);
}

.preview-end-indicator .el-divider {
  margin: 0;
}

.preview-end-indicator .el-divider__text {
  font-size: 0.9rem;
  color: var(--el-text-color-secondary);
  background-color: var(--el-bg-color-overlay);
  padding: 0 16px;
}

/* 伏笔预览样式增强 */
.foreshadow-property {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.foreshadow-property-label {
  font-weight: 600;
  color: var(--el-text-color-secondary);
  width: 80px;
  flex-shrink: 0;
}

.foreshadow-property-value {
  flex: 1;
  color: var(--el-text-color-primary);
}

.flow-label {
  font-weight: 600;
  color: var(--el-text-color-secondary);
}

/* 子桥段属性样式 */
.subsegment-property {
  margin-bottom: 4px;
  color: var(--el-text-color-primary);
}

.subsegment-property-label {
  font-weight: 600;
  color: var(--el-text-color-secondary);
  margin-right: 6px;
}

/* 简单预览样式增强 */
.simple-property-label {
  margin-right: 6px;
  color: var(--el-text-color-secondary);
}

.subsegment-simple-property {
  margin: 4px 0 4px 20px;
  color: var(--el-text-color-primary);
}

.foreshadow-simple-property {
  margin: 4px 0 4px 20px;
  color: var(--el-text-color-primary);
}
</style>
