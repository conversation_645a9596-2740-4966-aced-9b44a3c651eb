import mimetypes
import os.path
import json
import base64
import re
from backend.bridge.Base import ResponsePacket, BaseManager
from email import message_from_string, message_from_bytes
from email.policy import default
import quopri
import uuid
from urllib.parse import urlparse, unquote
import hashlib
import shutil
import logging


class LocalController(BaseManager):
    def __init__(self, base_dir):
        self.base_dir = base_dir
        self.task_dir = os.path.join(base_dir, "local")
        super().__init__(base_dir, "local")
        self.local_manager = LocalManager(self.task_dir, self.base_dir, "local")

    # 获取指定目录下面的MHTML文件结构
    def load_dir_mhtml(self, dir_path=None):
        """获取指定目录下的MHTML文件结构"""
        if dir_path is None:
            dir_path = self.task_dir
        response = self.local_manager.get_directory_structure(dir_path)
        # 确保返回格式一致，不要返回字符串
        if isinstance(response, str):
            try:
                return json.loads(response)
            except:
                # 如果不是有效的JSON，则包装为标准响应格式
                return {
                    "success": False,
                    "message": "解析目录结构失败",
                    "data": None
                }
        return response
    
    # 获取MHTML文件内容
    def get_mhtml_content(self, file_path):
        """获取MHTML文件内容及其批注"""
        return self.local_manager.read_mhtml_file(file_path)
    
    # 保存带批注的MHTML文件
    def save_mhtml_with_notes(self, file_path, content, notes):
        """保存MHTML文件的批注信息"""
        return self.local_manager.save_mhtml_with_notes(file_path, content, notes)

    # 新增：获取转换后的HTML内容
    def get_mhtml_as_html(self, file_path):
        """将MHTML文件转换为HTML内容"""
        return self.local_manager.convert_mhtml_to_html(file_path)

    # 新增：将MHTML转换为可显示的HTML (包含处理资源)
    def process_mhtml_to_html(self, file_path, charset='utf-8'):
        """处理MHTML文件并提取HTML内容，处理字符集和资源引用"""
        try:
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
                
            print(f"处理MHTML文件: {file_path}, 使用字符集: {charset}")
            
            # 处理非标准编码名
            if charset.startswith('3d'):
                charset = charset[2:]
            if charset == 'utf8':
                charset = 'utf-8'
            if charset == 'gb2312':
                charset = 'gbk'
            
            with open(file_path, 'rb') as f:
                mhtml_content = f.read()
            
            print(f"文件大小: {len(mhtml_content)} 字节")
            
            # 直接创建基础HTML用于显示原始内容的fallback
            base64_content = base64.b64encode(mhtml_content).decode('ascii')
            fallback_html = self._create_fallback_view(mhtml_content, charset)
            
            # 尝试检测字符集
            try:
                detected_charset = self._detect_charset(mhtml_content) or charset
                print(f"检测到字符集: {detected_charset}")
            except Exception as e:
                print(f"字符集检测失败，使用指定字符集: {charset}")
                detected_charset = charset
            
            # 尝试提取HTML
            try:
                html_content, extracted_resources = self._extract_html_and_resources(mhtml_content, detected_charset)
                
                if not html_content:
                    print("警告: 无法提取HTML内容，使用fallback视图")
                    return self._success_response("使用基本视图", {
                        'html': fallback_html,
                        'suggestedCharset': detected_charset,
                        'resourceCount': 0,
                        'usedFallback': True
                    })
                
                # 处理资源引用
                processed_html = self._process_resource_references(html_content, extracted_resources)
                
                return self._success_response("处理成功", {
                    'html': processed_html,
                    'suggestedCharset': detected_charset,
                    'resourceCount': len(extracted_resources),
                    'usedFallback': False
                })
            except Exception as e:
                print(f"HTML提取失败，使用fallback视图: {str(e)}")
                import traceback
                traceback.print_exc()
                
                return self._success_response("使用基本视图", {
                    'html': fallback_html,
                    'suggestedCharset': charset,
                    'resourceCount': 0,
                    'usedFallback': True,
                    'error': str(e)
                })
            
        except Exception as e:
            print(f"处理MHTML失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
            # 即使处理失败，也返回一个基本视图
            try:
                with open(file_path, 'rb') as f:
                    mhtml_content = f.read()
                fallback_html = self._create_fallback_view(mhtml_content, charset)
                return self._success_response("使用应急视图", {
                    'html': fallback_html,
                    'suggestedCharset': charset,
                    'error': str(e),
                    'usedFallback': True
                })
            except:
                return self._error_response(f"处理MHTML失败: {str(e)}")
    
    def _detect_charset(self, mhtml_bytes):
        """尝试从MHTML内容检测字符集并处理非标准编码名称"""
        try:
            # 尝试从Content-Type头部获取字符集
            mhtml_start = mhtml_bytes[:4000].decode('ascii', errors='ignore')
            charset_match = re.search(r'charset="?([^"\s;]+)"?', mhtml_start, re.IGNORECASE)
            if charset_match:
                detected = charset_match.group(1).lower()
                # 处理非标准编码名
                if detected.startswith('3d'):  # 如"3dutf-8"
                    detected = detected[2:]  # 移除开头的"3d"
                if detected == 'utf8':
                    detected = 'utf-8'
                if detected == 'gb2312':
                    detected = 'gbk'  # 使用gbk替代gb2312，兼容性更好
                
                print(f"检测到可能的字符集: {detected}")
                return detected
            
            # 查找GB系列编码的特征
            if b'gb2312' in mhtml_bytes.lower() or b'gbk' in mhtml_bytes.lower():
                return 'gbk'
            
            # 尝试检测中文内容
            try:
                sample = mhtml_bytes.decode('utf-8', errors='ignore')
                if re.search(r'[\u4e00-\u9fff]', sample):
                    return 'utf-8'
            except:
                pass
                
            try:
                sample = mhtml_bytes.decode('gbk', errors='ignore')
                if re.search(r'[\u4e00-\u9fff]', sample):
                    return 'gbk'
            except:
                pass
                
            return None
        except Exception as e:
            print(f"检测字符集失败: {str(e)}")
            return None
    
    def _extract_html_and_resources(self, mhtml_bytes, charset='utf-8'):
        """使用简化但更稳定的方法从MHTML中提取HTML内容"""
        try:
            # 尝试使用指定字符集解码整个文件
            try:
                mhtml_text = mhtml_bytes.decode(charset, errors='replace')
            except UnicodeDecodeError:
                # 如果失败，尝试其他常见中文编码
                for test_charset in ['utf-8', 'gbk', 'gb2312', 'big5']:
                    if test_charset == charset:
                        continue
                    try:
                        mhtml_text = mhtml_bytes.decode(test_charset, errors='replace')
                        charset = test_charset
                        print(f"成功使用{test_charset}解码")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    # 如果所有编码都失败，使用ascii强制解码
                    mhtml_text = mhtml_bytes.decode('ascii', errors='replace')
                
            print(f"MHTML文件已使用{charset}解码，文本长度：{len(mhtml_text)}")
            
            # 提取HTML部分 - 方法1：找到完整HTML标签
            html_match = re.search(r'<html[\s\S]*?</html>', mhtml_text, re.IGNORECASE)
            if html_match:
                html_content = html_match.group(0)
                print("已找到完整HTML标签")
                return html_content, {}
            
            # 方法2：寻找Content-Type: text/html之后的内容
            content_type_match = re.search(r'Content-Type:\s*text/html.*?\r?\n\r?\n([\s\S]*?)(?:\r?\n--|--)(?:$|\r?\n)', 
                                           mhtml_text, re.IGNORECASE)
            if content_type_match:
                html_part = content_type_match.group(1).strip()
                if html_part:
                    print("已通过Content-Type提取HTML内容")
                    # 尝试找到<html>标签，如果没有则添加
                    if not re.search(r'<html', html_part, re.IGNORECASE):
                        html_content = f"<html><head><meta charset='{charset}'></head><body>{html_part}</body></html>"
                    else:
                        html_content = html_part
                    return html_content, {}
            
            # 方法3：寻找<body>标签
            body_match = re.search(r'<body[\s\S]*?</body>', mhtml_text, re.IGNORECASE)
            if body_match:
                body_content = body_match.group(0)
                html_content = f"<html><head><meta charset='{charset}'></head>{body_content}</html>"
                print("已提取<body>标签部分")
                return html_content, {}
            
            # 方法4：寻找最长的非二进制内容部分并显示
            parts = re.split(r'\r?\n--[\w-]+\r?\n', mhtml_text)
            max_text_part = ""
            for part in parts:
                # 检查是否包含文本内容的特征
                if 'Content-Type: text/' in part and len(part) > len(max_text_part):
                    # 获取内容部分
                    content_match = re.search(r'\r?\n\r?\n([\s\S]+)', part)
                    if content_match:
                        max_text_part = content_match.group(1)
            
            if max_text_part:
                print("已提取最长文本部分")
                html_content = f"""
                <html>
                <head><meta charset='{charset}'></head>
                <body>
                    <pre style="white-space: pre-wrap; word-break: break-all; font-family: Arial, sans-serif;">
                    {max_text_part}
                    </pre>
                </body>
                </html>
                """
                return html_content, {}
            
            # 最后的方法：返回部分原始内容以直观显示
            preview = mhtml_text[:20000]  # 显示前20000个字符
            html_content = f"""
            <html>
            <head><meta charset='{charset}'></head>
            <body>
                <h1>MHTML文件内容预览</h1>
                <p>无法提取结构化HTML，显示原始内容预览：</p>
                <pre style="white-space: pre-wrap; word-break: break-all; font-family: monospace; background: #f5f5f5; padding: 10px; border: 1px solid #ddd;">
                {preview}...
                </pre>
            </body>
            </html>
            """
            print("无法提取结构化内容，返回原始预览")
            return html_content, {}
            
        except Exception as e:
            print(f"提取HTML失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
            # 返回错误信息
            error_html = f"""
            <html>
            <head><meta charset="utf-8"></head>
            <body>
                <h1>解析MHTML文件出错</h1>
                <p>错误信息: {str(e)}</p>
                <p>请尝试切换字符集或查看原始内容。</p>
            </body>
            </html>
            """
            return error_html, {}
    
    def _process_resource_references(self, html_content, resources):
        """简化的HTML处理方法"""
        # 添加基本的样式和脚本
        wrapped_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <title>MHTML查看器</title>
            <style>
                body {{ 
                    font-family: Arial, sans-serif; 
                    line-height: 1.6; 
                    padding: 20px;
                    margin: 0;
                    color: #333;
                    background: #fff;
                }}
                img {{ max-width: 100%; height: auto; }}
                pre {{ white-space: pre-wrap; word-break: break-all; background: #f8f8f8; padding: 10px; border: 1px solid #ddd; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 1em; }}
                td, th {{ border: 1px solid #ddd; padding: 8px; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                h1, h2, h3 {{ color: #333; }}
                a {{ color: #0066cc; text-decoration: none; }}
                a:hover {{ text-decoration: underline; }}
            </style>
        </head>
        <body>
            {html_content}
            <script>
                // 处理点击事件
                document.addEventListener('click', function(e) {{
                    if (e.target.tagName === 'A') {{
                        e.preventDefault();
                    }}
                }}, true);
                
                // 处理图片错误
                document.querySelectorAll('img').forEach(img => {{
                    img.onerror = function() {{
                        this.style.border = '1px dashed #ccc';
                        this.style.padding = '10px';
                        this.style.display = 'inline-block';
                        this.style.background = '#f8f8f8';
                        this.alt = '图片加载失败';
                    }};
                }});
                
                // 通知父窗口加载完成
                setTimeout(function() {{
                    window.parent.postMessage('mhtml-loaded', '*');
                }}, 500);
            </script>
        </body>
        </html>
        """
        return wrapped_html

    def get_file_url(self, file_path):
        """获取文件的本地URL，用于直接在浏览器中打开"""
        try:
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
            
            # 确保使用绝对路径
            abs_path = os.path.abspath(file_path)
            
            # 构建file://协议URL (注意：Windows系统需要三个斜杠)
            file_url = f"file:///{abs_path.replace(os.sep, '/')}"
            
            return self._success_response("成功获取文件URL", {
                "url": file_url,
                "path": abs_path
            })
        except Exception as e:
            print(f"获取文件URL失败: {str(e)}")
            return self._error_response(f"获取文件URL失败: {str(e)}")

    def _create_fallback_view(self, mhtml_bytes, charset='utf-8'):
        """创建一个基本的fallback视图，显示文件内容"""
        try:
            # 尝试转换为纯文本
            try:
                # 尝试直接解码
                text_content = mhtml_bytes.decode(charset, errors='replace')
            except Exception:
                # 如果失败，尝试以二进制方式处理
                text_content = mhtml_bytes.decode('ascii', errors='replace')
            
            # 只保留可见文本
            filtered_content = ''.join(c for c in text_content if c.isprintable() or c in '\r\n\t')
            
            # 基本HTML模板
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>MHTML内容</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }}
                    .warning {{ background: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 20px; }}
                    pre {{ white-space: pre-wrap; word-break: break-all; background: #f8f8f8; padding: 15px; border: 1px solid #ddd; max-height: 500px; overflow: auto; }}
                </style>
            </head>
            <body>
                <div class="warning">
                    <h2>解析MHTML文件出错</h2>
                    <p>无法完全解析此MHTML文件。以下是文件的基本文本内容：</p>
                    <p>请尝试切换到其他字符集或使用"简单文本模式"查看。</p>
                </div>
                
                <h3>文件内容预览：</h3>
                <pre>{filtered_content[:50000]}...</pre>
                
                <script>
                    // 通知父窗口加载完成
                    setTimeout(function() {{
                        window.parent.postMessage('mhtml-loaded', '*');
                    }}, 500);
                </script>
            </body>
            </html>
            """
            return html
        except Exception as e:
            print(f"创建fallback视图失败: {str(e)}")
            return f"""
            <html><body>
                <h1>无法读取文件内容</h1>
                <p>错误: {str(e)}</p>
            </body></html>
            """

    def extract_mhtml(self, file_path, output_dir=None, clear_output=False):
        """提取MHTML文件内容到指定目录"""
        try:
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
            
            # 如果没有指定输出目录，使用文件名作为目录名
            if not output_dir:
                file_name = os.path.splitext(os.path.basename(file_path))[0]
                output_dir = os.path.join(os.path.dirname(file_path), file_name + "_extracted")
            
            print(f"提取MHTML文件: {file_path} 到 {output_dir}")
            
            extractor = MHTMLExtractor(
                mhtml_path=file_path,
                output_dir=output_dir,
                clear_output_dir=clear_output
            )
            
            main_html = extractor.extract()
            
            if main_html:
                main_html_path = os.path.join(output_dir, main_html)
                return self._success_response("MHTML提取成功", {
                    "outputDir": output_dir,
                    "mainHtmlFile": main_html,
                    "mainHtmlPath": main_html_path,
                    "fileCount": extractor.extracted_count - 1
                })
            else:
                return self._error_response("无法提取主HTML文件")
        except Exception as e:
            print(f"提取MHTML失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return self._error_response(f"提取MHTML失败: {str(e)}")

    # 添加打开目录和文件的方法
    def open_directory(self, directory_path):
        """使用系统默认文件浏览器打开目录"""
        try:
            if not os.path.exists(directory_path):
                return self._error_response("目录不存在")
            
            import subprocess
            import platform
            
            system = platform.system()
            
            if system == 'Windows':
                os.startfile(directory_path)
            elif system == 'Darwin':  # macOS
                subprocess.run(['open', directory_path])
            else:  # Linux
                subprocess.run(['xdg-open', directory_path])
            
            return self._success_response("成功打开目录")
        except Exception as e:
            print(f"打开目录失败: {str(e)}")
            return self._error_response(f"打开目录失败: {str(e)}")

    def open_file(self, file_path):
        """使用系统默认程序打开文件"""
        try:
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
            
            import subprocess
            import platform
            
            system = platform.system()
            
            if system == 'Windows':
                os.startfile(file_path)
            elif system == 'Darwin':  # macOS
                subprocess.run(['open', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', file_path])
            
            return self._success_response("成功打开文件")
        except Exception as e:
            print(f"打开文件失败: {str(e)}")
            return self._error_response(f"打开文件失败: {str(e)}")

    def improve_mhtml_rendering(self, file_path, charset='utf-8'):
        """增强型MHTML渲染方法，优化内部显示效果"""
        try:
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
            
            # 处理编码问题
            if charset.startswith('3d'):
                charset = charset[2:]
            if charset == 'utf8':
                charset = 'utf-8'
            
            with open(file_path, 'rb') as f:
                mhtml_content = f.read()
            
            # 使用更可靠的方式获取编码
            detected_charset = self._detect_charset(mhtml_content) or charset
            
            # 第一阶段：资源提取
            resources = {}
            main_html = None
            
            # 创建临时目录存储提取的资源
            temp_dir = os.path.join(os.path.dirname(file_path), 
                                    f".temp_{os.path.basename(file_path).replace('.', '_')}")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            
            # 解析MHTML格式
            try:
                msg = message_from_bytes(mhtml_content, policy=default)
                
                # 处理所有部分
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_id = part.get('Content-ID', '')
                    content_location = part.get('Content-Location', '')
                    
                    # 跳过多部分
                    if content_type.startswith('multipart/'):
                        continue
                    
                    # 获取内容
                    try:
                        payload = part.get_payload(decode=True)
                    except:
                        payload = part.get_payload(decode=False)
                        if isinstance(payload, str):
                            payload = payload.encode('utf-8')
                    
                    # 创建资源ID
                    resource_id = None
                    if content_id:
                        # 处理Content-ID
                        if content_id.startswith('<') and content_id.endswith('>'):
                            content_id = content_id[1:-1]
                        resource_id = f"cid:{content_id}"
                    elif content_location:
                        resource_id = content_location
                    else:
                        resource_id = f"resource_{len(resources)}"
                    
                    # HTML内容特殊处理
                    if content_type == 'text/html':
                        try:
                            # 尝试不同编码解码HTML
                            for try_charset in [detected_charset, 'utf-8', 'gbk', 'big5', 'latin1']:
                                try:
                                    html_content = payload.decode(try_charset, errors='replace')
                                    main_html = html_content
                                    # 保存原始内容到资源dict
                                    resources[resource_id] = {
                                        'type': content_type,
                                        'content': html_content,
                                        'isMain': True
                                    }
                                    break
                                except:
                                    continue
                        except Exception as e:
                            print(f"解码HTML失败: {e}")
                    else:
                        # 其他资源处理
                        try:
                            # 数据转为base64
                            base64_data = base64.b64encode(payload).decode('ascii')
                            data_uri = f"data:{content_type};base64,{base64_data}"
                            resources[resource_id] = {
                                'type': content_type,
                                'content': data_uri,
                                'isMain': False
                            }
                        except Exception as e:
                            print(f"处理资源失败: {e}, 类型: {content_type}")
            
            except Exception as e:
                print(f"解析MHTML结构失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 如果无法获取主HTML，尝试备用方法
            if not main_html:
                # 尝试直接从文本中提取
                try:
                    text_content = mhtml_content.decode(detected_charset, errors='replace')
                    html_match = re.search(r'<html[\s\S]*?</html>', text_content, re.IGNORECASE)
                    if html_match:
                        main_html = html_match.group(0)
                except:
                    pass
            
            # 如果仍然无法获取HTML，创建一个简单的HTML
            if not main_html:
                main_html = f"""
                <html>
                <head><title>MHTML查看器</title></head>
                <body>
                    <h1>无法解析此MHTML文件的HTML内容</h1>
                    <p>请尝试使用不同的字符集或提取方法。</p>
                </body>
                </html>
                """
            
            # 第二阶段：资源引用处理
            processed_html = main_html
            
            # 处理cid资源引用
            cid_refs = re.findall(r'cid:([^"\'\s>]+)', processed_html)
            for cid in cid_refs:
                resource_id = f"cid:{cid}"
                if resource_id in resources:
                    processed_html = processed_html.replace(f'cid:{cid}', resources[resource_id]['content'])
            
            # 处理URL资源引用
            for resource_id, resource in resources.items():
                if not resource['isMain'] and resource_id.startswith('http'):
                    processed_html = processed_html.replace(resource_id, resource['content'])
            
            # 第三阶段：HTML增强

            enhanced_html= ""
            return self._success_response("MHTML渲染成功", {
                'html': enhanced_html,
                'charset': detected_charset,
                'resourceCount': len(resources)
            })
            
        except Exception as e:
            print(f"MHTML渲染失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return self._error_response(f"MHTML渲染失败: {str(e)}")


# 执行具体的代码，上面主要用来调用的
class LocalManager(BaseManager):
    def __init__(self, task_dir, base_dir, directory_name):
        # 把当前项目活动的目录拿到，这个全部是和本地化的界面相关的
        super().__init__(base_dir, directory_name)
        self.task_dir = task_dir
        # 确保目录存在
        os.makedirs(self.task_dir, exist_ok=True)

    def get_directory_structure(self, dir_path):
        """获取目录的树形结构，重点关注.mhtml文件"""
        result = []
        try:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                
            items = os.listdir(dir_path)
            for item in items:
                # 跳过隐藏文件和批注文件
                if item.startswith('.') or item.endswith('.notes.json'):
                    continue
                    
                item_path = os.path.join(dir_path, item)
                is_dir = os.path.isdir(item_path)
                if is_dir:
                    # 处理目录
                    children_response = self.get_directory_structure(item_path)
                    # 解析返回的JSON字符串为Python对象
                    if isinstance(children_response, str):
                        children_response = json.loads(children_response)
                    
                    if children_response.get('success') and children_response.get('data'):
                        # 只添加非空目录
                        result.append({
                            'name': item,
                            'path': item_path,
                            'type': 'directory',
                            'children': children_response.get('data')
                        })
                elif item.lower().endswith('.mhtml'):
                    # 处理MHTML文件
                    result.append({
                        'name': item,
                        'path': item_path,
                        'type': 'file',
                        'fileType': 'mhtml',
                        'hasNotes': self._has_notes(item_path)
                    })
            return self._success_response("获取目录结构成功", result)
        except Exception as e:
            return self._error_response(f"读取目录失败: {str(e)}")
    
    def read_mhtml_file(self, file_path):
        """读取MHTML文件内容及其批注"""
        try:
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
                
            with open(file_path, 'rb') as f:
                content = f.read()
                # 检查文件内容的前几个字节，确保是MHTML格式
                print(f"MHTML头部: {content[:50]}")
                
                # 使用base64编码，前端更容易处理二进制数据
                content_base64 = base64.b64encode(content).decode('utf-8')
                
                # 获取关联的批注（如果有）
                notes = self._get_notes(file_path)
                
                return self._success_response("文件读取成功", {
                    'content': content_base64,
                    'notes': notes,
                    'fileSize': len(content)  # 添加文件大小信息
                })
        except Exception as e:
            print(f"读取MHTML文件失败: {str(e)}")
            return self._error_response(f"读取MHTML文件失败: {str(e)}")
    
    def save_mhtml_with_notes(self, file_path, content=None, notes=None):
        """保存MHTML文件批注，可选择同时更新文件内容"""
        try:
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
                
            # 如果提供了文件内容，直接更新原始文件
            if content:
                try:
                    content_bytes = base64.b64decode(content)
                    # 创建备份文件
                    backup_path = file_path + ".bak"
                    if os.path.exists(file_path):
                        import shutil
                        shutil.copy2(file_path, backup_path)
                    
                    # 写入新内容
                    with open(file_path, 'wb') as f:
                        f.write(content_bytes)
                except Exception as e:
                    return self._error_response(f"更新文件内容失败: {str(e)}")
            
            # 保存批注到与MHTML文件相同目录
            if notes is not None:
                try:
                    self._save_notes(file_path, notes)
                except Exception as e:
                    return self._error_response(f"保存批注数据失败: {str(e)}")
                
            return self._success_response("批注保存成功", {"message": "文件和批注已保存"})
        except Exception as e:
            return self._error_response(f"保存操作失败: {str(e)}")
    
    def _get_notes_path(self, file_path):
        """获取批注文件路径 - 保存在与原文件相同的目录"""
        file_dir = os.path.dirname(file_path)
        file_name = os.path.basename(file_path)
        notes_file = f"{file_name}.notes.json"
        return os.path.join(file_dir, notes_file)
    
    def _has_notes(self, file_path):
        """检查文件是否有批注"""
        notes_path = self._get_notes_path(file_path)
        return os.path.exists(notes_path)
    
    def _get_notes(self, file_path):
        """获取文件批注"""
        notes_path = self._get_notes_path(file_path)
        if os.path.exists(notes_path):
            try:
                with open(notes_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return []
        return []
    
    def _save_notes(self, file_path, notes):
        """保存文件批注到原文件同目录"""
        notes_path = self._get_notes_path(file_path)
        with open(notes_path, 'w', encoding='utf-8') as f:
            json.dump(notes, f, ensure_ascii=False, indent=2)

    def test(self):
        """测试方法"""
        self._save_json()
        return self._success_response("测试成功")

    def convert_mhtml_to_html(self, file_path):
        """将MHTML文件转换为纯HTML内容"""
        try:
            if not os.path.exists(file_path):
                return self._error_response("文件不存在")
                
            with open(file_path, 'rb') as f:
                mhtml_content = f.read()
                
            # 提取HTML部分
            html_content = self._extract_html_from_mhtml(mhtml_content)
            
            if not html_content:
                return self._error_response("无法从MHTML提取HTML内容")
                
            # 可能需要进一步处理CSS和图片等资源...
            
            return self._success_response("转换成功", {
                'html': html_content
            })
        except Exception as e:
            print(f"转换MHTML失败: {str(e)}")
            return self._error_response(f"转换MHTML失败: {str(e)}")
    
    def _extract_html_from_mhtml(self, mhtml_bytes):
        try:
            # 转换为文本
            mhtml_str = mhtml_bytes.decode('utf-8', errors='ignore')
            
            # 查找分界线
            boundary_match = re.search(r'boundary="(.*?)"', mhtml_str, re.IGNORECASE)
            if boundary_match:
                boundary = boundary_match.group(1)
                
                # 查找text/html部分
                html_part_pattern = re.compile(
                    r'Content-Type: text/html.*?\r?\n\r?\n(.*?)(?:--' + 
                    re.escape(boundary) + '|$)', 
                    re.DOTALL | re.IGNORECASE
                )
                
                html_match = html_part_pattern.search(mhtml_str)
                if html_match:
                    return html_match.group(1).strip()
            
            # 直接查找HTML标签
            html_tag_match = re.search(r'<html\b[^>]*>[\s\S]*?</html>', mhtml_str, re.DOTALL | re.IGNORECASE)
            if html_tag_match:
                return html_tag_match.group(0)
                
            # 返回整个内容
            return mhtml_str
            
        except Exception as e:
            print(f"提取HTML失败: {str(e)}")
            return None


# 添加MHTML提取器类到LocalController.py
class MHTMLExtractor:
    """提取MHTML文件内容到单独的文件"""
    
    def __init__(self, mhtml_path, output_dir, buffer_size=8192, clear_output_dir=False):
        self.mhtml_path = mhtml_path
        self.output_dir = output_dir
        self.buffer_size = buffer_size
        self.boundary = None
        self.extracted_count = 0
        self.url_mapping = {}  # 原始URL到新文件名的映射
        self.saved_html_files = []  # 已保存的HTML文件列表
        
        self.ensure_directory_exists(self.output_dir, clear_output_dir)
    
    def ensure_directory_exists(self, directory_path, clear=False):
        try:
            if not os.path.exists(directory_path):
                os.makedirs(directory_path)
            elif clear:
                for filename in os.listdir(directory_path):
                    file_path = os.path.join(directory_path, filename)
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
        except Exception as e:
            print(f"目录设置错误: {e}")
    
    def _read_boundary(self, temp_buffer):
        """从MHTML头部提取边界字符串"""
        try:
            boundary_match = re.search(r'boundary="([^"]+)"', temp_buffer)
            if boundary_match:
                return boundary_match.group(1)
        except Exception as e:
            print(f"读取边界失败: {e}")
        return None
    
    def _decode_body(self, encoding, body):
        """根据Content-Transfer-Encoding解码内容"""
        try:
            if encoding == "base64":
                return base64.b64decode(body)
            elif encoding == "quoted-printable":
                return quopri.decodestring(body)
        except Exception as e:
            print(f"解码内容失败: {e}")
            return body
        return body
    
    def _extract_filename(self, headers, content_type):
        """根据头信息确定文件名"""
        try:
            content_location_match = re.search(r"Content-Location: ([^\n]+)", headers)
            extension = mimetypes.guess_extension(content_type) or ""
            
            if content_location_match:
                location = content_location_match.group(1)
                parsed_url = urlparse(location)
                base_name = os.path.basename(unquote(parsed_url.path)) or parsed_url.netloc
                url_hash = hashlib.md5(location.encode()).hexdigest()
                
                filename = f"{base_name}_{url_hash}{extension}"
                original_filename = filename
                counter = 1
                
                while os.path.exists(os.path.join(self.output_dir, filename)):
                    filename = f"{original_filename}_{counter}"
                    counter += 1
            else:
                filename = str(uuid.uuid4()) + extension
            return filename
        except Exception as e:
            print(f"提取文件名失败: {e}")
            return str(uuid.uuid4())
    
    def _process_part(self, part, no_css=False, no_images=False, html_only=False):
        """处理MHTML的每个部分并提取内容"""
        try:
            headers, body = part.strip().split("\n\n", 1)
            
            content_type_match = re.search(r"Content-Type: ([^\n]+)", headers, re.IGNORECASE)
            content_transfer_encoding_match = re.search(r"Content-Transfer-Encoding: ([^\n]+)", headers, re.IGNORECASE)
            content_location_match = re.search(r"Content-Location: ([^\n]+)", headers, re.IGNORECASE)
            content_id_match = re.search(r"Content-ID: <([^>]+)>", headers, re.IGNORECASE)
            
            if not content_type_match:
                return
            
            content_type = content_type_match.group(1).split(";")[0].strip()
            
            if no_css and "css" in content_type:
                return
            if no_images and ("image" in content_type or "img" in content_type):
                return
            if html_only and "html" not in content_type:
                return
            
            encoding = None
            if content_transfer_encoding_match:
                encoding = content_transfer_encoding_match.group(1).strip().lower()
            
            decoded_body = self._decode_body(encoding, body)
            
            filename = self._extract_filename(headers, content_type)
            
            if content_location_match:
                location = content_location_match.group(1)
                self.url_mapping[location] = filename
            
            if content_id_match:
                cid = "cid:" + content_id_match.group(1)
                self.url_mapping[cid] = filename
            
            self._write_to_file(filename, content_type, decoded_body)
        except Exception as e:
            print(f"处理MHTML部分失败: {e}")
    
    def _write_to_file(self, filename, content_type, decoded_body):
        """将解码的内容写入文件"""
        if isinstance(decoded_body, str):
            decoded_body = bytes(decoded_body, encoding="utf-8")
        
        if "html" in content_type:
            self.saved_html_files.append(filename)
        
        with open(os.path.join(self.output_dir, filename), "wb") as out_file:
            out_file.write(decoded_body)
    
    def _update_html_links(self, filepath, sorted_urls, hash_pattern, no_css=False, no_images=False, html_only=False):
        """更新HTML文件中的链接"""
        if html_only:
            return
        
        try:
            with open(filepath, "r", encoding="utf-8", errors="replace") as html_file:
                content = html_file.read()
                
                for original_url in sorted_urls:
                    new_filename = self.url_mapping[original_url]
                    
                    if no_css and new_filename.endswith(".css"):
                        continue
                    
                    if no_images and any(new_filename.endswith(ext) for ext in [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp", ".svg"]):
                        continue
                    
                    matches = list(re.finditer(re.escape(original_url), content))
                    
                    for match in reversed(matches):
                        if not hash_pattern.match(content, match.end()):
                            content = content[: match.start()] + new_filename + content[match.end() :]
            
            with open(filepath, "w", encoding="utf-8") as html_file:
                html_file.write(content)
        except Exception as e:
            print(f"更新HTML链接失败: {e}")
    
    def extract(self, no_css=False, no_images=False, html_only=False):
        """从MHTML提取文件"""
        temp_buffer_chunks = []
        
        try:
            with open(self.mhtml_path, "r", encoding="utf-8", errors="replace") as file:
                while True:
                    chunk = file.read(self.buffer_size)
                    if not chunk:
                        break
                    
                    temp_buffer_chunks.append(chunk)
                    
                    if not self.boundary:
                        self.boundary = self._read_boundary("".join(temp_buffer_chunks))
                        if not self.boundary:
                            # 如果仍然找不到boundary，尝试其他方式
                            boundary_match = re.search(r'boundary=([^\s"]+)', "".join(temp_buffer_chunks))
                            if boundary_match:
                                self.boundary = boundary_match.group(1)
                    
                    if not self.boundary:
                        # 仍然找不到，使用一个简单的启发式方法
                        possible_boundaries = re.findall(r'--([^\s]+)', "".join(temp_buffer_chunks))
                        if possible_boundaries:
                            for possible in possible_boundaries:
                                if len(possible) > 10 and "--" + possible in "".join(temp_buffer_chunks):
                                    self.boundary = possible
                                    break
                    
                    if self.boundary:
                        parts = "".join(temp_buffer_chunks).split("--" + self.boundary)
                        
                        temp_buffer_chunks = [parts[-1]]
                        
                        for part in parts[:-1]:
                            if self.extracted_count > 0:
                                self._process_part(part, no_css, no_images, html_only)
                            
                            self.extracted_count += 1
            
            if html_only:
                return
            
            sorted_urls = sorted(self.url_mapping.keys(), key=len, reverse=True)
            hash_pattern = re.compile(r"_[a-f0-9]{32}\.html")
            
            for filename in self.saved_html_files:
                filepath = os.path.join(self.output_dir, filename)
                self._update_html_links(filepath, sorted_urls, hash_pattern)
            
            print(f"已提取 {self.extracted_count-1} 个文件到 {self.output_dir}")
            return self.saved_html_files[0] if self.saved_html_files else None
        except Exception as e:
            print(f"提取过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None

