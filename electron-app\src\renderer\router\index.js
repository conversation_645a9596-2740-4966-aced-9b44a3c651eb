import { createRouter, createWebHashHistory } from 'vue-router'
import AppLayout from '@/components/layout/AppLayout.vue'
import { menuList, generateRoutes } from '@/config/menuConfig'

// 创建路由配置
const routes = [
  {
    path: '/',
    component: AppLayout,
    redirect: '/dashboard',
    children: [
      // 基于菜单配置生成的路由
      ...generateRoutes(menuList)
    ]
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫（可以在这里添加权限控制等逻辑）
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - PVV创作软件`
  } else {
    document.title = 'PVV创作软件'
  }

  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router
