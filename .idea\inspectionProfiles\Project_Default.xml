<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="99">
            <item index="0" class="java.lang.String" itemvalue="httpx" />
            <item index="1" class="java.lang.String" itemvalue="greenlet" />
            <item index="2" class="java.lang.String" itemvalue="tabulate" />
            <item index="3" class="java.lang.String" itemvalue="PyYAML" />
            <item index="4" class="java.lang.String" itemvalue="pycparser" />
            <item index="5" class="java.lang.String" itemvalue="lxml" />
            <item index="6" class="java.lang.String" itemvalue="et_xmlfile" />
            <item index="7" class="java.lang.String" itemvalue="gevent" />
            <item index="8" class="java.lang.String" itemvalue="jsonschema" />
            <item index="9" class="java.lang.String" itemvalue="pydantic" />
            <item index="10" class="java.lang.String" itemvalue="DrissionPage" />
            <item index="11" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="12" class="java.lang.String" itemvalue="click" />
            <item index="13" class="java.lang.String" itemvalue="attrs" />
            <item index="14" class="java.lang.String" itemvalue="psutil" />
            <item index="15" class="java.lang.String" itemvalue="openai" />
            <item index="16" class="java.lang.String" itemvalue="regex" />
            <item index="17" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="18" class="java.lang.String" itemvalue="DownloadKit" />
            <item index="19" class="java.lang.String" itemvalue="propcache" />
            <item index="20" class="java.lang.String" itemvalue="pythonnet" />
            <item index="21" class="java.lang.String" itemvalue="httpcore" />
            <item index="22" class="java.lang.String" itemvalue="idna" />
            <item index="23" class="java.lang.String" itemvalue="referencing" />
            <item index="24" class="java.lang.String" itemvalue="PyJWT" />
            <item index="25" class="java.lang.String" itemvalue="zhipuai" />
            <item index="26" class="java.lang.String" itemvalue="cffi" />
            <item index="27" class="java.lang.String" itemvalue="proxy_tools" />
            <item index="28" class="java.lang.String" itemvalue="bottle" />
            <item index="29" class="java.lang.String" itemvalue="requests" />
            <item index="30" class="java.lang.String" itemvalue="sniffio" />
            <item index="31" class="java.lang.String" itemvalue="websocket-client" />
            <item index="32" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="33" class="java.lang.String" itemvalue="zipp" />
            <item index="34" class="java.lang.String" itemvalue="tenacity" />
            <item index="35" class="java.lang.String" itemvalue="tldextract" />
            <item index="36" class="java.lang.String" itemvalue="websockets" />
            <item index="37" class="java.lang.String" itemvalue="annotated-types" />
            <item index="38" class="java.lang.String" itemvalue="importlib_metadata" />
            <item index="39" class="java.lang.String" itemvalue="iso8601" />
            <item index="40" class="java.lang.String" itemvalue="qiniu" />
            <item index="41" class="java.lang.String" itemvalue="dashscope" />
            <item index="42" class="java.lang.String" itemvalue="cachetools" />
            <item index="43" class="java.lang.String" itemvalue="clr_loader" />
            <item index="44" class="java.lang.String" itemvalue="multidict" />
            <item index="45" class="java.lang.String" itemvalue="yarl" />
            <item index="46" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="47" class="java.lang.String" itemvalue="webencodings" />
            <item index="48" class="java.lang.String" itemvalue="requests-toolbelt" />
            <item index="49" class="java.lang.String" itemvalue="tiktoken" />
            <item index="50" class="java.lang.String" itemvalue="protobuf" />
            <item index="51" class="java.lang.String" itemvalue="arrow" />
            <item index="52" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="53" class="java.lang.String" itemvalue="jiter" />
            <item index="54" class="java.lang.String" itemvalue="secure-cookie" />
            <item index="55" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="56" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="57" class="java.lang.String" itemvalue="h11" />
            <item index="58" class="java.lang.String" itemvalue="pywebview" />
            <item index="59" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="60" class="java.lang.String" itemvalue="tinycss2" />
            <item index="61" class="java.lang.String" itemvalue="frozenlist" />
            <item index="62" class="java.lang.String" itemvalue="fsspec" />
            <item index="63" class="java.lang.String" itemvalue="filelock" />
            <item index="64" class="java.lang.String" itemvalue="Nuitka" />
            <item index="65" class="java.lang.String" itemvalue="certifi" />
            <item index="66" class="java.lang.String" itemvalue="anyio" />
            <item index="67" class="java.lang.String" itemvalue="litellm" />
            <item index="68" class="java.lang.String" itemvalue="tokenizers" />
            <item index="69" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="70" class="java.lang.String" itemvalue="zope.interface" />
            <item index="71" class="java.lang.String" itemvalue="html2docx" />
            <item index="72" class="java.lang.String" itemvalue="lark-oapi" />
            <item index="73" class="java.lang.String" itemvalue="cssselect" />
            <item index="74" class="java.lang.String" itemvalue="requests-file" />
            <item index="75" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="76" class="java.lang.String" itemvalue="distro" />
            <item index="77" class="java.lang.String" itemvalue="leancloud" />
            <item index="78" class="java.lang.String" itemvalue="async-timeout" />
            <item index="79" class="java.lang.String" itemvalue="edge-tts" />
            <item index="80" class="java.lang.String" itemvalue="Jinja2" />
            <item index="81" class="java.lang.String" itemvalue="types-python-dateutil" />
            <item index="82" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="83" class="java.lang.String" itemvalue="rpds-py" />
            <item index="84" class="java.lang.String" itemvalue="urllib3" />
            <item index="85" class="java.lang.String" itemvalue="zope.event" />
            <item index="86" class="java.lang.String" itemvalue="python-docx" />
            <item index="87" class="java.lang.String" itemvalue="six" />
            <item index="88" class="java.lang.String" itemvalue="ordered-set" />
            <item index="89" class="java.lang.String" itemvalue="DataRecorder" />
            <item index="90" class="java.lang.String" itemvalue="unionllm" />
            <item index="91" class="java.lang.String" itemvalue="zstandard" />
            <item index="92" class="java.lang.String" itemvalue="tqdm" />
            <item index="93" class="java.lang.String" itemvalue="srt" />
            <item index="94" class="java.lang.String" itemvalue="colorama" />
            <item index="95" class="java.lang.String" itemvalue="pillow" />
            <item index="96" class="java.lang.String" itemvalue="aiohttp" />
            <item index="97" class="java.lang.String" itemvalue="aiosignal" />
            <item index="98" class="java.lang.String" itemvalue="openpyxl" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>