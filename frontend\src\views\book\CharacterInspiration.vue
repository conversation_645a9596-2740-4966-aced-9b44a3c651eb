<template>
  <div class="character-inspiration-page">
    <div class="page-header">
      <h2 class="title">
        <el-icon><MagicStick /></el-icon>
        人设灵感工坊 (MBTI 抽卡)
      </h2>

    </div>

    <el-card class="content-card glass-card">
      <template #header v-if="hasData">
        <div class="card-header-actions">
          <span>角色数据已加载 ({{ mbtiTypesCount }} MBTI 类型)</span>
          <div class="actions-group">
            <el-button type="primary" :icon="Upload" @click="openPasteImportDialog">导入/替换数据</el-button>
            <el-button type="success" :icon="Download" @click="exportData">导出JSON</el-button>
            <el-button type="warning" :icon="EditPen" @click="openEditDialog">编辑数据</el-button>
          </div>
        </div>
      </template>

      <!-- No Data State -->
      <div v-if="!hasData" class="no-data-state">
        <el-empty description="尚未导入角色数据">
          <el-button type="primary" :icon="Upload" @click="openPasteImportDialog" size="large">
            导入数据 (粘贴JSON)
          </el-button>
        </el-empty>
        <el-alert v-if="importError" :title="importError" type="error" show-icon class="error-alert" :closable="false"></el-alert>
      </div>

      <!-- Data Loaded State: Drawing Controls and Results -->
      <div v-else class="data-loaded-content">
        <el-row :gutter="30">
          <!-- Drawing Settings Column -->
          <el-col :xs="24" :sm="24" :md="8">
            <h3 class="section-title"><el-icon><Operation /></el-icon> 抽取设置</h3>
            <div class="drawing-controls">
              <el-form label-position="top" label-width="100px">
                <el-form-item label="选择MBTI类型">
                  <el-select v-model="selectedMbti" placeholder="请选择MBTI类型" class="full-width-select">
                    <el-option label="随机MBTI类型" value="RANDOM_MBTI"></el-option>
                    <el-option
                      v-for="mbti in availableMbtiTypes"
                      :key="mbti"
                      :label="mbti"
                      :value="mbti"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="选择角色分类">
                  <el-select v-model="selectedRoleType" placeholder="请选择角色分类" class="full-width-select">
                    <el-option
                      v-for="roleKey in selectableRoleKeys"
                      :key="roleKey.value"
                      :label="roleKey.label"
                      :value="roleKey.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="drawCharacter" 
                    class="full-width-button draw-button" 
                    :icon="Refresh"
                    :loading="isDrawing">
                    {{ isDrawing ? '抽取中...' : '开始抽取角色' }}
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-col>

          <!-- Drawing Results Column -->
          <el-col :xs="24" :sm="24" :md="16">
            <h3 class="section-title"><el-icon><User /></el-icon> 抽取结果</h3>
            <div class="results-area">
              <el-card v-if="drawnCharacterProfile" class="drawn-character-card" shadow="hover">
                <template #header>
                  <div class="drawn-card-header">
                    <div class="header-main">
                      <div class="mbti-badge">{{ drawnMbtiType }}</div>
                      <div class="role-type">{{ drawnRoleKeyDisplayName }}</div>
                    </div>
                    <el-button type="text" :icon="CopyDocument" @click="copyDrawnCharacter">复制</el-button>
                  </div>
                </template>
                <div class="drawn-character-details-content">
                  <div v-for="(value, key) in drawnCharacterProfile" :key="key" class="profile-item">
                    <div class="profile-label">{{ formatKeyDisplayName(key) }}</div>
                    <div class="profile-content" :class="{ 'list-content': Array.isArray(value) }">
                      <template v-if="Array.isArray(value)">
                        <div class="list-items">
                          <div v-for="(item, index) in value" :key="index" class="list-item">
                            <el-icon class="list-icon"><CircleCheck /></el-icon>
                            <span>{{ item }}</span>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        {{ value }}
                      </template>
                    </div>
                  </div>
                </div>
              </el-card>
              <el-empty v-else description="点击开始抽取角色获取灵感" />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- Edit JSON Dialog -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑角色数据 (JSON)"
      width="60%"
      :close-on-click-modal="false" 
      draggable
      destroy-on-close
      top="10vh"
      modal-class="edit-dialog-modal"
    >
      <el-input
        v-model="editableJsonData"
        type="textarea"
        :autosize="{ minRows: 15, maxRows: 25 }"
        placeholder="在这里编辑角色数据的JSON..."
        class="json-editor-dialog"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveJsonFromDialog">
            保存修改
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Paste Import JSON Dialog -->
    <el-dialog
      v-model="pasteImportDialogVisible"
      title="粘贴导入JSON数据"
      width="50%"
      :close-on-click-modal="false"
      draggable
      destroy-on-close
      top="15vh"
      class="character-import-dialog"
    >

      
      <div class="import-textarea-container">
        <el-input
          v-model="jsonInputForPaste"
          type="textarea"
          :autosize="{ minRows: 10, maxRows: 20 }"
          placeholder="在此处粘贴JSON数据..."
          resize="none"
          class="import-textarea"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="pasteImportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleJsonPasteImport">执行导入</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElNotification, ElMessageBox } from 'element-plus';
import { 
  MagicStick, Setting, Upload, Download, EditPen,
  View, Files, Operation, User, Refresh, CopyDocument,
  CircleCheck
} from '@element-plus/icons-vue';

// Main data store
const characterProfiles = ref({});
const rawJsonData = ref('');
const importError = ref('');

// Dialog specific state
const editDialogVisible = ref(false);
const editableJsonData = ref('');

// Paste Import Dialog specific state
const pasteImportDialogVisible = ref(false);
const jsonInputForPaste = ref('');

const hasData = computed(() => Object.keys(characterProfiles.value).length > 0);
const mbtiTypesCount = computed(() => Object.keys(characterProfiles.value).length);

// Watch for changes in characterProfiles to update rawJsonData
watch(characterProfiles, (newProfiles) => {
  try {
    rawJsonData.value = JSON.stringify(newProfiles, null, 2);
    // Reset drawing selections if data fundamentally changes
    selectedMbti.value = 'RANDOM_MBTI';
    selectedRoleType.value = 'RANDOM_ROLE';
    drawnCharacterProfile.value = null; // Clear previous result
  } catch (error) {
    console.error("Error stringifying character profiles:", error);
    rawJsonData.value = '';
  }
}, { deep: true });

// --- Data Management (Import, Export, Edit Dialog) --- 

// Open Paste Import Dialog
const openPasteImportDialog = () => {
  importError.value = ''; // Clear previous import errors from file upload if any
  jsonInputForPaste.value = ''; // Clear textarea
  pasteImportDialogVisible.value = true;
};

// Handle JSON Import from Pasted Text
const handleJsonPasteImport = async () => {
  try {
    const data = JSON.parse(jsonInputForPaste.value)
    const response = await window.pywebview.api.book_controller.save_character_inspiration(data)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result && result.status === 'success') {
      characterProfiles.value = data
      ElMessage.success('数据导入成功')
      pasteImportDialogVisible.value = false
      jsonInputForPaste.value = ''
    } else {
      throw new Error(result?.message || '导入失败')
    }
  } catch (error) {
    ElMessage.error('导入失败：' + error.message)
  }
};

const exportData = () => {
  if (!hasData.value) {
    ElMessage.warning('没有数据可导出');
    return;
  }
  try {
    const jsonStr = JSON.stringify(characterProfiles.value, null, 2);
    const textarea = document.createElement('textarea');
    textarea.value = jsonStr;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
    ElMessage.success('数据已复制到剪贴板');
  } catch (error) {
    ElMessage.error('导出失败：' + error.message);
  }
};

const openEditDialog = () => {
  editableJsonData.value = JSON.stringify(characterProfiles.value, null, 2);
  editDialogVisible.value = true;
};

const saveJsonFromDialog = async () => {
  try {
    const data = JSON.parse(editableJsonData.value)
    const response = await window.pywebview.api.book_controller.save_character_inspiration(data)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result && result.status === 'success') {
      characterProfiles.value = data
      ElMessage.success('保存成功')
      editDialogVisible.value = false
    } else {
      throw new Error(result?.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
};

// --- Drawing Logic and State --- 
const availableMbtiTypes = computed(() => Object.keys(characterProfiles.value));
const selectedMbti = ref('RANDOM_MBTI'); // Default to random

// 从所有MBTI类型的数据中收集所有可能的角色类型
const availableRoleTypes = computed(() => {
  const roleTypes = new Set(['RANDOM_ROLE']); // 始终包含随机选项
  
  // 遍历所有MBTI类型的数据，收集所有角色类型
  Object.values(characterProfiles.value).forEach(mbtiProfile => {
    if (mbtiProfile && typeof mbtiProfile === 'object') {
      Object.keys(mbtiProfile).forEach(roleType => {
        roleTypes.add(roleType);
      });
    }
  });
  
  return Array.from(roleTypes);
});

// 角色类型显示名称映射
const roleTypeMap = computed(() => {
  const map = {
    'RANDOM_ROLE': '随机角色类型'
  };
  
  // 为每个角色类型创建显示名称
  availableRoleTypes.value.forEach(roleType => {
    if (roleType !== 'RANDOM_ROLE') {
      map[roleType] = roleType; // 使用角色类型本身作为显示名称
    }
  });
  
  return map;
});

const selectableRoleKeys = computed(() => {
  return availableRoleTypes.value.map(value => ({
    label: roleTypeMap.value[value] || value,
    value: value
  }));
});

const selectedRoleType = ref('RANDOM_ROLE'); // Default to random

const drawnCharacterProfile = ref(null);
const drawnMbtiType = ref('');
const drawnRoleKey = ref(''); // This will store the actual key used, e.g., '正派角色' or 'minor'
const isDrawing = ref(false);

const drawnRoleKeyDisplayName = computed(() => roleTypeMap.value[drawnRoleKey.value] || drawnRoleKey.value);

const getRandomElement = (arr) => arr[Math.floor(Math.random() * arr.length)];

const drawCharacter = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先导入角色数据.');
    return;
  }
  isDrawing.value = true;
  drawnCharacterProfile.value = null; // Clear previous result

  // Simulate a short delay for UX
  await new Promise(resolve => setTimeout(resolve, 300)); 

  try {
    let mbtiToDraw = selectedMbti.value;
    if (mbtiToDraw === 'RANDOM_MBTI') {
      if (availableMbtiTypes.value.length === 0) {
        ElMessage.error('没有可用的MBTI类型进行随机抽取.');
        isDrawing.value = false;
        return;
      }
      mbtiToDraw = getRandomElement(availableMbtiTypes.value);
    }

    const mbtiProfile = characterProfiles.value[mbtiToDraw];
    if (!mbtiProfile) {
      ElMessage.error(`未找到MBTI类型 ${mbtiToDraw} 的数据.`);
      isDrawing.value = false;
      return;
    }

    let roleKeyToDraw = selectedRoleType.value;
    const actualRoleKeysInProfile = Object.keys(mbtiProfile);
    
    if (roleKeyToDraw === 'RANDOM_ROLE') {
      if (actualRoleKeysInProfile.length === 0) {
        ElMessage.error(`MBTI类型 ${mbtiToDraw} 下没有定义角色分类.`);
        isDrawing.value = false;
        return;
      }
      roleKeyToDraw = getRandomElement(actualRoleKeysInProfile);
    } else if (!mbtiProfile[roleKeyToDraw]) {
      // Fallback if selected role type doesn't exist for this MBTI, try random
      ElMessage.warning(`MBTI类型 ${mbtiToDraw} 下未找到 ${roleTypeMap.value[roleKeyToDraw]}，尝试随机抽取一个分类.`);
       if (actualRoleKeysInProfile.length === 0) {
        ElMessage.error(`MBTI类型 ${mbtiToDraw} 下没有可供随机抽取的角色分类.`);
        isDrawing.value = false;
        return;
      }
      roleKeyToDraw = getRandomElement(actualRoleKeysInProfile);
    }

    const characterData = mbtiProfile[roleKeyToDraw];
    if (typeof characterData !== 'object' || characterData === null) {
      ElMessage.error(`在 ${mbtiToDraw} - ${roleTypeMap.value[roleKeyToDraw]} 下未找到有效的角色对象数据.`);
      isDrawing.value = false;
      return;
    }

    drawnMbtiType.value = mbtiToDraw;
    drawnRoleKey.value = roleKeyToDraw;
    drawnCharacterProfile.value = characterData;
    
    ElNotification({
      title: '抽取成功!',
      message: `抽到 ${mbtiToDraw} - ${roleTypeMap.value[roleKeyToDraw] || roleKeyToDraw}`,
      type: 'success',
      duration: 2000
    });

  } catch (error) {
    console.error("抽取角色失败:", error);
    ElMessage.error("抽取角色时发生错误，请检查数据格式或控制台日志.");
  } finally {
    isDrawing.value = false;
  }
};

// Helper to format display keys (e.g., replace underscores)
const formatKeyDisplayName = (key) => {
  return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()); // Capitalize words
};

// Copy drawn character details to clipboard
const copyDrawnCharacter = () => {
  if (!drawnCharacterProfile.value) return;
  let textToCopy = `MBTI: ${drawnMbtiType.value}\n角色分类: ${drawnRoleKeyDisplayName.value}\n---\n`;
  for (const [key, value] of Object.entries(drawnCharacterProfile.value)) {
    const formattedValue = Array.isArray(value) ? value.join(', ') : value;
    textToCopy += `${formatKeyDisplayName(key)}: ${formattedValue}\n`;
  }
  window.pywebview.api.copy_to_clipboard(textToCopy).then(() => {
    ElMessage.success('角色信息已复制到剪贴板!');
  }).catch(err => {
    ElMessage.error('复制失败，请手动复制.');
    console.error('Clipboard copy failed:', err);
  });
};

const inspirationData = ref({})

// 加载数据
const loadData = async () => {
  try {
    const response = await window.pywebview.api.book_controller.get_character_inspiration()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result && result.status === 'success') {
      const data = typeof result.data === 'string' ? JSON.parse(result.data) : result.data
      characterProfiles.value = data
    } else {
      throw new Error(result?.message || '加载失败')
    }
  } catch (error) {
    console.error('加载数据错误:', error)
    ElMessage.error('加载数据失败：' + error.message)
  }
}

onMounted(() => {
  loadData()
})

</script>

<style scoped>
.character-inspiration-page {
  padding: 20px;
  height: 100%; /* Assuming parent of this component provides a defined height, e.g. the main layout content area */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.page-header {
  margin-bottom: 20px; /* Slightly reduced margin */
  text-align: center;
  flex-shrink: 0; /* Prevent header from shrinking if page is flex column */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  color: var(--el-color-primary-dark-2);
  margin: 0 0 8px;
}



.content-card {
  flex-grow: 1; 
  display: flex;
  flex-direction: column;
  overflow: hidden; /* CRITICAL: This card must contain its children and not grow due to them */
}
.content-card :deep(.el-card__header) {
  flex-shrink: 0; 
}
.content-card :deep(.el-card__body) {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding: 15px;
    overflow: hidden; 
}

.card-header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
.card-header-actions .actions-group {
  display: flex;
  gap: 10px;
}

.no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  flex-grow: 1; 
  height: 100%; /* Ensure it takes full height of card body */
}
.no-data-state .el-button {
  margin-top: 20px;
  padding: 12px 25px;
  font-size: 1rem;
}

.data-loaded-content {
  width: 100%;
  flex-grow: 1; 
  display: flex; /* Make it a flex container for its child row */
  height: 100%; /* Take full height of card body */
  overflow: hidden; /* Prevent this from overflowing */
}
.data-loaded-content > .el-row {
  height: 100%; /* Make the row take full height */
  width: 100%;
}

/* Make columns within the row also take full height and enable flex for their children */
.data-loaded-content > .el-row > .el-col {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent column itself from overflowing if its children are misbehaving */
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px; 
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-primary);
  flex-shrink: 0; 
}

.drawing-controls {
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(var(--el-color-primary-rgb), 0.03);
  /* This section will take its natural height and not grow/shrink in the flex column */
  flex-shrink: 0;
}
.dark .drawing-controls {
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
}

.full-width-select,
.full-width-button {
  width: 100%;
}

.draw-button {
  padding: 12px 0; 
  font-size: 1rem;
}

.results-area {
  flex-grow: 1; /* CRITICAL: Allow results area to take remaining vertical space in its column */
  /* padding: 0px 10px 10px 10px; - Padding might interfere with scroll height calculation, apply to inner card if needed */
  overflow-y: auto; /* CRITICAL: Enable vertical scrolling for this area */
  min-height: 0; /* Important for flex children that need to scroll */
  padding: 10px; /* Added padding */
  box-sizing: border-box; /* Added for safety */
  display: flex;
  flex-direction: column;
}

.results-area > .el-empty {
  flex-grow: 1; 
  justify-content: center;
}

.drawn-character-card {
  background-color: var(--el-bg-color);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  width: 100%;
  margin-bottom: 10px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  max-height: 100%;
  flex: 1;
}

.drawn-character-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.drawn-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: linear-gradient(to right, var(--el-color-primary-light-9), var(--el-bg-color));
  position: sticky;
  top: 0;
  z-index: 1;
  flex-shrink: 0;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mbti-badge {
  background-color: var(--el-color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.role-type {
  color: var(--el-text-color-primary);
  font-size: 1.1rem;
  font-weight: 500;
}

.drawn-character-details-content {
  padding: 20px;
  overflow-y: auto; 
  flex-grow: 1;
}

.profile-item {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px dashed var(--el-border-color-lighter);
}

.profile-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.profile-label {
  color: var(--el-text-color-secondary);
  font-size: 0.9rem;
  margin-bottom: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profile-content {
  color: var(--el-text-color-primary);
  font-size: 1.05rem;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  padding: 0 4px;
}

.profile-content.list-content {
  padding: 0;
}

.list-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}

.list-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.list-item:hover {
  background-color: var(--el-fill-color);
  transform: translateX(4px);
}

.list-icon {
  color: var(--el-color-success);
  font-size: 1.1em;
  margin-top: 3px;
  flex-shrink: 0;
}

.list-item span {
  flex: 1;
  line-height: 1.5;
}

/* Dark theme adjustments */
.dark .drawn-character-card {
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dark .drawn-card-header {
  background: linear-gradient(to right, var(--el-color-primary-dark-2), var(--el-bg-color-overlay));
  border-bottom-color: var(--el-border-color-darker);
  position: sticky;
  top: 0;
  z-index: 1;
}

.dark .profile-item {
  border-bottom-color: var(--el-border-color-darker);
}

.dark .profile-label {
  color: var(--el-text-color-secondary);
}

.dark .profile-content {
  color: var(--el-text-color-primary);
}

.dark .role-type {
  color: var(--el-text-color-primary);
}

.dark .list-item {
  background-color: var(--el-fill-color-darker);
}

.dark .list-item:hover {
  background-color: var(--el-fill-color-dark);
}

.error-alert {
  margin-top: 20px;
  width: 100%;
  max-width: 500px;
}



.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

:global(.edit-dialog-modal .el-dialog),
:global(.paste-import-dialog-modal .el-dialog) {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 10px;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.dark .glass-card {
  background: rgba(40, 40, 50, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}
.dark :global(.edit-dialog-modal .el-dialog),
.dark :global(.paste-import-dialog-modal .el-dialog) {
  background: rgba(30, 35, 45, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.dark :global(.edit-dialog-modal .el-dialog__title),
.dark :global(.paste-import-dialog-modal .el-dialog__title) {
  color: rgba(255,255,255,0.9);
}
.dark .title {
  color: var(--el-color-primary-light-3);
}
.dark .subtitle {
  color: var(--el-text-color-secondary-dark);
}
.dark .section-title {
    color: var(--el-text-color-primary-dark);
}
.dark .profile-key {
  color: var(--el-text-color-secondary-dark);
}
.dark .profile-value {
  color: var(--el-text-color-primary-dark);
}

/* Dark theme adjustments for dialog contents */

/* Ensure the modal overlay itself is targeted if .dark is on html/body */
:global(html.dark .paste-import-dialog-modal .el-dialog__body .el-input.json-paste-area .el-textarea__inner),
:global(html.dark .edit-dialog-modal .el-dialog__body .el-input.json-editor-dialog .el-textarea__inner) {
  background-color: var(--el-input-bg-color) !important;
  color: var(--el-input-text-color) !important;
  border-color: var(--el-input-border-color) !important;
}

:global(html.dark .paste-import-dialog-modal .el-dialog__body .el-input.json-paste-area .el-textarea__inner::placeholder),
:global(html.dark .edit-dialog-modal .el-dialog__body .el-input.json-editor-dialog .el-textarea__inner::placeholder) {
  color: var(--el-text-color-placeholder) !important;
}

:global(html.dark .paste-import-dialog-modal .el-dialog__body .el-alert.el-alert--info) {
  background-color: var(--el-color-info-light-9) !important; 
  /* Element Plus uses this for dark alert backgrounds too, or a similar one */
}
:global(html.dark .paste-import-dialog-modal .el-dialog__body .el-alert.el-alert--info .el-alert__title) {
  color: var(--el-text-color-regular) !important;
}
:global(html.dark .paste-import-dialog-modal .el-dialog__body .el-alert.el-alert--info .el-alert__icon) {
  color: var(--el-color-info) !important;
}

:global(html.dark .edit-dialog-modal .el-dialog__footer .el-button--default),
:global(html.dark .paste-import-dialog-modal .el-dialog__footer .el-button--default) {
  background-color: var(--el-button-bg-color) !important;
  color: var(--el-button-text-color) !important;
  border-color: var(--el-button-border-color) !important;
}

:global(html.dark .edit-dialog-modal .el-dialog__footer .el-button--default:hover),
:global(html.dark .edit-dialog-modal .el-dialog__footer .el-button--default:focus),
:global(html.dark .paste-import-dialog-modal .el-dialog__footer .el-button--default:hover),
:global(html.dark .paste-import-dialog-modal .el-dialog__footer .el-button--default:focus) {
  background-color: var(--el-button-hover-bg-color) !important;
  color: var(--el-button-hover-text-color) !important;
  border-color: var(--el-button-hover-border-color) !important;
}
/* Primary buttons in footer are usually fine with global dark theme variables */
:global(html.dark .edit-dialog-modal .el-dialog__footer .el-button--primary),
:global(html.dark .paste-import-dialog-modal .el-dialog__footer .el-button--primary) {
  background-color: var(--el-button-primary-bg-color) !important; /* Explicitly set for primary */
  color: var(--el-button-primary-text-color) !important;
  border-color: var(--el-button-primary-border-color) !important;
}

:global(html.dark .edit-dialog-modal .el-dialog__footer .el-button--primary:hover),
:global(html.dark .edit-dialog-modal .el-dialog__footer .el-button--primary:focus),
:global(html.dark .paste-import-dialog-modal .el-dialog__footer .el-button--primary:hover),
:global(html.dark .paste-import-dialog-modal .el-dialog__footer .el-button--primary:focus) {
  background-color: var(--el-button-primary-hover-bg-color) !important;
  color: var(--el-button-primary-hover-text-color) !important;
  border-color: var(--el-button-primary-hover-border-color) !important;
}

.toolbar {
  margin-bottom: 20px;
  padding: 10px;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  display: flex;
  gap: 10px;
}

/* Import Dialog Styles */
.character-import-dialog :deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
}

.character-import-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.character-import-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.character-import-dialog :deep(.el-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid var(--el-border-color-light);
}

/* Theme-aware styles */
.character-import-dialog :deep(.el-input__wrapper),
.character-import-dialog :deep(.el-textarea__inner) {
  background-color: var(--el-bg-color);
  border-color: var(--el-border-color);
  color: var(--el-text-color-primary);
}

.character-import-dialog :deep(.el-textarea__inner:focus) {
  border-color: var(--el-color-primary);
}

/* Dark theme specific overrides */
html.dark .character-import-dialog :deep(.el-dialog) {
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-darker);
}

html.dark .character-import-dialog :deep(.el-dialog__header) {
  border-bottom-color: var(--el-border-color-darker);
}

html.dark .character-import-dialog :deep(.el-dialog__footer) {
  border-top-color: var(--el-border-color-darker);
}

html.dark .character-import-dialog :deep(.el-input__wrapper),
html.dark .character-import-dialog :deep(.el-textarea__inner) {
  background-color: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-darker);
}

.import-alert {
  margin-bottom: 15px;
}

.import-textarea-container {
  margin: 15px 0;
}

.import-textarea {
  width: 100%;
}

.import-textarea :deep(.el-textarea__inner) {
  line-height: 1.6;
  padding: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 允许选择的区域 */
.character-card-content,
.import-textarea,
.edit-json-textarea {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* 标题和按钮区域禁止选择 */
.page-header,
.card-header-actions,
.control-panel,
.el-dialog__header,
.el-dialog__footer {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 禁止通知消息选择文本 */
:global(.el-notification) {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

:global(.el-notification__content),
:global(.el-notification__title) {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
</style>