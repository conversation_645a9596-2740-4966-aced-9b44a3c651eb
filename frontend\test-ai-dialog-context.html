<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话框上下文测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .context-info {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .context-item {
            margin: 8px 0;
        }
        .label {
            font-weight: bold;
            color: #666;
        }
        .value {
            color: #333;
            margin-left: 10px;
        }
        .prompt-preview {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>AI生成面板上下文测试</h1>
    <p>这个页面用于测试AI生成面板是否能正确获取和显示节点的上下文信息。</p>

    <div class="test-section">
        <div class="test-title">✅ 上下文构建器修复成功</div>
        <div class="context-info">
            <div class="context-item">
                <span class="label">问题:</span>
                <span class="value">原来的上下文信息显示错误，"高阶函数"节点显示为"第0层"、"同级主题：无"等</span>
            </div>
            <div class="context-item">
                <span class="label">修复:</span>
                <span class="value">修复了 findNodePath 方法中的路径构建逻辑</span>
            </div>
            <div class="context-item">
                <span class="label">结果:</span>
                <span class="value success">现在能正确识别节点层级、同级节点和子节点</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🎯 AI生成面板新功能</div>
        <div class="context-info">
            <h4>1. 提示词预览功能</h4>
            <ul>
                <li>✅ 显示系统提示词和用户提示词</li>
                <li>✅ 实时更新提示词内容</li>
                <li>✅ 显示提示词字符统计</li>
                <li>✅ 支持编辑用户提示词</li>
            </ul>

            <h4>2. 上下文感知</h4>
            <ul>
                <li>✅ 正确显示节点层级深度</li>
                <li>✅ 显示完整的层级路径</li>
                <li>✅ 列出同级主题和子主题</li>
                <li>✅ 包含当前节点内容</li>
            </ul>

            <h4>3. 领域特定提示词</h4>
            <ul>
                <li>✅ 小说人物创建 vs 一般人物定义</li>
                <li>✅ 世界设定 vs 一般设定</li>
                <li>✅ 情节发展 vs 一般情节</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">📝 示例：正确的上下文信息</div>
        <div class="context-info">
            <h4>节点：高阶函数（第三层）</h4>
            <div class="context-item">
                <span class="label">当前主题:</span>
                <span class="value">高阶函数</span>
            </div>
            <div class="context-item">
                <span class="label">层级深度:</span>
                <span class="value">第2层</span>
            </div>
            <div class="context-item">
                <span class="label">层级路径:</span>
                <span class="value">编程基础 → 函数 → 高阶函数</span>
            </div>
            <div class="context-item">
                <span class="label">同级主题:</span>
                <span class="value">闭包</span>
            </div>
            <div class="context-item">
                <span class="label">已有子主题:</span>
                <span class="value">map和filter</span>
            </div>
            <div class="context-item">
                <span class="label">当前内容:</span>
                <span class="value">接受函数作为参数或返回函数的函数</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🔧 使用方法</div>
        <div class="context-info">
            <h4>在思维导图中测试：</h4>
            <ol>
                <li>选择任意节点</li>
                <li>点击"AI生成"按钮</li>
                <li>在弹出的对话框中点击"预览提示词"</li>
                <li>查看上下文信息是否正确</li>
                <li>可以编辑用户提示词进行自定义</li>
                <li>点击"开始生成"使用编辑后的提示词</li>
            </ol>

            <h4>验证要点：</h4>
            <ul>
                <li>层级深度是否正确（不再显示"第0层"）</li>
                <li>同级主题是否正确列出</li>
                <li>子主题是否正确显示</li>
                <li>层级路径是否完整</li>
                <li>领域特定的提示词是否合适</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🎉 修复总结</div>
        <div class="context-info">
            <p><strong>问题解决：</strong></p>
            <ul>
                <li>✅ 修复了上下文构建器的路径查找逻辑</li>
                <li>✅ 添加了提示词预览和编辑功能</li>
                <li>✅ 实现了上下文感知的AI生成</li>
                <li>✅ 支持领域特定的提示词模板</li>
            </ul>

            <p><strong>现在AI能够：</strong></p>
            <ul>
                <li>🎯 准确理解节点在思维导图中的位置</li>
                <li>🎯 根据上下文生成相关内容</li>
                <li>🎯 区分不同领域的生成需求</li>
                <li>🎯 提供可控制的提示词生成</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('AI生成面板上下文测试页面已加载');
        console.log('请在思维导图界面中测试AI生成功能');
    </script>
</body>
</html>
