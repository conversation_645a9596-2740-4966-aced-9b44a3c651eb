<template>
  <div class="scene-card">
    <div class="card-header">
      <div class="card-icon">
        <el-icon><Picture /></el-icon>
      </div>
      <div class="card-title">{{ title }}</div>
      <div class="card-actions">
        <el-button
          type="danger"
          size="small"
          circle
          @click.stop="$emit('delete')"
          class="delete-btn">
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>
    <div class="card-content">
      <div class="card-description">{{ description }}</div>
      <div class="card-tags">
        <el-tag
          v-for="tag in tags"
          :key="tag"
          size="small"
          effect="light"
          class="card-tag">
          {{ tag }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Picture, Delete } from '@element-plus/icons-vue'

// 属性定义
const props = defineProps({
  title: {
    type: String,
    default: '未命名场景'
  },
  description: {
    type: String,
    default: ''
  },
  tags: {
    type: Array,
    default: () => []
  }
})

// 定义事件
defineEmits(['delete'])
</script>

<style scoped>
.scene-card {
  width: 240px;
  height: 280px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 2px 12px var(--el-mask-color-extra-light);
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scene-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--el-mask-color);
}

.card-header {
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.card-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-color-primary-light-9);
  border-radius: 6px;
  color: var(--el-color-primary);
}

.card-title {
  flex: 1;
  font-size: 1em;
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-actions {
  display: flex;
  gap: 4px;
}

.delete-btn {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scene-card:hover .delete-btn {
  opacity: 1;
}

.card-content {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-description {
  font-size: 0.9em;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
}

.card-tags {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.card-tag {
  transition: all 0.3s ease;
}

.card-tag:hover {
  transform: translateY(-1px);
}

/* 暗色主题适配 */
.dark .scene-card {
  --card-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

.dark .card-inner {
  box-shadow: var(--card-shadow);
}

.dark .card-face {
  border-color: var(--el-border-color-darker);
}
</style>