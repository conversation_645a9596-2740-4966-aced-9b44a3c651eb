import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useConfigStore = defineStore('config', () => {
  // 状态
  const config = ref({
    theme: 'light',
    language: 'zh-CN',
    fontSize: 14,
    autoSave: true,
    // AI配置
    openai: {
      apiKey: '',
      baseUrl: 'https://api.openai.com/v1',
      model: 'gpt-3.5-turbo',
      temperature: 0.7
    },
    // 备份配置
    backup: {
      autoBackup: false,
      interval: '3600000',
      maxBackups: 10
    }
  })

  const loading = ref(false)

  // 计算属性
  const theme = computed(() => config.value.theme)
  const isDark = computed(() => config.value.theme === 'dark')

  // 方法
  async function loadConfig() {
    loading.value = true
    try {
      const result = await window.pywebview.api.get_settings()
      if (result.status === 'success') {
        // 合并配置
        config.value = {
          ...config.value,
          ...result.data
        }
        
        // 应用主题
        applyTheme(config.value.theme)
      }
    } catch (error) {
      console.error('加载配置失败:', error)
    } finally {
      loading.value = false
    }
  }

  async function updateConfigItem(key, value) {
    try {
      // 更新本地状态
      if (key.includes('.')) {
        // 处理嵌套属性，如 'openai.apiKey'
        const keys = key.split('.')
        let target = config.value
        for (let i = 0; i < keys.length - 1; i++) {
          if (!target[keys[i]]) {
            target[keys[i]] = {}
          }
          target = target[keys[i]]
        }
        target[keys[keys.length - 1]] = value
      } else {
        config.value[key] = value
      }

      // 保存到后端
      const result = await window.pywebview.api.update_config(key, value)
      
      // 如果是主题变更，立即应用
      if (key === 'theme') {
        applyTheme(value)
      }
      
      return result
    } catch (error) {
      console.error('更新配置失败:', error)
      throw error
    }
  }

  async function updateConfig(newConfig) {
    try {
      // 批量更新配置
      for (const [key, value] of Object.entries(newConfig)) {
        await updateConfigItem(key, value)
      }
    } catch (error) {
      console.error('批量更新配置失败:', error)
      throw error
    }
  }

  function applyTheme(themeName) {
    const html = document.querySelector('html')
    if (html) {
      html.className = themeName
      
      // 更新CSS变量
      if (themeName === 'dark') {
        html.style.setProperty('--el-bg-color', '#1a1a1a')
        html.style.setProperty('--el-text-color-primary', '#e5eaf3')
        html.style.setProperty('--el-border-color', '#414243')
      } else {
        html.style.setProperty('--el-bg-color', '#ffffff')
        html.style.setProperty('--el-text-color-primary', '#303133')
        html.style.setProperty('--el-border-color', '#dcdfe6')
      }
    }
  }

  async function resetConfig() {
    try {
      // 重置为默认配置
      config.value = {
        theme: 'light',
        language: 'zh-CN',
        fontSize: 14,
        autoSave: true,
        openai: {
          apiKey: '',
          baseUrl: 'https://api.openai.com/v1',
          model: 'gpt-3.5-turbo',
          temperature: 0.7
        },
        backup: {
          autoBackup: false,
          interval: '3600000',
          maxBackups: 10
        }
      }
      
      // 保存到后端
      await updateConfig(config.value)
      
      return true
    } catch (error) {
      console.error('重置配置失败:', error)
      throw error
    }
  }

  // 获取特定配置项
  function getConfigItem(key) {
    if (key.includes('.')) {
      const keys = key.split('.')
      let value = config.value
      for (const k of keys) {
        value = value?.[k]
      }
      return value
    }
    return config.value[key]
  }

  // 初始化
  async function initialize() {
    await loadConfig()
  }

  return {
    // 状态
    config,
    loading,
    
    // 计算属性
    theme,
    isDark,
    
    // 方法
    loadConfig,
    updateConfigItem,
    updateConfig,
    applyTheme,
    resetConfig,
    getConfigItem,
    initialize
  }
})
