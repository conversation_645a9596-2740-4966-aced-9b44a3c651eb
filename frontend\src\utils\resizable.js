// resizable.js
export class WindowResizer {
    constructor() {
        this.isResizing = false;
        this.startWidth = 0;
        this.startHeight = 0;
        this.currentHandle = null;
        this.startMouseX = 0;
        this.startMouseY = 0;
        this.clickOffsetY = 0;  // 鼠标点击位置相对于handle的Y偏移（用于保持垂直位置）
        this.hasMoved = false;
        this.rafId = null;
        this.lastResizeTime = 0;
        this.THROTTLE_DELAY = 16; // ~60fps

        // 添加样式
        this.initStyles();
    }

    initStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .resize-handle {
                position: absolute;
                width: 10px;
                height: 10px;
                background: transparent;
                z-index: 9999;
            }
            .resize-handle.n { top: 0; left: 50%; transform: translateX(-50%); cursor: n-resize; height: 5px; width: 100%; }
            .resize-handle.s { bottom: 0; left: 50%; transform: translateX(-50%); cursor: s-resize; height: 5px; width: 100%; }
            .resize-handle.e { right: 0; top: 50%; transform: translateY(-50%); cursor: e-resize; width: 5px; height: 100%; }
            .resize-handle.w { left: 0; top: 50%; transform: translateY(-50%); cursor: w-resize; width: 5px; height: 100%; }
            .resize-handle.nw { top: 0; left: 0; cursor: nw-resize; }
            .resize-handle.ne { top: 0; right: 0; cursor: ne-resize; }
            .resize-handle.sw { bottom: 0; left: 0; cursor: sw-resize; }
            .resize-handle.se { bottom: 0; right: 0; cursor: se-resize; }
            body.resizing {
                cursor: var(--resize-cursor, default) !important;
                user-select: none;
            }
            body.resizing * {
                pointer-events: none;
            }
            .resize-handle:hover {
                background: rgba(0, 120, 255, 0.1);
            }
        `;
        document.head.appendChild(style);
    }

    async initResize(e) {
        e.preventDefault();

        try {
            // 获取窗口大小和位置
            const sizeResponse = await window.pywebview.api.get_window_size();
            const sizeResult = typeof sizeResponse === 'string' ? JSON.parse(sizeResponse) : sizeResponse;
            
            const posResponse = await window.pywebview.api.get_window_position();
            const posResult = typeof posResponse === 'string' ? JSON.parse(posResponse) : posResponse;
            
            if (sizeResult.status === 'success' && posResult.status === 'success') {
                this.startWidth = sizeResult.data.width;
                this.startHeight = sizeResult.data.height;
                this.startMouseX = e.screenX;
                this.startMouseY = e.screenY;
                this.currentHandle = e.target.dataset.direction || null;
                
                // 记录鼠标相对于handle的垂直偏移
                const handle = e.target;
                const handleRect = handle.getBoundingClientRect();
                this.clickOffsetY = e.clientY - handleRect.top;
                
                this.isResizing = true;
                this.hasMoved = false;
                
                document.body.classList.add('resizing');
                document.body.style.setProperty('--resize-cursor', window.getComputedStyle(e.target).cursor);
                
                document.addEventListener('mousemove', this.handleMouseMove.bind(this));
                document.addEventListener('mouseup', this.stopResize.bind(this));
            }
        } catch (error) {
            console.error('Failed to get window info:', error);
        }
    }

    handleMouseMove(e) {
        if (!this.isResizing) return;
        
        if (this.rafId !== null) {
            cancelAnimationFrame(this.rafId);
        }
        
        this.rafId = requestAnimationFrame(() => this.resize(e));
    }


    async resize(e) {
        // 如果没有有效的 handle，直接返回
        if (!this.currentHandle) {
            return;
        }

        const now = performance.now();
        if (now - this.lastResizeTime < this.THROTTLE_DELAY) {
            return;
        }

        this.hasMoved = true;

        // 计算鼠标移动的距离
        const deltaX = Math.round(e.screenX - this.startMouseX);
        const deltaY = Math.round(e.screenY - this.startMouseY);

        let newWidth = this.startWidth;
        let newHeight = this.startHeight;
        let moveX = 0;
        let moveY = 0;

        // 修改调整大小的逻辑
        if (this.currentHandle && this.currentHandle.includes('e')) {
            newWidth = Math.max(400, this.startWidth + deltaX);
        }
        if (this.currentHandle && this.currentHandle.includes('w')) {
            const widthDelta = -deltaX;
            newWidth = Math.max(400, this.startWidth + widthDelta);
            if (newWidth !== this.startWidth) {
                moveX = this.startWidth - newWidth;
            }
        }
        if (this.currentHandle && this.currentHandle.includes('s')) {
            newHeight = Math.max(300, this.startHeight + deltaY);
        }
        if (this.currentHandle && this.currentHandle.includes('n')) {
            const heightDelta = -deltaY;
            newHeight = Math.max(300, this.startHeight + heightDelta);
            if (newHeight !== this.startHeight) {
                moveY = this.startHeight - newHeight;
            }
        }

        if (newWidth > 0 && newHeight > 0) {
            try {
                // 先移动窗口位置（如果需要）
                if (moveX !== 0 || moveY !== 0) {
                    const moveResponse = await window.pywebview.api.move_window(moveX, moveY);
                    const moveResult = typeof moveResponse === 'string' ? JSON.parse(moveResponse) : moveResponse;
                    if (moveResult.status !== 'success') {
                        console.error('Move failed:', moveResponse);
                        return;
                    }
                }

                // 然后调整窗口大小
                const response = await window.pywebview.api.resize_window(
                    newWidth,
                    newHeight,
                    this.currentHandle,
                    0,  // 移动已经在上面处理过了，这里传0
                    0
                );
                const result = typeof response === 'string' ? JSON.parse(response) : response;

                if (result.status === 'success') {
                    // 获取调整后的窗口位置
                    const posResponse = await window.pywebview.api.get_window_position();
                    const posResult = typeof posResponse === 'string' ? JSON.parse(posResponse) : posResponse;

                    if (posResult.status === 'success') {
                        // 更新起始值，这样下一次计算偏移时会更准确
                        this.startWidth = newWidth;
                        this.startHeight = newHeight;
                        this.startMouseX = e.screenX;
                        this.startMouseY = e.screenY;
                    }
                }

                this.lastResizeTime = now;
            } catch (error) {
                console.error('Resize error:', error);
                this.stopResize();
            }
        }
    }
    stopResize() {
        if (this.isResizing && !this.hasMoved) {
            console.log('No resize: mouse did not move');
        }
        
        this.isResizing = false;
        this.hasMoved = false;
        
        if (this.rafId !== null) {
            cancelAnimationFrame(this.rafId);
            this.rafId = null;
        }
        
        document.body.classList.remove('resizing');
        document.body.style.removeProperty('--resize-cursor');
        
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
        document.removeEventListener('mouseup', this.stopResize.bind(this));
    }

    init() {
        // 创建调整大小的手柄
        const handles = ['n', 's', 'e', 'w', 'nw', 'ne', 'sw', 'se'];
        handles.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = `resize-handle ${direction}`;
            handle.dataset.direction = direction;
            document.body.appendChild(handle);
        });

        // 使用事件委托处理resize handles的点击
        document.body.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('resize-handle')) {
                this.initResize(e);
            }
        });
    }
}

// 导出一个初始化函数
export function initWindowResize() {
    const resizer = new WindowResizer();
    resizer.init();
    return resizer;
}
