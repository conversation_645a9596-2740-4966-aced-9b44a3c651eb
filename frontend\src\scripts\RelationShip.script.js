import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useBookStore } from '@/stores/book'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  Search,
  User,
  Connection,
  Plus,
  Edit,
  Delete,
  Refresh,
  Loading,
  FullScreen,
  ArrowLeft,
  Right,
  Document
} from '@element-plus/icons-vue'

import { useRelationshipStore } from '@/stores/relationship'


// 在顶部导入组件
import EntityAttributesDialog from '@/components/EntityAttributesDialog.vue';
import FullscreenGraphDialog from '@/components/FullscreenGraphDialog.vue';

// 添加状态变量
const fullScreenGraphVisible = ref(false);

// 修改toggleFullScreen方法，确保状态更新
const toggleFullScreen = () => {
  // 检查是否有关系数据
  if (!hasRelations.value) {
    ElMessage.warning('暂无关系数据，无法打开全屏图谱');
    return;
  }
  
  if (!entityList.value || entityList.value.length === 0) {
    ElMessage.warning('暂无角色数据，请先添加角色');
    return;
  }
  
  console.log('打开全屏弹窗，当前状态:', fullScreenGraphVisible.value);
  // 打开全屏弹窗
  fullScreenGraphVisible.value = true;
  
  // 确保在下一个渲染周期中获取到容器
  nextTick(() => {
    console.log('全屏弹窗状态已更新为:', fullScreenGraphVisible.value);
  });
};

// 添加一个明确的关闭弹窗方法，可以在需要时调用
const closeFullScreenGraph = () => {
  console.log('关闭全屏弹窗，当前状态:', fullScreenGraphVisible.value);
  fullScreenGraphVisible.value = false;
};

// 处理全屏模式下的节点点击
const handleFullscreenNodeClick = (entity) => {
  // 可以在这里处理，例如显示实体属性
  showEntityAttributes(entity);
};
// 在声明响应式变量的地方添加：
const attributesDialogVisible = ref(false);
const currentAttributesEntity = ref(null);

// 修改 showEntityAttributes 方法
const showEntityAttributes = (entity) => {
  currentAttributesEntity.value = entity;
  attributesDialogVisible.value = true;
};

// 初始化路由和状态
const router = useRouter()
const bookStore = useBookStore()

// 初始化store
const relationshipStore = useRelationshipStore()
const allRelations = computed(() => relationshipStore.relations || [])
// 状态变量
const loading = ref(true)
const selectedBookId = ref('')
const selectedEntityId = ref(null)
const selectedTemplateIds = ref([])
const searchKeyword = ref('')
const templates = ref([])
const entityList = ref([])
const relations = ref([])
const graphContainer = ref(null)
const chart = ref(null)
const fullScreenDialog = ref(false)
const fullScreenContainer = ref(null)

// 关系表单相关
const relationDialog = ref(false)
const isEditMode = ref(false)
const relationForm = ref({
  source: '',
  target: '',
  type: '',
  description: '',
  bidirectional: false,
  strength: 3,
  tags: []
})

// 标签输入相关
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInput = ref(null)

// 关系类型选项 - 使用正确的数据结构
const relationTypeOptions = ref([
  { value: "友好", label: "友好", color: "success" },
  { value: "敌对", label: "敌对", color: "danger" },
  { value: "中立", label: "中立", color: "info" },
  { value: "血缘", label: "血缘", color: "warning" },
  { value: "恋爱", label: "恋爱", color: "danger" },
  { value: "师徒", label: "师徒", color: "info" },
  { value: "上下级", label: "上下级", color: "info" },
  { value: "合作", label: "合作", color: "success" }
]);

// 关系标签列表
const relationTags = ref(['重要', '核心', '次要', '暂时', '长期']);

// 添加源模板和目标模板选择相关变量
const selectedSourceTemplateId = ref('');
const selectedTargetTemplateId = ref('');

// 过滤后的源实体列表
const filteredSourceEntities = computed(() => {
  if (!selectedSourceTemplateId.value) return [];
  return entityList.value.filter(entity =>
      entity.template_id === selectedSourceTemplateId.value
  );
});

// 过滤后的目标实体列表
const filteredTargetEntities = computed(() => {
  if (!selectedTargetTemplateId.value) return [];
  return entityList.value.filter(entity =>
      entity.template_id === selectedTargetTemplateId.value
  );
});

// 处理源模板变化
const handleSourceTemplateChange = (templateId) => {
  console.log('源模板变化为:', templateId);
  // 清空已选的源角色
  if (templateId !== selectedSourceTemplateId.value) {
    currentRelation.value.source = '';
    console.log('已清空源角色选择');
  }
};

// 处理目标模板变化
const handleTargetTemplateChange = (templateId) => {
  // 清空已选的目标角色
  relationForm.value.target = '';
};

// 重置关系表单
const resetRelationForm = () => {
  // 使用深拷贝方式创建新对象，避免引用原对象
  relationForm.value = {
    id: '',
    source: '',
    target: '',
    type: '',
    description: '',
    bidirectional: false,
    strength: 3,
    tags: []
  };
  
  // 重置模板选择
  selectedSourceTemplateId.value = '';
  selectedTargetTemplateId.value = '';
  
  // 重置编辑模式标记
  isEditMode.value = false;
};

// 计算属性
// 筛选实体列表
const filteredEntities = computed(() => {
  if (!Array.isArray(entityList.value)) {
    return []
  }

  let result = entityList.value

  // 按模板过滤
  if (selectedTemplateIds.value.length > 0) {
    result = result.filter(entity => {
      return selectedTemplateIds.value.some(templateId => {
        // 检查实体是否属于选定的模板
        return entity.template_id === templateId
      })
    })
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(entity => {
      return entity.name.toLowerCase().includes(keyword) ||
          (entity.description && entity.description.toLowerCase().includes(keyword))
    })
  }

  return result
})

// 是否有关系数据
const hasRelations = computed(() => Array.isArray(relations.value) && relations.value.length > 0)

// 当前选中的实体名称
const selectedEntityName = computed(() => {
  if (!selectedEntityId.value) return ''
  const entity = entityList.value.find(e => e.id === selectedEntityId.value)
  return entity ? entity.name : ''
})

// 当前实体的关系列表
const entityRelations = computed(() => {
  if (!selectedEntityId.value || !Array.isArray(relations.value)) return []

  return relations.value.filter(r =>
      r.source === selectedEntityId.value || r.target === selectedEntityId.value
  )
})

// 方法


// 获取实体类型名称
const getEntityType = (entity) => {
  if (!entity || !entity.template_id) return '未知类型'

  const template = templates.value.find(t => t.id === entity.template_id)
  return template ? template.name : '未知类型'
}

// 获取指定模板下的实体数量
const getEntityCountByTemplate = (templateId) => {
  return entityList.value.filter(entity => entity.template_id === templateId).length
}

// 获取实体的关系数量
const getEntityRelationsCount = (entityId) => {
  // 确保 relations.value 是数组
  if (!Array.isArray(relations.value)) {
    return 0;
  }
  return relations.value.filter(r =>
      r.source === entityId || r.target === entityId
  ).length
}

// 切换书籍
const handleBookChange = async (bookId) => {
  if (!bookId) return

  try {
    loading.value = true

    // 重置状态
    selectedEntityId.value = null
    selectedTemplateIds.value = []

    // 加载模板和实体
    await Promise.all([
      loadTemplates(bookId),
      loadEntities(bookId),
      loadRelations(bookId)  // 使用store加载关系
    ])

    console.log('加载完成：', {
      templates: templates.value.length,
      entities: entityList.value.length,
      relations: relations.value.length
    })

    // 初始化图表
    nextTick(() => {
      initGraph()
    })
  } catch (error) {
    console.error('加载书籍数据失败:', error)
    ElMessage.error('加载书籍数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载模板列表
const loadTemplates = async (bookId) => {
  if (!bookId) return

  try {
    const response = await window.pywebview.api.book_controller.get_templates(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      templates.value = result.data || []
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败: ' + (error.message || String(error)))
  }
}

// 加载实体列表
const loadEntities = async (bookId) => {
  try {
    if (!bookId) {
      entityList.value = []
      return
    }

    const response = await window.pywebview.api.book_controller.get_entities(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      entityList.value = Array.isArray(result.data) ? result.data : []
      console.log('实体加载成功：', entityList.value.length, '个实体')
    } else {
      console.error('加载实体失败:', result.message)
      ElMessage.error(result.message || '加载实体失败')
      entityList.value = []
    }
  } catch (error) {
    console.error('加载实体失败:', error)
    ElMessage.error('加载实体失败: ' + (error.message || String(error)))
    entityList.value = []
  }
}

// 加载关系数据
const loadRelations = async (bookId) => {
  try {
    // 如果没有选择书籍，则不加载
    if (!bookId) {
      relations.value = [];
      return;
    }

    // 使用store的获取关系方法
    const result = await relationshipStore.getRelations(bookId);
    // 确保 relations.value 是数组
    relations.value = Array.isArray(result) ? result : [];
    console.log('关系加载成功：', relations.value.length, '个关系')

    // 初始化图表
    nextTick(() => {
      initGraph();
    });
  } catch (error) {
    console.error('加载关系失败:', error);
    ElMessage.error('加载关系数据失败');
    relations.value = [];
  }
};

// 选择实体
const selectEntity = (entity) => {
  selectedEntityId.value = entity.id

  // 高亮实体在图谱中的节点
  if (chart.value) {
    highlightEntityInGraph(entity.id)
  }
}

// 显示添加关系对话框
const showAddRelationDialog = (sourceEntityId = null) => {
  // 确保选择了书籍
  if (!selectedBookId.value) {
    ElMessage.warning('请先选择一本书籍');
    return;
  }
  
  // 重置所有状态
  isEditingRelation.value = false;
  
  // 如果提供了sourceEntityId，使用它初始化关系
  if (sourceEntityId) {
    // 查找源实体
    const sourceEntity = entityList.value.find(e => e.id === sourceEntityId);
    if (sourceEntity) {
      // 设置当前实体
      currentEntity.value = sourceEntity;
      console.log('已从参数设置当前实体:', sourceEntity.name, sourceEntity.id);
      
      // 初始化关系数据
      currentRelation.value = {
        id: '',
        source: sourceEntityId,
        target: '',
        type: '友好',
        description: '',
        bidirectional: true,
        strength: 3,
        tags: []
      };
      
      // 预设源实体对应的模板
      selectedSourceTemplateId.value = sourceEntity.template_id || '';
    } else {
      console.warn('找不到ID为', sourceEntityId, '的实体');
    }
  } else {
    // 没有提供源实体ID，完全重置关系
    currentRelation.value = {
      id: '',
      source: '',
      target: '',
      type: '友好',
      description: '',
      bidirectional: true,
      strength: 3,
      tags: []
    };
    
    // 重置模板选择
    selectedSourceTemplateId.value = '';
    selectedTargetTemplateId.value = '';
  }
  
  // 打开弹窗
  relationEditVisible.value = true;
  
  console.log('打开添加关系弹窗，当前状态:', {
    currentEntityId: currentEntity.value?.id || null,
    currentRelationSource: currentRelation.value.source,
    currentRelationTarget: currentRelation.value.target
  });
};

// 显示编辑关系对话框 - 修复递归更新问题并支持两步选择
const showEditRelationDialog = (relation) => {
  // 先重置表单，防止之前的值影响
  resetRelationForm();

  // 使用 nextTick 确保 DOM 更新和响应式系统稳定
  nextTick(() => {
    // 设置编辑模式标志
    isEditMode.value = true;

    // 使用解构和扩展运算符创建一个新对象，避免直接引用
    relationForm.value = {
      id: relation.id || '',
      source: relation.source || '',
      target: relation.target || '',
      type: relation.type || '',
      description: relation.description || '',
      bidirectional: Boolean(relation.bidirectional),
      strength: relation.strength || 3,
      tags: Array.isArray(relation.tags) ? [...relation.tags] : []
    };

    // 设置源角色和目标角色所属的模板
    const sourceEntity = entityList.value.find(e => e.id === relation.source);
    if (sourceEntity) {
      selectedSourceTemplateId.value = sourceEntity.template_id;
    }

    const targetEntity = entityList.value.find(e => e.id === relation.target);
    if (targetEntity) {
      selectedTargetTemplateId.value = targetEntity.template_id;
    }

    // 打开对话框
    relationDialog.value = true;
  });
};

// 提交关系表单 - 修复递归更新问题
const submitRelationForm = async () => {
  // 表单验证
  if (!relationForm.value.source || !relationForm.value.target || !relationForm.value.type) {
    ElMessage.error('请完整填写关系信息');
    return;
  }

  try {
    // 创建一个新对象进行操作，避免直接修改响应式数据
    const formData = { ...relationForm.value };
    
    // 确保关系的必要字段都存在
    formData.strength = formData.strength || 3;
    formData.tags = Array.isArray(formData.tags) ? formData.tags : [];
    formData.bidirectional = Boolean(formData.bidirectional);
    
    if (isEditMode.value) {
      // 获取关系ID
      const relationId = formData.id;
      if (!relationId) {
        throw new Error('关系ID不能为空');
      }
      
      // 执行更新关系操作
      await updateRelation(relationId, formData);
      ElMessage.success('关系更新成功');
    } else {
      // 执行添加关系操作
      await addRelation(formData);
      ElMessage.success('关系添加成功');
    }

    // 先关闭对话框，再重置表单
    relationDialog.value = false;

    // 确保对话框已完全关闭
    nextTick(async () => {
      // 重置表单
      resetRelationForm();
      // 重新加载数据
      await loadRelations(selectedBookId.value);
      // 重新初始化图表
      initGraph();
    });
  } catch (error) {
    console.error('保存关系失败:', error);
    ElMessage.error('操作失败: ' + (error.message || '请重试'));
  }
};

// 更新关系的方法 - 修复参数传递
const updateRelation = async (relationId, relation) => {
  if (!relationId) {
    throw new Error('关系ID不能为空');
  }
  
  // 使用store的更新关系方法 - 正确传递参数
  return await relationshipStore.updateRelation(relationId, relation);
};

// 获取实体名称通过ID
const getEntityNameById = (id) => {
  const entity = entityList.value.find(e => e.id === id)
  return entity ? entity.name : '未知角色'
}

// 获取实体颜色
const getEntityColor = (entity) => {
  // 如果实体没有模板ID，则返回默认颜色
  if (!entity || !entity.template_id) return '#409EFF';

  // 查找实体对应的模板
  const template = templates.value.find(t => t.id === entity.template_id);
  if (!template) return '#409EFF';

  // 根据模板名称分配颜色
  const templateColors = {
    '正派人设': '#409EFF', // 蓝色
    '反派人设': '#F56C6C', // 红色
    '中立人设': '#67C23A', // 绿色
    '妖兽': '#E6A23C',    // 橙色
    '龙套角色': '#909399', // 灰色
    '装备': '#9370DB',    // 紫色
    '功法': '#FF9500',    // 橙黄色
    '道具': '#00CED1',    // 青色
    '场景': '#6D8B74'     // 灰绿色
  };

  return templateColors[template.name] || '#409EFF';
};

// 获取关系线条颜色
const getRelationLineColor = (relationType) => {
  // 根据关系类型返回不同颜色
  const colors = {
    '友好': '#67C23A',
    '敌对': '#F56C6C',
    '中立': '#909399',
    '血缘': '#E6A23C',
    '恋爱': '#FFB6C1',
    '师徒': '#409EFF',
    '上下级': '#9370DB',
    '合作': '#2E8B57',
    '竞争': '#FF7F50',
    '敬仰': '#00CED1',
    '蔑视': '#8B0000',
    '其他': '#A9A9A9'
  }

  return colors[relationType] || '#A9A9A9' // 默认灰色
}

// 重置图表
const resetGraph = () => {
  initGraph()
}

// 高亮实体在图谱中的节点
const highlightEntityInGraph = (entityId) => {
  if (!chart.value) return

  // 获取实体在数据中的索引
  const dataIndex = chart.value.getOption().series[0].data.findIndex(
      item => item.id === entityId
  )

  if (dataIndex === -1) return

  // 高亮节点
  chart.value.dispatchAction({
    type: 'highlight',
    seriesIndex: 0,
    dataIndex: dataIndex
  })

  // 显示相邻边
  chart.value.dispatchAction({
    type: 'focusNodeAdjacency',
    seriesIndex: 0,
    dataIndex: dataIndex
  })
}


// 获取特定模板下的所有实体
const getEntitiesByTemplate = (templateId) => {
  return entityList.value.filter(entity => entity.template_id === templateId)
}

// 获取关系类型的描述
const getRelationTypeDesc = (type) => {
  const descriptions = {
    '友好': '彼此友善，关系融洽',
    '敌对': '互相敌视，关系紧张',
    '中立': '保持距离，互不干涉',
    '血缘': '家族/血缘关系',
    '恋爱': '存在浪漫情感',
    '师徒': '授业解惑的传承关系',
    '上下级': '职位/阶级上的从属关系',
    '合作': '为共同目标协作',
    '竞争': '在相同领域争夺资源',
    '敬仰': '单方面的仰慕或崇拜',
    '蔑视': '单方面的轻视或鄙夷',
    '其他': '特殊关系类型'
  }

  return descriptions[type] || ''
}

// 获取关系类型对应的标签颜色
const getRelationTypeColor = (typeName) => {
  const typeObj = relationTypeOptions.value.find(t => t.value === typeName);
  // 如果找不到对应类型或颜色为空，返回 undefined 让组件使用默认值
  if (!typeObj || !typeObj.color) return undefined;
  return typeObj.color;
}

// 初始化关系图表
const initGraph = () => {
  if (!graphContainer.value) return

  // 如果已经初始化，先销毁
  if (chart.value) {
    chart.value.dispose()
  }

  // 创建图表实例
  chart.value = echarts.init(graphContainer.value)

  // 如果没有关系数据，返回
  if (!Array.isArray(relations.value) || relations.value.length === 0) {
    console.log('没有关系数据，无法初始化图表')
    return
  }

  // 准备节点数据：所有实体
  const nodes = []
  const nodeMap = new Map() // 用于快速查找节点

  // 遍历所有实体，首先添加有关系的实体
  const entityWithRelations = new Set()

  relations.value.forEach(relation => {
    if (relation && relation.source) entityWithRelations.add(relation.source)
    if (relation && relation.target) entityWithRelations.add(relation.target)
  })

  // 添加有关系的实体
  if (Array.isArray(entityList.value)) {
    entityList.value
        .filter(entity => entity && entity.id && entityWithRelations.has(entity.id))
        .forEach(entity => {
          const node = {
            id: entity.id,
            name: entity.name,
            value: getEntityRelationsCount(entity.id),
            symbolSize: 40 + getEntityRelationsCount(entity.id) * 5,
            itemStyle: {
              color: getEntityColor(entity)
            },
            label: {
              show: true
            },
            tooltip: {
              formatter: function() {
                return `<strong>${entity.name}</strong><br/>类型：${getEntityType(entity)}<br/>关系数：${getEntityRelationsCount(entity.id)}`
              }
            }
          }

          nodes.push(node)
          nodeMap.set(entity.id, node)
        })
  }

  // 准备边数据：所有关系
  const edges = relations.value.map(relation => {
    // 安全检查：确保关系和源目标节点都存在
    if (!relation || !relation.source || !relation.target) return null

    const sourceNode = nodeMap.get(relation.source)
    const targetNode = nodeMap.get(relation.target)

    if (!sourceNode || !targetNode) return null

    return {
      source: relation.source,
      target: relation.target,
      relationId: relation.id, // 存储关系ID以便点击时识别
      value: relation.strength || 1,
      label: {
        show: true,
        formatter: relation.type || '未知关系',
        fontSize: 12
      },
      lineStyle: {
        width: relation.strength || 1,
        color: getRelationLineColor(relation.type),
        type: relation.bidirectional ? 'solid' : 'dashed',
        curveness: 0.1
      },
      tooltip: {
        formatter: function() {
          const sourceEntity = entityList.value.find(e => e && e.id === relation.source)
          const targetEntity = entityList.value.find(e => e && e.id === relation.target)

          return `<strong>${sourceEntity?.name || '未知'} → ${targetEntity?.name || '未知'}</strong><br/>
                  类型：${relation.type || '未知'}<br/>
                  强度：${relation.strength || 1}<br/>
                  ${relation.description ? `描述：${relation.description}<br/>` : ''}
                  ${relation.bidirectional ? '双向关系' : '单向关系'}`
        }
      },
      // 箭头样式
      symbol: ['none', relation.bidirectional ? 'arrow' : 'arrow'],
      symbolSize: [5, 8]
    }
  }).filter(Boolean) // 过滤掉空值

  // 添加其余的图表初始化代码

  // 如果没有节点或边，显示空状态
  if (nodes.length === 0 || edges.length === 0) {
    console.log('图表无数据，节点数:', nodes.length, '边数:', edges.length)
    return
  }

  try {
    // 图表配置
    const option = {
      tooltip: {},
      legend: {
        show: false
      },
      animationDuration: 1500,
      animationEasingUpdate: 'quinticInOut',
      series: [{
        type: 'graph',
        layout: 'force',
        data: nodes,
        links: edges,
        roam: true,
        label: {
          show: true,
          position: 'right',
          formatter: '{b}'
        },
        force: {
          repulsion: 300, // 节点间斥力
          gravity: 0.1,   // 重力
          edgeLength: 150, // 边长
          layoutAnimation: true
        },
        lineStyle: {
          color: 'source',
          curveness: 0.2
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 6
          }
        }
      }]
    }

    // 设置图表配置
    chart.value.setOption(option)

    // 添加点击事件
    chart.value.on('click', {
      node: function(params) {
        if (!params.data || !params.data.id) return
        const entity = entityList.value.find(e => e && e.id === params.data.id)
        if (entity) {
          selectEntity(entity)
        }
      },
      edge: function(params) {
        if (!params.data || !params.data.relationId) return
        const relation = relations.value.find(r => r && r.id === params.data.relationId)
        if (relation) {
          showEditRelationDialog(relation)
        }
      }
    })
  } catch (error) {
    console.error('初始化图表失败:', error)
    ElMessage.error('初始化图表失败，请刷新页面重试')
  }
}

// 添加关系的方法 - 确保返回值
const addRelation = async (relation) => {
  try {
    const result = await relationshipStore.addRelation(relation);
    console.log('添加关系成功，返回结果:', result);
    // 确保result中包含我们需要的字段
    if (!result || !result.id) {
      console.warn('添加关系返回结果缺少关键字段:', result);
    }
    
    // 刷新图谱显示
    await loadRelations(selectedBookId.value);
    
    ElMessage.success('关系添加成功');
  } catch (addError) {
    console.error('添加关系失败:', addError);
    ElMessage.error('添加关系失败: ' + (addError.message || '未知错误'));
    throw addError;
  }
};

// 确认删除关系
const confirmDeleteRelation = (relationId) => {
  ElMessageBox.confirm(
      '确定要删除这个关系吗？此操作不可恢复。',
      '删除关系',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(async () => {
    try {
      await relationshipStore.deleteRelation(relationId);
      ElMessage.success('关系已删除');
      // 重新加载关系并刷新图表
      await loadRelations(selectedBookId.value);
      initGraph();
    } catch (error) {
      console.error('删除关系失败:', error);
      ElMessage.error('删除关系失败');
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 生命周期钩子
onMounted(async () => {
  try {
    // 确保书籍列表已加载
    await bookStore.loadBooks()

    // 如果有书籍列表，默认选择第一本书
    if (bookStore.bookList.length > 0) {
      selectedBookId.value = bookStore.bookList[0].id
      await handleBookChange(selectedBookId.value)
    } else {
      loading.value = false
      ElMessage.warning('请先创建一本书籍')
    }

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', () => {
      if (chart.value) {
        chart.value.resize()
      }
    })
  } catch (error) {
    console.error('初始化失败:', error)
    loading.value = false
    ElMessage.error('初始化失败: ' + (error.message || String(error)))
  }
})

// 添加实体关系弹窗
const entityRelationsDialog = ref(false)
const currentEntityForRelations = ref(null)
const currentEntityRelations = ref([])

// 显示实体关系弹窗
const showEntityRelationsDialog = (entity) => {
  currentEntityForRelations.value = entity
  if (Array.isArray(relations.value)) {
    currentEntityRelations.value = relations.value.filter(relation =>
        relation.source === entity.id || relation.target === entity.id
    )
  } else {
    currentEntityRelations.value = []
  }
  entityRelationsDialog.value = true
}

// 添加新关系 - 自动预填充当前选中的角色
const addNewRelation = () => {
  console.log('添加角色关系，当前选中实体ID:', selectedEntityId.value);
  console.log('当前书籍ID:', selectedBookId.value);

  // 检查是否已选择书籍
  if (!selectedBookId.value) {
    ElMessage.warning('请先选择一本书籍');
    return;
  }

  // 重置编辑状态
  isEditingRelation.value = false;
  
  // 重置当前选中的模板
  selectedSourceTemplateId.value = '';
  selectedTargetTemplateId.value = '';
  
  // 获取当前选中的实体
  const sourceEntityId = selectedEntityId.value || '';
  const sourceEntity = sourceEntityId 
    ? entityList.value.find(e => e.id === sourceEntityId) 
    : null;
  
  if (!sourceEntity) {
    console.warn('未选中源实体，或找不到对应实体');
    ElMessage.warning('请先选择一个角色');
    return;
  }
  
  // 确保currentEntity被正确设置 - 关键修复
  currentEntity.value = sourceEntity;
  
  console.log('找到源实体:', sourceEntity.name, '模板ID:', sourceEntity.template_id);
  
  // 重置并创建关系对象并预填充数据
  currentRelation.value = {
    id: '', // 使用空字符串而不是null
    source: sourceEntityId,  // 预填充源实体ID
    type: '友好',  // 默认关系类型
    target: '',
    description: '',
    bidirectional: true,
    strength: 3,
    tags: []
  };
  
  // 预设源实体对应的模板
  selectedSourceTemplateId.value = sourceEntity.template_id || '';
  
  // 确保模板选择器显示正确
  nextTick(() => {
    console.log('预选模板设置完成，值为:', selectedSourceTemplateId.value);
    console.log('当前关系对象:', JSON.stringify(currentRelation.value));
    console.log('当前实体已设置:', currentEntity.value.name, currentEntity.value.id);
  });
  
  // 打开关系编辑弹窗
  relationEditVisible.value = true;
};

// 实体属性弹窗相关
const entityAttributesVisible = ref(false);
const currentEntity = ref(null);
const entityAttributes = computed(() => {
  if (!currentEntity.value) return {};

  // 过滤掉不需要显示的内部属性
  const filteredAttributes = { ...currentEntity.value };
  ['_id', '_rev', 'id'].forEach(key => delete filteredAttributes[key]);

  return filteredAttributes;
});

// 关系管理弹窗相关
const relationManagerVisible = ref(false);
const relationsList = ref([]);

// 关系类型列表
const relationTypes = ref([
  '朋友', '亲人', '恋人', '同事', '上下级', '敌人', '师徒', '邻居', '同学'
]);
const showCustomRelationType = ref(false);
const customRelationType = ref('');

// 添加自定义关系类型
const addCustomRelationType = () => {
  if (customRelationType.value && !relationTypes.value.includes(customRelationType.value)) {
    relationTypes.value.push(customRelationType.value);
    currentRelation.value.type = customRelationType.value;
  }
  showCustomRelationType.value = false;
  customRelationType.value = '';
};

// 关系编辑弹窗相关
const relationEditVisible = ref(false);
const isEditingRelation = ref(false);
const currentRelation = ref({
  id: null,
  source: '',
  type: '',
  target: '',
  description: '',
  bidirectional: false,
  tags: []
});
const currentRelationIndex = ref(-1);

// 显示关系管理弹窗
const showRelationManager = (entity) => {
  currentEntity.value = entity;
  // 加载关系列表
  loadRelationsList(entity.id);
  relationManagerVisible.value = true;
};

// 加载关系列表
const loadRelationsList = async (entityId) => {
  try {
    // 这里应该是从后端获取关系列表的代码
    // 示例: 从所有关系中筛选出与当前实体相关的关系
    relationsList.value = allRelations.value.filter(
        relation => relation.source === entityId || relation.target === entityId
    );
  } catch (error) {
    console.error('加载关系列表失败:', error);
    ElMessage.error('加载关系列表失败');
  }
};

// 编辑关系
const editRelation = (relation) => {
  isEditingRelation.value = true;
  currentRelation.value = { ...relation };
  currentRelationIndex.value = relationsList.value.findIndex(r => r.id === relation.id);
  relationEditVisible.value = true;
};

// 删除关系
const deleteRelation = async (relation) => {
  try {
    await relationshipStore.deleteRelation(relation.id);
    ElMessage.success('关系已删除');

    // 重新加载关系数据
    await loadRelations(selectedBookId.value);

    // 更新图表
    initGraph();

    // 关闭弹窗
    relationManagerVisible.value = false;
  } catch (error) {
    console.error('删除关系失败:', error);
    ElMessage.error('删除关系失败');
  }
};

// 保存关系
const saveRelation = async () => {
  try {
    console.log('保存关系，状态检查:');
    console.log('- 当前关系数据:', JSON.stringify(currentRelation.value));
    console.log('- 当前书籍ID:', selectedBookId.value);
    
    // 验证书籍ID
    if (!selectedBookId.value) {
      ElMessage.warning('请先选择一本书籍');
      return;
    }
    
    // 验证表单
    if (!currentRelation.value.source || !currentRelation.value.type || !currentRelation.value.target) {
      ElMessage({
        type: 'warning',
        message: '请填写完整的关系信息'
      });
      return;
    }

    // 检查源实体和目标实体是否相同
    if (currentRelation.value.source === currentRelation.value.target) {
      ElMessage({
        type: 'warning',
        message: '源实体和目标实体不能相同'
      });
      return;
    }

    // 这里应该是保存关系的后端调用
    if (isEditingRelation.value && currentRelation.value.id) {
      console.log('更新现有关系:', currentRelation.value.id);
      
      await relationshipStore.updateRelation(currentRelation.value.id, {
        ...currentRelation.value,
        bookId: selectedBookId.value
      });
      ElMessage.success('关系更新成功');
    } else {
      console.log('添加新关系');
      const newRelation = {
        bookId: selectedBookId.value,
        source: currentRelation.value.source,
        target: currentRelation.value.target,
        type: currentRelation.value.type,
        description: currentRelation.value.description || '',
        bidirectional: currentRelation.value.bidirectional || false,
        strength: currentRelation.value.strength || 3,
        tags: currentRelation.value.tags || []
      };
      
      console.log('准备提交的新关系数据:', JSON.stringify(newRelation));
      
      try {
        const result = await relationshipStore.addRelation(newRelation);
        console.log('添加关系成功，返回结果:', result);
        // 确保result中包含我们需要的字段
        if (!result || !result.id) {
          console.warn('添加关系返回结果缺少关键字段:', result);
        }
        
        // 刷新图谱显示
        await loadRelations(selectedBookId.value);
        
        ElMessage.success('关系添加成功');
      } catch (addError) {
        console.error('添加关系失败:', addError);
        ElMessage.error('添加关系失败: ' + (addError.message || '未知错误'));
        throw addError;
      }
    }

    // 更新当前显示的关系列表 - 关键修复
    // 只有在currentEntity存在且有id时才调用loadRelationsList
    if (currentEntity.value && currentEntity.value.id) {
      loadRelationsList(currentEntity.value.id);
    } else {
      console.log('没有当前实体或从全屏模式添加，跳过更新关系列表');
    }

    // 重新加载关系数据
    await loadRelations(selectedBookId.value);
    
    // 更新图谱
    initGraph();

    // 关闭弹窗
    relationEditVisible.value = false;
  } catch (error) {
    console.error('保存关系失败:', error);
    console.error('保存关系失败时的关系数据:', JSON.stringify(currentRelation.value));
    console.error('保存关系失败时的书籍ID:', selectedBookId.value);
    ElMessage({
      type: 'error',
      message: '保存关系失败：' + (error.message || '请检查网络连接')
    });
  }
};

// 添加 updateGraph 函数作为 initGraph 的别名
// 这样调用 updateGraph() 的代码就不会报错
const updateGraph = () => {
  console.log('调用 updateGraph 重定向到 initGraph');
  return initGraph();
};

// 添加关系过滤搜索相关变量
const relationSearchKeyword = ref('');
const relationTypeFilter = ref('');
const availableRelationTypes = computed(() => {
  // 从关系列表中提取不重复的关系类型
  if (!relationsList.value || !Array.isArray(relationsList.value)) return [];
  const types = new Set();
  relationsList.value.forEach(relation => {
    if (relation.type) types.add(relation.type);
  });
  return Array.from(types);
});

// 过滤后的关系列表
const filteredRelationsList = computed(() => {
  if (!relationsList.value || !Array.isArray(relationsList.value)) return [];
  
  let result = [...relationsList.value];
  
  // 按关系类型筛选
  if (relationTypeFilter.value) {
    result = result.filter(relation => relation.type === relationTypeFilter.value);
  }
  
  // 按关键词搜索
  if (relationSearchKeyword.value) {
    const keyword = relationSearchKeyword.value.toLowerCase();
    result = result.filter(relation => {
      // 搜索关系类型
      if (relation.type && relation.type.toLowerCase().includes(keyword)) {
        return true;
      }
      
      // 搜索相关角色名称
      const relatedEntityId = relation.source === currentEntity.value?.id 
        ? relation.target 
        : relation.source;
      const relatedEntity = entityList.value.find(e => e.id === relatedEntityId);
      return relatedEntity && relatedEntity.name.toLowerCase().includes(keyword);
    });
  }
  
  return result;
});

// 清除关系过滤条件
const clearRelationFilters = () => {
  relationSearchKeyword.value = '';
  relationTypeFilter.value = '';
};

// 过滤关系
const filterRelations = () => {
  // 输入变化时自动更新筛选结果
  // 由于使用了计算属性，不需要其他逻辑
};

// 在来源处添加调试日志
const handleEntityClick = (entity) => {
  console.log('点击实体:', entity.name, '实体ID:', entity.id);
  selectedEntityId.value = entity.id;
  // 其他代码...
};

// 标签管理相关函数
// 显示标签输入框
const showTagInput = () => {
  tagInputVisible.value = true;
  nextTick(() => {
    if (tagInput.value) {
      tagInput.value.focus();
    }
  });
};

// 添加标签
const addTag = () => {
  if (tagInputValue.value) {
    if (!currentRelation.value.tags) {
      currentRelation.value.tags = [];
    }
    
    if (!currentRelation.value.tags.includes(tagInputValue.value)) {
      currentRelation.value.tags.push(tagInputValue.value);
    }
  }
  tagInputVisible.value = false;
  tagInputValue.value = '';
};

// 移除标签
const removeTag = (tag) => {
  if (currentRelation.value.tags) {
    const index = currentRelation.value.tags.indexOf(tag);
    if (index > -1) {
      currentRelation.value.tags.splice(index, 1);
    }
  }
};

// 处理全屏模式下边的点击
const handleFullscreenEdgeClick = (relation) => {
  console.log('点击关系线:', relation);
  // 打开编辑关系弹窗
  showEditRelationDialog(relation);
};

// 获取强度渐变颜色 - 接受参数版本，更通用
const getStrengthGradient = (strength) => {
  if (strength <= 2) {
    return 'linear-gradient(90deg, #909399, #A3C5FD)';
  } else if (strength <= 4) {
    return 'linear-gradient(90deg, #409EFF, #FF9F43)';
  } else {
    return 'linear-gradient(90deg, #FF9F43, #FF6D6A)';
  }
};

// 格式化强度提示文本 - 添加回这个函数
const formatStrengthTooltip = (val) => {
  return ['很弱', '较弱', '一般', '较强', '很强'][val-1];
};

// 定义强度标记和颜色
const strengthMarks = {
  1: { label: '弱', style: { color: '#909399', transform: 'translateY(10px)' } },
  3: { label: '中', style: { color: '#409EFF', transform: 'translateY(10px)' } },
  5: { label: '强', style: { color: '#FF6D6A', transform: 'translateY(10px)' } }
};

// 更新计算属性，使用统一的函数
const strengthGradient = computed(() => {
  return getStrengthGradient(currentRelation.value.strength);
});

