const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const copyFrontend = require('./copy-frontend');

class DevServer {
  constructor() {
    this.electronProcess = null;
    this.rendererProcess = null;
    this.isShuttingDown = false;
  }

  async start() {
    try {
      console.log('🚀 启动 Electron 开发环境...\n');
      
      // 1. 复制前端文件
      await copyFrontend();
      
      // 2. 安装渲染进程依赖
      await this.installRendererDependencies();
      
      // 3. 启动渲染进程开发服务器
      await this.startRenderer();
      
      // 4. 等待渲染进程启动
      await this.waitForRenderer();
      
      // 5. 启动主进程
      await this.startElectron();
      
      // 6. 设置进程清理
      this.setupCleanup();
      
    } catch (error) {
      console.error('❌ 启动失败:', error);
      await this.cleanup();
      process.exit(1);
    }
  }

  async installRendererDependencies() {
    console.log('📦 检查渲染进程依赖...');
    
    const rendererDir = path.join(__dirname, '../renderer');
    const nodeModulesPath = path.join(rendererDir, 'node_modules');
    
    if (!await fs.pathExists(nodeModulesPath)) {
      console.log('📦 安装渲染进程依赖...');
      
      return new Promise((resolve, reject) => {
        const npm = spawn('pnpm', ['install'], {
          cwd: rendererDir,
          stdio: 'inherit',
          shell: true
        });
        
        npm.on('close', (code) => {
          if (code === 0) {
            console.log('✅ 渲染进程依赖安装完成\n');
            resolve();
          } else {
            reject(new Error(`依赖安装失败，退出码: ${code}`));
          }
        });
        
        npm.on('error', reject);
      });
    } else {
      console.log('✅ 渲染进程依赖已存在\n');
    }
  }

  async startRenderer() {
    console.log('🌐 启动渲染进程开发服务器...');
    
    const rendererDir = path.join(__dirname, '../renderer');
    
    this.rendererProcess = spawn('pnpm', ['run', 'dev'], {
      cwd: rendererDir,
      stdio: 'pipe',
      shell: true
    });
    
    this.rendererProcess.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Local:') || output.includes('ready')) {
        console.log('📱 渲染进程:', output.trim());
      }
    });
    
    this.rendererProcess.stderr.on('data', (data) => {
      const error = data.toString();
      if (!error.includes('Warning') && !error.includes('deprecated')) {
        console.error('📱 渲染进程错误:', error.trim());
      }
    });
    
    this.rendererProcess.on('close', (code) => {
      if (!this.isShuttingDown) {
        console.log(`📱 渲染进程退出，代码: ${code}`);
      }
    });
  }

  async waitForRenderer() {
    console.log('⏳ 等待渲染进程启动...');
    
    const maxAttempts = 30;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      try {
        const response = await fetch('http://localhost:13001');
        if (response.ok) {
          console.log('✅ 渲染进程已启动\n');
          return;
        }
      } catch (error) {
        // 继续等待
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
    }
    
    throw new Error('渲染进程启动超时');
  }

  async startElectron() {
    console.log('⚡ 启动 Electron 主进程...');
    
    const electronPath = path.join(__dirname, '../node_modules/.bin/electron');
    const mainPath = path.join(__dirname, '../src/main/main.js');
    
    this.electronProcess = spawn(electronPath, [mainPath], {
      stdio: 'inherit',
      shell: true,
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    });
    
    this.electronProcess.on('close', (code) => {
      if (!this.isShuttingDown) {
        console.log(`⚡ Electron 进程退出，代码: ${code}`);
        this.cleanup();
      }
    });
    
    console.log('✅ Electron 应用已启动!\n');
  }

  setupCleanup() {
    const cleanup = () => {
      if (!this.isShuttingDown) {
        console.log('\n🛑 正在关闭开发服务器...');
        this.cleanup();
      }
    };
    
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    process.on('exit', cleanup);
  }

  async cleanup() {
    this.isShuttingDown = true;
    
    if (this.electronProcess) {
      this.electronProcess.kill();
      this.electronProcess = null;
    }
    
    if (this.rendererProcess) {
      this.rendererProcess.kill();
      this.rendererProcess = null;
    }
    
    console.log('✅ 清理完成');
  }
}

// 启动开发服务器
if (require.main === module) {
  const devServer = new DevServer();
  devServer.start();
}

module.exports = DevServer;
