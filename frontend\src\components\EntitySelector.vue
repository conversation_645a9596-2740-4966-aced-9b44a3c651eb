<template>
  <Teleport to="body">
    <div
      v-if="visible"
      ref="selectorRef"
      class="entity-selector"
      :style="{ top: position.top + 'px', left: position.left + 'px' }"
      @click.stop
      @keydown="handleKeydown"
      tabindex="-1"
    >


    <div class="selector-header">
      <el-input
        v-model="localSearchQuery"
        placeholder="搜索实体..."
        size="small"
        clearable
        @input="handleSearch"
        @keydown="handleKeydown"
        ref="searchInputRef"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>
    
    <div class="selector-content">
      <!-- 主选项 -->
      <div class="main-options">
        <div
          class="option-item main-option"
          @click="selectAllEntitiesMode"
          :class="{ active: mode === 'all-entities' }"
        >
          <el-icon><Collection /></el-icon>
          <span>选择模板下的所有实体</span>
        </div>
        <div
          class="option-item main-option"
          @click="selectSingleEntityMode"
          :class="{ active: mode === 'single-entity' }"
        >
          <el-icon><User /></el-icon>
          <span>选择单个实体</span>
        </div>
        <div
          class="option-item main-option"
          @click="selectChapterMode"
          :class="{ active: mode === 'chapters' }"
        >
          <el-icon><Reading /></el-icon>
          <span>选择章节目录</span>
        </div>
        <div
          class="option-item main-option"
          @click="selectScenePoolMode"
          :class="{ active: mode === 'scene-pools' }"
        >
          <el-icon><Collection /></el-icon>
          <span>选择场景卡池</span>
        </div>
      </div>
      
      <!-- 模板列表（选择所有实体模式） -->
      <div v-if="mode === 'all-entities'" class="template-list">
        <div class="section-title">选择模板</div>
        <div
          v-for="(template, index) in filteredTemplates"
          :key="template.id"
          :data-index="index"
          class="option-item template-item"
          :class="{ selected: selectedIndex === index }"
          @click="selectTemplate(template)"
        >
          <el-icon><Folder /></el-icon>
          <span class="template-name">{{ template.name }}</span>
          <span class="entity-count">{{ getEntityCount(template) }}个实体</span>
        </div>
      </div>
      
      <!-- 实体列表（选择单个实体模式） -->
      <div v-if="mode === 'single-entity'" class="entity-list">
        <div class="section-title">选择实体</div>
        <div v-for="template in filteredTemplatesWithEntities" :key="template.id" class="template-group">
          <div class="template-header">
            <el-icon><Folder /></el-icon>
            <span>{{ template.name }}</span>
            <span class="count">({{ template.entities.length }})</span>
          </div>
          <div
            v-for="(entity, entityIndex) in template.entities"
            :key="entity.id || entity.name"
            :data-index="getEntityGlobalIndex(template, entityIndex)"
            class="option-item entity-item"
            :class="{ selected: selectedIndex === getEntityGlobalIndex(template, entityIndex) }"
            @click="selectEntity(entity, template)"
          >
            <el-icon><Document /></el-icon>
            <div class="entity-info">
              <div class="entity-name-line">
                <span class="entity-name">{{ entity.name }}</span>
                <span v-if="template.isSearchResult && entity.templateInfo" class="template-badge">
                  {{ entity.templateInfo.name }}
                </span>
              </div>
              <span class="entity-desc" v-if="entity.description">{{ entity.description }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 章节目录列表（选择章节模式） -->
      <div v-if="mode === 'chapters'" class="chapters-list">
        <div class="section-title">选择章节</div>
        <div v-if="filteredVolumes.length > 0">
          <div v-for="volume in filteredVolumes" :key="volume.id" class="volume-group">
            <div class="volume-header">
              <el-icon><Folder /></el-icon>
              <span>{{ volume.title }}</span>
              <span class="count">({{ volume.chapters?.length || 0 }}章)</span>
            </div>
            <div
              v-for="(chapter, chapterIndex) in volume.chapters"
              :key="chapter.id"
              :data-index="getChapterGlobalIndex(volume, chapterIndex)"
              class="option-item chapter-item"
              :class="{ selected: selectedIndex === getChapterGlobalIndex(volume, chapterIndex) }"
              @click="selectChapter(chapter, volume)"
            >
              <el-icon><Document /></el-icon>
              <div class="chapter-info">
                <div class="chapter-name-line">
                  <span class="chapter-name">{{ chapter.title }}</span>
                  <span class="chapter-order">第{{ chapter.order }}章</span>
                </div>
                <span class="chapter-desc" v-if="chapter.summary">{{ chapter.summary }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <el-empty description="暂无章节" />
        </div>
      </div>

      <!-- 场景卡池列表（选择场景卡池模式） -->
      <div v-if="mode === 'scene-pools'" class="scene-pools-list">
        <div class="section-title">选择场景卡池</div>
        <div v-if="filteredScenePools.length > 0">
          <div
            v-for="(pool, poolIndex) in filteredScenePools"
            :key="pool.id"
            :data-index="poolIndex"
            class="option-item scene-pool-item"
            :class="{ selected: selectedIndex === poolIndex }"
            @click="selectScenePool(pool)"
          >
            <el-icon><Collection /></el-icon>
            <div class="pool-info">
              <div class="pool-name-line">
                <span class="pool-name">{{ pool.name }}</span>
                <span class="scene-count">{{ pool.scenes?.length || 0 }}个场景</span>
              </div>
              <span class="pool-desc" v-if="pool.description">{{ pool.description }}</span>
            </div>
          </div>
        </div>
        <div v-else>
          <el-empty description="暂无场景卡池" />
        </div>
      </div>
    </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, Teleport } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Collection, User, Folder, Document, Reading } from '@element-plus/icons-vue'

const props = defineProps({
  visible: Boolean,
  bookId: {
    type: [String, Number],
    required: true
  },
  position: {
    type: Object,
    default: () => ({ top: 0, left: 0 })
  },
  searchQuery: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['select', 'close'])

// 响应式数据
const templates = ref([])
const entities = ref([])
const volumes = ref([]) // 书籍卷和章节
const scenePools = ref([]) // 场景卡池
const mode = ref('') // 'all-entities' | 'single-entity' | 'chapters' | 'scene-pools'
const localSearchQuery = ref('')
const searchInputRef = ref(null)

// 键盘导航状态
const selectedIndex = ref(-1) // 当前选中的项目索引
const selectorRef = ref(null)

// 计算属性
const filteredTemplates = computed(() => {
  if (!localSearchQuery.value) return templates.value
  const query = localSearchQuery.value.toLowerCase()
  return templates.value.filter(template => 
    template.name.toLowerCase().includes(query)
  )
})

const filteredTemplatesWithEntities = computed(() => {
  const query = localSearchQuery.value.toLowerCase()

  if (!query) {
    // 没有搜索查询时，按模板分组显示
    return templates.value.map(template => {
      const templateEntities = entities.value.filter(entity =>
        entity.template_id === template.id
      )

      return {
        ...template,
        entities: templateEntities
      }
    }).filter(template => template.entities.length > 0)
  } else {
    // 有搜索查询时，全局搜索所有实体，不按模板分组
    const allMatchingEntities = entities.value.filter(entity =>
      entity.name.toLowerCase().includes(query) ||
      (entity.description && entity.description.toLowerCase().includes(query))
    )

    if (allMatchingEntities.length === 0) {
      return []
    }

    // 为每个匹配的实体添加其对应的模板信息
    const entitiesWithTemplates = allMatchingEntities.map(entity => {
      const entityTemplate = templates.value.find(t => t.id === entity.template_id)
      return {
        ...entity,
        templateInfo: entityTemplate
      }
    })

    // 创建一个虚拟的"搜索结果"模板来包含所有匹配的实体
    return [{
      id: 'search-results',
      name: `搜索结果`,
      isSearchResult: true,
      entities: entitiesWithTemplates
    }]
  }
})

// 过滤的卷和章节
const filteredVolumes = computed(() => {
  if (!localSearchQuery.value) return volumes.value
  const query = localSearchQuery.value.toLowerCase()

  return volumes.value.map(volume => {
    // 过滤章节
    const filteredChapters = (volume.chapters || []).filter(chapter =>
      chapter.title.toLowerCase().includes(query) ||
      (chapter.summary && chapter.summary.toLowerCase().includes(query))
    )

    // 如果卷标题匹配或有匹配的章节，则包含此卷
    if (volume.title.toLowerCase().includes(query) || filteredChapters.length > 0) {
      return {
        ...volume,
        chapters: filteredChapters
      }
    }
    return null
  }).filter(Boolean)
})

// 过滤的场景卡池
const filteredScenePools = computed(() => {
  if (!localSearchQuery.value) return scenePools.value
  const query = localSearchQuery.value.toLowerCase()

  return scenePools.value.filter(pool =>
    pool.name.toLowerCase().includes(query) ||
    (pool.description && pool.description.toLowerCase().includes(query))
  )
})

// 获取所有可选择的项目（用于键盘导航）
const selectableItems = computed(() => {
  const items = []

  if (mode.value === 'all-entities') {
    // 模板选择模式
    filteredTemplates.value.forEach(template => {
      items.push({
        type: 'template',
        data: template,
        key: `template-${template.id}`
      })
    })
  } else if (mode.value === 'single-entity') {
    // 实体选择模式
    filteredTemplatesWithEntities.value.forEach(template => {
      template.entities.forEach(entity => {
        items.push({
          type: 'entity',
          data: entity,
          template: template,
          key: `entity-${entity.id || entity.name}`
        })
      })
    })
  } else if (mode.value === 'chapters') {
    // 章节选择模式
    filteredVolumes.value.forEach(volume => {
      (volume.chapters || []).forEach(chapter => {
        items.push({
          type: 'chapter',
          data: chapter,
          volume: volume,
          key: `chapter-${chapter.id}`
        })
      })
    })
  } else if (mode.value === 'scene-pools') {
    // 场景卡池选择模式
    filteredScenePools.value.forEach(pool => {
      items.push({
        type: 'scenePool',
        data: pool,
        key: `pool-${pool.id}`
      })
    })
  }

  return items
})

// 方法
const loadTemplates = async () => {
  try {
    console.log('EntitySelector: 加载模板，bookId:', props.bookId)
    if (!props.bookId) {
      ElMessage.error('bookId 未提供')
      return
    }

    const response = await window.pywebview.api.book_controller.get_templates(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('EntitySelector: 模板加载结果:', result)

    if (result.status === 'success') {
      templates.value = result.data || []
      console.log('EntitySelector: 加载到模板数量:', templates.value.length)
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败：' + error.message)
  }
}

const loadEntities = async () => {
  try {
    console.log('EntitySelector: 加载实体，bookId:', props.bookId)
    if (!props.bookId) {
      ElMessage.error('bookId 未提供')
      return
    }

    const response = await window.pywebview.api.book_controller.get_entities(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('EntitySelector: 实体加载结果:', result)

    if (result.status === 'success') {
      entities.value = result.data || []
      console.log('EntitySelector: 加载到实体数量:', entities.value.length)
    } else {
      ElMessage.error(result.message || '加载实体失败')
    }
  } catch (error) {
    console.error('加载实体失败:', error)
    ElMessage.error('加载实体失败：' + error.message)
  }
}

const loadVolumes = async () => {
  try {
    console.log('EntitySelector: 加载卷和章节，bookId:', props.bookId)
    if (!props.bookId) {
      ElMessage.error('bookId 未提供')
      return
    }

    const response = await window.pywebview.api.book_controller.get_volumes(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('EntitySelector: 卷和章节加载结果:', result)

    if (result.status === 'success') {
      volumes.value = result.data || []
      console.log('EntitySelector: 加载到卷数量:', volumes.value.length)
    } else {
      ElMessage.error(result.message || '加载卷和章节失败')
    }
  } catch (error) {
    console.error('加载卷和章节失败:', error)
    ElMessage.error('加载卷和章节失败：' + error.message)
  }
}

const loadScenePools = async () => {
  try {
    console.log('EntitySelector: 加载场景卡池，bookId:', props.bookId)
    if (!props.bookId) {
      ElMessage.error('bookId 未提供')
      return
    }

    const response = await window.pywebview.api.book_controller.get_scene_events(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('EntitySelector: 场景卡池加载结果:', result)

    if (result.status === 'success') {
      const sceneData = result.data || {}
      scenePools.value = sceneData.pools || []
      console.log('EntitySelector: 加载到场景卡池数量:', scenePools.value.length)
    } else {
      ElMessage.error(result.message || '加载场景卡池失败')
    }
  } catch (error) {
    console.error('加载场景卡池失败:', error)
    ElMessage.error('加载场景卡池失败：' + error.message)
  }
}

const getEntityCount = (template) => {
  return entities.value.filter(entity => entity.template_id === template.id).length
}

const selectAllEntitiesMode = () => {
  mode.value = 'all-entities'
}

const selectSingleEntityMode = () => {
  mode.value = 'single-entity'
}

const selectChapterMode = () => {
  mode.value = 'chapters'
}

const selectScenePoolMode = () => {
  mode.value = 'scene-pools'
}

const selectTemplate = (template) => {
  const templateEntities = entities.value.filter(entity => entity.template_id === template.id)
  emit('select', {
    type: 'template',
    template,
    entities: templateEntities
  })
}

const selectEntity = (entity, template) => {
  // 如果是搜索结果，使用实体自带的模板信息
  const actualTemplate = template.isSearchResult ? entity.templateInfo : template

  emit('select', {
    type: 'entity',
    entity,
    template: actualTemplate
  })
}

const selectChapter = (chapter, volume) => {
  emit('select', {
    type: 'chapter',
    chapter,
    volume
  })
}

const selectScenePool = (pool) => {
  emit('select', {
    type: 'scenePool',
    pool
  })
}

const handleSearch = (value) => {
  localSearchQuery.value = value
  // 重置选中索引
  selectedIndex.value = -1
}

// 获取总章节数
const getTotalChapterCount = () => {
  return filteredVolumes.value.reduce((total, volume) => {
    return total + (volume.chapters?.length || 0)
  }, 0)
}

// 获取章节在全局选择列表中的索引
const getChapterGlobalIndex = (volume, index) => {
  if (mode.value !== 'chapters') return index

  let globalIndex = 0

  for (let i = 0; i < filteredVolumes.value.length; i++) {
    const vol = filteredVolumes.value[i]
    if (vol.id === volume.id) {
      return globalIndex + index
    }
    globalIndex += vol.chapters?.length || 0
  }

  return index
}

// 键盘导航处理
const handleKeydown = (event) => {
  const items = selectableItems.value
  if (items.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, items.length - 1)
      scrollToSelectedItem()
      break

    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1)
      scrollToSelectedItem()
      break

    case 'Enter':
      event.preventDefault()
      if (selectedIndex.value >= 0 && selectedIndex.value < items.length) {
        const selectedItem = items[selectedIndex.value]
        if (selectedItem.type === 'template') {
          selectTemplate(selectedItem.data)
        } else if (selectedItem.type === 'entity') {
          selectEntity(selectedItem.data, selectedItem.template)
        } else if (selectedItem.type === 'chapter') {
          selectChapter(selectedItem.data, selectedItem.volume)
        } else if (selectedItem.type === 'scenePool') {
          selectScenePool(selectedItem.data)
        }
      }
      break

    case 'Escape':
      event.preventDefault()
      emit('close')
      break
  }
}

// 滚动到选中的项目
const scrollToSelectedItem = () => {
  if (selectedIndex.value < 0) return

  nextTick(() => {
    const selectedElement = selectorRef.value?.querySelector(`[data-index="${selectedIndex.value}"]`)
    if (selectedElement) {
      selectedElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      })
    }
  })
}

// 获取实体在全局选择列表中的索引
const getEntityGlobalIndex = (template, entityIndex) => {
  if (mode.value === 'single-entity') {
    let globalIndex = 0
    const templatesWithEntities = filteredTemplatesWithEntities.value

    for (let i = 0; i < templatesWithEntities.length; i++) {
      if (templatesWithEntities[i].id === template.id) {
        return globalIndex + entityIndex
      }
      globalIndex += templatesWithEntities[i].entities.length
    }
  }
  return entityIndex
}

// 监听器
watch(() => props.visible, async (newVal) => {
  console.log('EntitySelector: visible 变化为:', newVal)
  console.log('EntitySelector: position:', props.position)

  if (newVal) {
    await loadTemplates()
    await loadEntities()
    await loadVolumes()
    await loadScenePools()
    mode.value = ''
    localSearchQuery.value = props.searchQuery
    selectedIndex.value = -1 // 重置选中索引

    nextTick(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus()
      }
      // 确保选择器能接收键盘事件
      if (selectorRef.value) {
        selectorRef.value.focus()
      }
    })
  }
})

watch(() => props.searchQuery, (newVal) => {
  localSearchQuery.value = newVal
})
</script>

<style lang="scss" scoped>
.entity-selector {
  position: fixed;
  z-index: 9999;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  width: 320px;
  height: 400px; /* 固定高度 */
  display: flex;
  flex-direction: column;

  .selector-header {
    padding: 12px;
    border-bottom: 1px solid var(--el-border-color-light);
    flex-shrink: 0; /* 头部不缩放 */
  }

  .selector-content {
    flex: 1; /* 占据剩余空间 */
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-lighter);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-darker);
      border-radius: 3px;

      &:hover {
        background: var(--el-border-color-extra-dark);
      }
    }
    
    .main-options {
      padding: 8px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);
      
      .main-option {
        font-weight: 500;
        color: var(--el-color-primary);
        
        &:hover {
          background: var(--el-color-primary-light-9);
        }
        
        &.active {
          background: var(--el-color-primary-light-8);
        }
      }
    }
    
    .section-title {
      padding: 8px 16px;
      font-size: 12px;
      font-weight: 600;
      color: var(--el-text-color-secondary);
      background: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-lighter);
    }
    
    .option-item {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: var(--el-fill-color-light);
      }

      &.selected {
        background: var(--el-color-primary-light-9);
        border-left: 3px solid var(--el-color-primary);
        padding-left: 13px;
      }

      .el-icon {
        margin-right: 8px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .template-item {
      .template-name {
        flex: 1;
        font-weight: 500;
      }
      
      .entity-count {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .template-group {
      .template-header {
        display: flex;
        align-items: center;
        padding: 6px 16px;
        background: var(--el-fill-color-lighter);
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-regular);
        
        .el-icon {
          margin-right: 6px;
        }
        
        .count {
          margin-left: auto;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .entity-item {
        padding-left: 32px;

        .entity-info {
          flex: 1;
          min-width: 0;
        }

        .entity-name-line {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 2px;
        }

        .entity-name {
          font-weight: 500;
          flex-shrink: 0;
        }

        .template-badge {
          background: var(--el-color-primary-light-8);
          color: var(--el-color-primary);
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 10px;
          font-weight: 500;
          flex-shrink: 0;
        }

        .entity-desc {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: block;
        }
      }
    }

    // 章节列表样式
    .chapters-list {
      .volume-group {
        margin-bottom: 8px;

        .volume-header {
          display: flex;
          align-items: center;
          padding: 4px 16px;
          background: var(--el-fill-color-light);
          font-size: 12px;
          font-weight: 500;
          color: var(--el-text-color-regular);

          .el-icon {
            margin-right: 6px;
          }

          .count {
            margin-left: auto;
            font-size: 11px;
            color: var(--el-text-color-secondary);
          }
        }

        .chapter-item {
          padding-left: 32px;

          .chapter-info {
            flex: 1;
            min-width: 0;
          }

          .chapter-name-line {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 2px;
          }

          .chapter-name {
            font-weight: 500;
            flex-shrink: 0;
          }

          .chapter-order {
            background: var(--el-color-success-light-8);
            color: var(--el-color-success);
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
            flex-shrink: 0;
          }

          .chapter-desc {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
          }
        }
      }
    }

    // 场景卡池列表样式
    .scene-pools-list {
      .scene-pool-item {
        .pool-info {
          flex: 1;
          min-width: 0;
        }

        .pool-name-line {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 2px;
        }

        .pool-name {
          font-weight: 500;
          flex-shrink: 0;
        }

        .scene-count {
          background: var(--el-color-warning-light-8);
          color: var(--el-color-warning);
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 10px;
          font-weight: 500;
          flex-shrink: 0;
        }

        .pool-desc {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: block;
        }
      }
    }
  }
}
</style>
