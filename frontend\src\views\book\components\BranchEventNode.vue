<template>
  <div 
    class="branch-event-node"
    :class="{ 
      'selected': selected, 
      'left-side': isLeftSide, 
      'right-side': !isLeftSide 
    }"
    :style="nodeStyle"
  >
    <!-- 左侧连接点 -->
    <Handle 
      id="left" 
      type="source" 
      :position="Position.Left" 
      class="branch-handle"
      :style="{ background: data.color }"
    />
    
    <!-- 节点内容 -->
    <div class="branch-event-content">
      <div class="event-date" v-if="formattedDate">{{ formattedDate }}</div>
      <div class="event-title">{{ data.label }}</div>
      <div class="event-desc" v-if="data.content">{{ data.content }}</div>
    </div>
    
    <!-- 右侧连接点 -->
    <Handle 
      id="right" 
      type="source" 
      :position="Position.Right" 
      class="branch-handle"
      :style="{ background: data.color }"
    />

    <!-- 顶部连接点 -->
    <Handle 
      id="top" 
      type="source" 
      :position="Position.Top" 
      class="branch-handle"
      :style="{ background: data.color }"
    />
    
    <!-- 底部连接点 -->
    <Handle 
      id="bottom" 
      type="source" 
      :position="Position.Bottom" 
      class="branch-handle"
      :style="{ background: data.color }"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Handle, Position } from '@vue-flow/core';

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  data: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
});

// 格式化日期
const formattedDate = computed(() => {
  // 如果使用自定义时间，直接返回
  if (props.data.useCustomTime && props.data.customTime) {
    return props.data.customTime;
  }
  
  // 否则使用标准年月日格式
  const { year, month, day } = props.data;
  if (!year) return '';
  return `${year}/${month || ''}/${day || ''}`;
});

// 判断是左侧还是右侧分支
const isLeftSide = computed(() => {
  return props.data.isLeftSide !== undefined 
    ? props.data.isLeftSide 
    : false;
});

// 节点样式
const nodeStyle = computed(() => {
  return {
    borderColor: props.data.color || '#e84393',
    backgroundColor: `${props.data.color || '#e84393'}22`
  };
});
</script>

<style scoped>
/* 分支事件节点样式 */
.branch-event-node {
  padding: 8px 12px;
  border-radius: 8px;
  min-width: 150px;
  border: 2px solid;
  font-size: 13px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  color: var(--el-text-color-primary);
  position: relative;
  z-index: 1;
}

.branch-event-node.selected {
  box-shadow: 0 0 0 2px currentColor;
}

.branch-handle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  z-index: 2;
}

.branch-event-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 事件内容样式 */
.event-title {
  font-weight: bold;
  font-size: 14px;
  text-align: center;
}

.event-date {
  font-size: 12px;
  opacity: 0.8;
  text-align: center;
}

.event-desc {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 4px;
  max-height: none;
  white-space: pre-line;
  word-break: break-word;
  overflow-wrap: break-word;
}
</style> 