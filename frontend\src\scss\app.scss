// 添加以下样式
.form-actions {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }
  
  .diff-dialog-content {
    height: calc(100vh - 200px);
    min-height: 400px;
  }
  
  /* 设置应用容器样式 */
  .app-settings {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 12px;
    background-color: var(--el-bg-color-overlay);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    user-select: none; /* 整体界面不可选择 */
  }
  
  .settings-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  /* 标签页样式 */
  .settings-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
  
    :deep(.el-tabs__header) {
      margin: 0;
      padding: 12px 16px 0;
      border-bottom: 1px solid var(--el-border-color-light);
      flex-shrink: 0;
      background-color: var(--el-bg-color);
      
      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }
  
      .el-tabs__item {
        font-size: 14px;
        padding: 0 20px;
        height: 40px;
        line-height: 40px;
        transition: all 0.3s ease;
        color: var(--el-text-color-regular);
  
        &.is-active {
          font-weight: 600;
          color: var(--el-color-primary);
        }
  
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  
    :deep(.el-tabs__content) {
      flex: 1;
      overflow: hidden;
      padding: 0;
  
      .el-tab-pane {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }
    }
  }
  
  /* 设置面板样式 */
  .settings-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &.chrome-settings {
      .panel-content {
        display: flex;
        flex-direction: column;
        padding: 16px;
        overflow: hidden;
        height: 100%;

        > .settings-card:first-child {
          flex-shrink: 0;
        }

        > .section-header {
          flex-shrink: 0;
          margin-bottom: 16px;
        }

        > .settings-card.table-container {
          flex: 1;
          min-height: 0;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          :deep(.el-table) {
            height: 100%;
            display: flex;
            flex-direction: column;

            .el-table__header-wrapper {
              flex-shrink: 0;
            }

            .el-table__body-wrapper {
              flex: 1;
              overflow-y: auto;
              height: 0; // 强制滚动
            }
          }
        }
      }
    }
  }
  
  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    
    /* 优化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }
  
    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color);
      border-radius: 3px;
    }
  
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
  
  /* 标题栏样式 */
  .section-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color-overlay);
    margin-bottom: 16px;
    border-radius: 8px 8px 0 0;
    user-select: none; /* 标题栏不可选择 */
  
    &.compact {
      margin-bottom: 8px;
    }
  
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  
    .header-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap; /* 允许按钮换行 */
    }
  }
  
  /* 内容卡片样式 */
  .settings-card {
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid var(--el-border-color-light);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
    &.table-container {
      padding: 0;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      :deep(.el-table) {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .el-table__header-wrapper {
          flex-shrink: 0;
        }
        
        .el-table__body-wrapper {
          flex: 1;
          overflow-y: auto;
        }
      }
    }
  }
  
  /* 输入框组样式 */
  .path-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
  
    .el-input {
      flex: 1;
    }
  }
  
  /* 表格样式 */
  :deep(.el-table) {
    --el-table-border-color: var(--el-border-color-light);
    --el-table-header-bg-color: var(--el-bg-color);
    --el-table-row-hover-bg-color: var(--el-color-primary-light-9);
    
    font-size: 14px;
    border-radius: 10px;
    overflow: hidden;
  
    .el-table__header-wrapper {
      th {
        background: linear-gradient(to right, var(--el-color-primary-light-9), var(--el-bg-color-overlay));
        font-weight: 600;
        color: var(--el-text-color-primary);
        padding: 12px 8px;
        font-size: 15px;
        user-select: none; /* 表格头部不可选择 */
      }
    }
  
    .el-table__body-wrapper {
      tr {
        background: var(--el-bg-color-overlay);
        transition: all 0.3s ease;
  
        &:hover > td {
          background: var(--el-color-primary-light-9) !important;
        }
  
        td {
          border-bottom: 1px solid var(--el-border-color-lighter);
          padding: 12px 8px;
          font-size: 14px;
          color: var(--el-text-color-primary);
        }
      }
    }
  }
  
  /* 按钮样式 */
  :deep(.el-button) {
    --el-button-hover-bg-color: var(--el-color-primary-light-3);
    --el-button-hover-border-color: var(--el-color-primary-light-3);
    
    display: inline-flex;
    align-items: center;
    gap: 6px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 14px;
    padding: 10px 16px;
    user-select: none; /* 按钮文字不可选择 */
  
    &:not(.is-text) {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 14px rgba(var(--el-color-primary-rgb), 0.25);
      }
    }
  
    .el-icon {
      margin: 0;
      font-size: 16px;
    }
  }
  
  /* 特别美化添加模型按钮 */
  button[type="primary"]:has(.el-icon > .Plus),
  button[type="success"]:has(.el-icon > .Refresh) {
    background: linear-gradient(to right, var(--el-color-primary), var(--el-color-primary-light-3));
    padding: 10px 18px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: none;
    
    &:hover {
      background: linear-gradient(to right, var(--el-color-primary-dark-2), var(--el-color-primary));
      transform: translateY(-2px) scale(1.02);
    }
    
    .el-icon {
      font-size: 16px;
    }
  }
  
  /* 优化按钮组布局 */
  .subsection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: var(--el-text-color-primary);
      display: flex;
      align-items: center;
      
      &:before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 18px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 10px;
      }
    }
    
    div {
      display: flex;
      gap: 8px;
      
      .el-button {
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-2px);
        }
      }
    }
  }
  /* AI模型服务设置样式增强 */
  .provider-list {
    overflow-y: visible;
    padding: 0;
    max-height: none;

    :deep(.el-collapse-item) {
      margin-bottom: 16px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      border: 1px solid var(--el-border-color-light);
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .el-collapse-item__header {
        background: linear-gradient(to right, var(--el-bg-color), var(--el-bg-color-overlay));
        height: 56px;
        line-height: 56px;
        padding: 0 16px;
        border-bottom: 1px solid var(--el-border-color-light);
        font-weight: 600;
        transition: all 0.3s ease;
        
        &:hover {
          background: linear-gradient(to right, var(--el-color-primary-light-9), var(--el-bg-color-overlay));
        }
        
        &.is-active {
          background: linear-gradient(to right, var(--el-color-primary-light-8), var(--el-color-primary-light-9));
          
          .el-collapse-item__arrow {
            color: var(--el-color-primary);
          }
        }
        
        .el-collapse-item__arrow {
          margin-right: 0;
          font-size: 16px;
          transition: transform 0.3s ease;
        }
      }
      
      .el-collapse-item__wrap {
        background-color: var(--el-bg-color);
        overflow: visible;
      }

      .el-collapse-item__content {
        padding: 0;
        overflow: visible;
      }
    }
  }

  .provider-header {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    
    .provider-name {
      font-weight: 600;
      font-size: 18px;
      color: var(--el-text-color-primary);
      flex: 1;
      
      /* 渐变效果的服务商名称 */
      background: linear-gradient(to right, var(--el-color-primary), var(--el-color-success));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
    }
  }

  .provider-content {
    padding: 20px;
    background: var(--el-bg-color);
    border-radius: 0 0 8px 8px;
    
    .el-form-item {
      margin-bottom: 22px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .el-form-item__label {
        font-size: 15px !important;
        font-weight: 500 !important;
        padding-right: 12px;
        color: var(--el-text-color-primary);
      }
    }
    
    .el-input, .el-select {
      .el-input__wrapper {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05), 0 0 0 1px var(--el-border-color-light) inset;
        transition: all 0.3s;
        border-radius: 8px;
        padding: 0 15px;
        
        &:hover, &:focus, &.is-focus {
          box-shadow: 0 3px 12px rgba(var(--el-color-primary-rgb), 0.15), 0 0 0 1px var(--el-color-primary-light-5) inset;
        }
      }
    }
  }

  /* 为subsection-header添加更现代的样式 */
  .subsection {
    margin-top: 24px;
    border-top: 1px solid var(--el-border-color-light);
    padding-top: 20px;
    position: relative;
    
    &:before {
      content: '';
      position: absolute;
      top: -1px;
      left: 0;
      width: 60px;
      height: 3px;
      background: linear-gradient(to right, var(--el-color-primary), var(--el-color-success));
      border-radius: 3px;
    }
  }

  .subsection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: var(--el-text-color-primary);
      display: flex;
      align-items: center;
      
      &:before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 18px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 10px;
      }
    }
    
    .el-button {
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  /* 美化模型列表和API密钥列表 */
  .models-list, .keys-list {
    margin-top: 20px;
    
    :deep(.el-table) {
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
      
      .el-table__header-wrapper {
        th {
          background: linear-gradient(to right, var(--el-color-primary-light-9), var(--el-bg-color-overlay));
          font-weight: 600;
          color: var(--el-text-color-primary);
          padding: 12px 0;
        }
      }
      
      .el-table__body-wrapper {
        tr {
          transition: all 0.2s ease;
          
          &:hover > td {
            background-color: var(--el-color-primary-light-9) !important;
          }
          
          td {
            padding: 12px 0;
          }
        }
        
        /* 渐变行背景 */
        .el-table__row:nth-child(even) {
          background-color: rgba(var(--el-color-primary-rgb), 0.02);
        }
      }
    }
    
    /* 表格中的输入框和控件 */
    :deep(.el-input__wrapper), :deep(.el-input-number__wrapper) {
      box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03), 0 0 0 1px var(--el-border-color-lighter) inset !important;
      
      &:hover, &:focus, &.is-focus {
        box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.1), 0 0 0 1px var(--el-color-primary-light-5) inset !important;
      }
    }
  }

  /* 空状态美化 */
  .empty-keys, .empty-models, .empty-providers {
    padding: 32px;
    text-align: center;
    background: rgba(var(--el-bg-color-rgb), 0.5);
    border-radius: 12px;
    margin: 16px 0;
    border: 1px dashed var(--el-border-color);
    transition: all 0.3s;
    user-select: none; /* 空状态提示不可选择 */
    
    &:hover {
      border-color: var(--el-color-primary-light-5);
      background: rgba(var(--el-color-primary-rgb), 0.03);
    }
    
    :deep(.el-empty__image) {
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.08));
      width: 80px !important;
      height: auto !important;
    }
    
    :deep(.el-empty__description) {
      margin-top: 16px;
      color: var(--el-text-color-secondary);
      font-size: 15px;
    }
    
    .el-button {
      margin-top: 16px;
      padding-left: 24px;
      padding-right: 24px;
      font-size: 14px;
    }
  }

  /* 强化底部操作按钮 */
  .provider-actions {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px dashed var(--el-border-color);
    padding-top: 20px;
    
    .el-button {
      padding: 12px 24px;
      font-weight: 500;
      
      &.el-button--primary {
        background: linear-gradient(to right, var(--el-color-primary), var(--el-color-primary-light-3));
        transition: all 0.3s;
        
        &:hover {
          background: linear-gradient(to right, var(--el-color-primary-dark-2), var(--el-color-primary));
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
        }
      }
    }
  }

  /* 美化Providers-info区域 */
  .providers-info {
    margin: 10px 0 25px;
    background: linear-gradient(to right, var(--el-color-info-light-9), var(--el-bg-color));
    border-radius: 10px;
    padding: 16px;
    border-left: 4px solid var(--el-color-info);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    
    &:before {
      content: '';
      position: absolute;
      top: -30px;
      right: -30px;
      width: 100px;
      height: 100px;
      background: radial-gradient(var(--el-color-info-light-8) 0%, transparent 70%);
      opacity: 0.6;
      border-radius: 50%;
      z-index: 0;
    }
    
    .el-alert {
      background-color: transparent !important;
      padding: 0;
      
      .el-alert__title {
        font-size: 15px;
        font-weight: 600;
      }
      
      .el-alert__icon {
        font-size: 18px;
        color: var(--el-color-info);
      }
    }
  }

  .providers-info-text {
    font-size: 13px;
    color: var(--el-text-color-primary);
    padding-top: 10px;
    line-height: 1.5;
    margin-left: 26px;
    position: relative;
    z-index: 1;
  }

  /* 添加深色模式适配 */
  .dark {
    .provider-list :deep(.el-collapse-item) {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      border-color: rgba(255, 255, 255, 0.1);
      
      .el-collapse-item__header {
        background: linear-gradient(to right, rgba(48, 48, 50, 0.8), rgba(42, 42, 45, 0.9));
        
        &:hover {
          background: linear-gradient(to right, rgba(var(--el-color-primary-rgb), 0.2), rgba(42, 42, 45, 0.9));
        }
        
        &.is-active {
          background: linear-gradient(to right, rgba(var(--el-color-primary-rgb), 0.3), rgba(var(--el-color-primary-rgb), 0.1));
        }
      }
    }
    
    .providers-info {
      background: linear-gradient(to right, rgba(var(--el-color-info-rgb), 0.1), rgba(36, 36, 38, 0.8));
      border-color: var(--el-color-info);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .empty-keys, .empty-models, .empty-providers {
      background: rgba(36, 36, 38, 0.6);
      border-color: rgba(255, 255, 255, 0.08);
      
      &:hover {
        border-color: rgba(var(--el-color-primary-rgb), 0.4);
        background: rgba(var(--el-color-primary-rgb), 0.08);
      }
    }
  }

  /* 添加模型服务相关的动画效果 */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .provider-content {
    animation: fadeIn 0.3s ease-out;
  }

  /* 按钮悬停特效 */
  :deep(.el-button) {
    &:not(.is-text):not(.is-link):not(.is-disabled) {
      &:hover {
        box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.2);
      }
    }
  }
  
  /* 备份历史区域 */
  .backup-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 16px;
    overflow: hidden; /* 防止溢出 */
  }
  
  .backup-settings {
    flex-shrink: 0;
  }
  
  .backup-history {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 重要：允许容器收缩 */
    overflow: hidden; /* 防止溢出 */
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    user-select: none; /* 卡片标题不可选择 */
    
    h3 {
      margin: 0;
      font-size: 15px;
      font-weight: 600;
    }
  }
  
  /* 确保表格容器可以滚动 */
  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 200px;
    
    :deep(.el-table) {
      height: 100%;
    }
  }
  
  /* 增强Git备份页面样式 */
  .git-panel-layout {
    display: flex;
    gap: 20px;
    height: 100%;
    overflow: hidden;
  }
  
  .git-config-section {
    flex: 3;
    overflow-y: auto;
    padding-right: 16px;
    
    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
  
  .git-history-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-left: 1px solid var(--el-border-color-light);
    padding-left: 16px;
    
    .section-header {
      margin-bottom: 12px;
      border-radius: 8px;
      background-color: var(--el-bg-color);
    }
  }
  
  /* 历史记录列表容器 */
  .custom-timeline {
    flex: 1;
    overflow-y: auto;
    padding: 0 4px;
    
    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    /* 时间线项目样式 */
    :deep(.el-timeline-item) {
      padding-bottom: 20px;
      
      .el-timeline-item__node {
        background-color: var(--el-color-primary);
      }
      
      .el-timeline-item__tail {
        border-left: 2px solid var(--el-border-color-light);
      }
      
      .el-timeline-item__timestamp {
        color: var(--el-text-color-secondary);
        font-size: 12px;
      }
    }
  }
  
  /* 提交卡片样式 */
  .commit-card {
    background-color: var(--el-bg-color);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid var(--el-border-color-light);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 8px;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
      transition: all 0.3s;
    }
  }
  
  .commit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .commit-badge {
    background-color: var(--el-color-info-light-9);
    color: var(--el-color-info);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    user-select: none; /* 标签不可选择 */
  }
  
  .current-badge {
    background-color: var(--el-color-success-light-9);
    color: var(--el-color-success);
  }
  
  .commit-date {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    user-select: none; /* 日期不可选择 */
  }
  
  .commit-message {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 500;
  }
  
  .commit-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .hash-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .history-actions {
    display: flex;
    gap: 8px;
  }
  
  /* 空历史状态 */
  .empty-history {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
  }
  
  /* 历史记录加载中状态 */
  .history-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
  }
  
  /* 确保标签页内容区域可滚动 */
  .settings-section {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    /* 添加内容区域滚动 */
    > .panel-content, > div:not(.section-header) {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      
      /* 优化滚动条 */
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: var(--el-border-color);
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }
  }
  
  /* Dialog样式 */
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    
    .el-dialog__header {
      padding: 16px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      margin: 0;
      user-select: none; /* 对话框标题不可选择 */
    }
    
    .el-dialog__body {
      padding: 20px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid var(--el-border-color-light);
    }
  }
  
  /* 表单提示样式 */
  .form-item-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
    user-select: none; /* 提示文字不可选择 */
    
    &.warning-tip {
      color: var(--el-color-warning);
    }
  }
  
  /* 输入框带提示的布局 */
  .input-with-tip {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  /* 单位标签 */
  .unit-label {
    color: var(--el-text-color-secondary);
    margin-left: 8px;
    user-select: none; /* 单位标签不可选择 */
  }
  
  /* 底部标签切换样式 */
  .tab-footer {
    display: flex;
    border-top: 1px solid var(--el-border-color-light);
    padding: 12px;
    justify-content: center;
    gap: 16px;
    background-color: var(--el-bg-color);
    user-select: none; /* 标签页不可选择 */
  }
  
  .tab-item {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    color: var(--el-text-color-regular);
    user-select: none; /* 标签项不可选择 */
    
    &.active {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      font-weight: 500;
    }
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
  
  /* 响应式调整 */
  @media (max-width: 992px) {
    .git-panel-layout {
      flex-direction: column;
    }
    
    .git-config-section {
      flex: none;
      max-height: 50%;
      padding-right: 0;
      margin-bottom: 16px;
    }
    
    .git-history-section {
      flex: none;
      border-left: none;
      border-top: 1px solid var(--el-border-color-light);
      padding-left: 0;
      padding-top: 16px;
      max-height: 50%;
    }
    
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
      
      .header-actions {
        width: 100%;
        justify-content: flex-start;
      }
    }
    
    /* 改进按钮组在移动设备上的显示 */
    :deep(.el-button-group) {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      
      .el-button {
        margin-left: 0 !important;
        border-radius: 6px !important;
      }
    }
  }
  
  /* 小屏幕设备的额外调整 */
  @media (max-width: 768px) {
    .settings-tabs {
      :deep(.el-tabs__header) {
        padding: 8px 8px 0;
        
        .el-tabs__item {
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }
    
    .panel-content {
      padding: 12px;
    }
    
    .section-header {
      padding: 10px 12px;
      
      .section-title {
        font-size: 15px;
      }
      
      .header-actions {
        .el-button {
          padding: 8px 12px;
          font-size: 12px;
        }
      }
    }
    
    .settings-card {
      padding: 12px;
    }
    
    .path-input-group {
      flex-direction: column;
      align-items: stretch;
      
      .el-input {
        margin-bottom: 8px;
      }
    }
    
    /* 调整表格显示 */
    :deep(.el-table) {
      .el-table__header-wrapper,
      .el-table__body-wrapper {
        overflow-x: auto;
      }
    }
  }
  
  /* 深色模式调整 */
  .dark {
    .app-settings {
      background-color: rgba(30, 30, 32, 0.7);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }
    
    .settings-card {
      background: rgba(36, 36, 38, 0.7);
      border-color: rgba(255, 255, 255, 0.1);
    }
    
    .section-header {
      background: rgba(28, 28, 30, 0.8);
      border-color: rgba(255, 255, 255, 0.1);
    }
    
    .provider-content {
      background: rgba(36, 36, 38, 0.8);
    }
    
    :deep(.el-tabs__item) {
      color: var(--el-text-color-secondary);
      
      &.is-active {
        color: var(--el-color-primary);
      }
    }
  }


  
/* 添加到样式表最后 */
.providers-info {
  margin-bottom: 20px;
  background-color: var(--el-bg-color);
}

.providers-info .el-alert {
  width: 100%;
  background-color: var(--el-bg-color);
}

.providers-info-text {
  font-size: 12px;
  color: var(--el-text-color-primary);
  padding-top: 5px;
}

/* 确保OpenAI配置面板正确滚动 */
.el-tab-pane[data-v-tab-pane="openai-config"] {
  .panel-content {
    overflow-y: auto;
    height: 100%;
  }
}

/* 标签页标题 */
:deep(.el-tabs__item) {
  user-select: none; /* 标签页标题不可选择 */
}

/* 表格头部 */
:deep(.el-table__header-wrapper) {
  th {
    user-select: none; /* 表格头部不可选择 */
  }
}

/* 对话框标题 */
:deep(.el-dialog__header) {
  user-select: none; /* 对话框标题不可选择 */
}

/* 表单标签 */
:deep(.el-form-item__label) {
  user-select: none; /* 表单标签不可选择 */
}

/* 下拉菜单项 */
:deep(.el-dropdown-menu__item) {
  user-select: none; /* 下拉菜单项不可选择 */
}

/* 开关标签 */
:deep(.el-switch__label) {
  user-select: none; /* 开关标签不可选择 */
}

/* 单选框标签 */
:deep(.el-radio__label) {
  user-select: none; /* 单选框标签不可选择 */
}

/* 复选框标签 */
:deep(.el-checkbox__label) {
  user-select: none; /* 复选框标签不可选择 */
}

/* 模型参数配置对话框样式 */
.param-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .param-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    line-height: 1.4;
    margin-top: 4px;
  }
}

/* 滑块输入组合样式 */
.slider-with-input {
  display: flex;
  align-items: center;
  gap: 12px;

  .el-slider {
    flex: 1;
  }

  .unit-label {
    color: var(--el-text-color-secondary);
    font-size: 12px;
    white-space: nowrap;
  }
}