{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=32768 --max-semi-space-size=4096\" vite build", "build:script": "node build-with-memory.js", "build:normal": "vite build", "build:memory": "node --max-old-space-size=16384 ./node_modules/vite/bin/vite.js build", "preview": "vite preview", "fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit"}, "dependencies": {"@devui-design/icons": "^1.4.0", "@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.18", "@matechat/core": "^1.5.2", "@tinymce/tinymce-vue": "^6.1.0", "@tiptap/core": "^2.11.5", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-placeholder": "^2.10.3", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-task-item": "^2.11.5", "@tiptap/extension-task-list": "^2.11.5", "@tiptap/pm": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "@tiptap/vue-3": "^2.10.3", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.44.0", "@vue-flow/minimap": "^1.5.3", "@vueuse/core": "^10.11.1", "ant-design-x-vue": "^1.2.4", "codemirror": "^5.65.19", "crypto-js": "^4.2.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "diff2html": "^3.4.51", "dompurify": "^3.2.4", "echarts": "^5.6.0", "element-plus": "^2.9.1", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "katex": "^0.16.18", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "marked": "^15.0.7", "markmap-lib": "^0.18.12", "markmap-view": "^0.18.12", "nanoid": "^5.0.9", "pinia": "^2.3.0", "prismjs": "^1.30.0", "simple-mind-map": "0.14.0-fix.1", "terser": "^5.39.0", "tinymce": "^7.7.1", "uuid": "^11.0.3", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-script-merger": "^1.0.7", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "javascript-obfuscator": "^4.1.1", "rollup-plugin-obfuscator": "^1.1.0", "sass-embedded": "^1.83.0", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.11", "vite-plugin-javascript-obfuscator": "^3.1.0"}}