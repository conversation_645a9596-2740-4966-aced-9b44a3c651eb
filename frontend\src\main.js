import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import './theme.css'
import './styles/markdown-theme-override.css'
import './styles/markdown-theme-override.css'
import { useConfigStore } from './stores/config'
import { ChatDotRound, DocumentCopy } from '@element-plus/icons-vue'
import MateChat from '@matechat/core'

// Vue Markdown Editor
import VueMarkdownEditor from '@kangc/v-md-editor'
import VMdPreview from '@kangc/v-md-editor/lib/preview'
import VMdEditor from '@kangc/v-md-editor/lib/codemirror-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import '@kangc/v-md-editor/lib/style/preview.css'
import '@kangc/v-md-editor/lib/style/codemirror-editor.css'
// 主题
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'

// 语法高亮
import Prism from 'prismjs'
import hljs from 'highlight.js'

// CodeMirror
import Codemirror from 'codemirror'
import 'codemirror/mode/markdown/markdown'
import 'codemirror/mode/javascript/javascript'
import 'codemirror/mode/css/css'
import 'codemirror/mode/htmlmixed/htmlmixed'
import 'codemirror/mode/vue/vue'
import 'codemirror/addon/edit/closebrackets'
import 'codemirror/addon/edit/closetag'
import 'codemirror/addon/edit/matchbrackets'
import 'codemirror/addon/display/placeholder'
import 'codemirror/addon/selection/active-line'
import 'codemirror/addon/scroll/simplescrollbars'
import 'codemirror/addon/scroll/simplescrollbars.css'
import 'codemirror/lib/codemirror.css'

// KaTeX 数学公式支持
import createKatexPlugin from '@kangc/v-md-editor/lib/plugins/katex/npm';
import 'katex/dist/katex.css';


import createMermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/npm';
import '@kangc/v-md-editor/lib/plugins/mermaid/mermaid.css';
// 代码复制插件
import createCopyCodePlugin from '@kangc/v-md-editor/lib/plugins/copy-code/index';
import '@kangc/v-md-editor/lib/plugins/copy-code/copy-code.css';

// Mermaid 配置
const mermaidConfig = {
  startOnLoad: false, // 禁用自动启动，避免初始化时的错误
  theme: 'default',
  themeVariables: {
    primaryColor: '#409EFF',
    primaryTextColor: '#fff',
    primaryBorderColor: '#409EFF',
    lineColor: '#409EFF'
  },
  flowchart: {
    useMaxWidth: true,
    htmlLabels: true
  },
  sequence: {
    useMaxWidth: true
  },
  gantt: {
    useMaxWidth: true
  },
  // 添加错误处理配置
  errorLevel: 'warn', // 将错误降级为警告
  suppressErrorRendering: true, // 抑制错误渲染
  // 安全渲染配置
  secure: ['secure', 'securityLevel', 'startOnLoad', 'maxTextSize'],
  securityLevel: 'loose'
};

// 安全的 Mermaid 插件创建函数
const createSafeMermaidPlugin = () => {
  try {
    return createMermaidPlugin(mermaidConfig)
  } catch (error) {
    console.warn('创建 Mermaid 插件失败:', error)
    // 返回一个空的插件对象，避免整个编辑器初始化失败
    return {
      install() {
        console.warn('Mermaid 插件未能正确安装')
      }
    }
  }
}

// 配置 Vue Markdown Editor
// 基础编辑器使用 vuepress 主题
VueMarkdownEditor.use(vuepressTheme, { Prism })
VueMarkdownEditor.use(createKatexPlugin());
VueMarkdownEditor.use(createCopyCodePlugin());
VueMarkdownEditor.use(createSafeMermaidPlugin());
// 预览组件使用 vuepress 主题
VMdPreview.use(vuepressTheme, { Prism })
VMdPreview.use(createKatexPlugin());
VMdPreview.use(createCopyCodePlugin());
VMdPreview.use(createSafeMermaidPlugin())

// CodeMirror 编辑器使用 github 主题和 hljs
VMdEditor.Codemirror = Codemirror
VMdEditor.use(githubTheme, { Hljs: hljs })
VMdEditor.use(createKatexPlugin())
VMdEditor.use(createCopyCodePlugin())
VMdEditor.use(createSafeMermaidPlugin())



const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000
})

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(router)
app.use(MateChat)

// 注册 Vue Markdown Editor 组件
app.use(VueMarkdownEditor)
app.use(VMdEditor)
app.use(VMdPreview)



// 初始化自动备份
async function initializeAutoBackup() {
  try {
      await window.pywebview.api.check_auto_backup()
  } catch (error) {
    console.error('初始化自动备份出错:', error)
  }
}

// 等待pywebview API加载完成的函数
function waitForPywebviewAPI() {
  return new Promise((resolve) => {
    // 如果API已经加载，直接返回
    if (window.pywebview && window.pywebview.hasOwnProperty('api')) {
      resolve()
      return
    }
    
    // 创建一个计时器反复检查API是否就绪
    const intervalId = setInterval(() => {
      console.log("浏览器初始化中，等待pywebview API...")
      if (window.pywebview && window.pywebview.hasOwnProperty('api')) {
        clearInterval(intervalId) // 清除计时器
        resolve() // 继续执行
      }
    }, 500) // 每500毫秒检查一次
  })
}

// 应用主题的函数
const applyTheme = (theme) => {
  document.querySelector('html').className = theme
  console.log('应用主题:', theme)
}

// 预加载主题配置
const preloadTheme = async () => {
  try {
    // 先应用默认主题，避免白屏
    applyTheme('light')

    // 尝试从 pywebview 获取主题配置
    if (window.pywebview && window.pywebview.api) {
      const result = await window.pywebview.api.get_settings()
      const config = typeof result === 'string' ? JSON.parse(result) : result

      if (config && config.theme) {
        applyTheme(config.theme)
        console.log('预加载主题成功:', config.theme)
      }
    }
  } catch (error) {
    console.warn('预加载主题失败，使用默认主题:', error)
    applyTheme('light')
  }
}

// 初始化应用
const initializeApp = async () => {
  // 在挂载应用之前预加载主题
  await preloadTheme()

  // 挂载应用
  app.mount('#app')

  // 等待 DOM 完全渲染
  await new Promise(resolve => {
    if (document.readyState === 'complete') {
      resolve()
    } else {
      window.addEventListener('load', resolve, { once: true })
    }
  })

  // 延迟初始化 Mermaid，避免第一次加载时的错误
  setTimeout(() => {
    try {
      if (window.mermaid) {
        window.mermaid.initialize({
          ...mermaidConfig,
          startOnLoad: true // 现在可以安全地启动
        })
        console.log('Mermaid 初始化完成')
      }
    } catch (error) {
      console.warn('Mermaid 初始化失败:', error)
    }
  }, 1000) // 延迟1秒初始化

  // 加载配置
  const configStore = useConfigStore()
  try {
    await configStore.loadConfig()
    console.log('配置加载完成')

    // 配置加载完成后再次应用主题，确保使用最新的主题设置
    applyTheme(configStore.theme)

    // 检查是否需要初始化自动备份
    if (configStore.backup?.autoBackup) {
      await initializeAutoBackup()
      console.log('自动备份已初始化')
    }
  } catch (error) {
    console.error('配置加载失败:', error)
  }
}

// 启动应用
async function startApp() {
  await waitForPywebviewAPI()
  initializeApp()
}

startApp()
