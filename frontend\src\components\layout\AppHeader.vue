<template>
  <div class="head">
    <el-header class="header" :class="{ 'dark': isDarkTheme }" @dblclick="maximizeApp">
      <div style="flex: 1; display: flex;">
        <div class="left">
          <img src="@/assets/logo.png" alt="Logo" class="logo" draggable="false" />
          <h1 class="title">PVV</h1>
        </div>
        

      </div>
      
      <div class="right">
        <!-- 修改用户菜单，添加阻止事件冒泡和调整触发方式 -->
        <el-dropdown v-if="userStore.isLoggedIn" trigger="click" @command="handleUserCommand" teleported @visible-change="handleDropdownVisibility">
          <span class="user-menu-trigger" @click.stop>
            <el-icon><UserFilled /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="activation">激活信息</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        
        <el-button class="action-btn" @click="minimizeApp">
          <el-icon><Minus /></el-icon>
        </el-button>

        <el-button class="action-btn" @click="maximizeApp">
          <el-icon><FullScreen /></el-icon>
        </el-button>

        <el-button class="action-btn" @click="closeApp">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </el-header>
    
    <!-- 激活信息对话框 -->
    <el-dialog
      v-model="showActivationInfo"
      title="激活状态"
      width="450px"
      center
      align-center
      class="activation-dialog"
      :close-on-click-modal="false"
      top="20vh"
      teleported
    >
      <div class="activation-dialog-content">
        <div class="activation-info-item">
          <div class="info-label">激活状态:</div>
          <div class="info-value" :class="{ 'status-active': userStore.isLoggedIn, 'status-inactive': !userStore.isLoggedIn }">
            {{ userStore.isLoggedIn ? '已激活' : '未激活' }}
          </div>
        </div>
        
        <div class="activation-info-item">
          <div class="info-label">到期日期:</div>
          <div class="info-value">{{ userStore.formattedExpiryDate }}</div>
        </div>
        
        <div class="activation-info-item">
          <div class="info-label">剩余时间:</div>
          <div class="info-value">{{ userStore.formattedTimeRemaining }}</div>
        </div>
        

      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showActivationInfo = false">关闭</el-button>
          <el-button type="primary" @click="refreshActivation">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, computed, ref, onBeforeUnmount } from "vue";
import { enableDragging } from "@/utils/draggable.js"
import { useConfigStore } from '@/stores/config'
import { useUserStore } from '@/stores/user';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { UserFilled, WarningFilled, Refresh } from '@element-plus/icons-vue';

const configStore = useConfigStore()
const isDarkTheme = computed(() => configStore.theme === 'dark')

const userStore = useUserStore();
const router = useRouter();
const showActivationInfo = ref(false);
const dropdownVisible = ref(false);  // 新增状态跟踪下拉菜单是否显示

const closeApp = () => {
  window.pywebview.api.destroy()
}

onMounted(() => {
  // 使用JavaScript方式控制左侧拖拽
  enableDragging(".header")
})

onMounted(async () => {
  try {
    
    // 启动激活码检查定时器


  } catch (error) {
    console.error('验证会话失败:', error);
  }
});

const maximizeApp = () => {
  // 防止在下拉菜单打开时触发
  if (!dropdownVisible.value) {
    window.pywebview.api.toggle_fullscreen()
  }
}

const minimizeApp = () => {
  window.pywebview.api.minimize()
}

// 处理下拉菜单可见性变化
const handleDropdownVisibility = (visible) => {
  dropdownVisible.value = visible;
  console.log('下拉菜单状态:', visible ? '打开' : '关闭');
}

const handleUserCommand = (command) => {
  if (command === 'logout') {
    handleLogout();
  } else if (command === 'activation') {
    showActivationInfo.value = true;
  }
}

const refreshActivation = async () => {
  try {
    await userStore.checkActivation();
    ElMessage.success('激活状态已刷新');
  } catch (error) {
    ElMessage.error('刷新激活状态失败');
  }
}

const handleLogout = async () => {
  try {
    await userStore.logout(true) // 传入 true 表示清除设置
    router.push('/login')
    ElMessage.success('已成功登出')
  } catch (error) {
    ElMessage.error('登出失败: ' + error.message)
  }
}

// 确保formattedTimeRemaining计算属性正确
console.log('剩余时间:', userStore.daysRemaining, userStore.hoursRemaining, userStore.minutesRemaining);
console.log('格式化后:', userStore.formattedTimeRemaining);
</script>

<style lang="scss" scoped>
.header {
  height: 60px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  user-select: none;
  transition: all 0.3s ease;
  z-index: 100;
}

.header.dark {
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.left {
  display: flex;
  align-items: center;
  gap: 12px;
  -webkit-app-region: drag;
}

.logo {
  height: 32px;
  width: 32px;
  -webkit-user-select: none;
  user-select: none;
  pointer-events: none;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
  user-select: none;
  pointer-events: none;
}

/* 激活信息样式 */
.activation-info {
  display: flex;
  align-items: center;
  margin-left: 24px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.activation-time {
  padding: 2px 8px;
  border-radius: 12px;
  background-color: rgba(var(--el-color-success-rgb), 0.1);
  color: var(--el-color-success);
  transition: all 0.3s;
}

.activation-time.expiring-soon {
  background-color: rgba(var(--el-color-warning-rgb), 0.1);
  color: var(--el-color-warning);
}

.right {
  display: flex;
  align-items: center;
  gap: 8px;
  -webkit-app-region: no-drag;
  position: relative; /* 确保子元素定位正确 */
  z-index: 101; /* 确保菜单在顶层 */
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
  color: var(--el-color-primary);
  margin-right: 8px;
  z-index: 101; /* 确保在更高层级 */
}

.user-menu-trigger:hover {
  background-color: rgba(var(--el-color-primary-rgb), 0.2);
}

.action-btn {
  padding: 8px;
  height: 32px;
  color: var(--el-text-color-regular);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  color: var(--el-text-color-primary);
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
}

.dark .action-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 激活对话框样式 */
.activation-dialog {
  :deep(.el-dialog__header) {
    padding: 20px;
    margin-right: 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  :deep(.el-dialog__title) {
    font-weight: 600;
    font-size: 18px;
    color: var(--el-text-color-primary);
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

.activation-dialog-content {
  padding: 8px;
}

.activation-info-item {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
}

.info-label {
  flex: 0 0 100px;
  font-weight: 600;
  color: var(--el-text-color-secondary);
}

.info-value {
  flex: 1;
  color: var(--el-text-color-primary);
}

.status-active {
  color: var(--el-color-success);
  font-weight: 600;
}

.status-inactive {
  color: var(--el-color-danger);
  font-weight: 600;
}

.expiring-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 4px;
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning-dark-2);
  border-left: 4px solid var(--el-color-warning);

  .el-icon {
    font-size: 18px;
    color: var(--el-color-warning);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}
</style>