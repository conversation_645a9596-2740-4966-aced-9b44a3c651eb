<template>
  <el-config-provider :locale="locale">
    <router-view v-if="initialized" />
    <div v-else class="loading-container">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
  </el-config-provider>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useConfigStore } from '@/stores/config'
import { Loading } from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const configStore = useConfigStore()
const router = useRouter()
const locale = ref(zhCn)
const initialized = ref(false)

// 应用主题
const applyTheme = (theme) => {
  document.querySelector('html').className = theme
}

// 监听主题变化 - 修复版本
const setupThemeWatcher = () => {
  // 初始设置主题
  applyTheme(configStore.theme)
  
  // 监听主题变化 - 添加安全检查
  const unwatch = configStore.$subscribe((mutation, state) => {
    if (mutation.payload && 'theme' in mutation.payload) {
      applyTheme(state.theme)
    }
  })
  
  return unwatch
}

// 清理函数
let cleanupNetworkListeners = null

// 初始化应用
const initApp = async () => {
  try {
    // 加载配置
    await configStore.loadConfig()

    // 应用主题（如果 main.js 中已经应用过，这里会确保使用最新的配置）
    applyTheme(configStore.theme)

    // 设置网络监听
    cleanupNetworkListeners = userStore.setupNetworkListeners()

    // 先设置初始化完成，让界面显示出来
    initialized.value = true

    // 然后在后台进行自动登录（不阻塞界面显示）
    userStore.autoLogin().then(() => {
      // 自动登录完成后，根据登录状态决定跳转
      if (userStore.isLoggedIn && router.currentRoute.value.path === '/login') {
        router.push('/')
      } else if (!userStore.isLoggedIn && router.currentRoute.value.path !== '/login' &&
                router.currentRoute.value.meta.requiresAuth) {
        router.push({ name: 'login' })
      }

      // 只在登录成功后启动激活检查定时器
      if (userStore.isLoggedIn) {
        console.log('自动登录成功，启动激活状态检查定时器')
        userStore.startActivationTimer()
      }
    }).catch(error => {
      console.error('自动登录失败:', error)
    })

    // 全局初始化AI提供商配置（在后台进行）
    console.log('App.vue: 开始全局初始化AI配置...')
    try {
      // 加载AI提供商配置（模型列表将从这里获取）
      const { useAIProvidersStore } = await import('@/stores/aiProviders')
      const aiProvidersStore = useAIProvidersStore()

      if (!aiProvidersStore.initialized) {
        console.log('App.vue: 加载AI提供商配置...')
        await aiProvidersStore.loadProviders()
        console.log('App.vue: AI提供商配置加载完成:', aiProvidersStore.providers.length, '个提供商')
        console.log('App.vue: 可用模型数量:', aiProvidersStore.allAvailableModels.length)
      }

      // 加载AI角色
      const { useAIRolesStore } = await import('@/stores/aiRoles')
      const aiRolesStore = useAIRolesStore()
      if (!aiRolesStore.roles.length) {
        console.log('App.vue: 加载AI角色...')
        await aiRolesStore.loadRoles()
        console.log('App.vue: AI角色加载完成:', aiRolesStore.roles.length, '个角色')
      }

      console.log('App.vue: AI配置全局初始化完成')
    } catch (aiError) {
      console.error('App.vue: AI配置初始化失败:', aiError)
      // 不阻止应用启动，但记录错误
    }
  } catch (error) {
    console.error('应用初始化失败:', error)
    initialized.value = true
  }
}

onMounted(async () => {
  initApp()
  const cleanup = setupThemeWatcher()
  
  // 清理函数
  onBeforeUnmount(() => {
    cleanup()
    // 清理激活检查定时器（如果存在）
    if (userStore.activationTimer) {
      userStore.startActivationTimer()() // 调用清理函数
    }
  })
})

onUnmounted(() => {
  // 清理网络监听器
  if (cleanupNetworkListeners) {
    cleanupNetworkListeners()
  }
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  overflow: hidden; /* 防止body出现滚动条 */
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden; /* 防止应用外层出现滚动条 */
}

/* 允许内部容器滚动的通用类 */
.scroll-container {
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

/* 定义基础布局容器样式 */
.el-container {
  height: 100%;
  width: 100%;
}

.el-main {
  padding: 0;
  height: 100%;
  overflow: hidden; /* 默认隐藏滚动条，由内部组件控制 */
}

/* 确保header和footer不会滚动 */
.el-header, .el-footer {
  flex-shrink: 0;
}

/* 确保侧边栏不会水平滚动 */
.el-aside {
  overflow-x: hidden;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: var(--el-border-color-light);
  border-radius: 3px;
}

/* 悬停时的滚动条样式 */
::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-extra-dark);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.loading-icon {
  font-size: 48px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>