import * as monaco from 'monaco-editor'

// Monaco Editor 配置
export const monacoConfig = {
  // 基础配置
  base: {
    automaticLayout: true,
    wordWrap: 'on',
    minimap: { enabled: true },
    scrollBeyondLastLine: false,
    fontSize: 14,
    lineHeight: 22,
    folding: true,
    renderWhitespace: 'boundary',
    tabSize: 2,
    insertSpaces: true,
    detectIndentation: true,
    trimAutoWhitespace: true,
    formatOnPaste: true,
    formatOnType: true
  },
  
  // Markdown 特定配置
  markdown: {
    language: 'markdown',
    theme: 'vs-dark',
    wordWrap: 'on',
    lineNumbers: 'on',
    glyphMargin: true,
    folding: true,
    // 禁用一些需要 Web Worker 的功能
    quickSuggestions: false,
    parameterHints: {
      enabled: false
    },
    hover: {
      enabled: false
    },
    links: false,
    autoClosingBrackets: 'always',
    autoClosingQuotes: 'always',
    autoSurround: 'languageDefined',
    // 启用 Markdown 语法高亮
    'bracketPairColorization.enabled': true
  }
}

// 自定义 Markdown 主题
export const customMarkdownTheme = {
  base: 'vs-dark',
  inherit: true,
  rules: [
    // Markdown 标题
    { token: 'keyword.md', foreground: '569cd6', fontStyle: 'bold' },
    { token: 'keyword.other.md', foreground: '569cd6' },
    
    // Markdown 链接
    { token: 'string.link.md', foreground: '4ec9b0' },
    { token: 'string.link.title.md', foreground: '4ec9b0' },
    
    // Markdown 代码
    { token: 'variable.md', foreground: 'ce9178' },
    { token: 'string.md', foreground: 'ce9178' },
    
    // Markdown 强调
    { token: 'emphasis.md', fontStyle: 'italic' },
    { token: 'strong.md', fontStyle: 'bold' },
    
    // Markdown 列表
    { token: 'keyword.list.md', foreground: 'dcdcaa' },
    
    // Markdown 引用
    { token: 'comment.quote.md', foreground: '6a9955', fontStyle: 'italic' },
    
    // Markdown 分隔线
    { token: 'meta.separator.md', foreground: '808080' }
  ],
  colors: {
    'editor.background': '#1e1e1e',
    'editor.foreground': '#d4d4d4',
    'editor.lineHighlightBackground': '#2d2d30',
    'editor.selectionBackground': '#264f78',
    'editor.inactiveSelectionBackground': '#3a3d41',
    'editorCursor.foreground': '#aeafad',
    'editorWhitespace.foreground': '#404040',
    'editorIndentGuide.background': '#404040',
    'editorIndentGuide.activeBackground': '#707070',
    'editorLineNumber.foreground': '#858585',
    'editorLineNumber.activeForeground': '#c6c6c6'
  }
}

// 初始化 Monaco Editor
export const initMonaco = () => {
  // 禁用 Web Worker 来避免配置问题
  window.MonacoEnvironment = {
    getWorker: () => {
      return {
        postMessage: () => {},
        addEventListener: () => {},
        removeEventListener: () => {},
        terminate: () => {}
      }
    }
  }

  // 注册自定义主题
  monaco.editor.defineTheme('markdown-dark', customMarkdownTheme)
  
  // 配置 Markdown 语言特性
  monaco.languages.setLanguageConfiguration('markdown', {
    brackets: [
      ['[', ']'],
      ['(', ')'],
      ['{', '}']
    ],
    autoClosingPairs: [
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '{', close: '}' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '`', close: '`' },
      { open: '*', close: '*' },
      { open: '_', close: '_' },
      { open: '**', close: '**' },
      { open: '__', close: '__' }
    ],
    surroundingPairs: [
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '{', close: '}' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '`', close: '`' },
      { open: '*', close: '*' },
      { open: '_', close: '_' }
    ]
  })
  
  // 注册 Markdown 代码补全提供者
  monaco.languages.registerCompletionItemProvider('markdown', {
    provideCompletionItems: (model, position) => {
      const suggestions = [
        // 标题
        {
          label: 'h1',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '# ${1:标题}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '一级标题'
        },
        {
          label: 'h2',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '## ${1:标题}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '二级标题'
        },
        {
          label: 'h3',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '### ${1:标题}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '三级标题'
        },
        
        // 链接
        {
          label: 'link',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '[${1:链接文本}](${2:URL})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入链接'
        },
        
        // 图片
        {
          label: 'image',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '![${1:图片描述}](${2:图片URL})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入图片'
        },
        
        // 代码块
        {
          label: 'code',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '```${1:language}\n${2:代码}\n```',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入代码块'
        },
        
        // 表格
        {
          label: 'table',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '| ${1:列1} | ${2:列2} | ${3:列3} |\n|---------|---------|----------|\n| ${4:内容1} | ${5:内容2} | ${6:内容3} |',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入表格'
        },
        
        // 列表
        {
          label: 'ul',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '- ${1:列表项}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '无序列表'
        },
        {
          label: 'ol',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '1. ${1:列表项}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '有序列表'
        },
        
        // 任务列表
        {
          label: 'task',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '- [ ] ${1:任务}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '任务列表'
        },
        
        // 引用
        {
          label: 'quote',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '> ${1:引用内容}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '引用'
        },
        
        // 分隔线
        {
          label: 'hr',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '---',
          documentation: '分隔线'
        },
        
        // 强调
        {
          label: 'bold',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '**${1:粗体文本}**',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '粗体'
        },
        {
          label: 'italic',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '*${1:斜体文本}*',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '斜体'
        },
        {
          label: 'strikethrough',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '~~${1:删除线文本}~~',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '删除线'
        }
      ]
      
      return { suggestions }
    }
  })
  
  // 注册格式化提供者
  monaco.languages.registerDocumentFormattingEditProvider('markdown', {
    provideDocumentFormattingEdits: (model) => {
      const text = model.getValue()
      const lines = text.split('\n')
      const edits = []
      
      // 简单的格式化规则
      lines.forEach((line, index) => {
        const trimmed = line.trim()
        
        // 标题后添加空行
        if (trimmed.match(/^#{1,6}\s+/)) {
          if (index < lines.length - 1 && lines[index + 1].trim() !== '') {
            edits.push({
              range: new monaco.Range(index + 2, 1, index + 2, 1),
              text: '\n'
            })
          }
        }
        
        // 列表项缩进
        if (trimmed.match(/^[-*+]\s+/) || trimmed.match(/^\d+\.\s+/)) {
          const leadingSpaces = line.length - line.trimLeft().length
          if (leadingSpaces % 2 !== 0) {
            edits.push({
              range: new monaco.Range(index + 1, 1, index + 1, leadingSpaces + 1),
              text: ' '.repeat(Math.floor(leadingSpaces / 2) * 2)
            })
          }
        }
      })
      
      return edits
    }
  })
}

// 创建 Monaco Editor 实例的辅助函数
export const createMarkdownEditor = (container, options = {}) => {
  const config = {
    ...monacoConfig.base,
    ...monacoConfig.markdown,
    ...options
  }
  
  return monaco.editor.create(container, config)
}

// 设置编辑器主题
export const setEditorTheme = (theme = 'markdown-dark') => {
  monaco.editor.setTheme(theme)
}

// 获取可用主题列表
export const getAvailableThemes = () => {
  return [
    { value: 'vs', label: '浅色主题' },
    { value: 'vs-dark', label: '深色主题' },
    { value: 'hc-black', label: '高对比度' },
    { value: 'markdown-dark', label: 'Markdown深色主题' }
  ]
}
