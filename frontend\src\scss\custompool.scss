.custom-pool-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: var(--el-bg-color);
    user-select: none; /* 添加：防止文本选择，模拟原生应用 */
  }
  
  .app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    margin-bottom: 20px;
    background: rgba(var(--el-bg-color-rgb), 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 4px 24px rgba(0, 0, 0, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    user-select: none; /* 添加：防止头部文本选择 */
  
    /* 科技感背景网格 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: 
        linear-gradient(90deg, rgba(var(--el-color-primary-rgb), 0.08) 1px, transparent 1px),
        linear-gradient(0deg, rgba(var(--el-color-primary-rgb), 0.08) 1px, transparent 1px);
      background-size: 24px 24px;
      opacity: 0.3;
      pointer-events: none;
    }
  
    /* 标题样式 */
    h2 {
      font-size: 22px;
      font-weight: 600;
      margin: 0;
      background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: 0.5px;
      position: relative;
      display: flex;
    align-items: center;
      gap: 12px;
  
      /* 标题前的装饰线 */
      &::before {
        content: '';
        width: 24px;
        height: 3px;
        background: linear-gradient(90deg, var(--el-color-primary), transparent);
        border-radius: 3px;
      }
    }
  
    /* 按钮组样式优化 */
    .header-actions {
      display: flex;
      gap: 12px;
      position: relative;
      z-index: 1;
  
      /* 按钮组的发光效果 */
      &::after {
        content: '';
        position: absolute;
        top: -20px;
        right: -20px;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, 
          rgba(var(--el-color-primary-rgb), 0.1) 0%,
          transparent 70%
        );
        pointer-events: none;
        opacity: 0.6;
        filter: blur(20px);
      }
    }
  }
  
  /* 暗色主题适配 */
  html.dark .app-header {
    background: rgba(30, 35, 45, 0.7);
    border-color: rgba(255, 255, 255, 0.05);
    box-shadow: 
      0 4px 24px rgba(0, 0, 0, 0.2),
      inset 0 0 0 1px rgba(255, 255, 255, 0.03);
  
    &::before {
      background-image: 
        linear-gradient(90deg, rgba(var(--el-color-primary-rgb), 0.12) 1px, transparent 1px),
        linear-gradient(0deg, rgba(var(--el-color-primary-rgb), 0.12) 1px, transparent 1px);
      opacity: 0.2;
    }
  
    h2::before {
      background: linear-gradient(90deg, 
        rgba(var(--el-color-primary-rgb), 0.8),
        transparent
      );
    }
  }
  
  /* 响应式调整 */
  @media screen and (max-width: 768px) {
    .app-header {
      flex-direction: column;
      gap: 16px;
      padding: 16px;
  
      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
  
  .app-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  /* 卡池容器 */
  .pools-container {
    background: linear-gradient(135deg, #f0f2f5 0%, #e6e9ec 100%);
    border-radius: 12px;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);
    padding: 10px 0;
    height: 40%;
  }
  
  .pools-wrapper {
    display: flex;
    height: 100%;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox完全隐藏滚动条 */
    padding: 0 16px;
    gap: 16px;
    scroll-behavior: auto; /* 改为auto以提升性能，通过JS控制平滑滚动 */
    cursor: grab; /* 指示可拖动 */
    user-select: none; /* 防止拖动时选中文本 */
    /* 添加硬件加速 */
    transform: translateZ(0);
    will-change: scroll-position;
  }
  
  /* 完全隐藏滚动条 - Webkit浏览器 */
  .pools-wrapper::-webkit-scrollbar {
    display: none; /* Chrome, Safari完全隐藏滚动条 */
  }
  
  /* 拖拽时的光标 */
  .pools-wrapper.grabbing {
    cursor: grabbing;
  }
  
  /* 完全隐藏el-scrollbar的滚动条 */
  .pool-items-wrapper :deep(.el-scrollbar__bar) {
    display: none !important; /* 强制隐藏滚动条 */
  }
  
  /* 移除不需要的样式 */
  .pools-wrapper::-webkit-scrollbar-thumb,
  .pools-wrapper::-webkit-scrollbar-track,
  .pool-items-wrapper:hover :deep(.el-scrollbar__bar) {
    display: none;
  }
  
  .pool-column {
    min-width: 250px;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--el-bg-color-overlay);
    border-radius: 12px;
    border: 1px solid rgba(var(--el-color-primary-rgb), 0.1); /* 添加微妙边框 */
    overflow: hidden;
    transition: all 0.3s;
    /* 精致的阴影效果 */
    box-shadow: 
      5px 5px 10px rgba(0, 0, 0, 0.06), 
      -5px -5px 10px rgba(255, 255, 255, 0.4),
      inset 0 0 0 1px rgba(255, 255, 255, 0.2); /* 内阴影边框 */
    height: 100%;
    transform: translateZ(0);
  }
  
  .pool-column:hover {
    box-shadow: 
      7px 7px 14px rgba(0, 0, 0, 0.08), 
      -7px -7px 14px rgba(255, 255, 255, 0.5),
      inset 0 0 0 1px rgba(255, 255, 255, 0.4); /* 内阴影边框增强 */
    border-color: rgba(var(--el-color-primary-rgb), 0.15);
    transform: translateY(-3px);
  }
  
  /* 卡池头部重新设计 */
  .pool-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 10px 8px !important; /* 减少水平内边距 */
    min-width: 250px !important;
    max-width: none !important;
    overflow: visible !important;
  }
  
  /* 活跃卡池样式 */
  .pool-active {
    background: linear-gradient(90deg, 
      rgba(var(--el-color-primary-rgb), 0.05) 0%,
      rgba(var(--el-bg-color-overlay-rgb), 1) 100%);
  }
  
  /* 标题包装容器 */
  .pool-title-wrapper {
    min-width: 50px !important;
    margin-right: 5px !important;
    max-width: 80px !important; /* 进一步限制标题宽度 */
  }
  
  /* 标题样式 */
  .pool-title {
    font-size: 14px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px; /* 减小最大宽度 */
    color: #fff;
    text-shadow: 0 1px 1px rgba(0,0,0,0.3);
    user-select: none; /* 添加：防止标题文本选择 */
  }
  
  /* 计数标签简化 */
  .pool-count {
    font-size: 12px;
    min-width: 24px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 5px;
    background: rgba(var(--el-color-primary-rgb), 0.2);
    color: #fff;
    border-radius: 9px;
    margin-left: 5px;
    user-select: none; /* 添加：防止计数标签文本选择 */
  }
  
  /* 优化右侧功能区 */
  .pool-functions {
    display: flex !important;
    align-items: center !important;
    gap: 4px !important; /* 减少按钮间距 */
    min-width: 110px !important; /* 确保足够显示所有按钮 */
    max-width: none !important;
    margin-left: auto !important;
    overflow: visible !important;
  }
  
  /* 缩小选择器 */
  .draw-count-select {
    width: 40px !important; /* 进一步减小选择器宽度 */
  }
  
  /* 缩小按钮尺寸 */
  .action-btn,
  .pool-active-icon {
    min-width: 20px !important;
    width: 20px !important;
    height: 20px !important;
    padding: 2px !important;
    margin: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  .pool-active-icon {
    font-size: 14px !important;
  }
  
  /* 彻底重写滚动区域的样式 */
  .pool-items-container {
    position: relative;
    height: 190px; /* 明确设置容器高度 */
    width: 100%;
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    border-radius: 8px;
    background: rgba(var(--el-bg-color-rgb), 0.5);
    box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.05);
    margin-top: 10px;
    overflow: hidden; /* 确保内容不会溢出 */
  }
  
  /* 确保滚动条组件正确显示 */
  .full-width-scrollbar {
    width: 100%;
    height: 100%;
  }
  
  /* 滚动条轨道可见性增强 */
  .full-width-scrollbar :deep(.el-scrollbar__bar) {
    opacity: 0.4;
    transition: opacity 0.3s;
  }
  
  .full-width-scrollbar:hover :deep(.el-scrollbar__bar) {
    opacity: 0.8;
  }
  
  /* 确保滚动内容区域正确布局 */
  .scrollable-content {
    padding: 10px;
    min-height: 100%;
  }
  
  /* 完全移除掉以前的滚动相关样式，避免冲突 */
  .pool-items-wrapper {
    position: relative;
    width: 100%;
    margin-top: 8px;
  }
  
  /* 调整卡片布局确保正确显示 */
  .pool-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 8px;
    margin: 4px 0;
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
    user-select: none; /* 添加：防止卡片项文本选择 */
  
    /* 选中状态样式 */
    &.item-selected {
      background: rgba(var(--el-color-primary-rgb), 0.1);
      border-color: rgba(var(--el-color-primary-rgb), 0.2);
      transform: translateX(4px);
      
      /* 添加左侧边框指示器 */
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: var(--el-color-primary);
        border-radius: 3px 0 0 3px;
      }
      
      /* 优化复选框样式 */
      :deep(.el-checkbox__input.is-checked) {
        .el-checkbox__inner {
          background-color: var(--el-color-primary);
          border-color: var(--el-color-primary);
          box-shadow: 0 0 8px rgba(var(--el-color-primary-rgb), 0.3);
        }
      }
      
      /* 优化标题文本样式 */
      .item-title {
        color: var(--el-color-primary);
        font-weight: 500;
      }
      
      /* 添加微光效果 */
      &::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.1) 50%,
          rgba(255, 255, 255, 0) 100%
        );
        transform: rotate(45deg);
        animation: shine 2s infinite;
        pointer-events: none;
      }
    }
  
    &:hover {
      background: var(--el-fill-color-light);
      transform: translateX(4px);
    }
  
    /* 标题样式 */
    .item-title {
      transition: all 0.3s ease;
      user-select: none; /* 添加：防止卡片标题文本选择 */
    }
  }
  
  /* 添加微光动画 */
  @keyframes shine {
    0% {
      transform: translateX(-100%) rotate(45deg);
    }
    100% {
      transform: translateX(100%) rotate(45deg);
    }
  }
  
  /* 暗色主题适配 */
  html.dark {
    .pool-item {
      &.item-selected {
        background: rgba(var(--el-color-primary-rgb), 0.15);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
        
        &::after {
          background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            rgba(255, 255, 255, 0) 100%
          );
        }
      }
    }
  }
  
  /* 优化复选框样式 */
  :deep(.el-checkbox) {
    margin-right: 4px;
    
    .el-checkbox__inner {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 4px;
    }
    
    .el-checkbox__input.is-checked {
      .el-checkbox__inner {
        transform: scale(1.1);
      }
    }
  }
  
  /* 针对有问题的el-scrollbar设置调试样式 */
  .el-scrollbar__wrap {
    overflow-x: hidden !important;
  }
  
  .el-scrollbar__view {
    height: 100%;
  }
  
  .pool-items-container .el-empty {
    padding: 20px 0;
    opacity: 0.8;
  }
  
  .pool-items-container .el-empty :deep(.el-empty__description) {
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }
  
  /* 下部分：抽卡结果区域 - 参考StoryInspiration风格 */
  .drawing-section tech-panel {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 8px;
    margin: 16px;
  }
  
  .tech-panel {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .drawing-header {
    display: flex;
    align-items: center;
    gap: 20px; /* 增加间距 */
    margin-bottom: 20px;
    padding: 20px 24px; /* 增加内边距 */
    user-select: none; /* 添加：防止抽卡结果标题文本选择 */
    border-bottom: 2px solid var(--el-border-color-light);
    position: sticky;
    top: 0;
    background: var(--el-bg-color-overlay);
    z-index: 2;
    backdrop-filter: blur(8px);
    flex-shrink: 0;
    justify-content: space-between;
  
  .section-title {
    display: flex;
    align-items: center;
      gap: 20px; /* 增加图标和文字的间距 */
      user-select: none; /* 添加：防止区域标题文本选择 */
  
      /* 调整星星图标样式 */
      .result-icon {
        font-size: 32px; /* 增大图标尺寸 */
        color: var(--el-color-primary);
        animation: rotateIcon 8s linear infinite;
        filter: drop-shadow(0 0 4px rgba(var(--el-color-primary-rgb), 0.4));
        margin-left: 4px; /* 增加左侧间距 */
      }
  
      h3 {
        margin: 0;
        font-size: 24px; /* 增大标题字号 */
        font-weight: 600;
        background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-success));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: 0.5px;
        user-select: none; /* 添加：防止标题文本选择 */
      }
    }
  
    .interaction-tips {
      display: flex;
      align-items: center;
      gap: 12px;
      color: var(--el-text-color-secondary);
      font-size: 15px; /* 增大提示文字大小 */
      margin-right: 4px; /* 增加右侧间距 */
      user-select: none; /* 添加：防止交互提示文本选择 */
  
      .tips-icon {
        font-size: 24px; /* 增大图标尺寸 */
        color: var(--el-color-primary-light-3);
        cursor: pointer;
        transition: all 0.3s;
  
        &:hover {
          color: var(--el-color-primary);
          transform: scale(1.1);
        }
      }
    }
  }
  
  /* 调整网格背景 */
  .tech-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      linear-gradient(90deg, rgba(var(--el-color-primary-rgb), 0.08) 1px, transparent 1px),
      linear-gradient(0deg, rgba(var(--el-color-primary-rgb), 0.08) 1px, transparent 1px);
    background-size: 24px 24px; /* 增大网格尺寸 */
    opacity: 0.3;
    pointer-events: none;
  }
  
  /* 添加旋转动画 */
  @keyframes rotateIcon {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  /* 暗色主题适配 */
  html.dark .drawing-header .section-title .result-icon {
    filter: drop-shadow(0 0 6px rgba(var(--el-color-primary-rgb), 0.6));
  }
  
  /* 暗色主题适配 */
  html.dark .drawing-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
    
    .tech-lines {
      background-image: 
        linear-gradient(90deg, rgba(var(--el-color-primary-rgb), 0.15) 1px, transparent 1px),
        linear-gradient(0deg, rgba(var(--el-color-primary-rgb), 0.15) 1px, transparent 1px);
      opacity: 0.4;
    }
  }
  
  .draw-controls {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .draw-button {
    position: relative;
    overflow: hidden;
  }
  
  .draw-button::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      to bottom right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(45deg);
    animation: shine 3s infinite;
  }
  
  @keyframes shine {
    0% {
      transform: translateX(-100%) rotate(45deg);
    }
    20%, 100% {
      transform: translateX(100%) rotate(45deg);
    }
  }
  
  .draw-results {
    flex: 1;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
    padding-right: 8px;
  }
  
  .card-item {
    transition: all 0.3s;
  }
  
  .card-container {
    height: 100%;
  }
  
  .card-header {
    display: flex;
    flex-direction: column;
  }
  
  .card-title {
    font-weight: bold;
    font-size: 16px;
  }
  
  .card-source {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }
  
  .card-content {
    display: flex;
    flex-direction: column;
  }
  
  .card-description {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .card-properties {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
  
  .property-item {
    border-top: 1px solid var(--el-border-color-lighter);
    padding-top: 5px;
    font-size: 13px;
  }
  
  /* 编辑对话框样式 */
  .pool-edit-dialog {
    --dialog-padding: 0;
  }
  
  .pool-edit-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #ebeef5;
    position: relative;
  }
  
  .pool-edit-container {
    padding: 20px;
  }
  
  .pool-basic-info {
    background-color: rgba(0, 0, 0, 0.01);
    border-radius: 8px;
    padding: 16px;
    border: 1px dashed #dcdfe6;
  }
  
  .required-field :deep(.el-form-item__label)::before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
  
  .divider-title {
    font-size: 16px;
    font-weight: 500;
    color: #606266;
  }
  
  .pool-edit-dialog :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: #f8f9fa;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none; /* 添加：防止对话框页脚文本选择 */
  }
  
  .delete-pool-btn {
    margin-right: auto; /* 将删除按钮推到左侧 */
  }
  
  /* 输入框和文本域样式优化 */
  .pool-edit-dialog :deep(.el-input__wrapper),
  .pool-edit-dialog :deep(.el-textarea__inner) {
    box-shadow: none;
    border: 1px solid #dcdfe6;
    transition: all 0.3s;
  }
  
  .pool-edit-dialog :deep(.el-input__wrapper:hover),
  .pool-edit-dialog :deep(.el-textarea__inner:hover) {
    border-color: var(--el-color-primary-light-5);
  }
  
  .pool-edit-dialog :deep(.el-input__wrapper:focus-within),
  .pool-edit-dialog :deep(.el-textarea__inner:focus) {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }
  
  /* 字数限制显示优化 */
  .pool-edit-dialog :deep(.el-input__count-inner) {
    background: transparent;
    font-size: 12px;
    color: #909399;
  }
  
  .dimensions-container {
    margin-bottom: 20px;
  }
  
  .dimension-item {
    margin-bottom: 10px;
  }
  
  .add-dimension-row {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    align-items: center;
  }
  
  .cards-container {
    margin-top: 20px;
  }
  
  .cards-toolbar {
    margin-bottom: 10px;
  }
  
  .cards-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }
  
  .card-edit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .card-edit-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .card-properties-edit {
    margin-top: 10px;
  }
  
  /* 动画 */
  .card-fade-enter-active,
  .card-fade-leave-active {
    transition: all 0.3s ease;
  }
  .card-fade-enter-from,
  .card-fade-leave-to {
    opacity: 0;
    transform: translateY(30px);
  }
  
  /* 技术卡片风格 */
  .tech-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .tech-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  }
  
  /* 卡片项样式 */
  .pool-item {
    padding: 8px 10px;
    margin-bottom: 6px;
    border-radius: 6px;
    background: rgba(var(--el-fill-color-light-rgb), 0.6);
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  
  .pool-item:hover {
    background: rgba(var(--el-fill-color-rgb), 0.8);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  }
  
  .pool-item.item-selected {
    background: rgba(var(--el-color-primary-rgb), 0.1);
    border-color: rgba(var(--el-color-primary-rgb), 0.2);
  }
  
  .item-title {
    margin-left: 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  /* 暗色主题适配 */
  html.dark .pools-container {
    background: linear-gradient(135deg, #1a1e21 0%, #161a1d 100%);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.07);
  }
  
  html.dark .pool-column {
    background: var(--el-bg-color);
    border: 1px solid rgba(255, 255, 255, 0.07);
    box-shadow: 
      5px 5px 15px rgba(0, 0, 0, 0.3), 
      -5px -5px 15px rgba(255, 255, 255, 0.02),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  }
  
  html.dark .pool-column:hover {
    box-shadow: 
      7px 7px 20px rgba(0, 0, 0, 0.4), 
      -7px -7px 20px rgba(255, 255, 255, 0.03),
      inset 0 0 0 1px rgba(255, 255, 255, 0.07);
    border-color: rgba(var(--el-color-primary-rgb), 0.3);
  }
  
  html.dark .pool-header {
    background: linear-gradient(135deg, 
      rgba(var(--el-color-primary-rgb), 0.15) 0%, 
      rgba(var(--el-color-primary-rgb), 0.05) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.03);
    color: var(--el-color-white);
  }
  
  html.dark .pool-header::before {
    background: linear-gradient(90deg, 
      var(--el-color-primary) 0%, 
      var(--el-color-primary-light-5) 100%);
    opacity: 0.8;
  }
  
  html.dark .pool-title {
    text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
    color: rgba(255, 255, 255, 0.95);
  }
  
  html.dark .draw-count-select :deep(.el-input__wrapper) {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      inset 2px 2px 4px rgba(0, 0, 0, 0.3),
      inset -2px -2px 4px rgba(255, 255, 255, 0.02);
  }
  
  html.dark .draw-count-select :deep(.el-input__inner) {
    color: rgba(255, 255, 255, 0.85);
  }
  
  html.dark .action-btn {
    color: rgba(255, 255, 255, 0.8);
  }
  
  html.dark .action-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--el-color-primary-light-3);
  }
  
  html.dark .pool-items-wrapper {
    background: rgba(255, 255, 255, 0.02);
  }
  
  html.dark .pool-item {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow: 
      0 2px 6px rgba(0, 0, 0, 0.2),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  }
  
  html.dark .pool-item:hover {
    background-color: rgba(var(--el-color-primary-rgb), 0.15);
  }
  
  html.dark .item-selected {
    background-color: rgba(var(--el-color-primary-rgb), 0.2);
    border-left: 3px solid var(--el-color-primary-light-3);
  }
  
  html.dark .item-title {
    color: rgba(255, 255, 255, 0.85);
  }
  
  html.dark .pool-item :deep(.el-checkbox__inner) {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  html.dark .pool-items-container .el-empty :deep(.el-empty__description) {
    color: rgba(255, 255, 255, 0.5);
  }
  
  /* 卡片抽取结果区域暗色适配 */
  html.dark .tech-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.07);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  html.dark .tech-card::before {
    opacity: 0.8;
  }
  
  html.dark .tech-panel {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  html.dark .card-title {
    color: rgba(255, 255, 255, 0.95);
  }
  
  html.dark .card-source {
    color: rgba(255, 255, 255, 0.5);
  }
  
  html.dark .card-description {
    color: rgba(255, 255, 255, 0.8);
  }
  
  html.dark .property-item {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
  }
  
  /* 滚动条暗色适配 */
  html.dark .el-scrollbar__thumb {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  html.dark .el-scrollbar__thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
  
  /* 卡池实体管理对话框样式 */
  .entity-manager-dialog {
    --dialog-padding: 0;
  }
  
  .entity-manager-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 16px 20px;
    background: var(--el-bg-color-overlay);
    border-bottom: 1px solid var(--el-border-color-lighter);
    position: relative;
  }
  
  .entity-manager-dialog :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .manager-header-line {
    height: 4px;
    background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-3));
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }
  
  .manager-container {
    position: relative;
    width: 100%;
    height: auto;
    overflow: hidden !important; /* 强制禁用滚动 */
  }
  
  .editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 10px;
    padding: 0 0 16px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .manager-tabs {
    flex: 1;
  }
  
  .toolbar-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .tab-icon {
    margin-right: 6px;
  }
  
  .entity-editor, .dimension-editor {
    margin-top: 20px;
  }
  
  .option-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
  }
  
  .options-editor {
    padding: 10px;
    max-height: 350px;
    overflow-y: auto;
  }
  
  .manager-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  
  /* 表格样式优化 */
  .entity-manager-dialog :deep(.el-table) {
    --el-table-border-color: var(--el-border-color-lighter);
    --el-table-header-bg-color: var(--el-fill-color-light);
    border-radius: 8px;
    overflow: hidden;
  }
  
  .entity-manager-dialog :deep(.el-table__header) {
    background-color: var(--el-color-primary-light-9);
    font-weight: 600;
  }
  
  .entity-manager-dialog :deep(.el-table__row) {
    transition: all 0.3s;
  }
  
  .entity-manager-dialog :deep(.el-table__row:hover) {
    background-color: var(--el-color-primary-light-9);
  }
  
  /* 适配暗色主题 */
  html.dark .entity-manager-dialog :deep(.el-dialog__header) {
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  html.dark .entity-manager-dialog :deep(.el-table__header) {
    background-color: rgba(var(--el-color-primary-rgb), 0.2);
  }
  
  html.dark .entity-manager-dialog :deep(.el-table__row:hover) {
    background-color: rgba(var(--el-color-primary-rgb), 0.1);
  }
  
  html.dark .editor-toolbar {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* 属性编辑相关样式 */
  .properties-editor {
    padding: 0;
    max-height: 500px;
    overflow-y: auto;
  }
  
  .properties-toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .dimension-select {
    flex: 1;
  }
  
  .property-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .property-item-editor {
    background: var(--el-fill-color-light);
    border-radius: 8px;
    padding: 16px;
    position: relative;
  }
  
  .property-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 8px;
  }
  
  .property-name {
    font-weight: 600;
    font-size: 14px;
    flex: 1;
  }
  
  .property-type-badge {
    background: var(--el-color-primary-light-8);
    color: var(--el-color-primary-dark-2);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
  
  .property-editor {
    margin-top: 8px;
  }
  
  .remove-property {
    position: absolute;
    top: 12px;
    right: 12px;
  }
  
  .array-editor {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .array-item {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  /* 暗黑模式适配 */
  html.dark .property-item-editor {
    background: rgba(255, 255, 255, 0.05);
  }
  
  html.dark .property-type-badge {
    background: rgba(var(--el-color-primary-rgb), 0.2);
    color: var(--el-color-primary-light-5);
  }
  
  html.dark .properties-toolbar {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .card-id {

    color: var(--el-text-color-secondary);
    background: var(--el-fill-color);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 13px;
  }
  
  html.dark .card-id {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
  }
  
  /* 添加卡片对话框样式 */
  .add-card-dialog {
    --dialog-padding: 0;
  }
  
  .add-card-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 16px 20px;
    background: var(--el-bg-color-overlay);
    border-bottom: 1px solid var(--el-border-color-lighter);
    position: relative;
  }
  
  .add-card-header-line {
    height: 4px;
    background: linear-gradient(90deg, var(--el-color-success), var(--el-color-success-light-3));
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }
  
  .add-card-container {
    max-height: calc(80vh - 160px);
    overflow: hidden;
  }
  
  .card-basic-info {
    margin-bottom: 20px;
    padding: 16px;
    border-radius: 8px;
    background: var(--el-fill-color-light);
  }
  
  .form-hint {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
  
  .card-properties-section {
    margin-top: 20px;
  }
  
  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .section-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    margin-right: 10px;
  }
  
  
  
  .property-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 0 4px;
  }
  
  .property-input-group {
    background: var(--el-fill-color-light);
    border-radius: 8px;
    padding: 16px;
  }
  
  .property-label {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 8px;
  }
  
  .property-label span:first-child {
    font-weight: 600;
    font-size: 14px;
  }
  
  .info-icon {
    color: var(--el-color-info);
    font-size: 16px;
    cursor: help;
  }
  
  .property-input {
    width: 100%;
  }
  
  /* 适配暗色主题 */
  html.dark .card-basic-info,
  html.dark .property-input-group {
    background: rgba(255, 255, 255, 0.05);
  }
  
  html.dark .add-card-dialog :deep(.el-dialog__header) {
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  html.dark .form-hint{
    color: rgba(255, 255, 255, 0.5);
  }
  
  html.dark .info-icon {
    color: rgba(255, 255, 255, 0.5);
  }
  
  /* 确保所有对话框内容区域都有统一的滚动行为 */
  .dialog-scrollable-content {
    max-height: 60vh;
    overflow-y: auto;
  }
  
  /* 设置一致的滚动条样式 */
  .dialog-scrollable-content::-webkit-scrollbar {
    width: 6px;
  }
  
  .dialog-scrollable-content::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 3px;
  }
  
  .dialog-scrollable-content::-webkit-scrollbar-track {
    background-color: var(--el-fill-color-light);
  }
  
  /* 全局对话框样式优化 - 使用固定高度 */
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    height: 700px !important; /* 固定高度 */
    margin: 5vh auto !important;
  }
  
  :deep(.el-dialog__body) {
    flex: 1;
    overflow: hidden; /* 防止对话框本身出现滚动条 */
    padding: 0; /* 移除默认内边距 */
  }
  
  /* 确保表格不会导致水平滚动 */
  .el-table {
    width: 100% !important;
    table-layout: fixed !important; /* 确保表格不会扩展 */
  }
  
  /* 表格单元格处理长文本 */
  .el-table .cell {
    word-break: break-all;
    white-space: normal;
    line-height: 1.5;
  }
  
  /* 对话框内容容器 */
  .manager-container,
  .add-card-container,
  .property-form {
    overflow-x: hidden; /* 防止水平滚动 */
    width: 100%;
    box-sizing: border-box;
  }
  
  /* 维度管理表格 */
  .dimension-editor .el-table__body-wrapper {
    overflow-x: hidden !important; /* 强制禁用水平滚动 */
  }
  
  /* 卡片属性编辑区域 */
  .properties-editor {
    overflow-x: hidden;
    width: 100%;
    padding: 20px !important;
    box-sizing: border-box;
  }
  
  /* 选项编辑器 */
  .options-editor {
    overflow-x: hidden;
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
  }
  
  /* 滚动区域宽度控制 */
  .el-scrollbar {
    width: 100% !important;
    overflow-x: hidden !important;
  }
  
  /* 修复对话框内部滚动问题的通用样式 - 使用固定高度计算 */
  .dialog-content-wrapper {
    overflow-y: auto;
    overflow-x: hidden;
    height: 600px; /* 固定高度，减去头部和底部约100px */
  }
  
  /* 确保卡片添加对话框也使用固定高度 */
  .add-card-dialog :deep(.el-dialog) {
    height: 600px !important; /* 稍小一些的固定高度 */
  }
  
  .add-card-dialog .dialog-content-wrapper {
    height: 500px; /* 对应调整内容区域高度 */
  }
  
  /* 属性编辑对话框固定高度 */
  .properties-editor {
    height: 450px;
    overflow-y: auto;
  }
  
  /* 选项编辑对话框固定高度 */
  .options-editor {
    height: 350px;
    overflow-y: auto;
  }
  
  /* 维度管理表格固定高度 */
  .dimension-editor .el-table {
    height: 500px !important;
  }
  
  /* 卡片管理表格固定高度 */
  .entity-editor .el-table {
    height: 500px !important;
  }
  
  /* 属性值样式 */
  .property-value {
    display: inline-block;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  /* 空属性样式 */
  .empty-property {
    color: var(--el-text-color-placeholder);
  }
  
  /* 颜色预览 */
  .color-preview {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-lighter);
    display: inline-block;
  }
  
  /* 卡片属性编辑按钮 */
  .entity-editor .el-button--small.is-circle {
    padding: 6px;
    margin: 0 2px;
  }
  
  .import-dialog-content {
    padding: 0 20px;
  }
  
  .import-tip {
    margin-bottom: 15px;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }
  
  .import-error {
    margin-top: 15px;
  }
  
  .import-dialog-content .el-textarea__inner {
    
    font-size: 13px;
  }
  
  /* 导入对话框固定高度 */
  .import-dialog :deep(.el-dialog) {
    height: auto !important;
    max-height: 600px;
  }
  
  /* 高科技拟态风格的卡池管理对话框 */
  .entity-manager-dialog :deep(.el-dialog) {
    border-radius: 12px;
    background: linear-gradient(145deg, var(--el-bg-color), var(--el-bg-color-overlay));
    box-shadow: 
      0 10px 30px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.08);
  }
  
  /* 标题栏改造 */
  .entity-manager-dialog :deep(.el-dialog__header) {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.05), transparent);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    padding: 18px 24px;
    position: relative;
  }
  
  /* 标题文字样式 */
  .entity-manager-dialog :deep(.el-dialog__title) {
    
    font-weight: 500;
    font-size: 18px;
    letter-spacing: 0.5px;
    background: linear-gradient(to right, var(--el-color-primary), var(--el-color-primary-light-3));
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    user-select: none; /* 添加：防止对话框标题文本选择 */
  }
  
  /* 顶部装饰线 */
  .manager-header-line {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, 
      var(--el-color-primary), 
      var(--el-color-success),
      var(--el-color-primary-light-3)
    );
    z-index: 1;
    box-shadow: 0 0 10px rgba(0, 100, 255, 0.5);
  }
  
  /* 高科技边框线 */
  .manager-container {
    position: relative;
  }
  
  .manager-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-top: none;
    pointer-events: none;
    box-sizing: border-box;
  }
  
  /* 工具栏美化 */
  .editor-toolbar {
    background: linear-gradient(to bottom, 
      rgba(255, 255, 255, 0.03), 
      transparent
    );
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px 8px 0 0;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 0 10px;
  }
  
  /* 标签页样式 */
  .manager-tabs :deep(.el-tabs__nav) {
    border: none !important;
  }
  
  .manager-tabs :deep(.el-tabs__item) {
    
    font-weight: 500;
    letter-spacing: 0.3px;
    height: 48px;
    line-height: 48px;
    transition: all 0.3s ease;
    user-select: none; /* 添加：防止标签页文本选择 */
  }
  
  .manager-tabs :deep(.el-tabs__item.is-active) {
    color: var(--el-color-primary);
    font-weight: 600;
    transform: translateY(-2px);
  }
  
  .manager-tabs :deep(.el-tabs__active-bar) {
    height: 3px;
    border-radius: 3px;
    background: linear-gradient(to right, 
      var(--el-color-primary), 
      var(--el-color-primary-light-3)
    );
    box-shadow: 0 0 8px var(--el-color-primary-light-3);
  }
  
  /* 标签页图标 */
  .tab-icon {
    margin-right: 6px;
    font-size: 16px;
    vertical-align: -3px;
  }
  
  /* 工具栏按钮 */
  .toolbar-actions {
    padding: 10px 0;
    display: flex;
    gap: 12px;
  }
  
  .toolbar-actions .el-button {
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.2s ease;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    user-select: none; /* 添加：防止按钮文本选择 */
  }
  
  .toolbar-actions .el-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.12);
  }
  
  /* 表格样式 */
  .entity-editor .el-table,
  .dimension-editor .el-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 
      0 8px 20px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }
  
  /* 表头样式 */
  .entity-editor .el-table :deep(th.el-table__cell),
  .dimension-editor .el-table :deep(th.el-table__cell) {
    background: linear-gradient(to bottom, 
      rgba(0, 0, 0, 0.03), 
      rgba(0, 0, 0, 0.06)
    );
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.3px;
    color: var(--el-text-color-primary);
    padding: 14px 12px;
    border-bottom: 2px solid rgba(0, 0, 0, 0.04);
  }
  
  /* 表格单元格 */
  .entity-editor .el-table :deep(td.el-table__cell),
  .dimension-editor .el-table :deep(td.el-table__cell) {
    padding: 12px;
    transition: background 0.2s ease;
  }
  
  /* 表格行悬浮效果 */
  .entity-editor .el-table :deep(.el-table__row:hover > td),
  .dimension-editor .el-table :deep(.el-table__row:hover > td) {
    background-color: rgba(var(--el-color-primary-rgb), 0.05) !important;
  }
  
  /* 操作按钮样式 */
  .entity-editor .el-button--small.is-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    padding: 8px;
    margin: 0 5px;
    transition: all 0.3s ease;
    box-shadow: 
      0 4px 8px rgba(0, 0, 0, 0.08),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }
  
  .entity-editor .el-button--small.is-circle:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 
      0 6px 12px rgba(0, 0, 0, 0.12),
      0 0 0 1px rgba(255, 255, 255, 0.08) inset;
  }
  
  /* 属性值显示改进 */
  .property-value {
    
    font-size: 13px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  /* 颜色预览增强 */
  .color-preview {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
  }
  
  .color-preview:hover {
    transform: scale(1.15);
  }
  
  /* 空状态美化 */
  .entity-editor .el-empty,
  .dimension-editor .el-empty {
    padding: 40px 0;
    background: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.03));
    border-radius: 10px;
    margin: 20px 0;
  }
  
  /* 标签样式 */
  .el-tag {
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 12px;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  /* 适配暗色主题 */
  html.dark .entity-manager-dialog :deep(.el-dialog) {
    background: linear-gradient(145deg, #1f2937, #111827);
    box-shadow: 
      0 10px 30px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.03) inset;
  }
  
  html.dark .manager-header-line {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
  }
  
  html.dark .editor-toolbar {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.01), transparent);
  }
  
  html.dark .entity-editor .el-table,
  html.dark .dimension-editor .el-table {
    background-color: rgba(30, 41, 59, 0.8);
    box-shadow: 
      0 8px 20px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.03);
  }
  
  html.dark .entity-editor .el-table :deep(th.el-table__cell),
  html.dark .dimension-editor .el-table :deep(th.el-table__cell) {
    background: linear-gradient(to bottom, #1e293b, #0f172a);
    border-bottom: 2px solid rgba(255, 255, 255, 0.03);
  }
  
  html.dark .entity-editor .el-button--small.is-circle {
    background: #1e293b;
    box-shadow: 
      0 4px 8px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.03) inset;
  }
  
  html.dark .property-value {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  /* 改良卡池内容滚动容器 */
  .pool-items-wrapper {
    position: relative;
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    border-radius: 9px;
    margin-top: 2px;
    background: rgba(var(--el-bg-color-rgb), 0.5);
    box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.05);
    height: 200px; /* 固定高度确保可以滚动 */
    overflow: hidden;
  }
  
  /* 原生滚动容器 */
  .native-scroll {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* Firefox */
    padding: 10px;
  }
  
  /* 自定义webkit滚动条样式 */
  .native-scroll::-webkit-scrollbar {
    width: 6px;
  }
  
  .native-scroll::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .native-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
  }
  
  .native-scroll::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
  
  /* 取消之前的滚动相关样式 */
  .pool-items-container {
    position: relative;
  }
  
  /* 解决水平滚动与垂直滚动冲突 */
  .pools-wrapper {
    /* 将鼠标滚轮事件的冒泡阻止从卡池内容区域传递 */
    pointer-events: auto;
  }
  
  .native-scroll {
    /* 在卡池内容区域设置，使其与水平滚动分离 */
    pointer-events: auto;
  }
  
  /* 修改卡池项样式以适应原生滚动 */
  .pool-item {
    padding: 8px 10px;
    margin-bottom: 8px;
    border-radius: 6px;
    background: rgba(var(--el-fill-color-light-rgb), 0.7);
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  
  .pool-item:hover {
    background: rgba(var(--el-fill-color-rgb), 0.8);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  }
  
  .pool-item.item-selected {
    background: rgba(var(--el-color-primary-rgb), 0.1);
    border-color: rgba(var(--el-color-primary-rgb), 0.2);
  }
  
  .item-title {
    margin-left: 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .active-tag {
    margin-left: 8px;
    border-radius: 4px;
    font-size: 11px;
    padding: 0 6px;
    height: 20px;
    line-height: 20px;
  }
  
  .active-switch {
    margin-right: 15px;
  }
  
  .pool-active .active-tag {
    background-color: #4CAF50;
  }
  
  .pool-active .active-switch {
    color: #4CAF50;
  }
  
  /* 顶部按钮样式调整 */
  .header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  
  .draw-button-top {
    margin-left: 10px; /* 与导入卡池按钮保持一定距离 */
  }
  
  /* 确保顶部按钮图标对齐 */
  .header-actions .el-button .el-icon {
    vertical-align: middle;
    margin-right: 4px;
  }
  
  /* 结果展示区域 - 性能优化版本 */
  .result-scroll-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    /* 添加硬件加速 */
    transform: translateZ(0);
    will-change: scroll-position;
  }

  .result-container {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 10px 0;
    cursor: grab;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    /* 性能优化 */
    scroll-behavior: auto; /* 通过JS控制平滑滚动 */
    transform: translateZ(0);
    will-change: scroll-position;

    /* 隐藏水平滚动条 - Chrome, Safari, Opera */
    &::-webkit-scrollbar {
      display: none;
    }

    /* 拖动时样式 */
    &.dragging {
      cursor: grabbing;
      scroll-behavior: auto; /* 拖动时禁用平滑滚动 */
    }

    /* 渐变遮罩提示可以滚动 */
    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 60px;
      background: linear-gradient(to right, transparent, var(--el-bg-color));
      pointer-events: none;
      opacity: 0.6;
      z-index: 1;
    }

    .result-group {
      min-width: 280px;
      max-width: 500px;
      flex-shrink: 0;
      margin-right: 0; /* 重置原有垂直布局的margin */
      /* 添加硬件加速 */
      transform: translateZ(0);
    }
  }
  
  /* 暗色主题适配渐变遮罩 */
  html.dark .result-container::after {
    background: linear-gradient(to right, transparent, rgba(30, 35, 45, 0.9));
  }
  
  /* 调整结果容器布局 */
  .result-container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap; /* 禁止换行 */
    width: 100%;
    padding: 15px;
    background: var(--el-bg-color);
    border-radius: 0 0 12px 12px;
    gap: 15px;
    overflow-x: auto; /* 添加水平滚动 */
    overflow-y: hidden;
    scrollbar-width: thin;
    -webkit-overflow-scrolling: touch; /* 流畅滚动体验 */
    padding-bottom: 20px; /* 为滚动条留出空间 */
  }
  
  /* 优化固定尺寸的结果组 */
  .result-group {
    min-width: 320px;
    width: 320px;
    max-width: 320px;
    height: 220px; /* 固定高度 */
    flex-shrink: 0; /* 防止压缩 */
    flex-grow: 0; /* 防止拉伸 */
    margin-bottom: 10px;
    background: linear-gradient(145deg, rgba(var(--el-bg-color-rgb), 0.6), rgba(var(--el-bg-color-rgb), 0.8));
    border-radius: 10px;
    padding: 12px 15px;
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    box-shadow: 
      0 4px 12px rgba(0, 0, 0, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 确保内容不溢出 */
  }
  
  /* 改进结果组头部，确保其不拉伸 */
  .result-group-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    flex-shrink: 0; /* 防止头部被压缩 */
    height: 40px; /* 固定头部高度 */
  }
  
  /* 优化结果项容器滚动 */
  .result-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 0 -5px;
    overflow-y: auto;
    max-height: 160px; /* 确保有足够高度可滚动 */
    padding: 5px;
    
    /* 滚动条基础样式 */
    scrollbar-width: thin !important;
    -ms-overflow-style: auto !important;
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 5px !important;
      height: 5px !important;
      display: block !important;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.02) !important;
      border-radius: 6px !important;
      margin: 3px 0 !important;
      display: block !important;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.3) !important;
      border-radius: 6px !important;
      border: 1px solid rgba(255, 255, 255, 0.08) !important;
      transition: all 0.3s ease !important;
      display: block !important;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.5) !important;
        cursor: pointer !important;
      }
    }
  }
  
  /* 滚动状态样式 */
  .result-items.scrolling {
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.6) !important;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.8) !important;
      }
    }
  }
  
  /* 悬停状态样式 */
  .result-items.hover {
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.4) !important;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.6) !important;
      }
    }
  }
  
  /* 暗色主题适配 */
  html.dark .result-items {
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.02) !important;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.4) !important;
      border-color: rgba(255, 255, 255, 0.03) !important;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.7) !important;
      }
    }
  }
  
  html.dark .result-items.scrolling {
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.7) !important;
      box-shadow: 0 0 8px rgba(var(--el-color-primary-rgb), 0.3) !important;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.9) !important;
      }
    }
  }
  
  html.dark .result-items.hover {
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.5) !important;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.7) !important;
      }
    }
  }
  
  /* 清除响应式调整 */
  @media (max-width: 992px) {
    .result-group {
      width: 320px; /* 保持固定宽度 */
      min-width: 320px;
      max-width: 320px;
    }
  }
  
  @media (max-width: 768px) {
    .result-group {
      width: 320px; /* 保持固定宽度 */
      min-width: 320px;
      max-width: 320px;
    }
  }
  
  /* 空状态居中显示 */
  .empty-result {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  /* 恢复结果组头部样式 */
  .result-group-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    user-select: none; /* 添加：防止结果组标题文本选择 */
  }
  
  /* 恢复图标样式 */
  .group-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 12px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  }
  
  /* 恢复结果项容器布局 */
  .result-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 0 -5px;
  }
  
  /* 恢复单个结果项样式 */
  .result-item {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    margin: 5px;
    white-space: nowrap;
    user-select: none; /* 添加：防止结果项文本选择 */
  }
  
  /* 恢复结果项悬停效果 */
  .result-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }
  
  /* 恢复结果组计数样式 */
  .group-count {
    margin-left: 12px;
    font-size: 13px;
    color: var(--el-text-color-secondary);
    background: rgba(var(--el-color-primary-rgb), 0.1);
    padding: 2px 10px;
    border-radius: 12px;
    font-weight: 500;
    user-select: none; /* 添加：防止计数文本选择 */
  }
  
  /* 恢复组标题样式 */
  .group-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    user-select: none; /* 添加：防止组标题文本选择 */
  }
  
  /* 恢复暗色模式适配 */
  .dark .result-group {
    background: linear-gradient(145deg, rgba(30, 35, 45, 0.8), rgba(25, 30, 40, 0.9));
    box-shadow: 
      0 4px 12px rgba(0, 0, 0, 0.2),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  }
  
  /* 新增控件容器样式 */
  .pool-controls {
    display: flex;
    align-items: center;
    margin: 0 8px;
    gap: 8px;
    flex-shrink: 0;
  }
  
  /* 确保激活开关样式 */
  .active-switch {
    flex-shrink: 0;
  }
  
  /* 确保数量选择器样式 */
  .draw-count-select {
    width: 60px;
    flex-shrink: 0;
  }
  
  /* 优化头部布局 */
  .pool-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    flex-wrap: nowrap;
    min-height: 60px;
    background: var(--el-bg-color-overlay);
    border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    border-radius: 12px 12px 0 0;
    overflow: hidden;
  }
  
  /* 恢复卡池标题样式 */
  .pool-info .pool-title {
    font-size: 15px;
    font-weight: 600;
    margin-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px;
    color: var(--el-text-color-primary);
  }
  
  /* 解决结果组样式冲突 */
  .result-group-header {
    margin-bottom: 8px;
    padding-bottom: 8px;
    height: 40px;
  }
  
  .result-items {
    padding: 2px 5px 5px 0;
    height: calc(100% - 48px);
    align-content: flex-start;
  }
  
  .result-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px 14px;
    font-size: 13px;
    font-weight: 500;
    height: 28px;
  }
  
  .pool-title-wrapper {
    display: flex;
    align-items: center;
    flex: 0 1 auto; /* 允许收缩但不拉伸 */
    min-width: 0; /* 确保可以收缩到最小宽度 */
    overflow: hidden;
    margin-right: 8px;
  }
  
  .pool-title {
    font-size: 15px;
    font-weight: 600;
    margin-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px; /* 限制最大宽度 */
  }
  
  .pool-count {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    background: rgba(var(--el-color-primary-rgb), 0.1);
    padding: 2px 8px;
    border-radius: 10px;
    white-space: nowrap;
    flex-shrink: 0; /* 防止被压缩 */
  }
  
  .pool-functions {
    display: flex;
    align-items: center;
    gap: 4px; /* 减少按钮间距 */
    flex-wrap: nowrap;
    flex-shrink: 0; /* 防止按钮被压缩 */
  }
  
  .pool-active-icon {
    padding: 6px;
    font-size: 16px;
    border-radius: 6px;
    transition: all 0.2s ease;
  }
  
  .pool-active-icon:hover {
    background-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
  }
  
  .pool-active-icon.is-active {
    background-color: #4CAF50;
    color: #4CAF50;
  }
  
  /* 重置并修复卡池标题和计数样式 */
  .pool-header .pool-title-wrapper {
    display: flex;
    align-items: center;
    min-width: 80px;
    margin-right: 10px;
  }
  
  /* 强制显示标题文本 */
  .pool-header .pool-title-wrapper .pool-title {
    color: var(--el-text-color-primary) !important; /* 使用主题文本颜色 */
    font-size: 14px !important;
    font-weight: 600 !important;
    margin-right: 6px !important;
    max-width: 70px !important;
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    text-shadow: none !important; /* 移除文字阴影 */
  }
  
  /* 暗色主题下的标题样式 */
  html.dark .pool-header .pool-title-wrapper .pool-title {
    color: #ffffff !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
  }
  
  /* 强制显示计数 */
  .pool-header .pool-title-wrapper .pool-count {
    color: var(--el-text-color-secondary) !important;
    font-size: 12px !important;
    background: var(--el-fill-color-light) !important;
    padding: 1px 6px !important;
    border-radius: 10px !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* 暗色主题下的计数样式 */
  html.dark .pool-header .pool-title-wrapper .pool-count {
    color: #ffffff !important;
    background: rgba(255, 255, 255, 0.2) !important;
  }
  
  /* 确保后面的按钮区域不会过大 */
  .pool-header .pool-functions {
    display: flex;
    align-items: center;
    gap: 4px;
    max-width: 150px;
  }
  
  /* 设置最小宽度防止被压缩消失 */
  .pool-header {
    min-width: 250px;
  }
  
  /* 优化app-header按钮质感 */
  .app-header .header-actions {
    display: flex;
    gap: 12px; /* 增加按钮间距 */
    align-items: center;
  }
  
  /* 顶部按钮通用样式 */
  .app-header .header-actions .el-button {
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 500; /* 增加字重 */
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px; /* 图标和文字间距 */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  }
  
  /* 按钮悬停效果 */
  .app-header .header-actions .el-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  /* 不同类型按钮的样式 */
  .app-header .header-actions .el-button--primary {
    background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
    border-color: var(--el-color-primary-light-5);
    color: white !important;
  }

  .app-header .header-actions .el-button--primary.is-plain {
    background: rgba(var(--el-color-primary-rgb), 0.05);
    border-color: rgba(var(--el-color-primary-rgb), 0.2);
    color: var(--el-color-primary) !important;
  }
  
  .app-header .header-actions .el-button--warning {
    background: linear-gradient(120deg, var(--el-color-warning), var(--el-color-warning-light-3));
    border-color: var(--el-color-warning-light-5);
  }
  
  .app-header .header-actions .el-button--success {
    background: linear-gradient(120deg, var(--el-color-success), var(--el-color-success-light-3));
    border-color: var(--el-color-success-light-5);
  }
  
  .app-header .header-actions .el-button--info {
    background: rgba(var(--el-color-info-rgb), 0.1);
    color: var(--el-color-info-dark-2);
    border-color: rgba(var(--el-color-info-rgb), 0.2);
  }
  
  /* 禁用状态优化 */
  .app-header .header-actions .el-button.is-disabled {
    opacity: 0.6;
    transform: none;
    background: var(--el-fill-color);
    box-shadow: none;
  }
  
  /* 应用标题样式优化 */
  .app-header h2 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    padding: 0;
    background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  /* 整体头部容器优化 */
  .app-header {
    user-select: none;
    padding: 16px 20px;
    background: var(--el-bg-color);
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    margin-bottom: 20px;
  }
  
  /* 导入配置弹窗样式优化 */
  .import-dialog {
    .el-dialog__header {
      padding: 20px 24px;
      margin: 0;
      border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
      background: linear-gradient(to right, var(--el-bg-color), var(--el-bg-color-overlay));
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    /* 导入说明文本样式 */
    .import-tips {
      font-size: 15px;
      color: var(--el-text-color-secondary);
      line-height: 1.6;
      margin-bottom: 20px;
      padding: 16px;
      background: rgba(var(--el-color-info-rgb), 0.05);
      border-radius: 8px;
      border: 1px dashed rgba(var(--el-border-color-rgb), 0.2);
      
      .tip-title {
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 6px;
        
        .el-icon {
          color: var(--el-color-primary);
        }
      }
      
      .tip-content {
        font-size: 14px;
        margin-left: 24px;
        
        li {
          margin-bottom: 6px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    /* 文件选择区域样式 */
    .file-select-area {
      margin: 24px 0;
      text-align: center;
      
      .el-upload {
        width: 100%;
        
        .el-upload-dragger {
          width: 100%;
          height: 180px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 12px;
          border: 2px dashed rgba(var(--el-border-color-rgb), 0.2);
          border-radius: 12px;
          background: rgba(var(--el-bg-color-rgb), 0.5);
          transition: all 0.3s ease;
          
          &:hover {
            border-color: var(--el-color-primary);
            background: rgba(var(--el-color-primary-rgb), 0.02);
            transform: translateY(-2px);
          }
          
          .el-icon {
            font-size: 48px;
            color: var(--el-color-primary);
            margin-bottom: 8px;
          }
          
          .upload-text {
            font-size: 16px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
          
          .upload-tip {
            font-size: 13px;
            color: var(--el-text-color-secondary);
            margin-top: 4px;
          }
        }
      }
    }
    
    /* 按钮样式优化 */
    .dialog-footer {
      padding-top: 20px;
      border-top: 1px solid rgba(var(--el-border-color-rgb), 0.1);
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      
      .el-button {
        padding: 10px 24px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        
        &:hover {
          transform: translateY(-2px);
        }
        
        &.el-button--primary {
          background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
          border-color: var(--el-color-primary-light-5);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
          
          &:hover {
            box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
          }
        }
      }
    }
    
    /* 导入进度提示 */
    .import-progress {
      margin-top: 16px;
      padding: 12px;
      background: rgba(var(--el-color-success-rgb), 0.05);
      border-radius: 8px;
      font-size: 14px;
      color: var(--el-color-success);
      display: flex;
      align-items: center;
      gap: 8px;
      
      .el-icon {
        font-size: 18px;
      }
    }
    
    /* 错误提示样式 */
    .import-error {
      margin-top: 16px;
      padding: 12px;
      background: rgba(var(--el-color-danger-rgb), 0.05);
      border-radius: 8px;
      font-size: 14px;
      color: var(--el-color-danger);
      display: flex;
      align-items: center;
      gap: 8px;
      
      .el-icon {
        font-size: 18px;
      }
    }
  }
  
  /* 暗色主题适配 */
  html.dark .import-dialog {
    .el-dialog__header {
      background: linear-gradient(to right, rgba(30, 35, 45, 0.8), rgba(25, 30, 40, 0.9));
    }
    
    .import-tips {
      background: rgba(255, 255, 255, 0.03);
      border-color: rgba(255, 255, 255, 0.05);
    }
    
    .file-select-area .el-upload-dragger {
      background: rgba(255, 255, 255, 0.02);
      border-color: rgba(255, 255, 255, 0.1);
      
      &:hover {
        background: rgba(255, 255, 255, 0.04);
        border-color: var(--el-color-primary);
      }
    }
  }
  
  /* 优化顶部按钮样式 */
  .app-header .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  
    .el-button {
      height: 40px;
      padding: 0 20px;
      font-size: 15px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      
      .el-icon {
        font-size: 16px;
        margin-right: 2px;
      }
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
      }
      
      /* 主要按钮样式 */
      &.el-button--primary {
        background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
        border-color: var(--el-color-primary-light-5);
        box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
        
        &:hover {
          box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
        }
      }
      
      /* 警告按钮样式 */
      &.el-button--warning {
        background: linear-gradient(120deg, var(--el-color-warning), var(--el-color-warning-light-3));
      }
      
      /* 成功按钮样式 */
      &.el-button--success {
        background: linear-gradient(120deg, var(--el-color-success), var(--el-color-success-light-3));
      }
      
      /* 信息按钮样式 */
      &.el-button--info {
        background: linear-gradient(120deg, var(--el-bg-color), var(--el-bg-color-overlay));
        border: 1px solid rgba(var(--el-border-color-rgb), 0.2);
        color: var(--el-text-color-regular);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        
        &:hover {
          border-color: var(--el-border-color);
          color: var(--el-color-primary);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
      
      /* 禁用状态 */
      &.is-disabled {
        opacity: 0.6;
        transform: none !important;
        box-shadow: none !important;
        
        &:hover {
          transform: none !important;
          box-shadow: none !important;
        }
      }
    }
  }
  
  /* 暗色主题适配 */
  html.dark .app-header .header-actions .el-button {
    &.el-button--info {
      background: rgba(255, 255, 255, 0.04);
      border-color: rgba(255, 255, 255, 0.1);
      
      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: var(--el-color-primary);
      }
    }
  }
  
  /* 优化顶部按钮样式 - 水晶透明质感 */
  .app-header .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  
    .el-button {
      height: 40px;
      padding: 0 24px;
      font-size: 15px;
      font-weight: 500;
      border-radius: 10px;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      .el-icon {
        font-size: 16px;
        margin-right: 2px;
      }
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
      }
      
      /* 主要按钮样式 */
      &.el-button--primary {
        background: linear-gradient(120deg, 
          rgba(var(--el-color-primary-rgb), 0.8),
          rgba(var(--el-color-primary-rgb), 0.6)
        );
        box-shadow: 
          0 4px 12px rgba(var(--el-color-primary-rgb), 0.3),
          inset 0 0 0 1px rgba(255, 255, 255, 0.1);
        
        &:hover {
          background: linear-gradient(120deg, 
            rgba(var(--el-color-primary-rgb), 0.9),
            rgba(var(--el-color-primary-rgb), 0.7)
          );
          box-shadow: 
            0 8px 20px rgba(var(--el-color-primary-rgb), 0.4),
            inset 0 0 0 1px rgba(255, 255, 255, 0.2);
        }
      }
      
      /* 警告按钮样式 */
      &.el-button--warning {
        background: linear-gradient(120deg, 
          rgba(var(--el-color-warning-rgb), 0.8),
          rgba(var(--el-color-warning-rgb), 0.6)
        );
        box-shadow: 
          0 4px 12px rgba(var(--el-color-warning-rgb), 0.3),
          inset 0 0 0 1px rgba(255, 255, 255, 0.1);
        
        &:hover {
          background: linear-gradient(120deg, 
            rgba(var(--el-color-warning-rgb), 0.9),
            rgba(var(--el-color-warning-rgb), 0.7)
          );
          box-shadow: 
            0 8px 20px rgba(var(--el-color-warning-rgb), 0.4),
            inset 0 0 0 1px rgba(255, 255, 255, 0.2);
        }
      }
      
      /* 成功按钮样式 */
      &.el-button--success {
        background: linear-gradient(120deg, 
          rgba(var(--el-color-success-rgb), 0.8),
          rgba(var(--el-color-success-rgb), 0.6)
        );
        box-shadow: 
          0 4px 12px rgba(var(--el-color-success-rgb), 0.3),
          inset 0 0 0 1px rgba(255, 255, 255, 0.1);
        
        &:hover {
          background: linear-gradient(120deg, 
            rgba(var(--el-color-success-rgb), 0.9),
            rgba(var(--el-color-success-rgb), 0.7)
          );
          box-shadow: 
            0 8px 20px rgba(var(--el-color-success-rgb), 0.4),
            inset 0 0 0 1px rgba(255, 255, 255, 0.2);
        }
      }
      
      /* 信息按钮样式 */
      &.el-button--info {
        background: rgba(255, 255, 255, 0.1);
        box-shadow: 
          0 4px 12px rgba(0, 0, 0, 0.05),
          inset 0 0 0 1px rgba(255, 255, 255, 0.05);
        color: var(--el-text-color-regular);
        
        &:hover {
          background: rgba(255, 255, 255, 0.15);
          color: var(--el-color-primary);
          box-shadow: 
            0 8px 20px rgba(0, 0, 0, 0.08),
            inset 0 0 0 1px rgba(255, 255, 255, 0.1);
        }
      }
      
      /* 禁用状态 */
      &.is-disabled {
        opacity: 0.5;
        transform: none !important;
        box-shadow: none !important;
        background: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.05) !important;
        
        &:hover {
          transform: none !important;
          box-shadow: none !important;
        }
      }
    }
  }
  
  /* 暗色主题适配 */
  html.dark .app-header .header-actions .el-button {
    border-color: rgba(255, 255, 255, 0.05);
    
    &.el-button--info {
      background: rgba(255, 255, 255, 0.05);
      
      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  /* 优化结果组和结果项容器的滚动 */
  .result-group {
    background: rgba(var(--el-bg-color-rgb), 0.6);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 4px 24px rgba(0, 0, 0, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.05);
    
    .result-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 8px 0;
      max-height: 200px; /* 设置最大高度 */
      overflow-y: auto; /* 允许垂直滚动 */
      
      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(var(--el-color-primary-rgb), 0.3);
        border-radius: 3px;
        
        &:hover {
          background: rgba(var(--el-color-primary-rgb), 0.5);
        }
      }
    }
  }
  
  /* 暗色主题滚动条适配 */
  html.dark .result-group .result-items {
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.4);
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.6);
      }
    }
  }
  
  /* 优化结果组标题样式 */
  .result-group-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    
    .group-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
    
    .group-count {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      background: var(--el-fill-color-light);
      padding: 2px 8px;
      border-radius: 10px;
    }
  }
  
  /* 更彻底地隐藏结果容器的滚动条 */
  .result-scroll-container,
  .result-container {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
    
    /* 彻底隐藏滚动条 - 所有浏览器 */
    &::-webkit-scrollbar,
    &::-webkit-scrollbar-track,
    &::-webkit-scrollbar-thumb {
      width: 0 !important;
      height: 0 !important;
      display: none !important;
      background: transparent !important;
    }
  }
  
  /* 确保所有子元素继承滚动条隐藏 */
  .result-scroll-container *,
  .result-container * {
    scrollbar-width: inherit;
    -ms-overflow-style: inherit;
  }
  
  /* 保留结果项 (result-items) 的垂直滚动条 */
  .result-items {
    scrollbar-width: thin !important;
    -ms-overflow-style: auto !important;
    
    &::-webkit-scrollbar {
      width: 6px !important;
      display: block !important;
    }
    
    &::-webkit-scrollbar-track {
      display: block !important;
    }
    
    &::-webkit-scrollbar-thumb {
      display: block !important;
    }
  }
  
  /* 增强滚动体验 */
  .result-items.scrolling {
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.6);
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.8);
      }
    }
  }
  
  .result-items.hover {
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.4);
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.6);
      }
    }
  }
  
  /* 暗色主题适配 */
  html.dark .result-items.scrolling {
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.7);
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.9);
      }
    }
  }
  
  html.dark .result-items.hover {
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.5);
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.7);
      }
    }
  }
  
  /* 美化自定义滚动条样式 - 添加在样式表最后以确保优先级 */
  .result-items {
    /* 强化滚动条样式 */
    scrollbar-width: thin !important;
    scrollbar-color: rgba(var(--el-color-primary-rgb), 0.3) transparent !important;
    -ms-overflow-style: auto !important;
    
    &::-webkit-scrollbar {
      width: 6px !important;
      height: 6px !important;
      background: transparent !important;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent !important;
      border-radius: 8px !important;
      margin: 2px !important;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.3) !important;
      border-radius: 8px !important;
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      background-clip: padding-box !important;
      min-height: 40px !important;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.6) !important;
        cursor: pointer !important;
      }
    }
    
    /* 确保滚动容器有鼠标手势提示 */
    cursor: default;
    
    /* 滚动状态特效 */
    &.scrolling::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.6) !important;
      box-shadow: 0 0 10px rgba(var(--el-color-primary-rgb), 0.4) !important;
    }
    
    /* 悬停状态特效 */
    &.hover::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.5) !important;
    }
    
    /* 添加渐变效果 */
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(to bottom, 
        rgba(var(--el-color-primary-rgb), 0.5), 
        rgba(var(--el-color-primary-rgb), 0.3)
      ) !important;
    }
    
    /* 滚动条轨道阴影效果 */
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.05) !important;
    }
  }
  
  /* 暗色主题适配加强版 */
  html.dark .result-items {
    scrollbar-color: rgba(var(--el-color-primary-rgb), 0.5) rgba(0, 0, 0, 0.2) !important;
    
    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.2) !important;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
    }
    
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(to bottom, 
        rgba(var(--el-color-primary-rgb), 0.7), 
        rgba(var(--el-color-primary-rgb), 0.4)
      ) !important;
      border-color: rgba(0, 0, 0, 0.4) !important;
      
      &:hover {
        background: linear-gradient(to bottom, 
          rgba(var(--el-color-primary-rgb), 0.9), 
          rgba(var(--el-color-primary-rgb), 0.6)
        ) !important;
      }
    }
    
    &.scrolling::-webkit-scrollbar-thumb {
      box-shadow: 0 0 12px rgba(var(--el-color-primary-rgb), 0.5) !important;
    }
  }
  
  /* 卡片详情对话框样式 - 全新精致设计 */
  .card-detail-dialog {
    --dialog-padding: 0;
    border-radius: 20px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .card-detail-dialog :deep(.el-dialog) {
    border-radius: 20px;
    background: linear-gradient(145deg,
      rgba(var(--el-bg-color-rgb), 0.95),
      rgba(var(--el-bg-color-overlay-rgb), 0.9)
    );
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset,
      0 8px 32px rgba(var(--el-color-primary-rgb), 0.1);
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.08);
    position: relative;
    transform: scale(1);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    animation: dialogSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  /* 对话框入场动画 */
  @keyframes dialogSlideIn {
    0% {
      opacity: 0;
      transform: scale(0.8) translateY(-50px);
      filter: blur(10px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
      filter: blur(0);
    }
  }

  /* 对话框退场动画 */
  @keyframes dialogSlideOut {
    0% {
      opacity: 1;
      transform: scale(1) translateY(0);
      filter: blur(0);
    }
    100% {
      opacity: 0;
      transform: scale(0.8) translateY(-50px);
      filter: blur(10px);
    }
  }

  /* 增强对话框样式 */
  .enhanced-dialog :deep(.el-dialog) {
    will-change: transform, opacity, filter;
    backface-visibility: hidden;
    perspective: 1000px;

    /* 防止滚动条抖动 */
    overflow: hidden;

    /* 添加微妙的边框动画 */
    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: 22px;
      background: linear-gradient(45deg,
        rgba(var(--el-color-primary-rgb), 0.3),
        transparent 30%,
        transparent 70%,
        rgba(var(--el-color-primary-rgb), 0.3)
      );
      z-index: -1;
      animation: borderRotate 6s linear infinite;
      pointer-events: none;
    }
  }

  /* 边框旋转动画 */
  @keyframes borderRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 添加鼠标悬停时的微妙缩放效果 */
  .enhanced-dialog :deep(.el-dialog):hover {
    transform: scale(1.002);
    transition: transform 0.3s ease;
  }

  /* 为对话框添加呼吸效果 */
  .enhanced-dialog :deep(.el-dialog) {
    animation: dialogSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1),
               dialogBreathe 8s ease-in-out infinite 1s;
  }

  /* 呼吸动画 */
  @keyframes dialogBreathe {
    0%, 100% {
      box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset,
        0 8px 32px rgba(var(--el-color-primary-rgb), 0.1);
    }
    50% {
      box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset,
        0 12px 40px rgba(var(--el-color-primary-rgb), 0.15);
    }
  }
  
  /* 详情头部样式 - 全新设计 */
  .detail-header {
    position: relative;
    padding: 28px 32px;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.08) 0%,
      rgba(var(--el-color-primary-rgb), 0.03) 50%,
      transparent 100%
    );
    border-bottom: 2px solid rgba(var(--el-color-primary-rgb), 0.1);
    overflow: hidden;
    user-select: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    /* 添加动态背景效果 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(var(--el-color-primary-rgb), 0.1),
        transparent
      );
      animation: shimmer 3s infinite;
      pointer-events: none;
    }
  }

  /* 闪光动画 */
  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  /* 详情标题样式 - 增强版 */
  .detail-title {
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 1;
    position: relative;
  }

  .detail-title h3 {
    margin: 0;
    font-size: 26px;
    user-select: none;
    font-weight: 700;
    background: linear-gradient(120deg,
      var(--el-color-primary),
      var(--el-color-primary-light-3),
      var(--el-color-success)
    );
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 0.8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: titleGlow 4s ease-in-out infinite alternate;
  }

  /* 标题发光动画 */
  @keyframes titleGlow {
    0% {
      filter: drop-shadow(0 0 5px rgba(var(--el-color-primary-rgb), 0.3));
    }
    100% {
      filter: drop-shadow(0 0 15px rgba(var(--el-color-primary-rgb), 0.6));
    }
  }

  .detail-category-badge {
    display: inline-block;
    padding: 8px 16px;
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.2),
      rgba(var(--el-color-primary-rgb), 0.1)
    );
    color: var(--el-color-primary);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    box-shadow:
      0 4px 12px rgba(var(--el-color-primary-rgb), 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(var(--el-color-primary-rgb), 0.3);
    user-select: none;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;

    /* 悬停效果 */
    &:hover {
      transform: translateY(-2px) scale(1.05);
      box-shadow:
        0 8px 20px rgba(var(--el-color-primary-rgb), 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    /* 内部光效 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
      );
      animation: badgeShine 2s infinite;
      pointer-events: none;
    }
  }

  /* 徽章闪光动画 */
  @keyframes badgeShine {
    0% { left: -100%; }
    100% { left: 100%; }
  }
  
  /* 详情内容容器 - 增强版 */
  .detail-content-wrapper {
    padding: 0;
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    background: linear-gradient(180deg,
      rgba(var(--el-bg-color-rgb), 0.8) 0%,
      rgba(var(--el-bg-color-rgb), 0.95) 100%
    );
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);

    /* 添加边框光效 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 16px;
      padding: 1px;
      background: linear-gradient(45deg,
        rgba(var(--el-color-primary-rgb), 0.3),
        transparent,
        rgba(var(--el-color-primary-rgb), 0.3)
      );
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      pointer-events: none;
      animation: borderGlow 3s linear infinite;
    }
  }

  /* 边框发光动画 */
  @keyframes borderGlow {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
  }

  /* 详情内容滚动区域 - 优化 */
  .detail-scrollbar {
    padding: 0;
    position: relative;
    border-radius: 16px;
  }

  /* 详情内容主体 - 增强版 */
  .detail-content {
    padding: 24px 28px;
    position: relative;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.02) 0%,
      transparent 50%,
      rgba(var(--el-color-primary-rgb), 0.02) 100%
    );
  }
  
  /* 详情部分样式 - 全新卡片设计 */
  .detail-section {
    margin-bottom: 20px;
    position: relative;
    padding: 20px 24px;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.02) 100%
    );
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    overflow: hidden;

    /* 添加微妙的动画效果 */
    animation: sectionFadeIn 0.6s ease-out;
    animation-fill-mode: both;

    /* 为每个section添加延迟动画 */
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    &:nth-child(5) { animation-delay: 0.5s; }
  }

  /* 卡片入场动画 */
  @keyframes sectionFadeIn {
    0% {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .detail-section:hover {
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.12) 0%,
      rgba(var(--el-color-primary-rgb), 0.06) 100%
    );
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.12),
      0 0 0 1px rgba(var(--el-color-primary-rgb), 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: rgba(var(--el-color-primary-rgb), 0.3);
  }

  /* 最后一个详情部分不需要底部间距 */
  .detail-section:last-child {
    margin-bottom: 0;
  }
  
  /* 详情部分标题 - 增强版 */
  .detail-section-title {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 700;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
    gap: 12px;
    user-select: none;
    position: relative;

    /* 添加标题下划线效果 */
    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 40px;
      height: 2px;
      background: linear-gradient(90deg,
        var(--el-color-primary),
        transparent
      );
      border-radius: 2px;
      animation: underlineGrow 0.8s ease-out;
    }
  }

  /* 下划线生长动画 */
  @keyframes underlineGrow {
    0% { width: 0; opacity: 0; }
    100% { width: 40px; opacity: 1; }
  }

  /* 图标容器 - 增强版 */
  .icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.15),
      rgba(var(--el-color-primary-rgb), 0.08)
    );
    border-radius: 10px;
    padding: 6px;
    box-shadow:
      0 4px 12px rgba(var(--el-color-primary-rgb), 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

    &:hover {
      transform: scale(1.1) rotate(5deg);
      box-shadow:
        0 6px 16px rgba(var(--el-color-primary-rgb), 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
  }

  /* 技术图标 - 增强版 */
  .tech-icon {
    width: 20px;
    height: 20px;
    fill: var(--el-color-primary);
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(var(--el-color-primary-rgb), 0.3));
  }
  
  /* 属性值样式 - 增强版 */
  .detail-property-value {
    font-size: 16px;
    line-height: 1.6;
    color: var(--el-text-color-primary);
    position: relative;
    padding: 8px 0;
    animation: valueSlideIn 0.5s ease-out;
  }

  /* 属性值入场动画 */
  @keyframes valueSlideIn {
    0% {
      opacity: 0;
      transform: translateX(-10px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* 文本样式 - 增强版 */
  .detail-text {
    margin: 0;
    white-space: pre-wrap;
    padding: 12px 16px;
    background: rgba(var(--el-fill-color-rgb), 0.3);
    border-radius: 12px;
    border-left: 4px solid var(--el-color-primary);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(var(--el-fill-color-rgb), 0.5);
      transform: translateX(4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  /* 数组列表样式 - 增强版 */
  .detail-array-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background: rgba(var(--el-fill-color-rgb), 0.2);
    border-radius: 12px;
    padding: 12px;
  }

  .detail-array-list li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-bottom: 1px dashed rgba(var(--el-border-color-rgb), 0.3);
    border-radius: 8px;
    transition: all 0.3s ease;
    animation: listItemSlideIn 0.4s ease-out;
    animation-fill-mode: both;

    /* 为每个列表项添加延迟动画 */
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    &:nth-child(5) { animation-delay: 0.5s; }

    &:hover {
      background: rgba(var(--el-color-primary-rgb), 0.08);
      transform: translateX(8px);
      border-color: rgba(var(--el-color-primary-rgb), 0.4);
    }
  }

  /* 列表项入场动画 */
  @keyframes listItemSlideIn {
    0% {
      opacity: 0;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .detail-array-list li:last-child {
    border-bottom: none;
  }

  .item-bullet {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: linear-gradient(135deg,
      var(--el-color-primary),
      var(--el-color-primary-light-3)
    );
    flex-shrink: 0;
    box-shadow: 0 2px 6px rgba(var(--el-color-primary-rgb), 0.3);
    animation: bulletPulse 2s infinite;
  }

  /* 子弹脉冲动画 */
  @keyframes bulletPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
  }
  
  /* 颜色值样式 - 增强版 */
  .detail-color {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    background: rgba(var(--el-fill-color-rgb), 0.3);
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(var(--el-fill-color-rgb), 0.5);
      transform: scale(1.02);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
  }

  .color-preview {
    width: 36px;
    height: 36px;
    border-radius: 12px;
    border: 3px solid rgba(255, 255, 255, 0.8);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.15),
      inset 0 0 0 1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;

    &:hover {
      transform: scale(1.15) rotate(5deg);
      box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.2),
        inset 0 0 0 1px rgba(0, 0, 0, 0.15);
    }

    /* 添加光泽效果 */
    &::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      right: 2px;
      height: 40%;
      background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.4) 0%,
        transparent 100%
      );
      border-radius: 8px 8px 20px 20px;
      pointer-events: none;
    }
  }

  .color-code {
    font-family: 'Courier New', monospace;
    font-size: 15px;
    font-weight: 600;
    background: linear-gradient(135deg,
      rgba(0, 0, 0, 0.08),
      rgba(0, 0, 0, 0.12)
    );
    color: var(--el-text-color-primary);
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid rgba(var(--el-border-color-rgb), 0.2);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.12),
        rgba(0, 0, 0, 0.16)
      );
      transform: translateY(-1px);
      box-shadow:
        inset 0 1px 2px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
  
  /* 技术装饰元素 - 完全重新设计 */
  .tech-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
    overflow: hidden;
  }
  
  /* 角落装饰 - 更精致的设计 */
  .corner-decoration {
    position: absolute;
    width: 60px;
    height: 60px;
    fill: rgba(var(--el-color-primary-rgb), 0.2);
    filter: drop-shadow(0 0 3px rgba(var(--el-color-primary-rgb), 0.3));
    transition: all 0.5s ease;
  }
  
  /* 修改SVG路径为更精致的科技风格 */
  .corner-decoration path {
    stroke: rgba(var(--el-color-primary-rgb), 0.5);
    stroke-width: 0.5;
  }
  
  .top-left {
    top: 0;
    left: 0;
  }
  
  .top-right {
    top: 0;
    right: 0;
    transform: rotate(90deg);
  }
  
  .bottom-left {
    bottom: 0;
    left: 0;
    transform: rotate(270deg);
  }
  
  .bottom-right {
    bottom: 0;
    right: 0;
    transform: rotate(180deg);
  }
  
  /* 添加背景网格效果 */
  .tech-decoration::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      linear-gradient(rgba(var(--el-color-primary-rgb), 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(var(--el-color-primary-rgb), 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
    z-index: -1;
  }
  
  /* 添加光效线条 */
  .tech-decoration::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 100px;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(var(--el-color-primary-rgb), 0.7), transparent);
    transform: rotate(-45deg);
    filter: blur(1px);
    animation: glowLine 4s infinite alternate;
  }
  
  @keyframes glowLine {
    0% {
      opacity: 0.3;
      width: 80px;
    }
    100% {
      opacity: 0.7;
      width: 120px;
    }
  }
  
  /* 暗色主题适配 */
  html.dark .corner-decoration {
    fill: rgba(var(--el-color-primary-rgb), 0.15);
    filter: drop-shadow(0 0 5px rgba(var(--el-color-primary-rgb), 0.4));
  }
  
  html.dark .corner-decoration path {
    stroke: rgba(var(--el-color-primary-rgb), 0.6);
  }
  
  html.dark .tech-decoration::before {
    background-image: 
      linear-gradient(rgba(var(--el-color-primary-rgb), 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(var(--el-color-primary-rgb), 0.08) 1px, transparent 1px);
    opacity: 0.2;
  }
  
  html.dark .tech-decoration::after {
    background: linear-gradient(to right, transparent, rgba(var(--el-color-primary-rgb), 0.8), transparent);
    filter: blur(2px);
  }
  
  /* 详情页脚 - 全新设计 */
  .detail-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 28px;
    background: linear-gradient(135deg,
      rgba(0, 0, 0, 0.03) 0%,
      rgba(var(--el-color-primary-rgb), 0.02) 100%
    );
    border-top: 2px solid rgba(var(--el-color-primary-rgb), 0.1);
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0 0 20px 20px;

    /* 添加顶部光效线条 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg,
        transparent,
        rgba(var(--el-color-primary-rgb), 0.6),
        transparent
      );
      animation: topLineFlow 3s infinite;
    }
  }

  /* 顶部线条流动动画 */
  @keyframes topLineFlow {
    0% { opacity: 0.3; }
    50% { opacity: 0.8; }
    100% { opacity: 0.3; }
  }

  /* 技术脉冲效果 - 增强版 */
  .tech-pulse {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(var(--el-color-primary-rgb), 0.3) 20%,
      var(--el-color-primary) 50%,
      rgba(var(--el-color-primary-rgb), 0.3) 80%,
      transparent 100%
    );
    opacity: 0.8;
    animation: enhancedPulse 4s infinite;
    border-radius: 0 0 20px 20px;
  }

  @keyframes enhancedPulse {
    0% {
      opacity: 0.4;
      transform: scaleX(0.6);
      filter: blur(1px);
    }
    50% {
      opacity: 0.9;
      transform: scaleX(1);
      filter: blur(0);
    }
    100% {
      opacity: 0.4;
      transform: scaleX(0.6);
      filter: blur(1px);
    }
  }
  
  /* 技术按钮 - 全新设计 */
  .tech-button {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg,
      var(--el-color-primary) 0%,
      var(--el-color-primary-light-3) 50%,
      var(--el-color-primary) 100%
    );
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
    letter-spacing: 0.8px;
    padding: 12px 24px;
    border-radius: 12px;
    box-shadow:
      0 6px 20px rgba(var(--el-color-primary-rgb), 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

    /* 添加内部发光效果 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.1) 100%
      );
      border-radius: 10px;
      pointer-events: none;
    }
  }

  .tech-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
      0 10px 30px rgba(var(--el-color-primary-rgb), 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg,
      var(--el-color-primary-light-3) 0%,
      var(--el-color-primary) 50%,
      var(--el-color-primary-light-3) 100%
    );
    border-color: rgba(255, 255, 255, 0.4);
  }

  .tech-button::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -100%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 70%
    );
    transform: rotate(45deg);
    animation: buttonShine 3s infinite;
    pointer-events: none;
  }

  @keyframes buttonShine {
    0% {
      left: -100%;
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      left: 100%;
      opacity: 0;
    }
  }
  
  /* 暗色主题适配 - 增强版 */
  html.dark .card-detail-dialog :deep(.el-dialog) {
    background: linear-gradient(145deg,
      rgba(26, 30, 33, 0.95),
      rgba(22, 26, 29, 0.9)
    );
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.6),
      0 0 0 1px rgba(255, 255, 255, 0.03) inset,
      0 8px 32px rgba(var(--el-color-primary-rgb), 0.15);
    border-color: rgba(255, 255, 255, 0.05);
  }

  html.dark .detail-header {
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.15) 0%,
      rgba(var(--el-color-primary-rgb), 0.05) 50%,
      rgba(0, 0, 0, 0.3) 100%
    );
    border-bottom-color: rgba(var(--el-color-primary-rgb), 0.2);

    &::before {
      background: linear-gradient(90deg,
        transparent,
        rgba(var(--el-color-primary-rgb), 0.2),
        transparent
      );
    }
  }

  html.dark .detail-title h3 {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    filter: drop-shadow(0 0 10px rgba(var(--el-color-primary-rgb), 0.4));
  }

  html.dark .detail-category-badge {
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.3),
      rgba(var(--el-color-primary-rgb), 0.15)
    );
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(var(--el-color-primary-rgb), 0.4);
  }

  html.dark .detail-content-wrapper {
    background: linear-gradient(180deg,
      rgba(26, 30, 33, 0.8) 0%,
      rgba(22, 26, 29, 0.95) 100%
    );

    &::before {
      background: linear-gradient(45deg,
        rgba(var(--el-color-primary-rgb), 0.4),
        transparent,
        rgba(var(--el-color-primary-rgb), 0.4)
      );
    }
  }

  html.dark .detail-section {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 100%
    );
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  html.dark .detail-section:hover {
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.15) 0%,
      rgba(var(--el-color-primary-rgb), 0.08) 100%
    );
    border-color: rgba(var(--el-color-primary-rgb), 0.4);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(var(--el-color-primary-rgb), 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  html.dark .icon-container {
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.2),
      rgba(var(--el-color-primary-rgb), 0.1)
    );
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  html.dark .detail-text {
    background: rgba(255, 255, 255, 0.03);
    border-left-color: var(--el-color-primary);

    &:hover {
      background: rgba(255, 255, 255, 0.06);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }

  html.dark .detail-array-list {
    background: rgba(255, 255, 255, 0.02);
  }

  html.dark .detail-array-list li {
    border-bottom-color: rgba(255, 255, 255, 0.08);

    &:hover {
      background: rgba(var(--el-color-primary-rgb), 0.1);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
    }
  }

  html.dark .detail-color {
    background: rgba(255, 255, 255, 0.03);

    &:hover {
      background: rgba(255, 255, 255, 0.06);
    }
  }

  html.dark .color-code {
    background: linear-gradient(135deg,
      rgba(0, 0, 0, 0.3),
      rgba(0, 0, 0, 0.4)
    );
    color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.1);

    &:hover {
      background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.4),
        rgba(0, 0, 0, 0.5)
      );
    }
  }

  html.dark .detail-footer {
    background: linear-gradient(135deg,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(var(--el-color-primary-rgb), 0.05) 100%
    );
    border-top-color: rgba(var(--el-color-primary-rgb), 0.2);
  }

  html.dark .tech-button {
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.1);
  }

  html.dark .tech-button:hover {
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
  }

  /* 暗色主题 - 增强对话框效果 */
  html.dark .enhanced-dialog :deep(.el-dialog) {
    &::after {
      background: linear-gradient(45deg,
        rgba(var(--el-color-primary-rgb), 0.4),
        transparent 30%,
        transparent 70%,
        rgba(var(--el-color-primary-rgb), 0.4)
      );
    }
  }

  /* 暗色主题呼吸动画 */
  html.dark .enhanced-dialog :deep(.el-dialog) {
    animation: dialogSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1),
               dialogBreatheDark 8s ease-in-out infinite 1s;
  }

  @keyframes dialogBreatheDark {
    0%, 100% {
      box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.6),
        0 0 0 1px rgba(255, 255, 255, 0.03) inset,
        0 8px 32px rgba(var(--el-color-primary-rgb), 0.15);
    }
    50% {
      box-shadow:
        0 35px 70px rgba(0, 0, 0, 0.7),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset,
        0 15px 45px rgba(var(--el-color-primary-rgb), 0.25);
    }
  }
  
  /* 添加以下样式到您的<style>部分 */
  
  /* 添加卡片对话框样式优化 */
  .add-card-dialog {
    .add-card-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      max-height: 60vh; /* 限制最大高度 */
    }
  
    .card-title-section {
      position: sticky;
      top: 0;
      z-index: 10;
      background: var(--el-bg-color);
      padding-bottom: 16px;
      margin-bottom: 8px;
      border-bottom: 1px solid var(--el-border-color-light);
    }
  
    .card-properties-section {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      position: sticky;
      top: 0;
      background: var(--el-bg-color);
      z-index: 5;
  
      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
  
      .section-hint {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  
    .property-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  
    .property-input-group {
      padding: 12px;
      border-radius: 8px;
      background: var(--el-fill-color-light);
      border: 1px solid var(--el-border-color-lighter);
    }
  
    .property-label {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      font-weight: 500;
    }
  
    .property-type-badge {
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 4px;
      background: var(--el-color-info-light-9);
      color: var(--el-color-info);
    }
    
    /* 修复对话框内容滚动 */
    :deep(.el-dialog__body) {
      max-height: 60vh;
      overflow-y: auto;
      padding-right: 10px; /* 为滚动条预留空间 */
    }
  }
  
  /* 确保对话框内容可以正常滚动 */
  .dialog-content-wrapper {
    width: 100%;
  }
  
  /* 优化复制按钮样式 */
  .header-actions {
    .el-button--primary.is-plain {
      color: var(--el-color-primary) !important;
      background: rgba(var(--el-color-primary-rgb), 0.05);
      border-color: rgba(var(--el-color-primary-rgb), 0.2);

      &:hover {
        background: linear-gradient(120deg,
          rgba(var(--el-color-primary-rgb), 0.1),
          rgba(var(--el-color-primary-rgb), 0.2)
        );
        border-color: var(--el-color-primary);
        color: var(--el-color-primary) !important;

        .el-icon {
          color: var(--el-color-primary);
          transform: scale(1.1);
        }
      }

      .el-icon {
        transition: transform 0.3s ease;
        color: var(--el-color-primary);
      }
    }
  }
  
  /* 暗色主题适配 */
  html.dark {
    .header-actions {
      .el-button--primary.is-plain {
        background: rgba(var(--el-color-primary-rgb), 0.1);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
        color: var(--el-color-primary) !important;

        &:hover {
          background: rgba(var(--el-color-primary-rgb), 0.2);
          border-color: rgba(var(--el-color-primary-rgb), 0.4);
          color: var(--el-color-primary) !important;
        }

        .el-icon {
          color: var(--el-color-primary);
        }
      }
    }
  }
  
  /* 自动滚动相关样式 */
  .detail-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    user-select: none; /* 添加：防止详情页脚文本选择 */
  }
  
  .detail-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .auto-scroll-switch {
    margin-right: 8px;
  }
  
  /* 滚动平滑效果 */
  .detail-scrollbar :deep(.el-scrollbar__wrap) {
    scroll-behavior: smooth;
    transition: scroll-top 0.3s ease;
  }
  
  /* 自动滚动动画指示器 */
  .detail-content-wrapper:not(:hover) .detail-scrollbar::after {
    content: '';
    position: absolute;
    bottom: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(var(--el-color-primary-rgb), 0.1);
    box-shadow: 0 0 8px rgba(var(--el-color-primary-rgb), 0.2);
    animation: pulse 2s infinite;
    z-index: 10;
    pointer-events: none;
    opacity: 0.8;
  }
  
  @keyframes pulse {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0.3);
    }
    
    70% {
      transform: scale(1);
      box-shadow: 0 0 0 8px rgba(var(--el-color-primary-rgb), 0);
    }
    
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0);
    }
  }
  
  /* 暗色主题适配 */
  html.dark .detail-content-wrapper:not(:hover) .detail-scrollbar::after {
    background: rgba(var(--el-color-primary-rgb), 0.15);
    box-shadow: 0 0 10px rgba(var(--el-color-primary-rgb), 0.3);
  }
  
  /* 滚动平滑效果增强 */
  .detail-scrollbar :deep(.el-scrollbar__wrap) {
    scroll-behavior: smooth;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    will-change: scroll-position;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  /* 自动滚动指示器动画增强 */
  .detail-content-wrapper:not(:hover) .detail-scrollbar::after {
    content: '';
    position: absolute;
    bottom: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, 
      rgba(var(--el-color-primary-rgb), 0.2), 
      rgba(var(--el-color-primary-rgb), 0.05)
    );
    box-shadow: 
      0 0 10px rgba(var(--el-color-primary-rgb), 0.3),
      inset 0 0 6px rgba(var(--el-color-primary-rgb), 0.3);
    animation: pulseAndFloat 3s infinite;
    z-index: 10;
    pointer-events: none;
    opacity: 0.8;
    backdrop-filter: blur(4px);
  }
  
  /* 更平滑的脉冲和浮动动画 */
  @keyframes pulseAndFloat {
    0% {
      transform: translateY(0) scale(0.95);
      box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0.3);
      opacity: 0.7;
    }
    
    50% {
      transform: translateY(-8px) scale(1);
      box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
      opacity: 0.9;
    }
    
    100% {
      transform: translateY(0) scale(0.95);
      box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0.3);
      opacity: 0.7;
    }
  }
  
  /* 滚动轨道增强 */
  .detail-scrollbar :deep(.el-scrollbar__wrap::-webkit-scrollbar-track) {
    background: rgba(var(--el-color-primary-rgb), 0.05);
    border-radius: 5px;
  }
  
  /* 滚动条增强 */
  .detail-scrollbar :deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb) {
    background: linear-gradient(
      to bottom,
      rgba(var(--el-color-primary-rgb), 0.6),
      rgba(var(--el-color-primary-rgb), 0.3)
    );
    border-radius: 5px;
    border: 2px solid transparent;
    background-clip: content-box;
    transition: all 0.3s ease;
  }
  
  /* 滚动条悬停效果 */
  .detail-scrollbar :deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb:hover) {
    background: linear-gradient(
      to bottom,
      rgba(var(--el-color-primary-rgb), 0.8),
      rgba(var(--el-color-primary-rgb), 0.5)
    );
  }
  
  /* 增加方向变化时的过渡效果 */
  .detail-scrollbar.changing-direction :deep(.el-scrollbar__wrap) {
    transition: all 1s cubic-bezier(0.34, 1.56, 0.64, 1);
  }
  
  /* 滚动内容渐变提示 */
  .detail-scrollbar::before,
  .detail-scrollbar::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 40px;
    pointer-events: none;
    z-index: 5;
  }
  
  /* 顶部渐变提示 - 向上滚动时 */
  .detail-scrollbar::before {
    top: 0;
    background: linear-gradient(to bottom, 
      var(--el-bg-color) 0%,
      rgba(var(--el-bg-color-rgb), 0) 100%
    );
    opacity: 0.8;
  }
  
  /* 底部渐变提示 - 向下滚动时 */
  .detail-scrollbar::after {
    bottom: 0;
    background: linear-gradient(to top, 
      var(--el-bg-color) 0%,
      rgba(var(--el-bg-color-rgb), 0) 100%
    );
    opacity: 0.8;
  }
  
  /* 暗色主题适配 */
  html.dark .detail-content-wrapper:not(:hover) .detail-scrollbar::after {
    background: linear-gradient(135deg, 
      rgba(var(--el-color-primary-rgb), 0.25), 
      rgba(var(--el-color-primary-rgb), 0.1)
    );
    box-shadow: 
      0 0 15px rgba(var(--el-color-primary-rgb), 0.4),
      inset 0 0 8px rgba(var(--el-color-primary-rgb), 0.3);
  }
  
  /* 删除或替换这个样式块（这是重复的） */
  .detail-content-wrapper:not(:hover) .detail-scrollbar::after {
    content: '';
    position: absolute;
    bottom: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(var(--el-color-primary-rgb), 0.1);
    box-shadow: 0 0 8px rgba(var(--el-color-primary-rgb), 0.2);
    animation: pulse 2s infinite;
    z-index: 10;
    pointer-events: none;
    opacity: 0.8;
  }
  
  @keyframes pulse {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0.3);
    }
    
    70% {
      transform: scale(1);
      box-shadow: 0 0 0 8px rgba(var(--el-color-primary-rgb), 0);
    }
    
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0);
    }
  }
  
  /* 重新修改这些样式，解决冲突 */
  .detail-scrollbar {
    position: relative;
  }
  
  /* 滚动指示器独立样式 */
  .detail-scrollbar.auto-scrolling::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(to bottom, 
      var(--el-bg-color) 0%,
      rgba(var(--el-bg-color-rgb), 0) 100%
    );
    opacity: 0.8;
    pointer-events: none;
    z-index: 5;
  }
  
  .detail-scrollbar.auto-scrolling::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(to top, 
      var(--el-bg-color) 0%,
      rgba(var(--el-bg-color-rgb), 0) 100%
    );
    opacity: 0.8;
    pointer-events: none;
    z-index: 5;
  }
  
  /* 自动滚动指示器 - 全新精致设计 */
  .auto-scroll-indicator {
    position: absolute;
    bottom: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.25),
      rgba(var(--el-color-primary-rgb), 0.1)
    );
    box-shadow:
      0 4px 16px rgba(var(--el-color-primary-rgb), 0.4),
      inset 0 2px 4px rgba(255, 255, 255, 0.2),
      inset 0 -2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateZ(0);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    pointer-events: none;
    z-index: 10;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 2px solid rgba(255, 255, 255, 0.15);

    /* 添加内部发光环 */
    &::before {
      content: '';
      position: absolute;
      top: 4px;
      left: 4px;
      right: 4px;
      bottom: 4px;
      border-radius: 50%;
      background: radial-gradient(circle,
        rgba(var(--el-color-primary-rgb), 0.6) 0%,
        rgba(var(--el-color-primary-rgb), 0.2) 50%,
        transparent 100%
      );
      animation: innerGlow 2s infinite alternate;
    }

    /* 添加外部光环 */
    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border-radius: 50%;
      background: radial-gradient(circle,
        transparent 60%,
        rgba(var(--el-color-primary-rgb), 0.1) 70%,
        rgba(var(--el-color-primary-rgb), 0.3) 80%,
        transparent 100%
      );
      animation: outerGlow 3s infinite;
    }
  }

  /* 内部发光动画 */
  @keyframes innerGlow {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
  }

  /* 外部光环动画 */
  @keyframes outerGlow {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  /* 自动滚动时显示指示器 */
  .detail-scrollbar.auto-scrolling:not(.user-hovering) .auto-scroll-indicator {
    opacity: 0.9;
    animation: enhancedFloatPulse 4s infinite cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 增强的浮动脉冲动画 */
  @keyframes enhancedFloatPulse {
    0%, 100% {
      transform: translateY(0) scale(0.95) rotate(0deg);
      opacity: 0.8;
    }
    25% {
      transform: translateY(-4px) scale(1) rotate(90deg);
      opacity: 0.9;
    }
    50% {
      transform: translateY(-8px) scale(1.05) rotate(180deg);
      opacity: 1;
    }
    75% {
      transform: translateY(-4px) scale(1) rotate(270deg);
      opacity: 0.9;
    }
  }
  
  /* 渐变过渡区域 */
  .detail-scrollbar.auto-scrolling::before,
  .detail-scrollbar.auto-scrolling::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 60px;
    pointer-events: none;
    z-index: 5;
    opacity: 0.95;
    transition: opacity 0.3s ease;
  }
  
  /* 顶部渐变 */
  .detail-scrollbar.auto-scrolling::before {
    top: 0;
    background: linear-gradient(to bottom, 
      var(--el-bg-color) 40%,
      rgba(var(--el-bg-color-rgb), 0) 100%
    );
  }
  
  /* 底部渐变 */
  .detail-scrollbar.auto-scrolling::after {
    bottom: 0;
    background: linear-gradient(to top, 
      var(--el-bg-color) 40%,
      rgba(var(--el-bg-color-rgb), 0) 100%
    );
  }
  
  /* 暗色主题适配 - 自动滚动指示器 */
  html.dark .auto-scroll-indicator {
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.35),
      rgba(var(--el-color-primary-rgb), 0.15)
    );
    box-shadow:
      0 6px 20px rgba(var(--el-color-primary-rgb), 0.5),
      inset 0 2px 4px rgba(255, 255, 255, 0.1),
      inset 0 -2px 4px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.08);

    &::before {
      background: radial-gradient(circle,
        rgba(var(--el-color-primary-rgb), 0.8) 0%,
        rgba(var(--el-color-primary-rgb), 0.4) 50%,
        transparent 100%
      );
    }

    &::after {
      background: radial-gradient(circle,
        transparent 60%,
        rgba(var(--el-color-primary-rgb), 0.2) 70%,
        rgba(var(--el-color-primary-rgb), 0.5) 80%,
        transparent 100%
      );
    }
  }
  
  /* 增强滚动效果的CSS样式 */
  .detail-scrollbar :deep(.el-scrollbar__wrap) {
    scroll-behavior: smooth;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    will-change: scroll-position;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  /* 滚动容器状态类 */
  .detail-scrollbar {
    &.auto-scrolling {
      &::after {
        content: '';
        position: absolute;
        bottom: 12px;
        right: 12px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--el-color-primary);
        opacity: 0.6;
        animation: pulseScroll 2s infinite;
        z-index: 10;
        pointer-events: none;
      }
    }
    
    &.changing-direction {
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          to bottom,
          rgba(var(--el-color-primary-rgb), 0.05),
          transparent 15%,
          transparent 85%,
          rgba(var(--el-color-primary-rgb), 0.05)
        );
        pointer-events: none;
        z-index: 2;
        animation: fadeInOut 0.6s ease;
      }
    }
    
    &.user-hovering {
      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 3px;
        height: 100%;
        background: linear-gradient(
          to bottom,
          transparent,
          var(--el-color-primary) 20%,
          var(--el-color-primary) 80%,
          transparent
        );
        opacity: 0.3;
        animation: fadeIn 0.3s ease;
      }
    }
  }
  
  @keyframes pulseScroll {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 0.9; transform: scale(1.5); }
  }
  
  @keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 0.5; }
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 0.3; }
  }
  
  /* 删除重复的滚动样式定义，只保留一处 */
  /* 暂时保留这些特效，但稍后可能需要进一步简化 */
  .detail-scrollbar {
    position: relative;
    
    :deep(.el-scrollbar__wrap) {
      scroll-behavior: auto; /* 改为auto，避免smooth造成的额外延迟 */
      will-change: scroll-position;
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;
    }
    
    &.changing-direction {
      :deep(.el-scrollbar__wrap) {
        scroll-behavior: smooth; /* 只在方向变化时使用平滑滚动 */
        transition: all 0.6s ease-in-out;
      }
    }
    
    &.auto-scrolling {
      &::after {
        content: '';
        position: absolute;
        bottom: 12px;
        right: 12px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: var(--el-color-primary);
        opacity: 0.6;
        animation: pulseScroll 2s infinite;
        z-index: 10;
      }
    }
  }

  /* 防止对话框滚动条抖动的全局修复 */
  .enhanced-dialog {
    /* 确保遮罩层不会导致滚动条变化 */
    :deep(.el-overlay) {
      overflow: hidden;
    }

    /* 对话框容器样式优化 */
    :deep(.el-overlay-dialog) {
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }

    /* 对话框主体样式 */
    :deep(.el-dialog) {
      margin: 0 !important;
      max-height: 90vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    /* 对话框头部 */
    :deep(.el-dialog__header) {
      flex-shrink: 0;
      padding: 20px 20px 10px 20px;
    }

    /* 对话框主体内容 */
    :deep(.el-dialog__body) {
      flex: 1;
      overflow-y: auto;
      padding: 10px 20px 20px 20px;

      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(var(--el-color-primary-rgb), 0.3);
        border-radius: 3px;

        &:hover {
          background: rgba(var(--el-color-primary-rgb), 0.5);
        }
      }
    }

    /* 对话框底部 */
    :deep(.el-dialog__footer) {
      flex-shrink: 0;
      padding: 10px 20px 20px 20px;
    }
  }

  /* 暗色主题滚动条 */
  html.dark .enhanced-dialog :deep(.el-dialog__body) {
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(var(--el-color-primary-rgb), 0.4);

      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.6);
      }
    }
  }

  /* 防止页面滚动条在对话框打开时抖动 */
  body.el-popup-parent--hidden {
    padding-right: 0 !important;
  }