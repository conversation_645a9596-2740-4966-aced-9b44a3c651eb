from backend.bridge.Base import ResponsePacket
import edge_tts
import asyncio
import os
import json
import base64
import webview
import time


class EdgeTtsController(ResponsePacket):
    def __init__(self, config_file):
        self.config_file = config_file
        # 默认配置
        self.default_config = {
            "voice": "zh-CN-XiaoxiaoNeural",
            "rate": "+0%",      # 语速，范围：-100%到+100%
            "volume": "+0%",    # 音量，范围：-100%到+100%
            "pitch": "+0Hz"     # 音调，范围：-100Hz到+100Hz
        }
        self.current_stream = None
        self.current_task = None
        self.is_speaking = False
        self._load_config()

    def _load_config(self):
        """加载TTS配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    tts_config = config.get('tts', {})
                    # 更新默认配置
                    self.default_config.update(tts_config)
        except Exception as e:
            print(f"加载TTS配置失败: {str(e)}")

    def _save_config(self):
        """保存TTS配置"""
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 确保配置中有tts字段
            if 'tts' not in config:
                config['tts'] = {}

            # 更新tts配置
            config['tts'] = self.default_config

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            print(f"保存TTS配置失败: {str(e)}")
            return False

    def get_config(self):
        """获取当前TTS配置"""
        try:
            # 返回不带单位的数值
            config = {
                'voice': self.default_config['voice'],
                'rate': self._parse_numeric_value(self.default_config['rate']),
                'volume': self._parse_numeric_value(self.default_config['volume']),
                'pitch': self._parse_numeric_value(self.default_config['pitch'])
            }
            return self._success_response("获取TTS配置成功", config)
        except Exception as e:
            return self._error_response(f"获取TTS配置失败: {str(e)}")

    def _parse_numeric_value(self, value):
        """从带单位的字符串中提取数值"""
        try:
            # 移除所有非数字字符（保留负号）
            value = value.replace('+', '')
            return int(''.join(c for c in value if c.isdigit() or c == '-'))
        except:
            return 0

    def update_config(self, config):
        """
        更新TTS配置
        :param config: 包含配置的字典，可以包含以下键：
            - voice: 语音名称
            - rate: 语速
            - volume: 音量
            - pitch: 音调
        """
        try:
            # 验证并格式化配置
            if 'voice' in config:
                self.default_config['voice'] = config['voice']

            if 'rate' in config:
                rate = config['rate']
                if not isinstance(rate, (int, float)) or not -100 <= rate <= 100:
                    return self._error_response("语速范围必须在-100到100之间")
                self.default_config['rate'] = f"{'+' if rate >= 0 else ''}{rate}%"

            if 'volume' in config:
                volume = config['volume']
                if not isinstance(volume, (int, float)) or not -100 <= volume <= 100:
                    return self._error_response("音量范围必须在-100到100之间")
                self.default_config['volume'] = f"{'+' if volume >= 0 else ''}{volume}%"

            if 'pitch' in config:
                pitch = config['pitch']
                if not isinstance(pitch, (int, float)) or not -100 <= pitch <= 100:
                    return self._error_response("音调范围必须在-100到100之间")
                self.default_config['pitch'] = f"{'+' if pitch >= 0 else ''}{pitch}Hz"

            # 保存配置
            if self._save_config():
                return self._success_response("TTS配置更新成功", self.default_config)
            return self._error_response("TTS配置保存失败")

        except Exception as e:
            return self._error_response(f"更新TTS配置失败: {str(e)}")

    async def get_voices(self):
        """获取所有可用的语音列表"""
        try:
            voices = await edge_tts.list_voices()
            return self._success_response("成功获取所有声音列表", voices)
        except Exception as e:
            return self._error_response(f"获取语音列表失败: {str(e)}")

    def get_voices_sync(self):
        """同步版本的获取语音列表"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(self.get_voices())
            return result
        except Exception as e:
            error_msg = f"获取语音列表失败: {str(e)}"
            print(error_msg)
            # 提供更详细的错误信息
            if "Connection" in str(e) or "timeout" in str(e).lower():
                return self._error_response(f"网络连接问题，无法获取语音列表。请检查您的网络连接并重试。")
            return self._error_response(error_msg)
        finally:
            try:
                # 确保资源被正确释放
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    if not task.done():
                        task.cancel()
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                loop.run_until_complete(loop.shutdown_asyncgens())
                loop.stop()
                loop.close()
            except Exception as e:
                print(f"关闭事件循环失败: {str(e)}")

    def format_param(self, value, unit='%', default='+0%'):
        """格式化参数，确保正确的格式"""
        try:
            if value is None:
                return default

            # 如果是字符串，先移除单位并转换为数字
            if isinstance(value, str):
                value = int(''.join(c for c in value if c.isdigit() or c == '-'))

            # 确保是数字并在范围内
            value = int(value)
            if not -100 <= value <= 100:
                return default

            # 添加正号和单位
            return f"{'+' if value >= 0 else ''}{value}{unit}"
        except:
            return default

    async def stream_text_to_speech(self, text, voice=None, rate=None, volume=None, pitch=None):
        """
        流式转换文本到语音
        :param text: 要转换的文本
        :param voice: 语音名称
        :param rate: 语速 (格式: "+N%" 或 "-N%")
        :param volume: 音量 (格式: "+N%" 或 "-N%")
        :param pitch: 音调 (格式: "+NHz" 或 "-NHz")
        """
        try:
            if not text:
                yield self._error_response("文本不能为空")
                return

            # 使用默认配置或传入的参数
            voice = voice or self.default_config['voice']
            rate = rate or '+0%'
            volume = volume or '+0%'
            pitch = pitch or '+0Hz'

            print(f"TTS参数: voice={voice}, rate={rate}, volume={volume}, pitch={pitch}")

            communicate = edge_tts.Communicate(
                text,
                voice=voice,
                rate=rate,
                volume=volume,
                pitch=pitch
            )

            yield self._success_response("开始TTS流式转换")

        except Exception as e:
            error_msg = f"TTS流式转换失败: {str(e)}"
            print(error_msg)
            yield self._error_response(error_msg)

    async def stream_speak(self, text, voice=None, rate=None, volume=None, pitch=None):
        """
        流式文本转语音
        :param text: 要转换的文本
        :param voice: 语音名称
        :param rate: 语速 (格式: "+N%" 或 "-N%")
        :param volume: 音量 (格式: "+N%" 或 "-N%")
        :param pitch: 音调 (格式: "+NHz" 或 "-NHz")
        """
        try:
            # 如果已经在播放，先停止
            if self.is_speaking:
                await self.stop_stream()

            if not text:
                yield self._error_response("文本不能为空")
                return

            # 使用默认配置或传入的参数
            voice = voice or self.default_config['voice']
            rate = rate or self.default_config['rate']
            volume = volume or self.default_config['volume']
            pitch = pitch or self.default_config['pitch']

            print(f"TTS参数: voice={voice}, rate={rate}, volume={volume}, pitch={pitch}")

            # 创建通信对象
            communicate = edge_tts.Communicate(
                text,
                voice=voice,
                rate=rate,
                volume=volume,
                pitch=pitch
            )

            # 保存当前流和状态
            self.current_stream = communicate
            self.is_speaking = True

            async for chunk in communicate.stream():
                if not self.is_speaking:  # 如果已停止
                    break

                if chunk["type"] == "audio":
                    # 将音频数据转换为base64
                    audio_data = base64.b64encode(chunk["data"]).decode('utf-8')
                    yield self._success_response("音频数据", {
                        "type": "audio",
                        "data": audio_data
                    })
                elif chunk["type"] == "WordBoundary":
                    yield self._success_response("单词边界", {
                        "type": "boundary",
                        "data": {
                            "text": chunk["text"],
                            "offset": chunk["offset"],
                            "duration": chunk["duration"]
                        }
                    })

            # 播放完成
            yield self._success_response("播放完成")

        except Exception as e:
            error_msg = f"流式语音转换失败: {str(e)}"
            print(error_msg)
            yield self._error_response(error_msg)
        finally:
            self.current_stream = None
            self.is_speaking = False

    def stream_speak_sync(self, text, voice=None, rate=None, volume=None, pitch=None):
        """
        同步版本的流式文本转语音
        """
        loop = None
        try:
            # 如果已经在播放，先停止
            if self.is_speaking:
                self.stop_stream_sync()
                # 添加小延迟确保资源被正确释放
                time.sleep(0.3)  # 导入time模块

            if not text:
                return self._error_response("文本不能为空")

            # 使用默认配置或传入的参数
            voice = voice or self.default_config['voice']
            rate = rate or self.default_config['rate']
            volume = volume or self.default_config['volume']
            pitch = pitch or self.default_config['pitch']

            print(f"TTS参数: voice={voice}, rate={rate}, volume={volume}, pitch={pitch}")

            # 创建通信对象
            communicate = edge_tts.Communicate(
                text,
                voice=voice,
                rate=rate,
                volume=volume,
                pitch=pitch
            )

            # 保存当前流和状态
            self.current_stream = communicate
            self.is_speaking = True
            audio_data = bytearray()

            async def run_stream():
                session = None
                try:
                    # Store the session reference as soon as it's created
                    if hasattr(communicate, '_session'):
                        session = communicate._session
                    
                    try:
                        async for chunk in communicate.stream():
                            if not self.is_speaking:
                                break

                            if chunk["type"] == "audio":
                                nonlocal audio_data
                                audio_data.extend(chunk["data"])
                                if len(audio_data) >= 32768:  # 32KB
                                    base64_chunk = base64.b64encode(bytes(audio_data)).decode('utf-8')
                                    webview.windows[0].evaluate_js(
                                        f'window.ttsAudioChunkCallback("{base64_chunk}")'
                                    )
                                    audio_data.clear()
                    except asyncio.CancelledError:
                        # 正常取消，不需要做额外处理
                        print("TTS stream task was cancelled - this is normal during shutdown")
                        return
                    except Exception as stream_error:
                        error_msg = f"Stream processing error: {str(stream_error)}"
                        print(error_msg)
                        try:
                            # Only try to call JS if the window still exists
                            if webview.windows and len(webview.windows) > 0:
                                webview.windows[0].evaluate_js(f'window.ttsAudioErrorCallback("{error_msg}")')
                        except:
                            pass
                        raise

                    # 发送剩余的数据
                    if audio_data and self.is_speaking:
                        base64_chunk = base64.b64encode(bytes(audio_data)).decode('utf-8')
                        webview.windows[0].evaluate_js(
                            f'window.ttsAudioChunkCallback("{base64_chunk}")'
                        )

                    if self.is_speaking:
                        webview.windows[0].evaluate_js('window.ttsAudioCompleteCallback()')

                except asyncio.CancelledError:
                    # 任务被取消的情况
                    print("TTS task was cancelled during cleanup")
                except Exception as e:
                    error_msg = f"Stream processing error: {str(e)}"
                    print(error_msg)
                    try:
                        # Only try to call JS if the window still exists
                        if webview.windows and len(webview.windows) > 0:
                            webview.windows[0].evaluate_js(f'window.ttsAudioErrorCallback("{error_msg}")')
                    except:
                        pass
                    raise
                finally:
                    # Ensure session is properly closed
                    try:
                        if session:
                            await session.close()
                        elif hasattr(communicate, '_session') and communicate._session:
                            await communicate._session.close()
                    except Exception as e:
                        print(f"Error closing session: {str(e)}")
                    
                    self.current_stream = None
                    self.is_speaking = False

            # 使用新的事件循环来运行异步代码
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 创建任务并保存引用
            self.current_task = loop.create_task(run_stream())
            
            try:
                loop.run_until_complete(self.current_task)
                return self._success_response("TTS流式转换完成")
            except asyncio.CancelledError:
                print("TTS stream was cancelled - this is normal during shutdown")
                return self._success_response("TTS流已取消")
            except Exception as e:
                error_msg = f"TTS流式转换失败: {str(e)}"
                print(error_msg)
                try:
                    if webview.windows and len(webview.windows) > 0:
                        webview.windows[0].evaluate_js(f'window.ttsAudioErrorCallback("{error_msg}")')
                except:
                    pass
                return self._error_response(error_msg)

        except Exception as e:
            error_msg = f"TTS初始化失败: {str(e)}"
            print(error_msg)
            return self._error_response(error_msg)

        finally:
            # 清理资源
            self.current_stream = None
            self.current_task = None
            self.is_speaking = False
            
            # 关闭事件循环
            if loop is not None:
                try:
                    # Close the loop only if it's not already closed
                    if not loop.is_closed():
                        # 强制结束所有pending的任务
                        try:
                            pending = asyncio.all_tasks(loop)
                            if pending:
                                for task in pending:
                                    if not task.done():
                                        task.cancel()
                                # 使用gather收集所有task的结果，但捕获所有异常
                                if loop.is_running():
                                    asyncio.ensure_future(
                                        asyncio.gather(*pending, return_exceptions=True), 
                                        loop=loop
                                    )
                                else:
                                    loop.run_until_complete(
                                        asyncio.gather(*pending, return_exceptions=True)
                                    )
                        except Exception as e:
                            print(f"取消pending tasks失败: {str(e)}")
                        
                        # 安全关闭loop
                        try:
                            if loop.is_running():
                                loop.call_soon_threadsafe(loop.stop)
                            else:
                                # Only run shutdown if loop is not running
                                loop.run_until_complete(loop.shutdown_asyncgens())
                                loop.close()
                        except Exception as e:
                            print(f"关闭事件循环失败: {str(e)}")
                except Exception as e:
                    print(f"清理事件循环资源失败: {str(e)}")

    def stop_stream(self):
        """停止当前的语音流"""
        try:
            # 先设置停止标志
            self.is_speaking = False

            # 清理资源
            if self.current_stream:
                # 创建一个新的事件循环来处理清理操作
                loop = None
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    async def cleanup():
                        try:
                            # 先关闭会话，这样会解决大部分WebSocket相关的问题
                            if hasattr(self.current_stream, '_session') and self.current_stream._session:
                                try:
                                    await self.current_stream._session.close()
                                    print("WebSocket session closed successfully")
                                except Exception as e:
                                    print(f"Error closing session: {str(e)}")
                                    
                            # 再取消任务
                            if self.current_task and not self.current_task.done():
                                try:
                                    self.current_task.cancel()
                                    # 给一点时间让任务清理自己
                                    await asyncio.sleep(0.1)
                                    print("TTS task cancelled successfully")
                                except Exception as cancel_error:
                                    print(f"Error cancelling task: {str(cancel_error)}")
                        except Exception as e:
                            print(f"清理任务时出错: {str(e)}")

                    # 根据loop状态选择运行方式
                    if loop.is_running():
                        # If loop is already running, we can't run_until_complete
                        asyncio.ensure_future(cleanup(), loop=loop)
                    else:
                        # 使用超时处理，确保cleanup不会卡住
                        try:
                            loop.run_until_complete(asyncio.wait_for(cleanup(), timeout=2.0))
                        except asyncio.TimeoutError:
                            print("清理操作超时，强制继续")
                        except Exception as timeout_error:
                            print(f"清理操作异常: {str(timeout_error)}")
                    
                except Exception as e:
                    print(f"运行清理任务时出错: {str(e)}")
                finally:
                    if loop:
                        try:
                            if not loop.is_closed():
                                # Safely close the event loop
                                if loop.is_running():
                                    loop.call_soon_threadsafe(loop.stop)
                                else:
                                    loop.close()
                        except Exception as e:
                            print(f"关闭事件循环失败: {str(e)}")

            # 清理资源
            self.current_stream = None
            self.current_task = None

            return self._success_response("语音流已停止")
        except Exception as e:
            error_msg = f"停止语音流失败: {str(e)}"
            print(error_msg)
            return self._error_response(error_msg)

    def stop_stream_sync(self):
        """同步版本的停止语音流"""
        try:
            result = self.stop_stream()
            # 添加小延迟确保资源被正确释放
            time.sleep(0.2)
            return result
        except Exception as e:
            print(f"同步停止TTS流失败: {str(e)}")
            # 确保即使出错也清理资源
            self.current_stream = None
            self.current_task = None
            self.is_speaking = False
            return self._error_response(f"停止TTS流失败: {str(e)}")

