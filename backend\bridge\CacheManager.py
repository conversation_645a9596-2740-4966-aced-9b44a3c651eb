# coding: utf-8
import time
import threading
import weakref
from typing import Dict, Any, Optional, Callable
from collections import defaultdict
import json
import os


class CacheEntry:
    """缓存条目"""
    def __init__(self, value: Any, ttl: float = 300):  # 默认5分钟过期
        self.value = value
        self.created_at = time.time()
        self.ttl = ttl
        self.access_count = 0
        self.last_access = time.time()
    
    def is_expired(self) -> bool:
        return time.time() - self.created_at > self.ttl
    
    def access(self) -> Any:
        self.access_count += 1
        self.last_access = time.time()
        return self.value


class CacheManager:
    """智能缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        self._file_watchers: Dict[str, float] = {}  # 文件路径 -> 最后修改时间
        self._dependencies: Dict[str, set] = defaultdict(set)  # 缓存键 -> 依赖的文件集合
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_thread.start()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return None
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                return None
            
            # 检查文件依赖是否有变化
            if self._check_file_dependencies(key):
                del self._cache[key]
                return None
            
            return entry.access()
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None, 
            file_dependencies: Optional[list] = None) -> None:
        """设置缓存值"""
        with self._lock:
            # 如果缓存已满，清理最少使用的条目
            if len(self._cache) >= self.max_size:
                self._evict_lru()
            
            ttl = ttl or self.default_ttl
            entry = CacheEntry(value, ttl)
            self._cache[key] = entry
            
            # 记录文件依赖
            if file_dependencies:
                self._dependencies[key] = set(file_dependencies)
                for file_path in file_dependencies:
                    if os.path.exists(file_path):
                        self._file_watchers[file_path] = os.path.getmtime(file_path)
    
    def delete(self, key: str) -> None:
        """删除缓存"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
            if key in self._dependencies:
                del self._dependencies[key]
    
    def invalidate_by_pattern(self, pattern: str) -> None:
        """根据模式删除缓存"""
        with self._lock:
            keys_to_delete = [key for key in self._cache.keys() if pattern in key]
            for key in keys_to_delete:
                self.delete(key)
    
    def invalidate_by_file(self, file_path: str) -> None:
        """根据文件路径删除相关缓存"""
        with self._lock:
            keys_to_delete = []
            for key, deps in self._dependencies.items():
                if file_path in deps:
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                self.delete(key)
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
            self._dependencies.clear()
            self._file_watchers.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_entries = len(self._cache)
            expired_entries = sum(1 for entry in self._cache.values() if entry.is_expired())
            
            return {
                'total_entries': total_entries,
                'expired_entries': expired_entries,
                'active_entries': total_entries - expired_entries,
                'max_size': self.max_size,
                'file_watchers': len(self._file_watchers)
            }
    
    def _check_file_dependencies(self, key: str) -> bool:
        """检查文件依赖是否有变化"""
        if key not in self._dependencies:
            return False
        
        for file_path in self._dependencies[key]:
            if not os.path.exists(file_path):
                return True  # 文件被删除
            
            current_mtime = os.path.getmtime(file_path)
            cached_mtime = self._file_watchers.get(file_path, 0)
            
            if current_mtime > cached_mtime:
                self._file_watchers[file_path] = current_mtime
                return True  # 文件被修改
        
        return False
    
    def _evict_lru(self) -> None:
        """清理最少使用的缓存条目"""
        if not self._cache:
            return
        
        # 找到最少使用的条目
        lru_key = min(self._cache.keys(), 
                     key=lambda k: (self._cache[k].access_count, self._cache[k].last_access))
        self.delete(lru_key)
    
    def _cleanup_loop(self) -> None:
        """定期清理过期缓存"""
        while True:
            try:
                time.sleep(60)  # 每分钟清理一次
                with self._lock:
                    expired_keys = [key for key, entry in self._cache.items() 
                                  if entry.is_expired()]
                    for key in expired_keys:
                        self.delete(key)
            except Exception as e:
                print(f"缓存清理出错: {e}")


# 全局缓存实例
cache_manager = CacheManager()


def cached(ttl: float = 300, key_func: Optional[Callable] = None, 
          file_dependencies: Optional[Callable] = None):
    """缓存装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 获取文件依赖
            deps = None
            if file_dependencies:
                deps = file_dependencies(*args, **kwargs)
            
            # 存入缓存
            cache_manager.set(cache_key, result, ttl, deps)
            return result
        
        return wrapper
    return decorator
