/**
 * 弹窗调试助手
 * 用于帮助排查和修复弹窗问题
 */

// 记录弹窗操作历史
const dialogHistory = [];

// 监控弹窗打开和关闭
export const monitorDialogs = () => {
  if (window.__dialogMonitorInstalled) return;
  
  try {
    // 保存原始方法
    const originalOpen = Element.prototype.appendChild;
    const originalRemove = Element.prototype.removeChild;
    
    // 重写appendChild方法来捕获弹窗添加
    Element.prototype.appendChild = function(...args) {
      const result = originalOpen.apply(this, args);
      
      try {
        // 检测是否为弹窗元素
        if (args[0] && (
          args[0].className?.includes('el-dialog') ||
          args[0].className?.includes('el-message-box') ||
          args[0].className?.includes('el-popover')
        )) {
          dialogHistory.push({
            type: 'open',
            element: args[0],
            timestamp: new Date().toISOString()
          });
          console.log('[弹窗监控] 弹窗已打开:', args[0]);
        }
      } catch(e) {}
      
      return result;
    };
    
    // 重写removeChild方法来捕获弹窗移除
    Element.prototype.removeChild = function(...args) {
      try {
        // 检测是否为弹窗元素
        if (args[0] && (
          args[0].className?.includes('el-dialog') ||
          args[0].className?.includes('el-message-box') ||
          args[0].className?.includes('el-popover')
        )) {
          dialogHistory.push({
            type: 'close',
            element: args[0],
            timestamp: new Date().toISOString()
          });
          console.log('[弹窗监控] 弹窗已关闭:', args[0]);
        }
      } catch(e) {}
      
      return originalRemove.apply(this, args);
    };
    
    window.__dialogMonitorInstalled = true;
    console.log('[弹窗监控] 监控已启动');
  } catch (e) {
    console.error('[弹窗监控] 安装失败:', e);
  }
};

// 强制显示所有弹窗
export const forceShowAllDialogs = () => {
  try {
    // 查找所有隐藏的弹窗
    const hiddenDialogs = document.querySelectorAll('.el-dialog[style*="display: none"]');
    hiddenDialogs.forEach(dialog => {
      dialog.style.display = 'block';
      console.log('[弹窗修复] 强制显示弹窗:', dialog);
    });
    
    // 查找所有隐藏的消息框
    const hiddenMessageBoxes = document.querySelectorAll('.el-message-box[style*="display: none"]');
    hiddenMessageBoxes.forEach(box => {
      box.style.display = 'block';
      console.log('[弹窗修复] 强制显示消息框:', box);
    });
    
    return {
      unhiddenDialogs: hiddenDialogs.length,
      unhiddenMessageBoxes: hiddenMessageBoxes.length
    };
  } catch (e) {
    console.error('[弹窗修复] 强制显示失败:', e);
    return { error: e.message };
  }
};

// 获取弹窗历史记录
export const getDialogHistory = () => {
  return dialogHistory;
};

// 在弹窗问题时调用的紧急修复函数
export const emergencyDialogFix = () => {
  try {
    // 1. 移除所有遮罩层
    const masks = document.querySelectorAll('.el-overlay');
    masks.forEach(mask => mask.remove());
    
    // 2. 重置body样式
    document.body.style.overflow = '';
    document.body.style.position = '';
    document.body.style.width = '';
    document.body.classList.remove('el-popup-parent--hidden');
    
    // 3. 显示所有隐藏的弹窗
    forceShowAllDialogs();
    
    return { success: true };
  } catch (e) {
    console.error('[紧急修复] 失败:', e);
    return { error: e.message };
  }
};

// 导出调试对象，便于控制台调用
window.__dialogDebug = {
  monitorDialogs,
  forceShowAllDialogs,
  getDialogHistory,
  emergencyDialogFix
};

// 自动启动监控
monitorDialogs();

export default {
  monitorDialogs,
  forceShowAllDialogs,
  getDialogHistory,
  emergencyDialogFix
}; 