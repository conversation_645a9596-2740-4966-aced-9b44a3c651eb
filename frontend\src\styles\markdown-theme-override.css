/* Markdown 主题强制覆盖样式 */
/* 这个文件专门用于覆盖第三方 markdown 组件的样式，确保主题适配生效 */

/* 编辑区域样式重置 - 确保编辑区域不受表格样式影响 */
.v-md-editor__main-left,
.v-md-editor__main-left .CodeMirror,
.v-md-editor__main-left .CodeMirror-lines,
.v-md-editor__main-left .CodeMirror-line {
  line-height: 1.5 !important;
  border: none !important;
  border-collapse: initial !important;
  border-spacing: initial !important;
  background: transparent !important;
  padding: initial !important;
}

.v-md-editor__main-left .CodeMirror-line span {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

/* 全局强制覆盖 - 最高优先级，只针对预览区域 */
html.dark .v-md-preview__content code,
html.dark .v-md-editor-preview code,
html.dark .vuepress-markdown-body code,
html.dark .v-md-editor__main-right code,
html.dark .preview-mode code {
  background-color: var(--el-fill-color-dark) !important;
  color: var(--el-color-danger-light-3) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
  border: none !important;
}

html.dark .v-md-preview__content pre,
html.dark .v-md-editor-preview pre,
html.dark .vuepress-markdown-body pre,
html.dark .v-md-editor__main-right pre,
html.dark .preview-mode pre {
  background-color: var(--el-fill-color-dark) !important;
  color: var(--el-text-color-primary) !important;
  border: 1px solid var(--el-border-color-light) !important;
  border-radius: 6px !important;
  padding: 16px !important;
  overflow-x: auto !important;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
  line-height: 1.45 !important;
}

html.dark .v-md-preview__content pre code,
html.dark .v-md-editor-preview pre code,
html.dark .vuepress-markdown-body pre code,
html.dark .v-md-editor__main-right pre code,
html.dark .preview-mode pre code {
  background-color: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
  border: none !important;
}

html.dark .v-md-preview__content table,
html.dark .v-md-editor-preview table,
html.dark .vuepress-markdown-body table,
html.dark .v-md-editor__main-right table,
html.dark .preview-mode table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  background: var(--el-bg-color) !important;
  background-color: var(--el-bg-color) !important;
  border: 1px solid var(--el-border-color-light) !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

html.dark .v-md-preview__content table th,
html.dark .v-md-editor-preview table th,
html.dark .vuepress-markdown-body table th,
html.dark .v-md-editor__main-right table th,
html.dark .preview-mode table th {
  background: var(--el-fill-color-dark) !important;
  background-color: var(--el-fill-color-dark) !important;
  color: var(--el-text-color-primary) !important;
  border: 1px solid var(--el-border-color-light) !important;
  padding: 8px 12px !important;
  font-weight: 600 !important;
}

html.dark .v-md-preview__content table td,
html.dark .v-md-editor-preview table td,
html.dark .vuepress-markdown-body table td,
html.dark .v-md-editor__main-right table td,
html.dark .preview-mode table td {
  background: var(--el-bg-color) !important;
  background-color: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border: 1px solid var(--el-border-color-light) !important;
  padding: 8px 12px !important;
}

html.dark .v-md-preview__content table tr:nth-child(even),
html.dark .v-md-editor-preview table tr:nth-child(even),
html.dark .vuepress-markdown-body table tr:nth-child(even),
html.dark .v-md-editor__main-right table tr:nth-child(even),
html.dark .preview-mode table tr:nth-child(even) {
  background: var(--el-fill-color-darker) !important;
  background-color: var(--el-fill-color-darker) !important;
}

html.dark .v-md-preview__content table tr:nth-child(even) td,
html.dark .v-md-editor-preview table tr:nth-child(even) td,
html.dark .vuepress-markdown-body table tr:nth-child(even) td,
html.dark .v-md-editor__main-right table tr:nth-child(even) td,
html.dark .preview-mode table tr:nth-child(even) td {
  background: var(--el-fill-color-darker) !important;
  background-color: var(--el-fill-color-darker) !important;
}

html.dark .v-md-preview__content table tr:hover,
html.dark .v-md-editor-preview table tr:hover,
html.dark .vuepress-markdown-body table tr:hover,
html.dark .v-md-editor__main-right table tr:hover,
html.dark .preview-mode table tr:hover {
  background: var(--el-fill-color-dark) !important;
  background-color: var(--el-fill-color-dark) !important;
}

html.dark .v-md-preview__content table tr:hover td,
html.dark .v-md-editor-preview table tr:hover td,
html.dark .vuepress-markdown-body table tr:hover td,
html.dark .v-md-editor__main-right table tr:hover td,
html.dark .preview-mode table tr:hover td {
  background: var(--el-fill-color-dark) !important;
  background-color: var(--el-fill-color-dark) !important;
}

/* 浅色主题强制覆盖 */
html.light .v-md-preview__content code,
html.light .v-md-editor-preview code,
html.light .vuepress-markdown-body code,
html.light .v-md-editor__main-right code,
html:not(.dark) .v-md-preview__content code,
html:not(.dark) .v-md-editor-preview code,
html:not(.dark) .vuepress-markdown-body code,
html:not(.dark) .v-md-editor__main-right code,
html:not(.dark) .preview-mode code {
  background-color: #f6f8fa !important;
  color: #d73a49 !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
  border: none !important;
}

html.light .v-md-preview__content pre,
html.light .v-md-editor-preview pre,
html.light .vuepress-markdown-body pre,
html.light .v-md-editor__main-right pre,
html:not(.dark) .v-md-preview__content pre,
html:not(.dark) .v-md-editor-preview pre,
html:not(.dark) .vuepress-markdown-body pre,
html:not(.dark) .v-md-editor__main-right pre,
html:not(.dark) .preview-mode pre {
  background-color: #f6f8fa !important;
  color: #24292e !important;
  border: 1px solid #e1e4e8 !important;
  border-radius: 6px !important;
  padding: 16px !important;
  overflow-x: auto !important;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
  line-height: 1.45 !important;
}

html.light .v-md-preview__content pre code,
html.light .v-md-editor-preview pre code,
html.light .vuepress-markdown-body pre code,
html.light .v-md-editor__main-right pre code,
html:not(.dark) .v-md-preview__content pre code,
html:not(.dark) .v-md-editor-preview pre code,
html:not(.dark) .vuepress-markdown-body pre code,
html:not(.dark) .v-md-editor__main-right pre code,
html:not(.dark) .preview-mode pre code {
  background-color: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
  border: none !important;
}

html.light .v-md-preview__content table,
html.light .v-md-editor-preview table,
html.light .vuepress-markdown-body table,
html.light .v-md-editor__main-right table,
html:not(.dark) .v-md-preview__content table,
html:not(.dark) .v-md-editor-preview table,
html:not(.dark) .vuepress-markdown-body table,
html:not(.dark) .v-md-editor__main-right table,
html:not(.dark) .preview-mode table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  background-color: var(--el-bg-color) !important;
  border: 1px solid #e1e4e8 !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

html.light .v-md-preview__content table th,
html.light .v-md-editor-preview table th,
html.light .vuepress-markdown-body table th,
html.light .v-md-editor__main-right table th,
html:not(.dark) .v-md-preview__content table th,
html:not(.dark) .v-md-editor-preview table th,
html:not(.dark) .vuepress-markdown-body table th,
html:not(.dark) .v-md-editor__main-right table th,
html:not(.dark) .preview-mode table th {
  background-color: #f6f8fa !important;
  color: #24292e !important;
  border: 1px solid #e1e4e8 !important;
  padding: 8px 12px !important;
  font-weight: 600 !important;
}

html.light .v-md-preview__content table td,
html.light .v-md-editor-preview table td,
html.light .vuepress-markdown-body table td,
html.light .v-md-editor__main-right table td,
html:not(.dark) .v-md-preview__content table td,
html:not(.dark) .v-md-editor-preview table td,
html:not(.dark) .vuepress-markdown-body table td,
html:not(.dark) .v-md-editor__main-right table td,
html:not(.dark) .preview-mode table td {
  border: 1px solid #e1e4e8 !important;
  color: #24292e !important;
  padding: 8px 12px !important;
  background-color: transparent !important;
}

html.light .v-md-preview__content table tr:nth-child(even),
html.light .v-md-editor-preview table tr:nth-child(even),
html.light .vuepress-markdown-body table tr:nth-child(even),
html.light .v-md-editor__main-right table tr:nth-child(even),
html:not(.dark) .v-md-preview__content table tr:nth-child(even),
html:not(.dark) .v-md-editor-preview table tr:nth-child(even),
html:not(.dark) .vuepress-markdown-body table tr:nth-child(even),
html:not(.dark) .v-md-editor__main-right table tr:nth-child(even),
html:not(.dark) .preview-mode table tr:nth-child(even) {
  background-color: #f8f9fa !important;
}

html.light .v-md-preview__content table tr:nth-child(even) td,
html.light .v-md-editor-preview table tr:nth-child(even) td,
html.light .vuepress-markdown-body table tr:nth-child(even) td,
html.light .v-md-editor__main-right table tr:nth-child(even) td,
html:not(.dark) .v-md-preview__content table tr:nth-child(even) td,
html:not(.dark) .v-md-editor-preview table tr:nth-child(even) td,
html:not(.dark) .vuepress-markdown-body table tr:nth-child(even) td,
html:not(.dark) .v-md-editor__main-right table tr:nth-child(even) td,
html:not(.dark) .preview-mode table tr:nth-child(even) td {
  background-color: #f8f9fa !important;
}

html.light .v-md-preview__content table tr:hover,
html.light .v-md-editor-preview table tr:hover,
html.light .vuepress-markdown-body table tr:hover,
html.light .v-md-editor__main-right table tr:hover,
html:not(.dark) .v-md-preview__content table tr:hover,
html:not(.dark) .v-md-editor-preview table tr:hover,
html:not(.dark) .vuepress-markdown-body table tr:hover,
html:not(.dark) .v-md-editor__main-right table tr:hover,
html:not(.dark) .preview-mode table tr:hover {
  background-color: #f1f3f4 !important;
}

html.light .v-md-preview__content table tr:hover td,
html.light .v-md-editor-preview table tr:hover td,
html.light .vuepress-markdown-body table tr:hover td,
html.light .v-md-editor__main-right table tr:hover td,
html:not(.dark) .v-md-preview__content table tr:hover td,
html:not(.dark) .v-md-editor-preview table tr:hover td,
html:not(.dark) .vuepress-markdown-body table tr:hover td,
html:not(.dark) .v-md-editor__main-right table tr:hover td,
html:not(.dark) .preview-mode table tr:hover td {
  background-color: #f1f3f4 !important;
}
