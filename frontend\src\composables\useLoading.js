import { ref } from 'vue'
import { ElLoading } from 'element-plus'

export function useLoading() {
  const isLoading = ref(false)
  let loadingInstance = null

  // 开始加载，显示全屏加载提示
  const startLoading = (text = '加载中...') => {
    if (loadingInstance) {
      stopLoading()
    }
    
    isLoading.value = true
    loadingInstance = ElLoading.service({
      lock: true,
      text: text,
      background: 'rgba(0, 0, 0, 0.7)'
    })
  }

  // 停止加载，关闭加载提示
  const stopLoading = () => {
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
    isLoading.value = false
  }

  return {
    isLoading,
    startLoading,
    stopLoading
  }
} 