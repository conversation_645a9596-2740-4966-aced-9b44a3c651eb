<template>
  <div class="setting-manager glass-bg">
    <!-- 顶部导航 -->
    <div class="page-header">
      <div class="left-section">
        <h1 class="book-title">{{ bookTitle }}</h1>
        <div class="tab-buttons">
          <el-button
            class="tab-button"
            :class="{ active: activeTab === 'entities' }"
            @click="activeTab = 'entities'"
            type="primary"
            :plain="activeTab !== 'entities'"
          >
            <el-icon><Collection /></el-icon>
            设定实体
          </el-button>
          <el-button
            class="tab-button"
            :class="{ active: activeTab === 'templates' }"
            @click="activeTab = 'templates'"
            type="primary"
            :plain="activeTab !== 'templates'"
          >
            <el-icon><Document /></el-icon>
            模板管理
          </el-button>
        </div>
      </div>
      <el-button
        class="back-button modern-button"
        @click="goBack"
        type="primary"
        size="large"
      >
        <el-icon><ArrowLeft /></el-icon>
        返回写作
      </el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-container">
        <!-- 设定实体管理 -->
        <div v-show="activeTab === 'entities'" class="entities-container glass-bg">
          <div class="section-header">
            <div class="header-content">
              <h2>设定实体列表</h2>
              <el-select
                v-model="currentTemplateId"
                placeholder="请选择模板"
                class="template-select"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in templateList"
                  :key="template.id"
                  :label="`${template.name} (${getEntityCount(template)}个实体)`"
                  :value="template.id"
                >
                  <div class="template-option">
                    <span class="template-name">{{ template.name }}</span>
                    <el-tag size="small" type="info" effect="plain" class="entity-count-tag">
                      {{ getEntityCount(template) }}个实体
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
              <el-input
                v-model="searchQuery"
                placeholder="搜索实体名称"
                class="search-input"
                clearable
                @clear="handleSearchClear"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="button-group">
              <el-dropdown @command="handleExportCommand" split-button type="primary">
                <span>导出</span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="exportCurrentNames" :disabled="!currentTemplateId">导出当前模板实体名称</el-dropdown-item>
                    <el-dropdown-item command="exportCurrentDetails" :disabled="!currentTemplateId">导出当前模板实体详情</el-dropdown-item>
                    <el-dropdown-item command="exportCurrentDetailsJson" :disabled="!currentTemplateId">导出当前模板实体详情(JSON)</el-dropdown-item>
                    <el-dropdown-item command="exportAllNames" :disabled="entityList.length === 0">导出所有模板实体名称</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button
                type="success"
                @click="handleImportEntity"
                :disabled="!currentTemplateId"
              >
                <el-icon><Upload /></el-icon>
                导入实体
              </el-button>
              <el-button
                type="primary"
                @click="showEntityDialog()"
                :disabled="!currentTemplateId"
                class="create-entity-button"
              >
                <el-icon><Plus /></el-icon>
                新建设定实体
              </el-button>
            </div>
          </div>

          <div class="table-container">
            <el-empty
              v-if="!currentTemplateId"
              description="请先选择一个模板来查看相关实体"
            />

            <el-table
              v-else
              :data="paginatedEntities"
              style="width: 100%"
              v-loading="loading"
              @row-click="showEntityDetail"
              :row-style="{ height: '120px' }"
              class="modern-entity-table"
            >
              <el-table-column prop="name" label="实体名称" width="200" fixed="left">
                <template #default="{ row }">
                  <div class="entity-name-cell">
                    <div class="entity-name">{{ row.name }}</div>
                    <div class="entity-meta">
                      <el-tag size="small" type="info" effect="plain">
                        {{ getTemplateName(row.template_id) }}
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="维度信息" min-width="400">
                <template #default="{ row }">
                  <div class="dimension-grid">
                    <div
                      v-for="(value, key) in limitedDimensions(row.dimensions, 6)"
                      :key="key"
                      class="dimension-item"
                      :class="{ 'unset': value === '未设定' }"
                    >
                      <div class="dimension-label">{{ key }}</div>
                      <div class="dimension-value">{{ value === '未设定' ? '—' : value }}</div>
                    </div>
                    <div
                      v-if="Object.keys(row.dimensions).length > 6"
                      class="dimension-item more-dimensions"
                    >
                      <div class="dimension-label">更多</div>
                      <div class="dimension-value">+{{ Object.keys(row.dimensions).length - 6 }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="280" fixed="right" align="center">
                <template #default="{ row }">
                  <div class="operation-buttons-wrapper">
                    <el-button-group class="operation-buttons">
                      <el-button size="small" type="primary" @click.stop="copyEntityToClipboard(row)">
                        <el-icon><Document /></el-icon>
                        复制
                      </el-button>
                      <el-button size="small" type="info" @click.stop="copyEntityJsonToClipboard(row)">
                        <el-icon><DocumentCopy /></el-icon>
                        JSON
                      </el-button>
                      <el-button size="small" type="danger" @click.stop="deleteEntity(row)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </el-button-group>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="pagination-container">
            <el-pagination
              v-model="currentPage"
              :page-size="pageSize"
              :page-sizes="[5,10, 20, 50, 100]"
              :total="total"
              :pager-count="5"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
              layout="total, sizes, prev, pager, next"
              background
            />
          </div>
        </div>

        <!-- 模板管理 -->
        <div v-show="activeTab === 'templates'" class="templates-container glass-bg">
          <div class="section-header">
            <div class="header-content">
              <h2>设定模板</h2>
              <el-input
                v-model="templateSearchQuery"
                placeholder="搜索模板名称"
                class="search-input"
                clearable
                @clear="handleTemplateSearchClear"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="template-actions">
              <el-button 
                type="success" 
                class="import-button" 
                @click="showImportTemplateDialog"
              >
                <el-icon><Upload /></el-icon>
                导入模板
              </el-button>
              <el-button 
                type="primary"
                class="create-button" 
                @click="showTemplateDialog()"
              >
                <el-icon><Plus /></el-icon>
                新建模板
              </el-button>
            </div>
          </div>
          
          <div class="table-container">
            <el-table
              :data="paginatedTemplates"
              style="width: 100%"
              v-loading="loading"
              :row-style="{ height: '100px' }"
              class="modern-template-table sortable-table"
              row-key="id"
            >
              <el-table-column label="排序" width="80" align="center" fixed="left">
                <template #default="{ $index }">
                  <div class="sort-handle" :data-index="$index" title="拖拽此处可调整模板顺序">
                    <el-icon class="drag-icon"><Rank /></el-icon>
                    <span class="sort-number">{{ $index + 1 }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="name" label="模板名称" width="200" fixed="left">
                <template #default="{ row }">
                  <div class="template-name-cell">
                    <div class="template-name">{{ row.name }}</div>
                    <div class="template-description" v-if="row.description">
                      {{ row.description }}
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="维度配置" min-width="350">
                <template #default="{ row }">
                  <div class="dimensions-grid">
                    <div
                      v-for="(dim, index) in row.dimensions.slice(0, 8)"
                      :key="dim.name"
                      class="dimension-chip"
                      :class="{ 'primary': index < 3, 'secondary': index >= 3 }"
                    >
                      {{ dim.name }}
                    </div>
                    <div
                      v-if="row.dimensions.length > 8"
                      class="dimension-chip more-chip"
                    >
                      +{{ row.dimensions.length - 8 }}
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="统计信息" width="160" align="center">
                <template #default="{ row }">
                  <div class="template-stats">
                    <div class="stat-item">
                      <div class="stat-value">{{ getEntityCount(row) }}</div>
                      <div class="stat-label">实体</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-value">{{ row.dimensions.length }}</div>
                      <div class="stat-label">维度</div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="260" fixed="right" align="center">
                <template #default="{ row }">
                  <div class="operation-buttons-wrapper">
                    <el-button-group class="operation-buttons">
                      <el-button
                        size="small"
                        type="primary"
                        @click="showTemplateDialog(row)"
                      >
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button
                        size="small"
                        type="success"
                        @click="handleExportTemplate(row)"
                      >
                        <el-icon><Download /></el-icon>
                        导出
                      </el-button>
                      <el-button
                        size="small"
                        type="danger"
                        @click="deleteTemplate(row)"
                      >
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </el-button-group>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

            <div class="pagination-container">
              <el-pagination
                v-model="templateCurrentPage"
                :page-size="templatePageSize"
                :page-sizes="[5, 10, 20, 50]"
                :total="templateTotal"
                :pager-count="5"
                @size-change="handleTemplateSizeChange"
                @current-change="handleTemplatePageChange"
                layout="total, sizes, prev, pager, next"
                background
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模板创建/编辑对话框 - 完全重新设计 -->
    <el-dialog
      v-model="templateDialogVisible"
      :title="editingTemplate.id ? '编辑模板' : '创建模板'"
      :close-on-click-modal="false"
      :append-to-body="true"
      :lock-scroll="true"
      :destroy-on-close="false"
      class="native-template-dialog"
      width="800px"
      :modal="true"
      :show-close="true"
      @open="handleTemplateDialogOpen"
      @close="handleTemplateDialogClose"
    >
      <!-- 固定的基本信息区域 -->
      <div class="dialog-fixed-content">
        <div class="basic-info-section">
          <h4 class="section-title">基本信息</h4>
          <div class="form-row">
            <div class="form-field">
              <label class="field-label">模板名称 <span class="required">*</span></label>
              <el-input
                v-model="editingTemplate.name"
                placeholder="请输入模板名称"
                clearable
                ref="templateNameInput"
                @keydown="handleTemplateInputKeydown"
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-field full-width">
              <label class="field-label">模板描述</label>
              <el-input
                v-model="editingTemplate.description"
                type="textarea"
                :rows="2"
                placeholder="请输入模板描述（可选）"
                @keydown="handleTemplateInputKeydown"
              />
            </div>
          </div>
        </div>

        <div class="dimensions-section">
          <div class="section-header-fixed">
            <h4 class="section-title">维度配置</h4>
          </div>
        </div>
      </div>

      <!-- 可滚动的维度列表区域 -->
      <div class="dialog-scrollable-content">
        <!-- 添加维度输入框 - 在滚动区域内 -->
        <div class="add-dimension-section">
          <div v-if="!showingDimensionInput" class="add-dimension-trigger">
            <el-button
              type="primary"
              @click="showingDimensionInput = true"
              class="add-btn-full"
            >
              <el-icon><Plus /></el-icon>
              添加新维度
            </el-button>
          </div>
          <div v-else class="dimension-input-inline">
            <el-input
              v-model="newDimensionName"
              placeholder="请输入维度名称"
              ref="dimensionNameInput"
              @keydown.enter.prevent="addDimension"
              @keydown.esc="showingDimensionInput = false"
              @blur="handleDimensionInputBlur"
              size="small"
            >
              <template #append>
                <el-button type="primary" @click.prevent="handleDimensionButtonClick" size="small">
                  <el-icon><Check /></el-icon>
                </el-button>
              </template>
            </el-input>
            <div class="input-tip">按回车确认，ESC取消</div>
          </div>
        </div>

        <!-- 维度列表区域 -->
        <div class="dimensions-list-container">
          <!-- 可滚动的维度列表 -->
          <div class="dimensions-list-scrollable">
            <div v-if="editingTemplate.dimensions.length === 0" class="empty-dimensions">
              <div class="empty-icon">📝</div>
              <div class="empty-text">暂无维度</div>
              <div class="empty-hint">点击上方按钮添加第一个维度</div>
            </div>
            <div v-else class="dimensions-list-native">
              <draggable
                v-model="editingTemplate.dimensions"
                group="dimensions"
                item-key="name"
                handle=".drag-handle"
                ghost-class="ghost-dimension"
                :animation="200"
                class="dimensions-draggable"
              >
                <template #item="{element, index}">
                  <div class="dimension-item-native">
                    <el-icon class="drag-handle"><Rank /></el-icon>
                    <span class="dimension-text">{{ element.name }}</span>
                    <el-button
                      type="danger"
                      size="small"
                      text
                      @click="removeDimension(index)"
                      class="remove-button"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer-fixed">
          <el-button @click="templateDialogVisible = false" size="large">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="saveTemplate"
            :disabled="!canSaveTemplate"
            size="large"
          >
            <el-icon><Check /></el-icon>
            {{ editingTemplate.id ? '保存修改' : '创建模板' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 实体创建/编辑对话框 -->
    <el-dialog
        v-model="entityDialogVisible"
        :title="editingEntity.id ? '编辑实体' : '创建实体'"
        :close-on-click-modal="false"
        :append-to-body="true"
        :lock-scroll="true"
        :destroy-on-close="false"
        class="entity-detail-dialog"
        :fullscreen="isFullscreen"
        @open="handleEntityDialogOpen"
        @close="handleEntityDialogClose"
    >
      <div class="entity-card">
        <div class="entity-header">
          <el-form :model="editingEntity" label-width="100px" @submit.prevent="saveEntity">
            <el-form-item label="实体名称" required>
              <el-input
                  v-model="editingEntity.name"
                  placeholder="输入实体名称"
                  clearable
                  @keydown="handleEntityInputKeydown"
                  ref="entityNameInput"
              />
            </el-form-item>
            <el-form-item label="描述">
              <el-input
                  v-model="editingEntity.description"
                  type="textarea"
                  :rows="3"
                  placeholder="输入实体描述"
                  @keydown="handleEntityInputKeydown"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 动态维度表单项 -->
        <div class="entity-dimensions" v-if="currentTemplate">
          <h3 class="dimensions-title">维度信息</h3>
          <div class="dimensions-container">
            <el-scrollbar height="400px" class="dimensions-scrollbar">
              <el-form :model="editingEntity" label-position="top" class="dimensions-form">
                <el-form-item
                    v-for="dimension in currentTemplate.dimensions"
                    :key="dimension.name"
                    :label="dimension.name"
                >
                  <el-input
                      v-model="editingEntity.dimensions[dimension.name]"
                      :placeholder="'输入' + dimension.name"
                      type="textarea"
                      :rows="2"
                      @keydown="handleEntityInputKeydown"
                  />
                </el-form-item>
              </el-form>
            </el-scrollbar>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isFullscreen = !isFullscreen">
            <el-icon><FullScreen /></el-icon>
            {{ isFullscreen ? '退出全屏' : '全屏模式' }}
          </el-button>
          <el-button @click="entityDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEntity">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 实体详情对话框 -->
    <el-dialog
        v-model="entityDetailVisible"
        
        fullscreen
        :show-close="false"
        :close-on-click-modal="false"
        class="entity-detail-dialog fullscreen-dialog"
    >
      <div class="entity-card">
        <div class="entity-header">
          <el-form :model="editingEntity" label-width="120px" @submit.prevent="saveEntity">
            <el-form-item label="实体名称" required>
              <el-input
                  v-model="editingEntity.name"
                  placeholder="实体名称"
                  size="large"
                  @keydown="handleEntityInputKeydown"
              />
            </el-form-item>
            <el-form-item label="描述">
              <el-input
                  v-model="editingEntity.description"
                  type="textarea"
                  :rows="4"
                  placeholder="输入实体描述"
                  @keydown="handleEntityInputKeydown"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="entity-dimensions" v-if="currentTemplate">
          <h3 class="dimensions-title">维度信息</h3>
          <div class="dimensions-container">
            <el-form :model="editingEntity" label-width="120px" class="dimensions-form">
              <el-form-item
                  v-for="dimension in currentTemplateDimensions"
                  :key="dimension.name"
                  :label="dimension.name"
              >
                <el-input
                    v-model="editingEntity.dimensions[dimension.name]"
                    :placeholder="'输入' + dimension.name"
                    type="textarea"
                    :rows="4"
                    resize="none"
                    @keydown="handleEntityInputKeydown"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="large" @click="entityDetailVisible = false">取消</el-button>
          <el-button size="large" type="primary" @click="saveEntity">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入实体对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入实体"
      width="600px"
      class="import-dialog"
    >
      <div class="import-content">
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="format-hint-title">
                <el-icon><InfoFilled /></el-icon>
                <span>查看JSON格式示例</span>
              </div>
            </template>
            <div class="format-hint-content">
              <pre>{
  "name": "实体名称",
  "description": "实体描述",
  "dimensions": {
    "维度1": "值1",
    "维度2": "值2"
  }
}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-input
          v-model="importJsonContent"
          type="textarea"
          :rows="10"
          placeholder="请输入JSON字符串"
          class="import-input"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确认导入</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入模板对话框 -->
    <el-dialog
      v-model="importTemplateDialogVisible"
      title="导入模板"
      width="800px"
      class="import-template-dialog"
      :close-on-click-modal="false"
      :show-close="true"
    >
      <div class="import-content">
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="format-hint-title">
                <el-icon><InfoFilled /></el-icon>
                <span>查看JSON格式示例</span>
              </div>
            </template>
            <div class="format-hint-content">
              <pre>{
  "name": "模板名称",
  "description": "模板描述",
  "dimensions": [
    {
      "name": "维度1",
      "type": "text"
    },
    {
      "name": "维度2",
      "type": "text"
    }
  ]
}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-input
          v-model="importTemplateContent"
          type="textarea"
          :rows="10"
          placeholder="请输入模板JSON数据"
          class="import-input"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="large" @click="importTemplateDialogVisible = false">取消</el-button>
          <el-button size="large" type="primary" @click="confirmImportTemplate">确认导入</el-button>
        </div>
      </template>
    </el-dialog>

</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Document, DocumentCopy, Timer, Edit, Delete, Search, More, Upload, Download, InfoFilled, DArrowLeft, FullScreen, Rank, Check, Close, Collection } from '@element-plus/icons-vue'
import * as d3 from 'd3'
import draggable from 'vuedraggable'

// 从路由中获取书籍信息
const route = useRoute()
const router = useRouter()

// 从路由中获取书籍信息
const bookId = ref(route.query.id || route.params.id)
const bookTitle = ref(route.query.title || route.params.title)
// 如果没有书籍ID，重定向到书籍列表
if (!bookId.value) {
  ElMessage.error('未找到书籍信息')
  router.push('/book/writing')
}

// 视图状态
const activeTab = ref('entities')
const currentTemplateId = ref('')
const templateDialogVisible = ref(false)
const entityDialogVisible = ref(false)
const entityDetailVisible = ref(false)

// 加载状态
const loading = ref(false)

// 数据列表
const templateList = ref([])
const entityList = ref([])
const allEntities = ref([]) // 新增：存储所有实体
const selectedEntity = ref(null)

// 编辑状态
const editingTemplate = ref({
  name: '',
  description: '',
  dimensions: []
})
const editingEntity = ref({})
// 维度输入状态
const inputDimensionVisible = ref(false)
const inputDimensionValue = ref('')
const dimensionInputRef = ref(null)
const entityNameInput = ref(null)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => filteredEntities.value.length)

const paginatedEntities = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredEntities.value.slice(start, end)
})

const handlePageChange = (page) => {
  currentPage.value = page
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

// 搜索相关
const searchQuery = ref('')

const handleSearchClear = () => {
  searchQuery.value = ''
  currentPage.value = 1
}

// 计算属性
const filteredEntities = computed(() => {
  if (!currentTemplateId.value) return []
  let filtered = entityList.value.filter(entity =>
      entity.template_id === currentTemplateId.value
  )
  
  // 如果有搜索关键词，进行过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(entity =>
        entity.name.toLowerCase().includes(query)
    )
  }
  
  // 按更新时间排序，最新的在前面
  filtered.sort((a, b) => {
    const timeA = new Date(a.updated_at || a.created_at).getTime()
    const timeB = new Date(b.updated_at || b.created_at).getTime()
    return timeB - timeA
  })
  
  return filtered
})

const canSaveTemplate = computed(() => {
  return editingTemplate.value.name.trim() &&
      editingTemplate.value.dimensions.length > 0
})

const currentTemplate = computed(() => {
  return templateList.value.find(t => t.id === currentTemplateId.value)
})

// 获取模板名称
const getTemplateName = (templateId) => {
  const template = templateList.value.find(t => t.id === templateId)
  return template ? template.name : '未知模板'
}

// 获取实体数量
const getEntityCount = (template) => {
  return allEntities.value.filter(entity => entity.template_id === template.id).length
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  if (isNaN(d.getTime())) return date
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 返回上一页
const goBack = () => {
  router.push({
    name: 'bookWriting'
  })
}

// 模板相关方法
const showTemplateDialog = (template = {}) => {
  editingTemplate.value = {
    name: '',
    description: '',
    dimensions: [],
    ...template
  }
  templateDialogVisible.value = true
}

const resetTemplateForm = () => {
  editingTemplate.value = {
    name: '',
    description: '',
    dimensions: []
  }
  inputDimensionVisible.value = false
  inputDimensionValue.value = ''
}

const handleDimensionInputKeydown = (e) => {
  if (e.key === 'Enter') {
    if (e.shiftKey) {
      // Shift+Enter: 确认并关闭
      e.preventDefault()
      handleDimensionInputConfirm(false)
    } else {
      // Enter: 确认并继续
      e.preventDefault()
      handleDimensionInputConfirm(true)
    }
  } else if (e.key === 'Escape') {
    // Esc: 取消输入
    inputDimensionVisible.value = false
    inputDimensionValue.value = ''
  }
}

const handleDimensionInputConfirm = (continueAdding = true) => {
  const value = inputDimensionValue.value.trim()
  if (value) {
    // 检查是否已存在相同名称的维度
    const isDuplicate = editingTemplate.value.dimensions.some(dim => dim.name === value)
    if (isDuplicate) {
      ElMessage.warning('已存在相同名称的维度')
      return
    }

    editingTemplate.value.dimensions = editingTemplate.value.dimensions || []
    editingTemplate.value.dimensions.push({
      name: value,
      type: 'text',
      required: false
    })
    
    if (continueAdding) {
      // 清空输入并保持输入框显示
      inputDimensionValue.value = ''
      nextTick(() => {
        dimensionInputRef.value?.input?.focus()
      })
    } else {
      // 关闭输入框
      inputDimensionVisible.value = false
      inputDimensionValue.value = ''
    }
  } else {
    // 如果输入为空且按了确认，则关闭输入框
    inputDimensionVisible.value = false
    inputDimensionValue.value = ''
  }
}

const showDimensionInput = () => {
  inputDimensionVisible.value = true
  nextTick(() => {
    dimensionInputRef.value?.input?.focus()
  })
}

const handleDimensionClose = (index) => {
  editingTemplate.value.dimensions.splice(index, 1)
}

const saveTemplate = async () => {
  try {
    console.log(bookId.value)
    const response = await window.pywebview.api.book_controller.save_template({
      ...editingTemplate.value,
      book_id: bookId.value
    })
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('保存成功')
      templateDialogVisible.value = false
      loadTemplates()
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
}

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
        '删除模板将同时删除该模板下的所有实体，是否继续？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await window.pywebview.api.book_controller.delete_template(template.id, bookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('删除成功')
      loadTemplates()
      await loadAllEntities()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 实体相关方法
const showEntityDialog = (entity = {}) => {
  const template = templateList.value.find(t => t.id === (entity.template_id || currentTemplateId.value))

  editingEntity.value = {
    id: '',
    name: '',
    description: '',
    template_id: template?.id || '',
    dimensions: {},
    ...entity
  }

  // 初始化维度字段
  if (template && template.dimensions) {
    const initialDimensions = template.dimensions.reduce((acc, dim) => {
      acc[dim.name] = editingEntity.value.dimensions?.[dim.name] || ''
      return acc
    }, {})
    editingEntity.value.dimensions = initialDimensions
  }

  entityDialogVisible.value = true
}

const createEntityFromTemplate = (template) => {
  currentTemplateId.value = template.id
  activeTab.value = 'entities'

  // 使用模板的维度创建新实体
  const initialDimensions = template.dimensions.reduce((acc, dim) => {
    acc[dim.name] = ''
    return acc
  }, {})

  showEntityDialog({
    template_id: template.id,
    dimensions: initialDimensions
  })
}

const handleEntityInputKeydown = (e) => {
  if (e.key === 'Enter') {
    if (e.shiftKey) {
      // Shift+Enter: 仅保存实体
      e.preventDefault()
      saveEntity()
    } else if (!e.ctrlKey && !e.altKey && !e.metaKey && e.target.tagName !== 'TEXTAREA') {
      // Enter: 保存并关闭弹窗（仅对非文本域元素）
      e.preventDefault()
      saveEntity().then(() => {
        entityDialogVisible.value = false
        entityDetailVisible.value = false
      })
    }
  }
}

const saveEntity = async () => {
  try {
    if (!editingEntity.value.name) {
      ElMessage.error('请输入实体名称')
      return Promise.reject('请输入实体名称')
    }

    if (!editingEntity.value.template_id) {
      ElMessage.error('请选择模板类型')
      return Promise.reject('请选择模板类型')
    }

    const template = templateList.value.find(t => t.id === editingEntity.value.template_id)
    if (!template) {
      ElMessage.error('无效的模板类型')
      return Promise.reject('无效的模板类型')
    }

    // 检查同一模板下是否存在同名实体
    const existingEntity = entityList.value.find(entity =>
        entity.template_id === editingEntity.value.template_id &&
        entity.name === editingEntity.value.name &&
        entity.id !== editingEntity.value.id  // 排除当前编辑的实体（编辑模式下）
    )

    if (existingEntity) {
      ElMessage.error(`当前模板下已存在名为"${editingEntity.value.name}"的实体`)
      return Promise.reject(`当前模板下已存在名为"${editingEntity.value.name}"的实体`)
    }

    // 将空维度设置为"未设定"，并确保所有维度值都是字符串类型
    template.dimensions.forEach(dim => {
      const dimensionValue = editingEntity.value.dimensions[dim.name]
      if (dimensionValue === null || dimensionValue === undefined || 
          (typeof dimensionValue === 'string' && dimensionValue.trim() === '')) {
        editingEntity.value.dimensions[dim.name] = '未设定'
      } else {
        // 确保所有值都转换为字符串
        editingEntity.value.dimensions[dim.name] = String(dimensionValue)
      }
    })

    const response = await window.pywebview.api.book_controller.save_entity({
      ...editingEntity.value,
      book_id: bookId.value
    })
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success(editingEntity.value.id ? '更新成功' : '创建成功')
      entityDialogVisible.value = false
      // 重新加载所有实体
      await loadAllEntities()
      return Promise.resolve()
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存实体失败:', error)
    ElMessage.error('保存失败：' + error.message)
    return Promise.reject(error)
  }
}

const deleteEntity = async (entity) => {
  try {
    await ElMessageBox.confirm(
        '确定要删除这个实体吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await window.pywebview.api.book_controller.delete_entity(entity.id, bookId.value, entity.template_id)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('删除成功')

      // 立即从本地状态中移除实体，提供即时反馈
      const entityIndex = allEntities.value.findIndex(e => e.id === entity.id)
      if (entityIndex !== -1) {
        allEntities.value.splice(entityIndex, 1)
      }

      // 同时更新当前显示的实体列表
      if (currentTemplateId.value) {
        entityList.value = allEntities.value.filter(e => e.template_id === currentTemplateId.value)
      }

      // 重新加载所有实体以确保数据一致性
      await loadAllEntities()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 处理模板选择变化
const handleTemplateChange = async (templateId) => {
  currentTemplateId.value = templateId
  if (templateId) {
    // 从所有实体中筛选当前模板的实体
    entityList.value = allEntities.value.filter(entity => entity.template_id === templateId)
  } else {
    entityList.value = []
  }
}

const limitedDimensions = (dimensions, limit = 3) => {
  const entries = Object.entries(dimensions)
  const limited = entries.slice(0, limit)
  return Object.fromEntries(limited)
}

const currentTemplateDimensions = computed(() => {
  if (!editingEntity.value?.template_id) return []
  const template = templateList.value.find(t => t.id === editingEntity.value.template_id)
  return template?.dimensions || []
})

const showEntityDetail = (entity) => {
  const template = templateList.value.find(t => t.id === entity.template_id)
  if (!template) {
    ElMessage.error('找不到对应的模板')
    return
  }

  // 创建编辑对象的副本
  editingEntity.value = JSON.parse(JSON.stringify(entity))

  // 确保dimensions对象存在
  if (!editingEntity.value.dimensions) {
    editingEntity.value.dimensions = {}
  }

  // 同步模板维度，保留已有的值，添加新的维度
  template.dimensions.forEach(dimension => {
    if (!(dimension.name in editingEntity.value.dimensions)) {
      editingEntity.value.dimensions[dimension.name] = ''
    }
  })

  entityDetailVisible.value = true
}

// 数据加载方法
const loadTemplates = async () => {
  loading.value = true
  try {
    const response = await window.pywebview.api.book_controller.get_templates(bookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      let templates = result.data || []

      // 按照sort_order排序，如果没有sort_order则按创建时间排序
      templates.sort((a, b) => {
        if (a.sort_order !== undefined && b.sort_order !== undefined) {
          return a.sort_order - b.sort_order
        }
        if (a.sort_order !== undefined) return -1
        if (b.sort_order !== undefined) return 1
        return new Date(a.created_at || 0) - new Date(b.created_at || 0)
      })

      templateList.value = templates

      // 如果有模板，默认选择第一个
      if (templateList.value.length > 0 && !currentTemplateId.value) {
        currentTemplateId.value = templateList.value[0].id
      }
      // 加载所有实体
      await loadAllEntities()

      // 延迟初始化拖拽排序，确保DOM已更新
      setTimeout(() => {
        initSortable()
      }, 100)
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    ElMessage.error('加载模板失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const loadAllEntities = async () => {
  try {
    const response = await window.pywebview.api.book_controller.get_entities(bookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      allEntities.value = result.data || []
      // 如果当前已选择模板，更新entityList
      if (currentTemplateId.value) {
        entityList.value = allEntities.value.filter(entity => 
          entity.template_id === currentTemplateId.value
        )
      }
    } else {
      ElMessage.error(result.message || '加载实体失败')
    }
  } catch (error) {
    console.error('加载实体失败：', error)
    ElMessage.error('加载实体失败：' + error.message)
  }
}

// 模板排序相关
const isDragging = ref(false)
const dragStartIndex = ref(-1)

// 初始化拖拽排序
const initSortable = () => {
  nextTick(() => {
    const tableBody = document.querySelector('.sortable-table .el-table__body-wrapper tbody')
    if (!tableBody) {
      console.log('未找到表格body')
      return
    }

    console.log('初始化拖拽排序')

    // 清除之前的事件监听器
    tableBody.removeEventListener('dragstart', handleDragStart)
    tableBody.removeEventListener('dragover', handleDragOver)
    tableBody.removeEventListener('drop', handleDrop)
    tableBody.removeEventListener('dragend', handleDragEnd)

    // 添加拖拽事件监听
    tableBody.addEventListener('dragstart', handleDragStart)
    tableBody.addEventListener('dragover', handleDragOver)
    tableBody.addEventListener('drop', handleDrop)
    tableBody.addEventListener('dragend', handleDragEnd)

    // 为每一行设置索引，但不设置为可拖拽
    const rows = tableBody.querySelectorAll('tr')
    console.log('找到行数:', rows.length)
    rows.forEach((row, index) => {
      row.dataset.index = index
      console.log(`设置第${index}行索引`)

      // 只为排序手柄设置拖拽属性
      const sortHandle = row.querySelector('.sort-handle')
      if (sortHandle) {
        sortHandle.draggable = true
        sortHandle.dataset.rowIndex = index
      }
    })
  })
}

// 拖拽开始
const handleDragStart = (e) => {
  // 检查是否从排序手柄开始拖拽
  const sortHandle = e.target.closest('.sort-handle')
  if (!sortHandle) {
    e.preventDefault()
    return
  }

  const row = e.target.closest('tr')
  if (!row || !row.dataset.index) {
    e.preventDefault()
    return
  }

  console.log('开始拖拽，行索引:', row.dataset.index)

  isDragging.value = true
  dragStartIndex.value = parseInt(row.dataset.index)
  row.style.opacity = '0.5'
  row.classList.add('dragging')
  e.dataTransfer.effectAllowed = 'move'
  e.dataTransfer.setData('text/plain', row.dataset.index)
}

// 拖拽经过
const handleDragOver = (e) => {
  if (!isDragging.value) return
  e.preventDefault()
  e.dataTransfer.dropEffect = 'move'

  const targetRow = e.target.closest('tr')
  if (targetRow) {
    targetRow.style.borderTop = '2px solid var(--el-color-primary)'
  }
}

// 拖拽放下
const handleDrop = (e) => {
  if (!isDragging.value) return
  e.preventDefault()

  const targetRow = e.target.closest('tr')
  if (targetRow) {
    const dropIndex = parseInt(targetRow.dataset.index)
    console.log('拖拽放下，从', dragStartIndex.value, '到', dropIndex)

    if (dragStartIndex.value !== dropIndex && !isNaN(dragStartIndex.value) && !isNaN(dropIndex)) {
      moveTemplate(dragStartIndex.value, dropIndex)
    }
    targetRow.style.borderTop = ''
  }
}

// 拖拽结束
const handleDragEnd = (e) => {
  console.log('拖拽结束')

  isDragging.value = false
  dragStartIndex.value = -1

  const row = e.target.closest('tr')
  if (row) {
    row.style.opacity = '1'
    row.classList.remove('dragging')
  }

  // 清除所有边框样式
  const rows = document.querySelectorAll('.sortable-table tbody tr')
  rows.forEach(row => {
    row.style.borderTop = ''
  })
}

// 移动模板位置
const moveTemplate = async (fromIndex, toIndex) => {
  try {
    console.log('移动模板从', fromIndex, '到', toIndex)

    // 更新本地数据
    const templates = [...templateList.value]
    const [movedTemplate] = templates.splice(fromIndex, 1)
    templates.splice(toIndex, 0, movedTemplate)

    // 更新排序字段
    templates.forEach((template, index) => {
      template.sort_order = index
    })

    templateList.value = templates

    // 保存到后端
    await saveTemplateOrder()

    ElMessage.success('模板排序已更新')

    // 重新初始化拖拽功能
    setTimeout(() => {
      initSortable()
    }, 100)
  } catch (error) {
    console.error('移动模板失败:', error)
    ElMessage.error('移动模板失败: ' + error.message)
    // 重新加载模板以恢复原始顺序
    await loadTemplates()
  }
}

// 保存模板排序到后端
const saveTemplateOrder = async () => {
  try {
    const orderData = templateList.value.map((template, index) => ({
      id: template.id,
      sort_order: index
    }))

    const response = await window.pywebview.api.book_controller.update_template_order({
      book_id: bookId.value,
      templates: orderData
    })

    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status !== 'success') {
      throw new Error(result.message || '保存排序失败')
    }
  } catch (error) {
    console.error('保存模板排序失败:', error)
    throw error
  }
}

// 监听activeTab变化，当切换到模板管理时重新初始化拖拽
watch(activeTab, (newTab) => {
  if (newTab === 'templates') {
    setTimeout(() => {
      console.log('切换到模板管理，重新初始化拖拽')
      initSortable()
    }, 200)
  }
})

// 生命周期钩子
onMounted(async () => {
  console.log('组件挂载，bookId:', bookId.value)
  if (bookId.value) {
    await loadTemplates()
    // 确保DOM完全渲染后再初始化拖拽
    setTimeout(() => {
      console.log('延迟初始化拖拽')
      initSortable()
    }, 500)
  }
})

onUnmounted(() => {
  // 清理拖拽事件监听
  const tableBody = document.querySelector('.sortable-table .el-table__body-wrapper tbody')
  if (tableBody) {
    tableBody.removeEventListener('dragstart', handleDragStart)
    tableBody.removeEventListener('dragover', handleDragOver)
    tableBody.removeEventListener('drop', handleDrop)
    tableBody.removeEventListener('dragend', handleDragEnd)
  }
})

// 导出实体名称
const exportEntityNames = async () => {
  try {
    // 获取当前模板信息
    const currentTemplate = templateList.value.find(t => t.id === currentTemplateId.value)
    if (!currentTemplate) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 检查当前模板下是否有实体
    const templateEntities = entityList.value.filter(e => e.template_id === currentTemplateId.value)
    if (templateEntities.length === 0) {
      ElMessage.error('当前模板下没有实体')
      return
    }

    // 准备导出数据
    const exportData = {
      book_id: bookId.value,
      template_id: currentTemplateId.value,
      type: 'names'
    }

    const response = await window.pywebview.api.book_controller.save_entity_export(exportData)
    const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response
    
    if (parsedResponse.status === "success") {
      ElMessage.success('导出成功，文件已保存到指定目录')
    } else {
      ElMessage.error(`导出失败: ${parsedResponse.message}`)
    }
  } catch (error) {
    console.error('导出错误:', error)
    ElMessage.error(`导出出错: ${error.message}`)
  }
}

// 处理导出下拉菜单命令
const handleExportCommand = (command) => {
  switch (command) {
    case 'exportCurrentNames':
      exportEntityNames();
      break;
    case 'exportCurrentDetails':
      exportEntityDetails();
      break;
    case 'exportCurrentDetailsJson':
      exportEntityDetailsJson();
      break;
    case 'exportAllNames':
      exportAllEntitiesNames();
      break;
  }
}

// 导出所有模板下的实体名称
const exportAllEntitiesNames = async () => {
  try {
    // 检查是否有实体
    if (entityList.value.length === 0) {
      ElMessage.error('没有可导出的实体')
      return
    }

    // 准备导出数据
    const exportData = {
      book_id: bookId.value,
      template_id: 'all',
      type: 'all_names'
    }

    const response = await window.pywebview.api.book_controller.save_entity_export(exportData)
    const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response
    
    if (parsedResponse.status === "success") {
      ElMessage.success('导出成功，文件已保存到指定目录')
    } else {
      ElMessage.error(`导出失败: ${parsedResponse.message}`)
    }
  } catch (error) {
    console.error('导出错误:', error)
    ElMessage.error(`导出出错: ${error.message}`)
  }
}

// 导出实体详情
const exportEntityDetails = async () => {
  try {
    // 获取当前模板信息
    const currentTemplate = templateList.value.find(t => t.id === currentTemplateId.value)
    if (!currentTemplate) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 检查当前模板下是否有实体
    const templateEntities = entityList.value.filter(e => e.template_id === currentTemplateId.value)
    if (templateEntities.length === 0) {
      ElMessage.error('当前模板下没有实体')
      return
    }

    // 准备导出数据
    const exportData = {
      book_id: bookId.value,
      template_id: currentTemplateId.value,
      type: 'details'
    }

    const response = await window.pywebview.api.book_controller.save_entity_export(exportData)
    const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response
    
    if (parsedResponse.status === "success") {
      ElMessage.success('导出成功，文件已保存到指定目录')
    } else {
      ElMessage.error(`导出失败: ${parsedResponse.message}`)
    }
  } catch (error) {
    console.error('导出错误:', error)
    ElMessage.error(`导出出错: ${error.message}`)
  }
}

// 导出实体详情JSON版本
const exportEntityDetailsJson = async () => {
  try {
    // 获取当前模板信息
    const currentTemplate = templateList.value.find(t => t.id === currentTemplateId.value)
    if (!currentTemplate) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 检查当前模板下是否有实体
    const templateEntities = entityList.value.filter(e => e.template_id === currentTemplateId.value)
    if (templateEntities.length === 0) {
      ElMessage.error('当前模板下没有实体')
      return
    }

    // 准备导出数据
    const exportData = {
      book_id: bookId.value,
      template_id: currentTemplateId.value,
      type: 'details_json'
    }

    const response = await window.pywebview.api.book_controller.save_entity_export(exportData)
    const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response

    if (parsedResponse.status === "success") {
      ElMessage.success('导出成功，文件已保存到指定目录')
    } else {
      ElMessage.error(`导出失败: ${parsedResponse.message}`)
    }
  } catch (error) {
    console.error('导出错误:', error)
    ElMessage.error(`导出出错: ${error.message}`)
  }
}

const handleDialogOpened = () => {
  nextTick(() => {
    entityNameInput.value?.input?.focus()
  })
}

// 在 script setup 部分添加复制功能
const copyEntityToClipboard = async (entity) => {
  try {
    // 构建格式化的文本内容
    let content = `【${entity.name}】\n`;

    if (entity.description && entity.description.trim()) {
      // 处理描述中的换行符，将其替换为空格
      const cleanDescription = entity.description.replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();
      content += `  描述: ${cleanDescription}\n`;
    }

    if (entity.dimensions && Object.keys(entity.dimensions).length > 0) {
      // 过滤出有效的维度信息
      const validDimensions = Object.entries(entity.dimensions).filter(([, value]) =>
        value && value.trim() !== '' && value !== '未设定'
      );

      if (validDimensions.length > 0) {
        content += '\n  维度信息:\n';
        validDimensions.forEach(([key, value]) => {
          // 处理值中的换行符，将其替换为空格或其他分隔符
          const cleanValue = value.replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();
          content += `    • ${key}: ${cleanValue}\n`;
        });
      }
    }

    // 添加分隔线
    content += '\n' + '─'.repeat(30) + '\n';

    await window.pywebview.api.copy_to_clipboard(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败：' + error.message)
  }
}

// 复制完整JSON数据功能
const copyEntityJsonToClipboard = async (entity) => {
  try {
    // 复制完整的实体对象JSON数据，包括所有字段
    await window.pywebview.api.copy_to_clipboard(JSON.stringify(entity, null, 2))
    ElMessage.success('实体完整JSON数据已复制到剪贴板')
  } catch (error) {
    console.error('复制完整JSON失败:', error)
    ElMessage.error('复制完整JSON失败：' + error.message)
  }
}

// 导入相关状态
const importDialogVisible = ref(false)
const importJsonContent = ref('')

// 处理导入实体
const handleImportEntity = () => {
  importDialogVisible.value = true
  importJsonContent.value = ''
}

// 确认导入
const confirmImport = async () => {
  try {
    if (!importJsonContent.value.trim()) {
      ElMessage.error('请输入JSON字符串')
      return
    }

    // 解析JSON
    const jsonContent = JSON.parse(importJsonContent.value)
    
    // 验证必要字段
    if (!jsonContent.name) {
      ElMessage.error('导入失败：缺少实体名称')
      return
    }

    // 检查是否存在有效的 template_id
    const targetTemplateId = currentTemplateId.value
    if (!targetTemplateId) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 检查同名实体
    const existingEntity = entityList.value.find(entity =>
        entity.template_id === targetTemplateId &&
        entity.name === jsonContent.name
    )

    // 如果存在同名实体，询问是否覆盖
    if (existingEntity) {
      const confirmResult = await ElMessageBox.confirm(
        `当前模板下已存在名为"${jsonContent.name}"的实体，是否覆盖？`,
        '警告',
        {
          confirmButtonText: '覆盖',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(() => false)

      if (!confirmResult) return
    }

    // 准备实体数据
    const entityData = {
      name: jsonContent.name,
      description: jsonContent.description || '',
      dimensions: {},
      template_id: targetTemplateId,
      book_id: bookId.value
    }

    // 如果是覆盖现有实体，添加id字段
    if (existingEntity) {
      entityData.id = existingEntity.id
    }

    // 获取当前模板
    const currentTemplate = templateList.value.find(t => t.id === targetTemplateId)
    
    // 处理维度数据，只接受模板中定义的维度
    currentTemplate.dimensions.forEach(dim => {
      entityData.dimensions[dim.name] = 
        jsonContent.dimensions && jsonContent.dimensions[dim.name] !== undefined
          ? jsonContent.dimensions[dim.name]
          : '未设定'
    })

    // 保存实体
    const response = await window.pywebview.api.book_controller.save_entity(entityData)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      ElMessage.success(existingEntity ? '更新成功' : '导入成功')
      importDialogVisible.value = false
      loadAllEntities()
    } else {
      throw new Error(result.message || '导入失败')
    }
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('导入失败：JSON格式不正确')
    } else {
      console.error('导入失败:', error)
      ElMessage.error('导入失败：' + error.message)
    }
  }
}

// 导入模板相关
const importTemplateDialogVisible = ref(false)
const importTemplateContent = ref('')

const showImportTemplateDialog = () => {
  importTemplateDialogVisible.value = true
  importTemplateContent.value = ''
}

const confirmImportTemplate = async () => {
  try {
    if (!importTemplateContent.value.trim()) {
      ElMessage.error('请输入模板数据')
      return
    }

    // 解析JSON
    const templateData = JSON.parse(importTemplateContent.value)
    
    // 验证必要字段
    if (!templateData.name || !Array.isArray(templateData.dimensions)) {
      ElMessage.error('模板数据格式不正确')
      return
    }

    // 检查是否存在同名模板
    const existingTemplate = templateList.value.find(t => t.name === templateData.name)
    if (existingTemplate) {
      const confirmResult = await ElMessageBox.confirm(
        `已存在名为"${templateData.name}"的模板，是否覆盖？`,
        '警告',
        {
          confirmButtonText: '覆盖',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(() => false)

      if (!confirmResult) return
    }

    // 准备模板数据
    const saveData = {
      ...templateData,
      book_id: bookId.value
    }

    // 如果是覆盖现有模板，添加id
    if (existingTemplate) {
      saveData.id = existingTemplate.id
    }

    // 保存模板
    const response = await window.pywebview.api.book_controller.save_template(saveData)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      ElMessage.success(existingTemplate ? '更新成功' : '导入成功')
      importTemplateDialogVisible.value = false
      loadTemplates()
    } else {
      throw new Error(result.message || '导入失败')
    }
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('导入失败：JSON格式不正确')
    } else {
      console.error('导入失败:', error)
      ElMessage.error('导入失败：' + error.message)
    }
  }
}

// 导出模板
const handleExportTemplate = async (template) => {
  try {
    // 准备导出数据
    const exportData = {
      name: template.name,
      description: template.description,
      dimensions: template.dimensions
    }

    // 复制到剪贴板
    await window.pywebview.api.copy_to_clipboard(JSON.stringify(exportData, null, 2))
    ElMessage.success('模板数据已复制到剪贴板')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

const templateCurrentPage = ref(1)
const templatePageSize = ref(10)
const templateTotal = computed(() => filteredTemplates.value.length)

const paginatedTemplates = computed(() => {
  const start = (templateCurrentPage.value - 1) * templatePageSize.value
  const end = start + templatePageSize.value
  return filteredTemplates.value.slice(start, end)
})

const handleTemplatePageChange = (page) => {
  templateCurrentPage.value = page
}

const handleTemplateSizeChange = (size) => {
  templatePageSize.value = size
  templateCurrentPage.value = 1
}

const templateSearchQuery = ref('')

const filteredTemplates = computed(() => {
  if (!templateSearchQuery.value.trim()) {
    return templateList.value
  }
  const query = templateSearchQuery.value.toLowerCase().trim()
  return templateList.value.filter(template => 
    template.name.toLowerCase().includes(query)
  )
})

const handleTemplateSearchClear = () => {
  templateSearchQuery.value = ''
  templateCurrentPage.value = 1
}

// 全屏状态
const isFullscreen = ref(false)

// 实体弹窗打开处理
const handleEntityDialogOpen = () => {
  document.body.style.overflow = 'hidden'
  
  // 如果维度很多，自动设置为全屏模式
  if (editingEntity.value.dimensions && 
      Object.keys(editingEntity.value.dimensionValues || {}).length > 6) {
    isFullscreen.value = true
  }
}

// 实体弹窗关闭处理
const handleEntityDialogClose = () => {
  document.body.style.overflow = ''
  isFullscreen.value = false
}

// 模板弹窗打开处理
// 优化模板弹窗处理方法
const handleTemplateDialogOpen = () => {
  document.body.style.overflow = 'hidden'
  document.body.classList.add('modal-open')
  
  // 延迟聚焦，确保弹窗完全显示
  nextTick(() => {
    if (templateNameInput.value) {
      templateNameInput.value.focus()
    }
  })
}

const handleTemplateDialogClose = () => {
  document.body.style.overflow = ''
  document.body.classList.remove('modal-open')
  
  // 重置表单状态
  showingDimensionInput.value = false
  newDimensionName.value = ''
}


// 移除维度
const removeDimension = (index) => {
  editingTemplate.value.dimensions.splice(index, 1)
}

// 添加维度
const addDimension = () => {
  const value = newDimensionName.value.trim()
  if (value) {
    // 检查是否已存在相同名称的维度
    const isDuplicate = editingTemplate.value.dimensions.some(dim => dim.name === value)
    if (isDuplicate) {
      ElMessage.warning('已存在相同名称的维度')
      return
    }

    editingTemplate.value.dimensions.push({
      name: value,
      type: 'text',
      required: false
    })
    newDimensionName.value = ''
    // 添加成功后仍然保持输入框显示，方便继续添加
    nextTick(() => {
      if (dimensionNameInput.value) {
        dimensionNameInput.value.focus()
      }
    })
  }
  return false // 确保不会触发表单提交
}

// 处理维度输入框失去焦点
const handleDimensionInputBlur = () => {
  // 如果正在添加维度，不立即关闭输入框
  if (isAddingDimension.value) return
  
  // 延迟关闭输入框，避免与点击事件冲突
  setTimeout(() => {
    if (!isAddingDimension.value) {
      showingDimensionInput.value = false
    }
  }, 200)
}

// 新维度名称
const newDimensionName = ref('')
const showingDimensionInput = ref(false)
const dimensionNameInput = ref(null)

// 添加templateNameInput的定义，放在其他ref定义附近
const templateNameInput = ref(null)

// 确保在显示维度输入框时聚焦
watch(showingDimensionInput, (newVal) => {
  if (newVal) {
    nextTick(() => {
      if (dimensionNameInput.value) {
        dimensionNameInput.value.focus()
      }
    })
  }
})

// 添加防止blur事件过早触发的机制
const handleDimensionButtonClick = () => {
  isAddingDimension.value = true
  addDimension()
  // 短暂延迟后重置状态，确保addDimension先执行完
  setTimeout(() => {
    isAddingDimension.value = false
  }, 100)
}

// 新增变量跟踪添加状态
const isAddingDimension = ref(false)

// 添加模板表单的键盘事件处理
const handleTemplateInputKeydown = (e) => {
  if (e.key === 'Enter') {
    // 阻止默认的提交行为
    e.preventDefault()
    
    // 如果按下的是Shift+Enter，则保存模板
    if (e.shiftKey && canSaveTemplate.value) {
      saveTemplate()
    }
    // 普通Enter键不执行任何操作，只阻止默认行为
  }
}

// 处理模板表单中的Enter键事件
const handleTemplateFormSubmit = (e) => {
  // 阻止表单默认提交行为
  e.preventDefault()
  
  // 如果表单可以保存，则执行保存
  if (canSaveTemplate.value) {
    saveTemplate()
  }
}
</script>

<style lang="scss" scoped>

/* 现代化按钮样式 */
.modern-button {
  position: relative;
  border-radius: 12px;
  backdrop-filter: blur(12px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
  font-weight: 600;
  letter-spacing: 0.5px;
  padding: 14px 24px !important;
  font-size: 16px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

  /* 现代玻璃效果背景 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
                               rgba(255, 255, 255, 0.2) 0%,
                               rgba(255, 255, 255, 0.05) 100%);
    z-index: -1;
    border-radius: 11px;
  }

  /* 光泽扫过效果 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
                               transparent 0%,
                               rgba(255, 255, 255, 0.4) 50%,
                               transparent 100%);
    z-index: 1;
    transition: all 0.6s ease;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    border-color: var(--el-color-primary);

    &::after {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
    transition: all 0.15s ease;
  }

  .el-icon {
    font-size: 18px !important;
    margin-right: 8px;
  }
}

/* Tab按钮样式 */
.tab-buttons {
  display: flex;
  gap: 8px;
  background: var(--el-bg-color-overlay);
  padding: 6px;
  border-radius: 12px;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);

  .tab-button {
    border-radius: 8px !important;
    padding: 12px 20px !important;
    font-weight: 500 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: none !important;
    position: relative;
    overflow: hidden;

    &.active {
      background: var(--el-color-primary) !important;
      color: white !important;
      box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.4) !important;
      transform: translateY(-1px);
    }

    &:not(.active) {
      background: transparent !important;
      color: var(--el-text-color-regular) !important;

      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.1) !important;
        color: var(--el-color-primary) !important;
      }
    }

    .el-icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }
}

/* 通用按钮样式增强 */
.button-group {
  display: flex;
  gap: 12px;
  align-items: center;

  .el-button {
    border-radius: 10px !important;
    font-weight: 500 !important;
    padding: 12px 20px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: 1px solid var(--el-border-color-light) !important;
    backdrop-filter: blur(8px) !important;
    position: relative;
    overflow: hidden;

    /* 光泽效果 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
                                 transparent 0%,
                                 rgba(255, 255, 255, 0.2) 50%,
                                 transparent 100%);
      z-index: 1;
      transition: all 0.5s ease;
    }

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0px) !important;
      transition: all 0.1s ease !important;
    }

    .el-icon {
      font-size: 16px;
      margin-right: 6px;
    }
  }

  .el-dropdown {
    .el-button {
      border-radius: 10px !important;
    }

    /* 确保下拉箭头正确显示 */
    :deep(.el-dropdown__caret-button) {
      border-left: 1px solid rgba(var(--el-border-color-rgb), 0.3);
      border-radius: 0 10px 10px 0 !important;
    }

    :deep(.el-button-group > .el-button:first-child) {
      border-radius: 10px 0 0 10px !important;
    }

    :deep(.el-button-group > .el-button:last-child) {
      border-radius: 0 10px 10px 0 !important;
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--el-border-color-lighter);

  .header-content {
    display: flex;
    align-items: center;
    gap: 24px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 700;
      color: var(--el-text-color-primary);
      background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .template-actions {
    display: flex;
    gap: 12px;
  }
}

/* 添加样式部分 */
.add-dimension-container {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  
  .add-dimension-button {
    display: flex;
    justify-content: center;
    
    .add-button {
      width: 100%;
      height: 48px;
      font-size: 15px;
      border-radius: 8px;
      border: 1px dashed var(--el-border-color);
      background: transparent;
      color: var(--el-color-primary);
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.05);
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.1);
      }
      
      .el-icon {
        font-size: 16px;
        margin-right: 8px;
      }
    }
  }
  
  .dimension-input-wrapper {
    :deep(.el-input) {
      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 0 0 1px var(--el-color-primary) inset,
                  0 0 0 3px rgba(var(--el-color-primary-rgb), 0.1);
      }
      
      .el-input__inner {
        height: 48px;
        font-size: 16px;
      }
      
      .el-input-group__append {
        padding: 0;
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
        overflow: hidden;
        
        .el-button {
          height: 48px;
          padding: 0 16px;
          border: none;
          margin: 0;
        }
      }
    }
    
    .dimension-input-hint {
      margin-top: 8px;
      font-size: 13px;
      color: var(--el-text-color-secondary);
      text-align: center;
    }
  }
}
.setting-manager {
  padding: 20px;
  height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 100%;
  position: relative;
  background: var(--el-bg-color);

  &.glass-bg {
    background: rgba(var(--el-bg-color-rgb), 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

.page-header {
  user-select: none;
  margin-bottom: 32px;
  padding: 24px 36px;
  background: linear-gradient(135deg,
              var(--el-bg-color) 0%,
              var(--el-bg-color-overlay) 100%);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.15);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
  flex-shrink: 0;
  min-height: 88px;

  /* 添加顶部光泽效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.5) 50%,
                transparent 100%);
    border-radius: 16px 16px 0 0;
  }

  .left-section {
    display: flex;
    align-items: center;
    gap: 40px;

    .book-title {
      margin: 0;
      font-size: 32px;
      font-weight: 700;
      color: var(--el-text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 70%;
      background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      letter-spacing: 0.5px;
      position: relative;

      /* 添加装饰性下划线 */
      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg,
                    var(--el-color-primary) 0%,
                    var(--el-color-primary-light-5) 100%);
        border-radius: 2px;
      }
    }
  }
}

.main-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  max-width: 100%;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;

  .header-content {
    user-select: none;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    min-width: 0;
    flex: 1;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      white-space: nowrap;
    }

    .template-select {
      width: 200px;
      min-width: 160px;
    }

    .search-input {
      width: 240px;
      min-width: 180px;

      :deep(.el-input__wrapper) {
        background: rgba(var(--el-color-primary-rgb), 0.05);
        border: 1px solid rgba(var(--el-color-primary-rgb), 0.1);
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          border-color: rgba(var(--el-color-primary-rgb), 0.2);
          background: rgba(var(--el-color-primary-rgb), 0.08);
        }

        &.is-focus {
          background: rgba(var(--el-color-primary-rgb), 0.1);
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
        }
      }
    }
  }

  .template-actions, .button-group {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-shrink: 0;

    .el-button {
      padding: 8px 20px;
      height: 40px;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 6px;
      transition: all 0.3s ease;

      .el-icon {
        font-size: 16px;
      }

      &.el-button--primary {
        background: rgba(var(--el-color-primary-rgb), 0.1);
        border-color: rgba(var(--el-color-primary-rgb), 0.2);
        color: var(--el-color-primary);

        &:hover {
          background: var(--el-color-primary);
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
        }
      }

      &.el-button--success {
        background: rgba(var(--el-color-success-rgb), 0.1);
        border-color: rgba(var(--el-color-success-rgb), 0.2);
        color: var(--el-color-success);

        &:hover {
          background: var(--el-color-success);
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(var(--el-color-success-rgb), 0.2);
        }
      }
    }
  }
}

:deep(.el-select),
:deep(.el-input) {
  .el-input__wrapper {
    background: transparent;
    box-shadow: 0 0 0 1px var(--el-border-color-light);

    &:hover {
      box-shadow: 0 0 0 1px var(--el-border-color);
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) !important;
    }
  }
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  position: relative;

  .entities-container,
  .templates-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 16px;
    padding: 28px;
    box-shadow: var(--el-box-shadow-light);
    overflow: hidden;

    .section-header {
      flex-shrink: 0;
      margin-bottom: 24px;
    }

    .table-container {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      min-height: 0;

      .el-table {
        flex: 1;
        overflow: hidden;

        :deep(.el-table__header) {
          th {
            font-size: 16px;
            font-weight: 600;
            padding: 16px 0;
            background-color: var(--el-bg-color-overlay);
          }
        }

        :deep(.el-table__body) {
          td {
            font-size: 15px;
            padding: 16px 0;
            line-height: 1.6;
          }
        }

        .el-table__inner-wrapper {
          height: 100%;
        }

        .el-table__header-wrapper {
          flex-shrink: 0;
        }

        .el-table__body-wrapper {
          overflow-y: auto;
          height: calc(100% - 48px) !important;
        }
      }
    }
  }
}

/* 现代化实体表格样式 */
.modern-entity-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  :deep(.el-table__header) {
    background: var(--el-bg-color-overlay);

    th {
      font-size: 16px;
      font-weight: 600;
      padding: 20px 16px;
      border-bottom: 2px solid var(--el-border-color-light);
      background: transparent;
    }
  }

  :deep(.el-table__body) {
    tr {
      transition: all 0.3s ease;

      &:hover {
        background: var(--el-fill-color-extra-light);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    td {
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      vertical-align: middle;
    }
  }
}

/* 实体名称单元格样式 */
.entity-name-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .entity-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    line-height: 1.4;
  }

  .entity-meta {
    display: flex;
    gap: 6px;

    .el-tag {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 6px;
    }
  }
}

/* 维度网格布局 */
.dimension-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
  padding: 8px 0;

  .dimension-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 10px 12px;
    background: var(--el-fill-color-extra-light);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      background: var(--el-fill-color-light);
      border-color: var(--el-color-primary-light-7);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.unset {
      opacity: 0.6;
      background: var(--el-fill-color-blank);

      .dimension-value {
        color: var(--el-text-color-placeholder);
        font-style: italic;
      }
    }

    &.more-dimensions {
      background: linear-gradient(135deg, var(--el-color-info-light-9), var(--el-color-info-light-8));
      border-color: var(--el-color-info-light-5);
      cursor: pointer;

      &:hover {
        background: linear-gradient(135deg, var(--el-color-info-light-8), var(--el-color-info-light-7));
        border-color: var(--el-color-info-light-3);
      }

      .dimension-label {
        color: var(--el-color-info);
        font-weight: 600;
      }

      .dimension-value {
        color: var(--el-color-info);
        font-weight: 600;
      }
    }

    .dimension-label {
      font-size: 12px;
      font-weight: 600;
      color: var(--el-text-color-secondary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      line-height: 1.2;
    }

    .dimension-value {
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      line-height: 1.3;
      word-break: break-word;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}

/* 操作按钮包装器 */
.operation-buttons-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* 模板表格样式 */
.modern-template-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  :deep(.el-table__header) {
    background: var(--el-bg-color-overlay);

    th {
      font-size: 16px;
      font-weight: 600;
      padding: 20px 16px;
      border-bottom: 2px solid var(--el-border-color-light);
      background: transparent;
    }
  }

  :deep(.el-table__body) {
    tr {
      transition: all 0.3s ease;
      cursor: default;

      &:hover {
        background: var(--el-fill-color-extra-light);
        /* 移除transform避免与拖拽冲突 */
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      &.dragging {
        opacity: 0.5;
        transform: rotate(2deg);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        z-index: 1000;
      }
    }

    td {
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      vertical-align: middle;
    }
  }
}

/* 排序拖拽手柄样式 */
.sort-handle {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: default; /* 默认光标 */
  user-select: none;
  border: 2px dashed transparent;

  /* 只有可拖拽的手柄才显示拖拽光标 */
  &[draggable="true"] {
    cursor: grab;

    &:hover {
      background: var(--el-fill-color-light);
      transform: scale(1.05);
      border-color: var(--el-color-primary-light-5);
      cursor: grab;
    }

    &:active {
      cursor: grabbing;
      transform: scale(0.95);
      background: var(--el-color-primary-light-9);
    }
  }

  /* 非拖拽状态的样式 */
  &:not([draggable="true"]):hover {
    background: var(--el-fill-color-extra-light);
    transform: scale(1.02);
  }

  .drag-icon {
    font-size: 18px;
    color: var(--el-color-info);
    transition: all 0.3s ease;
  }

  .sort-number {
    font-size: 12px;
    font-weight: 600;
    color: var(--el-text-color-secondary);
    line-height: 1;
  }

  &:hover .drag-icon {
    color: var(--el-color-primary);
    transform: scale(1.2);
  }
}

/* 拖拽状态样式 */
.sortable-table :deep(tr) {
  transition: all 0.3s ease;

  &.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    z-index: 1000;
  }

  /* 确保其他区域保持默认光标 */
  td:not(:first-child) {
    cursor: default !important;
  }

  /* 只有排序手柄区域可以拖拽 */
  td:first-child {
    .sort-handle[draggable="true"] {
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }
  }
}

/* 模板名称单元格样式 */
.template-name-cell {
  display: flex;
  flex-direction: column;
  gap: 6px;

  .template-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    line-height: 1.4;
  }

  .template-description {
    font-size: 13px;
    color: var(--el-text-color-secondary);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 维度网格布局 */
.dimensions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 4px 0;

  .dimension-chip {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: default;
    user-select: none;
    line-height: 1.2;

    &.primary {
      background: linear-gradient(135deg, var(--el-color-primary-light-9), var(--el-color-primary-light-8));
      color: var(--el-color-primary);
      border: 1px solid var(--el-color-primary-light-5);
    }

    &.secondary {
      background: var(--el-fill-color-light);
      color: var(--el-text-color-regular);
      border: 1px solid var(--el-border-color-light);
    }

    &.more-chip {
      background: linear-gradient(135deg, var(--el-color-info-light-9), var(--el-color-info-light-8));
      color: var(--el-color-info);
      border: 1px solid var(--el-color-info-light-5);
      font-weight: 600;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}

/* 模板统计信息样式 */
.template-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    padding: 8px 12px;
    background: var(--el-fill-color-extra-light);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    min-width: 60px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--el-fill-color-light);
      border-color: var(--el-color-primary-light-7);
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .stat-value {
      font-size: 18px;
      font-weight: 700;
      color: var(--el-color-primary);
      line-height: 1;
    }

    .stat-label {
      font-size: 11px;
      font-weight: 500;
      color: var(--el-text-color-secondary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      line-height: 1;
    }
  }
}

.dimension-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 4px 0;

  .dimension-tag {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 15px;
    border: 1px solid var(--el-color-primary-light-5);
    background: linear-gradient(145deg, var(--el-color-primary-light-9), var(--el-color-primary-light-8));
    transition: all 0.3s ease;
    cursor: default;
    user-select: none;

    .dimension-key {
      font-size: 15px;
      font-weight: 600;
    }

    .dimension-separator {
      margin: 0 6px;
      color: var(--el-text-color-secondary);
      font-size: 15px;
    }

    .dimension-value {
      font-size: 15px;
      
      &.unset {
        color: var(--el-text-color-disabled);
        font-style: italic;
      }
    }
  }

  .more-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 15px;
  }
}

.template-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.dimensions-list {
  .dimension-tag {
    font-size: 15px;
    padding: 8px 16px;
  }
}

.entity-count {
  .el-tag {
    font-size: 15px;
    padding: 8px 16px;
  }
}

.operation-buttons {
  display: flex;
  gap: 6px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(6px);
  border: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color-overlay);

  .el-button {
    font-size: 12px !important;
    font-weight: 500 !important;
    padding: 8px 12px !important;
    border: none !important;
    border-radius: 0 !important;
    margin: 0 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    backdrop-filter: blur(4px);
    min-width: 60px;
    background: transparent !important;

    &:not(:last-child) {
      border-right: 1px solid var(--el-border-color-lighter) !important;
    }

    &:first-child {
      border-top-left-radius: 7px !important;
      border-bottom-left-radius: 7px !important;
    }

    &:last-child {
      border-top-right-radius: 7px !important;
      border-bottom-right-radius: 7px !important;
    }

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1) !important;
      z-index: 2 !important;
    }

    &:active {
      transform: translateY(0px) !important;
      transition: all 0.1s ease !important;
    }

    .el-icon {
      font-size: 14px;
      margin-right: 4px;
    }

    /* 不同类型按钮的现代化样式 - 使用CSS变量适配主题 */
    &.el-button--primary {
      background: linear-gradient(135deg,
                  rgba(var(--el-color-primary-rgb), 0.08) 0%,
                  rgba(var(--el-color-primary-rgb), 0.04) 100%) !important;
      color: var(--el-color-primary) !important;

      &:hover {
        background: linear-gradient(135deg,
                    rgba(var(--el-color-primary-rgb), 0.15) 0%,
                    rgba(var(--el-color-primary-rgb), 0.08) 100%) !important;
        box-shadow: 0 3px 12px rgba(var(--el-color-primary-rgb), 0.2) !important;
      }
    }

    &.el-button--info {
      background: linear-gradient(135deg,
                  rgba(var(--el-color-info-rgb), 0.08) 0%,
                  rgba(var(--el-color-info-rgb), 0.04) 100%) !important;
      color: var(--el-color-info) !important;

      &:hover {
        background: linear-gradient(135deg,
                    rgba(var(--el-color-info-rgb), 0.15) 0%,
                    rgba(var(--el-color-info-rgb), 0.08) 100%) !important;
        box-shadow: 0 3px 12px rgba(var(--el-color-info-rgb), 0.2) !important;
      }
    }

    &.el-button--success {
      background: linear-gradient(135deg,
                  rgba(var(--el-color-success-rgb), 0.08) 0%,
                  rgba(var(--el-color-success-rgb), 0.04) 100%) !important;
      color: var(--el-color-success) !important;

      &:hover {
        background: linear-gradient(135deg,
                    rgba(var(--el-color-success-rgb), 0.15) 0%,
                    rgba(var(--el-color-success-rgb), 0.08) 100%) !important;
        box-shadow: 0 3px 12px rgba(var(--el-color-success-rgb), 0.2) !important;
      }
    }

    &.el-button--danger {
      background: linear-gradient(135deg,
                  rgba(var(--el-color-danger-rgb), 0.08) 0%,
                  rgba(var(--el-color-danger-rgb), 0.04) 100%) !important;
      color: var(--el-color-danger) !important;

      &:hover {
        background: linear-gradient(135deg,
                    rgba(var(--el-color-danger-rgb), 0.15) 0%,
                    rgba(var(--el-color-danger-rgb), 0.08) 100%) !important;
        box-shadow: 0 3px 12px rgba(var(--el-color-danger-rgb), 0.2) !important;
      }
    }
  }
}

.search-input, .template-select {
  :deep(.el-input__inner) {
    font-size: 15px;
  }
}

/* 响应式布局优化 */
@media (max-width: 1200px) {
  .section-header {
    .header-content {
      gap: 12px;

      .template-select {
        width: 180px;
        min-width: 140px;
      }

      .search-input {
        width: 200px;
        min-width: 160px;
      }
    }

    .template-actions, .button-group {
      gap: 8px;

      .el-button {
        padding: 8px 16px;
        font-size: 13px;

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
}

@media (max-width: 900px) {
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;

    .header-content {
      justify-content: space-between;

      h2 {
        font-size: 16px;
      }

      .template-select {
        width: 160px;
        min-width: 120px;
      }

      .search-input {
        width: 180px;
        min-width: 140px;
      }
    }

    .template-actions, .button-group {
      justify-content: flex-end;
      flex-wrap: wrap;
    }
  }
}

@media (max-width: 600px) {
  .section-header {
    .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .template-select,
      .search-input {
        width: 100%;
        min-width: auto;
      }
    }

    .template-actions, .button-group {
      justify-content: center;

      .el-button {
        flex: 1;
        min-width: 0;

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      /* 优化下拉按钮在小屏幕下的显示 */
      .el-dropdown {
        flex: 1;

        .el-button {
          width: 100%;
        }

        :deep(.el-dropdown__caret-button) {
          min-width: 32px;
        }
      }
    }
  }
}

/* 优化下拉菜单项的显示 */
:deep(.el-dropdown-menu) {
  max-width: 300px;

  .el-dropdown-menu__item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 8px 16px;
    font-size: 14px;

    &:disabled {
      color: var(--el-text-color-disabled);
    }
  }
}

@media (max-width: 600px) {
  :deep(.el-dropdown-menu) {
    max-width: 280px;

    .el-dropdown-menu__item {
      font-size: 13px;
      padding: 6px 12px;
    }
  }
}

.pagination-container {
  :deep(.el-pagination) {
    font-size: 15px;
    
    .el-pagination__total,
    .el-pagination__jump,
    .el-pagination__sizes {
      font-size: 15px;
    }
  }
}

.import-dialog,
.import-template-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    background: var(--el-bg-color);
    
    .el-dialog__header {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-light);
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      background: var(--el-bg-color-page);
    }
    
    .el-dialog__footer {
      margin: 0;
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
    }
  }

  .import-content {
    .el-collapse {
      margin-bottom: 16px;
      border: none;
      
      :deep(.el-collapse-item) {
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        overflow: hidden;
        
        .el-collapse-item__header {
          padding: 12px 16px;
          font-size: 15px;
          font-weight: normal;
          color: var(--el-text-color-regular);
          background: var(--el-fill-color-blank);
          border-bottom: none;
          
          &.is-active {
            border-bottom: 1px solid var(--el-border-color);
          }
        }
        
        .el-collapse-item__content {
          padding: 16px;
          background: var(--el-bg-color);
        }
      }
    }

    .format-hint-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--el-text-color-regular);
      
      .el-icon {
        font-size: 16px;
        color: var(--el-color-info);
      }
    }

    .format-hint-content {
      pre {
        margin: 0;
        padding: 16px;
        background: var(--el-bg-color-page);
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;

        font-size: 14px;
        line-height: 1.6;
        color: var(--el-text-color-primary);
        white-space: pre-wrap;
        
        &:hover {
          border-color: var(--el-border-color);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
      }
    }

    .import-input {
      margin-top: 16px;
      
      :deep(.el-textarea__inner) {

        font-size: 16px;
        line-height: 1.6;
        padding: 16px;
        border-radius: 8px;
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color);
        color: var(--el-text-color-primary);
        transition: all 0.3s ease;
        min-height: 240px !important;
        
        &:hover {
          border-color: var(--el-border-color-darker);
        }
        
        &:focus {
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
        }
      }
    }
  }
}

.entity-detail-dialog.fullscreen-dialog {
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    margin: 0;
    
    .el-dialog__header {
      padding: 20px 30px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
      
      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .el-dialog__body {
      flex: 1;
      padding: 30px;
      overflow-y: auto;
      background: var(--el-bg-color-page);
    }
    
    .el-dialog__footer {
      padding: 20px 30px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
      
      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }
    }
  }

  .entity-card {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    
    .entity-header {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      
      :deep(.el-form-item__label) {
        font-size: 26px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
      
      :deep(.el-input__wrapper) {
        padding: 8px 15px;
        font-size: 20px;
      }
      
      :deep(.el-textarea__inner) {
        padding: 12px 15px;
        font-size: 20px;
        line-height: 1.9;
      }
    }
    
    .entity-dimensions {
      .dimensions-title {
        font-size: 28px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 24px;
      }
      
      .dimensions-container {
        background: var(--el-bg-color-page);
        border-radius: 8px;
        padding: 14px;
        
        .dimensions-form {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(500px, 3fr));
          gap: 14px;
          
          :deep(.el-form-item) {
            margin-bottom: 0;
            
            .el-form-item__label {
              font-size: 22px;
              font-weight: 500;
              color: var(--el-text-color-primary);
            }
            
            .el-textarea__inner {
              padding: 12px 15px;
              font-size: 19px;
              line-height: 1.9;
              background: var(--el-bg-color);
              border: 1px solid var(--el-border-color-light);
              border-radius: 8px;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: var(--el-border-color);
              }
              
              &:focus {
                border-color: var(--el-color-primary);
                box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
              }
            }
          }
        }
      }
    }
  }
}

:deep(.el-overlay) {
  overflow: hidden;
  
  .el-dialog__wrapper {
    overflow: hidden;
  }
}

/* 重构模板弹窗样式，解决滚动问题 */
.template-detail-dialog {
  :deep(.el-overlay) {
    backdrop-filter: blur(8px);
    background-color: rgba(0, 0, 0, 0.4);
    overflow: hidden !important;
  }
  
  :deep(.el-dialog) {
    width: 700px;
    max-width: 95vw;
    margin: 5vh auto !important;
    height: auto;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    overflow: hidden !important;
    box-shadow: 
      0 15px 35px rgba(0, 0, 0, 0.2),
      0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  :deep(.el-dialog__header) {
    margin: 0;
    padding: 24px 30px;
    border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    background: var(--el-bg-color);
    position: relative;
    z-index: 10;
    flex-shrink: 0;
    
    .el-dialog__title {
      font-size: 22px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: -0.5px;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 0;
    overflow: hidden;
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 200px;
    max-height: calc(90vh - 140px);
  }
  
  :deep(.el-dialog__footer) {
    padding: 20px 30px;
    border-top: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    background: var(--el-bg-color);
    position: relative;
    z-index: 10;
    flex-shrink: 0;
  }
  
  .template-form-container {
    padding: 30px;
    overflow-y: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.el-form) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 24px;
  }
  
  :deep(.dimensions-form-item) {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
    
    .el-form-item__content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
  
  .dimensions-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .dimensions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .dimensions-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
    
    .dimensions-hint {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }
  
  .dimensions-list {
    flex: 1;
    border-radius: 12px;
    background: var(--el-fill-color-light);
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.02);
    min-height: 260px;
    display: flex;
    flex-direction: column;
  }
  
  .dimensions-scrollbar {
    flex: 1;
    border-radius: 12px;
    height: 100% !important;
    min-height: 260px;
    
    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }
    
    :deep(.el-scrollbar__bar) {
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover :deep(.el-scrollbar__bar) {
      opacity: 0.8;
    }
  }
  
  .dimensions-draggable {
    padding: 16px;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .dimension-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    
    &:hover {
      transform: translateY(-2px);
    }
    
    .drag-handle {
      color: var(--el-text-color-secondary);
      font-size: 18px;
      cursor: move;
    }
    
    .dimension-tag {
      flex: 1;
      font-size: 15px;
      padding: 8px 16px;
      border-radius: 8px;
      background: var(--el-bg-color);
      border: 1px solid rgba(var(--el-border-color-rgb), 0.2);
    }
  }
  
  .empty-dimensions {
    height: 100%;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: var(--el-text-color-secondary);
    
    .el-icon {
      font-size: 24px;
      color: var(--el-color-info-light-5);
    }
  }
  
  :deep(.el-form-item__label) {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  :deep(.el-input__wrapper),
  :deep(.el-textarea__wrapper) {
    padding: 2px 16px;
    border-radius: 10px;
    box-shadow: 
      0 0 0 1px rgba(var(--el-border-color-rgb), 0.1) inset,
      0 2px 4px rgba(0, 0, 0, 0.02);
  }
  
  :deep(.el-input__inner) {
    height: 44px;
    font-size: 16px;
  }
  
  /* 按钮样式 */
  .dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16px;
    
    :deep(.el-button) {
      font-size: 16px;
      padding: 12px 24px;
      border-radius: 10px;
      font-weight: 500;
    }
  }
}

.ghost-dimension {
  opacity: 0.5;
  background: var(--el-color-primary-light-9);
  border: 1px dashed var(--el-color-primary);
}

.template-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  
  .template-name {
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
  
  .entity-count-tag {
    font-size: 12px;
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
    border-radius: 4px;
  }
}

.dimensions-scrollbar {
  height: 400px;
}

/* 优化实体弹窗样式，增加精致感和高级质感 */
.entity-detail-dialog {
  :deep(.el-overlay) {
    backdrop-filter: blur(8px);
    background-color: rgba(0, 0, 0, 0.4);
  }
  
  &:not(.fullscreen-dialog) {
    :deep(.el-dialog) {
      width: 900px;
      max-width: 95vw;
      margin: 5vh auto !important;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.2),
        0 5px 15px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    :deep(.el-dialog__header) {
      margin: 0;
      padding: 24px 30px;
      border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
      background: var(--el-bg-color);
      position: relative;
      z-index: 10;
      
      .el-dialog__title {
        font-size: 22px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        letter-spacing: -0.5px;
      }
    }
    
    :deep(.el-dialog__body) {
      padding: 30px;
      overflow-y: auto;
      max-height: calc(90vh - 170px);
      scrollbar-width: thin;
      background: var(--el-bg-color);
    }
    
    :deep(.el-dialog__footer) {
      padding: 20px 30px;
      border-top: 1px solid rgba(var(--el-border-color-rgb), 0.1);
      background: var(--el-bg-color);
      position: relative;
      z-index: 10;
    }
  }

  .entity-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 30px;
  }
  
  .entity-header {
    :deep(.el-form-item__label) {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      padding-bottom: 8px;
    }
    
    :deep(.el-input__wrapper) {
      padding: 2px 16px;
      border-radius: 10px;
      box-shadow: 
        0 0 0 1px rgba(var(--el-border-color-rgb), 0.1) inset,
        0 2px 4px rgba(0, 0, 0, 0.02);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 
          0 0 0 1px rgba(var(--el-color-primary-rgb), 0.2) inset,
          0 2px 6px rgba(0, 0, 0, 0.03);
      }
      
      &.is-focus {
        box-shadow: 
          0 0 0 1px var(--el-color-primary) inset,
          0 2px 8px rgba(var(--el-color-primary-rgb), 0.1);
      }
    }
    
    :deep(.el-input__inner) {
      height: 44px;
      font-size: 16px;
      color: var(--el-text-color-primary);
    }
    
    :deep(.el-textarea__inner) {
      padding: 12px 16px;
      font-size: 16px;
      min-height: 120px;
      line-height: 1.6;
      color: var(--el-text-color-primary);
    }
  }

  /* 维度信息样式 */
  .entity-dimensions {
    .dimensions-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.15);
      display: flex;
      align-items: center;
      gap: 10px;
      
      &::before {
        content: "";
        display: block;
        width: 4px;
        height: 22px;
        background: var(--el-color-primary);
        border-radius: 2px;
      }
    }
    
    .dimensions-container {
      position: relative;
    }
    
    .dimensions-scrollbar {
      border-radius: 12px;
      background: var(--el-fill-color-light);
      padding: 2px;
      box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.02);
      
      :deep(.el-scrollbar__wrap) {
        overflow-x: hidden;
      }
      
      :deep(.el-scrollbar__bar) {
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      &:hover :deep(.el-scrollbar__bar) {
        opacity: 0.8;
      }
      
      :deep(.el-scrollbar__thumb) {
        background-color: var(--el-color-primary-light-5);
        border-radius: 10px;
      }
    }
    
    .dimensions-form {
      padding: 20px;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
      gap: 24px;
      
      :deep(.el-form-item__label) {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        padding-bottom: 10px;
      }
      
      :deep(.el-textarea__wrapper) {
        box-shadow: 
          0 0 0 1px rgba(var(--el-border-color-rgb), 0.1) inset,
          0 2px 6px rgba(0, 0, 0, 0.03);
        transition: all 0.3s ease;
        border-radius: 10px;
        
        &:hover {
          box-shadow: 
            0 0 0 1px rgba(var(--el-color-primary-rgb), 0.2) inset,
            0 2px 6px rgba(0, 0, 0, 0.03);
        }
        
        &.is-focus {
          box-shadow: 
            0 0 0 1px var(--el-color-primary) inset,
            0 2px 8px rgba(var(--el-color-primary-rgb), 0.1);
        }
      }
      
      :deep(.el-textarea__inner) {
        padding: 12px 16px;
        font-size: 15px;
        line-height: 1.6;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  /* 按钮样式 */
  .dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16px;
    
    :deep(.el-button) {
      font-size: 16px;
      padding: 12px 24px;
      border-radius: 10px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      
      &:hover {
        transform: translateY(-2px);
      }
      
      .el-icon {
        font-size: 18px;
        margin-right: 6px;
      }
    }
    
    :deep(.el-button--primary) {
      box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
      
      &:hover {
        box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
      }
    }
  }
}

/* 全屏模式下的特殊样式 */
.entity-detail-dialog.fullscreen-dialog {
  :deep(.el-dialog__body) {
    background: var(--el-bg-color);
    padding: 30px;
  }
  
  .entity-card {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .dimensions-scrollbar {
    height: calc(100vh - 400px) !important;
    min-height: 500px;
  }
}

/* 修复维度列表为水平布局 */
.dimensions-draggable {
  padding: 16px;
  min-height: 200px;
  display: flex;
  flex-wrap: wrap; /* 水平布局并换行 */
  align-content: flex-start;
  gap: 12px;
}

.dimension-item {
  display: inline-flex; /* 内联弹性布局 */
  align-items: center;
  gap: 8px;
  padding: 4px;
  margin-bottom: 8px;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  
  &:hover {
    transform: translateY(-2px);
  }
  
  .drag-handle {
    color: var(--el-text-color-secondary);
    font-size: 18px;
    cursor: move;
  }
  
  .dimension-tag {
    font-size: 15px;
    padding: 8px 16px;
    border-radius: 8px;
    background: var(--el-bg-color);
    border: 1px solid rgba(var(--el-border-color-rgb), 0.2);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--el-color-primary-light-5);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    
    .el-tag__close {
      right: 8px;
    }
  }
}

/* 改进空状态显示 */
.empty-dimensions {
  width: 100%;
  height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: var(--el-text-color-secondary);
  border-radius: 8px;
  border: 1px dashed var(--el-border-color-light);
  background: var(--el-fill-color-blank);
  
  .el-icon {
    font-size: 24px;
    color: var(--el-color-info-light-5);
  }
  
  span {
    font-size: 14px;
  }
}

/* 修复维度列表水平布局，确保一行多项 */
.template-detail-dialog {
  .dimensions-draggable {
    padding: 16px;
    min-height: 200px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    gap: 12px;
    width: 100%;
  }
  
  .dimension-item {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 4px;
    margin-bottom: 8px;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    width: auto !important; /* 强制宽度自适应内容 */
    max-width: calc(50% - 20px); /* 确保最多一行两个 */
    flex: 0 0 auto !important; /* 不伸缩，保持自身大小 */
    
    &:hover {
      transform: translateY(-2px);
    }
    
    .drag-handle {
      color: var(--el-text-color-secondary);
      font-size: 18px;
      cursor: move;
      flex-shrink: 0;
    }
    
    .dimension-tag {
      font-size: 15px;
      padding: 8px 16px;
      border-radius: 8px;
      background: var(--el-bg-color);
      border: 1px solid rgba(var(--el-border-color-rgb), 0.2);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
      transition: all 0.3s ease;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      max-width: calc(100% - 30px);
      
      &:hover {
        border-color: var(--el-color-primary-light-5);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }
      
      .el-tag__close {
        right: 8px;
      }
    }
  }
}

/* 修复维度容器样式 */
.template-detail-dialog .dimensions-container {
  width: 100%;
}

/* 修复维度列表样式 */
.template-detail-dialog .dimensions-list {
  width: 100%;
  display: block;
}

/* 彻底重构维度列表样式，确保水平布局生效 */
.template-detail-dialog {
  .dimensions-wrapper {
    width: 100%;
    padding: 10px;
  }
  
  .dimensions-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
    gap: 16px !important;
    width: 100% !important;
  }
  
  .dimension-item {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 6px !important;
    background: var(--el-bg-color) !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03) !important;
    transition: all 0.25s ease !important;
    
    &:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06) !important;
    }
    
    .drag-handle {
      flex-shrink: 0 !important;
      cursor: move !important;
      color: var(--el-text-color-secondary) !important;
      font-size: 18px !important;
    }
    
    .dimension-tag {
      flex: 1 !important;
      width: calc(100% - 36px) !important;
      margin: 0 !important;
      font-size: 15px !important;
      text-align: left !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      border: 1px solid rgba(var(--el-border-color-rgb), 0.2) !important;
      background: transparent !important;
      
      .el-tag__close {
        right: 8px !important;
      }
    }
  }
  
  /* 修改滚动区域样式 */
  .dimensions-scrollbar {
    padding: 0 !important;
    
    :deep(.el-scrollbar__wrap),
    :deep(.el-scrollbar__view) {
      width: 100% !important;
      overflow-x: hidden !important;
    }
  }
  
  /* 空状态样式 */
  .empty-dimensions {
    width: 100% !important;
    height: 160px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: var(--el-text-color-secondary) !important;
    border: 1px dashed var(--el-border-color-light) !important;
    border-radius: 8px !important;
  }
  
  /* 维度标题样式 */
  .dimensions-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 12px !important;
    
    .dimensions-title {
      font-size: 16px !important;
      font-weight: 500 !important;
      color: var(--el-text-color-primary) !important;
    }
    
    .dimensions-hint {
      font-size: 14px !important;
      color: var(--el-text-color-secondary) !important;
    }
  }
}

.add-dimension-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;

  .add-dimension-button {
    flex: 1;
    margin-right: 10px;
  }

  .dimension-input-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .dimension-input-hint {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.dimension-item {
  // 保留现有样式...
  
  .dimension-tag {
    // 保留现有样式...
    position: relative;
    padding-right: 28px !important; /* 为关闭按钮留出空间 */
    
    .close-icon {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 14px;
      color: var(--el-text-color-secondary);
      cursor: pointer;
      z-index: 10;
      
      &:hover {
        color: var(--el-color-danger);
      }
    }
    
    /* 隐藏原始的el-tag关闭按钮，我们使用自己的 */
    :deep(.el-tag__close) {
      display: none;
    }
  }
}

/* 原生风格模板对话框 - 最终解决方案 */
.native-template-dialog {
  :deep(.el-overlay) {
    backdrop-filter: blur(8px);
    overflow: hidden !important;
  }

  :deep(.el-dialog__wrapper) {
    overflow: hidden !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100vh !important;
    max-height: 100vh !important;
  }

  :deep(.el-dialog) {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid var(--el-border-color-light);
    overflow: hidden !important;

    /* 固定尺寸 - 适中高度 */
    width: 800px !important;
    height: 680px !important;
    max-width: 800px !important;
    max-height: 680px !important;
    min-width: 800px !important;
    min-height: 680px !important;

    /* Flex布局 */
    display: flex !important;
    flex-direction: column !important;

    /* 位置 */
    margin: 0 !important;
    position: relative !important;
    transform: none !important;
  }

  :deep(.el-dialog__header) {
    background: var(--el-bg-color);
    padding: 16px 20px !important;
    margin: 0 !important;
    border-bottom: 1px solid var(--el-border-color-light);
    flex: none !important;
    height: 56px !important;
    overflow: hidden !important;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    flex: 1 !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    height: calc(680px - 56px - 72px) !important; /* 总高度 - header - footer */
  }

  :deep(.el-dialog__footer) {
    padding: 16px 20px !important;
    margin: 0 !important;
    border-top: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color-page);
    flex: none !important;
    height: 72px !important;
    overflow: hidden !important;
  }
}

/* 原生风格对话框内容区域 - 固定不滚动 */
.dialog-fixed-content {
  flex: none !important;
  padding: 24px !important;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  overflow: hidden !important;
  height: 280px !important;
  max-height: 280px !important;
}

.basic-info-section {
  margin-bottom: 28px;
  flex-shrink: 0;

  .section-title {
    margin: 0 0 18px 0;
    font-size: 17px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    padding-bottom: 10px;
    border-bottom: 2px solid var(--el-color-primary-light-8);
    display: flex;
    align-items: center;

    &::before {
      content: '📋';
      margin-right: 10px;
      font-size: 16px;
    }
  }

  .form-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-field {
    .field-label {
      display: block;
      margin-bottom: 6px;
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-regular);

      .required {
        color: var(--el-color-danger);
        margin-left: 2px;
      }
    }
  }
}

.dimensions-section {
  .section-header-fixed {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .section-title {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

/* 维度配置区域样式 */
.dimensions-section {
  flex-shrink: 0;

  .section-header-fixed {
    margin-bottom: 0;

    .section-title {
      margin: 0;
      font-size: 17px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      padding-bottom: 10px;
      border-bottom: 2px solid var(--el-color-primary-light-8);
      display: flex;
      align-items: center;

      &::before {
        content: '🏷️';
        margin-right: 10px;
        font-size: 16px;
      }
    }
  }
}

/* 添加维度区域 - 在滚动区域内 */
.add-dimension-section {
  margin-bottom: 16px;

  .add-dimension-trigger {
    .add-btn-full {
      width: 100%;
      height: 40px;
      border-radius: 8px;
      border: 2px dashed var(--el-color-primary-light-5);
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      font-weight: 500;

      &:hover {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-8);
      }
    }
  }

  .dimension-input-inline {
    .input-tip {
      margin-top: 6px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      text-align: center;
    }
  }
}

/* 可滚动内容区域 - 增加高度 */
.dialog-scrollable-content {
  flex: 1 !important;
  overflow: hidden !important;
  padding: 24px !important;
  display: flex !important;
  flex-direction: column !important;
  height: calc(552px - 280px) !important; /* body高度 - 固定区域高度 */
  max-height: calc(552px - 280px) !important;
}

/* 维度列表容器 - 重新设计 */
.dimensions-list-container {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  min-height: 0 !important;
}



/* 可滚动的维度列表区域 */
.dimensions-list-scrollable {
  flex: 1 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  min-height: 0 !important;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 3px;

    &:hover {
      background: var(--el-border-color-dark);
    }
  }
}

.empty-dimensions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    margin-bottom: 8px;
  }

  .empty-hint {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.dimensions-container {
  .dimensions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .count-text {
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-regular);
    }

    .hint-text {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}

/* 维度列表样式 */
.dimensions-list-native {
  .dimensions-draggable {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
}

.dimension-item-native {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  transition: all 0.2s ease;
  gap: 8px;
  min-width: 0;

  &:hover {
    border-color: var(--el-color-primary-light-5);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .drag-handle {
    color: var(--el-color-info);
    cursor: grab;
    font-size: 14px;
    flex-shrink: 0;

    &:hover {
      color: var(--el-color-primary);
    }

    &:active {
      cursor: grabbing;
    }
  }

  .dimension-text {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
  }

  .remove-button {
    flex-shrink: 0;
    padding: 2px;
    font-size: 12px;

    &:hover {
      background: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
    }
  }
}

.ghost-dimension {
  opacity: 0.5;
  transform: rotate(2deg);
}

/* 底部按钮区域 */
.dialog-footer-fixed {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}









.ghost-dimension {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* 强制防止整体滚动 */
.native-template-dialog {
  :deep(.el-overlay) {
    overflow: hidden !important;
    height: 100vh !important;
    max-height: 100vh !important;
  }

  :deep(.el-dialog__wrapper) {
    overflow: hidden !important;
    height: 100vh !important;
    max-height: 100vh !important;
  }

  :deep(.el-dialog) {
    overflow: hidden !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
  }
}


</style>