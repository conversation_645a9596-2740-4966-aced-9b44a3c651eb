<template>
  <FloatingWindow
      title="AI助手"
      :visible="visible"
      :initial-position="windowConfig.position"
      :width="windowConfig.size.width"
      :height="windowConfig.size.height"
      :pinnable="true"
      class="ai-assistant-window"
      @close="$emit('close')"
      @move="handleWindowMove"
      @resize="handleWindowResize"
  >
    <template #title-extra>
      <div class="title-extra">
        <div class="model-tag" :class="getModelClass(model)">
          {{ getModelDisplayName(model) }}
        </div>
        <button 
          class="refresh-btn" 
          :disabled="isReceiving"
          @click="resendInitialPrompt"
          title="重新发送初始提示"
        >
          <el-icon><Refresh /></el-icon>
        </button>
      </div>
    </template>
    
    <div class="ai-chat-content">
      <!-- 消息列表 -->
      <div ref="messagesRef" class="messages-list">
        <div v-for="(message, index) in messages" :key="`msg-${index}`" class="message-container">
          <message-bubble
            :content="message.content"
            :isUser="message.role === 'user'"
            :isError="message.isError"
            :timestamp="message.timestamp"
            :selectedModel="model"
            :reasoning="message.reasoning"
            :reasoningTime="message.reasoningTime"
            :disabled="isReceiving"
            :hasEditor="true"
            @resend="resendMessage(message.content)"
            @insert="insertToEditor(message.content)"
            @copy-full-message="copyToClipboard($event)"
          />
          </div>
            </div>
            
      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-options">
          <!-- 记忆模式开关 -->
          <div 
            class="memory-toggle" 
            :class="memoryEnabled ? 'memory-enabled' : 'memory-disabled'"
            @click="toggleMemoryMode"
            :title="memoryEnabled ? '点击切换到单次模式' : '点击切换到记忆模式'"
          >
            <div class="memory-icon">
              <el-icon v-if="memoryEnabled"><Opportunity /></el-icon>
              <el-icon v-else><Lightning /></el-icon>
                </div>
            <span>{{ memoryEnabled ? '记忆模式' : '单次模式' }}</span>
                </div>
          
          <!-- 系统提示词开关 -->
          <div 
            v-if="!memoryEnabled"
            class="system-prompt-toggle" 
            :class="useSystemPrompt ? 'enabled' : 'disabled'"
            @click="toggleSystemPrompt"
            :title="useSystemPrompt ? '点击关闭系统提示词' : '点击开启系统提示词'"
          >
            <div class="prompt-icon">
              <el-icon><Histogram /></el-icon>
                </div>
            <span>系统提示词</span>
            </div>
            
          <!-- 使用选中文本按钮 -->
          <button 
            v-if="props.selectedText" 
            class="selected-text-btn"
            @click="useSelectedText"
                :disabled="isReceiving"
            title="使用编辑器中选中的文本"
              >
            <div class="btn-icon">
              <el-icon><Edit /></el-icon>
            </div>
            <span>使用选中文本</span>
          </button>
        </div>
        
        <div class="input-container">
          <textarea
            ref="inputRef"
            v-model="currentMessage"
            class="message-input"
            placeholder="输入消息..."
            :disabled="isReceiving"
            @keydown.enter.ctrl.prevent="sendToAI"
            @keydown.enter.meta.prevent="sendToAI"
          ></textarea>
          
          <button 
            v-if="currentMessage" 
              class="clear-btn"
              @click="clearInput"
            title="清空输入"
            >
              <el-icon><Delete /></el-icon>
          </button>
        </div>
        
        <button 
          class="send-btn" 
          @click="isReceiving ? stopChat() : sendToAI()" 
          :disabled="!currentMessage.trim() && !isReceiving"
          :class="{ 'is-stopping': isReceiving }"
        >
          <div v-if="isReceiving" class="btn-icon">
            <el-icon><VideoPause /></el-icon>
          </div>
          <div v-else class="btn-icon">
            <el-icon><ArrowUpBold /></el-icon>
          </div>
          <span>{{ isReceiving ? '停止生成' : '发送' }}</span>
        </button>
      </div>
    </div>
  </FloatingWindow>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted, computed, watch } from 'vue'
import FloatingWindow from './FloatingWindow.vue'
import MessageBubble from './MessageBubble.vue'
import { ElMessage } from 'element-plus'
import { nanoid } from 'nanoid'
import { 
  Refresh, 
  ArrowUpBold, 
  Delete, 
  Edit, 
  Histogram, 
  Lightning, 
  Opportunity, 
  Position,
  Loading,
  VideoPause
} from '@element-plus/icons-vue'
import { useConfigStore } from '../stores/config'
import { useAIProvidersStore } from '../stores/aiProviders'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  initialPrompt: {
    type: String,
    default: ''
  },
  initialContent: {
    type: String,
    default: ''
  },
  model: {
    type: String,
    default: 'gpt-3.5-turbo'
  },
  selectedText: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'close', 'insert-text'])

// State
const messages = ref([])
const currentMessage = ref('')
const messagesRef = ref(null)
const inputRef = ref(null)
const currentChatId = ref('')
const isReceiving = ref(false)
const initializationComplete = ref(false)
const memoryEnabled = ref(true) // 默认开启记忆模式
const useSystemPrompt = ref(true) // 默认启用系统提示词

// Configuration
const configStore = useConfigStore()
const aiProvidersStore = useAIProvidersStore()

const windowConfig = computed(() => {
  const editorConfig = configStore.state.config.editor
  return editorConfig?.aiAssistant || {
    position: { x: 100, y: 100 },
    size: { width: 400, height: 600 }
  }
})

// 获取模型配置的方法
const getModelConfig = (modelUniqueId) => {
  try {
    console.log('AIAssistantWindow获取模型配置，模型ID:', modelUniqueId)
    console.log('可用模型数量:', aiProvidersStore.allAvailableModels.length)

    // 从aiProvidersStore获取模型配置
    const model = aiProvidersStore.allAvailableModels.find(m => m.uniqueId === modelUniqueId)
    if (model && model.config) {
      console.log('AIAssistantWindow获取到模型配置:', model.config)
      return model.config
    }

    // 如果没有找到配置，返回默认配置
    console.log('AIAssistantWindow未找到模型配置，使用默认配置')
    console.log('查找的模型ID:', modelUniqueId)
    if (aiProvidersStore.allAvailableModels.length > 0) {
      console.log('第一个可用模型:', aiProvidersStore.allAvailableModels[0])
    }

    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  } catch (error) {
    console.error('AIAssistantWindow获取模型配置失败:', error)
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  }
}

// Handle window events
const handleWindowMove = (newPosition) => {
  configStore.updateConfigItem('editor.aiAssistant.position', newPosition)
}

const handleWindowResize = (newSize) => {
  configStore.updateConfigItem('editor.aiAssistant.size', newSize)
}

// Message handlers
const setupMessageHandlers = () => {
  const oldReceiveChunk = window.receiveChunk
  const oldOnMessageComplete = window.onMessageComplete
  const oldReceiveChatError = window.receiveChatError

  // Handle incoming message chunks
  window.receiveChunk = (chunk) => {
    try {
      const decodedBytes = atob(chunk)
      const decodedChunk = new TextDecoder('utf-8').decode(
        new Uint8Array([...decodedBytes].map(char => char.charCodeAt(0)))
      )
      
      const messageData = JSON.parse(decodedChunk)
      const { chat_id, content, reasoning } = messageData
      
      // Only process messages for current chat
      if (chat_id === currentChatId.value) {
        const lastMessage = messages.value[messages.value.length - 1]
        
        if (!lastMessage || lastMessage.role !== 'assistant') {
          // Create new assistant message
          const newMessage = {
            role: 'assistant', 
            content: content || '',
            timestamp: Date.now() 
          }
          
          // Add reasoning if present
          if (reasoning) {
            newMessage.reasoning = reasoning
            newMessage.reasoningCollapsed = true
            newMessage.reasoningTime = '思考中...'
            newMessage.reasoningStartTime = Date.now()
          }
          
          messages.value.push(newMessage)
        } else {
          // Update existing message
          if (content) {
            lastMessage.content += content
        }
        
          // Update reasoning
          if (reasoning) {
            if (!lastMessage.reasoning) {
              lastMessage.reasoning = ''
              lastMessage.reasoningCollapsed = true
              lastMessage.reasoningTime = '思考中...'
              lastMessage.reasoningStartTime = Date.now()
            }
            lastMessage.reasoning += reasoning
          }
        }
        
        // Force update and scroll
        messages.value = [...messages.value]
        nextTick(scrollToBottom)
      }
    } catch (error) {
      console.error('处理消息块失败:', error)
    }
  }

  // Handle message completion
  window.onMessageComplete = (chat_id) => {
    if (chat_id === currentChatId.value) {
      isReceiving.value = false
      
      // Update reasoning time
      for (const msg of messages.value) {
        if (msg.role === 'assistant' && msg.reasoning && msg.reasoningStartTime) {
          const reasoningDuration = Date.now() - msg.reasoningStartTime
          msg.reasoningTime = `${(reasoningDuration / 1000).toFixed(1)}秒`
          delete msg.reasoningStartTime
        }
      }
      
      // Save chat history
      saveChatHistory()
      
      // Scroll to bottom
      nextTick(scrollToBottom)
    }
  }

  // Handle errors
  window.receiveChatError = (chunk) => {
    try {
      const decodedBytes = atob(chunk)
      const decodedChunk = new TextDecoder('utf-8').decode(
        new Uint8Array([...decodedBytes].map(char => char.charCodeAt(0)))
      )
      
      const errorData = JSON.parse(decodedChunk)
      const { chat_id, error_message } = errorData
      
      console.log('AIAssistantWindow: 收到错误消息:', chat_id, error_message);
      
      // Always show error message to user regardless of chat_id
      ElMessage.error({
        message: `AI回复失败: ${error_message}`,
        duration: 5000
      });
      
      if (chat_id === currentChatId.value) {
        // Reset receiving state
        isReceiving.value = false
        
        // Add error message
        messages.value.push({
          role: 'assistant',
          content: error_message,
          timestamp: Date.now(),
          isError: true
        })
        
        // Update UI
        messages.value = [...messages.value]
        saveChatHistory()
        
        // Scroll to bottom
        nextTick(scrollToBottom)
      }
    } catch (error) {
      console.error('处理错误消息失败:', error)
      isReceiving.value = false
    }
  }

  return () => {
    window.receiveChunk = oldReceiveChunk
    window.onMessageComplete = oldOnMessageComplete
    window.receiveChatError = oldReceiveChatError
  }
}

// Chat initialization
const initializeChat = async () => {
  if (props.initialPrompt && !initializationComplete.value) {
    try {
      // Generate chat ID if needed
      if (!currentChatId.value) {
        currentChatId.value = nanoid()
      }

      // Add initial user message
      const initialMessage = {
        role: 'user',
        content: props.initialPrompt,
        timestamp: Date.now()
      }
      
      // Add to messages
      messages.value = [initialMessage]
      
      // Save initial message
      await saveChatHistory()
      
      // Scroll to bottom
      nextTick(scrollToBottom)
      
      isReceiving.value = true
      
      // Create messages for API
      const apiMessages = [{
        role: 'user',
        content: props.initialPrompt
      }]
      
      // Call AI API
      try {
        // 获取模型配置
        const modelConfig = getModelConfig(props.model)

        // 合并配置：模型配置优先，只有stream强制为true
        const finalConfig = {
          stream: true,  // 强制启用流式输出
          ...modelConfig  // 模型配置（包括temperature, top_p, max_tokens等）
        }

        const response = await window.pywebview.api.model_controller.chat(
          currentChatId.value,
          props.model,
          apiMessages,
          finalConfig
        )

        const result = typeof response === 'string' ? JSON.parse(response) : response
        
        // Handle non-streaming response
      if (!result.data?.stream && result.data?.content) {
        const assistantMessage = {
          role: 'assistant',
          content: result.data.content,
          timestamp: Date.now()
          }
          
          messages.value.push(assistantMessage)
          await saveChatHistory()
        
          isReceiving.value = false
          nextTick(scrollToBottom)
      }
    } catch (error) {
        console.error('发送对话请求失败:', error)
        
        // Show error message
        ElMessage.error(error.message || '发送对话请求失败')
        
        // Reset state
        isReceiving.value = false
      }
      
      initializationComplete.value = true
    } catch (error) {
      console.error('初始化对话失败:', error)
      ElMessage.error(error.message || '初始化对话失败')
      isReceiving.value = false
      initializationComplete.value = false
    }
  }
}

// Send message to AI
const sendToAI = async () => {
  if (!currentMessage.value.trim()) return
  if (isReceiving.value) {
    ElMessage.warning('请等待当前对话完成')
    return
  }

  try {
    // Ensure chat ID is set
    if (!currentChatId.value) {
      currentChatId.value = nanoid()
    }
    
    isReceiving.value = true

    // Add user message
      const userMessage = {
        role: 'user',
        content: currentMessage.value,
        timestamp: Date.now()
      }
      
      messages.value.push(userMessage)
      
    // Save chat history
      await saveChatHistory()
      
    // Clear input and scroll
    const userMessageContent = currentMessage.value
    currentMessage.value = ''
        scrollToBottom()
    
    // Prepare API messages
    let apiMessages = []
    
    if (memoryEnabled.value) {
      // Memory mode: use all history
      apiMessages = messages.value.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    } else {
      // Single mode: only send current message
      if (props.initialPrompt && useSystemPrompt.value) {
        apiMessages.push({
          role: 'system',
          content: props.initialPrompt
        })
      }
      
        apiMessages.push({
          role: 'user',
        content: userMessageContent
      })
      }
      
    // 获取模型配置
    const modelConfig = getModelConfig(props.model)

    // 合并配置：模型配置优先，只有stream强制为true
    const finalConfig = {
      stream: true,  // 强制启用流式输出
      ...modelConfig  // 模型配置（包括temperature, top_p, max_tokens等）
    }

    // Call API
    const response = await window.pywebview.api.model_controller.chat(
      currentChatId.value,
      props.model,
      apiMessages,
      finalConfig
    )

    // Handle non-streaming response
    if (response && typeof response === 'object') {
      const result = typeof response === 'string' ? JSON.parse(response) : response

    if (!result.data?.stream && result.data?.content) {
      const assistantMessage = {
        role: 'assistant',
        content: result.data.content,
        timestamp: Date.now()
        }
        
        messages.value.push(assistantMessage)
        await saveChatHistory()
        isReceiving.value = false
      }
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error(error.message || '发送消息失败')
    isReceiving.value = false
  }
}

// Resend initial prompt
const resendInitialPrompt = async () => {
  if (isReceiving.value) {
    ElMessage.warning('请等待当前对话完成')
    return
  }
  
  // Reset state
  messages.value = []
  initializationComplete.value = false
  currentChatId.value = nanoid()
  
  // Reinitialize chat
  await initializeChat()
      }
      
// Resend a message
const resendMessage = async (messageContent) => {
  if (isReceiving.value) {
    ElMessage.warning('请等待当前对话完成')
    return
  }
  
  // Clear input first
  currentMessage.value = messageContent
  
  // Send the message
  await sendToAI()
}

// Save chat history
const saveChatHistory = async () => {
  try {
    if (!currentChatId.value) return
    
    // Get first user message as title
    const firstUserMessage = messages.value.find(msg => msg.role === 'user')
    const title = firstUserMessage?.content.substring(0, 50) + (firstUserMessage?.content.length > 50 ? '...' : '') || '无标题对话'
    
    // Build chat object
    const chatObject = {
      id: currentChatId.value,
      model: props.model,
      messages: messages.value,
      title: title,
      memory_enabled: memoryEnabled.value,
      created_at: Date.now(),
      updated_at: Date.now()
    }
    
    const response = await window.pywebview.api.model_controller.save_chat(currentChatId.value, chatObject)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status !== 'success') {
      console.error('保存聊天历史失败:', result.message)
    }
  } catch (error) {
    console.error('保存聊天历史失败:', error)
  }
}

// Utility functions
const copyToClipboard = async (text) => {
  try {
    console.log("AIAssistantWindow: Copying text:", text ? text.substring(0, 50) + "..." : "undefined or empty");
    
    if (!text) {
      ElMessage.warning('没有内容可复制');
      return;
    }
    
    // Try using the Clipboard API
    await window.pywebview.api.copy_to_clipboard(text);
    ElMessage.success('复制成功');
  } catch (err) {
    console.error('AIAssistantWindow: 复制失败:', err);
    
    // Fallback method using document.execCommand (for older browsers)
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const successful = document.execCommand('copy');
      if (successful) {
        ElMessage.success('复制成功');
      } else {
        throw new Error('execCommand failed');
      }
      
      document.body.removeChild(textArea);
    } catch (fallbackErr) {
      console.error('AIAssistantWindow: 复制失败 (fallback):', fallbackErr);
      ElMessage.error('复制失败: ' + err.message);
    }
  }
}

const scrollToBottom = () => {
  nextTick(() => {
  if (messagesRef.value) {
    messagesRef.value.scrollTop = messagesRef.value.scrollHeight
  }
  })
}

const stopChat = async () => {
  if (currentChatId.value && isReceiving.value) {
    try {
      const response = await window.pywebview.api.model_controller.stop_chat(currentChatId.value)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      if (result.status === 'success') {
        isReceiving.value = false
      } else {
        throw new Error(result.message || '停止失败')
      }
    } catch (error) {
      console.error('停止对话失败:', error)
      ElMessage.error(error.message || '停止失败')
    } finally {
      isReceiving.value = false
    }
  }
}

const insertToEditor = (content) => {
  emit('insert-text', content)
  ElMessage.success('内容已插入到编辑器')
}

const useSelectedText = () => {
  if (props.selectedText) {
    currentMessage.value = props.selectedText
    ElMessage.success('已使用选中文本')
  }
}

const clearInput = () => {
  currentMessage.value = ''
  ElMessage({
    message: '输入框已清空',
    type: 'info',
    duration: 1500
  })
}

// Toggle functions
const toggleMemoryMode = () => {
  memoryEnabled.value = !memoryEnabled.value
  handleMemoryChange(memoryEnabled.value)
}

const toggleSystemPrompt = () => {
  useSystemPrompt.value = !useSystemPrompt.value
  handleSystemPromptChange(useSystemPrompt.value)
}

// Handlers
const handleMemoryChange = (value) => {
  const message = value 
    ? '已切换到记忆模式，AI将记住完整对话历史' 
    : '已切换到单次模式，AI将不会记住上下文，有效节省Token'
  
    ElMessage({
    message,
    type: value ? 'info' : 'success',
      duration: 3000
    })
}

const handleSystemPromptChange = (value) => {
  const message = value
    ? '系统提示词已开启，将发送初始提示'
    : '系统提示词已关闭，将不会发送初始提示，进一步节省Token'
  
    ElMessage({
    message,
    type: value ? 'success' : 'warning',
      duration: 3000
    })
  }

// Model helpers
const getModelClass = (model) => {
  if (model.includes('gpt-4')) return 'model-gpt4'
  if (model.includes('gpt-3.5')) return 'model-gpt35'
  if (model.includes('claude')) return 'model-claude'
  return 'model-default'
}

const getModelDisplayName = (model) => {
  if (!model) return 'AI Model'
  
  // Remove common prefixes
  let displayName = model
    .replace('gpt-', '')
    .replace('claude-', '')
  
  // Capitalize
  return displayName.toUpperCase()
}

// Lifecycle hooks
onMounted(async () => {
  // Setup message handlers
  const cleanup = setupMessageHandlers()
  
  // Focus input on mount
  nextTick(() => {
    inputRef.value?.focus()
  })
  
  // Initialize chat if needed
  if (props.initialContent && props.initialContent.chatId) {
    currentChatId.value = props.initialContent.chatId
    
    // Try to load chat history
    const response = await window.pywebview.api.model_controller.get_chat(currentChatId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success' && result.data?.messages) {
      messages.value = result.data.messages
      memoryEnabled.value = result.data.memory_enabled !== false
      initializationComplete.value = true
      
      // Scroll to bottom
      nextTick(scrollToBottom)
  } else {
      // Initialize new chat
      await initializeChat()
  }
  } else {
    // Initialize new chat
    await initializeChat()
  }
  
  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
    
    // Stop chat if active
    if (currentChatId.value && isReceiving.value) {
      stopChat().catch(error => {
        console.error('停止对话失败:', error)
  })
}
  })
})
</script>

<style lang="scss">
.ai-assistant-window {
  min-width: 320px;
  min-height: 400px;
}

.title-extra {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .model-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-regular);
    
    &.model-gpt4 {
      background-color: var(--el-color-success-light-8);
      color: var(--el-color-success);
    }
    
    &.model-gpt35 {
      background-color: var(--el-color-primary-light-8);
      color: var(--el-color-primary);
    }
    
    &.model-claude {
      background-color: var(--el-color-warning-light-8);
      color: var(--el-color-warning);
    }
      }
  
  .refresh-btn {
          display: flex;
          align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    background: none;
          border: none;
    color: var(--el-text-color-secondary);
          cursor: pointer;
    transition: all 0.2s;
          
    &:hover:not(:disabled) {
      background-color: var(--el-fill-color-light);
            color: var(--el-color-primary);
          }
          
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
          }
        }
      }
  
.ai-chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--el-bg-color);
  overflow: hidden;
      }

.messages-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-lighter);
    border-radius: 3px;
  }
}

.input-area {
  padding: 12px 16px;
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  .input-options {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .memory-toggle,
  .system-prompt-toggle {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 13px;
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-regular);
    cursor: pointer;
    transition: all 0.2s;
      user-select: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    .memory-icon,
    .prompt-icon {
  display: flex;
      color: inherit;
    }
    
    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    &.memory-enabled {
      background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      border-left: 2px solid var(--el-color-primary);
      }
    
    &.memory-disabled {
      background-color: var(--el-color-warning-light-9);
      color: var(--el-color-warning);
      border-left: 2px solid var(--el-color-warning);
    }
    
    &.enabled {
      background-color: var(--el-color-success-light-9);
        color: var(--el-color-success);
      border-left: 2px solid var(--el-color-success);
    }
    
    &.disabled {
      background-color: var(--el-fill-color-light);
      color: var(--el-text-color-secondary);
      border-left: 2px solid transparent;
    }
  }
  
  .selected-text-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 13px;
    background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: auto;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    &:hover:not(:disabled) {
      background-color: var(--el-color-primary-light-8);
      transform: translateY(-1px);
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .btn-icon {
      display: flex;
    }
  }

  .input-container {
    position: relative;
    width: 100%;
    
    .message-input {
      width: 100%;
      min-height: 80px;
      max-height: 200px;
      resize: none;
      padding: 12px 16px;
      border-radius: 8px;
      border: 1px solid var(--el-border-color-light);
      background-color: var(--el-bg-color);
      color: var(--el-text-color-primary);
      font-size: 14px;
      line-height: 1.5;
      transition: all 0.2s;
      
      &:focus {
        outline: none;
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
      }
      
      &:disabled {
        background-color: var(--el-fill-color-light);
        cursor: not-allowed;
      }
    }
    
    .clear-btn {
      position: absolute;
      right: 8px;
      top: 8px;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      border: none;
      background-color: var(--el-fill-color-light);
      color: var(--el-text-color-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      opacity: 0.7;
      
      &:hover {
        opacity: 1;
        background-color: var(--el-color-danger-light-9);
          color: var(--el-color-danger);
        transform: rotate(90deg);
      }
    }
  }

  .send-btn {
    align-self: flex-end;
  display: flex;
  align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 0 20px;
    height: 40px;
    border-radius: 8px;
    border: none;
    background-color: var(--el-color-primary);
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    &.is-stopping {
      background-color: var(--el-color-error);
      .loading-icon {
        animation: none;
      }
    }
    
    &:hover:not(:disabled):not(.is-stopping) {
      background-color: var(--el-color-primary-dark-2);
    transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    &:hover:not(:disabled).is-stopping {
      background-color: var(--el-color-error-dark-2);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  
    &:active:not(:disabled) {
    transform: translateY(0);
    }
    
    &:disabled {
      background-color: var(--el-fill-color);
      color: var(--el-text-color-placeholder);
      cursor: not-allowed;
      box-shadow: none;
  }
    
    .btn-icon {
  display: flex;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
  }
  
// Dark mode adaptations
:deep(html.dark) {
  .message-input {
    border-color: var(--el-border-color-darker);
  }
  
  .memory-toggle,
  .system-prompt-toggle {
    background-color: var(--el-bg-color-darker);
    
    &.memory-enabled {
    background-color: rgba(var(--el-color-primary-rgb), 0.15);
  }
  
    &.memory-disabled {
      background-color: rgba(var(--el-color-warning-rgb), 0.15);
    }
    
    &.enabled {
      background-color: rgba(var(--el-color-success-rgb), 0.15);
  }
}

  .selected-text-btn {
    background-color: rgba(var(--el-color-primary-rgb), 0.15);
  }
}

/* 允许AI助手窗口中AI响应内容的文本选择 */
.messages-list {
  .message-container .message-bubble:not(.user-bubble),
  .message-container .message-bubble:not(.user-bubble) * {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    cursor: text;
  }

  /* 确保按钮等交互元素保持正确的光标 */
  .message-container .message-bubble button,
  .message-container .message-bubble .action-button {
    cursor: pointer;
    user-select: none;
  }
}
</style>
