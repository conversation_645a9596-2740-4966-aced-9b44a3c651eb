import { ref, onMounted, computed, onUnmounted,watch, reactive } from 'vue'
import { useConfigStore } from '@/stores/config'
import { useAIRolesStore } from '@/stores/aiRoles'
import { useAIProvidersStore } from '@/stores/aiProviders'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Folder, Plus, Search, Upload, Download, Refresh, Check, RefreshLeft, Connection, Warning, View, Edit, InfoFilled, Setting, Loading, Tools, ArrowDown } from '@element-plus/icons-vue'
import GitDiffViewer from '@/components/GitDiffViewer.vue'

// Initialize stores
 const configStore = useConfigStore()
 const aiRolesStore = useAIRolesStore()
 const aiProvidersStore = useAIProvidersStore()
 const activeTab = ref('chrome')
 const userDataDirs = computed(() => configStore.chrome?.userDataDirs || [])
 const runningStatus = ref({})
 const isLoading = ref(false);
// 添加错误显示状态
 const historyError = ref(null);
 const statusCheckTimer = ref(null)
 const chromePath = computed(() => configStore.chrome?.default_path || '')

// Git备份状态
 const backupStatus = ref({
  isEnabled: false,
  inProgress: false,
  lastBackupTime: '从未备份'
});

// 修复isLoadingHistory变量缺失问题，将gitHistoryLoading导出为isLoadingHistory
const isLoadingHistory = ref(false);
const gitHistoryLoading = isLoadingHistory;

// 预定义的颜色
const predefineColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
  '#DCDFE6', '#F2F6FC', '#303133', '#606266', '#909399',
  'rgba(64, 158, 255, 0.1)', 'rgba(103, 194, 58, 0.1)', 
  'rgba(230, 162, 60, 0.1)', 'rgba(245, 108, 108, 0.1)'
]

// 检查Chrome实例状态
const checkChromeStatus = async (dir) => {
  try {
    const response = await window.pywebview.api.drssion_controller.check_chrome_status({
      path: `${dir.path}/${dir.name}`,
      port: dir.port
    })
    const data = typeof response === 'string' ? JSON.parse(response) : response
    if (data.status === 'success') {
      runningStatus.value[dir.id] = data.data.running
    } else {
      runningStatus.value[dir.id] = false
    }
  } catch (error) {
    console.error('检查Chrome状态失败:', error)
    runningStatus.value[dir.id] = false
  }
}

const startStatusCheck = async () => {
  try {
    const dirs = [...userDataDirs.value]
    for (const dir of dirs) {
      await checkChromeStatus(dir)
    }
  } catch (error) {
    console.error('检查状态失败:', error)
  }
}

// Chrome实例管理
 async function toggleChrome(dir) {
  try {
    if (runningStatus.value[dir.id]) {
      // 停止Chrome实例
      const result = await window.pywebview.api.drssion_controller.stop_chrome_profile({
        path: `${dir.path}/${dir.name}`,
        port: dir.port
      })
      const response = typeof result === 'string' ? JSON.parse(result) : response
      
      if (response.status === 'success') {
        runningStatus.value[dir.id] = false
        ElMessage.success('Chrome实例已停止')
      } else {
        throw new Error(response.message || '停止失败')
      }
    } else {
      // 启动Chrome实例
      const result = await window.pywebview.api.drssion_controller.start_chrome_with_profile({
        path: `${dir.path}/${dir.name}`,
        port: dir.port,
        browser_path: chromePath.value,
        extensions_enabled: dir.enableExtensions,
        extensions_path: dir.extensionsPath,
      })
      const response = typeof result === 'string' ? JSON.parse(result) : response
      
      if (response.status === 'success') {
        runningStatus.value[dir.id] = true
        ElMessage.success('Chrome实例已启动')
      } else {
        throw new Error(response.message || '启动失败')
      }
    }
  } catch (error) {
    ElMessage.error(error.message || (runningStatus.value[dir.id] ? '停止失败' : '启动失败'))
  }
}

// Chrome相关方法
 const select_file_path_js = async (type) => {
  const response = await window.pywebview.api.select_file_path()
  const result = typeof response === 'string' ? JSON.parse(response) : response
  if (result && result.status === 'success' && result.data.length > 0) {
    configStore.chrome.default_path = result.data[0]
    await configStore.updateConfigItem('chrome.default_path', result.data[0])
  }
}

// 处理目录选择
 const selectDirectory = async (type) => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success' && result.data) {
      // 添加用户数据目录路径处理
      if (type === 'userDataPath') {
        userDataDirDialog.value.form.path = result.data
      } else if (type === 'extensionsPath') {
        userDataDirDialog.value.form.extensionsPath = result.data
      } else if (type === 'downloadDir') {
        configStore.chrome.downloadDir = result.data
      } else if (type === 'gitLocalPath') {
        gitConfig.value.localPath = result.data
      } else if (type === 'gitBackupDir') {
        gitConfig.value.backupDir = result.data
      }
    }
  } catch (error) {
    ElMessage.error(`选择目录失败: ${error}`)
  }
}

 const detectChromePath = async () => {
  try {
    const result = await window.pywebview.api.drssion_controller.detect_chrome_path()
    const response = typeof result === 'string' ? JSON.parse(result) : response
    if (response.status === 'success') {
      await configStore.updateConfigItem('chrome.default_path', response.data.path)
      ElMessage.success('已自动检测并更新Chrome路径')
    }
  } catch (error) {
    ElMessage.error('检测Chrome径失败：' + error.message)
  }
}

// 用户数据目录管理方法
const addUserDataDir = () => {
  userDataDirDialog.value = {
    visible: true,
    isEdit: false,
    form: {
      name: '',
      path: '',
      port: 9222,
      isDefault: false,
      enableExtensions: false,
      extensionsPath: ''
    }
  }
}

const editUserDataDir = (dir) => {
  userDataDirDialog.value = {
    visible: true,
    isEdit: true,
    form: { ...dir }
  }
}

const saveUserDataDir = async () => {
  const formRef = userDataDirFormRef.value
  if (!formRef) return

  await formRef.validate(async (valid) => {
    if (valid) {
      const { form } = userDataDirDialog.value
      
      if (form.isDefault) {
        configStore.chrome.userDataDirs.forEach(dir => {
          if (dir.id !== form.id) {
            dir.isDefault = false
          }
        })
      }
      
      const hasDefault = configStore.chrome.userDataDirs.some(dir => dir.isDefault && dir.id !== form.id)
      if (!hasDefault && !form.isDefault && configStore.chrome.userDataDirs.length === 0) {
        form.isDefault = true
      }

      if (userDataDirDialog.value.isEdit) {
        const index = configStore.chrome.userDataDirs.findIndex(dir => dir.id === form.id)
        if (index !== -1) {
          configStore.chrome.userDataDirs[index] = { ...form }
        }
      } else {
        const newDir = {
          ...form,
          id: Date.now().toString()
        }
        configStore.chrome.userDataDirs.push(newDir)
      }

      await configStore.updateConfigItem('chrome.userDataDirs', configStore.chrome.userDataDirs)
      userDataDirDialog.value.visible = false
      ElMessage.success('保存成功')
    }
  })
}

const deleteUserDataDir = async (dir) => {
  try {
    await ElMessageBox.confirm('确定要删除该用户数据目录吗？', '提示', {
      type: 'warning'
    })
    const dirs = configStore.chrome.userDataDirs.filter(item => item.id !== dir.id)
    await configStore.updateConfigItem('chrome.userDataDirs', dirs)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message)
    }
  }
}

// AI角色管理方法
const addAIRole = () => {
  aiRoleDialog.value.isEdit = false
  aiRoleDialog.value.title = '添加AI角色'
  aiRoleDialog.value.form = {
    id: '',
    name: '',
    prompt: '',
    isEnabled: true
  }
  aiRoleDialog.value.visible = true
}

const editAIRole = (role) => {
  aiRoleDialog.value.isEdit = true
  aiRoleDialog.value.title = '编辑AI角色'
  aiRoleDialog.value.form = { ...role }
  aiRoleDialog.value.visible = true
}

const handleRoleEnableChange = async (role, val) => {
  try {
    await aiRolesStore.updateRole(role.id, { isEnabled: val })
    ElMessage.success(`已${val ? '启用' : '禁用'}角色 "${role.name}"`)
  } catch (error) {
    ElMessage.error('操作失败: ' + error.message)
    // 恢复原来的状态
    role.isEnabled = !val
  }
}

const saveAIRole = async () => {
  try {
    if (!aiRoleDialog.value.form.name) {
      ElMessage.warning('角色名称不能为空')
      return
    }
    if (!aiRoleDialog.value.form.prompt) {
      ElMessage.warning('提示词不能为空')
      return
    }
    
    let response;
    if (aiRoleDialog.value.isEdit) {
      response = await aiRolesStore.updateRole(aiRoleDialog.value.form.id, aiRoleDialog.value.form)
      ElMessage.success('AI角色更新成功')
    } else {
      response = await aiRolesStore.addRole(aiRoleDialog.value.form)
      ElMessage.success('AI角色添加成功')
    }
    
    // 关闭对话框
    aiRoleDialog.value.visible = false
    
    // 刷新角色列表并确保视图更新
    await aiRolesStore.loadRoles()
    
    // 如果是添加操作，确保新角色显示在列表中
    if (!aiRoleDialog.value.isEdit && response && response.data) {
      // 添加一个小延迟确保DOM更新
      setTimeout(() => {
        // 如果添加成功但列表中没有，手动添加
        const newRoleId = response.data.id;
        const exists = aiRolesStore.roles.some(r => r.id === newRoleId);
        if (!exists && response.data) {
          aiRolesStore.roles.push(response.data);
        }
      }, 100);
    }
  } catch (error) {
    ElMessage.error('操作失败: ' + error.message)
  }
}

const deleteAIRole = async (role) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色 "${role.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await aiRolesStore.deleteRole(role.id)
    ElMessage.success('角色已删除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 实现缺失的exportAIRoles方法（用于导出AI角色）
const exportAIRoles = () => {
  try {
    const roles = aiRolesStore.roles
    const rolesJson = JSON.stringify(roles, null, 2)
    
    // 创建Blob并下载
    const blob = new Blob([rolesJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `AI角色_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

// OpenAI配置相关方法
const initOpenAIConfig = () => {
  const config = configStore.openai
  openaiForm.value = {
    api_key: config.api_key || '',
    base_url: config.base_url || ''
  }
}

const saveOpenAIConfig = async () => {
  try {
    await configStore.updateConfigItem('openai', openaiForm.value)
    ElMessage.success('OpenAI 配置保存成功')
  } catch (error) {
    console.error('保存 OpenAI 配置失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  }
}

const resetOpenAIConfig = () => {
  initOpenAIConfig()
}

// 飞书置相关
const initFeishuConfig = () => {
  const config = configStore.feishu
  feishuForm.value = {
    app_id: config.app_id || '',
    app_secret: config.app_secret || '',
    encrypt_key: config.encrypt_key || '',
    verification_token: config.verification_token || ''
  }
}

const saveFeishuConfig = async () => {
  try {
    await configStore.updateConfigItem('feishu', feishuForm.value)
    ElMessage.success('飞书配置保存成功')
  } catch (error) {
    console.error('保存飞书配置失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  }
}

const resetFeishuConfig = () => {
  initFeishuConfig()
}

// 备份表单数据
const backupForm = ref({
  backupDir: '',
  targetDir: '',
  autoBackup: false,
  backupInterval: 30,
  keepBackups: 7,
  useZip: false
})

// 备份历史数据
const backupHistory = ref([])
const backupProgress = ref({
  visible: false,
  percent: 0,
  status: 'normal', // normal, success, exception
  message: ''
})

// 修改接收备份进度的全局函数
const receiveBackupProgress = (data) => {
  try {
    console.log('收到备份进度:', data)
    const progress = typeof data === 'string' ? JSON.parse(data) : data
    
    // 更新进度信息
    backupProgress.value = {
      ...backupProgress.value,
      percent: progress.percent || 0,
      message: progress.message || '',
      status: progress.status || 'normal',
      visible: true,
      // 保存备份路径用于显示
      backupPath: progress.path || backupProgress.value.backupPath
    }
    
    // 如果备份完成，在界面停留一段时间，不自动关闭
    if (progress.status === 'success' && progress.percent === 100) {
      // 刷新备份历史
      setTimeout(() => {
        refreshBackupHistory()
      }, 500)
    }
  } catch (error) {
    console.error('处理备份进度失败:', error)
  }
}

// 注册局函数以接收备份进度
window.receiveBackupProgress = receiveBackupProgress

// 备份相关方法
const backupNow = async () => {
  try {
    if (!backupForm.value.backupDir) {
      ElMessage.warning('请先选择需要备份的目录')
      return
    }
    if (!backupForm.value.targetDir) {
      ElMessage.warning('请先选择备份保存的目录')
      return
    }

    // 重置并显示进度条
    backupProgress.value = {
      visible: true,
      percent: 0,
      status: 'normal',
      message: '正在准备备份...'
    }

    // 修正为直接调用API的备份方法
    const response = await window.pywebview.api.backup_data({
      backup_dir: backupForm.value.backupDir,
      target_dir: backupForm.value.targetDir,
      use_zip: backupForm.value.useZip
    })
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('备份成功')
      refreshBackupHistory()
    } else {
      throw new Error(result.message || '备份失败')
    }
  } catch (error) {
    backupProgress.value.status = 'exception'
    backupProgress.value.message = '备份失败：' + error.message
    ElMessage.error('备份失败：' + error.message)
  }
}

const restoreBackup = async () => {
  try {
    const response = await window.pywebview.api.select_backup_file()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      await restoreFromBackup({ path: result.data })
    }
  } catch (error) {
    ElMessage.error('选择备份文件失败：' + error.message)
  }
}

const restoreFromBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(
      '恢复备份将覆盖当前数据，是否继续？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await window.pywebview.api.restore_backup({
      backup_path: backup.path
    })
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('恢复成功，应用将在3秒后重启')
      // 延迟3秒后重启应用
      setTimeout(async () => {
        try {
          // 调用重启方法
          await window.pywebview.api.restart_application()
        } catch (error) {
          ElMessage.error('重启应用失败，请手动重启')
          console.error('重启失败:', error)
        }
      }, 3000)
    } else {
      throw new Error(result.message || '恢复失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('恢复失败：' + error.message)
    }
  }
}

const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个备份吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await window.pywebview.api.delete_backup({
      backup_path: backup.path
    })
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('删除成功')
      refreshBackupHistory()
    } else {
      throw new Error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

const refreshBackupHistory = async () => {
  try {
    console.log('开始获取备份历史, 目标目录:', backupForm.value.targetDir)
    
    const response = await window.pywebview.api.get_backup_history({
      target_dir: backupForm.value.targetDir
    })
    console.log('API返回原始数据:', response)
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('解析后的数据:', result)
    
    if (result.status === 'success') {
      const processedData = result.data.map(item => {
        console.log('处理备份项:', item)
        return {
          ...item,
          time: item.time || (item.name ? new Date(item.name.replace('backup_', '').replace('_', 'T').replace(/(\d{2})(\d{2})(\d{2})$/, '$1:$2:$3')).getTime() / 1000 : null)
        }
      })
      console.log('处理后的数据:', processedData)
      backupHistory.value = processedData
    } else {
      console.error('获取备份历史失败:', result.message)
      throw new Error(result.message || '获取备份历史失败')
    }
  } catch (error) {
    console.error('获取备份历史出错:', error)
    ElMessage.error('获取备份历史失败：' + error.message)
  }
}

const handleAutoBackupChange = async (value) => {
  try {
    await configStore.updateConfigItem('backup.autoBackup', value)
    
    // 根据开关状态启动或停止自动备份
    if (value) {
      const response = await window.pywebview.api.check_auto_backup()
      const result = typeof response === 'string' ? JSON.parse(response) : response
      if (result.status === 'success') {
        ElMessage.success('自动备份已启动')
      } else {
        throw new Error(result.message || '启动自动备份失败')
      }
    } else {
      await window.pywebview.api.stop_auto_backup()
      ElMessage.info('自动备份已停止')
    }
  } catch (error) {
    ElMessage.error('更新自动备份设置失败：' + error.message)
    backupForm.value.autoBackup = !value
  }
}

// 添加备份周期变化处理
const handleBackupIntervalChange = async (value) => {
  try {
    await configStore.updateConfigItem('backup.backupInterval', value)
  } catch (error) {
    ElMessage.error('更新备份周期失败：' + error.message)
    backupForm.value.backupInterval = configStore.backup.backupInterval
  }
}

// 添加保留备份数量变化处理
const handleKeepBackupsChange = async (value) => {
  try {
    await configStore.updateConfigItem('backup.keepBackups', value)
  } catch (error) {
    ElMessage.error('更新保留备份数量失败：' + error.message)
    backupForm.value.keepBackups = configStore.backup.keepBackups
  }
}

// 监听备份表单变化
watch(() => backupForm.value.backupInterval, handleBackupIntervalChange)
watch(() => backupForm.value.keepBackups, handleKeepBackupsChange)

// 监听备份目录变化
watch(() => backupForm.value.targetDir, (newValue) => {
  if (newValue) {
    refreshBackupHistory()
  }
})

// 初始化备份配置
const initBackupConfig = async () => {
  const config = configStore.backup || {}
  backupForm.value = {
    backupDir: config.backupDir || '',
    targetDir: config.targetDir || '',
    autoBackup: config.autoBackup || false,
    backupInterval: config.backupInterval || 30,
    keepBackups: config.keepBackups || 7,
    useZip: config.useZip || false
  }
  
  // 如果有目标目录，刷新备份历史
  if (backupForm.value.targetDir) {
    refreshBackupHistory()
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = Math.abs(Number(bytes))
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  // 根据大小决定小数位数
  let decimals = 2
  if (unitIndex === 0) decimals = 0  // 字节不显示小数
  else if (size >= 100) decimals = 1  // 大于100时只显示1位小数
  
  return `${size.toFixed(decimals)} ${units[unitIndex]}`
}

// 格式化备份时间
const formatBackupTime = (timestamp) => {
  console.log('格式化时间戳:', timestamp)
  if (!timestamp) {
    console.log('时间戳为空')
    return ''
  }
  try {
    const date = new Date(timestamp * 1000)
    console.log('转换后的日期对象:', date)
    const formatted = date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
    console.log('格式化后的时间:', formatted)
    return formatted
  } catch (error) {
    console.error('格式化时间失败:', error)
    return ''
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 初始化
onMounted(async () => {
  try {
    // 加载配置
    await configStore.loadConfig()

    // 初始化状态检查定时器
    if (statusCheckTimer.value) {
      clearInterval(statusCheckTimer.value)
    }
    statusCheckTimer.value = setInterval(startStatusCheck, 1000)

    // 立即行一次状态检查
    await startStatusCheck()

    // 初始化其他配置
    await aiRolesStore.loadRoles()
    initOpenAIConfig()
    initFeishuConfig()
    await initBackupConfig()

    // 加载AI服务商配置 - 添加true参数以强制刷新
    try {
      await Promise.race([
        loadProviders(true),
        new Promise((_, reject) => setTimeout(() => reject(new Error('初始化加载超时')), 8000))
      ])
    } catch (providerError) {
      console.error('加载AI服务商失败，但继续初始化其他模块:', providerError)
      // 确保加载状态被重置
      aiProvidersStore.loading = false
    }
    
    // 启动自动备份检查
    if (configStore.backup?.autoBackup) {
      setTimeout(() => {
        window.pywebview.api.check_auto_backup()
          .then(response => {
            const result = typeof response === 'string' ? JSON.parse(response) : response
            console.log('自动备份检查结果:', result)
          })
          .catch(err => console.error('启动自动备份失败:', err))
      }, 5000) // 等待5秒后启动，避免应用启动时资源竞争
    }
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败: ' + error.message)
    // 确保所有加载状态被重置
    configStore.loading.value = false
    aiRolesStore.loading.value = false
    aiProvidersStore.loading = false
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (statusCheckTimer.value) {
    clearInterval(statusCheckTimer.value)
    statusCheckTimer.value = null
  }
})

const userDataDirDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    name: '',
    path: '',
    port: 9222,
    isDefault: false,
    enableExtensions: false,
    extensionsPath: ''
  }
})
const userDataDirFormRef = ref(null)
const userDataDirRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '请选择路径', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1024, max: 65535, message: '端口号范围在1024-65535之间', trigger: 'blur' }
  ]
}
const aiRoleDialog = ref({
  visible: false,
  title: '添加AI角色',
  form: {
    id: '',
    name: '',
    prompt: '',
    isEnabled: true
  },
  isEdit: false
})
const aiRoleImportDialog = ref({
  visible: false,
  content: ''
})
const openaiFormRef = ref(null)
const openaiForm = ref({
  api_key: '',
  base_url: ''
})
const feishuFormRef = ref(null)
const feishuForm = ref({
  app_id: '',
  app_secret: '',
  encrypt_key: '',
  verification_token: ''
})

// 聊天设置变量和方法
const chatSettings = ref({...configStore.chat})
const originalChatSettings = ref({...configStore.chat})

// 保存聊天设置
const saveChatSettings = async () => {
  try {
    await configStore.updateConfigItem('chat', chatSettings.value)
    ElMessage.success('聊天设置保存成功')
    // 更新原始设置，用于重置功能
    originalChatSettings.value = {...chatSettings.value}
  } catch (error) {
    ElMessage.error(`保存失败: ${error}`)
  }
}

// 重置聊天设置
const resetChatSettings = () => {
  chatSettings.value = {...originalChatSettings.value}
  ElMessage.info('已重置为上次保存的设置')
}

// 添加重试备份方法
const retryBackup = async () => {
  try {
    backupProgress.value.visible = false;
    await nextTick();
    backupNow();
  } catch (error) {
    ElMessage.error('重试备份失败：' + error.message)
  }
}

// 添加压缩选项变化处理
const handleUseZipChange = async (value) => {
  try {
    await configStore.updateConfigItem('backup.useZip', value)
  } catch (error) {
    ElMessage.error('更新压缩备份设置失败：' + error.message)
    backupForm.value.useZip = !value
  }
}

// 添加表格中的恢复按钮处理函数
const restoreFromHistory = (row) => {
  restoreFromBackup(row)
}

// Git备份相关变量
const isGitInstalled = ref(false)
const gitVersionInfo = ref('')
const gitInstallStatus = ref(null) // 添加Git安装状态变量
const isGitChecking = ref(false) // 添加Git检查中状态变量
const gitConfig = ref({
  repoUrl: '',
  authType: 'token',
  username: '',
  password: '',
  token: '',
  tokenUsername: 'git',
  localPath: '',
  backupDir: '',
  autoBackup: false,
  backupInterval: 60,
  // 添加手动备份相关字段
  manualBackupInfo: {
    commitMessage: '',
    tagName: '' 
  }
})
const gitHistory = ref([])
// 添加备份对话框的ref
const backupDialog = ref({
  visible: false,
  form: {
    commitMessage: '',
    tagName: ''
  }
});
const gitDiffDialog = ref({
  visible: false,
  content: '',
  loading: false
})
const gitProgress = ref({
  visible: false,
  percent: 0,
  status: 'normal',
  message: ''
})

// 检测是否是Coding平台
const isCodingPlatform = computed(() => {
  return gitConfig.value.repoUrl && gitConfig.value.repoUrl.includes('coding.net')
})

// 修改有效性检查计算属性
const isGitConfigValid = computed(() => {
  const hasRepoAndBackupDir = gitConfig.value.repoUrl && gitConfig.value.backupDir
  
  if (!hasRepoAndBackupDir) return false
  
  if (gitConfig.value.authType === 'password') {
    return gitConfig.value.username && gitConfig.value.password
  } else { // token认证
    if (isCodingPlatform.value) {
      return gitConfig.value.token && gitConfig.value.tokenUsername
    } else {
      return !!gitConfig.value.token
    }
  }
})

// 初始化Git配置
const initGitConfig = () => {
  const config = configStore.git || {}
  gitConfig.value = {
    repoUrl: config.repoUrl || '',
    authType: config.authType || 'token',
    username: config.username || '',
    password: config.password || '',
    token: config.token || '',
    tokenUsername: config.tokenUsername || '',  // 添加此字段
    localPath: config.localPath || '',
    backupDir: config.backupDir || '',
    autoBackup: config.autoBackup || false,
    backupInterval: config.backupInterval || 60
  }
  
  // 检查Git是否安装
  //checkGitInstallation()
  
  // 如果有配置，加载Git历史
  if (isGitConfigValid.value) {
    refreshGitHistory()
  }
}

// 检查Git安装情况
const checkGitInstallation = async () => {
  try {
    isGitChecking.value = true;
    gitInstallStatus.value = null;
    
    console.log('正在检查Git安装状态...');
    const response = await window.pywebview.api.check_git_installation();
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    console.log('Git检查结果:', result);
    
    if (result.status === 'success') {
      gitInstallStatus.value = result.data;
      isGitInstalled.value = gitInstallStatus.value.installed; // 添加这行更新isGitInstalled
      
      // 显示Git安装状态
      if (gitInstallStatus.value.installed) {
        ElMessage.success(`Git已安装: ${gitInstallStatus.value.version}`);
      } else {
        ElMessage.warning('未检测到Git安装，请先安装Git才能使用备份功能');
      }
    } else {
      throw new Error(result.message || '检测Git失败');
    }
  } catch (error) {
    console.error('检查Git安装失败:', error);
    ElMessage.error(`检测Git失败: ${error.message || '未知错误'}`);
    gitInstallStatus.value = { installed: false };
    isGitInstalled.value = false; // 添加这行，确保错误时也更新状态
  } finally {
    isGitChecking.value = false;
  }
};

// 保存Git配置
const saveGitConfig = async () => {
  try {
    // 修改为使用updateConfigItem方法而不是不存在的saveGitConfig方法
    await configStore.updateConfigItem('git', {
      repoUrl: gitConfig.value.repoUrl,
      authType: gitConfig.value.authType,
      username: gitConfig.value.username,
      password: gitConfig.value.password,
      token: gitConfig.value.token,
      tokenUsername: gitConfig.value.tokenUsername,
      localPath: gitConfig.value.localPath,
      backupDir: gitConfig.value.backupDir,
      autoBackup: gitConfig.value.autoBackup,
      backupInterval: gitConfig.value.backupInterval
    });
    
    ElMessage.success('Git配置保存成功');
    
    // 不再自动初始化Git仓库
  } catch (error) {
    ElMessage.error(`保存Git配置失败: ${error.message}`);
  }
};

// 初始化Git仓库
const initGitRepository = async () => {
  try {
    if (!isGitConfigValid.value) {
      ElMessage.warning('请先完成Git配置');
      return false;
    }
    
    // 显示加载中
    const loadingInstance = ElLoading.service({
      text: '正在初始化Git仓库...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    const params = {
      repo_url: gitConfig.value.repoUrl,
      auth_type: gitConfig.value.authType,
      username: gitConfig.value.username,
      password: gitConfig.value.password,
      token: gitConfig.value.token,
      token_username: gitConfig.value.tokenUsername,
      backup_dir: gitConfig.value.backupDir,
    };
    
    const response = await window.pywebview.api.init_git_repo(params);
    
    loadingInstance.close();
    
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.status === 'success') {
      ElMessage.success('Git仓库初始化成功');
      return true;
    } else {
      ElMessage.error(`初始化Git仓库失败: ${result.message}`);
      return false;
    }
  } catch (error) {
    ElMessage.error(`初始化Git仓库失败: ${error.message || error}`);
    return false;
  }
};

// 备份到Git
const backupToGit = async () => {
  try {
    backupDialogVisible.value = true;
    backupProgress.value = 0;
    backupStatus.value = '正在准备备份...';
    
    // 构建备份参数
    const backupParams = {
      repo_url: gitConfig.value.repoUrl,
      auth_type: gitConfig.value.authType,
      username: gitConfig.value.username,
      password: gitConfig.value.password,
      token: gitConfig.value.token,
      token_username: gitConfig.value.tokenUsername,
      local_path: gitConfig.value.localPath,
      backup_dir: gitConfig.value.backupDir
    };
    
    // 更新进度
    backupProgress.value = 30;
    backupStatus.value = '正在执行备份...';
    
    const response = await window.pywebview.api.backup_to_git(backupParams);
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.status === 'success') {
      backupProgress.value = 100;
      backupStatus.value = '备份完成';
      backupResult.value = result.data || {};
      backupSuccess.value = true;
      
      // 更新历史记录
      await loadGitHistory();
    } else {
      throw new Error(result.message || '备份失败');
    }
  } catch (error) {
    backupProgress.value = 100;
    backupStatus.value = `备份失败: ${error.message}`;
    backupSuccess.value = false;
  }
};

// 修复加载Git历史记录函数，确保正确传递仓库URL和本地路径
const refreshGitHistory = async () => {
  try {
    isLoadingHistory.value = true
    historyError.value = null
    
    // 检查Git配置是否有效
    if (!gitConfig.value || !gitConfig.value.backupDir) {
      historyError.value = '仓库URL或本地路径未提供'
      gitHistory.value = []
      return
    }
    
    console.log('正在加载Git历史，配置:', gitConfig.value)
    
    // 传递所有需要的参数
    const response = await window.pywebview.api.get_git_history({
      repo_url: gitConfig.value.repoUrl,  // 添加仓库URL
      local_path: gitConfig.value.backupDir,
      count: 50
    })
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    console.log('Git历史响应:', result)
    
    if (result.status === 'success') {
      // 处理返回的历史记录，清理哈希值中的引号
      gitHistory.value = (result.history || []).map(commit => ({
        ...commit,
        hash: commit.hash.replace(/"/g, '').trim(),
        message: commit.message.replace(/"/g, '').trim()
      }))
    } else {
      historyError.value = result.message || '加载历史记录失败'
      gitHistory.value = []
    }
  } catch (error) {
    console.error('加载Git历史错误:', error)
    historyError.value = `加载历史记录失败: ${error.message || error}`
    gitHistory.value = []
  } finally {
    isLoadingHistory.value = false
  }
}

// 查看Git差异
const viewGitDiff = async (commit) => {
  try {
    gitDiffDialog.value.visible = true
    gitDiffDialog.value.loading = true
    gitDiffDialog.value.content = '加载中...'
    
    const response = await window.pywebview.api.get_git_diff({
      local_path: gitConfig.value.localPath,
      commit_id: commit.commitId
    })
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      gitDiffDialog.value.content = result.data
    } else {
      throw new Error(result.message || '获取差异失败')
    }
  } catch (error) {
    gitDiffDialog.value.content = `获取差异失败: ${error.message}`
    ElMessage.error(`获取差异失败: ${error.message}`)
  } finally {
    gitDiffDialog.value.loading = false
  }
}

// 从Git恢复
const restoreFromGit = async (commit) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复到此版本 (${commit.commitId.substring(0, 7)})？当前数据将会被覆盖。`,
      '恢复确认',
      {
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    gitProgress.value = {
      visible: true,
      percent: 0,
      status: 'normal',
      message: '正在从Git恢复...'
    }
    
    const response = await window.pywebview.api.restore_from_git({
      local_path: gitConfig.value.localPath,
      commit_id: commit.commitId,
      backup_dir: gitConfig.value.backupDir
    })
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      gitProgress.value.percent = 100
      gitProgress.value.status = 'success'
      gitProgress.value.message = '恢复成功'
      
      ElMessage.success('Git恢复成功，应用将在3秒后重启')
      // 延迟3秒后重启应用
      setTimeout(async () => {
        try {
          // 调用重启方法
          await window.pywebview.api.restart_application()
        } catch (error) {
          ElMessage.error('重启应用失败，请手动重启')
          console.error('重启失败:', error)
        }
      }, 3000)
    } else {
      throw new Error(result.message || '恢复失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      gitProgress.value.status = 'exception'
      gitProgress.value.message = `恢复失败: ${error.message}`
      ElMessage.error(`Git恢复失败: ${error.message}`)
    }
  }
}

// 处理自动备份开关变化
const handleGitAutoBackupChange = async (value) => {
  try {
    await configStore.updateConfigItem('git.autoBackup', value)
    
    if (value) {
      const response = await window.pywebview.api.start_auto_git_backup()
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result.status === 'success') {
        ElMessage.success('Git自动备份已启动')
      } else {
        throw new Error(result.message || '启动Git自动备份失败')
      }
    } else {
      await window.pywebview.api.stop_auto_git_backup()
      ElMessage.info('Git自动备份已停止')
    }
  } catch (error) {
    ElMessage.error(`更新Git自动备份设置失败: ${error.message}`)
    gitConfig.value.autoBackup = !value
  }
}

// 格式化Git时间
const formatGitTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 重试Git备份
const retryGitBackup = () => {
  gitProgress.value.visible = false
  setTimeout(() => {
    backupToGit()
  }, 100)
}

// 在初始化时添加Git配置
onMounted(() => {
  // ... 现有代码保持不变 ...
  initGitConfig()
})

// 测试Git凭据
const testGitCredentials = async () => {
  try {
    if (!gitConfig.value.repoUrl) {
      ElMessage.warning('请填写Git仓库URL');
      return;
    }
    
    // 确保必要参数存在
    const params = {
      repo_url: gitConfig.value.repoUrl,
      auth_type: gitConfig.value.authType,
    };
    
    // 根据认证类型添加不同的参数
    if (gitConfig.value.authType === 'password') {
      if (!gitConfig.value.username || !gitConfig.value.password) {
        ElMessage.warning('请填写用户名和密码');
        return;
      }
      params.username = gitConfig.value.username;
      params.password = gitConfig.value.password;
    } else { // token认证
      if (!gitConfig.value.token) {
        ElMessage.warning('请填写访问令牌');
        return;
      }
      params.token = gitConfig.value.token;
      
      // 对于Coding平台，需要额外的用户名
      if (isCodingPlatform.value) {
        if (!gitConfig.value.tokenUsername) {
          ElMessage.warning('Coding平台需要填写用户名');
          return;
        }
        params.token_username = gitConfig.value.tokenUsername;
      }
    }
    
    // 显示加载中提示
    const loadingInstance = ElLoading.service({
      text: '正在测试Git连接...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    console.log('发送测试连接请求参数:', params);
    const response = await window.pywebview.api.test_git_credentials(params);
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    console.log('测试连接响应:', result);
    loadingInstance.close();
    
    if (result.status === 'success') {
      ElMessage.success(result.message || 'Git凭据验证成功');
    } else {
      throw new Error(result.message || 'Git凭据验证失败');
    }
  } catch (error) {
    ElMessage.error(`Git连接测试失败: ${error.message}`);
  }
};

// 显示Token帮助对话框
const tokenHelpVisible = ref(false)
const showTokenHelp = () => {
  tokenHelpVisible.value = true
}

// 使用 immediate 选项确保 watch 立即执行
watch(
  activeTab,
  (newTab) => {
    console.log('Tab切换为:', newTab);
    if (newTab === 'git-backup') {
      console.log('加载Git配置和检测Git安装');
      loadGitConfig();
      checkGitInstallation(); // 添加这行确保自动检测
    } else if (newTab === 'editor') {
      initEditorSettings();
    } else if (newTab === 'system') {
      initSystemSettings();
    } else if (newTab === 'chat') {
      initChatSettings();
    }
  },
  { immediate: true }
);

// 修改 loadGitConfig 函数
const loadGitConfig = () => {
  console.log('加载Git配置...');
  // 从configStore获取Git配置并更新本地状态
  const config = configStore.git || {};
  console.log('获取到的Git配置:', config);
  
  gitConfig.value = {
    repoUrl: config.repoUrl || '',
    authType: config.authType || 'token',
    username: config.username || '',
    password: config.password || '',
    token: config.token || '',
    tokenUsername: config.tokenUsername || '',
    localPath: config.localPath || '',
    backupDir: config.backupDir || '',
    autoBackup: config.autoBackup || false,
    backupInterval: config.backupInterval || 60
  };
  
  console.log('设置后的gitConfig:', gitConfig.value);
};

// 确保在组件初始化时立即调用 loadGitConfig
onMounted(() => {
  loadGitConfig();
  //checkGitInstallation();
});

// 添加 loadGitHistory 函数
const loadGitHistory = async () => {
  try {
    if (!gitConfig.value.repoUrl || !gitConfig.value.backupDir) {
      // 如果没有配置仓库URL或备份目录，则不加载历史记录
      gitHistory.value = [];
      return;
    }
    
    const loadingInstance = ElLoading.service({
      target: '.git-history',
      text: '加载Git历史记录...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    const params = {
      repo_url: gitConfig.value.repoUrl,
      auth_type: gitConfig.value.authType,
      username: gitConfig.value.username,
      password: gitConfig.value.password,
      token: gitConfig.value.token,
      token_username: gitConfig.value.tokenUsername,
      local_path: gitConfig.value.localPath,
      backup_dir: gitConfig.value.backupDir
    };
    
    const response = await window.pywebview.api.get_git_history(params);
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    loadingInstance.close();
    
    if (result.status === 'success') {
      // 限制只显示最新的50条记录
      gitHistory.value = (result.data || []).slice(0, 50);
    } else {
      throw new Error(result.message || '获取Git历史记录失败');
    }
  } catch (error) {
    console.error('加载Git历史记录失败:', error);
    gitHistory.value = [];
    ElMessage.error(`加载Git历史记录失败: ${error.message}`);
  }
};

// 手动提交备份相关状态
const manualCommitDialogVisible = ref(false)
const manualCommitForm = reactive({
  message: '',
  force: false,
  tagName: '' // 新增标签字段
})
const isSubmittingCommit = ref(false)

// 显示手动提交对话框
const showManualCommitDialog = () => {
  if (!isGitConfigValid.value) {
    ElMessage.warning('请先完成Git配置并保存')
    return
  }
  manualCommitDialogVisible.value = true
}

// 提交手动备份
const submitManualCommit = async () => {
  try {
    if (!manualCommitForm.message.trim()) {
      ElMessage.warning('请输入提交说明')
      return
    }
    
    // 检查是否包含非ASCII字符
    const hasNonAscii = /[^\x00-\x7F]/.test(manualCommitForm.message)
    if (hasNonAscii) {
      await ElMessageBox.confirm(
        '提交信息包含中文或特殊字符，可能导致备份失败。建议仅使用英文字母、数字和基本符号。是否继续？',
        '编码警告',
        {
          confirmButtonText: '继续备份',
          cancelButtonText: '返回修改',
          type: 'warning'
        }
      )
    }
    
    // 使用解构赋值确保获取正确的值
    const { repoUrl, authType, username, password, token, tokenUsername, backupDir } = gitConfig.value
    
    // 验证必要的路径
    if (!backupDir) {
      ElMessage.error('备份目录不能为空，请在Git配置中填写');
      return;
    }
    
    // 验证备份目录存在
    try {
      const dirExists = await window.pywebview.api.check_path_exists({ path: backupDir });
      if (!dirExists || !dirExists.exists) {
        ElMessage.error(`备份目录不存在: ${backupDir}`);
        return;
      }
    } catch (error) {
      ElMessage.error(`验证备份目录失败: ${error.message || error}`);
      return;
    }
    
    isSubmittingCommit.value = true
    
    // 显示加载中提示
    const loadingInstance = ElLoading.service({
      text: '准备备份...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    try {
      // 构建备份参数 - 移除localPath，直接使用backupDir
      const backupParams = {
        repo_url: repoUrl,
        auth_type: authType,
        username: username,
        password: password,
        token: token,
        token_username: tokenUsername,
        backup_dir: backupDir,
        commit_message: manualCommitForm.message.trim(),
        tag_name: manualCommitForm.tagName, // 添加标签名参数
        force: manualCommitForm.force // 添加强制备份参数
      }
      
      // 打印参数便于排查问题
      console.log('备份参数:', {
        ...backupParams,
        password: '***', // 安全起见隐藏密码/令牌
        token: '***'
      });
      
      loadingInstance.text = '正在提交备份...';
      const response = await window.pywebview.api.backup_to_git(backupParams)
      
      loadingInstance.close()
      
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result.status === 'success') {
        ElMessage.success('备份提交成功')
        manualCommitDialogVisible.value = false
        manualCommitForm.message = ''
        
        // 刷新历史记录
        await refreshGitHistory()
      } else if (result.status === 'info') {
        ElMessage.info(result.message)
        manualCommitDialogVisible.value = false
      } else if (result.status === 'warning') {
        // 处理警告状态 - 部分成功
        ElMessageBox.alert(
          result.message,
          '备份部分成功',
          {
            type: 'warning',
            confirmButtonText: '确定'
          }
        )
        manualCommitDialogVisible.value = false
      } else if (result.status === 'error' && result.message.includes('nothing to commit')) {
        // 特殊处理：提示用户先清空暂存区再重试
        ElMessageBox.confirm(
          '似乎文件已被暂存但尚未成功提交。要清空暂存区并重试吗？',
          '需要重置Git状态',
          {
            confirmButtonText: '清空暂存区',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(async () => {
          // 用户选择清空暂存区
          try {
            await window.pywebview.api.reset_git_changes({
              backup_dir: gitConfig.value.backupDir,
              mode: 'mixed'
            })
            ElMessage.success('已清空暂存区，请重新提交')
          } catch (e) {
            ElMessage.error(`重置失败: ${e.message || e}`)
          }
        }).catch(() => {})
      } else {
        throw new Error(result.message || '备份提交失败')
      }
    } catch (error) {
      loadingInstance.close()
      ElMessage.error(`备份提交失败: ${error.message || error}`)
    }
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error(`备份操作出错: ${error.message || error}`)
  } finally {
    isSubmittingCommit.value = false
  }
}

// 添加初始化Git仓库的单独按钮和方法
const initGitRepo = async () => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      '初始化仓库将创建一个新的Git仓库连接，确定要继续吗？',
      '初始化确认',
      {
        confirmButtonText: '确认初始化',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const loadingInstance = ElLoading.service({
      text: '正在初始化Git仓库...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    await initGitRepository()
    
    loadingInstance.close()
    ElMessage.success('Git仓库初始化成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`初始化Git仓库失败: ${error.message || error}`)
    }
  }
}

// 改进备份错误处理
const handleBackupError = (error) => {
  if (typeof error === 'string' && error.includes('UnicodeDecodeError')) {
    ElMessage.error('备份失败：编码错误，提交信息中包含特殊字符，请使用简单的中文和英文字符');
  } else if (typeof error === 'string' && error.includes('Authentication failed')) {
    ElMessage.error('备份失败：Git认证失败，请检查您的用户名和密码/Token');
  } else if (typeof error === 'string' && error.includes('Could not resolve host')) {
    ElMessage.error('备份失败：无法连接到Git服务器，请检查网络连接和仓库URL');
  } else {
    ElMessage.error(`备份失败：${error.message || error}`);
  }
};

// 在备份函数中使用
try {
  // 备份代码...
} catch (error) {
  handleBackupError(error);
}

// 添加模板相关逻辑
const applyTemplate = () => {
  if (manualCommitForm.template) {
    manualCommitForm.message = manualCommitForm.template
    manualCommitForm.template = ''
  }
}

// 提交前检查路径是否存在
const validateGitPaths = async (localPath, backupDir) => {
  if (!localPath || !backupDir) {
    throw new Error("Git仓库路径或备份目录不能为空");
  }
  
  try {
    // 检查路径是否存在
    const localPathExists = await window.pywebview.api.check_path_exists({ path: localPath });
    const backupDirExists = await window.pywebview.api.check_path_exists({ path: backupDir });
    
    if (!localPathExists || !localPathExists.exists) {
      throw new Error(`本地仓库路径不存在: ${localPath}`);
    }
    
    if (!backupDirExists || !backupDirExists.exists) {
      throw new Error(`备份目录不存在: ${backupDir}`);
    }
    
    return true;
  } catch (error) {
    console.error("路径验证错误:", error);
    throw error;
  }
};

// Git命令处理
const handleGitCommand = async (command) => {
  try {
    if (!gitConfig.value.backupDir) {
      ElMessage.error('备份目录不能为空，请先配置')
      return
    }
    
    if (command === 'checkStatus') {
      // 检查Git状态
      const response = await window.pywebview.api.check_git_status({
        backup_dir: gitConfig.value.backupDir
      })
      
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result.status === 'success') {
        const status = result.git_status
        
        // 显示状态信息
        ElMessageBox.alert(
          `<div style="text-align:left">
            <p><strong>当前分支:</strong> ${status.branch}</p>
            <p><strong>上次提交:</strong> ${status.last_commit}</p>
            <p><strong>是否有变更:</strong> ${status.has_changes ? '是' : '否'}</p>
            <p><strong>是否有暂存文件:</strong> ${status.has_staged ? '是' : '否'}</p>
            ${status.status_details ? `<p><strong>详细状态:</strong><br><pre>${status.status_details}</pre></p>` : ''}
          </div>`,
          'Git仓库状态',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定'
          }
        )
      } else {
        ElMessage.error(result.message || '检查状态失败')
      }
    } else if (['resetSoft', 'resetMixed', 'resetHard'].includes(command)) {
      // 确认重置操作
      let confirmMsg = '确定要撤销上次提交吗？'
      let confirmType = 'warning'
      
      if (command === 'resetMixed') {
        confirmMsg = '确定要重置并清空暂存区吗？这将保留文件更改，但撤销暂存操作。'
      } else if (command === 'resetHard') {
        confirmMsg = '警告：此操作将丢失所有未提交的文件更改，无法恢复！确定要强制重置吗？'
        confirmType = 'error'
      }
      
      try {
        await ElMessageBox.confirm(
          confirmMsg,
          'Git仓库重置确认',
          {
            confirmButtonText: '确定重置',
            cancelButtonText: '取消',
            type: confirmType
          }
        )
        
        // 执行重置
        const mode = command.replace('reset', '').toLowerCase()
        const response = await window.pywebview.api.reset_git_changes({
          backup_dir: gitConfig.value.backupDir,
          mode: mode
        })
        
        const result = typeof response === 'string' ? JSON.parse(response) : response
        
        if (result.status === 'success') {
          ElMessage.success(result.message)
          // 刷新历史记录
          await refreshGitHistory()
        } else {
          ElMessage.error(result.message || '重置操作失败')
        }
      } catch (e) {
        if (e !== 'cancel') {
          ElMessage.error(`重置操作出错: ${e.message || e}`)
        }
      }
    } else if (command === 'cleanStaged') {
      // 清空暂存区
      const response = await window.pywebview.api.reset_git_changes({
        backup_dir: gitConfig.value.backupDir,
        mode: 'mixed'
      })
      
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result.status === 'success') {
        ElMessage.success('已清空暂存区')
      } else {
        ElMessage.error(result.message || '清空暂存区失败')
      }
    }
  } catch (error) {
    ElMessage.error(`Git操作失败: ${error.message || error}`)
  }
}

// 显示提交详情
const viewCommitDetails = async (commit) => {
  try {
    // 显示加载中
    gitDiffDialog.value.loading = true
    gitDiffDialog.value.visible = true
    gitDiffDialog.value.content = '加载中...'
    
    // 清理提交哈希中的引号
    const cleanHash = commit.hash.replace(/"/g, '').trim()
    
    console.log('查看提交详情:', cleanHash, gitConfig.value.backupDir)
    
    const response = await window.pywebview.api.get_git_commit_details({
      commit_hash: cleanHash,
      repo_path: gitConfig.value.backupDir
    })
    
    console.log('提交详情响应:', response)
    
    if (response.status === 'success') {
      gitDiffDialog.value.content = response.diff || '此提交没有变更'
    } else {
      throw new Error(response.message || '获取提交详情失败')
    }
  } catch (error) {
    console.error('查看提交详情错误:', error)
    gitDiffDialog.value.content = `加载提交详情失败: ${error.message}`
    ElMessage.error(`查看提交详情失败: ${error.message}`)
  } finally {
    gitDiffDialog.value.loading = false
  }
}

// 确认恢复到特定提交
const confirmResetToCommit = async (commit) => {
  try {
    // 清理提交哈希中的引号
    const cleanHash = commit.hash.replace(/"/g, '').trim()
    
    await ElMessageBox.confirm(
      `确定要恢复到此版本吗？\n
      提交: ${cleanHash.substring(0, 7)} - ${commit.message}\n
      日期: ${commit.date}\n
      作者: ${commit.author}\n\n
      此操作会丢失当前未提交的更改!`,
      '恢复确认',
      {
        confirmButtonText: '确认恢复',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const loadingInstance = ElLoading.service({
      text: '恢复中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    const response = await window.pywebview.api.reset_to_commit({
      backup_dir: gitConfig.value.backupDir,
      commit_hash: cleanHash
    })
    
    loadingInstance.close()
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      // 使用更醒目的成功提示
      await ElMessageBox.alert(
        `<div style="text-align:center;">
          <i class="el-icon-success" style="font-size: 48px; color: #67C23A; margin-bottom: 20px;"></i>
          <h2 style="margin-bottom: 15px;">恢复成功</h2>
          <p>已成功恢复到版本: ${cleanHash.substring(0, 7)}</p>
          <p>提交说明: ${commit.message}</p>
          <p>恢复时间: ${new Date().toLocaleString()}</p>
        </div>`,
        '操作成功',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          callback: () => {
            ElMessage.success('版本恢复完成')
          }
        }
      )
      
      await refreshGitHistory() // 刷新历史记录
    } else {
      // 显示失败提示
      ElMessageBox.alert(
        `<div style="text-align:center;">
          <i class="el-icon-error" style="font-size: 48px; color: #F56C6C; margin-bottom: 20px;"></i>
          <h2 style="margin-bottom: 15px;">恢复失败</h2>
          <p>${result.message || '恢复操作未能完成'}</p>
        </div>`,
        '操作失败',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          type: 'error'
        }
      )
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`恢复操作失败: ${error.message || error}`)
    }
  }
}

// 获取备份状态信息
const getBackupStatus = async () => {
  try {
    const response = await window.pywebview.api.get_backup_status();
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.status === 'success') {
      backupStatus.value = {
        isEnabled: result.data.auto_backup_enabled,
        inProgress: result.data.backup_in_progress,
        lastBackupTime: result.data.last_backup_time || '从未备份'
      };
    }
  } catch (error) {
    console.error('获取备份状态失败:', error);
  }
};

// 定期更新备份状态
let statusUpdateTimer;
onMounted(() => {
  loadGitConfig();
  getBackupStatus();
  
  // 每30秒更新一次状态
  statusUpdateTimer = setInterval(getBackupStatus, 30000);
});

onUnmounted(() => {
  if (statusUpdateTimer) {
    clearInterval(statusUpdateTimer);
  }
});

// 添加差异对比相关的响应式状态
const diffDialogVisible = ref(false)
const diffText = ref('')
const changedFiles = ref([])
const loadingDiff = ref(false)
const diffError = ref('')

// 添加查看变更内容的方法
const showDiffDialog = async () => {
  try {
    diffError.value = ''
    loadingDiff.value = true
    diffDialogVisible.value = true
    
    const response = await window.pywebview.api.get_git_diff({
      backup_dir: gitConfig.value.backupDir
    })
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      diffText.value = result.data.diff
      changedFiles.value = result.data.changed_files
      
      if (!diffText.value.trim() && changedFiles.value.length === 0) {
        diffError.value = '没有检测到任何变更'
      }
    } else {
      diffError.value = result.message || '获取变更内容失败'
    }
  } catch (error) {
    diffError.value = `加载变更内容失败: ${error.message || error}`
    console.error('加载变更内容失败:', error)
  } finally {
    loadingDiff.value = false
  }
}

// 添加日期格式化函数到您的script部分
// 格式化日期显示，添加到script部分
const formatDate = (dateString) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', { 
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '-');
  } catch (e) {
    return dateString;
  }
};

// 添加这个函数作为别名
const restoreGitCommit = (commit) => {
  return confirmResetToCommit(commit);
};

// AI服务商配置
 const aiActiveProviders = ref([]);

// 添加新的服务商
 const addProvider = () => {
  const newProvider = {
    name: '新服务商',
    baseUrl: '',
    apiKeys: [],
    models: []
  };
  
  const provider = aiProvidersStore.addProvider(newProvider);
  aiActiveProviders.value = [provider.id];
};

 const setDefaultProvider = (providerId, isDefault) => {
  // 此功能已弃用 - 现在所有服务商都会被启用
  ElMessage.info('所有配置了API密钥的服务商都会被自动启用，无需设置默认服务商');
};

 const removeProvider = (index) => {
  ElMessageBox.confirm(
    '确定要删除该服务商配置吗？此操作不可恢复。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const provider = aiProvidersStore.providers[index];
    aiProvidersStore.removeProvider(provider.id);
    ElMessage.success('服务商已删除');
  }).catch(() => {});
};

 const addApiKey = (providerId) => {
  aiProvidersStore.addApiKey(providerId);
};

 const removeApiKey = (providerId, keyIndex) => {
  aiProvidersStore.removeApiKey(providerId, keyIndex);
};

 const testApiKey = async (providerId, keyId) => {
  await aiProvidersStore.testApiKey(providerId, keyId);
};

 const addModel = (providerId) => {
  // 使用简单的HTML字符串创建对话框内容
  const htmlContent = `
    <div class="add-model-form" style="padding: 10px;">
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">模型ID (必填):</label>
        <input
          id="model-id-input"
          class="el-input__inner"
          placeholder="例如: gpt-4-turbo"
          style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px;"
        />
      </div>
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">显示别名 (可选):</label>
        <input
          id="model-name-input"
          class="el-input__inner"
          placeholder="例如: GPT-4 Turbo (留空则使用模型ID)"
          style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px;"
        />
      </div>
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">
          <input
            id="model-available-input"
            type="checkbox"
            checked
            style="margin-right: 8px;"
          />
          启用此模型
        </label>
      </div>
      <div class="form-tip" style="font-size: 12px; color: #909399; line-height: 1.4;">
        • 模型ID用于与AI服务商通信，必须准确<br/>
        • 显示别名用于界面显示，可以自定义以避免重复<br/>
        • 只有启用的模型才会在选择器中显示
      </div>
    </div>
  `;

  ElMessageBox({
    title: '添加模型',
    dangerouslyUseHTMLString: true,
    message: htmlContent,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        const modelId = document.getElementById('model-id-input')?.value?.trim();
        const modelName = document.getElementById('model-name-input')?.value?.trim();
        const modelAvailable = document.getElementById('model-available-input')?.checked;

        if (!modelId) {
          ElMessage.warning('请输入模型ID');
          return;
        }

        // 检查模型ID是否已存在
        const provider = aiProvidersStore.providers.find(p => p.id === providerId);
        if (provider?.models?.some(m => m.id === modelId)) {
          ElMessage.warning('该模型ID已存在');
          return;
        }

        try {
          aiProvidersStore.addModel(providerId, {
            id: modelId,
            name: modelName || modelId,
            available: modelAvailable !== false
          });
          ElMessage.success('模型添加成功');
          done();
        } catch (error) {
          ElMessage.error('添加模型失败: ' + error.message);
        }
      } else {
        done();
      }
    }
  });
};

 const removeModel = (providerId, modelIndex) => {
  aiProvidersStore.removeModel(providerId, modelIndex);
};

// 验证模型ID
const validateModelId = (providerId, model) => {
  if (!model.id || !model.id.trim()) {
    ElMessage.warning('模型ID不能为空');
    return false;
  }

  // 检查是否有重复的模型ID
  const provider = aiProvidersStore.providers.find(p => p.id === providerId);
  if (provider?.models) {
    const duplicates = provider.models.filter(m => m.id === model.id);
    if (duplicates.length > 1) {
      ElMessage.warning('模型ID不能重复');
      return false;
    }
  }

  // 如果别名为空，使用模型ID作为别名
  if (!model.name || !model.name.trim()) {
    model.name = model.id;
  }

  return true;
};

// 处理模型可用性变化
const handleModelAvailableChange = (model) => {
  console.log(`模型 ${model.name} (${model.id}) 可用性变更为: ${model.available}`);
  // 这里可以添加额外的逻辑，比如自动保存配置
};

 const fetchModels = async (providerId) => {
  try {
    await aiProvidersStore.fetchModels(providerId);
  } catch (error) {
    console.error('获取模型错误', error);
  }
};

 const saveProviderConfig = async () => {
  try {
    await aiProvidersStore.saveProviders();
    ElMessage.success('AI服务商配置保存成功');
  } catch (error) {
    console.error('保存服务商配置错误', error);
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`);
  }
};

 // Update the loadProviders function to accept a forceRefresh parameter
 const loadProviders = async (forceRefresh = false) => {
  try {
    // Either force refresh or only load if not initialized
    if (forceRefresh || !aiProvidersStore.initialized) {
      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('加载AI服务商配置超时')), 10000);
      });
      
      // 与API调用竞争，哪个先完成就用哪个结果
      await Promise.race([
        aiProvidersStore.loadProviders(forceRefresh),
        timeoutPromise
      ]);
      
      // If there are providers, open the first one
      if (aiProvidersStore.providers.length > 0) {
        aiActiveProviders.value = [aiProvidersStore.providers[0].id];
      }
    }
  } catch (error) {
    console.error('加载服务商配置错误', error);
    // 确保出错时也重置loading状态
    aiProvidersStore.loading = false;
  }
};

// 在初始化时加载AI服务商配置
// onMounted(() => {
//   // 在现有的onMounted函数基础上添加此功能
//   setTimeout(() => {
//     loadProviders();
//   }, 500);
// });

// 监听activeTab变化，当切换到openai-config标签时加载服务商配置
watch(activeTab, (newValue) => {
  if (newValue === 'openai-config') {
    // Load providers if not initialized, but don't force refresh
    loadProviders();
  }
});

// 添加强制刷新AI角色列表功能
 const refreshAIRoles = async () => {
  try {
    await aiRolesStore.loadRoles(true) // 传递true表示强制刷新
    ElMessage.success('AI角色列表已刷新')
  } catch (error) {
    ElMessage.error('刷新AI角色失败: ' + error.message)
  }
}

// Add a function to refresh AI providers
const refreshProviders = async () => {
  try {
    await loadProviders(true) // 强制刷新
    ElMessage.success('AI服务商配置已刷新')
  } catch (error) {
    ElMessage.error('刷新AI服务商配置失败: ' + error.message)
  }
}

// 模型参数配置对话框状态
const modelConfigDialog = ref({
  visible: false,
  providerId: '',
  modelId: '',
  modelName: '',
  form: {
    temperature: 0.8,
    max_tokens: 8192,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: true
  }
})

// 打开模型配置对话框
const openModelConfigDialog = (providerId, model) => {
  modelConfigDialog.value.visible = true
  modelConfigDialog.value.providerId = providerId
  modelConfigDialog.value.modelId = model.id
  modelConfigDialog.value.modelName = model.name || model.id

  // 初始化表单数据，如果模型已有配置则使用现有配置，否则使用默认值
  if (model.config) {
    modelConfigDialog.value.form = {
      temperature: model.config.temperature ?? 0.8,
      max_tokens: model.config.max_tokens ?? 4096,
      top_p: model.config.top_p ?? 0.8,
      frequency_penalty: model.config.frequency_penalty ?? 0,
      presence_penalty: model.config.presence_penalty ?? 0,
      stream: model.config.stream ?? true
    }
  } else {
    // 如果没有配置，使用默认值并为模型添加config字段
    model.config = {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
    modelConfigDialog.value.form = { ...model.config }
  }
}

// 保存模型配置
const saveModelConfig = async () => {
  try {
    const provider = aiProvidersStore.providers.find(p => p.id === modelConfigDialog.value.providerId)
    if (!provider) {
      ElMessage.error('未找到对应的服务商')
      return
    }

    const model = provider.models.find(m => m.id === modelConfigDialog.value.modelId)
    if (!model) {
      ElMessage.error('未找到对应的模型')
      return
    }

    // 更新模型配置
    model.config = { ...modelConfigDialog.value.form }

    // 保存到后端
    await aiProvidersStore.saveProviders()

    modelConfigDialog.value.visible = false
    ElMessage.success('模型参数配置保存成功')
  } catch (error) {
    console.error('保存模型配置失败:', error)
    ElMessage.error('保存模型配置失败: ' + error.message)
  }
}

// 重置模型配置为默认值
const resetModelConfigToDefault = () => {
  modelConfigDialog.value.form = {
    temperature: 0.8,
    max_tokens: 8192,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: true
  }
  ElMessage.info('已重置为默认配置')
}
