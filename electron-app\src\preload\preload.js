const { contextBridge, ipcRenderer } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 通用API调用
  invoke: (method, ...args) => ipcRenderer.invoke('api-call', method, ...args),
  
  // 文件对话框
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  
  // 应用信息
  getAppInfo: () => ipcRenderer.invoke('get-app-info'),
  
  // 窗口控制
  windowMinimize: () => ipcRenderer.invoke('window-minimize'),
  windowMaximize: () => ipcRenderer.invoke('window-maximize'),
  windowClose: () => ipcRenderer.invoke('window-close'),
  
  // 外部链接和文件
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showItemInFolder: (path) => ipcRenderer.invoke('show-item-in-folder', path),
  
  // 事件监听
  on: (channel, callback) => {
    const validChannels = ['menu-action', 'app-update', 'theme-changed'];
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },
  
  // 移除事件监听
  removeListener: (channel, callback) => {
    const validChannels = ['menu-action', 'app-update', 'theme-changed'];
    if (validChannels.includes(channel)) {
      ipcRenderer.removeListener(channel, callback);
    }
  },
  
  // 发送事件到主进程
  send: (channel, data) => {
    const validChannels = ['renderer-ready', 'theme-change-request'];
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data);
    }
  }
});

// 兼容性API - 模拟pywebview.api的接口
contextBridge.exposeInMainWorld('pywebview', {
  api: {
    // 项目管理
    get_projects: () => window.electronAPI.invoke('get_projects'),
    create_project: (projectData) => window.electronAPI.invoke('create_project', projectData),
    update_project: (projectId, projectData) => window.electronAPI.invoke('update_project', projectId, projectData),
    delete_project: (projectId) => window.electronAPI.invoke('delete_project', projectId),
    
    // 配置管理
    get_settings: () => window.electronAPI.invoke('get_settings'),
    update_config: (key, value) => window.electronAPI.invoke('update_config', key, value),
    load_config: () => window.electronAPI.invoke('load_config'),
    
    // 书籍管理
    get_books: (projectId) => window.electronAPI.invoke('get_books', projectId),
    create_book: (projectId, bookData) => window.electronAPI.invoke('create_book', projectId, bookData),
    update_book: (bookId, bookData) => window.electronAPI.invoke('update_book', bookId, bookData),
    delete_book: (bookId) => window.electronAPI.invoke('delete_book', bookId),
    
    // 章节管理
    get_chapters: (bookId) => window.electronAPI.invoke('get_chapters', bookId),
    create_chapter: (bookId, chapterData) => window.electronAPI.invoke('create_chapter', bookId, chapterData),
    update_chapter: (chapterId, chapterData) => window.electronAPI.invoke('update_chapter', chapterId, chapterData),
    delete_chapter: (chapterId) => window.electronAPI.invoke('delete_chapter', chapterId),
    
    // 角色管理
    get_characters: (bookId) => window.electronAPI.invoke('get_characters', bookId),
    create_character: (bookId, characterData) => window.electronAPI.invoke('create_character', bookId, characterData),
    update_character: (characterId, characterData) => window.electronAPI.invoke('update_character', characterId, characterData),
    delete_character: (characterId) => window.electronAPI.invoke('delete_character', characterId),
    
    // 设定管理
    get_settings_data: (bookId) => window.electronAPI.invoke('get_settings_data', bookId),
    save_settings_data: (bookId, settingsData) => window.electronAPI.invoke('save_settings_data', bookId, settingsData),
    
    // 时间线管理
    get_timeline: (bookId) => window.electronAPI.invoke('get_timeline', bookId),
    save_timeline: (bookId, timelineData) => window.electronAPI.invoke('save_timeline', bookId, timelineData),
    
    // AI相关
    get_ai_roles: () => window.electronAPI.invoke('get_ai_roles'),
    add_ai_role: (roleData) => window.electronAPI.invoke('add_ai_role', roleData),
    update_ai_role: (roleId, roleData) => window.electronAPI.invoke('update_ai_role', roleId, roleData),
    delete_ai_role: (roleId) => window.electronAPI.invoke('delete_ai_role', roleId),
    chat_with_ai: (messages, config) => window.electronAPI.invoke('chat_with_ai', messages, config),
    
    // 文件操作
    read_file: (filePath) => window.electronAPI.invoke('read_file', filePath),
    write_file: (filePath, content) => window.electronAPI.invoke('write_file', filePath, content),
    delete_file: (filePath) => window.electronAPI.invoke('delete_file', filePath),
    
    // 备份相关
    create_backup: () => window.electronAPI.invoke('create_backup'),
    restore_backup: (backupPath) => window.electronAPI.invoke('restore_backup', backupPath),
    get_backup_list: () => window.electronAPI.invoke('get_backup_list'),
    
    // 用户管理
    login: (userData) => window.electronAPI.invoke('login', userData),
    check_activation_status: () => window.electronAPI.invoke('check_activation_status'),
    verify_activation_code: (activationData) => window.electronAPI.invoke('verify_activation_code', activationData),
    
    // 其他功能
    open_directory: (path) => window.electronAPI.invoke('open_directory', path),
    get_machine_code: () => window.electronAPI.invoke('get_machine_code'),
    export_data: (exportConfig) => window.electronAPI.invoke('export_data', exportConfig),
    import_data: (importConfig) => window.electronAPI.invoke('import_data', importConfig)
  }
});

// 平台信息
contextBridge.exposeInMainWorld('platform', {
  isWindows: process.platform === 'win32',
  isMacOS: process.platform === 'darwin',
  isLinux: process.platform === 'linux',
  platform: process.platform,
  arch: process.arch
});

// 开发环境标识
contextBridge.exposeInMainWorld('isDev', process.env.NODE_ENV === 'development');

console.log('Preload script loaded successfully');
