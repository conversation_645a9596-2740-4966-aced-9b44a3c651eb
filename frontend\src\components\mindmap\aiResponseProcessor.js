/**
 * AI响应处理器
 * 负责处理不同大模型的响应格式，提取有效内容
 */

export class AIResponseProcessor {
  constructor() {
    this.contentExtractors = new Map()
    this.contentFilters = []
    this.setupExtractors()
    this.setupFilters()
  }

  /**
   * 设置不同模型的内容提取器
   */
  setupExtractors() {
    // Claude系列模型
    this.contentExtractors.set('claude', (response) => {
      if (typeof response === 'string') return response
      
      // 优先提取content字段，忽略thinking
      if (response.content) return response.content
      if (response.text) return response.text
      if (response.message?.content) return response.message.content
      
      return JSON.stringify(response)
    })

    // OpenAI GPT系列
    this.contentExtractors.set('gpt', (response) => {
      if (!response || response === null || response === undefined) {
        return ''
      }

      if (typeof response === 'string') return response

      if (response.choices?.[0]?.message?.content) {
        return response.choices[0].message.content
      }
      if (response.choices?.[0]?.text) {
        return response.choices[0].text
      }
      if (response.content) return response.content

      return JSON.stringify(response)
    })

    // 通义千问
    this.contentExtractors.set('qwen', (response) => {
      if (!response || response === null || response === undefined) {
        return ''
      }

      if (typeof response === 'string') return response

      if (response.output?.text) return response.output.text
      if (response.text) return response.text
      if (response.content) return response.content

      return JSON.stringify(response)
    })

    // 文心一言
    this.contentExtractors.set('ernie', (response) => {
      if (!response || response === null || response === undefined) {
        return ''
      }

      if (typeof response === 'string') return response

      if (response.result) return response.result
      if (response.content) return response.content
      if (response.text) return response.text

      return JSON.stringify(response)
    })

    // 智谱GLM
    this.contentExtractors.set('glm', (response) => {
      if (!response || response === null || response === undefined) {
        return ''
      }

      if (typeof response === 'string') return response

      if (response.choices?.[0]?.message?.content) {
        return response.choices[0].message.content
      }
      if (response.data?.choices?.[0]?.content) {
        return response.data.choices[0].content
      }
      if (response.content) return response.content

      return JSON.stringify(response)
    })

    // 通用提取器（兜底）
    this.contentExtractors.set('default', (response) => {
      if (!response || response === null || response === undefined) {
        return ''
      }

      if (typeof response === 'string') return response

      // 按优先级尝试提取
      const possibleFields = [
        'content', 'text', 'result', 'output', 'message',
        'choices[0].message.content', 'choices[0].text',
        'data.content', 'response.content'
      ]

      for (const field of possibleFields) {
        const value = this.getNestedValue(response, field)
        if (value && typeof value === 'string') {
          return value
        }
      }

      return JSON.stringify(response)
    })
  }

  /**
   * 设置内容过滤器
   */
  setupFilters() {
    // 移除思考过程标记
    this.contentFilters.push((content) => {
      const thinkingPatterns = [
        /^<thinking>[\s\S]*?<\/thinking>\s*/gmi,
        /^思考过程：[\s\S]*?(?=##|\n\n|$)/gmi,
        /^让我思考一下[\s\S]*?(?=##|\n\n|$)/gmi,
        /^分析：[\s\S]*?(?=##|\n\n|$)/gmi,
        /^我需要[\s\S]*?(?=##|\n\n|$)/gmi
      ]
      
      let filtered = content
      thinkingPatterns.forEach(pattern => {
        filtered = filtered.replace(pattern, '')
      })
      
      return filtered.trim()
    })

    // 移除模型说明和道歉
    this.contentFilters.push((content) => {
      const noisePatterns = [
        /^(好的|当然|我来|让我|我将|我会)[\s\S]*?(?=##|\n\n)/gmi,
        /^(抱歉|对不起|很抱歉)[\s\S]*?(?=##|\n\n)/gmi,
        /^(根据您的要求|按照您的需求)[\s\S]*?(?=##|\n\n)/gmi,
        /^以下是[\s\S]*?(?=##|\n\n)/gmi,
        /^这里是[\s\S]*?(?=##|\n\n)/gmi
      ]
      
      let filtered = content
      noisePatterns.forEach(pattern => {
        filtered = filtered.replace(pattern, '')
      })
      
      return filtered.trim()
    })

    // 清理重复的标题
    this.contentFilters.push((content) => {
      // 移除连续重复的标题
      return content.replace(/^(#{1,6}\s+.*?)\n+\1/gmi, '$1')
    })

    // 标准化markdown格式
    this.contentFilters.push((content) => {
      return content
        .replace(/^#+\s*/gm, (match) => {
          // 统一使用##作为主要标题级别
          return '## '
        })
        .replace(/\n{3,}/g, '\n\n') // 移除多余空行
        .trim()
    })

    // 增强的内容质量过滤器
    this.contentFilters.push((content) => {
      return this.enhancedContentFilter(content)
    })
  }

  /**
   * 增强的内容质量过滤器
   */
  enhancedContentFilter(content) {
    if (!content || typeof content !== 'string') return content

    let filtered = content

    // 1. 移除AI模型的元信息和解释
    const metaPatterns = [
      /^(我理解您的需求|根据您的要求|基于上下文信息)[\s\S]*?(?=##|$)/gmi,
      /^(为了更好地|为了帮助您|为了满足您的需求)[\s\S]*?(?=##|$)/gmi,
      /^(以下内容|下面的内容|这些内容)[\s\S]*?(?=##|$)/gmi
    ]

    metaPatterns.forEach(pattern => {
      filtered = filtered.replace(pattern, '')
    })

    // 2. 移除重复的引导语
    filtered = filtered.replace(/^(##\s*[^#\n]*)\n+\1/gmi, '$1')

    // 3. 清理空的标题和内容块
    filtered = filtered.replace(/^##\s*$\n*/gm, '')
    filtered = filtered.replace(/^##\s+\n+(?=##)/gm, '')

    // 4. 规范化段落结构
    filtered = filtered.replace(/([。！？])\s*([^#\n])/g, '$1\n\n$2')

    // 5. 移除末尾的总结性语句
    const endingPatterns = [
      /\n*希望以上[内容建议信息].*$/gmi,
      /\n*如果您需要.*$/gmi,
      /\n*请根据实际情况.*$/gmi,
      /\n*以上仅供参考.*$/gmi
    ]

    endingPatterns.forEach(pattern => {
      filtered = filtered.replace(pattern, '')
    })

    return filtered.trim()
  }

  /**
   * 处理AI响应，提取有效内容
   */
  processResponse(response, modelType = 'default') {
    try {
      // 1. 根据模型类型提取内容
      const extractor = this.contentExtractors.get(modelType) || 
                       this.contentExtractors.get('default')
      
      let content = extractor(response)
      
      // 2. 应用内容过滤器
      for (const filter of this.contentFilters) {
        content = filter(content)
      }
      
      // 3. 验证内容质量
      const quality = this.assessContentQuality(content)
      
      return {
        content: content,
        quality: quality,
        originalLength: typeof response === 'string' ? response.length : JSON.stringify(response).length,
        processedLength: content.length,
        compressionRatio: content.length / (typeof response === 'string' ? response.length : JSON.stringify(response).length)
      }
      
    } catch (error) {
      console.error('AI响应处理失败:', error)
      return {
        content: typeof response === 'string' ? response : JSON.stringify(response),
        quality: { score: 0, issues: ['处理失败'] },
        error: error.message
      }
    }
  }

  /**
   * 评估内容质量
   */
  assessContentQuality(content) {
    const issues = []
    let score = 100
    const metrics = {}

    // 基础检查
    if (!content || content.trim().length === 0) {
      issues.push('内容为空')
      score -= 50
      return { score: 0, issues, metrics }
    }

    // 1. 标题结构检查
    const titleMatches = content.match(/^#{1,6}\s+.+$/gm) || []
    const titleCount = titleMatches.length
    metrics.titleCount = titleCount

    if (titleCount === 0) {
      issues.push('缺少标题结构')
      score -= 25
    } else if (titleCount < 2) {
      issues.push('标题数量偏少')
      score -= 10
    }

    // 2. 内容长度和密度检查
    const wordCount = content.length
    metrics.wordCount = wordCount

    if (wordCount < 50) {
      issues.push('内容过短')
      score -= 20
    } else if (wordCount < 100) {
      issues.push('内容较短')
      score -= 10
    }

    // 3. 内容质量检查
    const qualityIssues = this.checkContentQuality(content)
    issues.push(...qualityIssues.issues)
    score -= qualityIssues.penalty

    // 4. 结构完整性检查
    const structureScore = this.assessStructureQuality(content)
    metrics.structureScore = structureScore
    if (structureScore < 70) {
      issues.push('内容结构不完整')
      score -= (100 - structureScore) * 0.2
    }

    // 5. 内容唯一性检查
    const uniquenessRatio = this.calculateUniqueness(content)
    metrics.uniquenessRatio = uniquenessRatio
    if (uniquenessRatio < 0.7) {
      issues.push('内容重复度较高')
      score -= (0.7 - uniquenessRatio) * 50
    }

    return {
      score: Math.max(0, Math.round(score)),
      issues: issues,
      metrics: {
        ...metrics,
        avgTitleLength: titleCount > 0 ? titleMatches.reduce((sum, title) => sum + title.length, 0) / titleCount : 0,
        contentDensity: this.calculateContentDensity(content)
      }
    }
  }

  /**
   * 检查内容质量问题
   */
  checkContentQuality(content) {
    const issues = []
    let penalty = 0

    // 检查思考过程残留
    if (content.includes('<thinking>') || content.includes('让我思考') || content.includes('思考过程')) {
      issues.push('包含思考过程残留')
      penalty += 15
    }

    // 检查错误和道歉信息
    const errorPatterns = [
      /抱歉|对不起|很抱歉/g,
      /无法|不能|无法提供/g,
      /错误|失败|问题/g
    ]

    errorPatterns.forEach(pattern => {
      if (pattern.test(content)) {
        issues.push('包含错误或道歉信息')
        penalty += 10
      }
    })

    // 检查空洞内容
    const fluffPatterns = [
      /详细描述|具体说明|进一步完善/g,
      /仅供参考|根据实际情况|请注意/g,
      /希望对您有帮助|如果您需要/g
    ]

    let fluffCount = 0
    fluffPatterns.forEach(pattern => {
      const matches = content.match(pattern)
      if (matches) fluffCount += matches.length
    })

    if (fluffCount > 2) {
      issues.push('包含过多空洞表述')
      penalty += fluffCount * 3
    }

    return { issues, penalty }
  }

  /**
   * 评估结构质量
   */
  assessStructureQuality(content) {
    let score = 100

    // 检查标题层级合理性
    const titles = content.match(/^#{1,6}\s+.+$/gm) || []
    if (titles.length === 0) return 0

    // 检查每个标题下是否有内容
    const sections = content.split(/^#{1,6}\s+.+$/gm)
    const emptySections = sections.filter(section => section.trim().length < 20).length

    if (emptySections > sections.length * 0.3) {
      score -= 30 // 超过30%的空章节
    }

    // 检查内容分布均匀性
    const avgSectionLength = sections.reduce((sum, section) => sum + section.length, 0) / sections.length
    const variance = sections.reduce((sum, section) => sum + Math.pow(section.length - avgSectionLength, 2), 0) / sections.length

    if (variance > avgSectionLength * 2) {
      score -= 20 // 内容分布不均匀
    }

    return Math.max(0, score)
  }

  /**
   * 计算内容唯一性
   */
  calculateUniqueness(content) {
    const sentences = content.split(/[。！？\n]/).filter(s => s.trim().length > 5)
    if (sentences.length === 0) return 0

    const uniqueSentences = new Set(sentences.map(s => s.trim().toLowerCase()))
    return uniqueSentences.size / sentences.length
  }

  /**
   * 计算内容密度
   */
  calculateContentDensity(content) {
    const titles = content.match(/^#{1,6}\s+.+$/gm) || []
    const totalLength = content.length
    const titleLength = titles.reduce((sum, title) => sum + title.length, 0)

    return titleLength > 0 ? (totalLength - titleLength) / totalLength : 0
  }

  /**
   * 获取嵌套对象的值
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      if (key.includes('[') && key.includes(']')) {
        const [arrayKey, index] = key.split(/[\[\]]/)
        return current?.[arrayKey]?.[parseInt(index)]
      }
      return current?.[key]
    }, obj)
  }

  /**
   * 检测模型类型
   */
  detectModelType(modelId) {
    const modelId_lower = modelId.toLowerCase()
    
    if (modelId_lower.includes('claude')) return 'claude'
    if (modelId_lower.includes('gpt') || modelId_lower.includes('openai')) return 'gpt'
    if (modelId_lower.includes('qwen') || modelId_lower.includes('通义')) return 'qwen'
    if (modelId_lower.includes('ernie') || modelId_lower.includes('文心')) return 'ernie'
    if (modelId_lower.includes('glm') || modelId_lower.includes('智谱')) return 'glm'
    
    return 'default'
  }

  /**
   * 流式响应处理
   */
  processStreamChunk(chunk, modelType = 'default') {
    try {
      // 检查chunk是否为null、undefined或空
      if (!chunk || chunk === null || chunk === undefined) {
        return ''
      }

      // 解析流式数据
      let data
      if (typeof chunk === 'string') {
        // 处理SSE格式
        if (chunk.startsWith('data: ')) {
          const jsonStr = chunk.substring(6).trim()
          if (jsonStr === '[DONE]' || jsonStr === '') return null

          // 安全的JSON解析
          try {
            data = JSON.parse(jsonStr)
          } catch (parseError) {
            // 如果JSON解析失败，可能是纯文本内容
            console.warn('JSON解析失败，作为纯文本处理:', jsonStr.substring(0, 50))
            return this.lightFilter(jsonStr)
          }
        } else {
          // 尝试解析为JSON，失败则作为纯文本
          try {
            data = JSON.parse(chunk)
          } catch (parseError) {
            return this.lightFilter(chunk)
          }
        }
      } else {
        data = chunk
      }

      // 再次检查解析后的数据
      if (!data || data === null || data === undefined) {
        return ''
      }

      // 根据模型类型提取增量内容
      const extractor = this.contentExtractors.get(modelType) ||
                       this.contentExtractors.get('default')

      const content = extractor(data)

      // 对增量内容进行轻量级过滤
      return this.lightFilter(content)

    } catch (error) {
      console.error('流式响应处理失败:', error, '原始内容:', chunk)
      // 发生错误时，尝试直接返回原始内容
      return typeof chunk === 'string' ? this.lightFilter(chunk) : ''
    }
  }

  /**
   * 轻量级过滤（用于流式响应）
   */
  lightFilter(content) {
    if (!content || typeof content !== 'string') return content
    
    // 只做基本的清理，避免影响流式体验
    return content
      .replace(/^<thinking>[\s\S]*?<\/thinking>/gmi, '') // 移除thinking标签
      .replace(/^(好的|当然|让我)\s*/gmi, '') // 移除开头的客套话
  }

  /**
   * 批量处理多个响应
   */
  processBatchResponses(responses, modelType = 'default') {
    return responses.map(response => this.processResponse(response, modelType))
  }
}

// 创建全局实例
export const aiResponseProcessor = new AIResponseProcessor()
