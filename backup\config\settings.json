{"theme": "dark", "loaded": true, "customThemes": [{"id": "luxury-gold", "name": "奢华金色", "colors": {"background": "linear-gradient(135deg, #1c1c1c 0%, #2a2a2a 100%)", "textPrimary": "#e6c992", "textSecondary": "#b5965a", "textTertiary": "#8a7548", "cardBackground": "#262626", "accent": "#d4af37", "accentLight": "#3d3520", "avatarBackground": "#3d3d3d", "border": "#4d4d4d", "borderLight": "#3a3a3a", "sectionBackground": "#333333", "tagBackground": "#3d3520", "tagText": "#d4af37"}, "fonts": {"primary": "'Playfair Display', serif", "title": "700 32px 'Playfair Display', serif", "subtitle": "400 18px 'Playfair Display', serif", "body": "400 16px 'Roboto', sans-serif", "caption": "400 12px 'Roboto', sans-serif", "dimension": "500 15px 'Roboto', sans-serif"}, "spacing": {"cardPadding": "28px", "sectionGap": "24px", "itemGap": "14px", "avatarSize": "80px"}, "borderRadius": "10px", "shadow": "0 15px 40px rgba(0,0,0,0.5)", "cardShadow": "0 8px 24px rgba(212,175,55,0.15)", "backgroundPattern": {"type": "luxury", "opacity": 0.1, "svg": "<svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <pattern id=\"herringbone\" x=\"0\" y=\"0\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">\n          <path d=\"M0 10 L10 0 M10 20 L20 10\" stroke=\"rgba(212,175,55,0.1)\" stroke-width=\"1\"/>\n        </pattern>\n      </defs>\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#herringbone)\"/>\n    </svg>"}}, {"id": "elegant", "name": "雅致", "colors": {"background": "linear-gradient(135deg, #f5f7fa 0%, #e8edf5 100%)", "textPrimary": "#333333", "textSecondary": "#666666", "textTertiary": "#999999", "cardBackground": "#ffffff", "accent": "#4a6baf", "accentLight": "#e8edf5", "avatarBackground": "#3a558f", "border": "#e0e0e0", "borderLight": "#f0f0f0", "sectionBackground": "#f9f9f9", "tagBackground": "#e8edf5", "tagText": "#4a6baf"}, "fonts": {"primary": "'PingFang SC', 'Source Han Sans CN', sans-serif", "title": "600 28px 'PingFang SC', 'Source Han Sans CN', sans-serif", "subtitle": "500 16px 'PingFang SC', 'Source Han Sans CN', sans-serif", "body": "400 14px 'PingFang SC', 'Source Han Sans CN', sans-serif", "caption": "400 12px 'PingFang SC', 'Source Han Sans CN', sans-serif", "dimension": "500 15px 'PingFang SC', 'Source Han Sans CN', sans-serif"}, "spacing": {"cardPadding": "26px", "sectionGap": "22px", "itemGap": "13px", "avatarSize": "76px"}, "borderRadius": "15px", "shadow": "0 10px 25px rgba(74, 107, 175, 0.1)", "cardShadow": "0 4px 16px rgba(74, 107, 175, 0.08)", "backgroundPattern": {"type": "elegant", "opacity": 0.08, "svg": "<svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <pattern id=\"elegantPattern\" x=\"0\" y=\"0\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\">\n          <rect width=\"60\" height=\"60\" fill=\"none\"/>\n          <line x1=\"0\" y1=\"30\" x2=\"60\" y2=\"30\" stroke=\"rgba(74,107,175,0.05)\" stroke-width=\"0.5\"/>\n          <line x1=\"30\" y1=\"0\" x2=\"30\" y2=\"60\" stroke=\"rgba(74,107,175,0.05)\" stroke-width=\"0.5\"/>\n          <circle cx=\"15\" cy=\"15\" r=\"2\" fill=\"rgba(74,107,175,0.1)\"/>\n          <circle cx=\"45\" cy=\"15\" r=\"2\" fill=\"rgba(74,107,175,0.1)\"/>\n          <circle cx=\"15\" cy=\"45\" r=\"2\" fill=\"rgba(74,107,175,0.1)\"/>\n          <circle cx=\"45\" cy=\"45\" r=\"2\" fill=\"rgba(74,107,175,0.1)\"/>\n        </pattern>\n      </defs>\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#elegantPattern)\"/>\n    </svg>"}}, {"id": "stellar", "name": "星夜谧境", "colors": {"background": "linear-gradient(135deg, #2c1e44 0%, #1a122e 100%)", "textPrimary": "#ffffff", "textSecondary": "#c2b3d9", "textTertiary": "#9a8bb3", "cardBackground": "rgba(44, 30, 68, 0.9)", "accent": "#7956b3", "accentLight": "#3e2e5c", "avatarBackground": "#5c438a", "border": "#3e2e5c", "borderLight": "#2e1e3c", "sectionBackground": "rgba(34, 24, 52, 0.8)", "tagBackground": "#3e2e5c", "tagText": "#c2b3d9"}, "fonts": {"primary": "'PingFang SC', 'Source Han Sans CN', sans-serif", "title": "700 28px 'PingFang SC', 'Source Han Sans CN', sans-serif", "subtitle": "500 16px 'PingFang SC', 'Source Han Sans CN', sans-serif", "body": "400 14px 'PingFang SC', 'Source Han Sans CN', sans-serif", "caption": "400 12px 'PingFang SC', 'Source Han Sans CN', sans-serif", "dimension": "500 15px 'PingFang SC', 'Source Han Sans CN', sans-serif"}, "spacing": {"cardPadding": "28px", "sectionGap": "24px", "itemGap": "14px", "avatarSize": "78px"}, "borderRadius": "24px", "shadow": "0 16px 48px rgba(121, 86, 179, 0.2)", "cardShadow": "0 8px 24px rgba(121, 86, 179, 0.15)", "backgroundPattern": {"type": "stellar", "opacity": 0.15, "svg": "<svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\">\n  <defs>\n    <pattern id=\"stellarPattern\" x=\"0\" y=\"0\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\">\n      <path d=\"M30 0 L0 30 L30 60 L60 30 Z\" fill=\"none\" stroke=\"rgba(121,86,179,0.1)\" stroke-width=\"1\"/>\n      <circle cx=\"30\" cy=\"30\" r=\"12\" fill=\"none\" stroke=\"rgba(121,86,179,0.05)\" stroke-width=\"0.8\"/>\n    </pattern>\n  </defs>\n  <rect width=\"100%\" height=\"100%\" fill=\"url(#stellarPattern)\"/>\n</svg>"}}, {"id": "midnight-glass", "name": "暗夜琉璃", "colors": {"background": "linear-gradient(135deg, rgba(18,22,36,0.96) 0%, rgba(36,28,64,0.95) 100%)", "textPrimary": "rgba(235,228,255,0.92)", "textSecondary": "rgba(193,183,227,0.8)", "textTertiary": "rgba(153,143,187,0.7)", "cardBackground": "rgba(45,42,75,0.45)", "accent": "rgba(142,118,255,0.85)", "accentLight": "rgba(62,58,98,0.4)", "avatarBackground": "rgba(94,82,156,0.6)", "border": "rgba(255,255,255,0.12)", "borderLight": "rgba(255,255,255,0.08)", "sectionBackground": "rgba(62,58,98,0.3)", "tagBackground": "rgba(62,58,98,0.4)", "tagText": "rgba(193,183,227,0.9)"}, "fonts": {"primary": "'SF Pro Display', 'PingFang SC', sans-serif", "title": "600 28px 'SF Pro Display', 'PingFang SC', sans-serif", "subtitle": "500 16px 'SF Pro Display', 'PingFang SC', sans-serif", "body": "400 14px 'SF Pro Display', 'PingFang SC', sans-serif", "caption": "400 12px 'SF Pro Display', 'PingFang SC', sans-serif", "dimension": "500 15px 'SF Pro Display', 'PingFang SC', sans-serif"}, "spacing": {"cardPadding": "30px", "sectionGap": "26px", "itemGap": "15px", "avatarSize": "82px"}, "borderRadius": "24px", "shadow": "0 12px 35px rgba(0,0,0,0.25), inset 1px 1px 0 rgba(255,255,255,0.1)", "cardShadow": "0 8px 24px rgba(142,118,255,0.12)", "backgroundPattern": {"type": "glass", "opacity": 0.12, "svg": "<svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\">\n<defs>\n<filter id=\"glassEffect\">\n<feGaussianBlur stdDeviation=\"4\" />\n<feComposite operator=\"over\" in=\"SourceGraphic\"/>\n</filter>\n<pattern id=\"midnightPattern\" x=\"0\" y=\"0\" width=\"80\" height=\"80\" patternUnits=\"userSpaceOnUse\">\n<path d=\"M40 0L80 40L40 80L0 40Z\" fill=\"none\" stroke=\"rgba(142,118,255,0.08)\" stroke-width=\"1.2\"/>\n<circle cx=\"40\" cy=\"40\" r=\"28\" fill=\"none\" stroke=\"rgba(142,118,255,0.05)\" stroke-width=\"0.8\"/>\n</pattern>\n</defs>\n<rect width=\"100%\" height=\"100%\" fill=\"url(#midnightPattern)\" filter=\"url(#glassEffect)\"/>\n</svg>"}}, {"id": "amber-eye", "name": "琥珀护眼", "colors": {"background": "linear-gradient(145deg, #F9F3E6 0%, #EFE8D8 100%)", "textPrimary": "#4D443C", "textSecondary": "#756A5E", "textTertiary": "#9A8B7F", "cardBackground": "rgba(255,251,240,0.9)", "accent": "#D4A064", "accentLight": "#F5F0E6", "avatarBackground": "#B08F6E", "border": "rgba(212,160,100,0.15)", "borderLight": "rgba(212,160,100,0.08)", "sectionBackground": "rgba(255,248,230,0.7)", "tagBackground": "#F5F0E6", "tagText": "#B08F6E"}, "fonts": {"primary": "'LXGW WenKai', 'PingFang SC', sans-serif", "title": "600 28px 'LXGW WenKai', 'PingFang SC', sans-serif", "subtitle": "500 16px 'LXGW WenKai', 'PingFang SC', sans-serif", "body": "400 14px 'LXGW WenKai', 'PingFang SC', sans-serif", "caption": "400 12px 'LXGW WenKai', 'PingFang SC', sans-serif", "dimension": "500 15px 'LXGW WenKai', 'PingFang SC', sans-serif"}, "spacing": {"cardPadding": "26px", "sectionGap": "22px", "itemGap": "13px", "avatarSize": "74px"}, "borderRadius": "18px", "shadow": "0 8px 32px rgba(212,160,100,0.1), inset 0 1px 2px rgba(255,255,255,0.3)", "cardShadow": "0 4px 16px rgba(212,160,100,0.08)", "backgroundPattern": {"type": "amber", "opacity": 0.08, "svg": "<svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\">\n<defs>\n<pattern id=\"amberPattern\" x=\"0\" y=\"0\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\">\n<rect width=\"100%\" height=\"100%\" fill=\"rgba(212,160,100,0.03)\"/>\n<path d=\"M50 0L100 50L50 100L0 50Z\" fill=\"none\" stroke=\"rgba(212,160,100,0.05)\" stroke-width=\"1\"/>\n</pattern>\n</defs>\n<rect width=\"100%\" height=\"100%\" fill=\"url(#amberPattern)\"/>\n</svg>"}}, {"id": "cosmic-blur", "name": "深空朦胧", "colors": {"background": "linear-gradient(145deg, rgba(15,25,45,0.98) 0%, rgba(30,45,75,0.95) 100%)", "textPrimary": "rgba(220,230,255,0.95)", "textSecondary": "rgba(170,190,220,0.8)", "textTertiary": "rgba(130,150,190,0.7)", "cardBackground": "rgba(45,60,90,0.4)", "accent": "rgba(100,160,255,0.85)", "accentLight": "rgba(35,50,80,0.5)", "avatarBackground": "rgba(70,110,180,0.6)", "border": "rgba(100,160,255,0.15)", "borderLight": "rgba(100,160,255,0.08)", "sectionBackground": "rgba(35,50,80,0.3)", "tagBackground": "rgba(35,50,80,0.5)", "tagText": "rgba(170,190,220,0.9)"}, "fonts": {"primary": "'Inter', 'PingFang SC', sans-serif", "title": "600 28px 'Inter', 'PingFang SC', sans-serif", "subtitle": "500 16px 'Inter', 'PingFang SC', sans-serif", "body": "400 14px 'Inter', 'PingFang SC', sans-serif", "caption": "400 12px 'Inter', 'PingFang SC', sans-serif", "dimension": "500 15px 'Inter', 'PingFang SC', sans-serif"}, "spacing": {"cardPadding": "28px", "sectionGap": "24px", "itemGap": "14px", "avatarSize": "76px"}, "borderRadius": "22px", "shadow": "0 16px 40px rgba(0,20,60,0.3), inset 0 1px 1px rgba(255,255,255,0.08)", "cardShadow": "0 8px 24px rgba(100,160,255,0.12)", "backgroundPattern": {"type": "cosmic", "opacity": 0.1, "svg": "<svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\">\n<defs>\n<filter id=\"cosmicBlur\" x=\"-20%\" y=\"-20%\" width=\"140%\" height=\"140%\">\n<feGaussianBlur stdDeviation=\"8\" result=\"blur\"/>\n<feMerge>\n<feMergeNode in=\"blur\"/>\n<feMergeNode in=\"SourceGraphic\"/>\n</feMerge>\n</filter>\n<pattern id=\"cosmicPattern\" x=\"0\" y=\"0\" width=\"120\" height=\"120\" patternUnits=\"userSpaceOnUse\">\n<circle cx=\"60\" cy=\"60\" r=\"50\" fill=\"none\" stroke=\"rgba(100,160,255,0.06)\"/>\n<line x1=\"60\" y1=\"0\" x2=\"60\" y2=\"120\" stroke=\"rgba(100,160,255,0.04)\"/>\n</pattern>\n</defs>\n<rect width=\"100%\" height=\"100%\" fill=\"url(#cosmicPattern)\" filter=\"url(#cosmicBlur)\"/>\n</svg>"}}], "editor": {"fontFamily": "'Noto Serif SC', serif", "fontSize": 32, "lineHeight": 1.7, "contentWidth": 84, "lastState": {"bookId": "ce729137-9318-4429-bd23-e45fe6673246", "volumeId": "5700beba-5e47-4a8a-addf-6e506b2b7c4c", "chapterId": "5ac75587-d34e-4379-a2f9-a5fc84b69f41"}, "bgEnabled": true, "bgImage": "D:\\project\\go\\pvv\\backup\\backgrounds\\bg_1735795978.jpg", "bgOpacity": 23, "volumeSortOrder": "desc", "chapterSortOrder": "desc", "contextMenus": [{"id": "menu-1743680475159", "name": "生成十个人名", "icon": "chat", "shortcut": "", "aiPrompt": "生成十个番茄小说网站中的中国都市高武题材世界观，超凡力量世界的十个男生人名，十个女生人名。${entity_f0b7cc16-9575-46d7-b5d2-2f8678f436a0}\n${entity_1bfb25a4-cc93-4cc7-899b-124db9992dc4}\n${context}\n${selected_text}", "model": "moonshotai/kimi-k2:free", "disabled": false, "order": 0, "includeContext": true, "entityInfo": null, "entitySelection": {"enabled": true, "selectedTemplateIds": ["25a34f9a-b57a-4923-a921-68d306dd8e97"], "selectedEntityIds": []}, "entityInfoList": []}], "aiAssistant": {"position": {"x": 727, "y": 53}, "size": {"width": 521, "height": 720}}, "entityWindow": {"position": {"x": 65, "y": 49}, "size": {"width": 523, "height": 750}}, "sceneWindow": {"position": {"x": 146, "y": 134}, "size": {"width": 600, "height": 600}}, "fontColor": "rgba(146, 199, 139, 1)", "paragraphSpace": true, "recentFontColors": ["rgba(0, 0, 0, 1)", "rgba(207, 232, 204, 1)", "rgba(146, 199, 139, 1)", "rgba(12, 77, 5, 1)"]}, "search": {"providers": [{"id": "mbi5fszrkaxc9zb2rgj", "name": "Qwen3", "url": "https://chat.qwen.ai/query={query}", "isDefault": false}, {"id": "mbhhm6ccxdjmggy7pm8", "name": "纳米DeepSeek", "url": "https://www.n.cn/?q={query}&src=360portal", "isDefault": false}, {"id": "mbkpugqazc4194b232o", "name": "Gemini", "url": "https://aistudio.google.com/prompts/new_chat", "isDefault": false}, {"id": "mbhjpr6c4206pibydhb", "name": "DeepSeek", "url": "https://chat.deepseek.com/?query={query}", "isDefault": false}, {"id": "mbhefwxnrlc3n03fib", "name": "百度ai", "url": "https://chat.baidu.com/search?word={query}", "isDefault": false}, {"id": "baidu", "name": "百度搜索", "url": "https://www.baidu.com/s?wd={query}", "isDefault": false}, {"id": "mbhelv7czu0ssowkkbr", "name": "Bing搜索", "url": "https://cn.bing.com/search?q={query}", "isDefault": false}, {"id": "mbhehfpiwwzzdlt6w2p", "name": "百度图片", "url": "https://image.baidu.com/search/index?tn=baiduimage&ct=201326592&lm=-1&cl=2&word={query}", "isDefault": false}, {"id": "mbheiqv0ddi1o2qhskm", "name": "1688", "url": "https://www.1688.com/zw/page.html?hpageId=old-sem-pc-list&keywords={query}&cosite=baidujj_pz&location=re&trackid=885662561117990122602&spm=a2638t.b_30496503.szyx_head.submit&keywordid=&bt=&exp=pcDacuIconExp%3AA%3BpcCpxGuessExp%3AB%3BcplLeadsExp%3AA%3BpcCpxCpsExp%3AB%3BhotBangdanExp%3AB&ptid=hr09cc8e892566e7", "isDefault": false}, {"id": "mbheo0eeaj9kdafvt5c", "name": "Bing图片", "url": "https://cn.bing.com/images/search?q={query}&first=1", "isDefault": false}, {"id": "mdo31y71868w44zs4f6", "name": "KIMI", "url": "https://www.kimi.com/", "isDefault": true}]}, "storyInspiration": {"categories": {"theme": {"name": "主题层", "description": "故事的核心主题与情感基调", "icon": "Sunrise", "color": "primary", "defaultCount": 2, "maxCount": 5}, "volume": {"name": "卷级结构", "description": "故事的大纲架构与发展脉络", "icon": "Connection", "color": "success", "defaultCount": 4, "maxCount": 8}, "keyPoint": {"name": "关键点", "description": "故事中的重要转折与关键节点", "icon": "Key", "color": "warning", "defaultCount": 5, "maxCount": 8}, "technique": {"name": "技法卡", "description": "用于优化剧情的各种写作技巧", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color": "danger", "defaultCount": 3, "maxCount": 5}}, "theme": [], "volume": [], "keyPoint": [], "technique": []}, "chat": {"fontSize": 14, "fontFamily": "微软雅黑, sans-serif", "codeBlockTheme": "dark", "codeHighlightStyle": "tomorrow", "codeBlockFontSize": 15}, "tts": {"voice": "zh-CN-XiaoyiNeural", "rate": -6, "volume": -3, "pitch": -10}, "chrome": {"default_path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "downloadDir": "C:\\Users\\<USER>\\Desktop\\111111111\\01", "userDataDirs": [{"name": "番茄", "path": "C:\\Users\\<USER>\\Desktop\\111111111", "port": 9223, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1734843620557"}, {"name": "起点", "path": "C:\\Users\\<USER>\\Desktop\\111111111\\Crowd Deny", "port": 9225, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1734843843149"}, {"name": "无需cookie", "path": "E:\\pvv备份\\浏览器相关", "port": 9224, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1735733268122"}, {"name": "poe1 grok1", "path": "C:\\Users\\<USER>\\Desktop\\文件\\browserenv", "port": 9224, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1740996693342"}, {"name": "poe2", "path": "C:\\Users\\<USER>\\Desktop\\文件\\browserenv", "port": 9222, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1740996708453"}, {"name": "poe3", "path": "C:\\Users\\<USER>\\Desktop\\文件\\browserenv", "port": 9235, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1740996717342"}, {"name": "poe4", "path": "C:\\Users\\<USER>\\Desktop\\文件\\browserenv", "port": 9228, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1740996730159"}, {"name": "claude", "path": "E:\\browser", "port": 9332, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1741757976528"}, {"name": "bilibili", "path": "E:\\pvv浏览器", "port": 9228, "isDefault": false, "enableExtensions": false, "extensionsPath": "", "id": "1746011080452"}], "path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"}, "oneapi": {"exe": "", "port": ""}, "openai": {"api_key": "e5577cc438064a32af62e31c76d22827.0hWQKLyweWkZ1AB3", "base_url": "https://open.bigmodel.cn/api/paas/v4"}, "feishu": {"app_id": "1111", "app_secret": "1", "encrypt_key": "1", "verification_token": "11"}, "novel": {"rules": {"qidian": {"id": "qidian", "name": "起点", "book_title_rule": "//h1[@id='bookName']", "directory_rule": "(//div[@class='catalog-all']/div[1]/ul/li[count(../li) >= 10]/a) |(//div[@class='catalog-all']/div[2]/ul/li[count(//div[@class='catalog-all']/div[1]/ul/li) < 10]/a)", "content_rule": "//main[contains(@class, 'content')]", "description_rule": "//div[@class='intro-detail']", "need_decrypt": false}, "fanqie": {"id": "fan<PERSON>e", "name": "番茄", "book_title_rule": "//div[@class='info-name']/h1", "description_rule": "//div[@class='page-abstract-content']", "directory_rule": "(//div[contains(@class, 'volum')])[1]//following-sibling::div[@class='chapter']//a", "content_rule": "//div[@class='muye-reader-content noselect']", "need_decrypt": true}, "feilu": {"id": "feilu", "name": "飞卢", "book_title_rule": "//h1[@id='novelName']", "description_rule": "//div[@class='T-L-T-Content']/div", "directory_rule": "//div[@class='C-Fo-Z-ML-TitleBox mgTop20']/following-sibling::div[1]/div/div/a", "content_rule": "//div[@class='noveContent readline']", "need_decrypt": false}, "qqreader": {"id": "qqreader", "name": "QQ阅读", "book_title_rule": "//h1[@class='book-title']", "description_rule": "//div[contains(@class,'book-intro')]", "directory_rule": "(//div[@class='tab-panel'])[2]/ul[2]/li/a", "content_rule": "//div[@id='article']", "need_decrypt": false}, "ciweimao": {"id": "ciweimao", "name": "刺猬猫", "book_title_rule": "//h1[@class='title']", "description_rule": "//div[contains(@class,'book-desc')]", "directory_rule": "//div[@class='book-chapter-box']/ul[@class='book-chapter-list']/li[not(./a/i[@class='icon-lock'])]/a", "content_rule": "//div[@class='read-bd']", "need_decrypt": false}, "qimao": {"id": "qimao", "name": "七猫", "book_title_rule": "//div[@class='wrap-txt']/div[1]/span[1]", "description_rule": "//div[contains(@class,'book-introduction')]/div[1]/div[2]", "directory_rule": "//ul[contains(@class,'qm-book-catalog-list-content')]/li[not(./a/span[contains(@class,'vip-icon')])]/a", "content_rule": "//div[@class='chapter-detail-wrap-content']/div[1]", "need_decrypt": false}, "e7ffe0e0-f672-4d1a-9d27-a3be0c7427f3": {"id": "e7ffe0e0-f672-4d1a-9d27-a3be0c7427f3", "name": "bqgzz.com", "book_title_rule": "//div[@class=\"d_title\"]/h1", "directory_rule": "//ul[@id='chapterList']/li/a", "content_rule": " //div[@class='read-content']", "description_rule": "//div[@class='hm-scroll']", "need_decrypt": false}, "882a1365-1341-403a-b33a-fb5d59bbb4b3": {"id": "882a1365-1341-403a-b33a-fb5d59bbb4b3", "name": "https://www.biquge11.cc/", "book_title_rule": "/html/body/div[5]/div[2]/h1", "directory_rule": "//div[@class=\"listmain\"]/dl//a", "content_rule": "//div[@id='chaptercontent']", "description_rule": "//dt[contains(text(),'内容')]//following-sibling::dd", "need_decrypt": false}}, "download": {"chapterCount": 20, "intervalTime": 2, "downloadPath": "", "chromeConfig": {"chromePath": "", "userDataDir": "", "proxy": ""}}}, "backup": {"backupDir": "D:\\soft\\pvv\\backup", "targetDir": "E:\\pvv备份", "autoBackup": false, "backupInterval": 20, "keepBackups": 20, "lastBackupTime": 1749664364, "useZip": false}, "models": ["hunyuan-lite", "deepseek/deepseek-chat-v3-0324:free", "glm-4-flash", "lite", "google/gemini-2.0-flash-exp:free", "moonshotai/kimi-vl-a3b-thinking:free", "ernie-speed-128k", "gemini-2.5-flash-preview-04-17", "deepseek/deepseek-r1:free", "qwen/qwen3-235b-a22b:free", "deepseek/deepseek-r1-0528:free", "glm-z1-flash", "x1", "gpt-4.1-mini"], "isModelsLoaded": true, "selectedModel": "glm-z1-flash", "git": {"repoUrl": "https://e.coding.net/aiwork/author/pvv_backup.git", "authType": "token", "username": "", "password": "", "token": "d64e4df7ccd591b6ad4f31054d217e72dd59815b", "tokenUsername": "ptu925vao317", "localPath": "", "backupDir": "D:\\soft\\pvv\\backup", "autoBackup": false, "backupInterval": 30}, "aiRoles": [{"name": "json专家", "prompt": "我提的问题都按照json的格式返回。", "isEnabled": true, "id": "1734782868751"}, {"name": "外貌描写", "prompt": "生成三个个人物的外貌描写，json格式返回。\n要求有基本的人物细节描写，从外貌，整体的穿着介绍来写\n。\n", "isEnabled": true, "id": "1737031081572"}], "isAIRolesLoaded": true, "proxy": {"enabled": true, "type": "custom", "http": "http://127.0.0.1:7897", "https": "https://127.0.0.1:7897", "bypass": ""}, "markdownEditor": {"lastSelectedDirectory": "E:\\onedrive\\拆书\\本地文件\\工具\\output", "fontSize": {"edit": 18, "preview": 22, "mindmap": 17}, "preferences": {"showSidebar": false, "sidebarCollapsed": false, "defaultMode": "mindmap", "autoSave": true, "autoSaveInterval": 30000, "wordWrap": true, "lineNumbers": true, "theme": "auto"}, "recentFiles": [], "layout": {"sidebarWidth": 320, "editorSplit": 50}}}