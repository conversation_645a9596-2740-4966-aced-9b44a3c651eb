/**
 * 思维导图上下文构建器
 * 为AI生成提供完整的节点层级上下文信息
 */

export class MindmapContextBuilder {
  constructor() {
    this.maxAncestorLevels = 3 // 最多向上追溯3层
    this.maxSiblingCount = 5   // 最多显示5个同级节点
    this.maxChildrenPreview = 3 // 最多预览3个子节点
  }

  /**
   * 构建完整的节点上下文
   * @param {Object} currentNode - 当前节点
   * @param {Object} mindmapData - 完整的思维导图数据
   * @param {string} domain - 专业领域
   * @returns {Object} 完整的上下文信息
   */
  buildFullContext(currentNode, mindmapData, domain = 'general') {
    console.log('=== 上下文构建详细调试 ===')
    console.log('当前节点:', currentNode)
    console.log('思维导图数据根节点:', { id: mindmapData?.id, title: mindmapData?.title })
    console.log('节点ID类型:', typeof currentNode?.id, '值:', currentNode?.id)

    if (!currentNode || !mindmapData) {
      console.log('❌ 缺少必要参数，返回空上下文')
      return this.getEmptyContext()
    }

    // 先测试能否找到节点
    const foundNode = this.findNodeById(currentNode.id, mindmapData)
    console.log('能否在思维导图中找到节点:', !!foundNode, foundNode?.title)

    const nodeInfo = this.getNodeInfo(currentNode, mindmapData)
    console.log('节点信息:', nodeInfo)

    const hierarchyInfo = this.getHierarchyInfo(currentNode, mindmapData)
    console.log('层级信息:', hierarchyInfo)

    const relationshipInfo = this.getRelationshipInfo(currentNode, mindmapData)
    const contentInfo = this.getContentInfo(currentNode, mindmapData)

    console.log('上下文构建完成 - 路径:', hierarchyInfo.fullPath)

    return {
      // 基础信息
      title: currentNode.title || '未命名主题',
      depth: nodeInfo.depth,
      domain: domain,
      
      // 层级信息
      hierarchy: hierarchyInfo,
      
      // 关系信息
      relationships: relationshipInfo,
      
      // 内容信息
      content: contentInfo,
      
      // 统计信息
      stats: {
        totalDepth: nodeInfo.totalDepth,
        siblingCount: relationshipInfo.siblings.length,
        childrenCount: relationshipInfo.children.length,
        ancestorCount: hierarchyInfo.ancestors.length
      }
    }
  }

  /**
   * 获取节点基础信息
   */
  getNodeInfo(currentNode, mindmapData) {
    const path = this.findNodePath(currentNode.id, mindmapData)
    const depth = Math.max(0, path.length - 1)
    const totalDepth = this.getMaxDepth(mindmapData)

    return {
      id: currentNode.id,
      title: currentNode.title,
      depth: depth,
      totalDepth: totalDepth,
      path: path,
      isRoot: depth === 0,
      isLeaf: !currentNode.children || currentNode.children.length === 0
    }
  }

  /**
   * 获取层级信息（祖先节点）
   */
  getHierarchyInfo(currentNode, mindmapData) {
    const path = this.findNodePath(currentNode.id, mindmapData)
    const ancestors = []

    // 从根节点开始，但不包括当前节点
    for (let i = 0; i < path.length - 1 && i < this.maxAncestorLevels; i++) {
      const ancestorNode = this.findNodeById(path[i].id, mindmapData)
      if (ancestorNode) {
        ancestors.push({
          id: ancestorNode.id,
          title: ancestorNode.title,
          level: i,
          content: ancestorNode.content || ''
        })
      }
    }

    return {
      ancestors: ancestors,
      rootTitle: path.length > 0 ? path[0].title : '',
      parentTitle: path.length > 1 ? path[path.length - 2].title : '',
      fullPath: path.map(node => node.title).join(' → ')
    }
  }

  /**
   * 获取关系信息（同级、子级节点）
   */
  getRelationshipInfo(currentNode, mindmapData) {
    const siblings = this.getSiblingNodes(currentNode, mindmapData)
    const children = currentNode.children || []

    return {
      siblings: siblings.slice(0, this.maxSiblingCount).map(node => ({
        id: node.id,
        title: node.title,
        hasChildren: !!(node.children && node.children.length > 0),
        content: node.content || ''
      })),
      children: children.slice(0, this.maxChildrenPreview).map(node => ({
        id: node.id,
        title: node.title,
        hasChildren: !!(node.children && node.children.length > 0),
        content: node.content || ''
      })),
      hasMoreSiblings: siblings.length > this.maxSiblingCount,
      hasMoreChildren: children.length > this.maxChildrenPreview
    }
  }

  /**
   * 获取内容信息
   */
  getContentInfo(currentNode, mindmapData) {
    return {
      currentContent: currentNode.content || '',
      hasContent: !!(currentNode.content && currentNode.content.trim()),
      contentLength: currentNode.content ? currentNode.content.length : 0,
      isEmpty: !currentNode.content || currentNode.content.trim() === ''
    }
  }

  /**
   * 查找节点路径
   */
  findNodePath(nodeId, mindmapData) {
    let foundPath = []

    const findPath = (node, targetId, currentPath) => {
      const newPath = [...currentPath, { id: node.id, title: node.title }]

      if (node.id === targetId) {
        foundPath = newPath
        return true
      }

      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          if (findPath(child, targetId, newPath)) {
            return true
          }
        }
      }

      return false
    }

    findPath(mindmapData, nodeId, [])
    console.log(`查找节点路径 ${nodeId}:`, foundPath.map(n => n.title).join(' → '))
    return foundPath
  }

  /**
   * 根据ID查找节点
   */
  findNodeById(nodeId, mindmapData) {
    console.log(`🔍 查找节点ID: ${nodeId}, 当前检查节点: ${mindmapData.id} (${mindmapData.title})`)

    if (mindmapData.id === nodeId) {
      console.log(`✅ 找到匹配节点: ${mindmapData.title}`)
      return mindmapData
    }

    if (mindmapData.children) {
      console.log(`📁 检查 ${mindmapData.children.length} 个子节点`)
      for (const child of mindmapData.children) {
        const found = this.findNodeById(nodeId, child)
        if (found) return found
      }
    }

    return null
  }

  /**
   * 获取同级节点
   */
  getSiblingNodes(currentNode, mindmapData) {
    const parent = this.findParentNode(currentNode.id, mindmapData)
    if (!parent || !parent.children) {
      return []
    }
    
    return parent.children.filter(child => child.id !== currentNode.id)
  }

  /**
   * 查找父节点
   */
  findParentNode(nodeId, mindmapData) {
    if (mindmapData.children) {
      for (const child of mindmapData.children) {
        if (child.id === nodeId) {
          return mindmapData
        }
        
        const found = this.findParentNode(nodeId, child)
        if (found) return found
      }
    }
    
    return null
  }

  /**
   * 获取最大深度
   */
  getMaxDepth(mindmapData) {
    const getDepth = (node, currentDepth = 0) => {
      if (!node.children || node.children.length === 0) {
        return currentDepth
      }
      
      let maxChildDepth = currentDepth
      for (const child of node.children) {
        const childDepth = getDepth(child, currentDepth + 1)
        maxChildDepth = Math.max(maxChildDepth, childDepth)
      }
      
      return maxChildDepth
    }
    
    return getDepth(mindmapData)
  }

  /**
   * 获取空上下文
   */
  getEmptyContext() {
    return {
      title: '未命名主题',
      depth: 0,
      domain: 'general',
      hierarchy: {
        ancestors: [],
        rootTitle: '',
        parentTitle: '',
        fullPath: ''
      },
      relationships: {
        siblings: [],
        children: [],
        hasMoreSiblings: false,
        hasMoreChildren: false
      },
      content: {
        currentContent: '',
        hasContent: false,
        contentLength: 0,
        isEmpty: true
      },
      stats: {
        totalDepth: 0,
        siblingCount: 0,
        childrenCount: 0,
        ancestorCount: 0
      }
    }
  }

  /**
   * 格式化上下文为可读字符串
   */
  formatContextForPrompt(context) {
    const parts = []
    
    // 基础信息
    parts.push(`当前主题：${context.title}`)
    parts.push(`层级深度：第${context.depth}层`)
    
    // 层级路径
    if (context.hierarchy.fullPath) {
      parts.push(`完整路径：${context.hierarchy.fullPath}`)
    }
    
    // 祖先信息
    if (context.hierarchy.ancestors.length > 0) {
      const ancestorTitles = context.hierarchy.ancestors.map(a => a.title).join(' → ')
      parts.push(`上级主题：${ancestorTitles}`)
    }
    
    // 同级信息
    if (context.relationships.siblings.length > 0) {
      const siblingTitles = context.relationships.siblings.map(s => s.title).join('、')
      parts.push(`同级主题：${siblingTitles}${context.relationships.hasMoreSiblings ? '等' : ''}`)
    }
    
    // 子级信息
    if (context.relationships.children.length > 0) {
      const childrenTitles = context.relationships.children.map(c => c.title).join('、')
      parts.push(`已有子主题：${childrenTitles}${context.relationships.hasMoreChildren ? '等' : ''}`)
    }
    
    return parts.join('\n')
  }
}

// 创建全局实例
export const contextBuilder = new MindmapContextBuilder()
