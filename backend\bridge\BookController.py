# coding: utf-8
import os
import json
import uuid
import time
import datetime
import shutil
import re
import threading
from collections import defaultdict
from typing import Optional, Dict, List, Any

# 添加父目录到 Python 路径
# sys.path.append(str(Path(__file__).parent.parent))
from backend.bridge.Base import ResponsePacket, BaseManager
from backend.bridge.CacheManager import cache_manager, cached


class BooksManager(BaseManager):
    def __init__(self, base_dir):
        self.base_dir = base_dir  # 这里目录就是book，所以操作用这个拼接的活都是这个下面的响应管理book的，比如books中都是书籍，
        super().__init__(base_dir, "books")

        # 缓存和索引
        self._book_index: Dict[str, str] = {}  # book_id -> book_path
        self._index_built = False
        self._index_lock = threading.RLock()

        # 构建初始索引
        self._build_book_index()

    def _build_book_index(self):
        """构建书籍ID到路径的索引"""
        with self._index_lock:
            try:
                self._book_index.clear()
                books_dir = os.path.join(self.base_dir, "books")

                if not os.path.exists(books_dir):
                    os.makedirs(books_dir, exist_ok=True)
                    return

                for book_name in os.listdir(books_dir):
                    book_dir = os.path.join(books_dir, book_name)
                    if os.path.isdir(book_dir):
                        info_file = os.path.join(book_dir, 'info.json')
                        if os.path.exists(info_file):
                            book_info = self._load_json(info_file)
                            if book_info and book_info.get('id'):
                                self._book_index[book_info['id']] = book_dir

                self._index_built = True
                print(f"书籍索引构建完成，共 {len(self._book_index)} 本书")
            except Exception as e:
                print(f"构建书籍索引失败: {str(e)}")
                self._index_built = False

    def _invalidate_book_cache(self, book_id: str):
        """使书籍相关缓存失效"""
        # 清除书籍基本信息缓存
        cache_manager.invalidate_by_pattern(f"book_{book_id}")
        # 清除书籍列表缓存
        cache_manager.delete("book_list")

    def _invalidate_volumes_cache(self, book_id: str):
        """使卷列表缓存失效"""
        cache_manager.invalidate_by_pattern(f"volumes_meta_{book_id}")

    def _invalidate_chapters_cache(self, book_id: str, volume_id: str = None):
        """使章节列表缓存失效"""
        if volume_id:
            # 清除特定卷的章节缓存
            cache_manager.invalidate_by_pattern(f"volume_chapters_meta_{book_id}_{volume_id}")
        else:
            # 清除所有卷的章节缓存
            cache_manager.invalidate_by_pattern(f"volume_chapters_meta_{book_id}")

        # 清除包含章节信息的卷列表缓存
        cache_manager.invalidate_by_pattern(f"volumes_meta_{book_id}")

    def _invalidate_search_cache(self, book_id: str):
        """使搜索缓存失效"""
        cache_manager.invalidate_by_pattern(f"chapters_search_{book_id}")

    def _invalidate_all_related_cache(self, book_id: str):
        """清除书籍相关的所有缓存（用于重大结构变化）"""
        self._invalidate_book_cache(book_id)
        self._invalidate_volumes_cache(book_id)
        self._invalidate_chapters_cache(book_id)
        self._invalidate_search_cache(book_id)

    def get_book_path(self, book_id: str) -> Optional[str]:
        """获取书目录路径（优化版本）"""
        if not book_id:
            return None

        with self._index_lock:
            # 如果索引未构建或为空，重新构建
            if not self._index_built or not self._book_index:
                self._build_book_index()

            # 从索引中获取路径
            book_path = self._book_index.get(book_id)

            # 验证路径是否仍然有效
            if book_path and os.path.exists(book_path):
                return book_path

            # 如果路径无效，重新构建索引并再次尝试
            self._build_book_index()
            return self._book_index.get(book_id)

    def get_timeline(self, book_id):
        """获取时间线数据"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            timeline_path = os.path.join(book_path, 'timeLine')
            if not os.path.exists(timeline_path):
                os.makedirs(timeline_path, exist_ok=True)

            timeline_file = os.path.join(timeline_path, 'timeline.json')


            data = self._load_json(timeline_file)

            # 如果文件不存在或数据为空，返回默认结构
            if not data:
                data = {
                    "tracks": []
                }

            # 确保每个事件都有必要的字段
            for track in data.get("tracks", []):
                for event in track.get("events", []):
                    # 确保每个事件都有开始和结束时间
                    if "startTime" not in event:
                        event["startTime"] = {"year": 0, "month": 1, "day": 1}
                    if "endTime" not in event:
                        event["endTime"] = {"year": 0, "month": 1, "day": 1}
                    # 确保每个事件都有位置和宽度
                    if "position" not in event:
                        event["position"] = 300
                    if "width" not in event:
                        event["width"] = 200
                    # 确保每个事件都有类型
                    if "type" not in event:
                        event["type"] = "primary"

            return self._success_response("获取时间线成功", data)
        except Exception as e:
            return self._error_response(f"获取时间线失败: {str(e)}")

    def save_timeline(self, book_id, timeline_data):
        """保存时间线数据"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            timeline_path = os.path.join(book_path, 'timeLine')
            if not os.path.exists(timeline_path):
                os.makedirs(timeline_path, exist_ok=True)

            timeline_file = os.path.join(timeline_path, 'timeline.json')
            self._save_json(timeline_file, timeline_data)
            return self._success_response("保存时间线成功")
        except Exception as e:
            return self._error_response(f"保存时间线失败: {str(e)}")

    def save_timeline_event_type(self, book_id, event_types):
        """保存时间线事件类型配置"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            timeline_path = os.path.join(book_path, 'timeLine')
            if not os.path.exists(timeline_path):
                os.makedirs(timeline_path, exist_ok=True)

            event_types_file = os.path.join(timeline_path, 'event_types.json')
            data = self._load_json(event_types_file)

            # 如果文件不存在或数据为空，返回默认配置
            if not data:
                data = [
                    {"name": "主要", "value": "primary", "color": "#409EFF"},
                    {"name": "次要", "value": "secondary", "color": "#67C23A"},
                    {"name": "支线", "value": "side", "color": "#E6A23C"},
                    {"name": "背景", "value": "background", "color": "#909399"}
                ]
                # 保存默认配置
                self._save_json(event_types_file, data)

            return self._success_response("获取事件类型配置成功", data)
        except Exception as e:
            return self._error_response(f"获取事件类型配置失败: {str(e)}")

    def get_timeline_event_type(self, book_id):
        """获取时间线事件类型配置"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            timeline_path = os.path.join(book_path, 'timeLine')
            if not os.path.exists(timeline_path):
                os.makedirs(timeline_path, exist_ok=True)

            event_types_file = os.path.join(timeline_path, 'event_types.json')
            data = self._load_json(event_types_file)

            # 如果文件不存在或数据为空，返回默认配置
            if not data:
                data = [
                    {"name": "主要", "value": "primary", "color": "#409EFF"},
                    {"name": "次要", "value": "secondary", "color": "#67C23A"},
                    {"name": "支线", "value": "side", "color": "#E6A23C"},
                    {"name": "背景", "value": "background", "color": "#909399"}
                ]
                # 保存默认配置
                self._save_json(event_types_file, data)

            return self._success_response("获取事件类型配置成功", data)
        except Exception as e:
            return self._error_response(f"获取事件类型配置失败: {str(e)}")

    def save_book_scene(self,book_id, scene_data):
        "用来更新保存场景数据在本地"
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            scene_path = os.path.join(book_path, 'scene')
            if not os.path.exists(scene_path):
                os.makedirs(scene_path, exist_ok=True)

            scene_file = os.path.join(scene_path, 'scene.json')
            self._save_json(scene_file, scene_data)
            return self._success_response("保存场景成功")
        except Exception as e:
            return self._error_response(f"保存场景失败: {str(e)}")

    def get_book_scene(self,book_id):
        "获取在这个书籍中的书籍的json场景文件路径"
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return None

            scene_path = os.path.join(book_path, 'scene')
            if not os.path.exists(scene_path):
                os.makedirs(scene_path)
            scene_types_file = os.path.join(scene_path, 'scene.json')
            data = self._load_json(scene_types_file)
            if not data:
                data = {}
                # 保存默认配置
                self._save_json(scene_types_file, data)
            return self._success_response("获取场景成功", data)
        except Exception as e:
            return self._error_response(f"获取场景失败: {str(e)}")

    def get_book_setting_path(self, book_id):
        """获取书籍设定目录路径"""
        book_path = self.get_book_path(book_id)
        if not book_path:
            return None

        setting_path = os.path.join(book_path, 'settings')
        if not os.path.exists(setting_path):
            os.makedirs(setting_path)
        return setting_path

    def create_book(self, book_data):
        book_id = str(uuid.uuid4())
        book_data['id'] = book_id
        book_data['created_at'] = time.strftime("%Y-%m-%d %H:%M:%S")
        book_data['updated_at'] = book_data['created_at']

        book_data.setdefault('title', '未命名')
        book_data.setdefault('description', '')
        book_data.setdefault('theme_color', '#000000')
        book_data.setdefault('word_count', 0)

        book_dir = os.path.join(self.directory, book_data['title'])
        try:
            os.makedirs(book_dir, exist_ok=True)
            os.makedirs(os.path.join(book_dir, 'volumes'), exist_ok=True)
            self._save_json(os.path.join(book_dir, 'info.json'), book_data)

            # 创建默认卷
            volume_data = {
                'id': str(uuid.uuid4()),
                'title': '第一卷',
                'description': '',
                'order': 1,
                'created_at': time.strftime("%Y-%m-%d %H:%M:%S"),
                'updated_at': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            volume_dir = os.path.join(book_dir, 'volumes', volume_data['id'])
            os.makedirs(volume_dir, exist_ok=True)
            self._save_json(os.path.join(volume_dir, 'info.json'), volume_data)

            # 更新索引
            with self._index_lock:
                self._book_index[book_id] = book_dir

            # 使书籍列表缓存失效
            cache_manager.delete("book_list")

            return self._success_response("书籍创建成功", book_data)
        except Exception as e:
            return self._error_response(f"书籍创建失败: {str(e)}")

    @cached(ttl=300, key_func=lambda *args: f"book_{args[1]}")
    def get_book(self, book_id):
        book_path = self.get_book_path(book_id)
        if not book_path:
            return self._error_response("书籍不存在")

        info_file = os.path.join(book_path, 'info.json')
        book_data = self._load_json(info_file)

        if book_data:
            book_data['word_count'] = self._calculate_word_count(book_path)
            return self._success_response("获取书籍成功", book_data)
        return self._error_response("书籍不存在")

    def update_book(self, book_id, book_data):
        book_path = self.get_book_path(book_id)
        if not book_path:
            return self._error_response("书籍不存在")

        info_file = os.path.join(book_path, 'info.json')
        old_data = self._load_json(info_file)

        if not old_data:
            return self._error_response("书籍信息不存在")

        # 确保 book_data 是字典而非字符串
        if isinstance(book_data, str):
            try:
                book_data = json.loads(book_data)
            except:
                return self._error_response("输入数据格式错误")

        # 复制必要的字段
        book_data['id'] = old_data['id']
        book_data['created_at'] = old_data['created_at']
        book_data['updated_at'] = time.strftime("%Y-%m-%d %H:%M:%S")
        book_data['word_count'] = old_data.get('word_count', 0)

        try:
            new_title = book_data.get('title')
            book_name = os.path.basename(book_path)

            if new_title and new_title != book_name:
                new_book_dir = os.path.join(self.directory, new_title)
                if os.path.exists(new_book_dir):
                    return self._error_response(f"已存在同名书籍：{new_title}")
                os.rename(book_path, new_book_dir)
                info_file = os.path.join(new_book_dir, 'info.json')

                # 更新索引
                with self._index_lock:
                    self._book_index[book_id] = new_book_dir

            # 保存前检查确保是字典类型
            self._save_json(info_file, book_data)

            # 使相关缓存失效
            self._invalidate_book_cache(book_id)

            return self._success_response("更新书籍成功", book_data)
        except Exception as e:
            return self._error_response(f"更新书籍失败: {str(e)}")

    def delete_book(self, book_id):
        for book_name in os.listdir(self.directory):
            book_dir = os.path.join(self.directory, book_name)
            info_file = os.path.join(book_dir, 'info.json')
            book_data = self._load_json(info_file)

            if book_data and book_data.get('id') == book_id:
                try:
                    import shutil
                    # 生成一个时间戳后缀，避免同名文件冲突
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    trash_book_name = f"{book_name}_{timestamp}"
                    trash_dir = os.path.join(self.base_dir, "trash")
                    print(f"Moving {book_name} to trash directory: {trash_dir}")
                    os.makedirs(trash_dir, exist_ok=True)

                    # 移动到trash目录
                    trash_book_path = os.path.join(trash_dir, trash_book_name)
                    shutil.move(book_dir, trash_book_path)

                    # 更新书籍信息，添加删除时间
                    trash_info_file = os.path.join(trash_book_path, 'info.json')
                    if os.path.exists(trash_info_file):
                        trash_book_data = self._load_json(trash_info_file)
                        trash_book_data['deleted_at'] = time.strftime("%Y-%m-%d %H:%M:%S")
                        self._save_json(trash_info_file, trash_book_data)

                    # 清除所有相关缓存
                    cache_manager.delete("book_list")
                    cache_manager.delete(f"book_{book_id}")
                    cache_manager.delete(f"entities_{book_id}")

                    # 更新索引
                    with self._index_lock:
                        if book_id in self._book_index:
                            del self._book_index[book_id]

                    return self._success_response("书籍已移至回收站")
                except Exception as e:
                    return self._error_response(f"移动书籍到回收站失败: {str(e)}")
        return self._error_response("书籍不存在")

    @cached(ttl=60, key_func=lambda *args: "book_list")
    def list_books(self):
        try:
            books = []
            with self._index_lock:
                # 确保索引是最新的
                if not self._index_built:
                    self._build_book_index()

                for book_id, book_path in self._book_index.items():
                    if os.path.exists(book_path):
                        info_file = os.path.join(book_path, 'info.json')
                        book_data = self._load_json(info_file)

                        if book_data:
                            book_data['word_count'] = self._calculate_word_count(book_path)
                            books.append(book_data)

            return self._success_response("获取书籍列表成功", books)
        except Exception as e:
            return self._error_response(f"获取书籍列表失败: {str(e)}")

    def _calculate_word_count(self, book_dir):
        total_words = 0
        volumes_dir = os.path.join(book_dir, 'volumes')
        if os.path.exists(volumes_dir):
            for volume_id in os.listdir(volumes_dir):
                volume_dir = os.path.join(volumes_dir, volume_id)
                if os.path.isdir(volume_dir):
                    for chapter_file in os.listdir(volume_dir):
                        if chapter_file.endswith('.json') and chapter_file != 'info.json':
                            chapter_data = self._load_json(os.path.join(volume_dir, chapter_file))
                            total_words += chapter_data.get('word_count', 0)
        return total_words

    @cached(ttl=600, key_func=lambda self, book_id, page=1, page_size=50, include_chapters=True, volume_ids=None: f"volumes_meta_{book_id}_{page}_{page_size}_{include_chapters}")
    def get_volumes(self, book_id, page=1, page_size=50, include_chapters=True, volume_ids=None):
        """获取书籍的卷列表（支持分页和选择性加载）

        Args:
            book_id: 书籍ID
            page: 页码（从1开始）
            page_size: 每页数量
            include_chapters: 是否包含章节列表
            volume_ids: 指定要获取的卷ID列表（用于增量更新）
        """
        book_path = self.get_book_path(book_id)
        if not book_path:
            return self._error_response("书籍不存在")

        volumes_dir = os.path.join(book_path, 'volumes')
        if not os.path.exists(volumes_dir):
            return self._success_response("获取卷列表成功", {
                'volumes': [],
                'total': 0,
                'page': page,
                'page_size': page_size,
                'total_pages': 0
            })

        try:
            # 获取所有卷的基本信息
            all_volumes = []
            for volume_id in os.listdir(volumes_dir):
                volume_dir = os.path.join(volumes_dir, volume_id)
                if os.path.isdir(volume_dir):
                    # 如果指定了volume_ids，只处理指定的卷
                    if volume_ids and volume_id not in volume_ids:
                        continue

                    info_file = os.path.join(volume_dir, 'info.json')
                    volume_data = self._load_json(info_file)
                    if volume_data:
                        # 添加统计信息
                        volume_data['chapter_count'] = self._get_volume_chapter_count(volume_dir)
                        volume_data['word_count'] = self._get_volume_word_count(volume_dir)
                        all_volumes.append(volume_data)

            # 排序
            all_volumes.sort(key=lambda x: x.get('order', 0))

            # 如果指定了volume_ids，直接返回这些卷
            if volume_ids:
                if include_chapters:
                    for volume in all_volumes:
                        volume['chapters'] = self._get_volume_chapters(
                            os.path.join(volumes_dir, volume['id'])
                        )
                return self._success_response("获取指定卷成功", all_volumes)

            # 分页处理
            total = len(all_volumes)
            total_pages = (total + page_size - 1) // page_size
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size

            volumes_page = all_volumes[start_idx:end_idx]

            # 根据需要加载章节信息
            if include_chapters:
                for volume in volumes_page:
                    volume['chapters'] = self._get_volume_chapters(
                        os.path.join(volumes_dir, volume['id'])
                    )

            return self._success_response("获取卷列表成功", {
                'volumes': volumes_page,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': total_pages
            })

        except Exception as e:
            return self._error_response(f"获取卷列表失败: {str(e)}")

    def _get_volume_chapter_count(self, volume_dir):
        """获取卷的章节数量"""
        try:
            count = 0
            for file_name in os.listdir(volume_dir):
                if file_name.endswith('.json') and file_name != 'info.json':
                    count += 1
            return count
        except:
            return 0

    def _get_volume_word_count(self, volume_dir):
        """获取卷的总字数"""
        try:
            total_words = 0
            for file_name in os.listdir(volume_dir):
                if file_name.endswith('.json') and file_name != 'info.json':
                    chapter_data = self._load_json(os.path.join(volume_dir, file_name))
                    if chapter_data:
                        total_words += chapter_data.get('word_count', 0)
            return total_words
        except:
            return 0

    def _get_volume_chapters(self, volume_dir, page=1, page_size=100):
        """获取卷的章节列表（支持分页）"""
        try:
            chapters = []
            for chapter_file in os.listdir(volume_dir):
                if chapter_file.endswith('.json') and chapter_file != 'info.json':
                    chapter_data = self._load_json(os.path.join(volume_dir, chapter_file))
                    if chapter_data:
                        # 不返回章节内容，只返回元数据
                        chapter_data.pop('content', None)
                        chapters.append(chapter_data)

            # 排序
            chapters.sort(key=lambda x: x.get('order', 0))

            # 分页（如果需要）
            if page_size < len(chapters):
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                chapters = chapters[start_idx:end_idx]

            return chapters
        except:
            return []

    @cached(ttl=120, key_func=lambda self, book_id, query, page=1, page_size=20: f"chapters_search_{book_id}_{hash(query)}_{page}_{page_size}")
    def search_chapters(self, book_id, query, page=1, page_size=20):
        """搜索章节（支持分页）"""
        book_path = self.get_book_path(book_id)
        if not book_path:
            return self._error_response("书籍不存在")

        try:
            volumes_dir = os.path.join(book_path, 'volumes')
            if not os.path.exists(volumes_dir):
                return self._success_response("搜索完成", {
                    'results': [],
                    'total': 0,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': 0
                })

            query_lower = query.lower()
            all_results = []

            # 遍历所有卷和章节
            for volume_id in os.listdir(volumes_dir):
                volume_dir = os.path.join(volumes_dir, volume_id)
                if not os.path.isdir(volume_dir):
                    continue

                # 获取卷信息
                volume_info = self._load_json(os.path.join(volume_dir, 'info.json'))
                if not volume_info:
                    continue

                # 搜索该卷下的章节
                for chapter_file in os.listdir(volume_dir):
                    if chapter_file.endswith('.json') and chapter_file != 'info.json':
                        chapter_data = self._load_json(os.path.join(volume_dir, chapter_file))
                        if chapter_data and query_lower in chapter_data.get('title', '').lower():
                            # 不返回章节内容
                            chapter_data.pop('content', None)
                            all_results.append({
                                'volume': {
                                    'id': volume_info['id'],
                                    'title': volume_info['title']
                                },
                                'chapter': chapter_data
                            })

            # 按章节创建时间排序
            all_results.sort(key=lambda x: x['chapter'].get('created_at', ''), reverse=True)

            # 分页
            total = len(all_results)
            total_pages = (total + page_size - 1) // page_size
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            results_page = all_results[start_idx:end_idx]

            return self._success_response("搜索完成", {
                'results': results_page,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': total_pages,
                'query': query
            })

        except Exception as e:
            return self._error_response(f"搜索失败: {str(e)}")

    @cached(ttl=300, key_func=lambda self, book_id, volume_id, page=1, page_size=50: f"volume_chapters_meta_{book_id}_{volume_id}_{page}_{page_size}")
    def get_volume_chapters(self, book_id, volume_id, page=1, page_size=50):
        """获取指定卷的章节列表（支持分页）"""
        book_path = self.get_book_path(book_id)
        if not book_path:
            return self._error_response("书籍不存在")

        try:
            volume_dir = os.path.join(book_path, 'volumes', volume_id)
            if not os.path.exists(volume_dir):
                return self._error_response("卷不存在")

            chapters = self._get_volume_chapters(volume_dir, page, page_size)

            # 计算分页信息
            all_chapters = self._get_volume_chapters(volume_dir, 1, 999999)  # 获取所有章节用于计算总数
            total = len(all_chapters)
            total_pages = (total + page_size - 1) // page_size

            return self._success_response("获取章节列表成功", {
                'chapters': chapters,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': total_pages
            })

        except Exception as e:
            return self._error_response(f"获取章节列表失败: {str(e)}")

    def get_chapters_by_ids(self, book_id, chapter_ids):
        """根据章节ID列表获取章节信息（用于增量更新）"""
        book_path = self.get_book_path(book_id)
        if not book_path:
            return self._error_response("书籍不存在")

        try:
            volumes_dir = os.path.join(book_path, 'volumes')
            chapters = []

            # 遍历所有卷查找指定的章节
            for volume_id in os.listdir(volumes_dir):
                volume_dir = os.path.join(volumes_dir, volume_id)
                if not os.path.isdir(volume_dir):
                    continue

                for chapter_id in chapter_ids:
                    chapter_file = os.path.join(volume_dir, f"{chapter_id}.json")
                    if os.path.exists(chapter_file):
                        chapter_data = self._load_json(chapter_file)
                        if chapter_data:
                            # 不返回章节内容
                            chapter_data.pop('content', None)
                            chapters.append(chapter_data)

            return self._success_response("获取章节信息成功", chapters)

        except Exception as e:
            return self._error_response(f"获取章节信息失败: {str(e)}")

    def create_volume(self, book_id, volume_data):
        """创建新卷"""
        for book_name in os.listdir(self.directory):
            book_dir = os.path.join(self.directory, book_name)
            info_file = os.path.join(book_dir, 'info.json')
            book_data = self._load_json(info_file)

            if book_data and book_data.get('id') == book_id:
                volumes_dir = os.path.join(book_dir, 'volumes')
                if not os.path.exists(volumes_dir):
                    os.makedirs(volumes_dir)

                # 获取现有卷的数量作为新卷的顺序
                existing_volumes = [d for d in os.listdir(volumes_dir)
                                    if os.path.isdir(os.path.join(volumes_dir, d))]
                new_order = len(existing_volumes) + 1

                volume_id = str(uuid.uuid4())
                volume_data = {
                    'id': volume_id,
                    'title': volume_data.get('title', '未命名卷'),
                    'description': volume_data.get('description', ''),
                    'order': new_order,
                    'created_at': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'updated_at': time.strftime("%Y-%m-%d %H:%M:%S")
                }

                volume_dir = os.path.join(volumes_dir, volume_id)
                os.makedirs(volume_dir, exist_ok=True)
                self._save_json(os.path.join(volume_dir, 'info.json'), volume_data)

                # 新建卷后清除卷列表缓存
                self._invalidate_volumes_cache(book_id)

                return self._success_response("创建卷成功", volume_data)
        return self._error_response("书籍不存在")

    def update_volume(self, book_id, volume_id, volume_data):
        """更新卷信息"""
        for book_name in os.listdir(self.directory):
            book_dir = os.path.join(self.directory, book_name)
            info_file = os.path.join(book_dir, 'info.json')
            book_data = self._load_json(info_file)

            if book_data and book_data.get('id') == book_id:
                volume_dir = os.path.join(book_dir, 'volumes', volume_id)
                if os.path.exists(volume_dir):
                    volume_info_path = os.path.join(volume_dir, 'info.json')
                    volume_info = self._load_json(volume_info_path)
                    if volume_info:
                        volume_info['title'] = volume_data.get('title', volume_info['title'])
                        volume_info['description'] = volume_data.get('description', volume_info['description'])
                        volume_info['updated_at'] = time.strftime("%Y-%m-%d %H:%M:%S")
                        self._save_json(volume_info_path, volume_info)

                        # 卷信息更新后清除卷列表缓存
                        self._invalidate_volumes_cache(book_id)

                        return self._success_response("更新卷成功", volume_info)
                    return self._error_response("卷信息不存在")
                return self._error_response("卷不存在")
        return self._error_response("书籍不存在")

    def delete_volume(self, book_id, volume_id):
        """删除卷"""
        for book_name in os.listdir(self.directory):
            book_dir = os.path.join(self.directory, book_name)
            info_file = os.path.join(book_dir, 'info.json')
            book_data = self._load_json(info_file)

            if book_data and book_data.get('id') == book_id:
                volume_dir = os.path.join(book_dir, 'volumes', volume_id)
                if os.path.exists(volume_dir):
                    # 检查卷是否为空
                    chapter_files = [f for f in os.listdir(volume_dir)
                                     if f.endswith('.json') and f != 'info.json']
                    if chapter_files:
                        return self._error_response("卷中还有章节，请先删除所有章节")

                    # 删除卷目录
                    import shutil
                    shutil.rmtree(volume_dir)

                    # 删除卷后清除相关缓存
                    self._invalidate_volumes_cache(book_id)
                    self._invalidate_chapters_cache(book_id, volume_id)
                    self._invalidate_search_cache(book_id)

                    return self._success_response("删除卷成功")
                return self._error_response("卷不存在")
        return self._error_response("书籍不存在")

    def create_chapter(self, book_id, volume_id, chapter_data):
        """在指定下创建新章节"""
        for book_name in os.listdir(self.directory):
            book_dir = os.path.join(self.directory, book_name)
            info_file = os.path.join(book_dir, 'info.json')
            book_data = self._load_json(info_file)

            if book_data and book_data.get('id') == book_id:
                volume_dir = os.path.join(book_dir, 'volumes', volume_id)
                if os.path.exists(volume_dir):
                    # 获取现有章节数量作为新章节的顺序
                    existing_chapters = [f for f in os.listdir(volume_dir)
                                         if f.endswith('.json') and f != 'info.json']
                    new_order = len(existing_chapters) + 1

                    chapter_id = str(uuid.uuid4())
                    chapter_data = {
                        'id': chapter_id,
                        'title': chapter_data.get('title', '未命名章节'),
                        'content': chapter_data.get('content', ''),
                        'word_count': 0,
                        'order': new_order,
                        'created_at': time.strftime("%Y-%m-%d %H:%M:%S"),
                        'updated_at': time.strftime("%Y-%m-%d %H:%M:%S")
                    }

                    chapter_file = os.path.join(volume_dir, f"{chapter_id}.json")
                    self._save_json(chapter_file, chapter_data)

                    # 新建章节后清除相关缓存
                    self._invalidate_chapters_cache(book_id, volume_id)
                    self._invalidate_search_cache(book_id)

                    return self._success_response("创建章节成功", chapter_data)
                return self._error_response("卷不存在")
        return self._error_response("书籍不存在")

    def update_chapter(self, book_id, volume_id, chapter_id, chapter_data):
        """更新章节内容"""
        for book_name in os.listdir(self.directory):
            book_dir = os.path.join(self.directory, book_name)
            info_file = os.path.join(book_dir, 'info.json')
            book_data = self._load_json(info_file)

            if book_data and book_data.get('id') == book_id:
                volume_dir = os.path.join(book_dir, 'volumes', volume_id)
                if os.path.exists(volume_dir):
                    chapter_file = os.path.join(volume_dir, f"{chapter_id}.json")
                    if os.path.exists(chapter_file):
                        chapter_info = self._load_json(chapter_file)
                        if chapter_info:
                            chapter_info['title'] = chapter_data.get('title', chapter_info['title'])
                            if 'content' in chapter_data:
                                chapter_info['content'] = chapter_data['content']
                                # 更新字数统计
                                import re
                                content_text = re.sub(r'<[^>]+>', '', chapter_data['content'])
                                chapter_info['word_count'] = len(content_text)
                            chapter_info['updated_at'] = time.strftime("%Y-%m-%d %H:%M:%S")
                            self._save_json(chapter_file, chapter_info)

                            # 更新书籍总字数
                            book_data['word_count'] = self._calculate_word_count(book_dir)
                            self._save_json(info_file, book_data)

                            # 更新章节后清除相关缓存
                            self._invalidate_chapters_cache(book_id, volume_id)
                            self._invalidate_search_cache(book_id)
                            # 由于字数变化，也要清除书籍缓存
                            self._invalidate_book_cache(book_id)

                            return self._success_response("更新章节成功", chapter_info)
                        return self._error_response("章节信息不存在")
                    return self._error_response("章节不存在")
                return self._error_response("卷不存在")
        return self._error_response("书籍不存在")

    def delete_chapter(self, book_id, volume_id, chapter_id):
        """删除章节"""
        for book_name in os.listdir(self.directory):
            book_dir = os.path.join(self.directory, book_name)
            info_file = os.path.join(book_dir, 'info.json')
            book_data = self._load_json(info_file)

            if book_data and book_data.get('id') == book_id:
                volume_dir = os.path.join(book_dir, 'volumes', volume_id)
                if os.path.exists(volume_dir):
                    chapter_file = os.path.join(volume_dir, f"{chapter_id}.json")
                    if os.path.exists(chapter_file):
                        os.remove(chapter_file)

                        # 更新书籍总字数
                        book_data['word_count'] = self._calculate_word_count(book_dir)
                        self._save_json(info_file, book_data)

                        # 删除章节后清除相关缓存
                        self._invalidate_chapters_cache(book_id, volume_id)
                        self._invalidate_search_cache(book_id)
                        # 由于字数变化，也要清除书籍缓存
                        self._invalidate_book_cache(book_id)

                        return self._success_response("删除章节成功", [])
                    return self._error_response("章节不存在")
                return self._error_response("卷不存在")
        return self._error_response("书籍不存在")

    def get_chapter(self, book_id, volume_id, chapter_id):
        """获取章节内容"""
        for book_name in os.listdir(self.directory):
            book_dir = os.path.join(self.directory, book_name)
            info_file = os.path.join(book_dir, 'info.json')
            book_data = self._load_json(info_file)

            if book_data and book_data.get('id') == book_id:
                volume_dir = os.path.join(book_dir, 'volumes', volume_id)
                if os.path.exists(volume_dir):
                    chapter_file = os.path.join(volume_dir, f"{chapter_id}.json")
                    if os.path.exists(chapter_file):
                        chapter_data = self._load_json(chapter_file)
                        return self._success_response("获取章节成功", chapter_data)
                    return self._error_response("章节不存在")
                return self._error_response("卷不存在")
        return self._error_response("书籍不存在")

    def get_templates(self, book_id):
        """获取书籍的所有模板"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            # 这个是设定的意思，下面是设定路径
            setting_path = os.path.join(book_path, 'settings')
            if not os.path.exists(setting_path):
                os.makedirs(setting_path)

            templates_file = os.path.join(setting_path, 'templates.json')
            if not os.path.exists(templates_file):
                # 如果文件不存在，创建一个空的模板列表
                self._save_json(templates_file, [])
                return self._success_response("创建模板。", [])

            templates = self._load_json(templates_file)
            if templates is None:
                templates = []
            return self._success_response("成功获取模板。", templates)
        except Exception as e:
            return self._error_response(str(e))

    @cached(ttl=300, key_func=lambda *args: f"entities_{args[1]}")
    def get_entities(self, book_id):
        """获取书籍的所有实体"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")

            setting_path = os.path.join(book_path, 'settings')
            if not os.path.exists(setting_path):
                return self._success_response("没有创建过设定文件夹。", [])

            templates_file = os.path.join(setting_path, 'templates.json')
            if not os.path.exists(templates_file):
                return self._success_response("没有数据。", [])

            templates = self._load_json(templates_file)
            if not templates:
                return self._success_response("没有模板。", [])

            # 收集所有实体
            entities = []
            for template in templates:
                template_dir = os.path.join(setting_path, template['id'])
                if not os.path.exists(template_dir):
                    continue

                entities_file = os.path.join(template_dir, 'entities.json')
                if not os.path.exists(entities_file):
                    continue

                template_entities = self._load_json(entities_file)
                if template_entities:
                    for entity in template_entities:
                        entity['template_id'] = template['id']
                        # 确保实体有所有必要的字段
                        if 'name' not in entity:
                            entity['name'] = '未命名实体'
                        if 'description' not in entity:
                            entity['description'] = ''
                    entities.extend(template_entities)

            return self._success_response("获取成功。", entities)
        except Exception as e:
            return self._error_response(str(e))

    def save_template(self, template_data):
        """保存模板"""
        try:
            book_id = template_data.get('book_id')
            if not book_id:
                return self._error_response("缺少书籍ID")

            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")

            setting_path = os.path.join(book_path, 'settings')
            if not os.path.exists(setting_path):
                os.makedirs(setting_path)

            templates_file = os.path.join(setting_path, 'templates.json')

            # 加载现有模板
            templates = []
            if os.path.exists(templates_file):
                templates = self._load_json(templates_file) or []

            now = datetime.datetime.now().isoformat()

            # 确保维度列表存在
            if 'dimensions' not in template_data:
                template_data['dimensions'] = []

            if template_data.get('id'):
                # 更新现有模板
                template_id = template_data['id']
                template_index = next((i for i, t in enumerate(templates) if t['id'] == template_id), -1)
                if template_index == -1:
                    return self._error_response("模板不存在")

                templates[template_index].update({
                    'name': template_data['name'],
                    'description': template_data.get('description', ''),
                    'dimensions': template_data['dimensions'],
                    'updated_at': now
                })
                result = templates[template_index]
            else:
                # 创建新模板
                new_template = {
                    'id': str(uuid.uuid4()),
                    'name': template_data['name'],
                    'description': template_data.get('description', ''),
                    'dimensions': template_data['dimensions'],
                    'created_at': now,
                    'updated_at': now
                }
                templates.append(new_template)
                result = new_template

                # 创建模板目录
                template_dir = os.path.join(setting_path, new_template['id'])
                os.makedirs(template_dir, exist_ok=True)

                # 创建空的实体文件
                entities_file = os.path.join(template_dir, 'entities.json')
                self._save_json(entities_file, [])

            # 保存模板列表
            self._save_json(templates_file, templates)

            return self._success_response("保存模板", result)
        except Exception as e:
            return self._error_response(str(e))

    def update_template_order(self, order_data):
        """更新模板排序"""
        try:
            book_id = order_data.get('book_id')
            if not book_id:
                return self._error_response("缺少书籍ID")

            templates_order = order_data.get('templates')
            if not templates_order:
                return self._error_response("缺少模板排序数据")

            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")

            setting_path = os.path.join(book_path, 'settings')
            templates_file = os.path.join(setting_path, 'templates.json')

            if not os.path.exists(templates_file):
                return self._error_response("模板文件不存在")

            # 加载现有模板
            templates = self._load_json(templates_file)
            if not templates:
                return self._error_response("模板数据为空")

            # 创建模板ID到排序的映射
            order_map = {item['id']: item['sort_order'] for item in templates_order}

            # 更新每个模板的sort_order字段
            for template in templates:
                if template['id'] in order_map:
                    template['sort_order'] = order_map[template['id']]

            # 保存更新后的模板列表
            self._save_json(templates_file, templates)

            return self._success_response("更新模板排序成功")
        except Exception as e:
            return self._error_response(f"更新模板排序失败: {str(e)}")

    def delete_template(self, template_id, book_id):
        """删除设定模板"""
        try:
            setting_path = self.get_book_setting_path(book_id)
            if not setting_path:
                return self._error_response("书籍不存在")

            templates_file = os.path.join(setting_path, 'templates.json')
            if not os.path.exists(templates_file):
                return self._error_response("模板不存在")

            # 删除模板
            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)

            if not any(t['id'] == template_id for t in templates):
                return self._error_response("模板不存在")

            templates = [t for t in templates if t['id'] != template_id]

            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, ensure_ascii=False, indent=2)

            # 删除模板目录及其所有实体
            template_dir = os.path.join(setting_path, template_id)
            if os.path.exists(template_dir):
                shutil.rmtree(template_dir)

            # 清除实体缓存，因为删除模板会删除其下的所有实体
            cache_manager.delete(f"entities_{book_id}")

            return self._success_response(None)
        except Exception as e:
            return self._error_response(str(e))

    def save_entity(self, entity_data):
        """保存设定实体"""
        try:
            book_id = entity_data['book_id']
            template_id = entity_data['template_id']
            setting_path = self.get_book_setting_path(book_id)
            if not setting_path:
                return self._error_response("书籍不存在")

            template_dir = os.path.join(setting_path, template_id)
            entities_file = os.path.join(template_dir, 'entities.json')
            templates_file = os.path.join(setting_path, 'templates.json')

            # 确保模板目录存在
            if not os.path.exists(template_dir):
                return self._error_response("模板不存在")

            # 验证模板是否存在并获取其维度定义
            if not os.path.exists(templates_file):
                return self._error_response("模板文件不存在")

            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)

            template = next((t for t in templates if t['id'] == template_id), None)
            if not template:
                return self._error_response("模板不存在")

            # 验证所有必需的维度都已提供
            template_dimensions = {d['name']: d for d in template.get('dimensions', [])}
            provided_dimensions = entity_data.get('dimensions', {})

            for dim_key, dim_def in template_dimensions.items():
                if dim_def.get('required', False) and dim_key not in provided_dimensions:
                    return self._error_response(f"缺少必需的维度: {dim_def.get('name', dim_key)}")

            # 加载现有实体
            entities = []
            if os.path.exists(entities_file):
                with open(entities_file, 'r', encoding='utf-8') as f:
                    entities = json.load(f)

            now = datetime.datetime.now().isoformat()

            if entity_data.get('id'):

                # 更新现有体
                entity_id = entity_data['id']
                entity_index = next((i for i, e in enumerate(entities) if e['id'] == entity_id), -1)
                if entity_index == -1:
                    return self._error_response("实体不存在")

                entities[entity_index].update({
                    'name': entity_data['name'],
                    'description': entity_data.get('description', ''),
                    'dimensions': provided_dimensions,
                    'updated_at': now
                })
                result = entities[entity_index]
            else:
                # 创建新实体
                new_entity = {
                    'id': str(uuid.uuid4()),
                    'name': entity_data['name'],
                    'description': entity_data.get('description', ''),
                    'dimensions': provided_dimensions,
                    'created_at': now,
                    'updated_at': now
                }
                entities.append(new_entity)
                result = new_entity

            # 保存实体列表
            with open(entities_file, 'w', encoding='utf-8') as f:
                json.dump(entities, f, ensure_ascii=False, indent=2)

            # 清除实体缓存，确保下次获取时能获取到最新数据
            cache_manager.delete(f"entities_{book_id}")

            # 在返回结果中添加模板ID
            result['template_id'] = template_id
            return self._success_response("添加结果。", result)
        except Exception as e:
            return self._error_response(str(e))

    def delete_entity(self, entity_id, book_id, template_id):
        """删除设定实体"""
        try:
            setting_path = self.get_book_setting_path(book_id)
            if not setting_path:
                return self._error_response("书籍不存在")

            template_dir = os.path.join(setting_path, template_id)
            entities_file = os.path.join(template_dir, 'entities.json')

            if not os.path.exists(entities_file):
                return self._error_response("实体不存在")

            with open(entities_file, 'r', encoding='utf-8') as f:
                entities = json.load(f)

            if not any(e['id'] == entity_id for e in entities):
                return self._error_response("实体不存在")

            # 删除实体
            entities = [e for e in entities if e['id'] != entity_id]
            with open(entities_file, 'w', encoding='utf-8') as f:
                json.dump(entities, f, ensure_ascii=False, indent=2)

            # 清除实体缓存，确保下次获取时能获取到最新数据
            cache_manager.delete(f"entities_{book_id}")

            return self._success_response(None)
        except Exception as e:
            return self._error_response(str(e))

    def save_entity_export(self, data):
        try:
            # 获取基本参数
            book_id = data.get('book_id')
            template_id = data.get('template_id')
            export_type = data.get('type')
            
            if not all([book_id, template_id, export_type]):
                return self._error_response('缺少必要参数')
                
            # 获取书籍路径
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
                
            # 准备导出内容
            content = ""
            template_name = ""
            
            # 根据导出类型处理数据
            if template_id == 'all' and export_type == 'all_names':
                # 获取所有实体
                entities_response = self.get_entities(book_id)
                if isinstance(entities_response, str):
                    entities_response = json.loads(entities_response)
                
                if entities_response.get('status') != 'success':
                    return self._error_response('获取实体数据失败')
                
                entities = entities_response.get('data', [])
                if not entities:
                    return self._error_response('没有可导出的实体')
                
                # 格式化导出内容 - 一行一个实体名称
                content = '\n'.join([entity.get('name', '未命名') for entity in entities])
                template_name = '所有模板'
                
            elif export_type in ['names', 'details', 'details_json']:
                # 获取指定模板
                setting_path = os.path.join(book_path, 'settings')
                templates_file = os.path.join(setting_path, 'templates.json')
                
                if not os.path.exists(templates_file):
                    return self._error_response('模板文件不存在')
                
                templates = self._load_json(templates_file)
                current_template = next((t for t in templates if t['id'] == template_id), None)
                
                if not current_template:
                    return self._error_response('模板不存在')
                
                template_name = current_template.get('name', '未命名模板')
                
                # 获取模板下的实体
                template_dir = os.path.join(setting_path, template_id)
                entities_file = os.path.join(template_dir, 'entities.json')
                
                if not os.path.exists(entities_file):
                    return self._error_response('实体文件不存在')
                
                entities = self._load_json(entities_file)
                if not entities:
                    return self._error_response('当前模板下没有实体')
                
                # 根据导出类型格式化内容
                if export_type == 'names':
                    # 导出实体名称列表
                    content = '\n'.join([entity.get('name', '未命名') for entity in entities])
                elif export_type == 'details':
                    # 导出详细信息
                    content = f"{template_name}实体列表\n\n"
                    for index, entity in enumerate(entities):
                        content += f"{index + 1}. {entity.get('name', '未命名')}\n"
                        if entity.get('description'):
                            content += f"   描述：{entity['description']}\n"
                        if entity.get('dimensions'):
                            content += '   属性：\n'
                            for key, value in entity['dimensions'].items():
                                content += f"   - {key}: {value}\n"
                        content += '\n'
                elif export_type == 'details_json':
                    # 导出JSON格式的详细信息
                    content = json.dumps(entities, ensure_ascii=False, indent=2)
            else:
                return self._error_response('不支持的导出类型')

            # 根据导出类型设置文件类型和文件名
            current_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

            if export_type == 'details_json':
                file_types = ('JSON files (*.json)',)
                if template_id == 'all' and export_type == 'all_names':
                    default_name = f'所有实体名称_{current_time}.json'
                else:
                    default_name = f'{template_name}_{export_type}_{current_time}.json'
            else:
                file_types = ('Text files (*.txt)',)
                if template_id == 'all' and export_type == 'all_names':
                    default_name = f'所有实体名称_{current_time}.txt'
                else:
                    default_name = f'{template_name}_{export_type}_{current_time}.txt'
            
            import webview
            file_path = webview.windows[0].create_file_dialog(
                webview.SAVE_DIALOG,
                directory='',
                save_filename=default_name,
                file_types=file_types
            )

            if not file_path:
                return self._error_response('未选择保存位置')

            file_path = file_path[0] if isinstance(file_path, list) else file_path

            # 根据导出类型选择编码格式
            if export_type == 'details_json':
                # JSON文件使用UTF-8编码
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            else:
                # 文本文件使用gbk编码（主要讯飞识别的只能是gbk）
                with open(file_path, 'w', encoding='gbk') as f:
                    f.write(content)

            return self._success_response('导出成功')

        except Exception as e:
            return self._error_response(f'导出失败：{str(e)}')

    def export_timeline_events(self, book_id, export_type='names'):
        """导出时间线事件
        Args:
            book_id: 书籍ID
            export_type: 导出类型，'names' 或 'details'
        """
        try:
            timeline_data = self.get_timeline(book_id)
            # print(timeline_data)
            # 上面是一个json字符串
            timeline_data = json.loads(timeline_data)
            if timeline_data.get('status') != 'success':
                return self._error_response('获取时间线数据失败')

            data = timeline_data['data']
            content = ''
            
            if export_type == 'names':
                # 只导出事件名称列表
                event_names = []
                for track in data.get('tracks', []):
                    for event in track['events']:
                        event_names.append(event['title'])
                content = '\n'.join(sorted(event_names))
                file_name = '时间线事件名称'
                
            elif export_type == 'details':
                # 导出详细信息，按轨道顺序组织
                text_content = []
                for track in data.get('tracks', []):
                    text_content.append(f"=== {track['name']} ===")
                    # 按位置排序事件
                    sorted_events = sorted(track['events'], key=lambda x: x['position'])
                    for event in sorted_events:
                        text_content.append(f"- {event['title']}")
                        if event.get('description'):
                            text_content.append(f"  描述: {event['description']}")
                        if event.get('startTime'):
                            text_content.append(f"  开始时间: {event['startTime']}")
                        if event.get('endTime'):
                            text_content.append(f"  结束时间: {event['endTime']}")
                        if event.get('type'):
                            text_content.append(f"  类型: {event['type']}")
                        text_content.append("")
                    text_content.append("")
                content = '\n'.join(text_content)
                file_name = '时间线事件详情'
            else:
                return self._error_response('不支持的导出类型')

            # 打开文件保存对话框
            file_types = ('Text files (*.txt)',)
            current_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            default_name = f'{file_name}_{current_time}.txt'
            
            import webview
            file_path = webview.windows[0].create_file_dialog(
                webview.SAVE_DIALOG,
                directory='',
                save_filename=default_name,
                file_types=file_types
            )

            if not file_path:
                return self._error_response('未选择保存位置')

            file_path = file_path[0] if isinstance(file_path, list) else file_path

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return self._success_response('导出成功')
                
        except Exception as e:
            return self._error_response(f'导出时间线失败: {str(e)}')

    def export_scenes(self,scenes_data):
        """导出场景数据"""
        try:

            # 打开文件保存对话框
            file_types = ('Text files (*.json)',)
            current_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            default_name = f'场景卡_{current_time}.json'

            import webview
            file_path = webview.windows[0].create_file_dialog(
                webview.SAVE_DIALOG,
                directory='',
                save_filename=default_name,
                file_types=file_types
            )

            if not file_path:
                return self._error_response('未选择保存位置')

            file_path = file_path[0] if isinstance(file_path, list) else file_path

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(scenes_data)

            return self._success_response('导出成功')

        except Exception as e:
            return self._error_response(f'导出失败：{str(e)}')

    def import_scenes(self, scenes_data):
        """导入场景数据
        Args:
            scenes_data: 场景数据列表
        """
        pass

    def save_plot(self, book_id, plot_data):
        """保存剧情组合"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")

            plots_path = os.path.join(book_path, 'plots')
            if not os.path.exists(plots_path):
                os.makedirs(plots_path)
            plots_file = os.path.join(plots_path, 'plots.json')
            
            # 读取现有数据
            plots = self._load_json(plots_file) or []
            
            # 生成唯一ID
            plot_data['id'] = str(uuid.uuid4())
            
            # 添加新的剧情组合
            plots.append(plot_data)
            
            # 保存更新后的数据
            self._save_json(plots_file, plots)
            
            return self._success_response("保存剧情组合成功", plot_data)
        except Exception as e:
            return self._error_response(f"保存剧情组合失败: {str(e)}")

    def delete_plot(self, book_id, plot_id):
        """删除剧情组合"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")

            plots_path = os.path.join(book_path, 'plots')
            plots_file = os.path.join(plots_path, 'plots.json')
            
            # 读取现有数据
            plots = self._load_json(plots_file)
            if not plots:
                return self._error_response("剧情组合不存在")
            
            # 查找并删除指定的剧情组合
            plots = [plot for plot in plots if plot.get('id') != plot_id]
            
            # 保存更新后的数据
            self._save_json(plots_file, plots)
            
            return self._success_response("删除剧情组合成功")
        except Exception as e:
            return self._error_response(f"删除剧情组合失败: {str(e)}")

    def get_plots(self, book_id):
        """获取书籍的所有剧情组合"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")

            plots_path = os.path.join(book_path, 'plots')
            if not os.path.exists(plots_path):
                os.makedirs(plots_path)

            plots_file = os.path.join(plots_path, 'plots.json')
            plots = self._load_json(plots_file)

            # 如果文件不存在或数据为空，返回空列表
            if not plots:
                plots = []
                # 创建默认的空文件
                self._save_json(plots_file, plots)

            return self._success_response("获取剧情组合成功", plots)
        except Exception as e:
            return self._error_response(f"获取剧情组合失败: {str(e)}")

    def update_plot(self, book_id, plot_id, plot_data):
        """更新剧情组合"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")

            plots_path = os.path.join(book_path, 'plots')
            plots_file = os.path.join(plots_path, 'plots.json')
            
            # 读取现有数据
            plots = self._load_json(plots_file)
            if not plots:
                return self._error_response("剧情组合不存在")
            
            # 查找并更新指定的剧情组合
            plot_index = next((index for (index, plot) in enumerate(plots) 
                             if plot.get('id') == plot_id), -1)
            
            if plot_index == -1:
                return self._error_response("未找到指定的剧情组合")
            
            # 保持原有的ID和创建时间
            plot_data['id'] = plot_id
            if 'created_at' in plots[plot_index]:
                plot_data['created_at'] = plots[plot_index]['created_at']
            
            # 更新数据
            plots[plot_index] = plot_data
            
            # 保存更新后的数据
            self._save_json(plots_file, plots)
            
            return self._success_response("更新剧情组合成功", plot_data)
        except Exception as e:
            return self._error_response(f"更新剧情组合失败: {str(e)}")

    def save_prompt_rule(self, book_id, prompt_data):
        """保存提示词规则
        Args:
            book_id: 书籍ID
            prompt_data: 提示词规则数据
        """
        try:
            # print(f"准备保存提示词规则，书籍ID：{book_id}")
            book_path = self.get_book_path(book_id)
            if not book_path:
                print(f"书籍不存在，路径：{book_id}")
                return self._error_response("书籍不存在")
            
            # 创建prompts目录
            prompts_path = os.path.join(book_path, 'prompts')
            if not os.path.exists(prompts_path):
                os.makedirs(prompts_path, exist_ok=True)
                print(f"创建prompts目录：{prompts_path}")
            
            # 加载现有提示词规则
            prompts_file = os.path.join(prompts_path, 'prompts.json')
            prompts = self._load_json(prompts_file) or []
            print(f"当前规则列表数量：{len(prompts)}")
            
            # 解析传入的提示词数据
            if isinstance(prompt_data, str):
                print(f"解析字符串数据，长度：{len(prompt_data)}")
                try:
                    data = json.loads(prompt_data)
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误：{str(e)}")
                    return self._error_response(f"提示词数据格式错误: {str(e)}")
            else:
                print(f"使用对象数据，类型：{type(prompt_data)}")
                data = prompt_data
            
            # 检查名称重复
            name = data.get('name', '')
            data_id = data.get('id')
            
            # 检查是否有其他规则使用了相同的名称
            name_exists = any(p.get('name') == name and p.get('id') != data_id for p in prompts)
            if name_exists:
                print(f"规则名称 '{name}' 已存在")
                return self._error_response(f"规则名称 '{name}' 已存在，请使用不同的名称")
            
            # 确保有ID字段，如果没有则生成
            if 'id' not in data or not data['id']:
                data['id'] = str(uuid.uuid4())
                print(f"生成新ID：{data['id']}")
            
            # 添加/更新时间戳
            data['createTime'] = data.get('createTime', int(time.time() * 1000))
            data['updateTime'] = int(time.time() * 1000)
            
            # 检查是否存在相同ID的提示词规则
            existing_index = next((i for i, p in enumerate(prompts) if p.get('id') == data.get('id')), None)
            
            if existing_index is not None:
                # 更新现有规则
                print(f"更新现有规则，索引：{existing_index}")
                prompts[existing_index] = data
            else:
                # 添加新规则
                print(f"添加新规则")
                prompts.append(data)
            
            # 保存到文件
            print(f"保存规则列表，数量：{len(prompts)}")
            self._save_json(prompts_file, prompts)
            
            print("提示词规则保存成功")
            return self._success_response("保存提示词规则成功", data)
        except Exception as e:
            import traceback
            print(f"保存提示词规则失败: {str(e)}")
            print(traceback.format_exc())
            return self._error_response(f"保存提示词规则失败: {str(e)}")
    
    def get_prompt_rules(self, book_id):
        """获取书籍的所有提示词规则
        Args:
            book_id: 书籍ID
        """
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
                
            # 创建prompts目录
            prompts_path = os.path.join(book_path, 'prompts')
            if not os.path.exists(prompts_path):
                os.makedirs(prompts_path, exist_ok=True)
                
            # 加载提示词规则
            prompts_file = os.path.join(prompts_path, 'prompts.json')
            prompts = self._load_json(prompts_file)
            
            # 如果文件不存在或数据为空，返回空列表
            if not prompts:
                prompts = []
                # 保存空列表
                self._save_json(prompts_file, prompts)
                
            return self._success_response("获取提示词规则成功", prompts)
        except Exception as e:
            return self._error_response(f"获取提示词规则失败: {str(e)}")
    
    def delete_prompt_rule(self, book_id, prompt_id):
        """删除提示词规则
        Args:
            book_id: 书籍ID
            prompt_id: 提示词规则ID
        Returns:
            删除结果
        """
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            
            # 获取提示词规则文件
            prompts_path = os.path.join(book_path, 'prompts')
            prompts_file = os.path.join(prompts_path, 'prompts.json')
            
            # 加载现有提示词规则
            prompts = self._load_json(prompts_file)
            if not prompts:
                return self._error_response("提示词规则不存在")
            
            # 确保ID格式一致进行比较（将两者都转为字符串）
            original_length = len(prompts)
            str_prompt_id = str(prompt_id)
            new_prompts = []
            found = False
            
            for p in prompts:
                if str(p.get('id')) == str_prompt_id:
                    found = True
                    # 跳过要删除的规则
                    continue
                new_prompts.append(p)
            
            if not found:
                return self._error_response("未找到指定的提示词规则")
                
            # 更新为过滤后的列表
            prompts = new_prompts
                
            # 保存更新后的列表
            self._save_json(prompts_file, prompts)
            
            return self._success_response({"id": prompt_id, "message": "删除提示词规则成功"})
        except Exception as e:
            print(f"删除提示词规则异常: {str(e)}")
            return self._error_response(f"删除提示词规则失败: {str(e)}")

    def get_book_relations(self, book_id):
        """获取书籍中人物关系数据"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            
            relations_path = os.path.join(book_path, 'relations')
            if not os.path.exists(relations_path):
                os.makedirs(relations_path, exist_ok=True)
            
            relations_file = os.path.join(relations_path, 'relations.json')
            if not os.path.exists(relations_file):
                # 如果文件不存在，创建一个空的关系文件
                relations_data = {"relations": []}
                self._save_json(relations_file, relations_data)
                return self._success_response("获取关系数据成功", relations_data)
            
            relations_data = self._load_json(relations_file)
            if not relations_data:
                relations_data = {"relations": []}
                self._save_json(relations_file, relations_data)
            
            return self._success_response("获取关系数据成功", relations_data)
        except Exception as e:
            return self._error_response(f"获取关系数据失败: {str(e)}")

    def save_relation(self, book_id, relations_data):
        """保存书籍的人物关系数据（整个文件）"""
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            
            relations_path = os.path.join(book_path, 'relations')
            if not os.path.exists(relations_path):
                os.makedirs(relations_path, exist_ok=True)
            
            relations_file = os.path.join(relations_path, 'relations.json')
            
            # 确保数据格式正确
            if not isinstance(relations_data, dict):
                return self._error_response("关系数据格式错误，应为包含relations数组的对象")
            
            # 确保有relations数组
            if "relations" not in relations_data or not isinstance(relations_data["relations"], list):
                relations_data = {"relations": []}
            
            # 保存整个关系数据文件
            self._save_json(relations_file, relations_data)
            
            return self._success_response("保存关系数据成功", relations_data)
        except Exception as e:
            return self._error_response(f"保存关系数据失败: {str(e)}")

    def delete_relation(self, book_id, relation_data_or_id):
        """
        删除关系并保存数据文件
        如果传入的是整个关系数据对象，直接保存
        如果传入的是关系ID，先从文件中删除该关系再保存
        """
        try:
            book_path = self.get_book_path(book_id)
            if not book_path:
                return self._error_response("书籍不存在")
            
            relations_path = os.path.join(book_path, 'relations')
            if not os.path.exists(relations_path):
                os.makedirs(relations_path, exist_ok=True)
            
            relations_file = os.path.join(relations_path, 'relations.json')
            
            # 检查输入参数类型
            if isinstance(relation_data_or_id, dict):
                # 如果是整个关系数据对象，直接保存
                self._save_json(relations_file, relation_data_or_id)
                return self._success_response("关系数据已更新", relation_data_or_id)
            else:
                # 如果是单个关系ID，从文件中删除该关系
                relation_id = relation_data_or_id
                relations_data = self._load_json(relations_file)
                
                if not relations_data or "relations" not in relations_data:
                    return self._error_response("关系数据文件无效")
                
                # 查找并删除指定ID的关系
                relations = relations_data["relations"]
                original_length = len(relations)
                relations_data["relations"] = [r for r in relations if r.get("id") != relation_id]
                
                if len(relations_data["relations"]) == original_length:
                    return self._error_response(f"未找到ID为{relation_id}的关系")
                
                # 保存更新后的关系数据
                self._save_json(relations_file, relations_data)
                return self._success_response("关系已删除", relations_data)
        except Exception as e:
            return self._error_response(f"删除关系失败: {str(e)}")

    def get_story_inspiration(self):
        """获取故事灵感卡池数据"""
        try:
            # 确保配置目录存在
            config_dir = os.path.join(os.path.dirname(self.base_dir), "config")
            inspiration_file = os.path.join(config_dir, "story_inspiration.json")
            
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
                
            # 检查文件是否存在
            if not os.path.exists(inspiration_file):
                print(f"灵感卡池文件不存在，将创建: {inspiration_file}")
                
                # 创建默认数据结构
                default_data = {
                    "categories": {
                        "theme": {
                            "name": "主题层",
                            "description": "故事的核心主题与情感基调",
                            "icon": "Sunrise",
                            "color": "primary",
                            "defaultCount": 2,
                            "maxCount": 5
                        },
                        "volume": {
                            "name": "卷级结构",
                            "description": "故事的大纲架构与发展脉络",
                            "icon": "Connection",
                            "color": "success",
                            "defaultCount": 4,
                            "maxCount": 8
                        },
                        "keyPoint": {
                            "name": "关键点",
                            "description": "故事中的重要转折与关键节点",
                            "icon": "Key",
                            "color": "warning",
                            "defaultCount": 5,
                            "maxCount": 8
                        },
                        "technique": {
                            "name": "技法卡",
                            "description": "用于优化剧情的各种写作技巧",
                            "icon": "TrendCharts",
                            "color": "danger",
                            "defaultCount": 3,
                            "maxCount": 5
                        }
                    },
                    "theme": [],
                    "volume": [],
                    "keyPoint": [],
                    "technique": []
                }
                
                # 保存默认数据
                with open(inspiration_file, 'w', encoding='utf-8') as f:
                    json.dump(default_data, f, ensure_ascii=False, indent=2)
                
                print(f"已创建默认灵感卡池: {inspiration_file}")
                return self._success_response("创建默认故事灵感卡池成功", default_data)
            
            # 读取现有卡池数据
            try:
                print(f"正在读取灵感卡池: {inspiration_file}")
                with open(inspiration_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 确保数据结构完整
                if not data or not isinstance(data, dict):
                    raise ValueError("卡池数据格式无效")
                
                # 确保必要字段存在
                required_keys = ["categories", "theme", "volume", "keyPoint", "technique"]
                for key in required_keys:
                    if key not in data:
                        data[key] = [] if key != "categories" else {}
                
                print(f"灵感卡池读取成功: {len(data.get('theme', []))} 个主题, {len(data.get('volume', []))} 个卷级结构")
                return self._success_response("获取故事灵感卡池成功", data)
            except Exception as e:
                print(f"读取故事灵感卡池文件失败: {str(e)}")
                # 文件可能损坏，创建新的默认数据
                default_data = {
                    "categories": {
                        "theme": {
                            "name": "主题层",
                            "description": "故事的核心主题与情感基调",
                            "icon": "Sunrise",
                            "color": "primary",
                            "defaultCount": 2,
                            "maxCount": 5
                        },
                        "volume": {
                            "name": "卷级结构",
                            "description": "故事的大纲架构与发展脉络",
                            "icon": "Connection",
                            "color": "success",
                            "defaultCount": 4,
                            "maxCount": 8
                        },
                        "keyPoint": {
                            "name": "关键点",
                            "description": "故事中的重要转折与关键节点",
                            "icon": "Key",
                            "color": "warning",
                            "defaultCount": 5,
                            "maxCount": 8
                        },
                        "technique": {
                            "name": "技法卡",
                            "description": "用于优化剧情的各种写作技巧",
                            "icon": "TrendCharts",
                            "color": "danger",
                            "defaultCount": 3,
                            "maxCount": 5
                        }
                    },
                    "theme": [],
                    "volume": [],
                    "keyPoint": [],
                    "technique": []
                }
                
                # 保存默认数据
                with open(inspiration_file, 'w', encoding='utf-8') as f:
                    json.dump(default_data, f, ensure_ascii=False, indent=2)
                
                print(f"已修复灵感卡池: {inspiration_file}")
                return self._success_response("修复故事灵感卡池成功", default_data)
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"获取故事灵感卡池失败: {str(e)}")
            
            # 即使出错，也返回默认数据结构
            default_data = {
                "categories": {
                    "theme": {
                        "name": "主题层",
                        "description": "故事的核心主题与情感基调",
                        "icon": "Sunrise",
                        "color": "primary",
                        "defaultCount": 2,
                        "maxCount": 5
                    },
                    "volume": {
                        "name": "卷级结构",
                        "description": "故事的大纲架构与发展脉络",
                        "icon": "Connection",
                        "color": "success",
                        "defaultCount": 4,
                        "maxCount": 8
                    },
                    "keyPoint": {
                        "name": "关键点",
                        "description": "故事中的重要转折与关键节点",
                        "icon": "Key",
                        "color": "warning",
                        "defaultCount": 5,
                        "maxCount": 8
                    },
                    "technique": {
                        "name": "技法卡",
                        "description": "用于优化剧情的各种写作技巧",
                        "icon": "TrendCharts",
                        "color": "danger",
                        "defaultCount": 3,
                        "maxCount": 5
                    }
                },
                "theme": [],
                "volume": [],
                "keyPoint": [],
                "technique": []
            }
            
            return self._success_response("发生错误但已返回默认数据", default_data)

    def save_story_inspiration(self, inspiration_data):
        """保存故事灵感卡池数据"""
        try:
            # 确保配置目录存在 - 修正路径与get方法一致
            config_dir = os.path.join(os.path.dirname(self.base_dir), "config")
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 故事灵感卡池文件路径
            inspiration_file = os.path.join(config_dir, "story_inspiration.json")
            
            # 验证数据结构
            if not isinstance(inspiration_data, dict):
                return self._error_response("数据格式错误，应为包含类别和元素的对象")
            
            # 确保有必要的字段
            required_keys = ["categories", "theme", "volume", "keyPoint", "technique"]
            for key in required_keys:
                if key not in inspiration_data:
                    inspiration_data[key] = [] if key != "categories" else {}
            
            # 保存数据
            with open(inspiration_file, 'w', encoding='utf-8') as f:
                json.dump(inspiration_data, f, ensure_ascii=False, indent=2)
            
            print(f"已保存灵感卡池数据到: {inspiration_file}")
            return self._success_response("保存故事灵感卡池成功", inspiration_data)
        except Exception as e:
            import traceback
            traceback.print_exc()
            return self._error_response(f"保存故事灵感卡池失败: {str(e)}")

    def save_inspiration_category(self, category_key, elements):
        """保存特定类别的灵感元素"""
        try:
            print(f"开始保存灵感类别: {category_key}")
            
            # 确保配置目录存在
            config_dir = os.path.join(os.path.dirname(self.base_dir), "config")
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 故事灵感卡池文件路径
            inspiration_file = os.path.join(config_dir, "story_inspiration.json")
            
            # 直接读取文件，而不是调用get_story_inspiration()
            try:
                if os.path.exists(inspiration_file):
                    with open(inspiration_file, 'r', encoding='utf-8') as f:
                        inspiration_data = json.load(f)
                else:
                    # 如果文件不存在，创建默认结构
                    inspiration_data = {
                        "categories": {
                            "theme": {
                                "name": "主题层",
                                "description": "故事的核心主题与情感基调",
                                "icon": "Sunrise",
                                "color": "primary",
                                "defaultCount": 2,
                                "maxCount": 5
                            },
                            "volume": {
                                "name": "卷级结构",
                                "description": "故事的大纲架构与发展脉络",
                                "icon": "Connection",
                                "color": "success",
                                "defaultCount": 4,
                                "maxCount": 8
                            },
                            "keyPoint": {
                                "name": "关键点",
                                "description": "故事中的重要转折与关键节点",
                                "icon": "Key",
                                "color": "warning",
                                "defaultCount": 5,
                                "maxCount": 8
                            },
                            "technique": {
                                "name": "技法卡",
                                "description": "用于优化剧情的各种写作技巧",
                                "icon": "TrendCharts",
                                "color": "danger",
                                "defaultCount": 3,
                                "maxCount": 5
                            }
                        },
                        "theme": [],
                        "volume": [],
                        "keyPoint": [],
                        "technique": []
                    }
            except Exception as e:
                print(f"读取灵感卡池文件失败: {str(e)}")
                return self._error_response(f"读取灵感卡池文件失败: {str(e)}")
            
            # 检查类别是否有效
            if category_key not in ["theme", "volume", "keyPoint", "technique"]:
                return self._error_response(f"无效的类别: {category_key}")
            
            # 更新指定类别的数据
            inspiration_data[category_key] = elements
            
            # 保存更新后的数据
            try:
                with open(inspiration_file, 'w', encoding='utf-8') as f:
                    json.dump(inspiration_data, f, ensure_ascii=False, indent=2)
                
                print(f"已保存灵感类别 {category_key}, 共 {len(elements)} 个元素")
                return self._success_response(f"灵感类别 {category_key} 保存成功", inspiration_data)
            except Exception as e:
                print(f"保存灵感卡池文件失败: {str(e)}")
                return self._error_response(f"保存灵感卡池文件失败: {str(e)}")
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"保存灵感类别失败: {str(e)}")
            return self._error_response(f"保存灵感类别失败: {str(e)}")


class BookController(ResponsePacket):
    def __init__(self, base_dir):
        super().__init__()
        # 保存base_dir作为实例属性，解决'BookController' object has no attribute 'base_dir'错误
        self.base_dir = base_dir
        self.book_dir = os.path.join(base_dir, "book")
        os.makedirs(self.book_dir, exist_ok=True)
        self.custom_pools = os.path.join(base_dir, "config", "custom_pools.json")
        self.character_inspiration = os.path.join(base_dir, "config", "character_inspiration.json")
        # 新增剧情桥段配置文件路径
        self.bridge_configs = os.path.join(base_dir, "config", "bridge_configs.json")
        self.bridge_design = os.path.join(base_dir, "config", "bridge_design.json")
        self.outline_configs = os.path.join(base_dir, "config", "outline_configs.json")
        # 初始化各个管理器
        self.books = BooksManager(self.book_dir)

    # 自定义卡池相关方法
    def get_custom_pools(self):
        """获取所有自定义卡池"""
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.custom_pools)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 检查文件是否存在，不存在则创建
            if not os.path.exists(self.custom_pools):
                with open(self.custom_pools, 'w', encoding='utf-8') as f:
                    json.dump([], f)
                return self._success_response("获取自定义卡池成功", [])
                
            # 读取卡池数据
            with open(self.custom_pools, 'r', encoding='utf-8') as f:
                pools = json.load(f)
                
            return self._success_response("获取自定义卡池成功", pools)
        except Exception as e:
            return self._error_response(f"获取自定义卡池失败: {str(e)}")
    
    def save_custom_pool(self, pool_data):
        """保存自定义卡池"""
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.custom_pools)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
                
            # 读取现有卡池
            pools = []
            if os.path.exists(self.custom_pools):
                with open(self.custom_pools, 'r', encoding='utf-8') as f:
                    pools = json.load(f)
                
            # 生成唯一ID
            if 'id' not in pool_data or not pool_data['id']:
                pool_data['id'] = str(uuid.uuid4())
                
            # 添加/更新时间戳
            now = time.strftime("%Y-%m-%d %H:%M:%S")
            pool_data['created_at'] = pool_data.get('created_at', now)
            pool_data['updated_at'] = now
            
            # 查找是否已存在同ID的卡池
            existing_index = next((i for i, p in enumerate(pools) if p.get('id') == pool_data.get('id')), None)
            
            if existing_index is not None:
                # 更新现有卡池
                pools[existing_index] = pool_data
            else:
                # 添加新卡池
                pools.append(pool_data)
                
            # 保存到文件
            with open(self.custom_pools, 'w', encoding='utf-8') as f:
                json.dump(pools, f, ensure_ascii=False, indent=2)
            
            return self._success_response("保存自定义卡池成功", pool_data)
        except Exception as e:
            return self._error_response(f"保存自定义卡池失败: {str(e)}")
    
    def update_custom_pool(self, pool_id, pool_data):
        """更新自定义卡池"""
        try:
            if not os.path.exists(self.custom_pools):
                return self._error_response("自定义卡池文件不存在")
                
            # 读取现有卡池
            with open(self.custom_pools, 'r', encoding='utf-8') as f:
                pools = json.load(f)
                
            if not pools:
                return self._error_response("没有找到任何卡池")
                
            # 查找并更新指定的卡池
            pool_index = next((i for i, p in enumerate(pools) if p.get('id') == pool_id), -1)
            
            if pool_index == -1:
                return self._error_response(f"未找到ID为{pool_id}的卡池")
                
            # 保持原有的ID和创建时间
            pool_data['id'] = pool_id
            if 'created_at' in pools[pool_index]:
                pool_data['created_at'] = pools[pool_index]['created_at']
                
            # 更新时间戳
            pool_data['updated_at'] = time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 更新数据
            pools[pool_index] = pool_data
            
            # 保存更新后的数据
            with open(self.custom_pools, 'w', encoding='utf-8') as f:
                json.dump(pools, f, ensure_ascii=False, indent=2)
            
            return self._success_response("更新自定义卡池成功", pool_data)
        except Exception as e:
            return self._error_response(f"更新自定义卡池失败: {str(e)}")
    
    def delete_custom_pool(self, pool_id):
        """删除自定义卡池"""
        try:
            if not os.path.exists(self.custom_pools):
                return self._error_response("自定义卡池文件不存在")
                
            # 读取现有卡池
            with open(self.custom_pools, 'r', encoding='utf-8') as f:
                pools = json.load(f)
                
            if not pools:
                return self._error_response("没有找到任何卡池")
                
            # 过滤掉要删除的卡池
            original_length = len(pools)
            new_pools = [p for p in pools if p.get('id') != pool_id]
            
            if len(new_pools) == original_length:
                return self._error_response(f"未找到ID为{pool_id}的卡池")
                
            # 保存更新后的数据
            with open(self.custom_pools, 'w', encoding='utf-8') as f:
                json.dump(new_pools, f, ensure_ascii=False, indent=2)
            
            return self._success_response("删除自定义卡池成功")
        except Exception as e:
            return self._error_response(f"删除自定义卡池失败: {str(e)}")

    def get_timeline(self, book_id):
        """获取时间线数据"""
        try:
            book_path = self.book_manager.get_book_path(book_id)
            if not book_path:
                return json.dumps({"status": "error", "message": "书籍不存在"})
                
            timeline_path = os.path.join(book_path, 'timeLine')
            if not os.path.exists(timeline_path):
                os.makedirs(timeline_path, exist_ok=True)

            timeline_file = os.path.join(timeline_path, 'timeline_flow.json')
            
            # 尝试加载新版时间线数据
            data = self._load_json(timeline_file)
            
            # 如果文件不存在或数据为空，返回默认结构
            if not data:
                # 默认空时间线数据结构
                data = {
                    "nodes": [],
                    "edges": [],
                    "version": "2.0"
                }
                
            return json.dumps({"status": "success", "message": "获取时间线成功", "data": data})
        except Exception as e:
            return json.dumps({"status": "error", "message": f"获取时间线失败: {str(e)}"})
            
    def save_timeline(self, book_id, timeline_data_str):
        """保存时间线数据"""
        try:
            # 解析前端传来的JSON字符串
            timeline_data = json.loads(timeline_data_str)
            
            book_path = self.book_manager.get_book_path(book_id)
            if not book_path:
                return json.dumps({"status": "error", "message": "书籍不存在"})
                
            timeline_path = os.path.join(book_path, 'timeLine')
            if not os.path.exists(timeline_path):
                os.makedirs(timeline_path, exist_ok=True)

            # 使用新的文件名保存Vue Flow时间线数据
            timeline_file = os.path.join(timeline_path, 'timeline_flow.json')
            
            # 保存JSON数据
            with open(timeline_file, 'w', encoding='utf-8') as f:
                json.dump(timeline_data, f, ensure_ascii=False, indent=2)
                
            return json.dumps({"status": "success", "message": "保存时间线成功"})
        except json.JSONDecodeError as e:
            return json.dumps({"status": "error", "message": f"时间线数据格式错误: {str(e)}"})
        except Exception as e:
            return json.dumps({"status": "error", "message": f"保存时间线失败: {str(e)}"})

    def create_book(self, book_data):
        return self.books.create_book(book_data)

    def get_book(self, book_id):
        return self.books.get_book(book_id)

    def update_book(self, book_id, book_data):
        return self.books.update_book(book_id, book_data)

    def delete_book(self, book_id):
        return self.books.delete_book(book_id)

    def list_books(self):
        return self.books.list_books()

    def get_volumes(self, book_id, page=1, page_size=50, include_chapters=True, volume_ids=None):
        """获取书籍的卷列表（支持分页和选择性加载）"""
        return self.books.get_volumes(book_id, page, page_size, include_chapters, volume_ids)

    def search_chapters(self, book_id, query, page=1, page_size=20):
        """搜索章节（支持分页）"""
        return self.books.search_chapters(book_id, query, page, page_size)

    def get_volume_chapters(self, book_id, volume_id, page=1, page_size=50):
        """获取指定卷的章节列表（支持分页）"""
        return self.books.get_volume_chapters(book_id, volume_id, page, page_size)

    def get_chapters_by_ids(self, book_id, chapter_ids):
        """根据章节ID列表获取章节信息（用于增量更新）"""
        return self.books.get_chapters_by_ids(book_id, chapter_ids)

    def create_volume(self, book_id, volume_data):
        """创建新卷"""
        return self.books.create_volume(book_id, volume_data)

    def update_volume(self, book_id, volume_id, volume_data):
        """更新卷信息"""
        return self.books.update_volume(book_id, volume_id, volume_data)

    def delete_volume(self, book_id, volume_id):
        """删除卷"""
        return self.books.delete_volume(book_id, volume_id)

    def create_chapter(self, book_id, volume_id, chapter_data):
        """创建新章节"""
        return self.books.create_chapter(book_id, volume_id, chapter_data)

    def update_chapter(self, book_id, volume_id, chapter_id, chapter_data):
        """更新章节内容"""
        return self.books.update_chapter(book_id, volume_id, chapter_id, chapter_data)

    def delete_chapter(self, book_id, volume_id, chapter_id):
        """删除章节"""
        return self.books.delete_chapter(book_id, volume_id, chapter_id)

    def get_chapter(self, book_id, volume_id, chapter_id):
        """获取单个章节内容"""
        return self.books.get_chapter(book_id, volume_id, chapter_id)

    def get_templates(self, book_id):
        """获取书籍的所有模板"""
        return self.books.get_templates(book_id)

    def save_template(self, template_data):
        """保存模板"""
        return self.books.save_template(template_data)

    def delete_template(self, template_id, book_id):
        """删除设定模板"""
        return self.books.delete_template(template_id, book_id)

    def update_template_order(self, order_data):
        """更新模板排序"""
        return self.books.update_template_order(order_data)

    def get_entities(self, book_id):
        """获取书籍的所有实体"""
        return self.books.get_entities(book_id)

    def save_entity(self, entity_data):
        """保存设定实体"""
        return self.books.save_entity(entity_data)

    def delete_entity(self, entity_id, book_id, template_id):
        """删除设定实体"""
        return self.books.delete_entity(entity_id, book_id, template_id)

    def save_entity_export(self, data):
        """保存实体导出"""
        return self.books.save_entity_export(data)

    def get_timeline(self, book_id):
        """获取时间线数据"""
        return self.books.get_timeline(book_id)

    def save_timeline(self, book_id, timeline_data):
        """保存时间线数据"""
        return self.books.save_timeline(book_id, timeline_data)

    def save_timeline_event_type(self, book_id, event_types):
        """保存时间线事件类型配置"""
        return self.books.save_timeline_event_type(book_id, event_types)

    def get_scene_events(self,book_id):
        """获取场景事件"""
        return self.books.get_book_scene(book_id)

    def save_scene_events(self,book_id,scene_data):
        """保存场景事件"""
        return self.books.save_book_scene(book_id,scene_data)

    def get_timeline_event_type(self, book_id):
        """获取时间线事件类型配置"""
        return self.books.get_timeline_event_type(book_id)

    def export_timeline_events(self, book_id, export_type='names'):
        """导出时间线事件"""
        return self.books.export_timeline_events(book_id, export_type)

    def export_scenes(self,data):
        """导出场景数据"""
        print(data)
        return self.books.export_scenes(data)

    def import_scenes(self, scenes_data):
        """导入场景数据
        Args:
            scenes_data: 场景数据列表
        """
        return self.books.import_scenes()

    def save_plot(self, book_id, plot_data):
        """保存剧情组合"""
        return self.books.save_plot(book_id, plot_data)

    def get_plots(self, book_id):
        """获取书籍的所有剧情组合"""
        return self.books.get_plots(book_id)

    def delete_plot(self, book_id, plot_id):
        """删除剧情组合"""
        return self.books.delete_plot(book_id, plot_id)

    def update_plot(self, book_id, plot_id, plot_data):
        """更新剧情组合"""
        return self.books.update_plot(book_id, plot_id, plot_data)

    def add_prompt(self, book_id, prompt_data):
        """添加或更新提示词规则"""
        return self.books.save_prompt_rule(book_id, prompt_data)
        
    def get_prompts(self, book_id):
        """获取书籍的所有提示词规则"""
        return self.books.get_prompt_rules(book_id)
        
    def delete_prompt(self, book_id, prompt_id):
        """删除提示词规则"""
        return self.books.delete_prompt_rule(book_id, prompt_id)

    def get_book_relations(self, book_id):
        """获取书籍中人物关系数据"""
        return self.books.get_book_relations(book_id)

    def save_relation(self, book_id, relations_data):
        """保存书籍的实体关系数据"""
        return self.books.save_relation(book_id, relations_data)

    def delete_relation(self, book_id, relation_data_or_id):
        """删除一条关系"""
        return self.books.delete_relation(book_id, relation_data_or_id)

    def get_story_inspiration(self):
        """获取故事灵感卡池数据"""
        return self.books.get_story_inspiration()

    def save_story_inspiration(self, inspiration_data):
        """保存故事灵感卡池数据"""
        return self.books.save_story_inspiration(inspiration_data)

    def save_inspiration_category(self, category_key, elements):
        """保存特定类别的灵感元素"""
        return self.books.save_inspiration_category(category_key, elements)

    def get_character_inspiration(self):
        """获取人设灵感数据"""
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.character_inspiration)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 检查文件是否存在，不存在则创建
            if not os.path.exists(self.character_inspiration):
                with open(self.character_inspiration, 'w', encoding='utf-8') as f:
                    json.dump({}, f)
                return self._success_response("获取人设灵感数据成功", {})
                
            # 读取人设灵感数据
            with open(self.character_inspiration, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            return self._success_response("获取人设灵感数据成功", data)
        except Exception as e:
            return self._error_response(f"获取人设灵感数据失败: {str(e)}")

    def save_character_inspiration(self, inspiration_data):
        """保存人设灵感数据"""
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.character_inspiration)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 保存数据
            with open(self.character_inspiration, 'w', encoding='utf-8') as f:
                json.dump(inspiration_data, f, ensure_ascii=False, indent=2)
                
            return self._success_response("保存人设灵感数据成功", inspiration_data)
        except Exception as e:
            return self._error_response(f"保存人设灵感数据失败: {str(e)}")

    def export_book(self, export_data):
        """导出书籍选中的章节内容到单个文件
        Args:
            export_data: 包含以下字段的字典:
                book_id: 书籍ID
                format: 导出格式，如'txt'
                volumes: 要导出的卷和章节列表，格式为:
                    [
                        {
                            'id': '卷ID',
                            'title': '卷标题',
                            'chapters': [
                                {
                                    'id': '章节ID',
                                    'title': '章节标题'
                                }
                            ]
                        }
                    ]
        Returns:
            导出结果
        """
        try:
            # 解析参数
            if isinstance(export_data, str):
                try:
                    export_data = json.loads(export_data)
                except json.JSONDecodeError:
                    return self._error_response('导出数据格式无效')
            
            book_id = export_data.get('book_id')
            export_format = export_data.get('format', 'txt')
            selected_volumes = export_data.get('volumes', [])
            
            if not book_id:
                return self._error_response('缺少书籍ID')
            
            if not selected_volumes:
                return self._error_response('没有选择要导出的章节')
            
            # 获取书籍信息
            book_response = self.get_book(book_id)
            if isinstance(book_response, str):
                book_response = json.loads(book_response)
                
            if book_response.get('status') != 'success':
                return self._error_response('获取书籍信息失败')
                
            book_info = book_response.get('data', {})
            book_title = book_info.get('title', '未命名书籍')
            
            # 准备导出内容
            content = f"《{book_title}》\n\n"
            content += f"简介：{book_info.get('description', '')}\n\n"
            
            # 工具函数：清理HTML标签
            def clean_html(html_content):
                if not html_content:
                    return ''
                
                # 使用正则表达式移除HTML标签
                import re
                # 先替换一些常见的HTML实体
                html_content = html_content.replace('&nbsp;', ' ')
                html_content = html_content.replace('&lt;', '<')
                html_content = html_content.replace('&gt;', '>')
                html_content = html_content.replace('&amp;', '&')
                html_content = html_content.replace('&quot;', '"')
                
                # 处理段落和换行 - 在移除标签前先替换段落和换行标签为特殊标记
                html_content = re.sub(r'<p[^>]*>', '', html_content)  # 移除开始段落标签
                html_content = re.sub(r'</p>', '\n\n', html_content)  # 将段落结束转换为两个换行
                html_content = re.sub(r'<br[^>]*>|<br/>|<br>', '\n', html_content)  # 将br标签转换为换行
                html_content = re.sub(r'<div[^>]*>', '', html_content)  # 移除div开始标签
                html_content = re.sub(r'</div>', '\n', html_content)  # 将div结束标签转换为换行
                
                # 移除所有剩余的HTML标签，保留标签内的文本内容
                clean_text = re.sub(r'<[^>]+>', '', html_content)
                
                # 处理多个连续换行和空格
                clean_text = re.sub(r'\n{3,}', '\n\n', clean_text)  # 将3个以上连续换行替换为2个
                clean_text = re.sub(r'[ \t]+', ' ', clean_text)  # 合并多个空格
                
                return clean_text.strip()
            
            # 遍历选中的卷和章节
            for volume_data in selected_volumes:
                volume_id = volume_data.get('id')
                volume_title = volume_data.get('title', '未命名卷')
                selected_chapters = volume_data.get('chapters', [])
                
                if not selected_chapters:
                    continue  # 跳过没有选中章节的卷
                
                # 添加卷标题
                content += f"\n\n{volume_title}\n{'=' * len(volume_title)}\n\n"
                
                # 处理所选章节
                for chapter_data in selected_chapters:
                    chapter_id = chapter_data.get('id')
                    chapter_title = chapter_data.get('title', '未命名章节')
                    
                    # 获取章节内容
                    chapter_response = self.get_chapter(book_id, volume_id, chapter_id)
                    if isinstance(chapter_response, str):
                        try:
                            chapter_response = json.loads(chapter_response)
                        except:
                            print(f"章节内容解析失败: {chapter_response}")
                            continue
                        
                    if chapter_response.get('status') == 'success':
                        chapter_data = chapter_response.get('data', {})
                        chapter_content = chapter_data.get('content', '')
                        
                        # 清理HTML标签，获取纯文本内容
                        clean_content = clean_html(chapter_content)
                        
                        # 格式化章节内容（可能需要根据导出格式调整）
                        if export_format == 'txt':
                            # 添加章节到导出内容
                            content += f"\n\n{chapter_title}\n{'-' * len(chapter_title)}\n\n"
                            content += f"{clean_content}\n"
            
            # 打开文件保存对话框
            import webview
            current_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 根据导出格式设置文件扩展名
            extension = export_format.lower()
            default_name = f"{book_title}_{current_time}.{extension}"
            
            if export_format == 'txt':
                file_types = ('Text files (*.txt)',)
            else:
                # 如果添加其他格式支持，可以在此处扩展
                file_types = ('Text files (*.txt)',)
            
            file_path = webview.windows[0].create_file_dialog(
                webview.SAVE_DIALOG,
                directory='',
                save_filename=default_name,
                file_types=file_types
            )

            if not file_path:
                return self._error_response('未选择保存位置')

            file_path = file_path[0] if isinstance(file_path, list) else file_path

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return self._success_response('书籍导出成功')
        except Exception as e:
            print(f"导出书籍失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return self._error_response(f'导出书籍失败: {str(e)}')


    def export_timeline_to_txt(self, book_id, timeline_data):
        """
        将时间线导出为文本文件
        :param book_id: 书籍ID
        :param timeline_data: 时间线数据
        :return: 导出结果
        """
        try:
            import json
            import os
            import webview
            from datetime import datetime
            
            # 解析时间线数据
            if isinstance(timeline_data, str):
                timeline_data = json.loads(timeline_data)
                
            # 获取书籍信息并处理可能的字符串返回值
            book = self.get_book(book_id)
            if not book:
                return self._error_response("书籍不存在")
                
            # 处理book可能是字符串的情况
            book_title = "未命名书籍"
            try:
                if isinstance(book, dict):
                    book_title = book.get("title", "未命名书籍")
                elif isinstance(book, str):
                    # 尝试解析JSON字符串
                    try:
                        book_data = json.loads(book)
                        if isinstance(book_data, dict) and "data" in book_data:
                            book_title = book_data.get("data", {}).get("title", "未命名书籍")
                        elif isinstance(book_data, dict):
                            book_title = book_data.get("title", "未命名书籍")
                    except:
                        # 如果不是JSON，直接使用字符串作为书名
                        if book.strip():
                            book_title = book
                else:
                    # 其他情况使用默认书名
                    book_title = "未命名书籍"
            except Exception as e:
                print(f"提取书名失败: {str(e)}")
                book_title = "未命名书籍"
            
            # 获取书名作为默认文件名
            default_filename = f"{book_title}_时间线_{datetime.now().strftime('%Y%m%d%H%M%S')}.txt"
            
            # 打开文件选择对话框
            save_path = webview.windows[0].create_file_dialog(
                webview.SAVE_DIALOG,
                directory='~',
                save_filename=default_filename,
                file_types=('文本文件 (*.txt)',)
            )
            
            # 如果用户取消了选择，返回
            if not save_path:
                return self._error_response("导出已取消")
                
            save_path = save_path[0] if isinstance(save_path, list) else save_path
            
            # 确保文件扩展名为.txt
            if not save_path.endswith('.txt'):
                save_path += '.txt'

            # 从时间线数据中提取节点和边
            nodes = timeline_data.get('nodes', [])
            edges = timeline_data.get('edges', [])
            
            # 过滤出主线节点和分支节点
            main_nodes = [node for node in nodes if node.get('nodeType') == 'main' or node.get('data', {}).get('nodeType') == 'main']
            branch_nodes = [node for node in nodes if node.get('nodeType') == 'branch' or node.get('data', {}).get('nodeType') == 'branch']
            
            # 按Y坐标排序主线节点（时间先后顺序）
            main_nodes.sort(key=lambda n: n.get('y') if 'y' in n else n.get('position', {}).get('y', 0))
            
            # 创建节点ID到节点的映射，便于快速查找
            node_map = {node.get('id'): node for node in nodes}
            
            # 创建节点连接关系的映射（通过边连接）
            connections = {}
            for edge in edges:
                source = edge.get('source')
                target = edge.get('target')
                
                if source not in connections:
                    connections[source] = []
                connections[source].append(target)
                
                if target not in connections:
                    connections[target] = []
                connections[target].append(source)
            
            # 创建主线节点到分支节点的映射（通过parentId属性）
            parent_child_map = {}
            for branch in branch_nodes:
                branch_data = branch.get('data', {})
                parent_id = branch_data.get('parentId')
                
                if parent_id:
                    if parent_id not in parent_child_map:
                        parent_child_map[parent_id] = []
                    parent_child_map[parent_id].append(branch)
            
            # 准备文本内容
            content = []
            content.append(f"《{book_title}》时间线")
            content.append("=" * 40)
            content.append("")
            
            # 添加主线事件
            content.append("【主线事件】")
            content.append("")
            
            for i, node in enumerate(main_nodes):
                # 获取节点数据
                node_data = node.get('data', {})
                label = node_data.get('label', node.get('label', '未命名事件'))
                
                # 获取时间
                if node_data.get('useCustomTime') or node_data.get('customTime'):
                    time_str = node_data.get('customTime', '')
                else:
                    year = node_data.get('year', node.get('year', ''))
                    month = node_data.get('month', node.get('month', ''))
                    day = node_data.get('day', node.get('day', ''))
                    time_str = f"{year}年{month}月{day}日" if year else ""
                
                # 获取内容
                content_text = node_data.get('content', node.get('content', ''))
                
                # 添加到文本
                content.append(f"{i+1}. {label}")
                content.append(f"   时间: {time_str}")
                if content_text:
                    content.append(f"   内容: {content_text}")
                content.append("")
                
                # 查找与此主线节点关联的所有分支节点
                related_branches = []
                node_id = node.get('id')
                
                # 方法1：通过边连接查找相关支线
                if node_id in connections:
                    for connected_id in connections[node_id]:
                        connected_node = node_map.get(connected_id)
                        if connected_node:
                            # 检查是否是分支节点
                            node_type = connected_node.get('nodeType', connected_node.get('data', {}).get('nodeType', ''))
                            if node_type == 'branch':
                                if connected_node not in related_branches:  # 避免重复添加
                                    related_branches.append(connected_node)
                
                # 方法2：通过parentId属性查找相关支线
                if node_id in parent_child_map:
                    for branch_node in parent_child_map[node_id]:
                        if branch_node not in related_branches:  # 避免重复添加
                            related_branches.append(branch_node)
                
                # 如果有关联的分支，添加到文本
                if related_branches:
                    content.append(f"   【相关支线】")
                    for j, branch in enumerate(related_branches):
                        branch_data = branch.get('data', {})
                        branch_label = branch_data.get('label', branch.get('label', '未命名支线'))
                        
                        # 获取支线时间
                        if branch_data.get('useCustomTime') or branch_data.get('customTime'):
                            branch_time = branch_data.get('customTime', '')
                        else:
                            branch_year = branch_data.get('year', branch.get('year', ''))
                            branch_month = branch_data.get('month', branch.get('month', ''))
                            branch_day = branch_data.get('day', branch.get('day', ''))
                            branch_time = f"{branch_year}年{branch_month}月{branch_day}日" if branch_year else ""
                        
                        # 获取支线内容
                        branch_content = branch_data.get('content', branch.get('content', ''))
                        
                        content.append(f"      {j+1}) {branch_label}")
                        if branch_time:
                            content.append(f"         时间: {branch_time}")
                        if branch_content:
                            content.append(f"         内容: {branch_content}")
                        content.append("")
                    
                    content.append("")
            
            # 将内容写入文件
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            return self._success_response(f"时间线已成功导出到: {save_path}")
            
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return self._error_response(f"导出时间线失败: {str(e)}")

    # 剧情桥段设计相关方法
    def get_bridge_configs(self, book_id=None):
        """
        获取剧情桥段配置，不再绑定到特定书籍
        :param book_id: 书籍ID (可选，为了保持API兼容)
        :return: 剧情桥段配置列表
        """
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.bridge_configs)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 检查配置文件是否存在
            if not os.path.exists(self.bridge_configs):
                # 如果文件不存在，创建空配置
                with open(self.bridge_configs, "w", encoding="utf-8") as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                return self._success_response("获取剧情桥段配置成功", [])
            
            # 读取配置文件
            try:
                with open(self.bridge_configs, "r", encoding="utf-8") as f:
                    configs = json.load(f)
                    
                    # 确保configs是列表
                    if not isinstance(configs, list):
                        configs = []
                    
                    # 确保数据结构兼容性
                    for config in configs:
                        # 为前端提供bridgeA/bridgeB
                        if "sceneA" in config and "bridgeA" not in config:
                            config["bridgeA"] = config["sceneA"]
                        
                        if "sceneB" in config and "bridgeB" not in config:
                            config["bridgeB"] = config["sceneB"]
                    
                    return self._success_response("获取剧情桥段配置成功", configs)
            except Exception as e:
                print(f"读取桥段配置文件失败: {str(e)}")
                # 文件损坏，创建新的空配置
                with open(self.bridge_configs, "w", encoding="utf-8") as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                return self._success_response("获取剧情桥段配置成功", [])
            
        except Exception as e:
            error_msg = f"获取剧情桥段配置失败: {str(e)}"
            print(error_msg)
            return self._error_response(error_msg)

    def save_bridge_config(self, config_id=None, config_data=None):
        """
        保存剧情桥段配置，不再绑定到特定书籍
        :param config_id: 配置ID，如果提供则更新现有配置
        :param config_data: 配置数据
        :return: 保存结果
        """
        try:
            if not config_data:
                return self._error_response("配置数据不能为空")
            
            # 确保目录存在
            config_dir = os.path.dirname(self.bridge_configs)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 配置ID处理
            if config_id is None:
                # 如果未提供配置ID但数据中有ID，则使用数据中的ID
                config_id = config_data.get("id")
            
            # 如果仍然没有ID，则生成新ID
            if not config_id:
                config_id = f"scene_{int(time.time() * 1000)}"
                config_data["id"] = config_id
            else:
                # 确保数据中的ID与参数一致
                config_data["id"] = config_id
            
            # 添加或更新时间戳
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 读取现有配置列表
            configs = []
            if os.path.exists(self.bridge_configs):
                try:
                    with open(self.bridge_configs, "r", encoding="utf-8") as f:
                        configs = json.load(f)
                        if not isinstance(configs, list):
                            configs = []
                except Exception as e:
                    print(f"读取桥段配置文件失败: {str(e)}")
                    configs = []
            
            # 查找是否已存在同ID的配置
            existing_index = next((i for i, c in enumerate(configs) if c.get("id") == config_id), -1)
            
            if existing_index >= 0:
                # 更新现有配置
                if "created_at" in configs[existing_index]:
                    config_data["created_at"] = configs[existing_index]["created_at"]
                else:
                    config_data["created_at"] = current_time
                
                config_data["updated_at"] = current_time
                configs[existing_index] = config_data
            else:
                # 添加新配置
                config_data["created_at"] = current_time
                config_data["updated_at"] = current_time
                configs.append(config_data)
            
            # 确保数据格式一致性
            # 处理bridgeA/bridgeB和sceneA/sceneB的映射
            if "bridgeA" in config_data and "sceneA" not in config_data:
                config_data["sceneA"] = config_data["bridgeA"]
            
            if "bridgeB" in config_data and "sceneB" not in config_data:
                config_data["sceneB"] = config_data["bridgeB"]
            
            # 保存配置文件
            with open(self.bridge_configs, "w", encoding="utf-8") as f:
                json.dump(configs, f, ensure_ascii=False, indent=2)
            
            # print(f"桥段配置已保存: {self.bridge_configs}")
            
            return self._success_response("保存剧情桥段配置成功", config_data)
        except Exception as e:
            error_msg = f"保存剧情桥段配置失败: {str(e)}"
            print(error_msg)
            return self._error_response(error_msg)

    def delete_bridge_config(self, book_id=None, config_id=None):
        """
        删除剧情桥段配置，不再绑定到特定书籍
        :param book_id: 书籍ID (可选，为了保持API兼容)
        :param config_id: 配置ID
        :return: 删除结果
        """
        try:
            if not config_id:
                return self._error_response("配置ID不能为空")
            
            # 检查配置文件是否存在
            if not os.path.exists(self.bridge_configs):
                return self._error_response(f"配置文件不存在")
            
            # 读取现有配置列表
            try:
                with open(self.bridge_configs, "r", encoding="utf-8") as f:
                    configs = json.load(f)
                    if not isinstance(configs, list):
                        return self._error_response("配置文件格式错误")
            except Exception as e:
                return self._error_response(f"读取配置文件失败: {str(e)}")
            
            # 找到并删除指定的配置
            original_count = len(configs)
            configs = [c for c in configs if c.get("id") != config_id]
            
            if len(configs) == original_count:
                return self._error_response(f"未找到ID为{config_id}的配置")
            
            # 保存更新后的配置列表
            with open(self.bridge_configs, "w", encoding="utf-8") as f:
                json.dump(configs, f, ensure_ascii=False, indent=2)
            
            return self._success_response("删除剧情桥段配置成功")
        except Exception as e:
            return self._error_response(f"删除剧情桥段配置失败: {str(e)}")

    # 添加保存和加载场景设计的API方法
    def save_scene_design(self, design_data):
        """
        保存场景设计数据
        :param design_data: 包含id、name、bridgeA、bridgeB和guidelines的字典
        :return: 保存结果
        """
        try:
            if not design_data or 'id' not in design_data:
                return {'success': False, 'message': '设计数据不完整'}
            
            # 确保配置目录存在
            config_dir = os.path.dirname(self.bridge_design)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            # 读取现有设计数据
            designs = []
            if os.path.exists(self.bridge_design):
                try:
                    with open(self.bridge_design, 'r', encoding='utf-8') as f:
                        designs = json.load(f)
                except:
                    designs = []
            
            # 查找是否已存在同ID的设计
            design_index = next((i for i, d in enumerate(designs) if d.get('id') == design_data.get('id')), -1)
            
            if design_index >= 0:
                # 更新现有设计
                designs[design_index] = design_data
            else:
                # 添加新设计
                designs.append(design_data)
            
            # 保存到文件
            with open(self.bridge_design, 'w', encoding='utf-8') as f:
                json.dump(designs, f, ensure_ascii=False, indent=2)
            
            return {'success': True, 'data': design_data}
        except Exception as e:
            print(f"保存场景设计失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    def get_scene_designs(self):
        """
        获取所有场景设计数据
        :return: 所有设计数据的列表
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(self.bridge_design):
                return {'success': True, 'data': []}
            
            # 从文件中读取所有设计
            with open(self.bridge_design, 'r', encoding='utf-8') as f:
                designs = json.load(f)
            
            return {'success': True, 'data': designs}
        except Exception as e:
            print(f"获取场景设计列表失败: {str(e)}")
            return {'success': False, 'message': str(e)}
    
    def load_scene_design(self, design_id):
        """
        加载场景设计数据
        :param design_id: 设计ID
        :return: 设计数据
        """
        try:
            if not design_id:
                return {'success': False, 'message': '无效的设计ID'}
            
            # 检查文件是否存在
            if not os.path.exists(self.bridge_design):
                return {'success': False, 'message': '设计文件不存在'}
            
            # 从文件中读取所有设计
            with open(self.bridge_design, 'r', encoding='utf-8') as f:
                designs = json.load(f)
            
            # 查找指定ID的设计
            design = next((d for d in designs if d.get('id') == design_id), None)
            
            if design is None:
                return {'success': False, 'message': f'未找到ID为{design_id}的设计'}
            
            return {'success': True, 'data': design}
        except Exception as e:
            print(f"加载场景设计失败: {str(e)}")
            return {'success': False, 'message': str(e)}
    
    def delete_scene_design(self, design_id):
        """
        删除场景设计数据
        :param design_id: 设计ID
        :return: 删除结果
        """
        try:
            if not design_id:
                return {'success': False, 'message': '无效的设计ID'}
            
            # 检查文件是否存在
            if not os.path.exists(self.bridge_design):
                return {'success': False, 'message': '设计文件不存在'}
            
            # 从文件中读取所有设计
            with open(self.bridge_design, 'r', encoding='utf-8') as f:
                designs = json.load(f)
            
            # 记录原始设计数量
            original_count = len(designs)
            
            # 移除匹配ID的设计
            designs = [d for d in designs if d.get('id') != design_id]
            
            # 检查是否找到并删除了设计
            if len(designs) == original_count:
                return {'success': False, 'message': f'未找到ID为{design_id}的设计'}
            
            # 保存更新后的设计列表
            with open(self.bridge_design, 'w', encoding='utf-8') as f:
                json.dump(designs, f, ensure_ascii=False, indent=2)
            
            return {'success': True, 'message': '场景设计已成功删除'}
        except Exception as e:
            print(f"删除场景设计失败: {str(e)}")
            return {'success': False, 'message': str(e)}
    
    # 大纲相关方法
    def save_outline(self, outline_data):
        """保存大纲数据
        
        Args:
            outline_data: 大纲数据对象
            
        Returns:
            响应对象，包含保存结果
        """
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.outline_configs)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # 添加时间戳
            if not outline_data.get('timestamp'):
                outline_data['timestamp'] = int(time.time() * 1000)
                
            # 添加ID标识
            if not outline_data.get('id'):
                outline_data['id'] = str(uuid.uuid4())
            
            # 检查是否存在现有大纲列表
            outlines = []
            if os.path.exists(self.outline_configs):
                try:
                    with open(self.outline_configs, 'r', encoding='utf-8') as f:
                        outlines = json.load(f)
                        if not isinstance(outlines, list):
                            outlines = []
                except:
                    outlines = []
            
            # 查找是否已经存在同ID的大纲
            found = False
            for i, item in enumerate(outlines):
                if item.get('id') == outline_data.get('id'):
                    outlines[i] = outline_data
                    found = True
                    break
                    
            # 如果没有找到，则添加新大纲
            if not found:
                outlines.append(outline_data)
            
            # 写入文件
            with open(self.outline_configs, 'w', encoding='utf-8') as f:
                json.dump(outlines, f, ensure_ascii=False, indent=2)
            
            return self._success_response("大纲保存成功", outline_data)
        except Exception as e:
            return self._error_response(f"保存大纲失败: {str(e)}")
    
    def get_outlines(self):
        """获取所有大纲数据
        
        Returns:
            响应对象，包含所有大纲数据列表
        """
        try:
            # 确保目录和文件存在
            config_dir = os.path.dirname(self.outline_configs)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            if not os.path.exists(self.outline_configs):
                with open(self.outline_configs, 'w', encoding='utf-8') as f:
                    json.dump([], f)
                return self._success_response("获取大纲成功", [])
            
            # 读取大纲数据
            with open(self.outline_configs, 'r', encoding='utf-8') as f:
                outlines = json.load(f)
            
            # 确保返回的是列表
            if not isinstance(outlines, list):
                outlines = []
            
            # 按时间戳排序，最新的在前面
            outlines.sort(key=lambda x: x.get('timestamp', 0), reverse=True)
            
            return self._success_response("获取大纲成功", outlines)
        except Exception as e:
            return self._error_response(f"获取大纲失败: {str(e)}")
    
    def get_outline(self, outline_id):
        """获取指定ID的大纲数据
        
        Args:
            outline_id: 大纲ID
            
        Returns:
            响应对象，包含大纲数据
        """
        try:
            # 先获取所有大纲
            response = self.get_outlines()
            if not response.get('success'):
                return response
            
            outlines = response.get('data', [])
            
            # 查找指定ID的大纲
            for outline in outlines:
                if outline.get('id') == outline_id:
                    return self._success_response("获取大纲成功", outline)
            
            # 未找到指定ID的大纲
            return self._success_response("未找到指定大纲", None)
        except Exception as e:
            return self._error_response(f"获取大纲失败: {str(e)}")
    
    def delete_outline(self, outline_id):
        """删除指定ID的大纲数据
        
        Args:
            outline_id: 大纲ID
            
        Returns:
            响应对象，包含删除结果
        """
        try:
            # 确保文件存在
            if not os.path.exists(self.outline_configs):
                return self._success_response("大纲已删除", None)
            
            # 读取大纲数据
            with open(self.outline_configs, 'r', encoding='utf-8') as f:
                outlines = json.load(f)
            
            if not isinstance(outlines, list):
                return self._success_response("大纲已删除", None)
            
            # 过滤掉要删除的大纲
            new_outlines = [o for o in outlines if o.get('id') != outline_id]
            
            # 如果有变化，则写回文件
            if len(new_outlines) != len(outlines):
                with open(self.outline_configs, 'w', encoding='utf-8') as f:
                    json.dump(new_outlines, f, ensure_ascii=False, indent=2)
            
            return self._success_response("大纲已删除", None)
        except Exception as e:
            return self._error_response(f"删除大纲失败: {str(e)}")


if __name__ == '__main__':
    # 使用绝对路径进行测试
    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # controller = BookController(current_dir)
    # print(controller.books.book_test())
    # print(controller.timeline.timeline_test())
    pass
