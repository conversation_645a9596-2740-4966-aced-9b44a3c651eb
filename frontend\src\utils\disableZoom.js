// disableZoom.js
export function disableZoom() {
    // 禁用 ctrl + 滚轮缩放
    document.addEventListener('wheel', function(e) {
        if(e.ctrlKey) {
            e.preventDefault();
        }
    }, {passive: false});

    // 禁用 ctrl + +/- 键缩放
    document.addEventListener('keydown', function(e) {
        if(e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '=')) {
            e.preventDefault();
        }
    });
}