import threading
import os
import json
import copy
import time






class ConfigManager:
    def __init__(self, config_file):
        self.config_file = config_file
        self._config_cache = None
        self._lock = threading.Lock()
        self._last_read_time = 0
        self._cache_ttl = 5  # 缓存有效期（秒）
        self.default_config = {
            "theme": "dark",
            "editor": {
                "fontFamily": "汉仪旗黑",
                "fontSize": 16,
                "lineHeight": 1.6,
                "contentWidth": 60
            },
            "chrome": {
                "default_path": "",
                "downloadDir": "",
                "userDataDirs": [

                ]
            },
            "openai": {
                "api_key": "",
                "base_url": ""
            },
            "oneapi": {
                "exe": "",
                "port": ""
            },
            "ai": {

            },
            "backgroundImage": "",
            "loaded": False
        }
        self._ensure_config_file()

    def _ensure_config_file(self):
        """确保配置文件存在，如果不存在则创建默认配置"""
        if not os.path.exists(os.path.dirname(self.config_file)):
            os.makedirs(os.path.dirname(self.config_file))
        if not os.path.exists(self.config_file):
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.default_config, f, ensure_ascii=False, indent=2)

    def _is_cache_valid(self):
        """检查缓存是否有效"""
        return (
                self._config_cache is not None
                and time.time() - self._last_read_time < self._cache_ttl
        )

    def load_config(self):
        """加载配置，优先使用缓存"""
        with self._lock:
            if self._is_cache_valid():
                return copy.deepcopy(self._config_cache)

            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self._config_cache = config
                self._last_read_time = time.time()
                return copy.deepcopy(config)
            except (json.JSONDecodeError, FileNotFoundError):
                # 如果配置文件损坏或不存在，重置为默认配置
                self._ensure_config_file()
                return copy.deepcopy(self.default_config)

    def save_config(self, config):
        """保存配置并更新缓存"""
        with self._lock:
            try:
                # 获取当前存储的配置（如果有的话）
                current_config = None
                try:
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        current_config = json.load(f)
                except (FileNotFoundError, json.JSONDecodeError):
                    # 如果文件不存在或格式错误，使用默认配置作为基础
                    current_config = copy.deepcopy(self.default_config)
                
                # 执行深度合并，保留现有配置中未被新配置覆盖的部分
                merged_config = self._deep_merge_configs(current_config, config)
                
                # 创建临时文件
                temp_file = f"{self.config_file}.tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(merged_config, f, ensure_ascii=False, indent=2)

                # 原子性地替换原文件
                os.replace(temp_file, self.config_file)

                # 更新缓存
                self._config_cache = copy.deepcopy(merged_config)
                self._last_read_time = time.time()
                return True
            except Exception as e:
                print(f"保存配置失败: {str(e)}")
                return False
                
    def _deep_merge_configs(self, target, source):
        """深度合并两个配置字典，保留target中未被source覆盖的值"""
        if not source:
            return target
            
        merged = copy.deepcopy(target)
        
        for key, value in source.items():
            # 如果source中的值是字典，且目标中对应的key也存在且也是字典
            if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
                # 递归合并子字典
                merged[key] = self._deep_merge_configs(merged[key], value)
            # 对于列表，直接替换而不是合并
            elif isinstance(value, list) or value is not None:
                # 替换为source中的值
                merged[key] = copy.deepcopy(value)
        
        return merged

    def update_config_item(self, path, value):
        """更新单个配置项"""
        with self._lock:
            config = self.load_config()
            current = config
            parts = path.split('.')

            # 遍历路径直到倒数第二个部分
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]

            # 设置最后一个属性的值
            current[parts[-1]] = value

            return self.save_config(config)
