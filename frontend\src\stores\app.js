import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    sidebarCollapsed: false,
    device: 'desktop',
    size: 'default',
    theme: 'light'
  }),

  getters: {
    getSidebarStatus: (state) => state.sidebarCollapsed,
    getDevice: (state) => state.device
  },

  actions: {
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },
    closeSidebar() {
      this.sidebarCollapsed = true
    },
    toggleDevice(device) {
      this.device = device
    },
    setSize(size) {
      this.size = size
    }
  },

  persist: {
    enabled: true,
    strategies: [
      {
        key: 'app-settings',
        storage: localStorage,
        paths: ['sidebarCollapsed', 'size', 'theme']
      }
    ]
  }
})