<template>
  <div class="app-settings">
    <div class="settings-container"
         v-loading.fullscreen.lock="configStore.isLoading || aiProvidersStore.isLoading"
         element-loading-text="加载中..."
         element-loading-background="rgba(0, 0, 0, 0.7)">
      <el-tabs v-model="activeTab" class="settings-tabs">
        <el-tab-pane label="Chrome设置" name="chrome">
          <div class="settings-panel chrome-settings">
            <div class="section-header">
              <h2 class="section-title">Chrome 设置</h2>
            </div>

            <div class="panel-content">
              <div class="settings-card">
                <el-form label-width="120px">
                  <el-form-item label="Chrome路径">
                    <div class="path-input-group">
                      <el-input
                          v-model="configStore.chrome.default_path"
                          placeholder="请选择 Chrome 浏览器路径"
                      />
                      <el-button-group>
                        <el-button type="primary" @click="select_file_path_js('chromePath')">
                          <el-icon><Folder /></el-icon>
                          选择路径
                        </el-button>
                        <el-button type="success" @click="detectChromePath">
                          <el-icon><Search /></el-icon>
                          自动检测
                        </el-button>
                      </el-button-group>
                    </div>
                  </el-form-item>

                  <el-form-item label="下载目录">
                    <div class="path-input-group">
                      <el-input
                          v-model="configStore.chrome.downloadDir"
                          placeholder="请选择下载目录"
                      />
                      <el-button type="primary" @click="selectDirectory('downloadDir')">
                        <el-icon><Folder /></el-icon>
                        选择路径
                      </el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </div>

              <div class="section-header">
                <h2 class="section-title">用户数据目录</h2>
                <div class="header-actions">
                  <el-button type="primary" @click="addUserDataDir">
                    <el-icon><Plus /></el-icon>
                    添加目录
                  </el-button>
                </div>
              </div>

              <div class="settings-card table-container">
                <el-table :data="userDataDirs" style="width: 100%" border>
                  <el-table-column prop="name" label="名称" min-width="120" />
                  <el-table-column prop="path" label="路径" min-width="200" show-overflow-tooltip />
                  <el-table-column prop="port" label="端口" width="100" align="center" />
                  <el-table-column label="状态" width="100" align="center">
                    <template #default="{ row }">
                      <el-tag :type="runningStatus[row.id] ? 'success' : 'info'">
                        {{ runningStatus[row.id] ? '运行中' : '已停止' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="默认" width="80" align="center">
                    <template #default="{ row }">
                      <el-switch
                          v-model="row.isDefault"
                          @change="(val) => handleDefaultChange(row, val)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="280" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button-group>
                        <el-button
                            :type="runningStatus[row.id] ? 'danger' : 'success'"
                            size="small"
                            @click="toggleChrome(row)"
                        >
                          {{ runningStatus[row.id] ? '停止' : '启动' }}
                        </el-button>
                        <el-button
                            type="primary"
                            size="small"
                            @click="editUserDataDir(row)"
                        >
                          编辑
                        </el-button>
                        <el-button
                            type="danger"
                            size="small"
                            @click="deleteUserDataDir(row)"
                        >
                          删除
                        </el-button>
                      </el-button-group>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="AI角色" name="ai">
          <div class="settings-panel ai-roles-container">
            <div class="section-header">
              <h2 class="section-title">AI角色管理</h2>
              <div class="header-actions">
                <el-button-group>
                  <el-button type="primary" @click="addAIRole">
                    <el-icon><Plus /></el-icon>
                    添加角色
                  </el-button>
                  <el-button @click="refreshAIRoles" :loading="aiRolesStore.loading">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                  <el-button @click="aiRoleImportDialog.visible = true">
                    导入
                  </el-button>
                  <el-button @click="exportAIRoles">
                    导出
                  </el-button>
                </el-button-group>
              </div>
            </div>

            <div class="panel-content">
              <div class="settings-card table-container">
                <el-table :data="aiRolesStore.roles" style="width: 100%" border :key="'aiRoles-' + aiRolesStore.roles.length">
                  <el-table-column prop="name" label="名称" min-width="120" />
                  <el-table-column prop="prompt" label="提示词" min-width="200" show-overflow-tooltip />
                  <el-table-column label="启用" width="80" align="center">
                    <template #default="{ row }">
                      <el-switch
                          v-model="row.isEnabled"
                          @change="(val) => handleRoleEnableChange(row, val)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="200" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button-group>
                        <el-button
                            type="primary"
                            size="small"
                            @click="editAIRole(row)"
                        >
                          编辑
                        </el-button>
                        <el-button
                            type="danger"
                            size="small"
                            @click="deleteAIRole(row)"
                        >
                          删除
                        </el-button>
                      </el-button-group>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="OpenAI配置" name="openai-config">
          <div class="settings-section">
            <div class="section-header">
              <h2 class="section-title">AI模型服务配置</h2>
              <div class="header-actions">
                <el-button-group>
                  <el-button type="primary" @click="addProvider">
                    <el-icon><Plus /></el-icon>
                    添加服务商
                  </el-button>
                  <el-button @click="refreshProviders" :loading="aiProvidersStore.isLoading">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                </el-button-group>
              </div>
            </div>

            <div class="panel-content">
              <div v-if="aiProvidersStore.providers.length === 0" class="empty-providers">
                <el-empty description="暂无配置的服务商" />
                <el-button type="primary" @click="addProvider" class="mt-3">添加服务商</el-button>
              </div>

              <div v-else class="provider-list">
                <el-collapse v-model="aiActiveProviders">
                  <el-collapse-item 
                    v-for="(provider, index) in aiProvidersStore.providers" 
                    :key="provider.id" 
                    :name="provider.id"
                  >
                    <template #title>
                      <div class="provider-header">
                        <span class="provider-name">{{ provider.name }}</span>
                      </div>
                    </template>
                    
                    <div class="provider-content">

                      <el-form label-width="120px" size="default">
                        
                        <el-form-item label="服务商名称">
                          <el-input v-model="provider.name" placeholder="例如: OpenAI, Claude, 智谱AI等"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="API基础URL">
                          <el-input v-model="provider.baseUrl" placeholder="例如: https://api.openai.com/v1"></el-input>
                        </el-form-item>
                        
                        <el-form-item class="providers-info">

                        </el-form-item>
                        
                        <!-- 模型管理 -->
                        <div class="subsection">
                          <div class="subsection-header">
                            <h3>模型管理</h3>
                            <div>
                              <el-button 
                                type="primary" 
                                size="small" 
                                @click="addModel(provider.id)"
                              >
                                <el-icon><Plus /></el-icon>
                                添加模型
                              </el-button>
                              <el-button 
                                type="success" 
                                size="small" 
                                @click="fetchModels(provider.id)"
                              >
                                <el-icon><Refresh /></el-icon>
                                获取可用模型
                              </el-button>
                            </div>
                          </div>
                          
                          <div class="models-list">
                            <el-table :data="provider.models" style="width: 100%" border>
                              <el-table-column label="模型ID" min-width="180">
                                <template #default="{ row }">
                                  <el-input
                                    v-model="row.id"
                                    placeholder="模型ID"
                                    size="small"
                                    @blur="validateModelId(provider.id, row)"
                                  />
                                </template>
                              </el-table-column>
                              <el-table-column label="显示别名" min-width="150">
                                <template #default="{ row }">
                                  <el-input
                                    v-model="row.name"
                                    placeholder="显示别名"
                                    size="small"
                                  />
                                </template>
                              </el-table-column>
                              <el-table-column label="启用" width="80" align="center">
                                <template #default="{ row }">
                                  <el-switch
                                    v-model="row.available"
                                    @change="handleModelAvailableChange(row)"
                                  ></el-switch>
                                </template>
                              </el-table-column>
                              <el-table-column label="参数配置" min-width="120" align="center">
                                <template #default="{ row }">
                                  <el-button
                                    type="primary"
                                    size="small"
                                    @click="openModelConfigDialog(provider.id, row)"
                                  >
                                    配置参数
                                  </el-button>
                                </template>
                              </el-table-column>
                              <el-table-column label="操作" width="100" align="center">
                                <template #default="{ $index }">
                                  <el-button
                                    type="danger"
                                    size="small"
                                    @click="removeModel(provider.id, $index)"
                                  >
                                    删除
                                  </el-button>
                                </template>
                              </el-table-column>
                            </el-table>
                            
                            <div v-if="provider.models.length === 0" class="empty-models">
                              <el-empty description="暂无配置的模型" :image-size="60" />
                              <div class="empty-actions">
                                <el-button 
                                  type="primary" 
                                  size="small" 
                                  @click="addModel(provider.id)" 
                                  class="mt-2 mr-2"
                                >
                                  手动添加
                                </el-button>
                                <el-button 
                                  type="success" 
                                  size="small" 
                                  @click="fetchModels(provider.id)" 
                                  class="mt-2"
                                >
                                  获取可用模型
                                </el-button>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- API Keys管理 -->
                        <div class="subsection">
                          <div class="subsection-header">
                            <h3>API密钥管理</h3>
                            <el-button 
                              type="primary" 
                              size="small" 
                              @click="addApiKey(provider.id)"
                            >
                              <el-icon><Plus /></el-icon>
                              添加密钥
                            </el-button>
                          </div>
                          
                          <div class="keys-list">
                            <el-table :data="provider.apiKeys" style="width: 100%" border>
                              <el-table-column label="密钥" min-width="200">
                                <template #default="{ row }">
                                  <el-input 
                                    v-model="row.key" 
                                    placeholder="API密钥" 
                                    show-password
                                  ></el-input>
                                </template>
                              </el-table-column>
                              <el-table-column label="权重" width="100">
                                <template #default="{ row }">
                                  <el-input-number 
                                    v-model="row.weight" 
                                    :min="1" 
                                    :max="100" 
                                    controls-position="right"
                                    size="small"
                                  ></el-input-number>
                                </template>
                              </el-table-column>
                              <el-table-column label="状态" width="100" align="center">
                                <template #default="{ row }">
                                  <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                                    {{ row.status === 'active' ? '正常' : '异常' }}
                                  </el-tag>
                                </template>
                              </el-table-column>
                              <el-table-column label="操作" width="150" align="center">
                                <template #default="{ row, $index }">
                                  <el-button-group>
                                    <el-button 
                                      type="primary" 
                                      size="small" 
                                      @click="testApiKey(provider.id, row.id)"
                                    >
                                      测试
                                    </el-button>
                                    <el-button 
                                      type="danger" 
                                      size="small" 
                                      @click="removeApiKey(provider.id, $index)"
                                    >
                                      删除
                                    </el-button>
                                  </el-button-group>
                                </template>
                              </el-table-column>
                            </el-table>
                            
                            <div v-if="provider.apiKeys.length === 0" class="empty-keys">
                              <el-empty description="暂无配置的API密钥" :image-size="60" />
                              <el-button 
                                type="primary" 
                                size="small" 
                                @click="addApiKey(provider.id)" 
                                class="mt-2"
                              >
                                添加密钥
                              </el-button>
                            </div>
                          </div>
                        </div>
                        

                        
                        <div class="provider-actions mt-4">
                          <el-button-group>
                            <el-button 
                              type="danger" 
                              @click="removeProvider(index)"
                            >
                              删除服务商
                            </el-button>
                            <el-button 
                              type="primary" 
                              @click="saveProviderConfig"
                              :loading="aiProvidersStore.isLoading"
                            >
                              保存配置
                            </el-button>
                          </el-button-group>
                        </div>
                      </el-form>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="飞书配置" name="feishu-config">
          <div class="settings-section">
            <div class="section-header">
              <h2 class="section-title">飞书配置</h2>
            </div>
            
            <div class="panel-content">
              <div class="settings-card">
                <el-form ref="feishuFormRef" :model="feishuForm" label-width="150px">
                  <el-form-item label="App ID" prop="app_id">
                    <el-input 
                      v-model="feishuForm.app_id" 
                      placeholder="请输入飞书应用的 App ID"
                      show-password
                    />
                  </el-form-item>
                  <el-form-item label="App Secret" prop="app_secret">
                    <el-input 
                      v-model="feishuForm.app_secret" 
                      placeholder="请输入飞书应用的 App Secret"
                      show-password
                    />
                  </el-form-item>
                  <el-form-item label="Encrypt Key" prop="encrypt_key">
                    <el-input 
                      v-model="feishuForm.encrypt_key" 
                      placeholder="请输入加密密钥（可选）"
                      show-password
                    />
                  </el-form-item>
                  <el-form-item label="Verification Token" prop="verification_token">
                    <el-input 
                      v-model="feishuForm.verification_token" 
                      placeholder="请输入验证令牌（可选）"
                      show-password
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="saveFeishuConfig">保存配置</el-button>
                    <el-button @click="resetFeishuConfig">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="备份配置" name="backup-config">
          <div class="settings-section">
            <div class="section-header compact">
              <h2 class="section-title">备份设置</h2>
              <div class="header-actions">
                <el-button type="primary" @click="backupNow">
                  <el-icon><Upload /></el-icon>
                  立即备份
                </el-button>
              </div>
            </div>

            <div class="panel-content">
              <div class="backup-content">
                <div class="settings-card backup-settings">
                  <el-form :model="backupForm" label-width="100px" size="small">
                    <el-form-item label="自动备份" class="mb-2">
                      <el-switch
                        v-model="backupForm.autoBackup"
                        @change="handleAutoBackupChange"
                      />
                    </el-form-item>
                    
                    <template v-if="backupForm.autoBackup">
                      <el-form-item label="备份间隔" class="mb-2">
                        <div class="input-with-tip">
                          <el-input-number 
                            v-model="backupForm.backupInterval" 
                            :min="1" 
                            :max="1440"
                            :step="1"
                            @change="handleBackupIntervalChange"
                            controls-position="right"
                            size="small"
                          />
                          <span class="ml-2">分钟</span>
                          <el-tooltip content="设置自动备份的时间间隔（1-1440分钟）" placement="top">
                            <el-icon class="ml-1"><InfoFilled /></el-icon>
                          </el-tooltip>
                        </div>
                      </el-form-item>
                      
                      <el-form-item label="保留数量" class="mb-2">
                        <div class="input-with-tip">
                          <el-input-number
                            v-model="backupForm.keepBackups"
                            :min="1"
                            :max="100"
                            :step="1"
                            @change="handleKeepBackupsChange"
                            controls-position="right"
                            size="small"
                          />
                          <el-tooltip content="设置要保留的最新备份数量" placement="top">
                            <el-icon class="ml-1"><InfoFilled /></el-icon>
                          </el-tooltip>
                        </div>
                      </el-form-item>
                    </template>

                    <el-form-item label="压缩备份" class="mb-2">
                      <el-tooltip content="启用后将使用ZIP压缩格式进行备份，节省空间但可能增加处理时间" placement="top">
                        <el-switch
                          v-model="backupForm.useZip"
                          @change="handleUseZipChange"
                        />
                      </el-tooltip>
                    </el-form-item>

                    <el-form-item label="备份源目录" class="mb-2">
                      <div class="path-input-group">
                        <el-input
                          v-model="backupForm.backupDir"
                          placeholder="建议用软件的backup目录"
                          size="small"
                        />
                        <el-button type="primary" size="small" @click="selectDirectory('backupDir')">
                          <el-icon><Folder /></el-icon>
                        </el-button>
                      </div>
                    </el-form-item>

                    <el-form-item label="备份目录" class="mb-2">
                      <div class="path-input-group">
                        <el-input
                          v-model="backupForm.targetDir"
                          placeholder="备份存储目录"
                          size="small"
                        />
                        <el-button type="primary" size="small" @click="selectDirectory('targetDir')">
                          <el-icon><Folder /></el-icon>
                        </el-button>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>

                <div class="settings-card backup-history">
                  <div class="card-header">
                    <h3>备份历史</h3>
                    <el-button-group>
                      <el-button type="primary" size="small" @click="backupNow">
                        <el-icon><Upload /></el-icon>
                        立即备份
                      </el-button>
                      <el-button size="small" @click="refreshBackupHistory">
                        <el-icon><Refresh /></el-icon>
                      </el-button>
                    </el-button-group>
                  </div>
                  
                  <div class="table-container">
                    <el-table 
                      :data="backupHistory" 
                      style="width: 100%" 
                      border
                      size="small"
                      height="100%"
                    >
                      <el-table-column label="备份时间" min-width="160">
                        <template #default="{ row }">
                          {{ formatBackupTime(row.time) }}
                          <el-tag 
                            :type="row.type === 'auto' ? 'success' : 'primary'"
                            size="small"
                            class="ml-2"
                          >
                            {{ row.type === 'auto' ? '自动' : '手动' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="大小" width="100">
                        <template #default="{ row }">
                          {{ formatFileSize(row.size) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="路径" min-width="200" show-overflow-tooltip>
                        <template #default="{ row }">
                          {{ row.path }}
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="200" align="center" fixed="right">
                        <template #default="{ row }">
                          <el-button-group>
                            <el-button
                              type="primary"
                              size="small"
                              @click="restoreFromHistory(row)"
                            >
                              恢复
                            </el-button>
                            <el-button
                              type="danger"
                              size="small"
                              @click="deleteBackup(row)"
                            >
                              删除
                            </el-button>
                          </el-button-group>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="聊天设置" name="chat-settings">
          <div class="settings-section">
            <div class="section-header">
              <h2 class="section-title">AI聊天界面设置</h2>
              <div class="header-actions">
                <el-button type="primary" @click="saveChatSettings">
                  <el-icon><Check /></el-icon>
                  保存设置
                </el-button>
                <el-button @click="resetChatSettings">
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </el-button>
              </div>
            </div>

            <div class="panel-content">
              <div class="settings-card">
                <el-form label-width="140px">
                  <h3 class="subsection-title">界面样式</h3>
                  
                  <el-form-item label="字体大小">
                    <div class="slider-with-input">
                      <el-slider 
                        v-model="chatSettings.fontSize" 
                        :min="12" 
                        :max="20" 
                        :step="1"
                        show-input
                        :show-input-controls="false"
                        style="width: 300px;"
                      />
                      <span class="unit-label">像素</span>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="字体">
                    <el-select v-model="chatSettings.fontFamily" style="width: 300px;">
                      <el-option label="微软雅黑" value="微软雅黑, sans-serif" />
                      <el-option label="宋体" value="宋体, serif" />
                      <el-option label="黑体" value="黑体, sans-serif" />
                      <el-option label="楷体" value="楷体, serif" />
                      <el-option label="汉仪旗黑" value="汉仪旗黑, sans-serif" />
                      <el-option label="Arial" value="Arial, sans-serif" />
                    </el-select>
                  </el-form-item>

                  <h3 class="subsection-title">代码块样式</h3>
                  
                  <el-form-item label="代码块主题">
                    <el-radio-group v-model="chatSettings.codeBlockTheme">
                      <el-radio value="auto">跟随系统</el-radio>
                      <el-radio value="light">亮色主题</el-radio>
                      <el-radio value="dark">暗色主题</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  
                  <el-form-item label="代码块字体大小">
                    <div class="slider-with-input">
                      <el-slider 
                        v-model="chatSettings.codeBlockFontSize" 
                        :min="12" 
                        :max="20" 
                        :step="1"
                        show-input
                        :show-input-controls="false"
                        style="width: 300px;"
                      />
                      <span class="unit-label">像素</span>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="代码高亮样式">
                    <el-select v-model="chatSettings.codeHighlightStyle" style="width: 300px;">
                      <el-option label="Tomorrow (亮色)" value="tomorrow" />
                      <el-option label="Tomorrow Night (暗色)" value="tomorrow-night" />
                      <el-option label="Atom One Light" value="atom-one-light" />
                      <el-option label="Atom One Dark" value="atom-one-dark" />
                      <el-option label="Github" value="github" />
                      <el-option label="VS Code" value="vs2015" />
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="Git备份" name="git-backup">
          <div class="settings-section git-backup-container">
            <div class="section-header">
              <h2 class="section-title">Git备份配置</h2>
              <div class="header-actions">
                <el-button-group>
                  <el-button type="primary" @click="saveGitConfig">
                    <el-icon><Check /></el-icon>
                    保存配置
                  </el-button>
                  <el-button type="success" @click="checkGitInstallation">
                    <el-icon><Search /></el-icon>
                    检测Git
                  </el-button>
                  <el-button type="success" class="init-button" @click="initGitRepo" :disabled="!isGitConfigValid || !isGitInstalled">
                    <el-icon><Setting /></el-icon>
                    初始化仓库
                  </el-button>
                  <el-button type="success" @click="showManualCommitDialog" :disabled="!isGitConfigValid || !isGitInstalled">
                    <el-icon><Upload /></el-icon>
                    立即备份
                  </el-button>
                </el-button-group>
                <el-dropdown @command="handleGitCommand">
                  <el-button type="success" :disabled="!isGitConfigValid || !isGitInstalled">
                    <el-icon><Tools /></el-icon>
                    更多操作
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="checkStatus">检查仓库状态</el-dropdown-item>
                      <el-dropdown-item command="cleanStaged">清空暂存区</el-dropdown-item>
                      <el-dropdown-item command="resetSoft" divided>撤销上次提交(软)</el-dropdown-item>
                      <el-dropdown-item command="resetMixed">重置并清空暂存区</el-dropdown-item>
                      <el-dropdown-item command="resetHard">
                        <span style="color: #F56C6C;">强制重置(危险)</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <div class="panel-content">
              <div class="git-panel-layout">
                <!-- 左侧配置区域 -->
                <div class="git-config-section">
                  <div v-if="!isGitChecking && !gitInstallStatus?.installed" class="git-warning-message">
                    <el-alert
                      title="未检测到Git安装"
                      type="warning"
                      description="请先安装Git客户端，然后点击'检测Git'按钮重新检测。"
                      show-icon
                      :closable="false"
                    />
                  </div>
                  <div v-else-if="isGitChecking" class="git-checking-message">
                    <el-alert
                      title="正在检测Git安装..."
                      type="info"
                      show-icon
                      :closable="false"
                    />
                  </div>
                  <div v-else-if="gitInstallStatus?.installed" class="git-success-message">
                    <el-alert
                      :title="`Git已安装: ${gitInstallStatus.version}`"
                      type="success"
                      show-icon
                      :closable="false"
                    />
                  </div>
                  
                  <div class="git-settings-form">
                    <el-form label-width="140px" :model="gitConfig">
                      <h3 class="subsection-title">基本设置</h3>
                      
                      <el-form-item label="Git仓库URL" required>
                        <el-input 
                          v-model="gitConfig.repoUrl" 
                          placeholder="例如: https://e.coding.net/aiwork/author/pvv_backup.git"
                        />
                      </el-form-item>
                      
                      <el-form-item label="认证方式">
                        <el-radio-group v-model="gitConfig.authType">
                          <el-radio value="password">用户名和密码</el-radio>
                          <el-radio value="token">访问令牌</el-radio>
                        </el-radio-group>
                        <div class="form-item-tip">
                          <el-icon><InfoFilled /></el-icon>
                          推荐使用访问令牌，可以限制只访问特定仓库
                        </div>
                      </el-form-item>

                      <!-- 根据认证类型显示不同表单 -->
                      <template v-if="gitConfig.authType === 'password'">
                        <el-form-item label="Git用户名" required>
                          <el-input 
                            v-model="gitConfig.username" 
                            placeholder="Git用户名"
                          />
                        </el-form-item>
                        
                        <el-form-item label="Git密码" required>
                          <div class="path-input-group">
                            <el-input 
                              v-model="gitConfig.password" 
                              placeholder="Git密码"
                              show-password
                            />
                            <el-button 
                              type="primary" 
                              @click="testGitCredentials" 
                              :disabled="!gitConfig.repoUrl || !gitConfig.username || !gitConfig.password"
                            >
                              <el-icon><Connection /></el-icon>
                              测试连接
                            </el-button>
                          </div>
                          <div class="form-item-tip warning-tip">
                            <el-icon><Warning /></el-icon>
                            警告：使用密码方式会授予对所有仓库的访问权限
                          </div>
                        </el-form-item>
                      </template>

                      <template v-else>
                        <el-form-item label="Git用户名" required>
                          <el-input 
                            v-model="gitConfig.tokenUsername" 
                            placeholder="Git用户名（用于访问令牌认证）"
                          />
                          <div class="form-item-tip">
                            <el-icon><InfoFilled /></el-icon>
                            对于Coding平台，此处需填写您的用户名
                          </div>
                        </el-form-item>
                        
                        <el-form-item label="访问令牌" required>
                          <div class="path-input-group">
                            <el-input 
                              v-model="gitConfig.token" 
                              placeholder="Git个人访问令牌"
                              show-password
                            />
                            <el-button 
                              type="primary" 
                              @click="testGitCredentials" 
                              :disabled="!gitConfig.repoUrl || !gitConfig.token || (isCodingPlatform && !gitConfig.tokenUsername)"
                              class="test-connection-btn"
                            >
                              <el-icon><Connection /></el-icon>
                              测试连接
                            </el-button>
                          </div>
                          <div class="form-item-tip">
                            <el-icon><InfoFilled /></el-icon>
                            访问令牌可以限制只访问特定仓库，提高安全性
                            <el-link type="primary" @click="showTokenHelp" class="ml-2">如何创建?</el-link>
                          </div>
                        </el-form-item>
                      </template>
                      
                      <el-form-item label="备份内容目录" required>
                        <div class="path-input-group">
                          <el-input 
                            v-model="gitConfig.backupDir" 
                            placeholder="选择需要备份的目录"
                          />
                          <el-button type="primary" @click="selectDirectory('gitBackupDir')">
                            <el-icon><Folder /></el-icon>
                            选择目录
                          </el-button>
                        </div>
                        <div class="form-item-tip">
                          指定需要备份到Git的内容目录，通常是项目数据所在目录
                        </div>
                      </el-form-item>
                      
                      <el-form-item label="自动备份">
                        <el-switch v-model="gitConfig.autoBackup" @change="handleGitAutoBackupChange" />
                        <div class="form-item-tip" v-if="gitConfig.autoBackup">
                          系统将按照设定的时间间隔自动进行Git备份
                        </div>
                      </el-form-item>
                      
                      <el-form-item label="备份间隔" v-if="gitConfig.autoBackup">
                        <div class="input-with-tip">
                          <el-input-number 
                            v-model="gitConfig.backupInterval" 
                            :min="10" 
                            :max="1440"
                            :step="10"
                            controls-position="right"
                          />
                          <span class="ml-2">分钟</span>
                          <el-tooltip content="设置自动备份的时间间隔（10-1440分钟）" placement="top">
                            <el-icon class="ml-1"><InfoFilled /></el-icon>
                          </el-tooltip>
                        </div>
                      </el-form-item>
                    </el-form>
                  </div>
                </div>
                
                <!-- 右侧历史记录区域 -->
                <div class="git-history-section">
                  <div class="section-header">
                    <h3 class="history-title">备份历史记录</h3>
                    <el-button 
                      @click="refreshGitHistory" 
                      :loading="isLoadingHistory"
                      class="refresh-button"
                      type="primary"
                      plain
                      size="small"
                    >
                      <el-icon><Refresh /></el-icon>
                      刷新历史
                    </el-button>
                  </div>
                  
                  <div v-if="historyError" class="history-error">
                    <el-alert
                      :title="historyError"
                      type="error"
                      show-icon
                      :closable="false"
                    />
                  </div>
                  
                  <el-timeline v-else-if="gitHistory.length > 0" class="custom-timeline">
                    <el-timeline-item
                      v-for="commit in gitHistory"
                      :key="commit.hash"
                      :timestamp="commit.date"
                      :color="commit.isHead ? 'var(--el-color-success)' : 'var(--el-color-primary)'"
                      class="timeline-item"
                    >
                      <div class="commit-card">
                        <div class="commit-header">
                          <span class="commit-badge" :class="{ 'current-badge': commit.isHead }">
                            {{ commit.isHead ? '当前版本' : '历史版本' }}
                          </span>
                          <span class="commit-date">{{ formatDate(commit.date) }}</span>
                        </div>
                        
                        <h4 class="commit-message">{{ commit.message }}</h4>
                        
                        <div class="commit-meta">
                          <div class="hash-container">
                            <el-tag size="small" effect="dark" class="hash-tag">{{ commit.hash.substring(0, 7) }}</el-tag>
                            <span class="author">{{ commit.author }}</span>
                          </div>
                          
                          <div class="history-actions">
                            <el-tooltip content="查看详情" placement="top" :enterable="false">
                              <el-button size="small" circle @click="viewCommitDetails(commit)">
                                <el-icon><View /></el-icon>
                              </el-button>
                            </el-tooltip>
                            
                            <el-tooltip content="恢复到此版本" placement="top" :enterable="false">
                              <el-button 
                                size="small" 
                                circle
                                type="warning" 
                                @click="confirmResetToCommit(commit)"
                                :disabled="commit.isHead"
                              >
                                <el-icon><RefreshLeft /></el-icon>
                              </el-button>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                  
                  <div v-else-if="!isLoadingHistory" class="empty-history">
                    <el-empty description="暂无备份历史" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 添加/编辑用户数据目录对话框 -->
    <el-dialog
        v-model="userDataDirDialog.visible"
        :title="userDataDirDialog.isEdit ? '编辑用户数据目录' : '添加用户数据目录'"
        width="600px"
    >
      <el-form
          ref="userDataDirFormRef"
          :model="userDataDirDialog.form"
          :rules="userDataDirRules"
          label-width="120px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="userDataDirDialog.form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="路径" prop="path">
          <div class="path-input-group">
            <el-input v-model="userDataDirDialog.form.path" placeholder="请选择路径" />
            <el-button type="primary" @click="selectDirectory('userDataPath')">
              <el-icon><Folder /></el-icon>
              选择路径
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="userDataDirDialog.form.port" :min="1024" :max="65535" />
        </el-form-item>
        <el-form-item label="设为默认">
          <el-switch v-model="userDataDirDialog.form.isDefault" />
        </el-form-item>
        <el-form-item label="启用扩展">
          <el-switch v-model="userDataDirDialog.form.enableExtensions" />
        </el-form-item>
        <el-form-item 
            label="扩展路径" 
            prop="extensionsPath"
            v-if="userDataDirDialog.form.enableExtensions"
        >
          <div class="path-input-group">
            <el-input v-model="userDataDirDialog.form.extensionsPath" placeholder="请选择扩展路径" />
            <el-button type="primary" @click="selectDirectory('extensionsPath')">
              <el-icon><Folder /></el-icon>
              选择路径
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDataDirDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveUserDataDir">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- AI角色对话框 -->
    <el-dialog
      v-model="aiRoleDialog.visible"
      :title="aiRoleDialog.title"
      width="500px"
      destroy-on-close
    >
      <el-form :model="aiRoleDialog.form" label-width="100px">
        <el-form-item label="角色名称" required>
          <el-input v-model="aiRoleDialog.form.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="提示词" required>
          <el-input
            v-model="aiRoleDialog.form.prompt"
            type="textarea"
            :rows="8"
            placeholder="请输入角色提示词"
          />
        </el-form-item>
        <el-form-item label="启用">
          <el-switch v-model="aiRoleDialog.form.isEnabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="aiRoleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveAIRole">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- AI角色导入对话框 -->
    <el-dialog
      v-model="aiRoleImportDialog.visible"
      title="导入AI角色"
      width="600px"
      destroy-on-close
    >
      <div class="import-tip mb-3">
        请粘贴符合格式的JSON数据，可以粘贴单个角色或角色数组
      </div>
      <el-input
        v-model="aiRoleImportDialog.content"
        type="textarea"
        :rows="10"
        placeholder="粘贴JSON数据"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="aiRoleImportDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="importAIRoles">导入</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模型参数配置对话框 -->
    <el-dialog
      v-model="modelConfigDialog.visible"
      :title="`配置模型参数 - ${modelConfigDialog.modelName}`"
      width="600px"
      destroy-on-close
    >
      <el-form :model="modelConfigDialog.form" label-width="120px">
        <el-form-item label="温度 (Temperature)">
          <div class="param-input-group">
            <el-slider
              v-model="modelConfigDialog.form.temperature"
              :min="0"
              :max="2"
              :step="0.1"
              show-input
              :show-input-controls="false"
              style="width: 300px;"
            />
            <div class="param-tip">控制输出的随机性，值越高越随机</div>
          </div>
        </el-form-item>

        <el-form-item label="最大Token数">
          <div class="param-input-group">
            <el-input-number
              v-model="modelConfigDialog.form.max_tokens"
              :min="1"
              :max="131072"
              :step="256"
              controls-position="right"
              style="width: 200px;"
            />
            <div class="param-tip">限制生成文本的最大长度</div>
          </div>
        </el-form-item>

        <el-form-item label="Top P">
          <div class="param-input-group">
            <el-slider
              v-model="modelConfigDialog.form.top_p"
              :min="0"
              :max="1"
              :step="0.1"
              show-input
              :show-input-controls="false"
              style="width: 300px;"
            />
            <div class="param-tip">控制词汇选择的多样性</div>
          </div>
        </el-form-item>

        <el-form-item label="频率惩罚">
          <div class="param-input-group">
            <el-slider
              v-model="modelConfigDialog.form.frequency_penalty"
              :min="-2"
              :max="2"
              :step="0.1"
              show-input
              :show-input-controls="false"
              style="width: 300px;"
            />
            <div class="param-tip">减少重复词汇的出现频率</div>
          </div>
        </el-form-item>

        <el-form-item label="存在惩罚">
          <div class="param-input-group">
            <el-slider
              v-model="modelConfigDialog.form.presence_penalty"
              :min="-2"
              :max="2"
              :step="0.1"
              show-input
              :show-input-controls="false"
              style="width: 300px;"
            />
            <div class="param-tip">鼓励谈论新话题</div>
          </div>
        </el-form-item>

        <el-form-item label="流式输出">
          <el-switch
            v-model="modelConfigDialog.form.stream"
          />
          <div class="param-tip">启用实时流式输出</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="modelConfigDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveModelConfig">保存配置</el-button>
          <el-button @click="resetModelConfigToDefault">重置为默认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加备份进度条 -->
    <el-dialog
      v-model="backupProgress.visible"
      title="备份进度"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="backupProgress.status !== 'normal'"
    >
      <div class="backup-progress">
        <el-progress
          :percentage="backupProgress.percent"
          :status="backupProgress.status"
        />
        <div class="progress-message">{{ backupProgress.message }}</div>
      </div>
      <template #footer v-if="backupProgress.status !== 'normal' || backupProgress.percent === 100">
        <div class="backup-info" v-if="backupProgress.status === 'success'">
          <p>备份完成！文件已保存至：</p>
          <div class="backup-path">{{ backupProgress.backupPath }}</div>
        </div>
        <div class="backup-actions">
          <el-button @click="backupProgress.visible = false">关闭</el-button>
          <el-button 
            v-if="backupProgress.status === 'exception'" 
            type="primary" 
            @click="retryBackup"
          >重试</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Git差异对话框 -->
    <el-dialog
      v-model="gitDiffDialog.visible"
      title="提交差异"
      width="700px"
    >
      <div class="git-diff-container" v-loading="gitDiffDialog.loading">
        <pre class="git-diff-content">{{ gitDiffDialog.content }}</pre>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="gitDiffDialog.visible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Git备份进度对话框 -->
    <el-dialog
      v-model="gitProgress.visible"
      title="Git备份进度"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="gitProgress.status !== 'normal'"
    >
      <div class="backup-progress">
        <el-progress
          :percentage="gitProgress.percent"
          :status="gitProgress.status"
        />
        <div class="progress-message">{{ gitProgress.message }}</div>
      </div>
      <template #footer v-if="gitProgress.status !== 'normal' || gitProgress.percent === 100">
        <div class="backup-actions">
          <el-button @click="gitProgress.visible = false">关闭</el-button>
          <el-button 
            v-if="gitProgress.status === 'exception'" 
            type="primary" 
            @click="retryGitBackup"
          >重试</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Token帮助对话框 -->
    <el-dialog v-model="tokenHelpVisible" title="如何创建Git访问令牌" width="650px">
      <div class="token-help-content">
        <h3>GitHub访问令牌创建步骤</h3>
        <ol>
          <li>登录您的GitHub账户</li>
          <li>点击右上角头像 → Settings → Developer settings → Personal access tokens → Tokens (classic)</li>
          <li>点击"Generate new token" → "Generate new token (classic)"</li>
          <li>填写令牌描述，例如"PVV备份"</li>
          <li>设置适当的过期时间</li>
          <li>权限选择：建议只勾选"repo"权限（这只会授予对仓库的访问权限）</li>
          <li>点击"Generate token"</li>
          <li>复制生成的令牌（注意：令牌只会显示一次）</li>
        </ol>
        
        <h3>Gitee(码云)访问令牌创建步骤</h3>
        <ol>
          <li>登录您的Gitee账户</li>
          <li>点击右上角头像 → 设置 → 私人令牌</li>
          <li>点击"生成新令牌"</li>
          <li>填写令牌描述</li>
          <li>权限选择：建议只勾选"projects"权限</li>
          <li>点击"提交"</li>
          <li>复制生成的令牌</li>
        </ol>
        
        <h3>Coding 访问令牌创建与使用说明</h3>
        <ol>
          <li>登录您的 Coding 账户</li>
          <li>点击右上角头像 → 个人设置</li>
          <li>在左侧菜单栏选择"访问令牌"</li>
          <li>点击"新建令牌"按钮</li>
          <li>填写令牌名称，如"PVV备份"</li>
          <li>设置过期时间（可选择永不过期或特定日期）</li>
          <li>权限选择：勾选"项目"相关权限，建议选择"代码库 - 读写"权限</li>
          <li>单击"新建"按钮</li>
          <li>复制生成的令牌（注意：令牌只会显示一次）</li>
          <li><strong>使用方式</strong>：当选择访问令牌认证方式时，需要在"用户名"字段填写您的Coding用户名，在"令牌"字段填写获取的访问令牌</li>
        </ol>
        
        <h3>其他Git平台</h3>
        <p>大多数Git平台都支持个人访问令牌功能，请参考相应平台的文档进行创建。</p>
        
        <div class="security-tip">
          <el-alert
            title="安全提示"
            type="warning"
            description="访问令牌与密码一样重要，请妥善保管，不要泄露给他人。建议设置合理的过期时间并定期更新。"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 添加手动提交对话框 -->
    <el-dialog
      v-model="manualCommitDialogVisible"
      title="提交备份"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="manualCommitForm" label-width="100px">
        <el-form-item label="提交说明" required>
          <el-input
            v-model="manualCommitForm.message"
            type="textarea"
            :rows="4"
            placeholder="请输入本次备份的简要说明"
          ></el-input>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            建议使用英文描述，避免中文字符造成编码问题
          </div>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="manualCommitForm.force">
            强制备份（即使没有检测到变更）
          </el-checkbox>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            选择后将忽略文件变更检查，强制执行备份操作
          </div>
        </el-form-item>
        <!-- 添加查看变更按钮 -->
        <div class="form-actions">
          <el-button 
            type="primary" 
            plain 
            @click="showDiffDialog" 
            :loading="loadingDiff"
          >
            <el-icon><View /></el-icon>
            查看变更内容
          </el-button>
        </div>
        <!-- 在强制备份选项之后添加 -->
        <el-form-item label="标签名称">
          <el-input
            v-model="manualCommitForm.tagName"
            placeholder="例如: v1.0.0 或 完成第一章"
          ></el-input>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            为此备份创建一个有意义的标签名，方便后续查找和恢复
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="manualCommitDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitManualCommit" :loading="isSubmittingCommit">
            提交备份
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 差异对比弹窗 -->
    <el-dialog
      v-model="diffDialogVisible"
      title="变更内容预览"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
      fullscreen
    >
      <div class="diff-dialog-content">
        <GitDiffViewer
          :diff-text="diffText"
          :changed-files="changedFiles"
          :loading="loadingDiff"
          :error="diffError"
        />
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="diffDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="submitManualCommit">
            确认提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>

</script>

<style scoped lang="scss">
@import url("@/scss/app.scss");

</style>
