<template>
  <div class="simple-markdown-editor" :class="{ 'dark': isDarkTheme }">


    <!-- 内容区域 -->
    <div class="editor-content" :class="`mode-${currentMode}`">
      <!-- 编辑模式 -->
      <div v-show="currentMode === 'edit'" class="edit-mode">
        <textarea
          v-model="content"
          class="markdown-textarea"
          placeholder="请输入Markdown内容..."
          :style="editAreaStyle"
          @input="onContentChange"
        ></textarea>
      </div>

      <!-- 预览模式 -->
      <div v-show="currentMode === 'preview'" class="preview-mode">
        <div class="preview-content" :style="previewAreaStyle" v-html="renderedContent"></div>
      </div>

      <!-- 思维导图模式 -->
      <div v-show="currentMode === 'mindmap'" class="mindmap-mode">
        <div class="mindmap-placeholder" :style="mindmapAreaStyle">
          <el-empty description="思维导图功能开发中">
            <template #image>
              <el-icon size="60" color="var(--el-color-info)">
                <Share />
              </el-icon>
            </template>
            <p>将Markdown内容转换为可交互的思维导图</p>
            <p>支持AI辅助生成子节点内容</p>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, View, Document, Download, Share } from '@element-plus/icons-vue'
import { marked } from 'marked'
import { useConfigStore } from '@/stores/config'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '未命名文档'
  },
  mode: {
    type: String,
    default: 'edit'
  },
  editFontSize: {
    type: Number,
    default: 14
  },
  previewFontSize: {
    type: Number,
    default: 14
  },
  mindmapFontSize: {
    type: Number,
    default: 12
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save', 'export', 'content-change'])

// Store
const configStore = useConfigStore()

// 响应式数据
const currentMode = ref(props.mode)
const content = ref(props.modelValue)

// 计算属性
const isDarkTheme = computed(() => configStore.theme === 'dark')

// 动态字体大小样式
const editAreaStyle = computed(() => ({
  fontSize: `${props.editFontSize}px`,
  lineHeight: 1.6
}))

const previewAreaStyle = computed(() => ({
  fontSize: `${props.previewFontSize}px`,
  lineHeight: 1.6
}))

const mindmapAreaStyle = computed(() => ({
  fontSize: `${props.mindmapFontSize}px`,
  lineHeight: 1.5
}))

const renderedContent = computed(() => {
  if (!content.value) return ''
  try {
    return marked(content.value)
  } catch (error) {
    console.error('Markdown渲染错误:', error)
    return '<p>Markdown渲染出错</p>'
  }
})

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  content.value = newValue
})

watch(() => props.mode, (newMode) => {
  currentMode.value = newMode
})

watch(content, (newValue) => {
  emit('update:modelValue', newValue)
  emit('content-change')
})

// 监听字体大小变化（调试用）
watch(() => [props.editFontSize, props.previewFontSize, props.mindmapFontSize],
  ([editSize, previewSize, mindmapSize]) => {
    console.log('字体大小更新:', { editSize, previewSize, mindmapSize })
  }
)

// 方法
const onContentChange = () => {
  // 内容变化时的处理
}


</script>

<style lang="scss" scoped>
.simple-markdown-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--el-bg-color);
  transition: all 0.3s ease;
}



.editor-content {
  flex: 1;
  overflow: hidden;
}

.edit-mode {
  height: 100%;

  .markdown-textarea {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    resize: none;
    padding: 20px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    background: var(--el-bg-color);
    color: var(--el-text-color-primary);
    transition: font-size 0.2s ease;

    &::placeholder {
      color: var(--el-text-color-placeholder);
    }
  }
}

.preview-mode {
  height: 100%;
  overflow-y: auto;
  background: var(--el-bg-color);

  .preview-content {
    /* 基于GitHub主题的样式 */
    max-width: 860px;
    margin: 0 auto;
    padding: 30px;
    padding-bottom: 100px;
    font-family: "Open Sans","Clear Sans", "Helvetica Neue", Helvetica, Arial, 'Segoe UI Emoji', sans-serif;
    color: rgb(51, 51, 51);
    line-height: 1.6;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    transition: font-size 0.2s ease;

    /* 响应式设计 */
    @media only screen and (min-width: 1400px) {
      max-width: 1024px;
    }

    @media only screen and (min-width: 1800px) {
      max-width: 1200px;
    }

    /* 暗色主题适配 */
    .dark & {
      color: var(--el-text-color-primary);
      background: var(--el-bg-color);
    }
    
    /* GitHub主题标题样式 */
    h1, h2, h3, h4, h5, h6 {
      position: relative;
      margin-top: 1rem;
      margin-bottom: 1rem;
      font-weight: bold;
      line-height: 1.4;
      cursor: text;

      .dark & {
        color: var(--el-text-color-primary);
      }
    }

    h1 {
      font-size: 2.25em;
      line-height: 1.2;
      border-bottom: 1px solid #eee;

      .dark & {
        border-bottom-color: var(--el-border-color-light);
      }
    }

    h2 {
      font-size: 1.75em;
      line-height: 1.225;
      border-bottom: 1px solid #eee;

      .dark & {
        border-bottom-color: var(--el-border-color-light);
      }
    }

    h3 {
      font-size: 1.5em;
      line-height: 1.43;
    }

    h4 {
      font-size: 1.25em;
    }

    h5 {
      font-size: 1em;
    }

    h6 {
      font-size: 1em;
      color: #777;

      .dark & {
        color: var(--el-text-color-secondary);
      }
    }

    /* 首个列表的上边距 */
    > ul:first-child,
    > ol:first-child {
      margin-top: 30px;
    }
    
    /* GitHub主题段落和基础元素样式 */
    p, blockquote, ul, ol, dl, table {
      margin: 0.8em 0;
    }

    a {
      color: #4183C4;

      .dark & {
        color: var(--el-color-primary);
      }
    }
    
    /* GitHub主题代码样式 */
    code, tt {
      border: 1px solid #e7eaed;
      background-color: #f8f8f8;
      border-radius: 3px;
      padding: 2px 4px 0px 4px;
      font-size: 0.9em;

      .dark & {
        background-color: var(--el-fill-color-light);
        border-color: var(--el-border-color-lighter);
        color: var(--el-text-color-primary);
      }
    }

    code {
      background-color: #f3f4f4;
      padding: 0 2px 0 2px;

      .dark & {
        background-color: var(--el-fill-color-light);
      }
    }

    pre {
      margin-bottom: 15px;
      margin-top: 15px;
      padding-top: 8px;
      padding-bottom: 6px;
      background-color: #f8f8f8;

      .dark & {
        background-color: var(--el-fill-color-light);
      }
    }

    /* GitHub主题引用样式 */
    blockquote {
      border-left: 4px solid #dfe2e5;
      padding: 0 15px;
      color: #777777;

      .dark & {
        border-left-color: var(--el-border-color-light);
        color: var(--el-text-color-secondary);
      }

      blockquote {
        padding-right: 0;
      }
    }
    
    /* GitHub主题列表样式 */
    ul, ol {
      padding-left: 30px;

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    li > ol, li > ul {
      margin: 0 0;
    }

    li p.first {
      display: inline-block;
    }
    
    /* GitHub主题表格样式 */
    table {
      padding: 0;
      word-break: initial;
      border-collapse: collapse;

      tr {
        border: 1px solid #dfe2e5;
        margin: 0;
        padding: 0;

        .dark & {
          border-color: var(--el-border-color-light);
        }

        &:nth-child(2n) {
          background-color: #f8f8f8;

          .dark & {
            background-color: var(--el-fill-color-blank);
          }
        }
      }

      th {
        font-weight: bold;
        border: 1px solid #dfe2e5;
        border-bottom: 0;
        margin: 0;
        padding: 6px 13px;
        background-color: #f8f8f8;

        .dark & {
          border-color: var(--el-border-color-light);
          background-color: var(--el-fill-color-light);
          color: var(--el-text-color-primary);
        }

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      td {
        border: 1px solid #dfe2e5;
        margin: 0;
        padding: 6px 13px;

        .dark & {
          border-color: var(--el-border-color-light);
        }

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    /* GitHub主题分割线样式 */
    hr {
      height: 2px;
      padding: 0;
      margin: 16px 0;
      background-color: #e7e7e7;
      border: 0 none;
      overflow: hidden;
      box-sizing: content-box;

      .dark & {
        background-color: var(--el-border-color-light);
      }
    }

    /* GitHub主题图片样式 */
    img {
      max-width: 100%;
      height: auto;
      margin: 16px 0;
    }

    /* 强调文本样式 */
    strong, b {
      font-weight: bold;
    }

    em, i {
      font-style: italic;
    }
  }
}

.mindmap-mode {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .mindmap-placeholder {
    text-align: center;
    color: var(--el-text-color-secondary);
    transition: font-size 0.2s ease;

    p {
      margin: 8px 0;
    }
  }
}

// 深色主题适配
.dark {
  .markdown-textarea {
    background: var(--el-bg-color);
    color: var(--el-text-color-primary);
  }
  
  .preview-content {
    code {
      background: var(--el-fill-color-dark);
      color: var(--el-color-danger-light-3);
    }
    
    pre {
      background: var(--el-fill-color-dark);
      border-color: var(--el-border-color);
    }
    
    blockquote {
      background: var(--el-fill-color-dark);
    }
    
    table {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      
      th {
        background: var(--el-fill-color-dark);
      }
      
      tr:nth-child(even) {
        background: var(--el-fill-color-darker);
      }
      
      tr:hover {
        background: var(--el-fill-color-dark);
      }
    }
  }
}
</style>
