import os
import json
import hashlib
import base64
import time
from datetime import datetime, timedelta
from .Base import ResponsePacket
import jwt
import uuid
import platform
import hmac
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes
from .TimeValidator import get_network_time, has_valid_time, get_time_info

class UserManager(ResponsePacket):
    def __init__(self):
        """初始化用户管理器"""
        # 用户数据存储路径
        self._storage_file = os.path.join(os.path.expanduser('~'), '.pvv', 'user_data.json')
        
        # 激活码密钥 - 确保与激活码生成器匹配
        self._activation_secret = "nukita_pvv_secure_activation_key_2023"
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self._storage_file), exist_ok=True)
        
        # 初始化用户数据（如果不存在）
        if not os.path.exists(self._storage_file):
            self._save_user_data({
                "machine_code": "",
                "activation_code": "",
                "auto_login": False,
                "expires_at": 0,
                "days_remaining": 0,
                "last_login": None
            })

    def get_user_settings(self):
        """获取用户设置"""
        user_data = self._load_user_data() or {}
        return {
            "status": "success", 
            "data": {
                "machine_code": user_data.get("machine_code", ""),
                "activation_code": user_data.get("activation_code", "") if user_data.get("auto_login", False) else "",
                "auto_login": user_data.get("auto_login", False),
                "last_login": user_data.get("last_login")
            }
        }
    
    def save_user_settings(self, machine_code=None, activation_code=None, auto_login=None):
        """保存用户设置 - 确保记录变更"""
        user_data = self._load_user_data() or {}
        changes_made = False
        
        if machine_code is not None:
            user_data["machine_code"] = machine_code
            changes_made = True
        
        # 注意这里是 is not None 而不是直接判断真假值，以便允许清空激活码
        if activation_code is not None:
            user_data["activation_code"] = activation_code
            changes_made = True
        
        # 明确指定的自动登录状态
        if auto_login is not None:
            user_data["auto_login"] = auto_login
            changes_made = True
        
        # 只有在实际有变更时才更新时间戳和保存
        if changes_made:
            user_data["last_updated"] = datetime.now().isoformat()
            self._save_user_data(user_data)

        
        return {"status": "success", "message": "设置已保存" if changes_made else "无变更"}
    
    def login(self, profile_data):
        """用户登录 - 包含完整的验证逻辑和错误处理"""
        try:

            
            # 完整参数验证
            if not isinstance(profile_data, dict):
                return self._error_response("登录数据格式错误，需要提供一个包含登录信息的对象")
            
            activation_code = profile_data.get("activation_code", "")
            remember_login = profile_data.get("remember_login", False)
            
            if not activation_code:
                return self._error_response("激活码不能为空，请提供有效的激活码")
            
            # 获取系统机器码 - 只信任后端生成的
            system_machine_code = self.get_system_machine_code()
            if not system_machine_code:
                return self._error_response("无法获取系统机器码，请联系技术支持")
            

            
            # 检查网络时间可用性，提供更详细的错误信息
            if not has_valid_time():
                return self._error_response("无法获取网络时间进行激活码验证。请检查网络连接是否正常，确保能够访问互联网后重试。")
            
            # 验证激活码
            verify_result = self._verify_activation_code(system_machine_code, activation_code)

            
            if not verify_result['valid']:
                return self._error_response(verify_result['message'])
            
            # 验证成功，更新用户数据
            expires_at = verify_result['expires_at']
            days_remaining = verify_result['days_remaining']
            
            # 更新用户数据
            user_data = self._load_user_data() or {}
            user_data.update({
                'machine_code': system_machine_code,
                'activation_code': activation_code if remember_login else "",  # 仅在记住登录时保存激活码
                'auto_login': remember_login,
                'last_updated': datetime.now().isoformat(),
                'expires_at': expires_at,
                'days_remaining': days_remaining,
                'login_time': int(time.time()),
                'last_login': datetime.now().isoformat()
            })
            
            self._save_user_data(user_data)

            
            return self._success_response('登录成功', {
                'expires_at': expires_at,
                'expires_at_formatted': datetime.fromtimestamp(expires_at).strftime('%Y-%m-%d %H:%M:%S'),
                'days_remaining': days_remaining,
                'system_machine_code': system_machine_code
            })
        except Exception as e:

            import traceback
            traceback.print_exc()
            return self._error_response(f"登录失败: {str(e)}")
    
    def logout(self):
        """用户登出"""
        try:
            # 加载现有数据
            user_data = self._load_user_data() or {}
            
            # 保留机器码，清除其他数据
            user_data.update({
                'activation_code': "",
                'auto_login': False,
                'expires_at': 0,
                'days_remaining': 0,
                'login_time': 0,
                'last_login': datetime.now().isoformat()
            })
            
            # 移除 is_active 字段（如果存在）
            if 'is_active' in user_data:
                del user_data['is_active']
            
            # 保存更新后的数据
            self._save_user_data(user_data)
            
            return self._success_response("登出成功")
        except Exception as e:
            return self._error_response(f"登出失败: {str(e)}")
    
    def validate_session(self):
        """验证当前会话是否有效 - 使用严格的网络时间验证"""
        try:
            # 1. 快速预检查：确保有网络时间
            if not has_valid_time():
                time_info = get_time_info()
                return self._error_response('激活验证需要网络时间，请确保网络连接可用')
            
            # 2. 读取用户数据
            user_data = self._load_user_data()
            if not user_data:
                return self._error_response('未找到用户数据')
            
            # 3. 验证激活码存在
            activation_code = user_data.get('activation_code', '')
            if not activation_code:
                return self._error_response('请先输入激活码')
            
            # 4. 获取机器码并验证
            system_machine_code = self.get_system_machine_code()
            verify_result = self._verify_activation_code(system_machine_code, activation_code)
            
            if not verify_result['valid']:
                return self._error_response(verify_result['message'])
            
            # 5. 获取网络时间并计算剩余时间
            expires_at = verify_result['expires_at']
            network_time = get_network_time()
            
            # 再次确认网络时间可用
            if network_time is None:
                return self._error_response('无法获取网络时间，请稍后重试')
            
            # 计算剩余时间
            time_remaining = expires_at - network_time
            days_remaining = max(0, time_remaining // (24 * 60 * 60))
            hours_remaining = max(0, (time_remaining % (24 * 60 * 60)) // 3600)
            minutes_remaining = max(0, (time_remaining % 3600) // 60)
            
            # 6. 更新用户数据
            user_data.update({
                'expires_at': expires_at,
                'days_remaining': days_remaining,
                'machine_code': system_machine_code,
                'last_verified': datetime.now().isoformat()
            })
            self._save_user_data(user_data)
            
            # 7. 获取时间源信息
            time_source_info = get_time_info()
            
            # 8. 返回成功响应
            return self._success_response('验证成功', {
                'valid': True,
                'expires_at': expires_at,
                'expires_at_formatted': datetime.fromtimestamp(expires_at).strftime('%Y-%m-%d %H:%M:%S'),
                'days_remaining': days_remaining,
                'hours_remaining': hours_remaining,
                'minutes_remaining': minutes_remaining,
                'time_remaining': time_remaining,
                'system_machine_code': system_machine_code,
                'time_source': time_source_info.get('source'),
                'time_source_name': time_source_info.get('source_name'),
                'time_verified': time_source_info.get('verified')
            })
            
        except Exception as e:

            return self._error_response(f"验证失败: {str(e)}")

    def _verify_activation_code(self, machine_code, activation_code):
        """
        验证激活码的有效性
        返回结果字典包含验证状态和详细信息
        """
        try:
            # 解码激活码
            try:
                decoded_bytes = base64.urlsafe_b64decode(activation_code)
            except Exception as decode_error:

                return {'valid': False, 'message': '激活码格式错误，请检查激活码是否完整且未被修改', 'expires_at': 0}
            
            # 检查长度，至少需要IV(16字节) + AES块(至少16字节) + HMAC签名(16字节)
            if len(decoded_bytes) < 48:

                return {'valid': False, 'message': '激活码长度不正确，请检查激活码是否完整', 'expires_at': 0}
            
            # 从密钥派生AES加密密钥和HMAC密钥
            key_material = hashlib.sha256(self._activation_secret.encode()).digest()
            aes_key = key_material[:16]  # 取前16字节作为AES密钥
            hmac_key = key_material[16:] # 取后16字节作为HMAC密钥
            
            # 分解激活码
            iv = decoded_bytes[:16]
            signature = decoded_bytes[-16:]  # 最后16字节是签名
            encrypted_data = decoded_bytes[16:-16]  # 中间部分是加密数据
            
            # 验证HMAC签名
            expected_signature = hmac.new(
                hmac_key,
                decoded_bytes[:-16],  # IV + 加密数据
                digestmod=hashlib.sha256
            ).digest()[:16]
            
            if not hmac.compare_digest(signature, expected_signature):

                return {'valid': False, 'message': '激活码验证失败，可能已被修改或来源不正确', 'expires_at': 0}
            
            # 解密数据
            try:
                cipher = AES.new(aes_key, AES.MODE_CBC, iv)
                decrypted_padded = cipher.decrypt(encrypted_data)
                decrypted_data = unpad(decrypted_padded, AES.block_size).decode()
                
                # 分解解密后的数据
                parts = decrypted_data.split(':')
                if len(parts) != 2:

                    return {'valid': False, 'message': '激活码内容格式错误，请确认激活码是否正确', 'expires_at': 0}
                
                machine_id, expires_at_str = parts

            except Exception as e:

                return {'valid': False, 'message': '激活码解析失败，请检查激活码是否正确或完整', 'expires_at': 0}
            
            # 验证机器码 - 修改为更灵活的匹配方式
            # 只检查机器码前16位，允许后面部分有变化（例如硬盘更换、系统升级等）
            if machine_id != machine_code[:16]:

                
                # 计算匹配度 - 如果匹配超过75%，仍然允许激活
                match_count = sum(a == b for a, b in zip(machine_id, machine_code[:16]))
                match_percent = (match_count / 16) * 100
                
                if not match_percent >= 75:
                    return {'valid': False, 'message': '激活码与当前设备不匹配，请使用为此设备生成的激活码', 'expires_at': 0}
            
            # 修改: 强制使用网络时间，不再兼容本地时间
            current_time = get_network_time()
            if current_time is None:
                return {'valid': False, 'message': '无法获取网络时间进行激活码验证，请检查网络连接是否正常', 'expires_at': 0}
            
            # 验证过期时间
            try:
                expires_at = int(expires_at_str)
                
                # 记录时间信息（调试用）
                time_info = get_time_info()
                time_source = time_info.get("source", "unknown") if time_info.get("verified", False) else None

                
                if current_time > expires_at:
                    days_expired = (current_time - expires_at) // (24 * 60 * 60)
                    if days_expired == 0:
                        hours_expired = (current_time - expires_at) // 3600
                        return {'valid': False, 'message': f'激活码已过期 {hours_expired} 小时，请联系客服获取新的激活码', 'expires_at': expires_at}
                    else:
                        return {'valid': False, 'message': f'激活码已过期 {days_expired} 天，请联系客服获取新的激活码', 'expires_at': expires_at}
            except Exception as time_error:

                return {'valid': False, 'message': f'时间戳处理错误: {str(time_error)}', 'expires_at': 0}
            
            # 计算剩余有效天数
            days_remaining = (expires_at - current_time) // (24 * 60 * 60)
            hours_remaining = (expires_at - current_time - days_remaining * 24 * 60 * 60) // 3600
            

            
            return {
                'valid': True,
                'message': f'激活码有效，剩余 {days_remaining} 天',
                'expires_at': expires_at,
                'days_remaining': days_remaining,
                'hours_remaining': hours_remaining,
                'current_time': current_time
            }
            
        except Exception as e:
            import traceback

            traceback.print_exc()
            return {'valid': False, 'message': f'验证失败: {str(e)}', 'expires_at': 0}

    def generate_activation_code(self, machine_code, valid_days=365):
        """
        生成激活码 - 该方法通常由管理员使用
        确保对相同输入生成相同输出，使用AES加密保护激活码内容
        """
        try:
            # 验证机器码
            if not machine_code or len(machine_code) < 16:
                return self._error_response("机器码无效或长度不足")
            
            # 标准化机器码 - 取前16位作为标识符
            machine_id = machine_code[:16]
            
            # 计算过期时间 (以天为单位，避免秒级差异)
            # 使用网络时间代替本地时间
            network_time = get_network_time()
            if network_time is None:
                return self._error_response("无法获取网络时间，请检查网络连接")
            
            # 使用网络时间作为基准
            current_date = datetime.fromtimestamp(network_time).replace(hour=0, minute=0, second=0, microsecond=0)
            expiry_date = current_date + timedelta(days=valid_days)
            expires_at = int(expiry_date.timestamp())
            
            # 构建激活数据
            activation_data = f"{machine_id}:{expires_at}"
            
            # 从密钥派生AES加密密钥和HMAC密钥
            key_material = hashlib.sha256(self._activation_secret.encode()).digest()
            aes_key = key_material[:16]  # 取前16字节作为AES密钥
            hmac_key = key_material[16:] # 取后16字节作为HMAC密钥
            
            # 生成随机初始化向量
            iv = get_random_bytes(16)
            
            # 使用AES-CBC模式加密数据
            cipher = AES.new(aes_key, AES.MODE_CBC, iv)
            encrypted_data = cipher.encrypt(pad(activation_data.encode(), AES.block_size))
            
            # 组合IV和加密数据
            encrypted_package = iv + encrypted_data
            
            # 为整个加密包计算HMAC签名
            signature = hmac.new(
                hmac_key,
                encrypted_package,
                digestmod=hashlib.sha256
            ).digest()[:16]  # 使用16字节的签名
            
            # 最终激活码：IV + 加密数据 + 签名
            final_package = encrypted_package + signature
            
            # Base64编码，确保可移植性
            activation_code = base64.urlsafe_b64encode(final_package).decode()
            
            return self._success_response("激活码生成成功", {
                'activation_code': activation_code,
                'expires_at': expires_at,
                'expires_at_formatted': datetime.fromtimestamp(expires_at).strftime('%Y-%m-%d')
            })
            
        except Exception as e:
            return self._error_response(f"生成激活码失败: {str(e)}")

    def generate_activation_code_with_params(self, params):
        """带参数的激活码生成方法"""
        try:
            # 检查是否可以获取网络时间
            if not has_valid_time():
                return self._error_response('无法获取网络时间，请确保网络连接可用')
            
            machine_code = params.get('machine_code', '')
            valid_days = int(params.get('valid_days', 365))
            
            if not machine_code:
                return self._error_response("机器码不能为空")
            
            return self.generate_activation_code(machine_code, valid_days)
        except Exception as e:
            return self._error_response(f"生成激活码失败: {str(e)}")

    def _save_user_data(self, data):
        """保存用户数据到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self._storage_file), exist_ok=True)
            
            # 保存到临时文件
            temp_file = f"{self._storage_file}.tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 如果原文件存在，先删除
            if os.path.exists(self._storage_file):
                try:
                    os.remove(self._storage_file)
                except (PermissionError, OSError):
                    pass
            
            # 重命名临时文件
            os.rename(temp_file, self._storage_file)
            return True
            
        except Exception as e:

            return False

    def _load_user_data(self):
        """从文件加载用户数据并清理过时字段"""
        try:
            if not os.path.exists(self._storage_file):
                return None
            
            with open(self._storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 清理过时的is_active字段
            if 'is_active' in data:
                del data['is_active']
                # 立即保存清理后的数据
                self._save_user_data(data)
                
            return data
                
        except Exception as e:

            return None

    def get_system_machine_code(self):
        """获取系统机器码，确保稳定性和一致性"""
        try:
            # 直接使用HardwareIdentifier获取机器ID
            # 不从存储中获取，而是每次直接从硬件生成
            from .HardwareIdentifier import HardwareIdentifier

            
            # 尝试多种方法获取机器标识符
            identifier = HardwareIdentifier()
            machine_id = identifier.get_machine_id()
            
            # 确保长度一致性，便于后续处理
            if not machine_id:
                return None
            
            if len(machine_id) > 32:
                machine_id = machine_id[:32]
            
            # 记录但不保存机器码(我们每次都会重新从硬件生成)

            
            return machine_id
        
        except Exception as e:

            import traceback
            traceback.print_exc()
            return None

    def check_activation_status(self):
        """
        检查当前激活状态，返回剩余天数和其他详细信息
        不依赖存储的登录状态，始终进行实际验证
        增加网络时间等待和重试机制
        """
        try:
            # 增强的网络时间检查，带重试机制
            max_retries = 3
            retry_delay = 1  # 秒

            for retry_count in range(max_retries):
                # 检查是否可以获取网络时间
                if has_valid_time():
                    break

                if retry_count < max_retries - 1:
                    # 强制刷新网络时间并等待
                    print(f"网络时间未就绪，尝试刷新（第{retry_count + 1}次）...")
                    get_network_time(force_refresh=True)

                    # 等待一段时间让时间同步完成
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    # 最后一次尝试失败
                    return self._error_response('无法获取网络时间，请检查网络连接是否正常')

            # 读取用户数据
            user_data = self._load_user_data()
            if not user_data:
                return self._error_response("激活码验证失败。")

            # 获取保存的激活码
            activation_code = user_data.get('activation_code', '')

            # 如果没有激活码，无法验证
            if not activation_code:
                return self._error_response("激活码验证失败。")

            # 始终获取系统当前的机器码
            system_machine_code = self.get_system_machine_code()

            # 验证激活码
            verify_result = self._verify_activation_code(system_machine_code, activation_code)

            if not verify_result['valid']:
                # 验证失败，返回具体的错误信息
                return self._error_response(verify_result.get('message', '激活码验证失败'))
            
            # 验证成功，计算各种时间信息并返回
            expires_at = verify_result['expires_at']
            current_time = get_network_time()
            
            # 计算详细的剩余时间
            time_remaining = expires_at - current_time
            days_remaining = time_remaining // (24 * 60 * 60)
            hours_remaining = (time_remaining % (24 * 60 * 60)) // 3600
            minutes_remaining = (time_remaining % 3600) // 60
            
            # 创建易读的剩余时间格式
            if days_remaining > 0:
                readable_time = f"{days_remaining} 天 {hours_remaining} 小时"
            elif hours_remaining > 0:
                readable_time = f"{hours_remaining} 小时 {minutes_remaining} 分钟"
            else:
                readable_time = f"{minutes_remaining} 分钟"
            
            # 更新用户数据
            user_data['expires_at'] = expires_at
            user_data['days_remaining'] = days_remaining
            user_data['last_verified'] = datetime.now().isoformat()
            self._save_user_data(user_data)
            
            # 获取时间源信息
            time_source_info = get_time_info()
            
            return self._success_response('激活有效', {
                'valid': True,
                'expires_at': expires_at,
                'expires_at_formatted': datetime.fromtimestamp(expires_at).strftime('%Y-%m-%d %H:%M:%S'),
                'days_remaining': days_remaining,
                'hours_remaining': hours_remaining,
                'minutes_remaining': minutes_remaining,
                'readable_time': readable_time,
                'time_remaining': time_remaining,
                'time_source': time_source_info.get('source', 'unknown') if time_source_info.get('verified', False) else None
            })
            
        except Exception as e:

            return self._error_response(f"检查激活状态失败: {str(e)}")

    def verify_activation_code(self, data):
        """验证激活码的完整方法"""
        try:
            machine_code = data.get('machine_code', '')
            activation_code = data.get('activation_code', '')
            
            # 参数验证
            if not machine_code or not activation_code:
                return self._error_response('机器码和激活码不能为空')
            
            # 验证激活码
            verify_result = self._verify_activation_code(machine_code, activation_code)
            
            if not verify_result['valid']:
                return self._error_response(verify_result['message'])
            
            # 验证成功，更新用户数据
            expires_at = verify_result['expires_at']
            days_remaining = (datetime.fromtimestamp(expires_at) - datetime.now()).days
            
            # 更新用户数据
            self.save_user_settings(
                machine_code=machine_code,
                activation_code=activation_code,
                auto_login=None  # 不更改自动登录设置
            )
            
            return self._success_response('验证成功', {
                'expires_at': expires_at,
                'expires_at_formatted': datetime.fromtimestamp(expires_at).strftime('%Y-%m-%d %H:%M:%S'),
                'days_remaining': days_remaining
            })
        except Exception as e:
            import traceback
            traceback.print_exc()
            return self._error_response(f"验证激活码失败: {str(e)}")

    def get_machine_code(self):
        """获取系统机器码，包含完整错误处理"""
        try:
            machine_code = self.get_system_machine_code()
            if not machine_code:
                return self._error_response("无法获取系统机器码")
            
            return self._success_response("成功获取机器码", {"machine_code": machine_code})
        except Exception as e:

            import traceback
            traceback.print_exc()
            return self._error_response(f"获取机器码失败: {str(e)}")