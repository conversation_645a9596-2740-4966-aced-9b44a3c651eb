<template>
  <div class="highlight-input-container">
    <div
      ref="editableDiv"
      class="editable-input"
      contenteditable="true"
      :data-placeholder="placeholder"
      @input="handleInput"
      @keydown="handleKeydown"
      @paste="handlePaste"
      @focus="$emit('focus')"
      @blur="$emit('blur')"
    ></div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  entityReferences: {
    type: Map,
    default: () => new Map()
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'input', 'keydown', 'focus', 'blur', 'at-typed'])

// Refs
const editableDiv = ref(null)
const isInternalUpdate = ref(false) // 标记是否为内部更新

// 初始化
onMounted(() => {
  if (props.modelValue) {
    updateDisplayContent(props.modelValue)
  }
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  // 如果是内部更新触发的，跳过
  if (isInternalUpdate.value) return

  // 只有当外部值与当前文本内容不同时才更新显示
  const currentText = getTextContent()
  if (newValue !== currentText) {
    updateDisplayContent(newValue)
  }
})

// 监听entityReferences变化，重新渲染高亮
watch(() => props.entityReferences, () => {
  // 当实体引用发生变化时，重新渲染当前内容
  const currentText = getTextContent()
  updateDisplayContent(currentText)
}, { deep: true })

// 更新显示内容
const updateDisplayContent = (text) => {
  if (!editableDiv.value) return

  // 转换@实体为高亮标签
  const htmlContent = convertTextToHtml(text)

  // 保存光标位置
  const selection = window.getSelection()
  let cursorOffset = 0

  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    cursorOffset = getTextOffset(range.startContainer, range.startOffset)
  }

  // 更新内容
  editableDiv.value.innerHTML = htmlContent

  // 恢复光标位置
  nextTick(() => {
    setCursorPosition(cursorOffset)
  })
}

// 将纯文本转换为HTML（包含实体高亮）
const convertTextToHtml = (text) => {
  if (!text) return ''

  // 转义HTML特殊字符
  let html = text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')

  // 处理换行
  html = html.replace(/\n/g, '<br>')

  // 转换@实体为高亮标签
  // 首先处理已知的实体引用（从props中获取）
  if (props.entityReferences && props.entityReferences.size > 0) {
    for (const [entityTag, fullContent] of props.entityReferences.entries()) {
      // 转义特殊字符用于正则表达式
      const escapedTag = entityTag.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      const regex = new RegExp(escapedTag + '(?=\\s|$|<br>)', 'g')
      html = html.replace(regex, `<span class="entity-tag" contenteditable="false">${entityTag}</span>`)
    }
  }

  // 然后处理其他可能的@标签（不包含空格的简单情况）
  html = html.replace(/@([^\s\n@<>]+)(?=\s|$|<br>)/g, (match, content) => {
    // 检查是否已经被处理过（避免重复处理）
    if (html.includes(`<span class="entity-tag" contenteditable="false">@${content}</span>`)) {
      return match
    }
    return `<span class="entity-tag" contenteditable="false">@${content}</span>`
  })

  return html
}

// 获取纯文本内容
const getTextContent = () => {
  if (!editableDiv.value) return ''

  // 使用更简单的方法获取文本内容
  const clonedDiv = editableDiv.value.cloneNode(true)

  // 将所有实体标签替换为其文本内容
  const entityTags = clonedDiv.querySelectorAll('.entity-tag')
  entityTags.forEach(tag => {
    const textNode = document.createTextNode(tag.textContent)
    tag.parentNode.replaceChild(textNode, tag)
  })

  // 将 BR 标签替换为换行符
  const brTags = clonedDiv.querySelectorAll('br')
  brTags.forEach(br => {
    const textNode = document.createTextNode('\n')
    br.parentNode.replaceChild(textNode, br)
  })

  return clonedDiv.textContent || ''
}

// 获取光标在纯文本中的偏移位置
const getTextOffset = (container, offset) => {
  if (!editableDiv.value) return 0

  const range = document.createRange()
  range.selectNodeContents(editableDiv.value)
  range.setEnd(container, offset)

  // 获取范围内容并处理实体标签
  const content = range.cloneContents()

  // 处理实体标签
  const entityTags = content.querySelectorAll('.entity-tag')
  entityTags.forEach(tag => {
    const textNode = document.createTextNode(tag.textContent)
    tag.parentNode.replaceChild(textNode, tag)
  })

  // 处理换行
  const brTags = content.querySelectorAll('br')
  brTags.forEach(br => {
    const textNode = document.createTextNode('\n')
    br.parentNode.replaceChild(textNode, br)
  })

  return (content.textContent || '').length
}

// 获取当前光标位置
const getCursorPosition = () => {
  if (!editableDiv.value) return 0

  const selection = window.getSelection()
  if (selection.rangeCount === 0) return 0

  const range = selection.getRangeAt(0)

  // 创建一个范围从开始到光标位置
  const preRange = document.createRange()
  preRange.selectNodeContents(editableDiv.value)
  preRange.setEnd(range.startContainer, range.startOffset)

  // 获取这个范围的文本内容
  const preContent = preRange.cloneContents()

  // 处理实体标签
  const entityTags = preContent.querySelectorAll('.entity-tag')
  entityTags.forEach(tag => {
    const textNode = document.createTextNode(tag.textContent)
    tag.parentNode.replaceChild(textNode, tag)
  })

  // 处理换行
  const brTags = preContent.querySelectorAll('br')
  brTags.forEach(br => {
    const textNode = document.createTextNode('\n')
    br.parentNode.replaceChild(textNode, br)
  })

  return (preContent.textContent || '').length
}

// 设置光标位置
const setCursorPosition = (offset) => {
  if (!editableDiv.value) return

  const walker = document.createTreeWalker(
    editableDiv.value,
    NodeFilter.SHOW_TEXT,
    null,
    false
  )

  let currentOffset = 0
  let node

  while (node = walker.nextNode()) {
    const nodeLength = node.textContent.length
    if (currentOffset + nodeLength >= offset) {
      const range = document.createRange()
      const selection = window.getSelection()

      range.setStart(node, offset - currentOffset)
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
      return
    }
    currentOffset += nodeLength
  }

  // 如果没有找到合适的位置，设置到最后
  const range = document.createRange()
  const selection = window.getSelection()
  range.selectNodeContents(editableDiv.value)
  range.collapse(false)
  selection.removeAllRanges()
  selection.addRange(range)
}

// 处理输入
const handleInput = () => {
  const textContent = getTextContent()

  // 设置内部更新标记，避免触发外部监听
  isInternalUpdate.value = true
  emit('update:modelValue', textContent)
  emit('input', textContent)

  // 重置标记
  nextTick(() => {
    isInternalUpdate.value = false
  })
}

// 处理键盘事件
const handleKeydown = (event) => {
  // 检测@符号输入 - 支持多种方式
  const isAtSymbol = event.key === '@' ||
                    (event.shiftKey && event.key === '2') ||
                    (event.shiftKey && event.code === 'Digit2')

  if (isAtSymbol) {
    // 延迟触发，让@字符先输入
    setTimeout(() => {
      const cursorPos = getCursorPosition()
      emit('at-typed', { position: cursorPos })
    }, 10) // 使用 setTimeout 而不是 nextTick，确保字符已经输入
  }

  const selection = window.getSelection()

  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)

    // 处理退格键 - 只有在光标紧邻实体标签时才删除实体
    if (event.key === 'Backspace' && range.collapsed) {
      // 检查光标是否在文本节点的开头，且前面紧邻实体标签
      if (range.startOffset === 0 && range.startContainer.nodeType === Node.TEXT_NODE) {
        const prevSibling = range.startContainer.previousSibling
        if (prevSibling && prevSibling.classList && prevSibling.classList.contains('entity-tag')) {
          event.preventDefault()
          prevSibling.remove()
          handleInput()
          return
        }
      }
      // 检查光标是否在实体标签后面的位置
      else if (range.startContainer.nodeType === Node.ELEMENT_NODE) {
        const prevNode = range.startContainer.childNodes[range.startOffset - 1]
        if (prevNode && prevNode.classList && prevNode.classList.contains('entity-tag')) {
          event.preventDefault()
          prevNode.remove()
          handleInput()
          return
        }
      }
    }

    // 处理删除键 - 只有在光标紧邻实体标签时才删除实体
    if (event.key === 'Delete' && range.collapsed) {
      // 检查光标是否在文本节点的末尾，且后面紧邻实体标签
      if (range.startContainer.nodeType === Node.TEXT_NODE &&
          range.startOffset === range.startContainer.textContent.length) {
        const nextSibling = range.startContainer.nextSibling
        if (nextSibling && nextSibling.classList && nextSibling.classList.contains('entity-tag')) {
          event.preventDefault()
          nextSibling.remove()
          handleInput()
          return
        }
      }
      // 检查光标是否在实体标签前面的位置
      else if (range.startContainer.nodeType === Node.ELEMENT_NODE) {
        const nextNode = range.startContainer.childNodes[range.startOffset]
        if (nextNode && nextNode.classList && nextNode.classList.contains('entity-tag')) {
          event.preventDefault()
          nextNode.remove()
          handleInput()
          return
        }
      }
    }
  }

  emit('keydown', event)
}

// 处理粘贴
const handlePaste = (event) => {
  event.preventDefault()
  const text = event.clipboardData.getData('text/plain')

  // 使用现代方法插入文本
  const selection = window.getSelection()
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    range.deleteContents()
    range.insertNode(document.createTextNode(text))
    range.collapse(false)
    selection.removeAllRanges()
    selection.addRange(range)
    handleInput()
  }
}

// 聚焦方法
const focus = () => {
  if (editableDiv.value) {
    editableDiv.value.focus()
  }
}

// 替换@符号和搜索文本为实体标签
const replaceAtWithEntity = (entityText, searchText = '') => {
  if (!editableDiv.value) return

  console.log('replaceAtWithEntity 被调用:', { entityText, searchText })

  // 获取当前文本和光标位置
  const currentText = getTextContent()
  const cursorPos = getCursorPosition()

  console.log('当前文本:', currentText)
  console.log('光标位置:', cursorPos)

  // 查找光标前最近的@符号位置
  const beforeCursor = currentText.slice(0, cursorPos)
  const atIndex = beforeCursor.lastIndexOf('@')

  console.log('@ 符号位置:', atIndex)

  if (atIndex === -1) return

  // 构建新的文本内容
  const beforeAt = currentText.slice(0, atIndex)
  const afterSearch = currentText.slice(atIndex + 1 + searchText.length)
  const newText = beforeAt + entityText + afterSearch

  console.log('新文本内容:', newText)

  // 直接更新模型值
  isInternalUpdate.value = true
  emit('update:modelValue', newText)

  // 重新渲染内容
  nextTick(() => {
    updateDisplayContent(newText)

    // 设置光标位置到实体标签后面
    const newCursorPos = atIndex + entityText.length
    setCursorPosition(newCursorPos)

    console.log('设置新光标位置:', newCursorPos)

    isInternalUpdate.value = false
  })
}

// 获取选择范围
const getSelectionRange = () => {
  if (!editableDiv.value) return { start: 0, end: 0 }

  const selection = window.getSelection()
  if (selection.rangeCount === 0) return { start: 0, end: 0 }

  const range = selection.getRangeAt(0)
  const startOffset = getTextOffset(range.startContainer, range.startOffset)
  const endOffset = getTextOffset(range.endContainer, range.endOffset)

  return {
    start: startOffset,
    end: endOffset
  }
}

// 设置选择范围
const setSelectionRange = (start, end) => {
  setCursorPosition(start)
  // 如果需要选择范围，可以在这里扩展
}

// 暴露方法给父组件
defineExpose({
  focus,
  editableDiv,
  replaceAtWithEntity,
  getSelectionRange,
  setSelectionRange,
  getCursorPosition
})
</script>

<style scoped>
.highlight-input-container {
  position: relative;
  width: 100%;
  cursor: text !important;
}

.editable-input {
  width: 100%;
  padding: 12px 16px;
  font-family: inherit;
  font-size: inherit;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  background: var(--el-fill-color-blank);
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  outline: none;
  min-height: 50px;
  max-height: 150px;
  overflow-y: hidden;
  white-space: pre-wrap;
  word-wrap: break-word;
  cursor: text !important;
}

/* 确保在任何情况下都显示文本光标 */
.editable-input * {
  cursor: text !important;
}

.editable-input:focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.editable-input:empty:before {
  content: attr(data-placeholder);
  color: var(--el-text-color-placeholder);
  pointer-events: none;
}

/* 实体标签高亮样式 */
:deep(.entity-tag) {
  background: var(--el-color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 16px;
  font-size: 0.85em;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: inline-block;
  margin: 0 1px;
  line-height: 1.4;
  cursor: default;
  user-select: none;
}

/* 暗色主题适配 */
html.dark .editable-input {
  background: var(--el-fill-color-blank);
  color: var(--el-text-color-primary);
  border-color: var(--el-border-color);
}

html.dark :deep(.entity-tag) {
  background: var(--el-color-primary);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
</style>