import time
import threading
import traceback
import calendar
from datetime import datetime
import logging
import requests
import email.utils

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("TimeValidator")

class TimeValidator:
    """网络时间验证器 - 重构版，职责更清晰"""
    
    def __init__(self):
        """初始化时间验证器"""
        self.last_checked = 0
        self.time_offset = 0
        self.check_interval = 3600  # 1小时检查一次
        self.verified = False
        self.lock = threading.Lock()
        self.time_source = None
        self.initialization_complete = threading.Event()  # 添加初始化完成事件

        # 立即进行一次时间同步
        threading.Thread(target=self._initialize_time_sync, daemon=True).start()
    
    def get_network_time(self, force_refresh=False):
        """获取当前有效的网络时间
        
        Args:
            force_refresh: 是否强制刷新网络时间
            
        Returns:
            网络时间戳或None（如果获取失败）
        """
        current_local = time.time()
        
        with self.lock:
            # 判断是否需要重新获取网络时间
            time_since_last_check = current_local - self.last_checked
            if not force_refresh and time_since_last_check <= self.check_interval and self.verified:
                # 如果不需要刷新且已验证，直接返回校正后的时间
                return current_local + self.time_offset
        
        # 如果需要刷新，在锁外执行同步
        if force_refresh or time_since_last_check > self.check_interval or not self.verified:
            # 启动后台线程更新，但不等待
            if force_refresh:
                # 如果强制刷新，同步执行但设置超时
                self._synchronize_time()
            else:
                # 否则在后台执行
                threading.Thread(target=self._synchronize_time, daemon=True).start()
        
        # 再次检查验证状态
        with self.lock:
            if self.verified:
                return current_local + self.time_offset
            
            return None
    
    def has_valid_time(self, wait_for_init=True, timeout=10):
        """检查是否有有效的网络时间校准

        Args:
            wait_for_init: 是否等待初始化完成
            timeout: 等待超时时间（秒）
        """
        if wait_for_init:
            # 等待初始化完成，但设置超时
            if not self.initialization_complete.wait(timeout):
                logger.warning(f"等待时间同步初始化超时（{timeout}秒）")
                return False

        return self.verified
    
    def get_time_info(self):
        """获取时间源和同步信息"""
        with self.lock:
            if not self.verified:
                return {"verified": False}
                
            return {
                "verified": True,
                "source": self.time_source,
                "source_name": self._get_source_name(self.time_source),
                "offset": self.time_offset,
                "last_sync": datetime.fromtimestamp(self.last_checked).isoformat()
            }

    def _initialize_time_sync(self):
        """初始化时间同步，确保完成后设置事件"""
        try:
            success = self._synchronize_time()
            if success:
                logger.info("时间同步初始化完成")
            else:
                logger.warning("时间同步初始化失败，但不阻止应用启动")
        except Exception as e:
            logger.error(f"时间同步初始化异常: {str(e)}")
        finally:
            # 无论成功失败都设置初始化完成事件
            self.initialization_complete.set()

    def _synchronize_time(self):
        """同步网络时间（内部方法）"""
        try:
            # 获取当前本地时间
            local_time = time.time()
            
            # 增加总体超时时间，提高成功率
            timeout_seconds = 15
            
            # 尝试多种方式获取网络时间
            result = self._fetch_network_time(timeout_seconds)
            if not result:
                logger.error("所有网络时间源均失败")
                with self.lock:
                    self.verified = False
                return False
            
            network_time, source = result

            # 验证获取的时间是否合理（不能太偏离当前时间）
            time_diff = abs(network_time - local_time)
            if time_diff > 86400 * 365:  # 不能相差超过1年
                logger.error(f"获取的网络时间异常，与本地时间相差 {time_diff} 秒")
                with self.lock:
                    self.verified = False
                return False

            # 更新状态时加锁
            with self.lock:
                # 计算偏移量并更新状态
                self.time_offset = network_time - local_time
                self.last_checked = local_time
                self.time_source = source
                self.verified = True
            
            # 记录日志
            if abs(self.time_offset) > 300:  # 5分钟
                logger.warning(f"本地时间与网络时间差异较大: {self.time_offset:.2f}秒")
            else:
                logger.info(f"时间同步完成，来源: {source}，偏移量: {self.time_offset:.2f}秒")

            return True
        except Exception as e:
            logger.error(f"时间同步出错: {str(e)}")
            traceback.print_exc()
            with self.lock:
                self.verified = False
            return False
    
    def _fetch_network_time(self, total_timeout=10):
        """尝试多种方式获取网络时间，带总体超时和重试机制

        Args:
            total_timeout: 总体超时时间（秒）

        Returns:
            (timestamp, source_name) 元组或None（如果所有方法失败）
        """
        # 定义获取方法列表 - 按优先级排序，增加更多时间源
        methods = [
            ("https", self._try_https_time),
            ("worldtime", self._try_worldtime_api),
            ("ntp", self._try_ntp_time),
        ]

        start_time = time.time()

        # 增加重试机制 - 每种方法最多重试2次
        for retry_count in range(2):
            for source, method in methods:
                # 检查是否已超时
                elapsed = time.time() - start_time
                if elapsed > total_timeout:
                    logger.warning(f"获取网络时间总体超时（{total_timeout}秒）")
                    return None

                # 计算当前方法的可用超时时间，给每个方法更多时间
                remaining_time = total_timeout - elapsed
                method_timeout = max(2, min(4, remaining_time / len(methods)))

                try:
                    time_value = method(method_timeout)
                    if time_value:
                        logger.info(f"成功从 {source} 获取网络时间（重试次数: {retry_count}）")
                        return (time_value, source)
                except Exception as e:
                    logger.warning(f"{source}时间获取失败（重试{retry_count+1}/2）: {str(e)}")

                # 如果不是最后一次重试，短暂等待后继续
                if retry_count < 1 and elapsed < total_timeout - 1:
                    time.sleep(0.5)

        logger.error("所有网络时间获取方法均失败")
        return None
    
    def _try_https_time(self, timeout=4):
        """通过HTTPS头获取时间，增加更多可靠的时间源"""
        # 扩展的网站列表 - 包含更多稳定的服务器
        sites = [
            "https://www.baidu.com",
            "https://www.qq.com",
            "https://www.aliyun.com",
            "https://www.163.com",
            "https://www.taobao.com",
            "https://www.jd.com"
        ]

        # 尝试更多站点，提高成功率
        for site in sites[:4]:  # 尝试前4个站点
            try:
                # 增加请求头，提高成功率
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                response = requests.head(site, timeout=timeout, allow_redirects=True, headers=headers)
                if response.status_code < 400 and 'Date' in response.headers:
                    date_str = response.headers['Date']
                    date_tuple = email.utils.parsedate(date_str)
                    if date_tuple:
                        # 使用calendar.timegm处理UTC时间，避免时区问题
                        timestamp = calendar.timegm(date_tuple)
                        logger.debug(f"从 {site} 获取时间成功: {timestamp}")
                        return timestamp
            except Exception as e:
                logger.debug(f"从 {site} 获取时间失败: {str(e)}")
                continue

        return None
    
    def _try_worldtime_api(self, timeout=4):
        """通过WorldTime API获取时间，增加多个API源"""
        # 多个时间API源，提高可用性
        apis = [
            "http://worldtimeapi.org/api/timezone/Asia/Shanghai",
            "http://worldtimeapi.org/api/ip",  # 根据IP自动检测时区
            "https://worldtimeapi.org/api/timezone/Asia/Shanghai",  # HTTPS版本
        ]

        for api_url in apis:
            try:
                response = requests.get(api_url, timeout=timeout)
                if response.status_code == 200:
                    data = response.json()
                    timestamp = data.get('unixtime')
                    if timestamp:
                        logger.debug(f"从 {api_url} 获取时间成功: {timestamp}")
                        return timestamp
            except Exception as e:
                logger.debug(f"从 {api_url} 获取时间失败: {str(e)}")
                continue

        return None
    
    def _try_ntp_time(self, timeout=4):
        """尝试使用NTP获取时间，增加更多NTP服务器"""
        try:
            import ntplib

            # 扩展NTP服务器列表，提高成功率
            servers = [
                "ntp.aliyun.com",
                "ntp.tencent.com",
                "cn.pool.ntp.org",
                "asia.pool.ntp.org",
                "pool.ntp.org"
            ]

            client = ntplib.NTPClient()
            for server in servers[:3]:  # 尝试前3个服务器
                try:
                    response = client.request(server, timeout=timeout)
                    timestamp = response.tx_time
                    if timestamp:
                        logger.debug(f"从NTP服务器 {server} 获取时间成功: {timestamp}")
                        return timestamp
                except Exception as e:
                    logger.debug(f"从NTP服务器 {server} 获取时间失败: {str(e)}")
                    continue

        except ImportError:
            logger.warning("未安装ntplib库，无法使用NTP")
        except Exception as e:
            logger.warning(f"NTP时间获取过程出错: {str(e)}")

        return None
    
    def _get_source_name(self, source_code):
        """获取时间源的友好名称"""
        names = {
            "https": "HTTPS服务器时间",
            "worldtime": "世界时间API",
            "ntp": "NTP时间服务器"
        }
        return names.get(source_code, "未知时间源")

# 创建单例实例
time_validator = TimeValidator()

# 提供简洁的全局接口
def get_network_time(force_refresh=False):
    """获取当前网络时间"""
    return time_validator.get_network_time(force_refresh)

def has_valid_time():
    """检查是否有有效的网络时间"""
    return time_validator.has_valid_time()

def get_time_info():
    """获取时间源和同步信息"""
    return time_validator.get_time_info()

# 添加一个新的函数来获取网络时间信息，返回标准格式的响应
def get_network_time_info(force_refresh=True):
    """获取网络时间详细信息（包括来源、偏移量等），带超时保护"""
    from .Base import ResponsePacket  # 导入响应包装器基类
    response = ResponsePacket()
    
    try:
        # 设置超时，避免长时间阻塞
        timeout_result = threading.Event()
        time_info = {"completed": False}
        
        def get_time_with_timeout():
            try:
                network_time = get_network_time(force_refresh=force_refresh)
                local_time = time.time()
                
                if network_time:
                    time_diff = abs(network_time - local_time)
                    time_info_result = get_time_info()
                    
                    time_info["completed"] = True
                    time_info["success"] = True
                    time_info["network_time"] = network_time
                    time_info["local_time"] = local_time
                    time_info["time_diff"] = time_diff
                    time_info["time_source"] = time_info_result.get('source')
                    time_info["time_source_name"] = time_info_result.get('source_name')
                else:
                    time_info["completed"] = True
                    time_info["success"] = False
            except Exception as e:
                time_info["completed"] = True
                time_info["success"] = False
                time_info["error"] = str(e)
            finally:
                timeout_result.set()
        
        # 在后台线程中获取时间
        time_thread = threading.Thread(target=get_time_with_timeout)
        time_thread.daemon = True
        time_thread.start()
        
        # 等待最多20秒，给网络时间获取更多时间
        if not timeout_result.wait(20):
            return response._error_response("获取网络时间超时，请检查网络连接")
        
        if not time_info["completed"] or not time_info.get("success", False):
            return response._error_response("无法获取网络时间，请检查网络连接")
        
        return response._success_response("获取网络时间成功", {
            'network_time': time_info["network_time"],
            'network_time_formatted': datetime.fromtimestamp(time_info["network_time"]).strftime('%Y-%m-%d %H:%M:%S'),
            'local_time': time_info["local_time"],
            'local_time_formatted': datetime.fromtimestamp(time_info["local_time"]).strftime('%Y-%m-%d %H:%M:%S'),
            'time_diff': time_info["time_diff"],
            'time_source': time_info["time_source"],
            'time_source_name': time_info["time_source_name"]
        })
    except Exception as e:
        return response._error_response(f"获取网络时间失败: {str(e)}") 