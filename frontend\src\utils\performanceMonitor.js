/**
 * 性能监控工具
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.apiCalls = []
    this.maxHistorySize = 1000
  }

  /**
   * 开始性能测量
   * @param {string} name - 测量名称
   */
  start(name) {
    this.metrics.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    })
  }

  /**
   * 结束性能测量
   * @param {string} name - 测量名称
   * @returns {number} 持续时间（毫秒）
   */
  end(name) {
    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`Performance metric '${name}' not found`)
      return 0
    }

    metric.endTime = performance.now()
    metric.duration = metric.endTime - metric.startTime
    
    return metric.duration
  }

  /**
   * 获取测量结果
   * @param {string} name - 测量名称
   * @returns {object|null} 测量结果
   */
  getMetric(name) {
    return this.metrics.get(name) || null
  }

  /**
   * 记录API调用
   * @param {string} method - API方法名
   * @param {number} duration - 持续时间
   * @param {boolean} success - 是否成功
   * @param {any} error - 错误信息
   */
  recordApiCall(method, duration, success = true, error = null) {
    const record = {
      method,
      duration,
      success,
      error,
      timestamp: Date.now()
    }

    this.apiCalls.push(record)

    // 限制历史记录大小
    if (this.apiCalls.length > this.maxHistorySize) {
      this.apiCalls.shift()
    }
  }

  /**
   * 获取API调用统计
   * @param {number} timeWindow - 时间窗口（毫秒），默认最近1小时
   * @returns {object} 统计信息
   */
  getApiStats(timeWindow = 3600000) {
    const now = Date.now()
    const recentCalls = this.apiCalls.filter(
      call => now - call.timestamp <= timeWindow
    )

    if (recentCalls.length === 0) {
      return {
        totalCalls: 0,
        successRate: 0,
        averageDuration: 0,
        slowestCall: null,
        fastestCall: null,
        errorCount: 0
      }
    }

    const successfulCalls = recentCalls.filter(call => call.success)
    const durations = recentCalls.map(call => call.duration)
    
    return {
      totalCalls: recentCalls.length,
      successRate: (successfulCalls.length / recentCalls.length) * 100,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      slowestCall: recentCalls.reduce((prev, current) => 
        prev.duration > current.duration ? prev : current
      ),
      fastestCall: recentCalls.reduce((prev, current) => 
        prev.duration < current.duration ? prev : current
      ),
      errorCount: recentCalls.filter(call => !call.success).length,
      methodStats: this._getMethodStats(recentCalls)
    }
  }

  /**
   * 获取方法级别的统计
   * @param {Array} calls - API调用记录
   * @returns {object} 方法统计
   */
  _getMethodStats(calls) {
    const methodGroups = calls.reduce((groups, call) => {
      if (!groups[call.method]) {
        groups[call.method] = []
      }
      groups[call.method].push(call)
      return groups
    }, {})

    const methodStats = {}
    
    for (const [method, methodCalls] of Object.entries(methodGroups)) {
      const durations = methodCalls.map(call => call.duration)
      const successfulCalls = methodCalls.filter(call => call.success)
      
      methodStats[method] = {
        callCount: methodCalls.length,
        successRate: (successfulCalls.length / methodCalls.length) * 100,
        averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
        maxDuration: Math.max(...durations),
        minDuration: Math.min(...durations)
      }
    }

    return methodStats
  }

  /**
   * 检测慢API调用
   * @param {number} threshold - 阈值（毫秒）
   * @returns {Array} 慢调用列表
   */
  getSlowApiCalls(threshold = 1000) {
    return this.apiCalls.filter(call => call.duration > threshold)
  }

  /**
   * 清理旧的记录
   * @param {number} maxAge - 最大年龄（毫秒）
   */
  cleanup(maxAge = 86400000) { // 默认24小时
    const now = Date.now()
    this.apiCalls = this.apiCalls.filter(
      call => now - call.timestamp <= maxAge
    )
  }

  /**
   * 导出性能数据
   * @returns {object} 性能数据
   */
  export() {
    return {
      metrics: Object.fromEntries(this.metrics),
      apiCalls: this.apiCalls,
      stats: this.getApiStats(),
      timestamp: Date.now()
    }
  }

  /**
   * 重置所有数据
   */
  reset() {
    this.metrics.clear()
    this.apiCalls = []
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

/**
 * 性能监控装饰器
 * @param {string} name - 监控名称
 * @returns {Function} 装饰器函数
 */
export const monitor = (name) => {
  return (target, propertyKey, descriptor) => {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args) {
      const monitorName = name || `${target.constructor.name}.${propertyKey}`
      
      performanceMonitor.start(monitorName)
      
      try {
        const result = await originalMethod.apply(this, args)
        const duration = performanceMonitor.end(monitorName)
        
        performanceMonitor.recordApiCall(monitorName, duration, true)
        
        return result
      } catch (error) {
        const duration = performanceMonitor.end(monitorName)
        
        performanceMonitor.recordApiCall(monitorName, duration, false, error)
        
        throw error
      }
    }

    return descriptor
  }
}

/**
 * 监控API调用的高阶函数
 * @param {Function} apiCall - API调用函数
 * @param {string} methodName - 方法名称
 * @returns {Function} 包装后的函数
 */
export const monitorApiCall = (apiCall, methodName) => {
  return async (...args) => {
    const startTime = performance.now()
    
    try {
      const result = await apiCall(...args)
      const duration = performance.now() - startTime
      
      performanceMonitor.recordApiCall(methodName, duration, true)
      
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      
      performanceMonitor.recordApiCall(methodName, duration, false, error)
      
      throw error
    }
  }
}

/**
 * 获取性能统计信息
 * @param {number} timeWindow - 时间窗口
 * @returns {object} 统计信息
 */
export const getPerformanceStats = (timeWindow) => {
  return performanceMonitor.getApiStats(timeWindow)
}

/**
 * 清理性能数据
 * @param {number} maxAge - 最大年龄
 */
export const cleanupPerformanceData = (maxAge) => {
  performanceMonitor.cleanup(maxAge)
}

/**
 * 导出性能数据
 * @returns {object} 性能数据
 */
export const exportPerformanceData = () => {
  return performanceMonitor.export()
}

export default performanceMonitor
