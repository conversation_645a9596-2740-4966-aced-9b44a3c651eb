<template>
  <div 
    ref="containerRef" 
    class="virtual-scroll-container"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <!-- 虚拟滚动区域 -->
    <div 
      class="virtual-scroll-content"
      :style="{ height: totalHeight + 'px' }"
    >
      <!-- 可见项目渲染区域 -->
      <div 
        class="virtual-scroll-items"
        :style="{ transform: `translateY(${offsetY}px)` }"
      >
        <div
          v-for="item in visibleItems"
          :key="getItemKey(item)"
          :class="['virtual-scroll-item', getItemClass(item)]"
          :style="{ height: itemHeight + 'px' }"
          @click="handleItemClick(item, $event)"
        >
          <slot :item="item" :index="item.index">
            {{ getItemText(item) }}
          </slot>
        </div>
      </div>
    </div>

    <!-- 加载更多指示器 -->
    <div v-if="loading" class="virtual-scroll-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && items.length === 0" class="virtual-scroll-empty">
      <slot name="empty">
        <el-empty description="暂无数据" />
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Loading } from '@element-plus/icons-vue'

const props = defineProps({
  // 数据项列表
  items: {
    type: Array,
    default: () => []
  },
  // 每项的高度（像素）
  itemHeight: {
    type: Number,
    default: 40
  },
  // 容器高度（像素）
  containerHeight: {
    type: Number,
    default: 400
  },
  // 缓冲区大小（在可见区域外渲染的项目数）
  bufferSize: {
    type: Number,
    default: 5
  },
  // 获取项目唯一键的函数
  keyField: {
    type: String,
    default: 'id'
  },
  // 获取项目显示文本的函数
  textField: {
    type: String,
    default: 'title'
  },
  // 获取项目CSS类的函数
  classField: {
    type: [String, Function],
    default: ''
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  },
  // 是否启用无限滚动
  infiniteScroll: {
    type: Boolean,
    default: false
  },
  // 触发加载更多的距离（像素）
  loadMoreThreshold: {
    type: Number,
    default: 100
  }
})

const emit = defineEmits(['item-click', 'load-more', 'scroll'])

// 引用
const containerRef = ref(null)

// 滚动状态
const scrollTop = ref(0)

// 计算属性
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight))

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight) - props.bufferSize
  return Math.max(0, index)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value + props.bufferSize * 2
  return Math.min(props.items.length, index)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value).map((item, index) => ({
    ...item,
    index: startIndex.value + index
  }))
})

const offsetY = computed(() => startIndex.value * props.itemHeight)

// 方法
const getItemKey = (item) => {
  return item[props.keyField] || item.index
}

const getItemText = (item) => {
  return item[props.textField] || ''
}

const getItemClass = (item) => {
  if (typeof props.classField === 'function') {
    return props.classField(item)
  }
  return props.classField ? item[props.classField] : ''
}

const handleScroll = (event) => {
  scrollTop.value = event.target.scrollTop
  
  emit('scroll', {
    scrollTop: scrollTop.value,
    scrollHeight: event.target.scrollHeight,
    clientHeight: event.target.clientHeight
  })

  // 无限滚动检测
  if (props.infiniteScroll && !props.loading) {
    const { scrollTop: top, scrollHeight, clientHeight } = event.target
    const distanceToBottom = scrollHeight - top - clientHeight
    
    if (distanceToBottom <= props.loadMoreThreshold) {
      emit('load-more')
    }
  }
}

const handleItemClick = (item, event) => {
  emit('item-click', item, event)
}

// 滚动到指定项目
const scrollToItem = (index) => {
  if (containerRef.value) {
    const targetScrollTop = index * props.itemHeight
    containerRef.value.scrollTop = targetScrollTop
  }
}

// 滚动到指定位置
const scrollTo = (scrollTop) => {
  if (containerRef.value) {
    containerRef.value.scrollTop = scrollTop
  }
}

// 获取当前滚动位置
const getScrollTop = () => {
  return containerRef.value ? containerRef.value.scrollTop : 0
}

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollTo,
  getScrollTop
})

// 监听数据变化，重置滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  // 如果数据完全重新加载，重置滚动位置
  if (oldLength > 0 && newLength === 0) {
    nextTick(() => {
      scrollTo(0)
    })
  }
})
</script>

<style lang="scss" scoped>
.virtual-scroll-container {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

.virtual-scroll-content {
  position: relative;
  width: 100%;
}

.virtual-scroll-items {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-scroll-item {
  display: flex;
  align-items: center;
  padding: 0 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--el-fill-color-light);
  }

  &.active {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }
}

.virtual-scroll-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--el-text-color-secondary);
  
  .el-icon {
    margin-right: 8px;
  }
}

.virtual-scroll-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}
</style>
