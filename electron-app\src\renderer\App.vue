<template>
  <div id="app">
    <AppLayout />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import AppLayout from '@/components/layout/AppLayout.vue'
import { useConfigStore } from '@/stores/config'
import { useUserStore } from '@/stores/user'

const configStore = useConfigStore()
const userStore = useUserStore()

onMounted(async () => {
  // 初始化配置
  await configStore.initialize()

  // 初始化用户状态
  await userStore.initialize()

  // 测试API连接
  try {
    const result = await window.electronAPI.invoke('get_app_info')
    console.log('应用信息:', result)
  } catch (error) {
    console.error('获取应用信息失败:', error)
  }
})
</script>

<style>
#app {
  height: 100vh;
  overflow: hidden;
}

/* 全局主题变量 */
:root {
  --el-color-primary: #409eff;
  --el-color-primary-light-9: #ecf5ff;
  --el-bg-color: #ffffff;
  --el-bg-color-page: #f5f7fa;
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-border-color: #dcdfe6;
  --el-fill-color-light: #f5f7fa;
}

/* 深色主题 */
html.dark {
  --el-bg-color: #1a1a1a;
  --el-bg-color-page: #0a0a0a;
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-border-color: #414243;
  --el-fill-color-light: #262727;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-text-color-regular);
}
</style>
