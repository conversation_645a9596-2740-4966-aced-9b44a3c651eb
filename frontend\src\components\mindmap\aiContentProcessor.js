/**
 * AI内容处理器
 * 负责处理AI生成的内容并应用到思维导图中
 */

import { createUid } from 'simple-mind-map/src/utils'

export class AIContentProcessor {
  constructor() {
    this.markdownParser = new MarkdownParser()
  }

  /**
   * 应用AI生成的内容到思维导图
   * @param {Object} params - 生成参数
   * @param {Object} params.node - 目标节点
   * @param {string} params.mode - 生成模式
   * @param {string} params.content - 生成的内容
   * @param {Object} params.context - 上下文信息
   * @param {Object} mindmapData - 思维导图数据
   * @returns {Object} 更新后的思维导图数据
   */
  applyGeneratedContent(params, mindmapData) {
    const { node, mode, content, context } = params
    
    try {
      switch (mode) {
        case 'subtopics':
        case 'children':
        case 'analysis':
        case 'creative':
          // 所有模式都生成子节点，只是内容类型不同
          return this.addChildrenNodes(node, content, mindmapData, mode)
        default:
          throw new Error(`未知的生成模式: ${mode}`)
      }
    } catch (error) {
      console.error('应用AI内容失败:', error)
      throw error
    }
  }



  /**
   * 添加子节点（统一处理所有生成模式）
   */
  addChildrenNodes(targetNode, content, mindmapData, mode = 'children') {
    const updatedData = this.deepClone(mindmapData)
    const parentNode = this.findNodeById(updatedData, targetNode.id)

    if (parentNode) {
      const newChildren = this.parseContentToNodes(content, mode)

      if (!parentNode.children) {
        parentNode.children = []
      }

      // 添加新的子节点
      parentNode.children.push(...newChildren)

      // 更新父节点的更新时间
      parentNode.updatedAt = new Date().toISOString()

      console.log(`为节点"${parentNode.title}"添加了${newChildren.length}个${mode}类型的子节点`)
    }

    return updatedData
  }



  /**
   * 重新设计：根据模式解析内容为节点数组
   */
  parseContentToNodes(content, mode = 'children') {
    console.log(`=== AI内容解析开始 ===`)
    console.log(`模式: ${mode}`)
    console.log(`原始内容长度: ${content.length}`)
    console.log(`原始内容预览: ${content.substring(0, 300)}...`)

    // 清理AI响应中的垃圾内容
    const cleanedContent = this.cleanAIResponse(content)
    console.log(`清理后内容长度: ${cleanedContent.length}`)

    // 根据模式直接选择处理方式
    switch (mode) {
      case 'subtopics':
        // 子主题模式：只生成平级的子主题，不要层级结构
        return this.parseSubtopicsContent(cleanedContent, mode)

      case 'children':
        // 要点分解模式：深入分析当前主题，生成详细要点
        return this.parseDetailedContent(cleanedContent, mode)

      case 'analysis':
      case 'creative':
        // 分析和创意模式：智能处理
        return this.parseSmartContent(cleanedContent, mode)

      default:
        console.warn(`未知模式: ${mode}，使用默认处理`)
        return this.parseSmartContent(cleanedContent, mode)
    }
  }

  /**
   * 清理AI响应中的垃圾内容
   */
  cleanAIResponse(content) {
    if (!content || typeof content !== 'string') {
      return ''
    }

    let cleaned = content

    // 移除 <think></think> 标签及其内容
    cleaned = cleaned.replace(/<think>[\s\S]*?<\/think>/gi, '')

    // 移除其他常见的AI标签
    cleaned = cleaned.replace(/<reasoning>[\s\S]*?<\/reasoning>/gi, '')
    cleaned = cleaned.replace(/<analysis>[\s\S]*?<\/analysis>/gi, '')
    cleaned = cleaned.replace(/<reflection>[\s\S]*?<\/reflection>/gi, '')

    // 移除多余的空行
    cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n')

    // 移除开头和结尾的空白
    cleaned = cleaned.trim()

    // 移除常见的AI回复前缀
    const prefixesToRemove = [
      '好的，我来为您',
      '我来为您',
      '让我来',
      '我将为您',
      '根据您的要求',
      '基于您的需求',
      '以下是',
      '这里是'
    ]

    prefixesToRemove.forEach(prefix => {
      const regex = new RegExp(`^${prefix}[^。\n]*[。\n]?`, 'i')
      cleaned = cleaned.replace(regex, '')
    })

    // 再次清理空白
    cleaned = cleaned.trim()

    console.log('AI响应清理完成')
    return cleaned
  }

  /**
   * 详细内容处理：生成带层级结构的详细要点节点
   */
  parseDetailedContent(content, mode) {
    console.log(`=== 详细内容处理（保持层级结构）===`)

    const nodes = []
    const nodeStyle = this.getNodeStyle(mode)

    // 使用markdown解析器来保持层级结构
    const sections = this.markdownParser.parseToSections(content)
    console.log(`解析到 ${sections.length} 个章节`)

    if (sections.length > 0) {
      sections.forEach((section, index) => {
        if (section.title && section.title.trim()) {
          console.log(`处理要点 ${index + 1}: ${section.title} (层级: ${section.level})`)

          const mainNode = {
            id: createUid(),
            title: section.title.trim(),
            content: section.content ? section.content.trim() : '',
            children: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            aiGenerated: true,
            aiMode: mode,
            style: nodeStyle,
            level: section.level // 保存层级信息
          }

          // 处理子章节（层级结构）
          if (section.children && section.children.length > 0) {
            console.log(`节点 "${section.title}" 有 ${section.children.length} 个子章节`)

            section.children.forEach(childSection => {
              const childNode = {
                id: createUid(),
                title: childSection.title.trim(),
                content: childSection.content ? childSection.content.trim() : '',
                children: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                aiGenerated: true,
                aiMode: mode,
                style: { ...nodeStyle, opacity: 0.8 },
                level: childSection.level
              }

              // 如果子章节还有更深层级的内容
              if (childSection.children && childSection.children.length > 0) {
                childSection.children.forEach(grandChildSection => {
                  const grandChildNode = {
                    id: createUid(),
                    title: grandChildSection.title.trim(),
                    content: grandChildSection.content ? grandChildSection.content.trim() : '',
                    children: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    aiGenerated: true,
                    aiMode: mode,
                    style: { ...nodeStyle, opacity: 0.6 },
                    level: grandChildSection.level
                  }
                  childNode.children.push(grandChildNode)
                })
              }

              mainNode.children.push(childNode)
            })
          } else if (section.content && section.content.trim().length > 20) {
            // 如果没有子章节但有详细内容，将内容分段作为子节点
            console.log(`节点 "${section.title}" 包含详细内容，长度: ${section.content.length}`)

            const contentParts = this.splitDetailedContent(section.content)
            contentParts.forEach((part, partIndex) => {
              if (part.trim()) {
                const contentNode = {
                  id: createUid(),
                  title: part.trim(),
                  content: '',
                  children: [],
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  aiGenerated: true,
                  aiMode: mode,
                  style: { ...nodeStyle, opacity: 0.8 }
                }
                mainNode.children.push(contentNode)
              }
            })
          }

          nodes.push(mainNode)
        }
      })
    } else {
      // 如果没有明确的标题结构，按段落分割
      console.log(`没有找到标题结构，按段落分割`)
      const paragraphs = this.splitByParagraphs(content)

      paragraphs.forEach((paragraph, index) => {
        if (paragraph.trim().length > 10) {
          const title = this.extractTitleFromText(paragraph)
          const node = {
            id: createUid(),
            title: title,
            content: '',
            children: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            aiGenerated: true,
            aiMode: mode,
            style: nodeStyle
          }

          // 将段落的剩余内容作为子节点
          const remainingContent = paragraph.replace(title, '').trim()
          if (remainingContent.length > 10) {
            const contentParts = this.splitDetailedContent(remainingContent)
            contentParts.forEach(part => {
              if (part.trim()) {
                const contentNode = {
                  id: createUid(),
                  title: part.trim(),
                  content: '',
                  children: [],
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  aiGenerated: true,
                  aiMode: mode,
                  style: { ...nodeStyle, opacity: 0.8 }
                }
                node.children.push(contentNode)
              }
            })
          }

          nodes.push(node)
        }
      })
    }

    console.log(`详细内容处理完成，生成 ${nodes.length} 个主节点`)
    return nodes
  }

  /**
   * 分割详细内容为多个部分
   */
  splitDetailedContent(content) {
    // 按句号、换行等分割
    const parts = content
      .split(/[。\n]/)
      .map(part => part.trim())
      .filter(part => part.length > 5)

    // 如果分割后的部分太少，按长度分割
    if (parts.length < 2 && content.length > 50) {
      const chunkSize = Math.ceil(content.length / 3)
      const chunks = []
      for (let i = 0; i < content.length; i += chunkSize) {
        chunks.push(content.substring(i, i + chunkSize))
      }
      return chunks
    }

    return parts
  }

  /**
   * 子主题内容处理：只生成平级的子主题，不要层级结构
   */
  parseSubtopicsContent(content, mode) {
    console.log(`=== 子主题内容处理（平级结构）===`)

    // 先进行内容预处理，提高解析质量
    const preprocessedContent = this.preprocessContent(content)
    const sections = this.markdownParser.parseToSections(preprocessedContent)
    console.log(`解析到 ${sections.length} 个子主题`)

    if (sections.length === 0) {
      console.log('没有解析到标题结构，尝试智能提取')
      return this.intelligentTopicExtraction(preprocessedContent, mode)
    }

    const nodes = []
    const nodeStyle = this.getNodeStyle(mode)

    sections.forEach((section, index) => {
      if (section.title && section.title.trim()) {
        console.log(`处理子主题 ${index + 1}: ${section.title}`)

        // 清理和优化标题和内容
        const cleanTitle = this.cleanTitle(section.title)
        const cleanContent = this.cleanContent(section.content || '')

        // 只创建主节点，不创建子层级
        const mainNode = {
          id: createUid(),
          title: cleanTitle,
          content: cleanContent,
          children: [], // 强制为空，不创建子层级
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          aiGenerated: true,
          aiMode: mode,
          style: nodeStyle,
          quality: this.assessNodeQuality(cleanTitle, cleanContent)
        }

        // 只添加质量合格的节点
        if (mainNode.quality.score >= 60) {
          nodes.push(mainNode)
        } else {
          console.warn('跳过低质量节点:', cleanTitle, mainNode.quality.issues)
        }
      }
    })

    console.log(`生成了 ${nodes.length} 个平级子主题节点`)
    return this.deduplicateNodes(nodes)
  }

  /**
   * 从段落中解析子主题（平级结构）
   */
  parseSubtopicsFromParagraphs(content, mode) {
    console.log('从段落中提取子主题')

    const paragraphs = this.splitByParagraphs(content)
    const nodes = []
    const nodeStyle = this.getNodeStyle(mode)

    paragraphs.forEach((paragraph, index) => {
      if (paragraph.trim().length > 10) {
        const title = this.extractTitleFromText(paragraph)

        // 只创建主节点，不创建子层级
        const node = {
          id: createUid(),
          title: title,
          content: paragraph.replace(title, '').trim(), // 将剩余内容作为节点内容而不是子节点
          children: [], // 强制为空，不创建子层级
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          aiGenerated: true,
          aiMode: mode,
          style: nodeStyle
        }

        nodes.push(node)
      }
    })

    console.log(`从段落生成了 ${nodes.length} 个平级子主题节点`)
    return nodes
  }

  /**
   * 智能内容处理：根据内容特征自动选择策略
   */
  parseSmartContent(content, mode) {
    console.log(`=== 智能内容处理 ===`)

    const sections = this.markdownParser.parseToSections(content)
    console.log(`解析到 ${sections.length} 个章节`)

    if (sections.length === 0) {
      // 没有解析到结构，使用简单分割
      return this.parseDetailedContent(content, mode)
    }

    // 分析内容特征
    const hasRichContent = sections.some(s => s.content && s.content.length > 50)

    if (hasRichContent) {
      console.log(`检测到丰富内容，使用详细提取`)
      return this.extractDetailedNodes(sections, mode)
    } else {
      console.log(`内容较简洁，使用大纲提取`)
      return this.extractOutlineNodes(sections, mode)
    }
  }

  /**
   * 大纲内容处理：只生成主题标题，不包含详细内容
   */
  parseOutlineContent(content, mode) {
    console.log(`=== 大纲内容处理 ===`)

    const nodes = []
    const nodeStyle = this.getNodeStyle(mode)

    // 提取所有可能的标题
    const titles = this.extractTitles(content)

    titles.forEach((title, index) => {
      if (title.trim()) {
        const node = {
          id: createUid(),
          title: title.trim(),
          content: '', // 大纲模式不包含详细内容
          children: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          aiGenerated: true,
          aiMode: mode,
          style: nodeStyle
        }
        nodes.push(node)
      }
    })

    console.log(`大纲处理完成，生成 ${nodes.length} 个主题节点`)
    return nodes
  }

  /**
   * 确定提取策略
   */
  determineExtractionStrategy(sections, mode) {
    // 分析内容特征
    const totalSections = sections.length
    const sectionsWithContent = sections.filter(s => s.content && s.content.trim().length > 20).length
    const sectionsWithSubtitles = sections.filter(s => s.children && s.children.length > 0).length
    const avgContentLength = sections.reduce((sum, s) => sum + (s.content?.length || 0), 0) / totalSections

    console.log('内容分析:', {
      总章节数: totalSections,
      有实质内容的章节: sectionsWithContent,
      有子标题的章节: sectionsWithSubtitles,
      平均内容长度: Math.round(avgContentLength)
    })

    // 策略判断逻辑
    if (mode === 'outline') {
      return 'outline' // 大纲模式强制使用大纲提取
    }

    if (mode === 'expand' || mode === 'analysis') {
      return 'detailed' // 扩展和分析模式使用详细提取
    }

    // 智能判断
    if (avgContentLength > 50 && sectionsWithContent / totalSections > 0.6) {
      return 'detailed' // 内容丰富，使用详细提取
    } else if (totalSections > 5 && avgContentLength < 30) {
      return 'outline' // 章节多但内容少，使用大纲提取
    } else {
      return 'mixed' // 混合策略
    }
  }

  /**
   * 详细提取：标题作为父节点，内容作为子节点
   */
  extractDetailedNodes(sections, mode) {
    const nodes = []
    const nodeStyle = this.getNodeStyle(mode)

    sections.forEach((section, index) => {
      if (!section.title) return

      const parentNode = {
        id: createUid(),
        title: section.title,
        content: '', // 父节点不保存内容，内容在子节点中
        children: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        aiGenerated: true,
        aiMode: mode,
        style: nodeStyle
      }

      // 如果有实质内容，直接解析内容中的实际结构
      if (section.content && section.content.trim()) {
        const parsedNodes = this.parseContentDirectly(section.content, mode)

        if (parsedNodes.length > 0) {
          // 直接添加解析出的节点，不创建"要点"中间层级
          parsedNodes.forEach(node => {
            const childNode = {
              id: createUid(),
              title: node.title,
              content: node.content || '',
              children: [],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              aiGenerated: true,
              aiMode: mode,
              style: { ...nodeStyle, opacity: 0.8 }
            }
            parentNode.children.push(childNode)
          })
          console.log(`从内容中解析出 ${parsedNodes.length} 个直接子节点`)
        } else {
          // 如果无法解析出结构，创建一个详细内容子节点
          const contentNode = {
            id: createUid(),
            title: '详细内容',
            content: section.content.trim(),
            children: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            aiGenerated: true,
            aiMode: mode,
            style: { ...nodeStyle, opacity: 0.8 }
          }
          parentNode.children.push(contentNode)
          console.log(`内容无明确结构，创建详细内容子节点`)
        }
      }

      // 处理子标题
      if (section.children && section.children.length > 0) {
        section.children.forEach(child => {
          parentNode.children.push({
            id: createUid(),
            title: child.title,
            content: child.content || '',
            children: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            aiGenerated: true,
            aiMode: mode,
            style: { ...nodeStyle, opacity: 0.8 }
          })
        })
      }

      // 如果没有子节点，添加一个默认内容节点
      if (parentNode.children.length === 0 && section.content && section.content.trim()) {
        const contentNode = {
          id: createUid(),
          title: '详细内容',
          content: section.content.trim(),
          children: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          aiGenerated: true,
          aiMode: mode,
          style: { ...nodeStyle, opacity: 0.8 }
        }
        parentNode.children.push(contentNode)
        console.log(`添加默认详细内容子节点，内容长度: ${section.content.trim().length}`)
      }

      nodes.push(parentNode)
    })

    console.log(`详细提取完成，创建${nodes.length}个父节点，${nodes.reduce((sum, n) => sum + n.children.length, 0)}个子节点`)
    return nodes
  }

  /**
   * 大纲提取：仅提取标题作为平级节点
   */
  extractOutlineNodes(sections, mode) {
    const nodes = []
    const nodeStyle = this.getNodeStyle(mode)

    sections.forEach(section => {
      if (!section.title) return

      // 主标题节点
      nodes.push({
        id: createUid(),
        title: section.title,
        content: section.content ? section.content.substring(0, 100) + '...' : '',
        children: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        aiGenerated: true,
        aiMode: mode,
        style: nodeStyle
      })

      // 子标题节点（平级）
      if (section.children && section.children.length > 0) {
        section.children.forEach(child => {
          nodes.push({
            id: createUid(),
            title: child.title,
            content: child.content ? child.content.substring(0, 100) + '...' : '',
            children: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            aiGenerated: true,
            aiMode: mode,
            style: { ...nodeStyle, opacity: 0.9 }
          })
        })
      }
    })

    console.log(`大纲提取完成，创建${nodes.length}个平级节点`)
    return nodes
  }

  /**
   * 混合提取：根据内容长度决定是否创建子节点
   */
  extractMixedNodes(sections, mode) {
    const nodes = []
    const nodeStyle = this.getNodeStyle(mode)

    sections.forEach(section => {
      if (!section.title) return

      const hasSubstantialContent = section.content && section.content.trim().length > 50

      if (hasSubstantialContent) {
        // 有实质内容，使用详细提取
        const detailedNodes = this.extractDetailedNodes([section], mode)
        nodes.push(...detailedNodes)
      } else {
        // 内容较少，使用大纲提取
        const outlineNodes = this.extractOutlineNodes([section], mode)
        nodes.push(...outlineNodes)
      }
    })

    console.log(`混合提取完成，创建${nodes.length}个节点`)
    return nodes
  }

  /**
   * 获取节点样式
   */
  getNodeStyle(mode) {
    const nodeStyles = {
      expand: { backgroundColor: '#f0f9ff', borderColor: '#0ea5e9' },
      children: { backgroundColor: '#f0fdf4', borderColor: '#22c55e' },
      analysis: { backgroundColor: '#fef3c7', borderColor: '#f59e0b' },
      outline: { backgroundColor: '#f3e8ff', borderColor: '#a855f7' },
      creative: { backgroundColor: '#fce7f3', borderColor: '#ec4899' },
      related: { backgroundColor: '#f1f5f9', borderColor: '#64748b' }
    }
    return nodeStyles[mode] || nodeStyles.children
  }

  /**
   * 直接解析内容中的实际结构，避免创建"要点"等中间层级
   */
  parseContentDirectly(content, mode) {
    if (!content || !content.trim()) return []

    const nodes = []
    const lines = content.split('\n').map(line => line.trim()).filter(line => line)

    let currentNode = null
    let currentContent = []

    lines.forEach(line => {
      // 检查是否是标题格式
      const headingMatch = line.match(/^(#+)\s+(.+)$/)
      const listMatch = line.match(/^[-*+]\s+(.+)$/)
      const numberedMatch = line.match(/^\d+\.\s+(.+)$/)
      const colonMatch = line.match(/^([^：:]+)[：:]\s*(.*)$/)

      if (headingMatch) {
        // Markdown标题
        if (currentNode) {
          currentNode.content = currentContent.join('\n').trim()
          if (currentNode.title) nodes.push(currentNode)
        }
        currentNode = { title: headingMatch[2], content: '' }
        currentContent = []
      } else if (listMatch || numberedMatch) {
        // 列表项或编号项
        if (currentNode) {
          currentNode.content = currentContent.join('\n').trim()
          if (currentNode.title) nodes.push(currentNode)
        }
        const title = listMatch ? listMatch[1] : numberedMatch[1]
        currentNode = { title: title, content: '' }
        currentContent = []
      } else if (colonMatch && !currentNode) {
        // 冒号分隔的标题:内容格式
        const title = colonMatch[1].trim()
        const content = colonMatch[2].trim()
        nodes.push({ title: title, content: content })
      } else {
        // 普通内容行
        currentContent.push(line)
      }
    })

    // 处理最后一个节点
    if (currentNode) {
      currentNode.content = currentContent.join('\n').trim()
      if (currentNode.title) nodes.push(currentNode)
    }

    // 如果没有解析出任何结构，尝试按段落分割
    if (nodes.length === 0 && content.trim()) {
      const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim())
      if (paragraphs.length > 1) {
        return paragraphs.map((paragraph, index) => ({
          title: this.extractTitleFromParagraph(paragraph),
          content: paragraph.trim()
        }))
      }
    }

    console.log(`直接解析内容，提取出 ${nodes.length} 个节点`)
    return nodes
  }

  /**
   * 按标题分割内容
   */
  splitByHeaders(content) {
    const sections = []
    const lines = content.split('\n')
    let currentSection = null

    lines.forEach(line => {
      const trimmedLine = line.trim()

      // 检测标题格式
      const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/)
      const listMatch = trimmedLine.match(/^[-*+]\s+(.+)$/)
      const numberedMatch = trimmedLine.match(/^\d+\.\s+(.+)$/)
      const colonMatch = trimmedLine.match(/^([^：:]+)[：:]\s*(.*)$/)

      if (headerMatch || listMatch || numberedMatch || colonMatch) {
        // 保存前一个section
        if (currentSection) {
          sections.push(currentSection)
        }

        // 创建新section
        let title = ''
        let initialContent = ''

        if (headerMatch) {
          title = headerMatch[2]
        } else if (listMatch) {
          title = listMatch[1]
        } else if (numberedMatch) {
          title = numberedMatch[1]
        } else if (colonMatch) {
          title = colonMatch[1]
          initialContent = colonMatch[2] || ''
        }

        currentSection = {
          title: title,
          content: initialContent
        }
      } else if (currentSection && trimmedLine) {
        // 添加到当前section的内容
        currentSection.content += (currentSection.content ? '\n' : '') + trimmedLine
      }
    })

    // 添加最后一个section
    if (currentSection) {
      sections.push(currentSection)
    }

    return sections
  }

  /**
   * 按段落分割内容
   */
  splitByParagraphs(content) {
    return content
      .split(/\n\s*\n/)
      .map(p => p.trim())
      .filter(p => p.length > 0)
  }

  /**
   * 从文本中提取标题
   */
  extractTitleFromText(text) {
    const firstLine = text.split('\n')[0].trim()

    // 移除markdown标记
    const cleanLine = firstLine.replace(/^#+\s*/, '').replace(/^[-*+]\s*/, '').replace(/^\d+\.\s*/, '')

    // 如果第一行很短且像标题，直接使用
    if (cleanLine.length <= 30 && !cleanLine.includes('。') && !cleanLine.includes('.')) {
      return cleanLine
    }

    // 否则取前15个字符作为标题
    return cleanLine.substring(0, 15) + (cleanLine.length > 15 ? '...' : '')
  }

  /**
   * 提取所有标题
   */
  extractTitles(content) {
    const titles = []
    const lines = content.split('\n')

    lines.forEach(line => {
      const trimmedLine = line.trim()

      // 检测各种标题格式
      const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/)
      const listMatch = trimmedLine.match(/^[-*+]\s+(.+)$/)
      const numberedMatch = trimmedLine.match(/^\d+\.\s+(.+)$/)
      const colonMatch = trimmedLine.match(/^([^：:]+)[：:]\s*(.*)$/)

      if (headerMatch) {
        titles.push(headerMatch[2])
      } else if (listMatch) {
        titles.push(listMatch[1])
      } else if (numberedMatch) {
        titles.push(numberedMatch[1])
      } else if (colonMatch) {
        titles.push(colonMatch[1])
      }
    })

    // 如果没有找到标题，按段落提取
    if (titles.length === 0) {
      const paragraphs = this.splitByParagraphs(content)
      paragraphs.forEach(paragraph => {
        const title = this.extractTitleFromText(paragraph)
        if (title) {
          titles.push(title)
        }
      })
    }

    return titles
  }

  /**
   * 从段落中提取标题
   */
  extractTitleFromParagraph(paragraph) {
    return this.extractTitleFromText(paragraph)
  }

  /**
   * 将内容分割为多个部分 - 已废弃，使用parseContentDirectly替代
   */
  splitContentIntoParts(content) {
    if (!content || content.trim().length < 50) {
      console.log('内容较短，不分割:', content.substring(0, 30))
      // 内容较短时，直接返回内容，不创建额外的标题
      return []
    }

    console.log('开始分割内容，原始长度:', content.length)

    // 按段落分割（支持多种换行格式）
    const paragraphs = content
      .split(/\n\s*\n/) // 双换行分割
      .map(p => p.trim())
      .filter(p => p.length > 0)

    console.log('分割后段落数:', paragraphs.length)

    if (paragraphs.length <= 1) {
      // 尝试按句号分割
      const sentences = content.split(/[。！？.!?]\s*/).filter(s => s.trim().length > 10)
      if (sentences.length > 1) {
        console.log('按句子分割，句子数:', sentences.length)
        return sentences.map((sentence, index) => ({
          title: `要点 ${index + 1}`,
          content: sentence.trim() + (sentence.match(/[。！？.!?]$/) ? '' : '。')
        }))
      }

      // 无法有效分割时，直接返回空数组，让内容保留在父节点
      return []
    }

    // 如果段落较多，尝试智能分组
    if (paragraphs.length > 4) {
      const parts = []
      const groupSize = Math.ceil(paragraphs.length / 3) // 分成3组

      for (let i = 0; i < paragraphs.length; i += groupSize) {
        const group = paragraphs.slice(i, i + groupSize)
        const groupContent = group.join('\n\n')
        parts.push({
          title: `要点 ${Math.floor(i / groupSize) + 1}`,
          content: groupContent
        })
        console.log(`创建分组 ${Math.floor(i / groupSize) + 1}，内容长度: ${groupContent.length}`)
      }

      return parts
    } else {
      // 段落较少，每个段落一个部分
      const parts = paragraphs.map((paragraph, index) => ({
        title: `要点 ${index + 1}`,
        content: paragraph.trim()
      }))

      console.log('每段落一个要点，共', parts.length, '个要点')
      return parts
    }
  }

  /**
   * 解析大纲内容
   */
  parseOutlineContent(content) {
    return this.markdownParser.parseToHierarchy(content)
  }

  /**
   * 根据ID查找节点
   */
  findNodeById(data, targetId) {
    if (data.id === targetId) {
      return data
    }
    
    if (data.children) {
      for (const child of data.children) {
        const result = this.findNodeById(child, targetId)
        if (result) return result
      }
    }
    
    return null
  }

  /**
   * 查找父节点
   */
  findParentNode(data, targetId) {
    if (data.children) {
      for (const child of data.children) {
        if (child.id === targetId) {
          return data
        }
        const result = this.findParentNode(child, targetId)
        if (result) return result
      }
    }
    
    return null
  }

  /**
   * 深度克隆对象
   */
  deepClone(obj) {
    return JSON.parse(JSON.stringify(obj))
  }

  /**
   * 内容预处理 - 提高解析质量
   */
  preprocessContent(content) {
    if (!content) return content

    return content
      // 清理多余的空行
      .replace(/\n{3,}/g, '\n\n')
      // 移除行首的序号
      .replace(/^\d+[\.\)]\s*/gm, '')
      // 清理特殊字符
      .replace(/[【】「」]/g, '')
      // 移除开头的客套话
      .replace(/^(好的|当然|让我)[^。\n]*[。\n]?/gm, '')
      .trim()
  }

  /**
   * 智能主题提取
   */
  intelligentTopicExtraction(content, mode) {
    console.log('使用智能提取算法')

    const sentences = content.split(/[。！？\n]/).filter(s => s.trim().length > 5)
    const nodes = []
    const nodeStyle = this.getNodeStyle(mode)

    // 使用关键词密度和语义分析提取主题
    const topics = this.extractTopicsFromSentences(sentences)

    topics.forEach(topic => {
      const node = {
        id: createUid(),
        title: topic.title,
        content: topic.content,
        children: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        aiGenerated: true,
        aiMode: mode,
        style: nodeStyle,
        confidence: topic.confidence
      }

      if (topic.confidence >= 0.6) {
        nodes.push(node)
      }
    })

    return nodes
  }

  /**
   * 清理标题
   */
  cleanTitle(title) {
    return title
      .replace(/^[#\s]*/, '')
      .replace(/[：:]\s*$/, '')
      .replace(/^\d+[\.\)]\s*/, '')
      .trim()
  }

  /**
   * 清理内容
   */
  cleanContent(content) {
    return content
      .replace(/^[#\s]*/, '')
      .replace(/详细描述|具体说明|进一步完善/g, '')
      .trim()
  }

  /**
   * 评估节点质量
   */
  assessNodeQuality(title, content) {
    const issues = []
    let score = 100

    if (!title || title.length < 2) {
      issues.push('标题过短')
      score -= 30
    }

    if (title.length > 50) {
      issues.push('标题过长')
      score -= 20
    }

    if (content && content.length > 200) {
      issues.push('内容过长')
      score -= 10
    }

    // 检查是否包含无意义内容
    const fluffPatterns = [
      /详细描述|具体说明|进一步完善/,
      /仅供参考|根据实际情况/,
      /请注意|需要注意/
    ]

    fluffPatterns.forEach(pattern => {
      if (pattern.test(title) || pattern.test(content)) {
        issues.push('包含无意义内容')
        score -= 15
      }
    })

    return {
      score: Math.max(0, score),
      issues: issues
    }
  }

  /**
   * 去重节点
   */
  deduplicateNodes(nodes) {
    const seen = new Set()
    return nodes.filter(node => {
      const key = node.title.toLowerCase().trim()
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  /**
   * 从句子中提取主题
   */
  extractTopicsFromSentences(sentences) {
    const topics = []
    const keywords = new Map()

    // 分析关键词频率
    sentences.forEach(sentence => {
      const words = sentence.match(/[\u4e00-\u9fa5]{2,}/g) || []
      words.forEach(word => {
        keywords.set(word, (keywords.get(word) || 0) + 1)
      })
    })

    // 根据关键词聚类句子
    const clusters = this.clusterSentencesByKeywords(sentences, keywords)

    clusters.forEach((cluster, index) => {
      if (cluster.sentences.length > 0) {
        const title = this.generateTopicTitle(cluster.keywords)
        const content = cluster.sentences.join('。')

        topics.push({
          title: title,
          content: content,
          confidence: cluster.confidence
        })
      }
    })

    return topics
  }

  /**
   * 按关键词聚类句子
   */
  clusterSentencesByKeywords(sentences, keywords) {
    // 简化的聚类算法
    const clusters = []
    const topKeywords = Array.from(keywords.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([word]) => word)

    topKeywords.forEach(keyword => {
      const relatedSentences = sentences.filter(sentence =>
        sentence.includes(keyword)
      )

      if (relatedSentences.length > 0) {
        clusters.push({
          keywords: [keyword],
          sentences: relatedSentences,
          confidence: Math.min(0.9, relatedSentences.length / sentences.length * 2)
        })
      }
    })

    return clusters
  }

  /**
   * 生成主题标题
   */
  generateTopicTitle(keywords) {
    if (keywords.length === 0) return '未命名主题'

    const mainKeyword = keywords[0]
    const prefixes = ['关于', '基于', '针对', '']
    const suffixes = ['方案', '策略', '方法', '要点', '']

    // 随机选择前缀和后缀，但保持简洁
    const prefix = Math.random() > 0.7 ? prefixes[Math.floor(Math.random() * prefixes.length)] : ''
    const suffix = Math.random() > 0.5 ? suffixes[Math.floor(Math.random() * suffixes.length)] : ''

    return `${prefix}${mainKeyword}${suffix}`.trim()
  }
}

/**
 * 增强的Markdown解析器
 */
class MarkdownParser {
  /**
   * 解析Markdown为章节数组（完整提取标题和内容）
   */
  parseToSections(markdown) {
    const lines = markdown.split('\n')
    const sections = []
    let currentSection = null
    let currentSubSection = null

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const trimmedLine = line.trim()

      // 跳过空行
      if (!trimmedLine) continue

      // 检测标题级别
      const headerLevel = this.getHeaderLevel(trimmedLine)

      if (headerLevel === 2) {
        // 二级标题 - 主要章节
        if (currentSection) {
          sections.push(currentSection)
        }

        currentSection = {
          title: this.extractTitle(trimmedLine, 2),
          content: '',
          children: [],
          rawContent: '' // 保存原始内容用于调试
        }
        currentSubSection = null

        // 收集该标题下的所有内容
        const sectionContent = this.collectSectionContent(lines, i + 1, 2)
        currentSection.content = sectionContent.content
        currentSection.rawContent = sectionContent.raw

      } else if (headerLevel === 3 && currentSection) {
        // 三级标题 - 子章节
        currentSubSection = {
          title: this.extractTitle(trimmedLine, 3),
          content: '',
          rawContent: ''
        }

        // 收集子章节内容
        const subContent = this.collectSectionContent(lines, i + 1, 3)
        currentSubSection.content = subContent.content
        currentSubSection.rawContent = subContent.raw

        currentSection.children.push(currentSubSection)

      } else if (headerLevel === 1) {
        // 一级标题 - 通常是文档标题，跳过
        continue
      }
    }

    // 添加最后一个章节
    if (currentSection) {
      sections.push(currentSection)
    }

    // 过滤掉空章节
    return sections.filter(section =>
      section.title && (section.content.trim() || section.children.length > 0)
    )
  }

  /**
   * 收集章节内容（直到下一个同级或更高级标题）
   */
  collectSectionContent(lines, startIndex, currentLevel) {
    const contentLines = []
    const rawLines = []

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i]
      const trimmedLine = line.trim()

      // 如果遇到同级或更高级的标题，停止收集
      const headerLevel = this.getHeaderLevel(trimmedLine)
      if (headerLevel > 0 && headerLevel <= currentLevel) {
        break
      }

      rawLines.push(line)

      // 跳过空行和子标题行（子标题会单独处理）
      if (!trimmedLine || (headerLevel > currentLevel && headerLevel > 0)) {
        continue
      }

      // 处理列表项
      if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ') ||
          trimmedLine.match(/^\d+\.\s/)) {
        contentLines.push(trimmedLine)
      } else if (trimmedLine) {
        contentLines.push(trimmedLine)
      }
    }

    return {
      content: contentLines.join('\n').trim(),
      raw: rawLines.join('\n').trim()
    }
  }

  /**
   * 提取标题文本
   */
  extractTitle(line, level) {
    const prefix = '#'.repeat(level) + ' '
    return line.startsWith(prefix) ? line.substring(prefix.length).trim() : line.trim()
  }

  /**
   * 获取标题级别
   */
  getHeaderLevel(line) {
    const match = line.match(/^(#+)\s/)
    return match ? match[1].length : 0
  }

  /**
   * 智能内容清理
   */
  cleanContent(content) {
    return content
      .replace(/^\s*[-*]\s*/gm, '') // 移除列表标记
      .replace(/\n{3,}/g, '\n\n') // 合并多余空行
      .replace(/^\s+|\s+$/g, '') // 移除首尾空白
      .trim()
  }

  /**
   * 解析为层级结构
   */
  parseToHierarchy(markdown) {
    const lines = markdown.split('\n')
    const hierarchy = []
    const stack = []
    
    for (const line of lines) {
      const trimmedLine = line.trim()
      if (!trimmedLine) continue
      
      const level = this.getHeaderLevel(trimmedLine)
      if (level > 0) {
        const title = trimmedLine.substring(level + 1).trim()
        const node = {
          id: createUid(),
          title,
          content: '',
          children: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          aiGenerated: true
        }
        
        // 调整栈深度
        while (stack.length >= level) {
          stack.pop()
        }
        
        if (stack.length === 0) {
          hierarchy.push(node)
        } else {
          const parent = stack[stack.length - 1]
          if (!parent.children) parent.children = []
          parent.children.push(node)
        }
        
        stack.push(node)
      } else if (stack.length > 0) {
        // 内容行
        const currentNode = stack[stack.length - 1]
        currentNode.content += (currentNode.content ? '\n' : '') + trimmedLine
      }
    }
    
    return hierarchy
  }

  /**
   * 获取标题级别
   */
  getHeaderLevel(line) {
    const match = line.match(/^(#+)\s/)
    return match ? match[1].length : 0
  }

}

// 创建全局实例
export const aiContentProcessor = new AIContentProcessor()
