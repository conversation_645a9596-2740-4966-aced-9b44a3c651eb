// 创作辅助配置
export const storyHelperConfig = {
  // 卷级规划工具
  volumePlanner: {
    // 卷级模板
    templates: [
      {
        name: '引子卷',
        description: '介绍主角背景，埋下故事种子',
        keyElements: [
          '主角身世介绍',
          '世界观铺垫',
          '初始矛盾引入',
          '关键人物登场'
        ],
        structure: [
          '平静生活',
          '意外事件',
          '改变契机',
          '踏上征程'
        ],
        wordCount: '15-20万字',
        tips: [
          '不要急于展开所有设定',
          '为后续发展留下伏笔',
          '设置小型冲突吸引读者'
        ]
      },
      {
        name: '崛起卷',
        description: '主角实力和地位的首次提升',
        keyElements: [
          '初始机缘获得',
          '能力体系建立',
          '对手与盟友',
          '小规模冲突'
        ],
        structure: [
          '适应新环境',
          '获得机缘',
          '实力提升',
          '初步胜利'
        ],
        wordCount: '25-30万字',
        tips: [
          '合理安排提升节奏',
          '通过对手衬托成长',
          '增加情感线铺垫'
        ]
      },
      {
        name: '转折卷',
        description: '故事走向发生重大转变',
        keyElements: [
          '重大危机出现',
          '身世之谜揭露',
          '敌对势力登场',
          '重要人物牺牲'
        ],
        structure: [
          '平稳发展',
          '意外变故',
          '绝境危机',
          '寻求突破'
        ],
        wordCount: '30-35万字',
        tips: [
          '转折要有充分铺垫',
          '注意情感渲染',
          '为新篇章做准备'
        ]
      }
    ],
    
    // 卷级检查项
    checkPoints: {
      '情节连贯性': [
        '前后卷的剧情是否自然衔接',
        '伏笔是否得到合理回收',
        '人物行为是否符合性格'
      ],
      '节奏控制': [
        '是否有明显的高潮点',
        '情节密度是否适中',
        '是否有足够的铺垫'
      ],
      '人物塑造': [
        '性格是否立体丰满',
        '成长是否合理',
        '关系是否有发展'
      ]
    },
    
    // 写作建议
    writingAdvice: {
      '开卷技巧': [
        '设置悬念或冲突',
        '简洁介绍背景',
        '突出主角特点'
      ],
      '收卷技巧': [
        '给出阶段性成果',
        '埋下新的悬念',
        '为下卷做铺垫'
      ],
      '高潮处理': [
        '充分的情感铺垫',
        '细节描写要生动',
        '节奏要有张力'
      ]
    }
  },
  
  // 情感曲线设计
  emotionCurves: [
    {
      title: '高潮型',
      description: '故事从平静开始，逐渐上升到高潮，然后迅速回落。适合短篇故事。',
      pattern: '↗↗↗↘',
      examples: [
        '《gift of magi》',
        '《最后一片叶子》',
      ]
    },
    {
      title: '双峰型',
      description: '故事有两个高潮点，第二个通常比第一个更强烈。适合中长篇。',
      pattern: '↗↘↗↗↘',
      examples: [
        '《红楼梦》（宝玉黛玉重逢/宝玉出家）',
        '《三体》（水滴攻击/智子锁死）',
      ]
    },
    {
      title: '递进型',
      description: '多个小高潮逐渐递进，最后达到最大高潮。适合长篇。',
      pattern: '↗↘↗↘↗↗',
      examples: [
        '《哈利波特》系列',
        '《斗破苍穹》',
      ]
    }
  ],

  // 角色关系矩阵
  characterRelations: [
    {
      title: '师徒',
      dynamics: ['信任与背叛', '传承与创新', '亲情与责任'],
      conflicts: ['理念冲突', '期望差异', '成长超越'],
      examples: [
        '《星球大战》欧比旺与安纳金',
        '《功夫》包租公与周星驰',
      ]
    },
    {
      title: '对手',
      dynamics: ['相互欣赏', '针锋相对', '惺惺相惜'],
      conflicts: ['实力较量', '理念之争', '资源争夺'],
      examples: [
        '《死亡笔记》L与夜神月',
        '《名侦探柯南》柯南与怪盗基德',
      ]
    },
    {
      title: '亲情',
      dynamics: ['血缘羁绊', '责任与自由', '代沟矛盾'],
      conflicts: ['期望与现实', '传统与现代', '独立与依赖'],
      examples: [
        '《父母爱情》',
        '《寻梦环游记》',
      ]
    }
  ],

  // 场景设计指南
  sceneDesign: [
    {
      title: '矛盾激化场',
      description: '用于推动剧情发展的关键场景，需要设置明确的冲突点。',
      elements: ['空间氛围', '人物立场', '矛盾触发点'],
      checkPoints: [
        '场景是否有明确的空间感？',
        '人物的立场是否鲜明？',
        '矛盾点是否突出？'
      ],
      examples: [
        '《三体》面壁者与罗辑的第一次见面',
        '《大话西游》至尊宝与紫霞的城墙诀别',
      ]
    },
    {
      title: '情感升华场',
      description: '用于深化人物情感的场景，需要细腻的环境描写和心理刻画。',
      elements: ['环境烘托', '心理变化', '情感触发点'],
      checkPoints: [
        '环境描写是否契合情感？',
        '心理变化是否自然？',
        '情感升华是否有说服力？'
      ],
      examples: [
        '《白夜行》雪夜中的相遇',
        '《你的名字》彗星划过天际的场景',
      ]
    },
    {
      title: '悬念设置场',
      description: '用于制造悬念和期待的场景，需要巧妙的信息设置和节奏把控。',
      elements: ['信息暗示', '节奏控制', '悬念埋点'],
      checkPoints: [
        '信息暗示是否恰到好处？',
        '节奏把控是否合理？',
        '悬念设置是否有效？'
      ],
      examples: [
        '《盗梦空间》陀螺转动的结尾',
        '《无间道》天台对峙',
      ]
    }
  ],

  // 情节检查清单
  plotChecklist: [
    {
      phase: '开篇',
      items: [
        '是否设置了引人入胜的开场？',
        '主要人物是否得到合理引入？',
        '故事背景是否交代清晰？',
        '核心冲突是否埋下伏笔？'
      ]
    },
    {
      phase: '发展',
      items: [
        '情节推进是否自然？',
        '人物动机是否合理？',
        '冲突升级是否有层次？',
        '悬念设置是否到位？'
      ]
    },
    {
      phase: '高潮',
      items: [
        '核心冲突是否得到充分展现？',
        '人物情感是否达到顶点？',
        '前文伏笔是否得到回应？',
        '转折是否令人信服？'
      ]
    },
    {
      phase: '结局',
      items: [
        '主要矛盾是否得到解决？',
        '人物成长是否明显？',
        '情感共鸣是否达成？',
        '主题是否得到升华？'
      ]
    }
  ]
};
