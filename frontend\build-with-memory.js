#!/usr/bin/env node

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径（ES模块中的__dirname替代方案）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 设置Node.js内存限制
const maxMemory = process.env.NODE_MAX_MEMORY || '8192';

console.log(`正在使用 ${maxMemory}MB 内存限制构建项目...`);

// 构建命令
const vitePath = path.resolve(__dirname, 'node_modules/.bin/vite');
const buildProcess = spawn('node', [
  `--max-old-space-size=${maxMemory}`,
  vitePath,
  'build',
  '--emptyOutDir'
], {
  stdio: 'inherit',
  cwd: __dirname
});

buildProcess.on('close', (code) => {
  if (code === 0) {
    console.log('构建成功完成！');
  } else {
    console.error(`构建失败，退出码: ${code}`);
    process.exit(code);
  }
});

buildProcess.on('error', (error) => {
  console.error('构建过程中发生错误:', error);
  process.exit(1);
});
