const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const { nanoid } = require('../utils/idGenerator');

class FileController {
  constructor(baseDir) {
    this.baseDir = baseDir;
    this.filesDir = path.join(baseDir, 'files');
    this.tempDir = path.join(baseDir, 'temp');
  }

  async initialize() {
    try {
      await fs.ensureDir(this.filesDir);
      await fs.ensureDir(this.tempDir);
      console.log('FileController 初始化完成');
    } catch (error) {
      console.error('FileController 初始化失败:', error);
      throw error;
    }
  }

  // ==================== 基础文件操作 ====================
  async read_file(filePath) {
    try {
      // 安全检查：确保文件路径在允许的目录内
      const safePath = this.validatePath(filePath);
      
      if (!await fs.pathExists(safePath)) {
        return {
          status: 'error',
          message: '文件不存在'
        };
      }

      const content = await fs.readFile(safePath, 'utf8');
      const stats = await fs.stat(safePath);
      
      return {
        status: 'success',
        message: '读取文件成功',
        data: {
          content,
          size: stats.size,
          modified: stats.mtime,
          created: stats.birthtime
        }
      };
    } catch (error) {
      console.error('读取文件失败:', error);
      return {
        status: 'error',
        message: '读取文件失败',
        error: error.message
      };
    }
  }

  async write_file(filePath, content, options = {}) {
    try {
      const safePath = this.validatePath(filePath);
      
      // 确保目录存在
      await fs.ensureDir(path.dirname(safePath));
      
      // 备份现有文件（如果存在且启用备份）
      if (options.backup && await fs.pathExists(safePath)) {
        await this.createBackup(safePath);
      }
      
      // 写入文件
      await fs.writeFile(safePath, content, 'utf8');
      
      const stats = await fs.stat(safePath);
      
      return {
        status: 'success',
        message: '写入文件成功',
        data: {
          path: safePath,
          size: stats.size,
          modified: stats.mtime
        }
      };
    } catch (error) {
      console.error('写入文件失败:', error);
      return {
        status: 'error',
        message: '写入文件失败',
        error: error.message
      };
    }
  }

  async delete_file(filePath) {
    try {
      const safePath = this.validatePath(filePath);
      
      if (!await fs.pathExists(safePath)) {
        return {
          status: 'error',
          message: '文件不存在'
        };
      }

      // 移动到回收站而不是直接删除
      const deletedPath = await this.moveToTrash(safePath);
      
      return {
        status: 'success',
        message: '删除文件成功',
        data: {
          originalPath: safePath,
          deletedPath
        }
      };
    } catch (error) {
      console.error('删除文件失败:', error);
      return {
        status: 'error',
        message: '删除文件失败',
        error: error.message
      };
    }
  }

  async copy_file(sourcePath, targetPath) {
    try {
      const safeSourcePath = this.validatePath(sourcePath);
      const safeTargetPath = this.validatePath(targetPath);
      
      if (!await fs.pathExists(safeSourcePath)) {
        return {
          status: 'error',
          message: '源文件不存在'
        };
      }

      await fs.ensureDir(path.dirname(safeTargetPath));
      await fs.copy(safeSourcePath, safeTargetPath);
      
      return {
        status: 'success',
        message: '复制文件成功',
        data: {
          sourcePath: safeSourcePath,
          targetPath: safeTargetPath
        }
      };
    } catch (error) {
      console.error('复制文件失败:', error);
      return {
        status: 'error',
        message: '复制文件失败',
        error: error.message
      };
    }
  }

  async move_file(sourcePath, targetPath) {
    try {
      const safeSourcePath = this.validatePath(sourcePath);
      const safeTargetPath = this.validatePath(targetPath);
      
      if (!await fs.pathExists(safeSourcePath)) {
        return {
          status: 'error',
          message: '源文件不存在'
        };
      }

      await fs.ensureDir(path.dirname(safeTargetPath));
      await fs.move(safeSourcePath, safeTargetPath);
      
      return {
        status: 'success',
        message: '移动文件成功',
        data: {
          sourcePath: safeSourcePath,
          targetPath: safeTargetPath
        }
      };
    } catch (error) {
      console.error('移动文件失败:', error);
      return {
        status: 'error',
        message: '移动文件失败',
        error: error.message
      };
    }
  }

  // ==================== 目录操作 ====================
  async list_directory(dirPath) {
    try {
      const safePath = this.validatePath(dirPath);
      
      if (!await fs.pathExists(safePath)) {
        return {
          status: 'error',
          message: '目录不存在'
        };
      }

      const items = await fs.readdir(safePath);
      const itemsWithStats = [];
      
      for (const item of items) {
        try {
          const itemPath = path.join(safePath, item);
          const stats = await fs.stat(itemPath);
          
          itemsWithStats.push({
            name: item,
            path: itemPath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime,
            created: stats.birthtime
          });
        } catch (error) {
          console.error(`获取文件信息失败: ${item}`, error);
        }
      }
      
      return {
        status: 'success',
        message: '获取目录列表成功',
        data: itemsWithStats
      };
    } catch (error) {
      console.error('获取目录列表失败:', error);
      return {
        status: 'error',
        message: '获取目录列表失败',
        error: error.message
      };
    }
  }

  async create_directory(dirPath) {
    try {
      const safePath = this.validatePath(dirPath);
      
      await fs.ensureDir(safePath);
      
      return {
        status: 'success',
        message: '创建目录成功',
        data: {
          path: safePath
        }
      };
    } catch (error) {
      console.error('创建目录失败:', error);
      return {
        status: 'error',
        message: '创建目录失败',
        error: error.message
      };
    }
  }

  async delete_directory(dirPath) {
    try {
      const safePath = this.validatePath(dirPath);
      
      if (!await fs.pathExists(safePath)) {
        return {
          status: 'error',
          message: '目录不存在'
        };
      }

      // 移动到回收站
      const deletedPath = await this.moveToTrash(safePath);
      
      return {
        status: 'success',
        message: '删除目录成功',
        data: {
          originalPath: safePath,
          deletedPath
        }
      };
    } catch (error) {
      console.error('删除目录失败:', error);
      return {
        status: 'error',
        message: '删除目录失败',
        error: error.message
      };
    }
  }

  // ==================== 文件搜索 ====================
  async search_files(searchOptions) {
    try {
      const {
        directory = this.baseDir,
        pattern = '*',
        content = '',
        fileTypes = [],
        maxResults = 100
      } = searchOptions;

      const safePath = this.validatePath(directory);
      const results = [];
      
      await this.searchInDirectory(safePath, {
        pattern,
        content,
        fileTypes,
        maxResults,
        results
      });
      
      return {
        status: 'success',
        message: '搜索完成',
        data: results.slice(0, maxResults)
      };
    } catch (error) {
      console.error('搜索文件失败:', error);
      return {
        status: 'error',
        message: '搜索文件失败',
        error: error.message
      };
    }
  }

  async searchInDirectory(dirPath, options) {
    try {
      const items = await fs.readdir(dirPath);
      
      for (const item of items) {
        if (options.results.length >= options.maxResults) {
          break;
        }
        
        const itemPath = path.join(dirPath, item);
        const stats = await fs.stat(itemPath);
        
        if (stats.isDirectory()) {
          await this.searchInDirectory(itemPath, options);
        } else if (stats.isFile()) {
          if (this.matchesSearchCriteria(item, itemPath, options)) {
            options.results.push({
              name: item,
              path: itemPath,
              size: stats.size,
              modified: stats.mtime,
              type: path.extname(item).toLowerCase()
            });
          }
        }
      }
    } catch (error) {
      console.error(`搜索目录失败: ${dirPath}`, error);
    }
  }

  matchesSearchCriteria(fileName, filePath, options) {
    // 文件名模式匹配
    if (options.pattern && options.pattern !== '*') {
      const regex = new RegExp(options.pattern.replace(/\*/g, '.*'), 'i');
      if (!regex.test(fileName)) {
        return false;
      }
    }
    
    // 文件类型过滤
    if (options.fileTypes.length > 0) {
      const ext = path.extname(fileName).toLowerCase();
      if (!options.fileTypes.includes(ext)) {
        return false;
      }
    }
    
    return true;
  }

  // ==================== 文件安全和工具方法 ====================
  validatePath(filePath) {
    // 解析路径
    const resolvedPath = path.resolve(filePath);
    const basePath = path.resolve(this.baseDir);

    // 检查路径是否在基础目录内
    if (!resolvedPath.startsWith(basePath)) {
      // 如果是相对路径，尝试相对于基础目录解析
      const relativePath = path.resolve(basePath, filePath);
      if (!relativePath.startsWith(basePath)) {
        throw new Error('访问路径不被允许');
      }
      return relativePath;
    }

    return resolvedPath;
  }

  async createBackup(filePath) {
    try {
      const backupDir = path.join(this.baseDir, 'backups', 'files');
      await fs.ensureDir(backupDir);

      const fileName = path.basename(filePath);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `${fileName}.${timestamp}.bak`;
      const backupPath = path.join(backupDir, backupFileName);

      await fs.copy(filePath, backupPath);

      return backupPath;
    } catch (error) {
      console.error('创建备份失败:', error);
      throw error;
    }
  }

  async moveToTrash(filePath) {
    try {
      const trashDir = path.join(this.baseDir, 'trash');
      await fs.ensureDir(trashDir);

      const fileName = path.basename(filePath);
      const timestamp = Date.now();
      const trashedFileName = `${fileName}.${timestamp}`;
      const trashedPath = path.join(trashDir, trashedFileName);

      await fs.move(filePath, trashedPath);

      // 记录删除信息
      const trashInfo = {
        originalPath: filePath,
        trashedPath,
        deletedAt: new Date().toISOString(),
        size: (await fs.stat(trashedPath)).size
      };

      const trashInfoFile = path.join(trashDir, `${trashedFileName}.info`);
      await fs.writeJson(trashInfoFile, trashInfo, { spaces: 2 });

      return trashedPath;
    } catch (error) {
      console.error('移动到回收站失败:', error);
      throw error;
    }
  }

  // ==================== 文件哈希和完整性 ====================
  async calculateFileHash(filePath, algorithm = 'md5') {
    try {
      const safePath = this.validatePath(filePath);

      if (!await fs.pathExists(safePath)) {
        throw new Error('文件不存在');
      }

      const hash = crypto.createHash(algorithm);
      const stream = fs.createReadStream(safePath);

      return new Promise((resolve, reject) => {
        stream.on('data', data => hash.update(data));
        stream.on('end', () => resolve(hash.digest('hex')));
        stream.on('error', reject);
      });
    } catch (error) {
      console.error('计算文件哈希失败:', error);
      throw error;
    }
  }

  async verifyFileIntegrity(filePath, expectedHash, algorithm = 'md5') {
    try {
      const actualHash = await this.calculateFileHash(filePath, algorithm);
      return actualHash === expectedHash;
    } catch (error) {
      console.error('验证文件完整性失败:', error);
      return false;
    }
  }

  // ==================== 临时文件管理 ====================
  async createTempFile(content = '', extension = '.tmp') {
    try {
      const tempFileName = `${nanoid()}${extension}`;
      const tempFilePath = path.join(this.tempDir, tempFileName);

      await fs.writeFile(tempFilePath, content, 'utf8');

      return {
        status: 'success',
        message: '创建临时文件成功',
        data: {
          path: tempFilePath,
          name: tempFileName
        }
      };
    } catch (error) {
      console.error('创建临时文件失败:', error);
      return {
        status: 'error',
        message: '创建临时文件失败',
        error: error.message
      };
    }
  }

  async cleanupTempFiles(maxAge = 24 * 60 * 60 * 1000) { // 默认24小时
    try {
      const tempFiles = await fs.readdir(this.tempDir);
      const now = Date.now();
      let cleanedCount = 0;

      for (const file of tempFiles) {
        try {
          const filePath = path.join(this.tempDir, file);
          const stats = await fs.stat(filePath);

          if (now - stats.mtime.getTime() > maxAge) {
            await fs.remove(filePath);
            cleanedCount++;
          }
        } catch (error) {
          console.error(`清理临时文件失败: ${file}`, error);
        }
      }

      return {
        status: 'success',
        message: '清理临时文件完成',
        data: {
          cleanedCount
        }
      };
    } catch (error) {
      console.error('清理临时文件失败:', error);
      return {
        status: 'error',
        message: '清理临时文件失败',
        error: error.message
      };
    }
  }

  // ==================== 文件监控 ====================
  async watchFile(filePath, callback) {
    try {
      const safePath = this.validatePath(filePath);

      if (!await fs.pathExists(safePath)) {
        throw new Error('文件不存在');
      }

      const watcher = fs.watch(safePath, (eventType, filename) => {
        callback({
          eventType,
          filename,
          path: safePath,
          timestamp: new Date().toISOString()
        });
      });

      return watcher;
    } catch (error) {
      console.error('监控文件失败:', error);
      throw error;
    }
  }

  async watchDirectory(dirPath, callback) {
    try {
      const safePath = this.validatePath(dirPath);

      if (!await fs.pathExists(safePath)) {
        throw new Error('目录不存在');
      }

      const watcher = fs.watch(safePath, { recursive: true }, (eventType, filename) => {
        callback({
          eventType,
          filename,
          path: safePath,
          timestamp: new Date().toISOString()
        });
      });

      return watcher;
    } catch (error) {
      console.error('监控目录失败:', error);
      throw error;
    }
  }

  // ==================== 文件压缩和解压 ====================
  async compressFiles(files, outputPath) {
    try {
      // 这里可以集成压缩库，如 archiver
      // 暂时返回占位符实现
      return {
        status: 'success',
        message: '压缩文件功能待实现',
        data: {
          outputPath,
          fileCount: files.length
        }
      };
    } catch (error) {
      console.error('压缩文件失败:', error);
      return {
        status: 'error',
        message: '压缩文件失败',
        error: error.message
      };
    }
  }

  async extractArchive(archivePath, outputDir) {
    try {
      // 这里可以集成解压库
      // 暂时返回占位符实现
      return {
        status: 'success',
        message: '解压文件功能待实现',
        data: {
          archivePath,
          outputDir
        }
      };
    } catch (error) {
      console.error('解压文件失败:', error);
      return {
        status: 'error',
        message: '解压文件失败',
        error: error.message
      };
    }
  }

  // ==================== 清理资源 ====================
  cleanup() {
    // 清理临时文件
    this.cleanupTempFiles().catch(error => {
      console.error('清理临时文件失败:', error);
    });
  }
}

module.exports = FileController;
