const fs = require('fs-extra');
const path = require('path');
const dayjs = require('dayjs');
const { nanoid } = require('../utils/idGenerator');

// 简单的ID生成器，替代nanoid
function generateId() {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

class ProjectController {
  constructor(baseDir) {
    this.baseDir = baseDir;
    this.projectsDir = path.join(baseDir, 'projects');
    this.projectsFile = path.join(this.projectsDir, 'projects.json');
  }

  async initialize() {
    try {
      await fs.ensureDir(this.projectsDir);
      
      // 如果项目文件不存在，创建空的项目列表
      if (!await fs.pathExists(this.projectsFile)) {
        await fs.writeJson(this.projectsFile, [], { spaces: 2 });
      }
      
      console.log('ProjectController 初始化完成');
    } catch (error) {
      console.error('ProjectController 初始化失败:', error);
      throw error;
    }
  }

  async getProjects() {
    try {
      if (!await fs.pathExists(this.projectsFile)) {
        return [];
      }

      const projects = await fs.readJson(this.projectsFile);
      
      // 按最后修改时间排序
      return projects.sort((a, b) => 
        new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt)
      );
    } catch (error) {
      console.error('获取项目列表失败:', error);
      return [];
    }
  }

  async getProject(projectId) {
    try {
      const projects = await this.getProjects();
      return projects.find(p => p.id === projectId) || null;
    } catch (error) {
      console.error('获取项目失败:', error);
      return null;
    }
  }

  async createProject(projectData) {
    try {
      const projects = await this.getProjects();
      
      const newProject = {
        id: nanoid(),
        name: projectData.name || '新项目',
        description: projectData.description || '',
        author: projectData.author || '',
        genre: projectData.genre || '',
        tags: projectData.tags || [],
        status: projectData.status || 'active', // active, archived, completed
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        settings: {
          wordCountTarget: projectData.wordCountTarget || 0,
          dailyWordTarget: projectData.dailyWordTarget || 0,
          theme: projectData.theme || 'default',
          ...projectData.settings
        },
        statistics: {
          totalWords: 0,
          totalChapters: 0,
          totalCharacters: 0,
          lastWriteDate: null,
          writingDays: 0
        }
      };

      projects.push(newProject);
      await fs.writeJson(this.projectsFile, projects, { spaces: 2 });

      // 创建项目专用目录
      const projectDir = path.join(this.projectsDir, newProject.id);
      await fs.ensureDir(projectDir);
      await fs.ensureDir(path.join(projectDir, 'books'));
      await fs.ensureDir(path.join(projectDir, 'characters'));
      await fs.ensureDir(path.join(projectDir, 'settings'));
      await fs.ensureDir(path.join(projectDir, 'timelines'));
      await fs.ensureDir(path.join(projectDir, 'exports'));

      return newProject;
    } catch (error) {
      console.error('创建项目失败:', error);
      throw error;
    }
  }

  async updateProject(projectId, updateData) {
    try {
      const projects = await this.getProjects();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      
      if (projectIndex === -1) {
        throw new Error('项目不存在');
      }

      const existingProject = projects[projectIndex];
      const updatedProject = {
        ...existingProject,
        ...updateData,
        id: projectId, // 确保ID不被修改
        updatedAt: new Date().toISOString(),
        settings: {
          ...existingProject.settings,
          ...updateData.settings
        }
      };

      projects[projectIndex] = updatedProject;
      await fs.writeJson(this.projectsFile, projects, { spaces: 2 });

      return updatedProject;
    } catch (error) {
      console.error('更新项目失败:', error);
      throw error;
    }
  }

  async deleteProject(projectId) {
    try {
      const projects = await this.getProjects();
      const filteredProjects = projects.filter(p => p.id !== projectId);
      
      if (filteredProjects.length === projects.length) {
        throw new Error('项目不存在');
      }

      await fs.writeJson(this.projectsFile, filteredProjects, { spaces: 2 });

      // 删除项目目录（可选，也可以移动到回收站）
      const projectDir = path.join(this.projectsDir, projectId);
      if (await fs.pathExists(projectDir)) {
        // 为了安全，先重命名为删除标记，而不是直接删除
        const deletedDir = path.join(this.projectsDir, `${projectId}_deleted_${Date.now()}`);
        await fs.move(projectDir, deletedDir);
      }

      return true;
    } catch (error) {
      console.error('删除项目失败:', error);
      throw error;
    }
  }

  async archiveProject(projectId) {
    try {
      return await this.updateProject(projectId, { 
        status: 'archived',
        archivedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('归档项目失败:', error);
      throw error;
    }
  }

  async restoreProject(projectId) {
    try {
      const updateData = { 
        status: 'active'
      };
      
      // 移除归档时间
      const project = await this.getProject(projectId);
      if (project && project.archivedAt) {
        delete project.archivedAt;
        Object.assign(updateData, project);
      }

      return await this.updateProject(projectId, updateData);
    } catch (error) {
      console.error('恢复项目失败:', error);
      throw error;
    }
  }

  async updateProjectStatistics(projectId, statistics) {
    try {
      const project = await this.getProject(projectId);
      if (!project) {
        throw new Error('项目不存在');
      }

      const updatedStatistics = {
        ...project.statistics,
        ...statistics,
        lastUpdated: new Date().toISOString()
      };

      return await this.updateProject(projectId, {
        statistics: updatedStatistics
      });
    } catch (error) {
      console.error('更新项目统计失败:', error);
      throw error;
    }
  }

  async getProjectPath(projectId) {
    return path.join(this.projectsDir, projectId);
  }

  async exportProject(projectId, exportPath) {
    try {
      const project = await this.getProject(projectId);
      if (!project) {
        throw new Error('项目不存在');
      }

      const projectDir = path.join(this.projectsDir, projectId);
      const exportData = {
        project,
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
      };

      // 读取项目相关数据
      const booksDir = path.join(projectDir, 'books');
      if (await fs.pathExists(booksDir)) {
        exportData.books = await this.readDirectoryData(booksDir);
      }

      const charactersDir = path.join(projectDir, 'characters');
      if (await fs.pathExists(charactersDir)) {
        exportData.characters = await this.readDirectoryData(charactersDir);
      }

      const settingsDir = path.join(projectDir, 'settings');
      if (await fs.pathExists(settingsDir)) {
        exportData.settings = await this.readDirectoryData(settingsDir);
      }

      const timelinesDir = path.join(projectDir, 'timelines');
      if (await fs.pathExists(timelinesDir)) {
        exportData.timelines = await this.readDirectoryData(timelinesDir);
      }

      // 写入导出文件
      await fs.writeJson(exportPath, exportData, { spaces: 2 });
      
      return exportData;
    } catch (error) {
      console.error('导出项目失败:', error);
      throw error;
    }
  }

  async importProject(importPath) {
    try {
      const importData = await fs.readJson(importPath);
      
      if (!importData.project) {
        throw new Error('无效的项目导入文件');
      }

      // 创建新的项目ID避免冲突
      const newProjectId = nanoid();
      const projectData = {
        ...importData.project,
        id: newProjectId,
        name: `${importData.project.name} (导入)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 创建项目
      const newProject = await this.createProject(projectData);
      const projectDir = path.join(this.projectsDir, newProjectId);

      // 恢复项目数据
      if (importData.books) {
        await this.writeDirectoryData(path.join(projectDir, 'books'), importData.books);
      }

      if (importData.characters) {
        await this.writeDirectoryData(path.join(projectDir, 'characters'), importData.characters);
      }

      if (importData.settings) {
        await this.writeDirectoryData(path.join(projectDir, 'settings'), importData.settings);
      }

      if (importData.timelines) {
        await this.writeDirectoryData(path.join(projectDir, 'timelines'), importData.timelines);
      }

      return newProject;
    } catch (error) {
      console.error('导入项目失败:', error);
      throw error;
    }
  }

  // 辅助方法：读取目录中的所有JSON文件
  async readDirectoryData(dirPath) {
    try {
      const files = await fs.readdir(dirPath);
      const data = {};

      for (const file of files) {
        if (path.extname(file) === '.json') {
          const filePath = path.join(dirPath, file);
          const fileName = path.basename(file, '.json');
          data[fileName] = await fs.readJson(filePath);
        }
      }

      return data;
    } catch (error) {
      console.error('读取目录数据失败:', error);
      return {};
    }
  }

  // 辅助方法：写入目录数据
  async writeDirectoryData(dirPath, data) {
    try {
      await fs.ensureDir(dirPath);

      for (const [fileName, fileData] of Object.entries(data)) {
        const filePath = path.join(dirPath, `${fileName}.json`);
        await fs.writeJson(filePath, fileData, { spaces: 2 });
      }
    } catch (error) {
      console.error('写入目录数据失败:', error);
      throw error;
    }
  }
}

module.exports = ProjectController;
