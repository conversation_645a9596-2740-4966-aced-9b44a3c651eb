export const menuList = [
  {
    title: '首页',
    path: '/dashboard',
    icon: 'House',
    component: () => import('@/views/dashboard/index.vue')
  },
  {
    title: '书籍',
    path: '/book',
    icon: 'Notebook',
    children: [
      {
        title: '写作',
        path: '/book/写作',
        component: () => import('@/views/book/写作.vue')
      },

      {
        title: '设定',
        name: '设定',
        path: '/book/设定/:id/:title',
        component: () => import('@/views/book/设定.vue'),
        hidden: true,
        meta: {
          keepAlive: true
        }
      },
      {
        title: '大纲',
        path: '/book/outline',
        component: () => import('@/views/book/outline.vue')
      },



      {
        title: 'A2B',
        path: '/book/bridge',
        component: () => import('@/views/book/A2B.vue')
      },
      {
        title: '场景卡',
        path: '/book/card',
        component: () => import('@/views/book/SceneCards.vue')
      },

      {
        title: 'Ai提示词',
        path: '/book/aiprompt',
        component: () => import('@/views/book/AiPrompt.vue')
      },
      {
        title: '关系图谱',
        path: '/book/relation',
        component: () => import('@/views/book/RelationShip.vue')
      },
      {
        title: '故事抽卡',
        path: '/book/inspiration',
        component: () => import('@/views/book/StoryInspiration.vue')
      },
      
      {
        title: '剧情抽卡',
        path: '/book/pilot',
        component: () => import('@/views/book/pilot.vue')
      },
      {
        title: '人设抽卡',
        path: '/book/character',
        component: () => import('@/views/book/CharacterInspiration.vue')
      },

      {
        title: '汉语词典',
        path: '/book/dictionary',
        icon: 'ReadOutlined',
        component: () => import('@/views/book/ChineseDictionary.vue'),
        meta: {
          keepAlive: false
        }
      },      {
        title: '自定义卡池',
        path: '/book/custompool',
        component: () => import('@/views/book/CustomPool.vue')
      },
    ]
  },
  {
    title: '大模型',
    path: '/ai',
    icon: 'ChatSquare',
    children: [
      {
        title: '在线',
        path: '/chat/list',
        component: () => import('@/views/chat/chat.vue')
      }
    ]
  },
  {
    title: '万能工具箱',
    path: '/box',
    icon: 'Box',

    children: [

      {
        title: '高级Markdown编辑器',
        path: '/box/advanced-markdown',
        component: () => import('@/views/workers/AdvancedMarkdownEditor.vue')
      }
    ]
  },
  {
    title: '项目',
    path: '/inject_project',
    icon: 'Platform',
    children: [
      {
        title: '项目',
        path: '/inject_project/项目',
        component: () => import('@/views/inject_project/项目.vue')
      },
    ]
  },
  {
    title: '本地化',
    path: '/download',
    icon: 'Download',
    children: [
      {
        title: '阅读器',
        path: '/download/list',
        component: () => import('@/views/tools/阅读器.vue')
      },
      {
        title: '小说下载',
        path: '/download/小说下载',
        component: () => import('@/views/download/小说下载.vue')
      },
      {
        title: '知乎',
        path: '/download/知乎下载',
        component: () => import('@/views/download/知乎下载.vue')
      },
      {
        title: '工作',
        path: '/download/工作',
        component: () => import('@/views/download/工作.vue')
      },
    ]
  },
]