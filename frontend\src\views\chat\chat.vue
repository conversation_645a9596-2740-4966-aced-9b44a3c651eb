<template>
  <div class="chat-layout">
    <!-- Sidebar -->
    <aside class="sidebar" :class="{ 'sidebar-collapsed': isSidebarCollapsed }">
      <div class="sidebar-header">
        <button class="btn new-chat-btn" v-if="!isSidebarCollapsed" @click="newChat">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          <span>新建聊天</span>
        </button>
        <button class="btn toggle-sidebar-btn" @click="toggleSidebar">
          <svg v-if="isSidebarCollapsed" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>
      </div>
      <nav class="chat-list">
        <a
          v-for="chat in chatList"
          :key="chat.id"
          href="#"
          class="chat-list-item"
          :class="{ active: chat.id === currentChatId }"
          @click.prevent="selectChat(chat.id)"
        >
          <span v-if="isSidebarCollapsed" class="chat-item-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
          </span>
          <span v-if="!isSidebarCollapsed" class="chat-item-title">{{ chat.title || 'Untitled Chat' }}</span>
          <button
            v-if="!isSidebarCollapsed"
            class="btn delete-chat-btn"
            @click.stop.prevent="deleteChat(chat.id)"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 6h18"></path>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
              <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            </svg>
          </button>
        </a>
      </nav>
      <div class="sidebar-footer">
        <!-- Settings, profile, etc. -->
        <button v-if="!isSidebarCollapsed" class="btn clear-all-btn" @click="clearAllChats">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 6h18"></path>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
          <span>清空所有聊天</span>
        </button>
      </div>
    </aside>

    <!-- Main Chat Panel -->
    <main class="main-chat-panel">
      <header class="chat-panel-header">
        <h2 class="chat-title">{{ currentChat ? currentChat.title : '新建聊天' }}</h2>
        <div class="chat-controls">
          <!-- Model Selector -->
          <div class="control-group model-selector">
            <label>模型:</label>
            <UniversalSelector
              v-model="selectedModel"
              :options="modelOptions"
              placeholder="请选择模型"
              header-title="选择AI模型"
              :searchable="modelOptions.length > 8"
              max-height="300px"
              @change="onModelChange"
            />
          </div>
          <!-- Role Selector -->
          <div class="control-group role-selector">
            <label>角色:</label>
            <UniversalSelector
              v-model="selectedRoles"
              :options="roleOptions"
              :multiple="true"
              placeholder="默认角色"
              header-title="选择AI角色"
              :searchable="roleOptions.length > 6"
              max-height="280px"
              @change="onRoleChange"
            />
          </div>
          <!-- Memory Toggle -->
          <button class="btn memory-toggle-btn" @click="toggleMemory" :class="{ active: chatMemoryEnabled }">
            <svg v-if="chatMemoryEnabled" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5V7H6V4.5A2.5 2.5 0 0 1 8.5 2h1Z"></path>
              <path d="M15 10.5A2.5 2.5 0 0 1 12.5 13H12v1.5a2.5 2.5 0 0 1-5 0V13h-.5A2.5 2.5 0 0 1 4 10.5v-1A2.5 2.5 0 0 1 6.5 7H7V4.5A2.5 2.5 0 0 1 9.5 2h5A2.5 2.5 0 0 1 17 4.5V7h.5A2.5 2.5 0 0 1 20 9.5v1a2.5 2.5 0 0 1-2.5 2.5H15Z"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
            </svg>
            <span>{{ chatMemoryEnabled ? '记忆模式' : '单次对话' }}</span>
          </button>
        </div>
      </header>

      <div class="message-display-area" ref="messagesContainer">
        <!-- Use MessageBubble component for each message -->
        <template v-for="message in currentMessages">
          <message-bubble
            v-if="message.role === 'user' || message.role === 'assistant'"
            :key="message.id || message.timestamp"
            :content="message.content"
            :isUser="message.role === 'user'"
            :isError="message.isError"
            :timestamp="message.timestamp || Date.now()"
            :selectedModel="selectedModel"
            :reasoning="message.reasoning"
            :reasoningTime="message.reasoningTime"
            @resend="resendMessage(message)"
          />
          <div v-else-if="message.role === 'system'" :key="'system-' + (message.id || message.timestamp)" class="system-message">
            系统提示: {{ message.content }}
          </div>
        </template>
        
        <div v-if="isAiTyping" class="typing-indicator">
          AI 思考中...
        </div>
      </div>

      <footer class="chat-input-area">
        <div class="input-toolbar">
          <!-- File upload, tools, etc. (Future) -->
          <button class="btn tool-btn" title="Upload File (Coming Soon)" disabled>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
            </svg>
          </button>
        </div>
        <div class="main-input-wrapper">
          <textarea
            v-model="userInput"
            placeholder="发送消息... (Ctrl+Enter 换行)"
            @keydown.enter.exact.prevent="sendMessage"
            @keydown.ctrl.enter.exact="userInput += '\n'"
            :disabled="isCurrentChatSending"
          ></textarea>
          <button class="btn send-btn" @click="isCurrentChatSending ? stopChat() : sendMessage()" :disabled="!userInput.trim() && !isCurrentChatSending">
            <svg v-if="!isCurrentChatSending" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m22 2-11 11-4-4-7 7 4 4 7-7 11-11z"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            </svg>
            <span>{{ isCurrentChatSending ? '停止' : '发送' }}</span>
          </button>
        </div>
        <div class="input-footer">
          <span>AI 可能会出错，请核实重要信息。</span>
        </div>
      </footer>
    </main>

    <!-- Element Plus dialogs for confirmations -->
    <el-dialog
      v-model="showDeleteConfirmation"
      title="删除聊天"
      width="400px"
      :show-close="false"
      center
    >
      <span>确定要删除 "{{ chatToDelete ? chatToDelete.title : '这个聊天' }}" 吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDelete">取消</el-button>
          <el-button type="danger" @click="executeDelete">删除</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="showClearAllConfirmation"
      title="清空所有聊天"
      width="400px"
      :show-close="false"
      center
    >
      <span>确定要删除所有聊天记录吗？此操作无法撤销。</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClearAll">取消</el-button>
          <el-button type="danger" @click="executeClearAllChats">清空所有</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick, onUnmounted } from 'vue'
import { useConfigStore } from '@/stores/config'
import { useAIRolesStore } from '@/stores/aiRoles'
import { useAIProvidersStore } from '@/stores/aiProviders'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/base16/tomorrow.css'  // Light theme default
import 'highlight.js/styles/base16/tomorrow-night.css'  // Dark theme
import MessageBubble from '@/components/MessageBubble.vue'
import UniversalSelector from '@/components/UniversalSelector.vue'
import { nanoid } from 'nanoid'

// Import common languages for code highlighting
import 'highlight.js/lib/languages/javascript'
import 'highlight.js/lib/languages/typescript'
import 'highlight.js/lib/languages/python'
import 'highlight.js/lib/languages/java'
import 'highlight.js/lib/languages/go'

// Import stores
const configStore = useConfigStore()
const aiRolesStore = useAIRolesStore()
const aiProvidersStore = useAIProvidersStore()

// State management
const isSidebarCollapsed = ref(false)
const userInput = ref('')
const sendingChats = ref(new Set())  // Set of chat IDs currently sending
const chatList = ref([])
const currentChatId = ref('')
const messagesContainer = ref(null)
const isAiTyping = ref(false)
const chatMemoryEnabled = ref(true) // Default to memory mode enabled
const selectedRoles = ref([])

// Dialog state management
const showDeleteConfirmation = ref(false)
const chatToDelete = ref(null)
const showClearAllConfirmation = ref(false)

// Model management - 使用本地状态而不是 configStore
const selectedModel = ref('')

// Available models from AI providers store (using modelOptions for better display)
const availableModels = computed(() => {
  const options = aiProvidersStore.modelOptions
  return options.map(option => ({
    id: option.uniqueId,  // 使用唯一标识符作为ID
    name: option.label,  // 使用 label 作为显示名称
    providerId: option.providerId,
    providerName: option.providerName,
    uniqueId: option.uniqueId,  // 添加uniqueId字段
    config: option.config  // 添加config字段
  }))
})

// Model options for UniversalSelector
const modelOptions = computed(() => {
  return availableModels.value.map(model => ({
    value: model.id,  // 这里的model.id现在是uniqueId
    label: model.name,
    description: model.providerName ? `提供商: ${model.providerName}` : undefined,
    provider: model.providerName
  }))
})

// Role options for UniversalSelector
const roleOptions = computed(() => {
  return availableRoles.value.map(role => ({
    value: role.id,
    label: role.name || role.id,
    description: role.description
  }))
})

// Get current chat messages
const currentMessages = computed(() => {
  const chat = chatList.value.find(c => c.id === currentChatId.value)
  return chat ? chat.messages : []
})

// Is current chat sending?
const isCurrentChatSending = computed(() => {
  return sendingChats.value.has(currentChatId.value)
})

// Theme-related computed properties
const chatConfig = computed(() => {
  return configStore.chat || {
    fontSize: 14,
    fontFamily: '微软雅黑, sans-serif',
    codeBlockTheme: 'auto',
    chatBackground: '',
    messageMaxWidth: 75,
    userMessageColor: '#f0f7ff',
    aiMessageColor: '#ffffff',
    showTimestamp: false,
    codeHighlightStyle: 'tomorrow',
    customCSS: ''
  }
})

// Setup highlight theme based on current theme
const setupHighlightTheme = () => {
  const isDarkMode = document.documentElement.classList.contains('dark')
  const configTheme = chatConfig.value.codeBlockTheme || 'auto'
  
  let themeToUse = 'tomorrow' // Default light theme
  
  if (configTheme === 'auto') {
    // In auto mode, use system theme
    themeToUse = isDarkMode ? 'tomorrow-night' : 'tomorrow'
  } else if (configTheme === 'dark') {
    // Force dark theme
    themeToUse = 'tomorrow-night'
  } else if (configTheme === 'light') {
    // Force light theme
    themeToUse = 'tomorrow'
  }
  
  console.log('Applying code highlight theme:', themeToUse)
  
  // Apply theme to CSS variables
  document.documentElement.style.setProperty('--code-bg-color', themeToUse === 'tomorrow-night' ? '#1e1e1e' : '#f6f8fa')
  document.documentElement.style.setProperty('--code-text-color', themeToUse === 'tomorrow-night' ? '#e0e0e0' : '#24292e')
  document.documentElement.style.setProperty('--code-border-color', themeToUse === 'tomorrow-night' ? '#333' : '#e8e8e8')
  document.documentElement.style.setProperty('--code-header-bg', themeToUse === 'tomorrow-night' ? '#2d2d2d' : '#f6f8fa')
}

// Toggle sidebar
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

// Selector event handlers
const onModelChange = async (value, option) => {
  console.log('Model changed:', value, option)
  // 更新当前聊天的模型
  const chat = chatList.value.find(c => c.id === currentChatId.value)
  if (chat) {
    chat.model_id = value
    await saveChat(chat)
  }
}

const onRoleChange = async (values, option) => {
  console.log('Roles changed:', values, option)
  // 更新选中的角色
  selectedRoles.value = values

  // 更新当前聊天的角色设置
  const chat = chatList.value.find(c => c.id === currentChatId.value)
  if (chat) {
    chat.roles = values
    await saveChat(chat)

    // 显示角色变更提示
    if (values.length > 0) {
      const roleNames = values.map(roleId => {
        const role = availableRoles.value.find(r => r.id === roleId)
        return role ? (role.name || roleId) : roleId
      }).join(', ')
      console.log(`已选择角色: ${roleNames}`)
    } else {
      console.log('已清除所有角色设定')
    }
  }
}

// Scroll to the bottom of the messages container
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// Show error message
const showError = (error) => {
  console.error(typeof error === 'string' ? error : error.message || 'Operation failed')
}

// Initialize chat list
const initChatList = async () => {
  try {
    const response = await window.pywebview.api.model_controller.get_all_chats()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result && result.status === 'success' && Array.isArray(result.data)) {
      chatList.value = result.data
      
      if (chatList.value.length > 0) {
        const latestChat = chatList.value[0]
        currentChatId.value = latestChat.id
        if (latestChat.model_id) {
          selectedModel.value = latestChat.model_id
        }
      } else {
        await newChat()
      }
    } else {
      throw new Error(result?.message || 'Failed to load chats')
    }
  } catch (error) {
    console.error('Failed to load chats:', error)
    showError(error.message || 'Failed to load chats')
    await newChat()
  }
}

// Create a new chat
const newChat = async () => {
  if (!selectedModel.value) {
    console.warn('Please select a model first')
    return
  }

  const id = nanoid()
  const chat = {
    id,
    title: `New Chat ${chatList.value.length + 1}`,
    name: `New Chat ${chatList.value.length + 1}`, // Keep for compatibility
    messages: [], // We'll add system messages if roles are selected
    model_id: selectedModel.value,
    roles: selectedRoles.value || [], // 保存当前选中的角色
    last_updated: Date.now() / 1000
  }

  try {
    await saveChat(chat)
    chatList.value.push(chat)
    currentChatId.value = id
  } catch (error) {
    console.error('Failed to create chat:', error)
    showError('Failed to create new chat')
  }
}

// Save chat to backend
const saveChat = async (chat) => {
  try {
    // Ensure chat object has title field
    if (!chat.title && chat.name) {
      chat.title = chat.name // For backwards compatibility
    } else if (!chat.title) {
      chat.title = `New Chat ${Date.now()}`
    }
    
    // Call backend API to save chat
    await window.pywebview.api.model_controller.save_chat(chat.id, chat)
    return true
  } catch (error) {
    console.error('Failed to save chat:', error)
    throw error
  }
}

// Select chat
const selectChat = (id) => {
  if (currentChatId.value === id) return
  currentChatId.value = id
  // Find the current chat
  const chat = chatList.value.find(c => c.id === id)

  if (chat) {
    // Update the selected model if the chat has a model_id
    if (chat.model_id) {
      const modelId = chat.model_id
      console.log('切换聊天，模型ID:', modelId)
      console.log('可用模型数量:', availableModels.value.length)
      if (availableModels.value.length > 0) {
        console.log('第一个可用模型:', availableModels.value[0])
      }

      // 检查是否已经是新格式的唯一标识符
      const directMatch = availableModels.value.find(m => m.id === modelId)
      if (directMatch) {
        // 已经是新格式，直接使用
        selectedModel.value = modelId
        console.log('使用新格式模型ID:', modelId)
      } else {
        // 可能是旧格式，尝试转换
        console.log('尝试转换旧格式模型ID:', modelId)
        const matchingModel = availableModels.value.find(m => m.uniqueId && m.uniqueId.endsWith(':' + modelId))
        if (matchingModel) {
          selectedModel.value = matchingModel.id
          // 更新聊天记录中的模型ID为新格式
          chat.model_id = matchingModel.id
          console.log('转换成功:', modelId, '->', matchingModel.id)
        } else {
          // 找不到匹配的模型，使用默认模型
          console.log('找不到匹配模型，使用默认模型')
          if (availableModels.value.length > 0) {
            selectedModel.value = availableModels.value[0].id
            chat.model_id = availableModels.value[0].id
            console.log('使用默认模型:', availableModels.value[0].id)
          } else {
            console.warn('没有可用模型')
          }
        }
      }
    }

    // Update the selected roles if the chat has roles
    if (chat.roles && Array.isArray(chat.roles)) {
      selectedRoles.value = chat.roles
    } else {
      selectedRoles.value = []
    }
  }
}

// Delete chat
const deleteChat = async (id) => {
  chatToDelete.value = chatList.value.find(chat => chat.id === id)
  showDeleteConfirmation.value = true
}

const cancelDelete = () => {
  showDeleteConfirmation.value = false
  chatToDelete.value = null
}

const executeDelete = async () => {
  try {
    if (!chatToDelete.value) return
    
    // Call backend API to delete
    const response = await window.pywebview.api.model_controller.delete_chat(chatToDelete.value.id)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status !== 'success') {
      throw new Error(result.message || 'Delete failed')
    }

    const index = chatList.value.findIndex(chat => chat.id === chatToDelete.value.id)
    if (index === -1) return

    chatList.value.splice(index, 1)
    if (currentChatId.value === chatToDelete.value.id) {
      currentChatId.value = chatList.value[0]?.id || ''
    }

    console.log('Delete successful')
    showDeleteConfirmation.value = false
    chatToDelete.value = null
  } catch (err) {
    console.error('Failed to delete chat:', err)
    showDeleteConfirmation.value = false
    chatToDelete.value = null
  }
}

// Clear all chats
const clearAllChats = () => {
  showClearAllConfirmation.value = true
}

const cancelClearAll = () => {
  showClearAllConfirmation.value = false
}

const executeClearAllChats = async () => {
  try {
    const response = await window.pywebview.api.model_controller.clear_all_chats()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (!result || result.status !== 'success') {
      throw new Error(result?.message || 'Failed to clear chats')
    }

    chatList.value = []
    currentChatId.value = ''
    console.log('Cleared all chats')
    showClearAllConfirmation.value = false
    
    // Create a new chat
    await newChat()
  } catch (error) {
    console.error('Failed to clear chats:', error)
    showError('Failed to clear chats, please try again')
    showClearAllConfirmation.value = false
  }
}

// Toggle memory mode
const toggleMemory = () => {
  chatMemoryEnabled.value = !chatMemoryEnabled.value
  // Optionally: Save user preference to local storage
  localStorage.setItem('chat_memory_enabled', chatMemoryEnabled.value)
  console.log(chatMemoryEnabled.value ? 'Memory mode enabled' : 'Memory mode disabled')
}

// Load AI roles
const loadAIRoles = async () => {
  try {
    await aiRolesStore.loadRoles()
    console.log('Roles loaded successfully:', aiRolesStore.roles)
  } catch (error) {
    console.error('Failed to load AI roles:', error)
  }
}

// Send message
const sendMessage = async () => {
  if (!userInput.value.trim() || isCurrentChatSending.value) return

  // Get current chat
  const chat = chatList.value.find(c => c.id === currentChatId.value)
  if (!chat) return

  // Save current model to chat record
  chat.model_id = selectedModel.value
  
  // Get system prompts if roles are selected
  const systemPrompts = []

  if (selectedRoles.value && selectedRoles.value.length > 0) {
    for (const roleId of selectedRoles.value) {
      const role = aiRolesStore.roles.find(r => r.id === roleId)
      if (role && role.prompt) {
        systemPrompts.push(role.prompt)
      }
    }
  }

  // Create user message
  const userMessage = {
    role: 'user',
    content: userInput.value
  }

  // Build messages array to send
  let messagesToSend = []

  // Determine which messages to send based on memory mode
  if (chatMemoryEnabled.value) {
    // Memory mode: send system messages and all history
    const systemMessages = systemPrompts.length > 0 ? [{
      role: 'system',
      content: systemPrompts.join('\n\n')
    }] : []

    // Create a copy of history messages (excluding system messages)
    const historyMessages = chat.messages
        .filter(msg => msg.role !== 'system')
        .map(msg => ({ role: msg.role, content: msg.content }))

    messagesToSend = [...systemMessages, ...historyMessages, userMessage]
  } else {
    // No memory mode: only send system messages and current user message
    if (systemPrompts.length > 0) {
      messagesToSend.push({
        role: 'system',
        content: systemPrompts.join('\n\n')
      })
    }
    messagesToSend.push(userMessage)
  }

  // Add user message to chat history
  chat.messages.push(userMessage)

  // Update chat time
  chat.last_updated = Date.now() / 1000

  // Clear input
  userInput.value = ''

  // Mark current chat as sending
  sendingChats.value.add(currentChatId.value)
  isAiTyping.value = true

  // Scroll to bottom
  nextTick(() => {
    scrollToBottom()
  })

  try {
    // Save chat
    await saveChat(chat)
    
    // Auto-update title if first message
    await updateTitleFromMessage(currentChatId.value, userMessage.content)

    // 获取选中模型的配置
    const modelConfig = getModelConfig(selectedModel.value)

    // 合并配置：模型配置优先，只有stream强制为true
    const finalConfig = {
      stream: true,  // 强制启用流式输出
      ...modelConfig  // 模型配置（包括temperature, top_p, max_tokens等）
    }

    // Call backend API
    console.log('发送消息到后端，模型ID:', selectedModel.value)
    console.log('模型配置:', finalConfig)
    window.pywebview.api.model_controller.chat(
        currentChatId.value,
        selectedModel.value,
        messagesToSend,
        finalConfig
    )
  } catch (error) {
    sendingChats.value.delete(currentChatId.value)
    isAiTyping.value = false
    console.error('Failed to send message:', error)
    showError(error.message || 'Failed to send message')
  }
}

// Stop chat generation
const stopChat = async () => {
  if (!currentChatId.value || !sendingChats.value.has(currentChatId.value)) return

  try {
    const response = await window.pywebview.api.model_controller.stop_chat(currentChatId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      sendingChats.value.delete(currentChatId.value)
      isAiTyping.value = false
      console.log('Generation stopped')
    } else {
      throw new Error(result.message || 'Stop failed')
    }
  } catch (error) {
    console.error('Failed to stop chat:', error)
    showError(error.message || 'Failed to stop chat')
  } finally {
    sendingChats.value.delete(currentChatId.value)
    isAiTyping.value = false
  }
}

// Auto-update title from first message
const updateTitleFromMessage = async (chatId, content) => {
  try {
    // Get chat data
    const response = await window.pywebview.api.model_controller.get_chat(chatId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      const chatData = result.data
      
      // If first user message and title is default format
      const userMessages = chatData.messages.filter(m => m.role === 'user')
      if (userMessages.length <= 1 && /^New Chat/.test(chatData.title)) {
        // Extract first 20 chars as title
        const newTitle = content.slice(0, 20) + (content.length > 20 ? '...' : '')
        await updateChatTitle(chatId, newTitle)
      }
    }
  } catch (error) {
    console.error('Failed to auto-update title:', error)
  }
}

// Update chat title
const updateChatTitle = async (chatId, newTitle) => {
  try {
    // Get latest chat data
    const response = await window.pywebview.api.model_controller.get_chat(chatId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      const chatData = result.data
      // Update title
      chatData.title = newTitle
      // For compatibility also update name field
      chatData.name = newTitle
      
      // Save updated chat data
      const saveResponse = await window.pywebview.api.model_controller.save_chat(chatId, chatData)
      const saveResult = typeof saveResponse === 'string' ? JSON.parse(saveResponse) : saveResponse
      
      if (saveResult.status === 'success') {
        // Update title in local chat list
        const chat = chatList.value.find(c => c.id === chatId)
        if (chat) {
          chat.title = newTitle
          chat.name = newTitle
        }
        return true
      }
    }
    return false
  } catch (error) {
    console.error('Failed to update chat title:', error)
    return false
  }
}

// Format markdown content
const renderMarkdown = (content) => {
  if (!content) return ''
  
  try {
    const markedOutput = marked.parse(content)
    
    // Clean HTML to prevent script execution
    return sanitizeHTML(markedOutput)
  } catch (error) {
    console.error('Failed to format Markdown:', error)
    return `<pre class="error">${sanitizeHTML(content)}</pre>`
  }
}

// Sanitize HTML
const sanitizeHTML = (html) => {
  if (!html) return ''

  try {
    // Process all <style> tags, add scoped attribute
    html = html.replace(/<style([^>]*)>/gi, '<style$1 scoped>')

    // Generate namespace to prevent style conflicts
    const namespace = `chat-${Date.now()}`

    // Wrap code blocks in namespace div
    html = html.replace(
        /<div class="code-wrapper">/g,
        `<div class="code-wrapper ${namespace}">`
    )

    // Disable inline scripts
    html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        (match) => `<!-- Disabled script: ${match.length} bytes -->`)

    return html
  } catch (e) {
    console.error('HTML sanitization failed:', e)
    return html // Return original HTML to avoid complete disruption
  }
}

// Handle received message chunks
window.receiveChunk = (chunk) => {
  try {
    const decodedBytes = atob(chunk)
    const decodedChunk = new TextDecoder('utf-8').decode(
        new Uint8Array([...decodedBytes].map(char => char.charCodeAt(0)))
    )

    const messageData = JSON.parse(decodedChunk)
    const { chat_id, content, reasoning } = messageData

    // Debug output for reasoning
    if (reasoning) {
      console.log('Received reasoning content:', reasoning)
    }

    // Only process messages for current chat
    if (chat_id === currentChatId.value) {
      const chat = chatList.value.find(c => c.id === chat_id)
      if (!chat) return

      if (!chat.messages) {
        chat.messages = []
      }

      // Check if last message is from AI
      const lastMessage = chat.messages[chat.messages.length - 1]

      if (!lastMessage || lastMessage.role !== 'assistant') {
        // Add new AI message
        const newMessage = {
          role: 'assistant',
          content: content || '',
          reasoningStartTime: reasoning ? Date.now() : null
        }
        if (reasoning) {
          newMessage.reasoning = reasoning
          newMessage.reasoningCollapsed = false
          newMessage.reasoningTime = 'Thinking...'
        }
        chat.messages.push(newMessage)
      } else {
        // Update last message
        if (content) lastMessage.content += content
        if (reasoning) {
          // If first reasoning content, record start time
          if (!lastMessage.reasoningStartTime) {
            lastMessage.reasoningStartTime = Date.now()
          }
          if (!lastMessage.reasoning) lastMessage.reasoning = ''
          // Handle escape characters
          const formattedReasoning = reasoning.replace(/\\n/g, '\n')
          lastMessage.reasoning += formattedReasoning

          if (!('reasoningCollapsed' in lastMessage)) {
            lastMessage.reasoningCollapsed = false
            lastMessage.reasoningTime = 'Thinking...'
          }
        }
      }

      // Scroll to bottom
      nextTick(() => {
        scrollToBottom()
      })

      isAiTyping.value = false
    }
  } catch (error) {
    console.error('Failed to process message chunk:', error)
  }
}

// Handle message completion
window.onMessageComplete = (chat_id) => {
  if (chat_id === currentChatId.value) {
    sendingChats.value.delete(chat_id)
    isAiTyping.value = false

    // Save chat
    const chat = chatList.value.find(c => c.id === chat_id)
    if (chat && chat.messages.length > 0) {
      const lastMessage = chat.messages[chat.messages.length - 1]
      if (lastMessage && lastMessage.reasoning && lastMessage.reasoningStartTime) {
        // Calculate reasoning time (ms)
        const reasoningDuration = Date.now() - lastMessage.reasoningStartTime
        // Convert to seconds with 1 decimal place
        const seconds = (reasoningDuration / 1000).toFixed(1)
        lastMessage.reasoningTime = `for ${seconds}s`
        // Clean up temp timestamp
        delete lastMessage.reasoningStartTime
      }
      saveChat(chat).catch(error => {
        console.error('Failed to save chat:', error)
      })
    }
  }
}

// Handle error messages
window.receiveChatError = (chunk) => {
  try {
    const decodedBytes = atob(chunk)
    const decodedChunk = new TextDecoder('utf-8').decode(
        new Uint8Array([...decodedBytes].map(char => char.charCodeAt(0)))
    )

    const errorData = JSON.parse(decodedChunk)
    const { chat_id, error_message } = errorData

    console.log('接收到聊天错误消息:', chat_id, error_message);
    
    // Always show error message to user
    import('element-plus').then(({ ElMessage }) => {
      ElMessage.error({
        message: `AI回复失败: ${error_message}`,
        duration: 5000
      });
    });
    
    // Handle errors for current chat
    if (chat_id === currentChatId.value) {
      // Reset sending state
      sendingChats.value.delete(chat_id)
      isAiTyping.value = false
      
      // Find current chat
      const chat = chatList.value.find(c => c.id === chat_id)
      if (chat && chat.messages.length > 0) {
        // If last message is empty AI message, convert it to error message
        const lastMessage = chat.messages[chat.messages.length - 1]
        if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.content.trim()) {
          lastMessage.content = `[Error: ${error_message}]`
          lastMessage.isError = true
          
          // Save updated chat
          saveChat(chat).catch(error => {
            console.error('Failed to save chat:', error)
          })
        }
      }
    }
  } catch (error) {
    console.error('Failed to process error message:', error)
  }
}

// Initialize on mount
onMounted(async () => {
  // Initialize code highlight
  setupHighlightTheme()

  console.log('Chat.vue: 开始初始化聊天界面...')

  try {
    // 0. 首先加载AI提供商配置(最重要的配置)
    try {
      // 导入并使用aiProvidersStore
      const { useAIProvidersStore } = await import('@/stores/aiProviders')
      const aiProvidersStore = useAIProvidersStore()

      // 检查是否已经初始化，避免重复加载
      if (!aiProvidersStore.initialized) {
        console.log('Chat.vue: 开始加载AI提供商配置...')
        await aiProvidersStore.loadProviders(true) // 强制刷新
        console.log('Chat.vue: AI提供商配置加载完成:', aiProvidersStore.providers.length, '个提供商')
      } else {
        console.log('Chat.vue: AI提供商配置已初始化，跳过加载')
      }
    } catch (providerError) {
      console.error('Chat.vue: 加载AI提供商配置失败:', providerError)
    }

    // 1. 模型列表现在从 AI 提供商配置中获取，无需单独加载
    console.log('Chat.vue: 可用模型数量:', availableModels.value.length)
    if (availableModels.value.length === 0) {
      console.warn('Chat.vue: 没有可用的模型，请检查AI提供商配置')
    } else {
      // 如果没有选中的模型，默认选择第一个可用模型
      if (!selectedModel.value && availableModels.value.length > 0) {
        const firstModel = availableModels.value[0]
        selectedModel.value = firstModel.id
        console.log('Chat.vue: 默认选择模型:', selectedModel.value)
      }
    }

    // 2. 加载AI角色
    try {
      if (!aiRolesStore.roles.length) {
        console.log('Chat.vue: 开始加载AI角色...')
        await aiRolesStore.loadRoles()
        console.log('Chat.vue: AI角色加载完成:', aiRolesStore.roles.length, '个角色')
      } else {
        console.log('Chat.vue: AI角色已加载，跳过加载')
      }
    } catch (roleError) {
      console.error('Chat.vue: 加载AI角色失败:', roleError)
    }

    // 3. 加载聊天历史
    try {
      console.log('Chat.vue: 开始加载聊天历史...')
      await initChatList()
      console.log('Chat.vue: 聊天历史加载完成:', chatList.value.length, '个聊天')
    } catch (chatError) {
      console.error('Chat.vue: 加载聊天历史失败:', chatError)
      await newChat()
    }
  } catch (error) {
    console.error('Chat.vue: 初始化错误:', error)
  }

  // Load memory mode from local storage
  const savedMemoryMode = localStorage.getItem('chat_memory_enabled')
  if (savedMemoryMode !== null) {
    chatMemoryEnabled.value = savedMemoryMode === 'true'
  }
  
  console.log('Chat interface initialized')
})

// Get current chat
const currentChat = computed(() => {
  return chatList.value.find(c => c.id === currentChatId.value)
})

// Get available roles from aiRolesStore
const availableRoles = computed(() => {
  return aiRolesStore.roles.filter(role => role.isEnabled !== false)
})

// 获取模型配置的方法
const getModelConfig = (modelUniqueId) => {
  try {
    // 从aiProvidersStore获取模型配置
    const model = availableModels.value.find(m => m.id === modelUniqueId)
    if (model && model.config) {
      console.log('获取到模型配置:', model.config)
      return model.config
    }

    // 如果没有找到配置，返回默认配置
    console.log('未找到模型配置，使用默认配置')
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  } catch (error) {
    console.error('获取模型配置失败:', error)
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  }
}

// Resend message
const resendMessage = async (message) => {
  if (isCurrentChatSending.value) return;

  // Only resend user messages
  if (message.role !== 'user') return;

  // Get current chat
  const chat = chatList.value.find(c => c.id === currentChatId.value);
  if (!chat) return;
  
  // Save current model to chat record
  chat.model_id = selectedModel.value;

  // Get system prompts
  const systemPrompts = [];

  if (selectedRoles.value && selectedRoles.value.length > 0) {
    for (const roleId of selectedRoles.value) {
      const role = aiRolesStore.roles.find(r => r.id === roleId);
      if (role && role.prompt) {
        systemPrompts.push(role.prompt);
      }
    }
  }

  // Create user message (using resent message content)
  const userMessage = {
    role: 'user',
    content: message.content
  };

  // Build messages array
  let messagesToSend = [];

  // Determine messages to send based on memory mode
  if (chatMemoryEnabled.value) {
    // Memory mode: send all history
    const systemMessages = systemPrompts.length > 0 ? [{
      role: 'system',
      content: systemPrompts.join('\n\n')
    }] : [];

    // Get complete history (excluding system messages)
    const historyMessages = chat.messages
      .filter(msg => msg.role !== 'system')
      .map(msg => ({ role: msg.role, content: msg.content }));

    messagesToSend = [...systemMessages, ...historyMessages];
  } else {
    // Single mode: only system + current user message
    if (systemPrompts.length > 0) {
      messagesToSend.push({
        role: 'system',
        content: systemPrompts.join('\n\n')
      });
    }
    messagesToSend.push(userMessage);
  }

  // Mark as sending
  sendingChats.value.add(currentChatId.value);
  isAiTyping.value = true;

  // Scroll to bottom
  nextTick(() => {
    scrollToBottom();
  });

  try {
    // 获取选中模型的配置
    const modelConfig = getModelConfig(selectedModel.value)

    // 合并配置：模型配置优先，只有stream强制为true
    const finalConfig = {
      stream: true,  // 强制启用流式输出
      ...modelConfig  // 模型配置（包括temperature, top_p, max_tokens等）
    }

    // Call API
    window.pywebview.api.model_controller.chat(
      currentChatId.value,
      selectedModel.value,
      messagesToSend,
      finalConfig
    );
  } catch (error) {
    sendingChats.value.delete(currentChatId.value);
    isAiTyping.value = false;
    console.error('Failed to resend message:', error);
    showError(error.message || 'Failed to resend message');
  }
};


</script>

<style lang="scss" scoped>
.chat-layout {
  display: flex;
  flex: 1;
  height: 100vh; /* 确保占满整个视口高度 */
  overflow: hidden;
  background-color: var(--el-bg-color, #f0f2f5); // Use theme variable with fallback
  color: var(--el-text-color-primary, #333);
  
  user-select: none; // Prevent selection for UI container
}

// Basic styling for buttons
.btn {
  padding: 8px 12px;
  border: 1px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
  background-color: var(--el-bg-color, #fff);
  border-color: var(--el-border-color, #d9d9d9);
  color: var(--el-text-color-regular, #555);
  user-select: none; // Prevent selection for buttons

  &:hover {
    border-color: var(--el-color-primary, #40a9ff);
    color: var(--el-color-primary, #40a9ff);
  }

  &.btn-danger {
    background-color: var(--el-color-danger, #ff4d4f);
    border-color: var(--el-color-danger, #ff4d4f);
    color: white;
    &:hover {
      background-color: var(--el-color-danger-dark-2, #d9363e);
      border-color: var(--el-color-danger-dark-2, #d9363e);
    }
  }
   &.btn-secondary {
    background-color: var(--el-fill-color-light, #f0f0f0);
    border-color: var(--el-border-color, #d9d9d9);
    color: var(--el-text-color-regular, #555);
    &:hover {
      background-color: var(--el-fill-color, #e6e6e6);
      border-color: var(--el-border-color-darker, #b3b3b3);
    }
  }
}

// Sidebar
.sidebar {
  width: 260px;
  min-width: 260px;
  flex-shrink: 0;
  background-color: var(--el-bg-color, #ffffff);
  border-right: 1px solid var(--el-border-color-light, #e8e8e8);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  user-select: none; // Prevent selection for sidebar

  &.sidebar-collapsed {
    width: 60px;
    min-width: 60px;
    .sidebar-header {
      padding: 10px;
      justify-content: center;
    }
    .chat-item-title, .sidebar-footer span, .new-chat-btn span {
      display: none;
    }
    .chat-list-item {
      justify-content: center;
    }
    .delete-chat-btn {
      display: none;
    }
     .clear-all-btn {
      justify-content: center;
    }
  }
}

.sidebar-header {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--el-border-color-light, #e8e8e8);
  min-height: 50px; // Ensure consistent height

  .new-chat-btn {
    flex-grow: 1;
    margin-right: 10px;
    background-color: var(--el-color-primary, #1890ff);
    color: white;
    border-color: var(--el-color-primary, #1890ff);
    &:hover {
      background-color: var(--el-color-primary-light-3, #40a9ff);
      border-color: var(--el-color-primary-light-3, #40a9ff);
    }
  }
  .toggle-sidebar-btn {
    // Styles for toggle button
    border: 1px solid var(--el-border-color-light, #e8e8e8);
    background-color: var(--el-bg-color, #fff);
  }
}

.chat-list {
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px 0;
  max-height: calc(100vh - 120px); /* 确保可以在侧边栏内滚动 */

  .chat-list-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    text-decoration: none;
    color: var(--el-text-color-regular, #555);
    border-bottom: 1px solid var(--el-border-color-light, #f0f0f0);
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 确保内容不溢出 */

    &:hover {
      background-color: var(--el-color-primary-light-9, #e6f7ff);
    }
    &.active {
      background-color: var(--el-color-primary-light-8, #bae7ff);
      color: var(--el-color-primary-dark-2, #096dd9);
      font-weight: 500;
    }
    .chat-item-icon svg { width: 18px; height: 18px; }
    .chat-item-title {
      flex: 1;
      min-width: 0; /* 允许内容收缩 */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0 8px;
    }
    .delete-chat-btn {
      flex-shrink: 0; /* 防止按钮被压缩 */
      background: none;
      border: none;
      color: var(--el-text-color-placeholder, #aaa);
      padding: 4px;
      &:hover { 
        color: var(--el-color-danger, #ff4d4f); 
        background-color: var(--el-color-danger-light-9, #fff1f0); 
      }
    }
  }
}

.sidebar-footer {
  padding: 15px;
  border-top: 1px solid var(--el-border-color-light, #e8e8e8);
  .clear-all-btn {
    width: 100%;
  }
}

// Main Chat Panel
.main-chat-panel {
  flex: 1;
  min-width: 0; /* 允许内容区域在必要时缩小 */
  display: flex;
  flex-direction: column;
  overflow: hidden; // Important for child scrolling
}

.chat-panel-header {
  padding: 12px 20px;
  border-bottom: 1px solid var(--el-border-color-light, #e8e8e8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--el-bg-color, #fff);
  min-height: 60px;
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  position: relative;
  z-index: 10;
  user-select: none; // Prevent selection for header

  .chat-title {
    font-size: 1.15em;
    font-weight: 600;
    margin: 0;
    color: var(--el-text-color-primary, #333);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 250px;
  }
  
  .chat-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    
    .control-group {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 120px;
      
      label { 
        font-size: 0.9em; 
        color: var(--el-text-color-secondary, #777);
        white-space: nowrap;
        font-weight: 500;
      }
      
      select {
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid var(--el-border-color, #d9d9d9);
        background-color: var(--el-bg-color, #fff);
        color: var(--el-text-color-primary, #333);
        font-size: 0.9em;
        width: 100%;
        min-width: 120px;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23606266' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 12px center;
        cursor: pointer;
        transition: all 0.2s ease;

        // 限制下拉选项的最大高度
        option {
          padding: 8px 12px;
          background-color: var(--el-bg-color, #fff);
          color: var(--el-text-color-primary, #333);

          &:hover {
            background-color: var(--el-color-primary-light-9, #ecf5ff);
          }

          &:checked {
            background-color: var(--el-color-primary, #409eff);
            color: #fff;
          }
        }

        &:hover {
          border-color: var(--el-color-primary-light-5, #69b1ff);
        }

        &:focus {
          border-color: var(--el-color-primary, #409eff);
          outline: none;
          box-shadow: 0 0 0 2px var(--el-color-primary-light-8, #c6e2ff);
        }

        &:disabled {
          background-color: var(--el-fill-color-light, #f5f5f5);
          cursor: not-allowed;
        }
      }
    }

    .role-selector {
      min-width: 150px;
    }
    
    .memory-toggle-btn {
      background-color: var(--el-bg-color, #fff);
      border-color: var(--el-border-color, #d9d9d9);
      color: var(--el-text-color-regular, #555);
      padding: 8px 16px;
      border-radius: 6px;
      transition: all 0.25s cubic-bezier(0.3, 0, 0.2, 1);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.05);
        transform: translateY(100%);
        transition: transform 0.25s cubic-bezier(0.3, 0, 0.2, 1);
      }
      
      &:hover {
        border-color: var(--el-border-color-darker, #c0c4cc);
        
        &::before {
          transform: translateY(0);
        }
      }
      
      &.active {
        background-color: var(--el-color-success, #67c23a);
      color: white;
        border-color: var(--el-color-success, #67c23a);
        
        &:hover { 
          background-color: var(--el-color-success-dark-2, #529b2e);
        }
      }
      
      svg {
        margin-right: 6px;
      }
    }
  }
}

// System message styling
.system-message {
  align-self: center;
  background-color: var(--el-color-info-light-9, #f4f4f5);
  color: var(--el-text-color-secondary, #909399);
  padding: 8px 16px;
  border-radius: 4px;
  margin: 8px 0;
  font-size: 0.9em;
  max-width: 80%;
  text-align: center;
  border: 1px dashed var(--el-border-color, #dcdfe6);
  user-select: text; // Allow selection for system messages
}

.message-display-area {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  background-color: var(--el-bg-color-page, #f0f2f5);
  background-image: 
    radial-gradient(var(--el-border-color-light, rgba(0,0,0,0.02)) 1px, transparent 1px),
    radial-gradient(var(--el-border-color-light, rgba(0,0,0,0.02)) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  scroll-behavior: smooth;
  user-select: text; // Allow selection for message content
  min-height: 100px; /* 确保消息区域有最小高度 */

  .typing-indicator {
    align-self: flex-start;
    font-style: italic;
    color: var(--el-text-color-secondary, #aaa);
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: var(--el-bg-color, rgba(255, 255, 255, 0.8));
    backdrop-filter: blur(8px);
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-left: 10px;
    
    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--el-color-primary, #1890ff);
      opacity: 0.7;
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0% { transform: scale(0.8); opacity: 0.7; }
      50% { transform: scale(1.2); opacity: 1; }
      100% { transform: scale(0.8); opacity: 0.7; }
    }
  }
}

.chat-input-area {
  padding: 15px 20px 20px;
  border-top: 1px solid var(--el-border-color-light, #e8e8e8);
  background-color: var(--el-bg-color, #fdfdfd);
  position: relative;
  z-index: 5;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  user-select: none; // Prevent selection for input area container

  .input-toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
    
    .tool-btn {
      background: none;
      border: 1px solid var(--el-border-color, #d9d9d9);
      padding: 8px;
      border-radius: 6px;
      color: var(--el-text-color-secondary, #909399);
      transition: all 0.2s ease;
      
      &:hover:not(:disabled) {
        background-color: var(--el-fill-color-light, #f5f5f5);
        color: var(--el-text-color-primary, #303133);
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      svg { 
        width: 18px; 
        height: 18px; 
      }
    }
  }

  .main-input-wrapper {
    display: flex;
    gap: 10px;
    
    textarea {
      flex-grow: 1;
      padding: 14px 18px;
      border-radius: 10px;
      border: 1px solid var(--el-border-color, #d9d9d9);
      resize: none;
      min-height: 56px;
      max-height: 180px;
      font-size: 1.2em;
      line-height: 1.6;
      
      background-color: var(--el-bg-color, #fff);
      color: var(--el-text-color-primary, #303133);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      transition: all 0.25s ease;
      user-select: text; // Allow selection for textarea
      letter-spacing: 0.01em;
      
      &:focus {
        border-color: var(--el-color-primary, #40a9ff);
        box-shadow: 0 0 0 2px var(--el-color-primary-light-8, rgba(24, 144, 255, 0.1));
        outline: none;
      }
      
      &:disabled {
        background-color: var(--el-fill-color-light, #f5f5f5);
      }
    }
    
    .send-btn {
      min-height: 56px;
      min-width: 100px;
      background-color: #10b981;
      background-image: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      border: none;
      border-radius: 10px;
      font-weight: 500;
      font-size: 1.05em;
      letter-spacing: 0.3px;
      box-shadow: 0 3px 12px rgba(16, 185, 129, 0.3);
      transition: all 0.25s cubic-bezier(0.3, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
        border-radius: 10px 10px 0 0;
      }
      
      &:hover:not(:disabled) {
        background-image: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(5, 150, 105, 0.4);
      }
      
      &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(5, 150, 105, 0.35);
      }
      
      &:disabled {
        background: #a7f3d0;
        background-image: none;
        cursor: not-allowed;
        box-shadow: none;
        opacity: 0.8;
      }
      
      svg { 
        width: 18px; 
        height: 18px;
        margin-right: 6px;
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
      }

      span {
        font-size: 1.1em;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
      }
    }
  }
  
  .input-footer {
    margin-top: 12px;
    font-size: 0.9em;
    color: var(--el-text-color-secondary, #909399);
    text-align: center;
    opacity: 0.8;
    transition: opacity 0.2s ease;
    user-select: none; // Prevent selection for footer text
    
    &:hover {
      opacity: 1;
    }
  }
}

// SVG icons
svg {
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  width: 16px; // Default size
  height: 16px;
}

// Dark mode specific overrides
:global(.dark) {
  .chat-layout {
    background-color: var(--el-bg-color-overlay, #1d1e1f);
  }
  
  .message-display-area {
    background-color: var(--el-bg-color-page, #141414);
  }
}

// Add style for Element Plus dialog footer
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
  user-select: none; // Prevent selection for dialog footer
}

// Style Element Plus dialogs to fit the app theme
:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.08);
  user-select: none; // Prevent selection for dialog

  .el-dialog__header {
    margin: 0;
    padding: 16px 20px;
    background-color: var(--el-bg-color, #fff);
    border-bottom: 1px solid var(--el-border-color-light, #e8e8e8);

    .el-dialog__title {
      font-size: 1.1em;
      font-weight: 600;
      color: var(--el-text-color-primary, #333);
    }
  }

  .el-dialog__body {
    padding: 24px 20px;
    color: var(--el-text-color-regular, #666);
    font-size: 1em;
    line-height: 1.5;
    user-select: text; // Allow selection for dialog content
  }

  .el-dialog__footer {
    padding: 12px 20px 20px;
    border-top: none;
  }
}
</style>


