// 自定义卡池状态管理
import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'

export const useCustomPoolStore = defineStore('customPool', () => {
  // 状态
  const pools = ref([]) // 所有卡池列表
  const currentPool = ref(null) // 当前选中的卡池
  const isLoading = ref(false) // 加载状态
  const error = ref(null) // 错误信息
  const selectedPoolIds = ref([]) // 选中的卡池ID列表
  const poolDrawCounts = reactive({}) // 每个卡池的抽取数量

  // 计算属性
  const poolCount = computed(() => pools.value.length)
  
  // 获取所有卡池
  async function fetchPools() {
    isLoading.value = true
    error.value = null
    try {
      const response = await window.pywebview.api.get_custom_pools()
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        pools.value = result.data || []
        // 初始化抽取数量
        pools.value.forEach(pool => {
          if (!poolDrawCounts[pool.id]) {
            poolDrawCounts[pool.id] = 1
          }
        })
      } else {
        error.value = result?.message || '获取卡池失败'
        console.error('获取卡池失败:', result?.message)
      }
    } catch (err) {
      error.value = err.message || '获取卡池时发生错误'
      console.error('获取卡池错误:', err)
    } finally {
      isLoading.value = false
    }
  }

  // 保存卡池
  async function savePool(poolData) {
    isLoading.value = true
    error.value = null
    try {
      const response = await window.pywebview.api.save_custom_pool(poolData)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 如果是新增，则添加到列表中
        if (!poolData.id) {
          pools.value.push(result.data)
          // 初始化新卡池的抽取数量
          poolDrawCounts[result.data.id] = 1
        } else {
          // 如果是更新，则替换原有数据
          const index = pools.value.findIndex(p => p.id === result.data.id)
          if (index !== -1) {
            pools.value[index] = result.data
          }
        }
        return result.data
      } else {
        error.value = result?.message || '保存卡池失败'
        console.error('保存卡池失败:', result?.message)
        return null
      }
    } catch (err) {
      error.value = err.message || '保存卡池时发生错误'
      console.error('保存卡池错误:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  // 更新卡池
  async function updatePool(poolId, poolData) {
    isLoading.value = true
    error.value = null
    try {
      const response = await window.pywebview.api.update_custom_pool(poolId, poolData)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 更新本地列表中的数据
        const index = pools.value.findIndex(p => p.id === poolId)
        if (index !== -1) {
          pools.value[index] = result.data
        }
        return result.data
      } else {
        error.value = result?.message || '更新卡池失败'
        console.error('更新卡池失败:', result?.message)
        return null
      }
    } catch (err) {
      error.value = err.message || '更新卡池时发生错误'
      console.error('更新卡池错误:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  // 删除卡池
  async function deletePool(poolId) {
    isLoading.value = true
    error.value = null
    try {
      const response = await window.pywebview.api.delete_custom_pool(poolId)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 从本地列表中移除
        pools.value = pools.value.filter(p => p.id !== poolId)
        // 如果当前选中的就是被删除的卡池，清空当前选中
        if (currentPool.value && currentPool.value.id === poolId) {
          currentPool.value = null
        }
        // 从选中列表中移除
        selectedPoolIds.value = selectedPoolIds.value.filter(id => id !== poolId)
        // 删除抽取数量设置
        delete poolDrawCounts[poolId]
        return true
      } else {
        error.value = result?.message || '删除卡池失败'
        console.error('删除卡池失败:', result?.message)
        return false
      }
    } catch (err) {
      error.value = err.message || '删除卡池时发生错误'
      console.error('删除卡池错误:', err)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 设置当前卡池
  function setCurrentPool(pool) {
    currentPool.value = pool
  }

  // 切换卡池选择状态
  function togglePoolSelection(poolId) {
    const index = selectedPoolIds.value.indexOf(poolId)
    if (index === -1) {
      // 添加到选中列表
      selectedPoolIds.value.push(poolId)
      // 确保有抽取数量设置
      if (!poolDrawCounts[poolId]) {
        poolDrawCounts[poolId] = 1
      }
    } else {
      // 从选中列表移除
      selectedPoolIds.value.splice(index, 1)
    }
  }

  // 设置卡池抽取数量
  function setPoolDrawCount(poolId, count) {
    poolDrawCounts[poolId] = count
  }

  // 获取卡池抽取数量
  function getPoolDrawCount(poolId) {
    return poolDrawCounts[poolId] || 1
  }

  // 从卡池中抽取随机元素
  function drawFromPool(poolId, count = 1) {
    const pool = pools.value.find(p => p.id === poolId)
    if (!pool || !pool.cards || pool.cards.length === 0) {
      return []
    }

    // 获取卡池中的卡片
    const cards = pool.cards
    // 随机抽取指定数量的卡片
    const result = []
    const cardsCopy = [...cards] // 复制一份，避免修改原数组

    for (let i = 0; i < count && cardsCopy.length > 0; i++) {
      const randomIndex = Math.floor(Math.random() * cardsCopy.length)
      result.push(cardsCopy.splice(randomIndex, 1)[0])
    }

    return result
  }

  // 从给定的卡片集合中随机抽取指定数量的卡片
  function drawRandomCards(cards, count = 1) {
    if (!cards || cards.length === 0) {
      return []
    }

    // 随机抽取指定数量的卡片
    const result = []
    const cardsCopy = [...cards] // 复制一份，避免修改原数组

    for (let i = 0; i < count && cardsCopy.length > 0; i++) {
      const randomIndex = Math.floor(Math.random() * cardsCopy.length)
      result.push(cardsCopy.splice(randomIndex, 1)[0])
    }

    return result
  }

  // 创建新卡池
  function createNewPool() {
    return {
      id: null, // 后端会自动生成ID
      name: '新建卡池',
      description: '这是一个新的自定义卡池',
      cards: [], // 卡池中的卡片
      dimensions: [] // 卡片的维度定义
    }
  }

  // 创建新卡片
  function createNewCard(dimensions = []) {
    const card = {
      id: Date.now().toString(), // 前端临时ID
      title: '新卡片',
      description: '',
      properties: {}
    }

    // 根据维度定义初始化属性
    dimensions.forEach(dim => {
      card.properties[dim.key] = dim.defaultValue || ''
    })

    return card
  }

  // 初始化
  function init() {
    fetchPools()
  }

  // 更新卡池抽取数量
  function updatePoolDrawCount(poolId, count) {
    // 找到要更新的卡池
    const pool = pools.value.find(p => p.id === poolId)
    if (pool) {
      // 创建一个副本以避免直接修改原对象
      const updatedPool = { ...pool, drawCount: count }
      // 使用现有的updatePool方法更新卡池
      customPoolStore.updatePool(poolId, updatedPool)
    }
  }

  // 确保有一个savePools方法来持久化存储pools数据
  function savePools() {
    // 根据您现有的存储逻辑实现
    // 例如：localStorage.setItem('custom_pools', JSON.stringify(this.pools))
    // 或者调用API保存到服务器
  }

  // 获取所有卡池
  async function getPools() {
    try {
      // 这里应该调用API获取卡池列表
      // 如果已有现成方法，直接使用即可
      return await this.loadPools() // 或者返回 this.pools
    } catch (error) {
      console.error('Failed to get pools:', error)
      throw error
    }
  }

  // 更新卡池激活状态
  async function updatePoolActive(poolId, active) {
    try {
      // 找到要更新的卡池
      const pool = pools.value.find(p => p.id === poolId)
      if (!pool) {
        throw new Error('卡池不存在')
      }
      
      // 创建更新后的卡池对象
      const updatedPool = { ...pool, active }
      
      // 使用现有的updatePool方法保存更新
      await updatePool(poolId, updatedPool)
      
      // 更新本地状态
      pool.active = active
      
      return true
    } catch (error) {
      console.error('更新卡池激活状态失败:', error)
      throw error
    }
  }

  return {
    // 状态
    pools,
    currentPool,
    selectedPoolIds,
    poolDrawCounts,
    isLoading,
    error,
    
    // 计算属性
    poolCount,
    
    // 方法
    fetchPools,
    savePool,
    updatePool,
    deletePool,
    setCurrentPool,
    togglePoolSelection,
    setPoolDrawCount,
    getPoolDrawCount,
    drawFromPool,
    drawRandomCards,
    createNewPool,
    createNewCard,
    init,
    updatePoolDrawCount,
    savePools,
    getPools,
    updatePoolActive
  }
}) 