<template>
  <div
    ref="mindmapCanvas"
    class="mindmap-canvas"
    :class="{ dark: isDarkTheme }"
  >
    <!-- 工具栏 -->
    <div class="mindmap-toolbar">
      <!-- 左侧：基础操作 -->
      <div class="toolbar-left">
        <el-button-group size="small">
          <el-tooltip content="适应画布" placement="bottom">
            <el-button @click="fitView" :icon="Aim" />
          </el-tooltip>
          <el-tooltip content="放大" placement="bottom">
            <el-button @click="zoomIn" :icon="Plus" />
          </el-tooltip>
          <el-tooltip content="缩小" placement="bottom">
            <el-button @click="zoomOut" :icon="Minus" />
          </el-tooltip>
        </el-button-group>

        <div class="toolbar-divider"></div>

        <el-button-group size="small">
          <el-tooltip content="展开全部" placement="bottom">
            <el-button @click="expandAll" :icon="ArrowDown" />
          </el-tooltip>
          <el-tooltip content="折叠全部" placement="bottom">
            <el-button @click="collapseAll" :icon="ArrowUp" />
          </el-tooltip>
          <el-tooltip content="重置布局" placement="bottom">
            <el-button @click="resetLayout" :icon="Refresh" />
          </el-tooltip>
        </el-button-group>
      </div>

      <!-- 右侧：配置选项 -->
      <div class="toolbar-right">
        <div class="toolbar-item">
          <span class="toolbar-label">布局:</span>
          <select
            v-model="selectedLayout"
            @change="handleLayoutChange"
            class="native-select layout-select"
          >
            <option value="mindMap">思维导图</option>
            <option value="logicalStructure">逻辑结构</option>
            <option value="organizationStructure">组织结构</option>
            <option value="timeline">时间轴</option>
            <option value="fishbone">鱼骨图</option>
          </select>
        </div>

        <div class="toolbar-item">
          <span class="toolbar-label">AI模型:</span>
          <select
            v-model="selectedModelValue"
            class="native-select model-select"
          >
            <option
              v-for="model in availableModels"
              :key="model.id"
              :value="model.id"
            >
              {{ model.name }}
            </option>
          </select>
        </div>

        <div class="toolbar-divider"></div>

        <el-tooltip
          :content="isFullscreen ? '退出全屏' : '进入全屏'"
          placement="bottom"
        >
          <el-button
            @click="toggleFullscreen"
            :icon="isFullscreen ? ScaleToOriginal : FullScreen"
            size="small"
            :type="isFullscreen ? 'primary' : 'default'"
          >
            {{ isFullscreen ? "退出全屏" : "全屏" }}
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 思维导图容器 -->
    <div
      ref="mindmapContainer"
      class="mindmap-container"
      :style="{ fontSize: `${fontSize}px` }"
    ></div>

    <!-- 右键菜单 -->
    <div
      v-show="contextMenu.show"
      class="context-menu"
      :style="{ left: `${contextMenu.x}px`, top: `${contextMenu.y}px` }"
      @click.stop
    >
      <!-- 节点右键菜单 -->
      <template v-if="contextMenu.type === 'node'">
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('INSERT_NODE')"
        >
          <span>插入同级节点</span>
          <span class="shortcut">Enter</span>
        </div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('INSERT_CHILD_NODE')"
        >
          <span>插入子级节点</span>
          <span class="shortcut">Tab</span>
        </div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('INSERT_PARENT_NODE')"
        >
          <span>插入父节点</span>
          <span class="shortcut">Shift + Tab</span>
        </div>
        <div class="menu-divider"></div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('EXPAND_ALL_CHILDREN')"
        >
          <span>展开所有下级节点</span>
        </div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('COLLAPSE_ALL_CHILDREN')"
        >
          <span>收起所有下级节点</span>
        </div>
        <div class="menu-divider"></div>
        <div class="menu-item" @click="handleContextMenuItemClick('UP_NODE')">
          <span>上移节点</span>
          <span class="shortcut">Ctrl + ↑</span>
        </div>
        <div class="menu-item" @click="handleContextMenuItemClick('DOWN_NODE')">
          <span>下移节点</span>
          <span class="shortcut">Ctrl + ↓</span>
        </div>
        <div class="menu-divider"></div>
        <div class="menu-item" @click="handleContextMenuItemClick('COPY_NODE')">
          <span>复制节点</span>
          <span class="shortcut">Ctrl + C</span>
        </div>
        <div class="menu-item" @click="handleContextMenuItemClick('CUT_NODE')">
          <span>剪切节点</span>
          <span class="shortcut">Ctrl + X</span>
        </div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('PASTE_NODE')"
        >
          <span>粘贴节点</span>
          <span class="shortcut">Ctrl + V</span>
        </div>
        <div class="menu-divider"></div>
        <div
          class="menu-item danger"
          @click="handleContextMenuItemClick('REMOVE_NODE')"
        >
          <span>删除节点</span>
          <span class="shortcut">Delete</span>
        </div>
        <!-- <div
          class="menu-item warning"
          @click="handleContextMenuItemClick('REMOVE_ALL_CHILDREN')"
          v-if="hasChildren"
        >
          <span>删除所有子节点</span>
          <span class="shortcut">Shift+Del</span>
        </div> -->
        <div class="menu-divider"></div>
        <div
          class="menu-item ai-item"
          @click="handleContextMenuItemClick('AI_GENERATE')"
        >
          <span>🤖 AI生成</span>
        </div>
        <div
          class="menu-item ai-item"
          @click="handleContextMenuItemClick('AI_EXPAND_CONTENT')"
        >
          <span>📝 AI扩展内容</span>
        </div>
      </template>

      <!-- 画布右键菜单 -->
      <template v-else-if="contextMenu.type === 'canvas'">
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('RETURN_CENTER')"
        >
          <span>回到中心</span>
          <span class="shortcut">Ctrl + Enter</span>
        </div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('EXPAND_ALL')"
        >
          <span>展开全部</span>
        </div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('UNEXPAND_ALL')"
        >
          <span>收起全部</span>
        </div>
        <div class="menu-divider"></div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('PASTE_NODE')"
        >
          <span>粘贴节点</span>
          <span class="shortcut">Ctrl + V</span>
        </div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('SELECT_ALL')"
        >
          <span>全选</span>
          <span class="shortcut">Ctrl + A</span>
        </div>
        <div class="menu-divider"></div>
        <div
          class="menu-item"
          @click="handleContextMenuItemClick('RESET_LAYOUT')"
        >
          <span>重置布局</span>
          <span class="shortcut">Ctrl + L</span>
        </div>
        <div class="menu-item" @click="handleContextMenuItemClick('FIT_VIEW')">
          <span>适应画布</span>
          <span class="shortcut">Ctrl + I</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watch,
  computed,
  nextTick,
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  ArrowDown,
  ArrowUp,
  Plus,
  Minus,
  FullScreen,
  Refresh,
  Aim,
  ScaleToOriginal,
} from "@element-plus/icons-vue";
import MindMap from "simple-mind-map";
import Select from "simple-mind-map/src/plugins/Select.js";
import { nanoid } from "nanoid";
import { ContentProcessor } from "./contentProcessor.js";
// import aiPromptBuilder from './aiPromptBuilder.js' // 暂时注释

// 注册插件
MindMap.usePlugin(Select);

// Props
const props = defineProps({
  mindmapData: {
    type: Object,
    default: null,
  },
  fontSize: {
    type: Number,
    default: 14,
  },
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
  availableModels: {
    type: Array,
    default: () => [],
  },
  selectedModel: {
    type: String,
    default: "",
  },
});

// Emits
const emit = defineEmits([
  "node-click",
  "ai-generate",
  "ai-generate-request",
  "markdown-change",
  "update:selectedModel",
]);

// 响应式数据
const mindmapCanvas = ref(null);
const mindmapContainer = ref(null);
const mindMapInstance = ref(null);
const selectedLayout = ref("mindMap");
const contentProcessor = new ContentProcessor();
const isGenerating = ref(false);
const cleanupTimer = ref(null);
const isNodeOperating = ref(false); // 标记是否正在进行节点操作
const isInternalDataUpdate = ref(false); // 标记是否是内部数据更新，避免循环

// 设置节点操作状态，自动重置
const setNodeOperating = (operating, duration = 1000) => {
  isNodeOperating.value = operating;

  if (operating) {
    // 自动重置操作状态，避免状态卡死
    setTimeout(() => {
      isNodeOperating.value = false;
    }, duration);
  }
};
const resizeObserver = ref(null); // 容器尺寸监听器
const isFullscreen = ref(false); // 全屏状态
const textColorFixTimer = ref(null); // 文字颜色修复定时器

// 右键菜单状态
const contextMenu = ref({
  show: false,
  x: 0,
  y: 0,
  type: "", // 'node' 或 'canvas'
  node: null,
});

// 检查当前右键节点是否有子节点
const hasChildren = computed(() => {
  if (!contextMenu.value.node) return false;

  const node = contextMenu.value.node;

  // 检查多种可能的子节点属性
  const childrenCount =
    (node.children && node.children.length) ||
    (node._children && node._children.length) ||
    (node.nodeData?.children && node.nodeData.children.length) ||
    (node.nodeData?.data?.children && node.nodeData.data.children.length) ||
    0;

  console.log("hasChildren检查:", {
    nodeText: node.nodeData?.data?.text,
    childrenCount,
    hasChildren: childrenCount > 0,
  });

  return childrenCount > 0;
});

// 计算属性
const selectedModelValue = computed({
  get: () => props.selectedModel,
  set: (value) => emit("update:selectedModel", value),
});

// 监听数据变化 - 避免内部操作导致的循环更新
watch(
  () => props.mindmapData,
  (newData) => {
    // 如果是内部数据更新导致的变化，忽略
    if (isInternalDataUpdate.value) {
      isInternalDataUpdate.value = false;
      return;
    }

    if (newData && mindMapInstance.value) {
      console.log("外部数据变化，更新思维导图");

      // 保存当前视图状态，避免数据更新后回到中心
      const currentTransform = mindMapInstance.value.view.getTransformData();
      const currentScale = mindMapInstance.value.view.scale;

      nextTick(() => {
        updateMindmapData(newData);

        // 延迟恢复视图状态
        setTimeout(() => {
          if (mindMapInstance.value) {
            try {
              mindMapInstance.value.view.setTransformData({
                ...currentTransform,
                scale: currentScale,
              });
            } catch (error) {
              console.warn("恢复视图状态失败:", error);
            }
          }
        }, 100);
      });
    }
  },
  { deep: true }
);

// 监听主题变化
watch(
  () => props.isDarkTheme,
  (isDark) => {
    if (mindMapInstance.value) {
      updateTheme(isDark);
    }
  }
);

// 使用Intersection Observer监听组件可见性变化
let intersectionObserver = ref(null);

const handleVisibilityChange = (entries) => {
  const entry = entries[0];
  if (entry.isIntersecting) {
    // 组件变为可见
    if (!mindMapInstance.value && mindmapContainer.value) {
      // 延迟初始化，确保DOM完全准备好
      setTimeout(() => {
        if (checkContainerSize()) {
          initMindmap();
        }
      }, 200);
    }
  }
};

// 监听字体大小变化
watch(
  () => props.fontSize,
  (newSize) => {
    if (mindMapInstance.value) {
      updateFontSize(newSize);
    }
  }
);

// 窗口大小变化处理
const handleResize = () => {
  if (mindMapInstance.value) {
    mindMapInstance.value.resize();
    // 移除清理调用，避免影响视图
  }
};

// 容器尺寸变化处理
const handleContainerResize = () => {
  if (mindMapInstance.value && checkContainerSize()) {
    // 延迟执行，确保DOM更新完成
    setTimeout(() => {
      if (mindMapInstance.value && checkContainerSize()) {
        try {
          mindMapInstance.value.resize();
        } catch (error) {
          console.error("思维导图resize失败:", error);
        }
      }
    }, 100);
  }
};

// 全屏状态变化处理
const handleFullscreenChange = () => {
  const fullscreenElement =
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement;

  isFullscreen.value = !!fullscreenElement;

  // 全屏状态变化时调整思维导图大小
  if (mindMapInstance.value) {
    setTimeout(() => {
      mindMapInstance.value.resize();
    }, 100);
  }
};

// 简化的节点操作执行 - 减少视图刷新
const executeNodeOperationWithStableView = (command, ...args) => {
  if (!mindMapInstance.value) return;

  try {
    // 设置操作状态，但不进行复杂的视图保存和恢复
    isNodeOperating.value = true;

    // 直接执行命令，让simple-mind-map自己处理视图更新
    mindMapInstance.value.execCommand(command, ...args);

    // 简单的操作完成标记
    setTimeout(() => {
      isNodeOperating.value = false;
    }, 100);
  } catch (error) {
    console.error("执行节点操作失败:", error);
    isNodeOperating.value = false;
  }
};

// 清理临时节点
const cleanupTempNodes = () => {
  if (mindmapContainer.value) {
    // 清理可能残留的临时节点元素
    const tempNodes = mindmapContainer.value.querySelectorAll(
      '.smm-temp-node, .smm-node-animation, [class*="temp"], [class*="animation"], .smm-node[style*="position: absolute"]'
    );
    tempNodes.forEach((node) => {
      if (node && node.style) {
        node.style.opacity = "0";
        node.style.pointerEvents = "none";
        setTimeout(() => {
          if (node.parentNode) {
            try {
              node.parentNode.removeChild(node);
            } catch (e) {
              // 忽略已经被移除的节点
            }
          }
        }, 300);
      }
    });

    // 额外清理可能出现在左上角的重复节点
    const allNodes = mindmapContainer.value.querySelectorAll(".smm-node");
    const nodePositions = new Map();

    allNodes.forEach((node) => {
      const rect = node.getBoundingClientRect();
      const position = `${Math.round(rect.left)},${Math.round(rect.top)}`;

      if (nodePositions.has(position)) {
        // 发现重复位置的节点，移除后添加的节点
        // 如果节点在左上角(0,0)附近，很可能是bug节点
        if (rect.left < 50 && rect.top < 50) {
          node.style.opacity = "0";
          node.style.pointerEvents = "none";
          setTimeout(() => {
            if (node.parentNode) {
              try {
                node.parentNode.removeChild(node);
              } catch (e) {
                // 忽略已经被移除的节点
              }
            }
          }, 100);
        }
      } else {
        nodePositions.set(position, node);
      }
    });
  }
};

// 生命周期
onMounted(() => {
  // 使用多重延迟确保DOM完全渲染和容器尺寸准备好
  nextTick(() => {
    setTimeout(() => {
      // 再次确保容器存在且有尺寸
      if (mindmapContainer.value && checkContainerSize()) {
        initMindmap();
      } else {
        // 如果还没准备好，再等待一段时间
        setTimeout(() => {
          initMindmap();
        }, 200);
      }
    }, 50);
  });

  // 添加窗口大小变化监听
  window.addEventListener("resize", handleResize);

  // 添加容器尺寸变化监听
  if (mindmapContainer.value && window.ResizeObserver) {
    resizeObserver.value = new ResizeObserver(handleContainerResize);
    resizeObserver.value.observe(mindmapContainer.value);
  }

  // 添加全屏状态变化监听
  document.addEventListener("fullscreenchange", handleFullscreenChange);
  document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
  document.addEventListener("mozfullscreenchange", handleFullscreenChange);
  document.addEventListener("MSFullscreenChange", handleFullscreenChange);

  // 添加全局点击事件监听，用于隐藏右键菜单
  document.addEventListener("click", hideContextMenu);

  // 移除定期清理定时器，避免影响视图
  // cleanupTimer.value = setInterval(() => {
  //   cleanupTempNodes()
  // }, 2000) // 每2秒清理一次

  // 设置可见性监听器
  if (mindmapCanvas.value && window.IntersectionObserver) {
    intersectionObserver.value = new IntersectionObserver(
      handleVisibilityChange,
      {
        threshold: 0.1, // 当10%的组件可见时触发
      }
    );
    intersectionObserver.value.observe(mindmapCanvas.value);
  }
});

onBeforeUnmount(() => {
  // 移除事件监听
  window.removeEventListener("resize", handleResize);
  document.removeEventListener("click", hideContextMenu);

  // 移除全屏事件监听
  document.removeEventListener("fullscreenchange", handleFullscreenChange);
  document.removeEventListener(
    "webkitfullscreenchange",
    handleFullscreenChange
  );
  document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
  document.removeEventListener("MSFullscreenChange", handleFullscreenChange);

  // 移除容器尺寸监听
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
    resizeObserver.value = null;
  }

  // 移除可见性监听
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
    intersectionObserver.value = null;
  }

  // 清理定时器
  if (cleanupTimer.value) {
    clearInterval(cleanupTimer.value);
    cleanupTimer.value = null;
  }

  // 停止文字颜色监控
  stopTextColorMonitoring();

  // 最后一次清理
  cleanupTempNodes();

  // 清理思维导图实例
  destroyMindmapInstance();
});

// 思维导图操作
const expandAll = () => {
  if (mindMapInstance.value) {
    mindMapInstance.value.execCommand("EXPAND_ALL");
  }
};

const collapseAll = () => {
  if (mindMapInstance.value) {
    mindMapInstance.value.execCommand("UNEXPAND_ALL");
  }
};

const resetLayout = () => {
  if (mindMapInstance.value) {
    mindMapInstance.value.view.reset();
  }
};

const fitView = () => {
  if (mindMapInstance.value) {
    // 先重置视图
    mindMapInstance.value.view.reset();
    // 然后适应视图
    setTimeout(() => {
      if (mindMapInstance.value) {
        mindMapInstance.value.view.fit();
      }
    }, 100);
  }
};

const zoomIn = () => {
  if (mindMapInstance.value) {
    mindMapInstance.value.view.enlarge();
  }
};

const zoomOut = () => {
  if (mindMapInstance.value) {
    mindMapInstance.value.view.narrow();
  }
};

// 全屏切换功能
const toggleFullscreen = async () => {
  try {
    if (!isFullscreen.value) {
      // 进入全屏
      const element = mindmapCanvas.value;
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      } else {
        ElMessage.warning("您的浏览器不支持全屏功能");
        return;
      }
    } else {
      // 退出全屏
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen();
      }
    }
  } catch (error) {
    console.error("全屏切换失败:", error);
    ElMessage.error("全屏切换失败");
  }
};

// 处理原生select的布局变化事件
const handleLayoutChange = (event) => {
  const layout = event.target.value;
  changeLayout(layout);
};

const changeLayout = (layout) => {
  selectedLayout.value = layout;
  if (mindMapInstance.value) {
    // 直接切换布局，让simple-mind-map自己处理视图更新
    mindMapInstance.value.setLayout(layout);
  }
};

// 展开节点的所有下级节点
const expandNodeChildren = (node) => {
  if (!node || !mindMapInstance.value) return;

  try {
    // 递归展开所有子节点
    const expandRecursively = (targetNode) => {
      if (targetNode.children && targetNode.children.length > 0) {
        // 显示子节点
        targetNode.showChildren();
        // 递归展开子节点
        targetNode.children.forEach((child) => {
          expandRecursively(child);
        });
      }
    };

    expandRecursively(node);

    console.log("展开所有下级节点完成");
  } catch (error) {
    console.error("展开节点失败:", error);
  }
};

// 收起节点的所有下级节点
const collapseNodeChildren = (node) => {
  if (!node || !mindMapInstance.value) return;

  try {
    // 递归收起所有子节点
    const collapseRecursively = (targetNode) => {
      if (targetNode.children && targetNode.children.length > 0) {
        // 先递归处理子节点
        targetNode.children.forEach((child) => {
          collapseRecursively(child);
        });
        // 隐藏子节点
        targetNode.hideChildren();
      }
    };

    collapseRecursively(node);

    console.log("收起所有下级节点完成");
  } catch (error) {
    console.error("收起节点失败:", error);
  }
};

// 检查容器尺寸是否准备好
const checkContainerSize = () => {
  if (!mindmapContainer.value) return false;

  try {
    const rect = mindmapContainer.value.getBoundingClientRect();
    // 更宽松的检查条件，允许小尺寸容器
    const isValid = rect.width >= 100 && rect.height >= 100;
    if (!isValid) {
      console.debug("容器尺寸检查:", {
        width: rect.width,
        height: rect.height,
      });
    }
    return isValid;
  } catch (error) {
    console.warn("获取容器尺寸失败:", error);
    return false;
  }
};

// 清理思维导图实例
const destroyMindmapInstance = () => {
  // 停止文字颜色监控
  stopTextColorMonitoring();

  if (mindMapInstance.value) {
    try {
      // 移除所有事件监听器
      mindMapInstance.value.off();
      // 清理DOM中的思维导图内容
      if (mindmapContainer.value) {
        mindmapContainer.value.innerHTML = "";
      }
      // 销毁实例
      mindMapInstance.value.destroy();
      mindMapInstance.value = null;
      console.log("思维导图实例已清理");
    } catch (error) {
      console.error("清理思维导图实例失败:", error);
      // 强制清理
      mindMapInstance.value = null;
      if (mindmapContainer.value) {
        mindmapContainer.value.innerHTML = "";
      }
    }
  }
};

// 思维导图初始化
const initMindmap = () => {
  if (!mindmapContainer.value) {
    console.warn("思维导图容器不存在");
    return;
  }

  // 先清理之前的实例，防止重复创建
  destroyMindmapInstance();

  // 检查容器尺寸
  if (!checkContainerSize()) {
    // 延迟重试，增加更多重试机会
    setTimeout(() => {
      if (checkContainerSize()) {
        initMindmap();
      } else {
        // 再次重试
        setTimeout(() => {
          if (checkContainerSize()) {
            initMindmap();
          } else {
            // 改为警告而不是错误，因为思维导图可能仍然能正常工作
            console.warn("容器尺寸检查失败，但思维导图可能仍能正常显示");
            // 不再强制初始化，避免潜在问题
          }
        }, 200);
      }
    }, 100);
    return;
  }

  try {
    // 准备初始数据 - 严格按照官方文档格式
    let initialData = null;

    // 尝试转换用户数据
    if (props.mindmapData) {
      initialData = convertToSimpleMindMapData(props.mindmapData);
      console.log("用户数据转换结果:", initialData);
    }

    // 如果转换失败或没有数据，使用官方文档的标准格式
    if (!initialData) {
      initialData = {
        data: {
          text: "根节点",
        },
        children: [],
      };
      console.log("使用官方标准默认数据:", initialData);
    }

    console.log("思维导图初始化，容器元素:", mindmapContainer.value);

    // 详细的容器检查
    const containerInfo = {
      exists: !!mindmapContainer.value,
      width: mindmapContainer.value.offsetWidth,
      height: mindmapContainer.value.offsetHeight,
      clientWidth: mindmapContainer.value.clientWidth,
      clientHeight: mindmapContainer.value.clientHeight,
      rect: mindmapContainer.value.getBoundingClientRect(),
      style: {
        display: getComputedStyle(mindmapContainer.value).display,
        visibility: getComputedStyle(mindmapContainer.value).visibility,
        position: getComputedStyle(mindmapContainer.value).position,
      },
    };
    console.log("容器详细信息:", containerInfo);

    // 检查容器是否满足渲染条件
    if (containerInfo.width === 0 || containerInfo.height === 0) {
      console.error("容器尺寸为0，强制设置尺寸");
      // 强制设置容器尺寸
      mindmapContainer.value.style.width = "100%";
      mindmapContainer.value.style.height = "100%";
      mindmapContainer.value.style.minWidth = "800px";
      mindmapContainer.value.style.minHeight = "600px";

      // 重新获取尺寸
      setTimeout(() => {
        console.log("强制设置后的容器尺寸:", {
          width: mindmapContainer.value.offsetWidth,
          height: mindmapContainer.value.offsetHeight,
        });
      }, 100);
    }

    // 创建 simple-mind-map 实例 - 防止自动回到中心
    mindMapInstance.value = new MindMap({
      el: mindmapContainer.value,
      data: initialData,
      layout: selectedLayout.value,
      theme: "default", // 使用固定主题，通过themeConfig自定义
      // 激活状态边框颜色 - 使用通用的高对比度颜色
      hoverRectColor: "rgba(255, 165, 0, 0.9)", // 橙色，在黑白背景下都清晰可见
      hoverRectPadding: 3,

      // 多选节点功能配置
      enableCtrlKeyNodeSelection: true, // 启用Ctrl+左键多选节点
      useLeftKeySelectionRightKeyDrag: false, // 保持默认：左键选择，右键拖动画布

      themeConfig: {
        // https://github.com/wanglin2/mind-map/blob/main/simple-mind-map/src/theme/default.js
        second: {
        },
        node: {},
      },
      // 视图配置
      viewConfig: {
        // 启用缩放
        enableScale: true,
        // 缩放范围
        scaleRange: [0.1, 2],
      },
      // 启用拖拽
      enableFreeDrag: true,
      // 启用节点编辑
      enableNodeRichText: false,
      // 水印配置
      watermarkConfig: {
        show: false,
      },
      // 快捷键配置
      keyboardConfig: {
        enable: true,
      },

      // 节点文本编辑层级
      nodeTextEditZIndex: 1000,
      // 节点备注提示层级
      nodeNoteTooltipZIndex: 1000,
      // 启用键盘自动进入文本编辑
      enableAutoEnterTextEditWhenKeydown: true,
      // 禁用双击回到根节点
      enableDblclickBackToRootNode: false,
      // 移动节点到画布中心时不重置缩放
      resetScaleOnMoveNodeToCenter: false,
      fit: false,
      // 不限制思维导图在画布内
      isLimitMindMapInCanvas: false,
      // 删除节点后不自动激活相邻节点（可能会触发视图调整）
      // 布局配置
      layoutConfig: {
        // 节点间距
        nodeSpacing: 50,
        // 层级间距
        rankSpacing: 100,
        // 第二层节点间距
        secondNodeSpacing: 40,
        // 第二层节点偏移量
        secondNodeOffset: 20,
      },
    });

    console.log("MindMap实例创建完成:", mindMapInstance.value);
    console.log("渲染器:", mindMapInstance.value.renderer);
    console.log("视图:", mindMapInstance.value.view);

    // 验证实例创建
    if (!mindMapInstance.value) {
      console.error("MindMap实例创建失败");
      return;
    }

    if (!mindMapInstance.value.renderer) {
      console.error("思维导图渲染器未正确初始化");
      return;
    }

    console.log("思维导图实例创建成功");

    // 绑定基本事件
    bindMindmapEvents();

    // 添加快捷键
    bindShortcutKeys();

    // 启动文字颜色监控
    startTextColorMonitoring();

    // 如果有数据，渲染思维导图
    if (props.mindmapData) {
      updateMindmapData(props.mindmapData);
    }

    // 移除自动适应视图，避免回到中心
    // setTimeout(() => {
    //   if (mindMapInstance.value) {
    //     mindMapInstance.value.view.fit()
    //   }
    // }, 100)

    // 延迟同步数据，但不适应视图
    setTimeout(() => {
      if (mindMapInstance.value) {
        syncToMarkdown();
      }
    }, 300);

    // 延迟检查，给渲染一些时间
    setTimeout(() => {
      if (!mindMapInstance.value) {
        console.error("思维导图实例丢失");
        return;
      }

      console.log("延迟检查渲染状态");
      const nodes = mindMapInstance.value.renderer?.nodeList || [];
      console.log("延迟检查节点数量:", nodes.length);

      if (nodes.length === 0) {
        console.warn("延迟检查仍然没有节点，等待node_tree_render_end事件");
      } else {
        console.log("延迟检查发现节点已渲染");
      }
    }, 500);
  } catch (error) {
    console.error("初始化思维导图失败:", error);
    console.error("容器信息:", {
      exists: !!mindmapContainer.value,
      rect: mindmapContainer.value
        ? mindmapContainer.value.getBoundingClientRect()
        : null,
    });

    // 如果是容器尺寸问题，尝试重新初始化
    if (error.message && error.message.includes("宽高不能为0")) {
      setTimeout(() => {
        if (checkContainerSize()) {
          initMindmap();
        } else {
          ElMessage.error("思维导图容器尺寸异常，请刷新页面重试");
        }
      }, 1000);
    } else {
      ElMessage.error("初始化思维导图失败: " + error.message);
    }
  }
};

// 简化的文字颜色修复方案 - 移除复杂的DOM操作
const fixActiveNodeTextColor = (node) => {
  // 不再进行复杂的DOM操作，避免group节点查询方法的兼容性问题
  // 改为通过CSS样式来处理选中节点的文字颜色问题
};

// 简化的所有激活节点文字颜色修复
const fixAllActiveNodesTextColor = () => {
  // 移除复杂的DOM操作逻辑，改为通过CSS处理
};

// 简化的文字颜色监控
const startTextColorMonitoring = () => {
  // 移除定时器监控，避免性能问题和兼容性错误
};

// 停止文字颜色监控定时器
const stopTextColorMonitoring = () => {
  if (textColorFixTimer.value) {
    clearInterval(textColorFixTimer.value);
    textColorFixTimer.value = null;
  }
};

// 绑定思维导图事件
const bindMindmapEvents = () => {
  if (!mindMapInstance.value) return;

  // 节点点击事件
  mindMapInstance.value.on("node_click", (node) => {
    const convertedNode = convertFromSimpleMindMapNode(node);
    emit("node-click", convertedNode);
  });

  // 节点激活状态变化事件
  mindMapInstance.value.on("node_active", (node, isActive) => {
    // 移除文字颜色修复逻辑
  });

  // 节点双击事件（暂时注释AI功能）
  mindMapInstance.value.on("node_dblclick", (node) => {
    // handleAIGenerateChildren(node)
    console.log("双击节点:", node);
  });

  // 数据变化事件 - 简化处理，减少抖动
  let dataChangeTimer = null;
  mindMapInstance.value.on("data_change", (data) => {
    console.log("思维导图数据发生变化");

    // 使用更长的防抖时间，减少频繁同步
    const debounceTime = 800;

    // 防抖处理，避免频繁同步
    if (dataChangeTimer) {
      clearTimeout(dataChangeTimer);
    }
    dataChangeTimer = setTimeout(() => {
      // 简化同步逻辑，减少清理调用
      syncToMarkdown();
    }, debounceTime);
  });

  // 节点渲染完成事件 - 移除清理逻辑
  mindMapInstance.value.on("node_tree_render_end", () => {
    // 移除清理调用，避免影响视图
  });

  // 节点右键菜单事件
  mindMapInstance.value.on("node_contextmenu", (e, node) => {
    showContextMenu(e, "node", node);
  });

  // 画布右键菜单事件
  let mousedownX = 0;
  let mousedownY = 0;
  let isMousedown = false;

  mindMapInstance.value.on("svg_mousedown", (e) => {
    if (e.which !== 3) return; // 只处理右键
    mousedownX = e.clientX;
    mousedownY = e.clientY;
    isMousedown = true;
  });

  mindMapInstance.value.on("mouseup", (e) => {
    if (!isMousedown) return;
    isMousedown = false;

    // 判断是否为点击事件（移动距离小于3像素）
    if (
      Math.abs(mousedownX - e.clientX) > 3 ||
      Math.abs(mousedownY - e.clientY) > 3
    ) {
      return;
    }

    showContextMenu(e, "canvas");
  });

  // 隐藏右键菜单的事件 - 减少清理频率
  mindMapInstance.value.on("node_click", () => {
    hideContextMenu();
    // 移除频繁的清理调用，避免影响视图
  });
  mindMapInstance.value.on("draw_click", () => {
    hideContextMenu();
    // 移除频繁的清理调用，避免影响视图
  });
  mindMapInstance.value.on("expand_btn_click", hideContextMenu);
};

// 绑定快捷键
const bindShortcutKeys = () => {
  if (!mindMapInstance.value) return;

  try {
    // 回到中心
    mindMapInstance.value.keyCommand.addShortcut("Control+Enter", () => {
      if (mindMapInstance.value.renderer.root) {
        mindMapInstance.value.renderer.setRootNodeCenter();
      }
    });

    // 适应画布
    mindMapInstance.value.keyCommand.addShortcut("Control+i", () => {
      fitView();
    });

    // 重置布局
    mindMapInstance.value.keyCommand.addShortcut("Control+l", () => {
      resetLayout();
    });

    // 展开全部
    mindMapInstance.value.keyCommand.addShortcut("Control+Shift+e", () => {
      expandAll();
    });

    // 收起全部
    mindMapInstance.value.keyCommand.addShortcut("Control+Shift+c", () => {
      collapseAll();
    });

    // 删除所有子节点快捷键 (Shift + Delete)
    mindMapInstance.value.keyCommand.addShortcut("Shift+Delete", () => {
      const activeNodes = mindMapInstance.value.renderer.activeNodeList;
      if (activeNodes && activeNodes.length > 0) {
        const node = activeNodes[0];
        handleRemoveAllChildren(node);
      } else {
        ElMessage.warning("请先选择一个节点");
      }
    });
  } catch (error) {
    console.error("绑定快捷键失败:", error);
  }
};

// 更新思维导图数据
const updateMindmapData = (data) => {
  if (!mindMapInstance.value || !data) return;

  try {
    const simpleMindMapData = convertToSimpleMindMapData(data);
    mindMapInstance.value.setData(simpleMindMapData);

    // 重新应用当前布局，因为setData可能会重置布局
    if (selectedLayout.value) {
      mindMapInstance.value.setLayout(selectedLayout.value);
    }

    // 不自动适应视图，避免抖动
    // setTimeout(() => {
    //   if (mindMapInstance.value) {
    //     mindMapInstance.value.view.fit()
    //   }
    // }, 100)
  } catch (error) {
    console.error("更新思维导图数据失败:", error);
  }
};

// 更新主题 - 使用正确的 simple-mind-map 主题配置格式
const updateTheme = (isDark) => {
  if (!mindMapInstance.value) return;

  try {
    // 不需要动态更新hoverRectColor，使用固定的通用颜色
    const themeConfig = isDark
      ? {
          // 深色主题配置 - 使用正确的层级结构
          backgroundColor: "#1a1a1a",
          lineColor: "#888888",
          lineWidth: 2,
          lineOpacity: 0.8,

          // 根节点样式
          root: {
            fillColor: "#409eff",
            color: "#ffffff",
            borderColor: "#409eff",
            borderWidth: 2,
            fontWeight: "bold",
            fontSize: props.fontSize || 16,
            fontFamily:
              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',
            hoverRectColor: "", // 空字符串表示使用实例化选项的值
            hoverRectRadius: 5,
          },

          // 二级节点样式
          second: {
            fillColor: "#363636",
            color: "#ffffff",
            borderColor: "#888888",
            borderWidth: 1.5,
            fontWeight: "500",
            fontSize: props.fontSize || 14,
            fontFamily:
              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',
            hoverRectColor: "", // 空字符串表示使用实例化选项的值
            hoverRectRadius: 5,
          },

          // 三级及以下节点样式
          node: {
            fillColor: "#2d2d2d",
            color: "#ffffff",
            borderColor: "#666666",
            borderWidth: 1,
            fontWeight: "400",
            fontSize: props.fontSize || 14,
            fontFamily:
              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',
            hoverRectColor: "", // 空字符串表示使用实例化选项的值
            hoverRectRadius: 5,
          },
        }
      : {
          // 浅色主题配置 - 使用正确的层级结构
          backgroundColor: "#fafafa",
          lineColor: "#999999",
          lineWidth: 2,
          lineOpacity: 0.8,

          // 根节点样式
          root: {
            fillColor: "#409eff",
            color: "#ffffff",
            borderColor: "#409eff",
            borderWidth: 2,
            fontWeight: "bold",
            fontSize: props.fontSize || 16,
            fontFamily:
              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',
            hoverRectColor: "", // 空字符串表示使用实例化选项的值
            hoverRectRadius: 5,
          },

          // 二级节点样式
          second: {
            fillColor: "#ffffff",
            color: "#333333",
            borderColor: "#cccccc",
            borderWidth: 1,
            fontWeight: "500",
            fontSize: props.fontSize || 14,
            fontFamily:
              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',
            hoverRectColor: "", // 空字符串表示使用实例化选项的值
            hoverRectRadius: 5,
          },

          // 三级及以下节点样式
          node: {
            fillColor: "#ffffff",
            color: "#333333",
            borderColor: "#dddddd",
            borderWidth: 1,
            fontWeight: "400",
            fontSize: props.fontSize || 14,
            fontFamily:
              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif',
            hoverRectColor: "", // 空字符串表示使用实例化选项的值
            hoverRectRadius: 5,
          },
        };

    mindMapInstance.value.setThemeConfig(themeConfig);
  } catch (error) {
    console.error("更新主题失败:", error);
  }
};

// 更新字体大小 - 使用正确的层级结构
const updateFontSize = (fontSize) => {
  if (!mindMapInstance.value) return;

  try {
    mindMapInstance.value.setThemeConfig({
      root: {
        fontSize: fontSize,
      },
      second: {
        fontSize: fontSize,
      },
      node: {
        fontSize: fontSize,
      },
    });
  } catch (error) {
    console.error("更新字体大小失败:", error);
  }
};

// 数据转换：将我们的数据格式转换为 simple-mind-map 格式
const convertToSimpleMindMapData = (data) => {
  if (!data) return null;

  const convertNode = (node) => {
    const result = {
      data: {
        text: node.title || "未命名节点",
        uid: node.id, // 直接使用节点的id作为uid
      },
    };

    if (node.children && node.children.length > 0) {
      result.children = node.children.map((child) => convertNode(child));
    }

    return result;
  };

  return convertNode(data);
};

// 数据转换：将 simple-mind-map 节点转换为我们的格式
// 根据UID在思维导图数据中查找节点
const findNodeByUid = (data, uid) => {
  if (!data || !uid) return null;

  const findNode = (node) => {
    // 检查节点的id字段（我们的数据结构中使用id存储uid）
    if (node.id === uid) {
      return node;
    }

    if (node.children) {
      for (const child of node.children) {
        const found = findNode(child);
        if (found) return found;
      }
    }

    return null;
  };

  return findNode(data);
};

// 根据标题在思维导图数据中查找节点
const findNodeByTitle = (data, title) => {
  if (!data || !title) return null;

  const findNode = (node) => {
    if (node.title === title) {
      return node;
    }

    if (node.children) {
      for (const child of node.children) {
        const found = findNode(child);
        if (found) return found;
      }
    }

    return null;
  };

  return findNode(data);
};

const convertFromSimpleMindMapNode = (node) => {
  if (!node) {
    return null;
  }

  // simple-mind-map的节点可能有不同的数据结构
  let nodeData = null;
  if (node.data) {
    nodeData = node.data;
  } else if (node.nodeData && node.nodeData.data) {
    nodeData = node.nodeData.data;
  } else {
    console.warn("convertFromSimpleMindMapNode: 无法找到节点数据", node);
    return null;
  }

  // 使用节点自身的层级信息
  const isRootNode = node.isRoot || false;
  const nodeLevel = node.layerIndex || 0; // layerIndex从0开始，根节点为0

  return {
    id: nodeData.uid, // 直接使用simple-mind-map的uid，不生成新的
    title: nodeData.text || "未命名节点",
    level: nodeLevel,
    content: nodeData.note || "", // simple-mind-map 的备注字段
    expanded: nodeData.expand !== false, // 默认展开
    isRoot: isRootNode,
    children: node.children
      ? node.children.map((child) => convertFromSimpleMindMapNode(child))
      : [],
  };
};

// 获取当前思维导图数据
const getCurrentMindmapData = () => {
  if (!mindMapInstance.value) return null;

  try {
    const data = mindMapInstance.value.getData();
    

    // 🔧 关键修复：直接使用simple-mind-map的数据结构
    // 转换为我们需要的格式，但保持uid的一致性
    const converted = convertSimpleMindMapData(data);
    console.log("转换后的数据:", converted);
    console.log("转换后数据检查:");
    console.log("- 根节点ID:", converted?.id);
    console.log("- 根节点标题:", converted?.title);
    console.log("- 子节点数量:", converted?.children?.length || 0);

    if (converted?.children && converted.children.length > 0) {
      console.log("- 第一个子节点ID:", converted.children[0]?.id);
      console.log("- 第一个子节点标题:", converted.children[0]?.title);
    }

    return converted;
  } catch (error) {
    console.error("获取思维导图数据失败:", error);
    return null;
  }
};

// 转换simple-mind-map数据为我们的格式，保持uid一致性
const convertSimpleMindMapData = (node) => {
  if (!node || !node.data) {
    console.warn("convertSimpleMindMapData: 无效节点", node);
    return null;
  }

  const result = {
    id: node.data.uid, // 直接使用simple-mind-map的uid，不生成新的
    title: node.data.text || "未命名节点",
    content: node.data.note || "",
    children: node.children ? node.children.map(child => convertSimpleMindMapData(child)).filter(Boolean) : []
  };

 
  return result;
};

// 添加新节点
const addNode = (parentNodeId, nodeData) => {
  if (!mindMapInstance.value) return;

  try {
    // 查找父节点
    const parentNode = findNodeById(parentNodeId);
    if (parentNode) {
      mindMapInstance.value.execCommand(
        "INSERT_CHILD_NODE",
        parentNode,
        nodeData.title
      );
    }
  } catch (error) {
    console.error("添加节点失败:", error);
  }
};

// 批量添加AI生成的子节点
const addAIGeneratedNodes = (parentNodeId, nodeDataList) => {
  if (!mindMapInstance.value || !nodeDataList || nodeDataList.length === 0) {
    console.warn("addAIGeneratedNodes: 参数无效", {
      mindMapInstance: !!mindMapInstance.value,
      nodeDataList,
      parentNodeId,
    });
    return;
  }

  try {
    console.log("开始查找父节点，ID:", parentNodeId);

    // 查找父节点
    const parentNode = findNodeById(parentNodeId);
    if (!parentNode) {
      console.error("找不到父节点:", parentNodeId);
      return;
    }

    console.log("找到父节点:", parentNode);
    console.log(
      `为节点"${parentNode.data?.text || "未知节点"}"添加${
        nodeDataList.length
      }个AI生成的子节点`
    );

    // 保存父节点引用，避免在异步回调中丢失
    const savedParentNode = parentNode;

    // 批量添加子节点，支持递归添加
    nodeDataList.forEach((nodeData, index) => {
      setTimeout(() => {
        try {
          // 检查父节点是否仍然有效
          if (!savedParentNode || !mindMapInstance.value) {
            console.error(
              `第${index + 1}个AI节点添加失败: 父节点或思维导图实例无效`
            );
            return;
          }

          // 先激活父节点，然后添加主节点
          mindMapInstance.value.renderer.setNodeActive(savedParentNode, true);

          // 添加主节点
          mindMapInstance.value.execCommand(
            "INSERT_CHILD_NODE",
            false, // 不自动进入编辑模式
            [savedParentNode], // 指定父节点数组
            { text: nodeData.title || nodeData.text }, // 节点数据
            [] // 子节点数组
          );

          console.log(`添加AI主节点: ${nodeData.title || nodeData.text}`);

          // 如果有子节点，递归添加
          if (nodeData.children && nodeData.children.length > 0) {
            setTimeout(() => {
              // 找到刚添加的节点
              const newlyAddedNode = savedParentNode.children[savedParentNode.children.length - 1];
              if (newlyAddedNode) {
                console.log(`为节点 "${nodeData.title}" 添加 ${nodeData.children.length} 个子节点`);
                addChildrenRecursively(newlyAddedNode, nodeData.children);
              }
            }, 200); // 等待主节点添加完成
          }
        } catch (error) {
          console.error(`添加第${index + 1}个AI节点失败:`, error);
          console.error("父节点状态:", savedParentNode);
          console.error("节点数据:", nodeData);
        }
      }, index * 300); // 增加间隔时间，确保递归添加有足够时间
    });
  } catch (error) {
    console.error("批量添加AI节点失败:", error);
  }
};

// 递归添加子节点
const addChildrenRecursively = (parentNode, childrenData) => {
  if (!parentNode || !childrenData || childrenData.length === 0) {
    return;
  }

  childrenData.forEach((childData, index) => {
    setTimeout(() => {
      try {
        if (!mindMapInstance.value) {
          console.error("mindMapInstance 不存在，无法添加子节点");
          return;
        }

        // 激活父节点并添加子节点
        mindMapInstance.value.renderer.setNodeActive(parentNode, true);
        mindMapInstance.value.execCommand(
          "INSERT_CHILD_NODE",
          false,
          [parentNode],
          { text: childData.title || childData.text },
          []
        );

        console.log(`添加子节点: ${childData.title || childData.text}`);

        // 如果还有更深层的子节点，继续递归
        if (childData.children && childData.children.length > 0) {
          setTimeout(() => {
            const newChildNode = parentNode.children[parentNode.children.length - 1];
            if (newChildNode) {
              addChildrenRecursively(newChildNode, childData.children);
            }
          }, 200);
        }
      } catch (error) {
        console.error(`添加子节点失败:`, error);
      }
    }, index * 200); // 子节点间隔200ms
  });
};

// 根据ID查找节点
const findNodeById = (nodeId) => {
  if (!mindMapInstance.value) {
    console.error("mindMapInstance 不存在");
    return null;
  }

  if (!nodeId) {
    console.error("nodeId 为空:", nodeId);
    return null;
  }

  try {
    console.log("正在查找节点，ID:", nodeId);
    console.log(
      "mindMapInstance.value.renderer:",
      mindMapInstance.value.renderer
    );

    // 检查renderer是否有root节点
    if (!mindMapInstance.value.renderer.root) {
      console.error("renderer.root 不存在，思维导图可能未正确初始化");
      return null;
    }

    console.log("renderer.root 存在:", mindMapInstance.value.renderer.root);

    // 使用simple-mind-map的findNodeByUid方法
    const foundNode = mindMapInstance.value.renderer.findNodeByUid(nodeId);

    if (!foundNode) {
      console.warn(`未找到ID为 ${nodeId} 的节点`);
      // 尝试获取所有节点来调试
      try {
        const rootNode = mindMapInstance.value.renderer.root;
        console.log("根节点:", rootNode);
        if (rootNode) {
          console.log("根节点数据:", rootNode.nodeData?.data);
          console.log("根节点UID:", rootNode.getData("uid"));
        }
      } catch (debugError) {
        console.error("调试信息获取失败:", debugError);
      }
    } else {
      console.log("找到节点:", foundNode);
      console.log("找到节点的UID:", foundNode.getData("uid"));
    }

    return foundNode;
  } catch (error) {
    console.error("查找节点失败:", error);
    return null;
  }
};

// AI生成相关方法
const handleAIGenerate = (node) => {
  if (!node) {
    ElMessage.warning("请选择要生成内容的节点");
    return;
  }

  console.log("=== handleAIGenerate 调试 ===");
  console.log("接收到的simple-mind-map节点:", node);
  console.log("节点数据结构:", node.nodeData?.data);

  // 🔧 关键修复：直接使用simple-mind-map的uid
  // 根据官方文档，每个节点都有唯一的uid
  const nodeUid = node.nodeData?.data?.uid || node.data?.uid;
  const nodeText = node.nodeData?.data?.text || node.data?.text;

  console.log("节点UID:", nodeUid);
  console.log("节点标题:", nodeText);

  if (!nodeUid) {
    console.error("无法获取节点UID");
    ElMessage.error("无法获取节点信息");
    return;
  }

  // 获取完整的思维导图数据
  const fullMindmapData = getCurrentMindmapData();
  console.log("完整思维导图数据:", fullMindmapData);

  // 🔧 使用uid在思维导图数据中查找对应的节点
  const realNode = findNodeByUid(fullMindmapData, nodeUid);
  console.log("查找到的真实节点:", realNode);

  if (!realNode) {
    console.error("无法在思维导图数据中找到对应节点，UID:", nodeUid);
    ElMessage.error("无法获取节点信息");
    return;
  }

  // 触发AI对话框
  emit("ai-generate-request", {
    node: realNode, // 传递从思维导图数据中找到的真实节点
    mindmapData: fullMindmapData, // 传递完整的思维导图数据
  });
};

// 删除所有子节点
const handleRemoveAllChildren = (node) => {
  if (!node) {
    ElMessage.warning("请选择要删除子节点的节点");
    return;
  }

  // 尝试获取子节点数量
  let childrenCount = 0;
  let childrenArray = null;

  if (node.children && Array.isArray(node.children)) {
    childrenArray = node.children;
    childrenCount = node.children.length;
  } else if (node._children && Array.isArray(node._children)) {
    childrenArray = node._children;
    childrenCount = node._children.length;
  } else if (node.nodeData?.children && Array.isArray(node.nodeData.children)) {
    childrenArray = node.nodeData.children;
    childrenCount = node.nodeData.children.length;
  }

  console.log("检测到的子节点数组:", childrenArray);
  console.log("子节点数量:", childrenCount);

  if (childrenCount === 0) {
    ElMessage.info("该节点没有子节点");
    return;
  }

  // 确认对话框
  ElMessageBox.confirm(
    `确定要删除"${
      node.nodeData?.data?.text || "未知节点"
    }"的所有 ${childrenCount} 个子节点吗？\n\n注意：只删除子节点，当前节点会保留。\n\n此操作不可撤销。`,
    "删除子节点确认",
    {
      confirmButtonText: "确定删除",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: "el-button--danger",
    }
  )
    .then(() => {
      try {
        console.log("用户确认删除，开始执行删除操作");

        // 使用新的删除方法，如果失败则使用备用方法
        try {
          removeAllChildrenByIteration(node, childrenCount);
        } catch (error) {
          console.error("主要删除方法失败，尝试备用方法:", error);
          removeChildrenSafely(node).catch(err => {
            console.error("备用删除方法也失败:", err);
            ElMessage.error("所有删除方法都失败了，请刷新页面后重试");
          });
        }
      } catch (error) {
        console.error("删除子节点失败:", error);
        ElMessage.error("删除子节点失败: " + error.message);
      }
    })
    .catch(() => {
      console.log("用户取消删除子节点操作");
    });
};

// 通过分批删除所有子节点（最稳定版本）
const removeAllChildrenByIteration = (parentNode, expectedCount) => {
  if (!mindMapInstance.value || !parentNode) {
    console.error("mindMapInstance 或 parentNode 不存在");
    ElMessage.error("删除失败：思维导图实例或节点无效");
    return;
  }

  try {
    console.log("开始分批删除子节点，父节点:", parentNode);
    console.log("预期删除数量:", expectedCount);

    // 检查思维导图状态
    if (!mindMapInstance.value.renderer || !mindMapInstance.value.renderer.setNodeActive) {
      throw new Error("思维导图渲染器状态异常");
    }

    // 使用分批删除策略，每批最多删除3个节点
    const BATCH_SIZE = 3;
    const BATCH_DELAY = 200; // 每批之间延迟200ms

    let totalDeleted = 0;
    let batchCount = 0;

    const deleteBatch = () => {
      // 重新获取当前的子节点列表（因为之前的删除可能已经改变了结构）
      const currentChildren = [...(parentNode.children || [])];

      if (currentChildren.length === 0) {
        // 所有子节点已删除完成
        ElMessage.success(`成功删除 ${totalDeleted} 个子节点`);

        // 重新激活父节点并强制重新渲染
        setTimeout(() => {
          try {
            mindMapInstance.value.renderer.setNodeActive(parentNode, true);
            // 强制重新渲染整个思维导图
            mindMapInstance.value.reRender();
          } catch (error) {
            console.warn("重新渲染失败:", error);
            try {
              mindMapInstance.value.render();
            } catch (e) {
              console.warn("备用渲染方法也失败:", e);
            }
          }
        }, 100);
        return;
      }

      batchCount++;
      console.log(`开始第${batchCount}批删除，剩余节点数量:`, currentChildren.length);

      // 取当前批次要删除的节点（最多BATCH_SIZE个）
      const batchToDelete = currentChildren.slice(0, BATCH_SIZE);
      let batchDeleted = 0;

      // 删除当前批次的节点
      batchToDelete.forEach((childNode, index) => {
        setTimeout(() => {
          try {
            // 再次检查节点是否仍然存在
            if (!parentNode.children || !parentNode.children.includes(childNode)) {
              console.log(`节点已被删除，跳过: ${childNode.nodeData?.data?.text}`);
              batchDeleted++;

              // 如果是批次中的最后一个节点，继续下一批
              if (batchDeleted === batchToDelete.length) {
                setTimeout(deleteBatch, BATCH_DELAY);
              }
              return;
            }

            // 检查节点数据完整性
            if (!childNode.nodeData || !childNode.nodeData.data) {
              console.error(`节点数据不完整，跳过: ${childNode}`);
              batchDeleted++;

              if (batchDeleted === batchToDelete.length) {
                setTimeout(deleteBatch, BATCH_DELAY);
              }
              return;
            }

            // 激活并删除节点
            mindMapInstance.value.renderer.setNodeActive(childNode, true);
            mindMapInstance.value.execCommand("REMOVE_NODE", [childNode]);

            totalDeleted++;
            batchDeleted++;
            console.log(`删除节点: ${childNode.nodeData?.data?.text} (总计: ${totalDeleted})`);

            // 如果是批次中的最后一个节点，继续下一批
            if (batchDeleted === batchToDelete.length) {
              setTimeout(deleteBatch, BATCH_DELAY);
            }

          } catch (error) {
            console.error(`删除节点失败:`, error);
            batchDeleted++;

            if (batchDeleted === batchToDelete.length) {
              setTimeout(deleteBatch, BATCH_DELAY);
            }
          }
        }, index * 50); // 批次内节点间隔50ms
      });
    };

    // 开始第一批删除
    deleteBatch();



  } catch (error) {
    console.error("遍历删除子节点失败:", error);
    ElMessage.error("删除子节点失败: " + error.message);

    // 发生错误时，尝试强制重新渲染
    try {
      mindMapInstance.value.reRender();
    } catch (e) {
      console.warn("错误恢复渲染失败:", e);
    }
  }
};

// 简化的删除子节点方法 - 减少视图刷新
const removeAllChildrenSimple = (node) => {
  if (!node || !mindMapInstance.value) {
    console.error("节点或思维导图实例不存在");
    return false;
  }

  try {
    console.log("=== 删除所有子节点 ===");
    console.log("目标节点:", node.nodeData?.data?.text);

    // 先选中目标节点
    mindMapInstance.value.renderer.setNodeActive(node, true);

    // 直接使用simple-mind-map的内置命令删除所有子节点
    mindMapInstance.value.execCommand("REMOVE_ALL_CHILDREN");

    console.log("删除子节点完成");
    return true;
  } catch (error) {
    console.error("删除子节点失败:", error);
    return false;
  }
};

// 超级安全的备用删除方法（逐个删除，最大兼容性）
const removeChildrenSafely = (node) => {
  if (!node.children || node.children.length === 0) {
    ElMessage.info("没有子节点需要删除");
    return true;
  }

  return new Promise((resolve) => {
    console.log("使用超级安全备用删除方法");

    let deletedCount = 0;
    const totalCount = node.children.length;

    // 逐个删除，每次删除后等待渲染完成
    const deleteOneByOne = () => {
      // 重新获取当前子节点（因为删除会改变数组）
      const currentChildren = node.children || [];

      if (currentChildren.length === 0) {
        // 删除完成
        ElMessage.success(`超级安全删除完成，共删除 ${deletedCount} 个子节点`);

        // 重新选中父节点并强制渲染
        setTimeout(() => {
          try {
            mindMapInstance.value.renderer.setNodeActive(node, true);
            mindMapInstance.value.reRender();
          } catch (error) {
            console.warn("最终渲染失败:", error);
          }
          resolve(true);
        }, 100);
        return;
      }

      // 删除第一个子节点
      const childToDelete = currentChildren[0];

      try {
        if (childToDelete && childToDelete.nodeData) {
          console.log(`超级安全删除: ${childToDelete.nodeData?.data?.text} (${deletedCount + 1}/${totalCount})`);

          // 选中并删除节点
          mindMapInstance.value.renderer.setNodeActive(childToDelete, true);
          mindMapInstance.value.execCommand("REMOVE_NODE");

          deletedCount++;

          // 等待一段时间后继续删除下一个
          setTimeout(deleteOneByOne, 100);
        } else {
          // 节点无效，跳过并继续
          console.warn("跳过无效节点");
          setTimeout(deleteOneByOne, 50);
        }
      } catch (error) {
        console.error("删除单个节点失败:", error);
        // 即使失败也继续删除下一个
        setTimeout(deleteOneByOne, 100);
      }
    };

    // 开始删除
    deleteOneByOne();
  });
};

// 同步到Markdown - 优化避免抖动
const syncToMarkdown = () => {
  // 延迟同步，避免频繁更新导致抖动
  if (syncToMarkdown.timer) {
    clearTimeout(syncToMarkdown.timer);
  }

  syncToMarkdown.timer = setTimeout(() => {
    const currentData = getCurrentMindmapData();

    if (currentData) {
      const markdown = contentProcessor.convertMindmapToMarkdown(currentData);


      // 设置内部更新标志，避免触发数据监听器
      isInternalDataUpdate.value = true;
      emit("markdown-change", markdown);
    } else {
      console.warn("无法获取思维导图数据");
    }
  }, 150); // 150ms延迟，避免频繁更新
};

// 显示右键菜单 - 优化边界检测
const showContextMenu = (e, type, node = null) => {
  // 先临时显示菜单在屏幕外，用于测量尺寸
  contextMenu.value = {
    show: true,
    x: -9999,
    y: -9999,
    type,
    node,
  };

  // 下一帧计算正确位置
  nextTick(() => {
    const menuElement = document.querySelector(".context-menu");
    if (!menuElement) {
      console.warn("右键菜单元素未找到");
      return;
    }

    // 获取菜单实际尺寸
    const menuRect = menuElement.getBoundingClientRect();
    const menuWidth = menuRect.width || 200; // 默认宽度
    const menuHeight = menuRect.height || 300; // 默认高度

    // 获取视窗尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 计算初始位置（鼠标右下方）
    let x = e.clientX + 10;
    let y = e.clientY + 10;

    // 安全边距
    const safeMargin = 20;

    // 右边界检测 - 如果菜单会超出右边界，显示在鼠标左侧
    if (x + menuWidth > viewportWidth - safeMargin) {
      x = e.clientX - menuWidth - 10;
      // 如果左侧也放不下，尝试居中显示
      if (x < safeMargin) {
        x = Math.max(safeMargin, (viewportWidth - menuWidth) / 2);
      }
    }

    // 底部边界检测 - 如果菜单会超出底部，显示在鼠标上方
    if (y + menuHeight > viewportHeight - safeMargin) {
      y = e.clientY - menuHeight - 10;
      // 如果上方也放不下，尝试居中显示
      if (y < safeMargin) {
        y = Math.max(safeMargin, (viewportHeight - menuHeight) / 2);
      }
    }

    // 最终边界保护 - 确保菜单完全在视窗内
    const maxX = viewportWidth - menuWidth - safeMargin;
    const maxY = viewportHeight - menuHeight - safeMargin;

    x = Math.max(safeMargin, Math.min(x, maxX));
    y = Math.max(safeMargin, Math.min(y, maxY));

    // 如果菜单尺寸超过视窗，调整菜单位置到安全区域
    if (menuWidth > viewportWidth - 2 * safeMargin) {
      x = safeMargin;
    }
    if (menuHeight > viewportHeight - 2 * safeMargin) {
      y = safeMargin;
    }

    // 设置最终位置
    contextMenu.value.x = x;
    contextMenu.value.y = y;

    console.debug("右键菜单位置:", {
      x,
      y,
      menuWidth,
      menuHeight,
      viewportWidth,
      viewportHeight,
      mouseX: e.clientX,
      mouseY: e.clientY,
    });

    // 再次检查位置，确保菜单完全可见
    setTimeout(() => {
      const finalMenuElement = document.querySelector(".context-menu");
      if (finalMenuElement) {
        const finalRect = finalMenuElement.getBoundingClientRect();
        let needsAdjustment = false;
        let newX = contextMenu.value.x;
        let newY = contextMenu.value.y;

        // 检查是否超出右边界
        if (finalRect.right > viewportWidth - 10) {
          newX = viewportWidth - finalRect.width - 10;
          needsAdjustment = true;
        }

        // 检查是否超出底部边界
        if (finalRect.bottom > viewportHeight - 10) {
          newY = viewportHeight - finalRect.height - 10;
          needsAdjustment = true;
        }

        // 检查是否超出左边界
        if (finalRect.left < 10) {
          newX = 10;
          needsAdjustment = true;
        }

        // 检查是否超出顶部边界
        if (finalRect.top < 10) {
          newY = 10;
          needsAdjustment = true;
        }

        if (needsAdjustment) {
          contextMenu.value.x = newX;
          contextMenu.value.y = newY;
          console.debug("调整后的菜单位置:", { x: newX, y: newY });
        }
      }
    }, 10); // 短暂延迟，确保DOM更新完成
  });
};

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.value = {
    show: false,
    x: 0,
    y: 0,
    type: "",
    node: null,
  };
};

// 处理右键菜单项点击
const handleContextMenuItemClick = (action) => {
  const { type, node } = contextMenu.value;

  hideContextMenu(); // 先隐藏菜单

  if (type === "node" && node) {
    // 先激活/选中当前节点
    try {
      mindMapInstance.value.renderer.setNodeActive(node, true);

      switch (action) {
        case "INSERT_NODE":
          executeNodeOperationWithStableView("INSERT_NODE");
          break;

        case "INSERT_CHILD_NODE":
          executeNodeOperationWithStableView("INSERT_CHILD_NODE");
          break;

        case "INSERT_PARENT_NODE":
          executeNodeOperationWithStableView("INSERT_PARENT_NODE");
          break;
        case "EXPAND_ALL_CHILDREN":
          expandNodeChildren(node);
          break;
        case "COLLAPSE_ALL_CHILDREN":
          collapseNodeChildren(node);
          break;
        case "UP_NODE":
          mindMapInstance.value.execCommand("UP_NODE");
          break;
        case "DOWN_NODE":
          mindMapInstance.value.execCommand("DOWN_NODE");
          break;
        case "REMOVE_NODE":
          // 保存当前视图状态
          const currentTransform =
            mindMapInstance.value.view.getTransformData();
          const currentScale = mindMapInstance.value.view.scale;

          mindMapInstance.value.execCommand("REMOVE_NODE");

          // 延迟恢复视图状态，抵消可能的自动重置
          setTimeout(() => {
            if (mindMapInstance.value) {
              try {
                mindMapInstance.value.view.setTransformData({
                  ...currentTransform,
                  scale: currentScale,
                });
              } catch (error) {
                console.warn("恢复视图状态失败:", error);
              }
            }
          }, 200); // 200ms后恢复，给删除操作足够时间完成
          break;
        case "COPY_NODE":
          mindMapInstance.value.renderer.copy();
          break;
        case "CUT_NODE":
          mindMapInstance.value.renderer.cut();
          break;
        case "PASTE_NODE":
          // 简化粘贴操作，直接执行
          mindMapInstance.value.renderer.paste();
          break;
        case "AI_GENERATE":
          handleAIGenerate(node);
          break;
        case "REMOVE_ALL_CHILDREN":
          handleRemoveAllChildren(node);
          break;
        default:
          break;
      }
    } catch (error) {
      console.error("执行右键菜单操作失败:", error);
      ElMessage.error("操作失败: " + error.message);
    }
  } else if (type === "canvas") {
    try {
      switch (action) {
        case "RETURN_CENTER":
          // 回到中心
          if (mindMapInstance.value.renderer.root) {
            mindMapInstance.value.renderer.setRootNodeCenter();
          }
          break;
        case "EXPAND_ALL":
          mindMapInstance.value.execCommand("EXPAND_ALL");
          break;
        case "UNEXPAND_ALL":
          mindMapInstance.value.execCommand("UNEXPAND_ALL");
          break;
        case "PASTE_NODE":
          mindMapInstance.value.renderer.paste();
          break;
        case "SELECT_ALL":
          mindMapInstance.value.execCommand("SELECT_ALL");
          break;
        case "RESET_LAYOUT":
          resetLayout();
          break;
        case "FIT_VIEW":
          fitView();
          break;
        default:
          break;
      }
    } catch (error) {
      console.error("执行画布操作失败:", error);
      ElMessage.error("操作失败: " + error.message);
    }
  }
};

// AI扩展内容
const handleAIExpandContent = async (node) => {
  if (isGenerating.value) {
    ElMessage.warning("AI正在生成中，请稍候...");
    return;
  }

  try {
    isGenerating.value = true;

    const targetNode = convertFromSimpleMindMapNode(node);
    const rootData = getCurrentMindmapData();
    const context = contentProcessor.getNodeContext(targetNode, rootData);
    const prompt = aiPromptBuilder.buildContentPrompt(context);

    const result = await callAIGenerate(prompt, "content");

    if (result && result.content) {
      const success = contentProcessor.applyAIGeneratedContent({
        node: targetNode,
        type: "expand",
        content: result.content,
        mindmapData: rootData,
      });

      if (success) {
        // 保存当前视图状态
        const currentTransform = mindMapInstance.value.view.getTransformData();
        const currentScale = mindMapInstance.value.view.scale;

        updateMindmapData(rootData);

        // 延迟恢复视图状态，避免AI生成后回到中心
        setTimeout(() => {
          if (mindMapInstance.value) {
            try {
              mindMapInstance.value.view.setTransformData({
                ...currentTransform,
                scale: currentScale,
              });
            } catch (error) {
              console.warn("AI生成后恢复视图状态失败:", error);
            }
          }
        }, 100);

        ElMessage.success("AI扩展内容成功");
      }
    }
  } catch (error) {
    console.error("AI扩展内容失败:", error);
    ElMessage.error("AI扩展内容失败");
  } finally {
    isGenerating.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  expandAll,
  collapseAll,
  resetLayout,
  fitView,
  zoomIn,
  zoomOut,
  toggleFullscreen,
  isFullscreen,
  getCurrentMindmapData,
  addNode,
  addAIGeneratedNodes,
  expandNodeChildren,
  collapseNodeChildren,
  // handleAIGenerateChildren, // 暂时注释
  // handleAIExpandContent, // 暂时注释
  syncToMarkdown,
  showContextMenu,
  hideContextMenu,
  cleanupTempNodes,
  initMindmap,
  destroyMindmapInstance,
});
</script>

<style lang="scss" scoped>
.mindmap-canvas {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);

  .mindmap-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color-light);
    flex-shrink: 0;
    height: 48px;
    box-sizing: border-box;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .toolbar-divider {
      width: 1px;
      height: 20px;
      background: var(--el-border-color-light);
      margin: 0 4px;
    }

    .toolbar-item {
      display: flex;
      align-items: center;
      gap: 8px;
      white-space: nowrap;

      .toolbar-label {
        font-size: 13px;
        color: var(--el-text-color-regular);
        font-weight: 500;
      }
    }

    // 按钮组样式优化
    :deep(.el-button-group) {
      .el-button {
        border-color: var(--el-border-color);
        background: var(--el-bg-color);
        color: var(--el-text-color-primary);

        &:hover {
          background: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary-light-7);
          color: var(--el-color-primary);
        }

        &:focus {
          border-color: var(--el-color-primary);
          color: var(--el-color-primary);
        }
      }
    }

    // 原生选择器样式
    .native-select {
      height: 28px;
      padding: 4px 24px 4px 8px;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      background: var(--el-bg-color);
      color: var(--el-text-color-primary);
      font-size: 13px;
      font-family: inherit;
      outline: none;
      cursor: pointer;
      transition: all 0.2s ease;
      appearance: none;
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 6px center;
      background-size: 14px;

      &:hover {
        border-color: var(--el-color-primary-light-7);
        background-color: var(--el-color-primary-light-9);
      }

      &:focus {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px var(--el-color-primary-light-9);
      }

      &.layout-select {
        width: 120px;
      }

      &.model-select {
        width: 160px;
      }

      // 选项样式
      option {
        background: var(--el-bg-color);
        color: var(--el-text-color-primary);
        padding: 8px 12px;

        &:hover {
          background: var(--el-color-primary-light-9);
        }

        &:checked {
          background: var(--el-color-primary);
          color: white;
        }
      }
    }
  }

  .mindmap-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: var(--el-fill-color-blank);
    width: 100%;
    height: 100%;
    min-width: 800px;
    min-height: 600px;
    z-index: 1;
    pointer-events: all;

    // 确保容器完全可见
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;

    // simple-mind-map 容器样式
    :deep(.smm-container) {
      width: 100% !important;
      height: 100% !important;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }

    // simple-mind-map SVG 样式
    :deep(.smm-svg) {
      width: 100% !important;
      height: 100% !important;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }

    // 确保 simple-mind-map 的画布占满整个容器
    :deep(.smm-canvas) {
      width: 100% !important;
      height: 100% !important;
      overflow: hidden; // 防止内容溢出
    }
  }
}

.dark {
  .mindmap-toolbar {
    .native-select {
      // 深色主题下的下拉箭头（白色）
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");

      option {
        background: var(--el-bg-color-overlay);
        color: var(--el-text-color-primary);
      }
    }
  }
}

// 右键菜单样式 - 优化边界处理
.context-menu {
  position: fixed;
  z-index: 10000; // 确保在所有内容之上
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  box-shadow: var(--el-box-shadow);
  padding: 16px 0;
  min-width: 200px;
  max-width: 280px;
  max-height: 400px; // 限制最大高度，防止超出视窗
  backdrop-filter: blur(12px);
  pointer-events: auto; // 确保菜单可以交互
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  // 防止在屏幕外时影响滚动条
  overflow: hidden;
  // 确保菜单不会被裁剪
  contain: layout;
  // 防止菜单内容溢出
  box-sizing: border-box;

  .menu-item {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 28px;
    line-height: 28px;
    padding: 0 16px;
    cursor: pointer;
    font-size: 14px;
    color: var(--el-text-color-primary);
    transition: all 0.2s ease;
    white-space: nowrap;

    &:hover {
      background: var(--el-fill-color-light);
    }

    &.danger {
      color: var(--el-color-danger);

      &:hover {
        background: var(--el-color-danger-light-9);
      }
    }

    &.ai-item {
      color: var(--el-color-primary);

      &:hover {
        background: var(--el-color-primary-light-9);
      }
    }

    &.disabled {
      color: var(--el-text-color-disabled);
      cursor: not-allowed;
      pointer-events: none;

      &:hover {
        background: transparent;
      }
    }

    .shortcut {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-left: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .menu-divider {
    height: 1px;
    background: var(--el-border-color-lighter);
    margin: 4px 0;
  }
}

// 主题配置现在通过 simple-mind-map 的 themeConfig 处理
// 保留基本的深色主题样式增强

// 浅色主题样式现在通过 themeConfig 处理

// 响应式设计
@media screen and (max-width: 768px) {
  .mindmap-canvas {
    .mindmap-toolbar {
      padding: 8px 12px;
      flex-direction: column;
      gap: 8px;
      align-items: stretch;

      .toolbar-center,
      .toolbar-right {
        justify-content: center;
      }

      .el-select {
        min-width: unset;
      }
    }
  }

  .context-menu {
    min-width: 160px;

    .menu-item {
      padding: 10px 14px;
      font-size: 16px;
    }
  }
}

// 响应式设计
@media screen and (max-width: 1024px) {
  .mindmap-canvas {
    .mindmap-toolbar {
      .toolbar-right {
        gap: 12px;

        .toolbar-item {
          .toolbar-label {
            display: none;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .mindmap-canvas {
    .mindmap-toolbar {
      flex-direction: column;
      height: auto;
      padding: 12px 16px;
      gap: 12px;

      .toolbar-left,
      .toolbar-right {
        justify-content: center;
      }

      .toolbar-right {
        gap: 8px;
        flex-wrap: wrap;

        .toolbar-item {
          .el-select {
            width: 140px !important;
          }
        }
      }
    }
  }
}

// 全屏模式样式
.mindmap-canvas:fullscreen {
  background: var(--el-bg-color) !important;

  .mindmap-toolbar {
    background: var(--el-bg-color-page) !important;
    border-bottom: 1px solid var(--el-border-color-light) !important;
  }

  .mindmap-container {
    background: var(--el-fill-color-blank) !important;
  }
}

// Webkit全屏样式
.mindmap-canvas:-webkit-full-screen {
  background: var(--el-bg-color) !important;

  .mindmap-toolbar {
    background: var(--el-bg-color-page) !important;
    border-bottom: 1px solid var(--el-border-color-light) !important;
  }

  .mindmap-container {
    background: var(--el-fill-color-blank) !important;
  }
}

// Mozilla全屏样式
.mindmap-canvas:-moz-full-screen {
  background: var(--el-bg-color) !important;

  .mindmap-toolbar {
    background: var(--el-bg-color-page) !important;
    border-bottom: 1px solid var(--el-border-color-light) !important;
  }

  .mindmap-container {
    background: var(--el-fill-color-blank) !important;
  }
}

// MS全屏样式
.mindmap-canvas:-ms-fullscreen {
  background: var(--el-bg-color) !important;

  .mindmap-toolbar {
    background: var(--el-bg-color-page) !important;
    border-bottom: 1px solid var(--el-border-color-light) !important;
  }

  .mindmap-container {
    background: var(--el-fill-color-blank) !important;
  }
}

/* 移除所有影响节点样式的CSS，让simple-mind-map完全通过自身配置控制样式 */
</style>
