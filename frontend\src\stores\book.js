import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import EncryptionService from '@/utils/encryption'
import {
  handleApiCall,
  handleBatchApiCalls,
  parseApiResponse,
  isApiSuccess,
  getApiData,
  getApiError
} from '@/utils/apiUtils'

// 存储当前会话中书籍密码的缓存
const sessionBookPasswords = new Map()

export const useBookStore = defineStore('book', () => {
  // 状态
  const bookList = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Actions
  const loadBooks = async () => {
    try {
      loading.value = true
      const data = await handleApiCall(
        () => window.pywebview.api.book_controller.list_books(),
        {
          errorMessage: '获取书籍列表失败',
          showError: true
        }
      )
      bookList.value = data || []
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const createBook = async (bookData) => {
    try {
      // 检查是否需要加密
      if (bookData.password) {
        // 生成加密所需的数据
        const { checksum, salt, iv } = EncryptionService.generateChecksum(bookData.password)
        
        // 更新书籍数据，添加加密标记
        bookData = {
          ...bookData,
          encrypted: true,
          salt,
          iv,
          checksum
        }
        
        // 存储当前会话的密码
        const temporaryPassword = bookData.password
        delete bookData.password // 不传递密码到后端
        
        const response = await window.pywebview.api.book_controller.create_book(bookData)
        const result = typeof response === 'string' ? JSON.parse(response) : response
        
        if (result && result.status === 'success') {
          // 保存会话密码
          sessionBookPasswords.set(result.data.id, temporaryPassword)
          // 重新加载书籍列表
          await loadBooks()
          return true
        } else {
          throw new Error(result?.message || '创建失败')
        }
      } else {
        // 普通书籍创建流程
        const response = await window.pywebview.api.book_controller.create_book(bookData)
        const result = typeof response === 'string' ? JSON.parse(response) : response
        if (result && result.status === 'success') {
          // 重新加载书籍列表
          await loadBooks()
          return true
        } else {
          throw new Error(result?.message || '创建失败')
        }
      }
    } catch (error) {
      console.error('创建失败:', error)
      error.value = error.message
      ElMessage.error('创建失败：' + error.message)
      return false
    }
  }

  const removeBook = async (book) => {
    try {
      const response = await window.pywebview.api.book_controller.delete_book(book.id)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      if (result && result.status === 'success') {
        // 如果删除成功，清除会话中的密码
        sessionBookPasswords.delete(book.id)

        // 立即从本地状态中移除书籍，提供即时反馈
        const bookIndex = bookList.value.findIndex(b => b.id === book.id)
        if (bookIndex !== -1) {
          bookList.value.splice(bookIndex, 1)
        }

        // 重新加载书籍列表
        try {
          await loadBooks()
        } catch (err) {
          console.warn('重新加载书籍列表失败，但删除操作已成功:', err)
        }

        return true
      } else {
        throw new Error(result?.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      error.value = error.message
      ElMessage.error('删除失败：' + error.message)
      return false
    }
  }

  const updateBook = async (bookid, book) => {
    try {
      // 确保book是对象而非字符串
      const bookData = typeof book === 'string' ? JSON.parse(book) : book;
      
      const response = await window.pywebview.api.book_controller.update_book(bookid, bookData)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 重新加载书籍列表
        await loadBooks()
        return true
      } else {
        throw new Error(result?.message || '更新失败')
      }
    } catch (error) {
      console.error('更新失败:', error)
      error.value = error.message
      ElMessage.error('更新失败：' + error.message)
      return false
    }
  }

  // 获取书籍的加密状态
  const isBookEncrypted = (bookId) => {
    const book = bookList.value.find(b => b.id === bookId)
    return book && book.encrypted === true
  }

  // 设置书籍密码（加密现有书籍）
  const setBookPassword = async (bookId, password) => {
    try {
      // 获取书籍信息
      const book = bookList.value.find(b => b.id === bookId)
      if (!book) throw new Error('书籍不存在')
      
      // 生成加密所需的数据
      const { checksum, salt, iv } = EncryptionService.generateChecksum(password)
      
      // 更新书籍数据，添加加密标记
      const updatedBook = {
        ...book,
        encrypted: true,
        salt,
        iv,
        checksum
      }
      
      // 更新书籍信息
      const result = await updateBook(bookId, updatedBook)
      if (result) {
        // 保存会话密码
        sessionBookPasswords.set(bookId, password)
        
        // 加密书籍所有章节
        await encryptAllChapters(bookId, password, salt, iv)
        return true
      }
      return false
    } catch (error) {
      console.error('设置密码失败:', error)
      ElMessage.error('设置密码失败：' + error.message)
      return false
    }
  }

  // 移除书籍密码（解密现有书籍）
  const removeBookPassword = async (bookId, password) => {
    try {
      // 获取书籍信息
      const book = bookList.value.find(b => b.id === bookId)
      if (!book) throw new Error('书籍不存在')
      
      // 验证密码
      if (!EncryptionService.verifyPassword(password, book.checksum, book.salt, book.iv)) {
        throw new Error('密码错误')
      }
      
      // 解密所有章节
      await decryptAllChapters(bookId, password, book.salt, book.iv)
      
      // 更新书籍信息，移除加密标记
      const { encrypted, checksum, salt, iv, ...cleanBook } = book
      
      // 更新书籍信息
      const result = await updateBook(bookId, cleanBook)
      if (result) {
        // 清除会话密码
        sessionBookPasswords.delete(bookId)
        return true
      }
      return false
    } catch (error) {
      console.error('移除密码失败:', error)
      ElMessage.error('移除密码失败：' + error.message)
      return false
    }
  }

  // 验证书籍密码并保存到会话
  const verifyBookPassword = async (bookId, password) => {
    try {
      // 获取书籍信息
      const book = bookList.value.find(b => b.id === bookId)
      if (!book) throw new Error('书籍不存在')
      
      // 验证密码
      if (!book.encrypted) return true // 非加密书籍
      
      const isValid = EncryptionService.verifyPassword(
        password,
        book.checksum,
        book.salt,
        book.iv
      )
      
      if (isValid) {
        // 密码正确，保存到会话
        sessionBookPasswords.set(bookId, password)
        return true
      }
      
      return false
    } catch (error) {
      console.error('验证密码失败:', error)
      return false
    }
  }

  // 获取当前会话中保存的书籍密码
  const getBookPassword = (bookId) => {
    return sessionBookPasswords.get(bookId) || null
  }

  // 加密章节内容
  const encryptChapterContent = async (bookId, volumeId, chapterId, content, password) => {
    try {
      // 获取书籍信息
      const book = bookList.value.find(b => b.id === bookId)
      if (!book || !book.encrypted) return content
      
      // 使用密码或会话中的密码
      const bookPassword = password || sessionBookPasswords.get(bookId)
      if (!bookPassword) throw new Error('需要密码')
      
      // 加密内容
      const { content: encryptedContent } = EncryptionService.encrypt(
        content,
        bookPassword,
        book.salt,
        book.iv
      )
      
      return encryptedContent
    } catch (error) {
      console.error('加密章节失败:', error)
      throw error
    }
  }

  // 添加清除密码缓存方法
  const clearBookPassword = (bookId) => {
    if (bookId) {
      // 清除指定书籍的密码
      sessionBookPasswords.delete(bookId)
    } else {
      // 清除所有书籍密码
      sessionBookPasswords.clear()
    }
  }

  // 强制要求密码验证，忽略已有缓存
  const forcePromptBookPassword = async (bookId) => {
    try {
      const book = bookList.value.find(b => b.id === bookId)
      if (!book) throw new Error('书籍不存在')
      
      // 非加密书籍不需要密码
      if (!book.encrypted) return true
      
      // 弹出输入密码对话框
      const { value: password } = await ElMessageBox.prompt('请输入书籍密码', '密码验证', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputValidator: val => {
          if (!val) return '密码不能为空'
          return true
        }
      })
      
      // 验证密码
      const isValid = EncryptionService.verifyPassword(
        password,
        book.checksum,
        book.salt,
        book.iv
      )
      
      if (isValid) {
        // 密码正确，保存到会话
        sessionBookPasswords.set(bookId, password)
        return true
      } else {
        ElMessage.error('密码错误')
        return false
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('密码验证失败:', error)
        ElMessage.error('密码验证失败')
      }
      return false
    }
  }

  // 修改解密章节内容方法，添加forcePrompt参数
  const decryptChapterContent = async (bookId, volumeId, chapterId, encryptedContent, forcePrompt = false) => {
    try {
      // 获取书籍信息
      const book = bookList.value.find(b => b.id === bookId)
      if (!book || !book.encrypted) return encryptedContent
      
      // 使用会话中的密码或强制验证
      let password = sessionBookPasswords.get(bookId)
      
      // 如果需要强制验证或没有缓存密码
      if (forcePrompt || !password) {
        // 需要输入密码
        const result = forcePrompt 
          ? await forcePromptBookPassword(bookId) 
          : await promptBookPassword(bookId)
          
        if (!result) throw new Error('需要密码')
        
        // 重新获取会话中的密码
        password = sessionBookPasswords.get(bookId)
        if (!password) throw new Error('需要密码')
      }
      
      // 解密内容
      const decryptedContent = EncryptionService.decrypt(
        encryptedContent,
        password,
        book.salt,
        book.iv
      )
      
      if (decryptedContent === null) {
        // 如果解密失败，清除密码缓存并抛出错误
        clearBookPassword(bookId)
        throw new Error('解密失败，可能密码错误')
      }
      
      return decryptedContent
    } catch (error) {
      console.error('解密章节失败:', error)
      throw error
    }
  }

  // 提示用户输入书籍密码的方法
  const promptBookPassword = async (bookId) => {
    try {
      const book = bookList.value.find(b => b.id === bookId)
      if (!book) throw new Error('书籍不存在')
      
      // 如果已有会话密码，直接返回
      if (sessionBookPasswords.has(bookId)) {
        return true
      }
      
      // 弹出输入密码对话框
      const { value: password } = await ElMessageBox.prompt('请输入书籍密码', '密码验证', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputValidator: val => {
          if (!val) return '密码不能为空'
          return true
        }
      })
      
      // 验证密码
      const isValid = await verifyBookPassword(bookId, password)
      if (!isValid) {
        ElMessage.error('密码错误')
        return false
      }
      
      return true
    } catch (error) {
      if (error !== 'cancel') {
        console.error('密码验证失败:', error)
        ElMessage.error('密码验证失败')
      }
      return false
    }
  }

  // 遍历所有章节并加密（优化版本）
  const encryptAllChapters = async (bookId, password, salt, iv) => {
    try {
      // 获取书籍的所有卷
      const volumes = await handleApiCall(
        () => window.pywebview.api.book_controller.get_volumes(bookId),
        { errorMessage: '获取卷失败' }
      )

      if (!volumes || volumes.length === 0) {
        return true
      }

      // 收集所有需要加密的章节
      const chapterTasks = []

      for (const volume of volumes) {
        if (!volume.chapters) continue

        for (const chapter of volume.chapters) {
          chapterTasks.push(async () => {
            // 获取章节内容
            const chapterData = await handleApiCall(
              () => window.pywebview.api.book_controller.get_chapter(
                bookId,
                volume.id,
                chapter.id
              ),
              { showError: false }
            )

            if (!chapterData || !chapterData.content) return

            // 加密章节内容
            const { content: encryptedContent } = EncryptionService.encrypt(
              chapterData.content,
              password,
              salt,
              iv
            )

            // 更新章节内容
            return handleApiCall(
              () => window.pywebview.api.book_controller.update_chapter(
                bookId,
                volume.id,
                chapter.id,
                {
                  ...chapterData,
                  content: encryptedContent
                }
              ),
              { showError: false }
            )
          })
        }
      }

      // 批量处理章节（并发执行）
      const { errors } = await handleBatchApiCalls(chapterTasks, {
        concurrent: true,
        showError: false
      })

      const errorCount = errors.filter(e => e).length
      if (errorCount > 0) {
        console.warn(`${errorCount} 个章节加密失败`)
      }

      return true
    } catch (error) {
      console.error('加密所有章节失败:', error)
      throw error
    }
  }

  // 遍历所有章节并解密
  const decryptAllChapters = async (bookId, password, salt, iv) => {
    try {
      // 获取书籍的所有卷
      const response = await window.pywebview.api.book_controller.get_volumes(bookId)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result.status !== 'success') {
        throw new Error(result.message || '获取卷失败')
      }
      
      const volumes = result.data || []
      
      // 遍历所有卷和章节
      for (const volume of volumes) {
        if (!volume.chapters) continue
        
        for (const chapter of volume.chapters) {
          // 获取章节内容
          const chapterResponse = await window.pywebview.api.book_controller.get_chapter(
            bookId,
            volume.id,
            chapter.id
          )
          
          const chapterResult = typeof chapterResponse === 'string' 
            ? JSON.parse(chapterResponse) 
            : chapterResponse
            
          if (chapterResult.status !== 'success') continue
          
          const chapterData = chapterResult.data
          
          // 解密章节内容
          if (chapterData.content) {
            try {
              const decryptedContent = EncryptionService.decrypt(
                chapterData.content,
                password,
                salt,
                iv
              )
              
              if (decryptedContent !== null) {
                // 更新章节内容为解密后的内容
                await window.pywebview.api.book_controller.update_chapter(
                  bookId,
                  volume.id,
                  chapter.id,
                  {
                    ...chapterData,
                    content: decryptedContent
                  }
                )
              }
            } catch (decryptError) {
              console.error('解密章节失败:', decryptError)
              // 继续处理下一个章节
            }
          }
        }
      }
      
      return true
    } catch (error) {
      console.error('解密所有章节失败:', error)
      throw error
    }
  }

  return {
    // 状态
    bookList,
    loading,
    error,

    // Actions
    loadBooks,
    createBook,
    removeBook,
    updateBook,
    
    // 加密相关
    isBookEncrypted,
    setBookPassword,
    removeBookPassword,
    verifyBookPassword,
    getBookPassword,
    encryptChapterContent,
    decryptChapterContent,
    promptBookPassword,
    forcePromptBookPassword,
    clearBookPassword
  }
})
