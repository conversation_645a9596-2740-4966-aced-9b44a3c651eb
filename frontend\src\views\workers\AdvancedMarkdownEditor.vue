<template>
  <div class="markdown-editor-workspace" :class="{ 'dark': isDarkTheme }">
    <!-- 顶部工具栏 -->
    <div class="workspace-header">
      <div class="header-left">
        <!-- 侧边栏切换 -->
        <el-button
          @click="toggleSidebar"
          :icon="Menu"
          size="default"
          text
          class="sidebar-toggle"
          title="切换侧边栏"
        />

        <!-- 文档标题 -->
        <div class="document-info" v-if="currentDocument">
          <span class="document-title">{{ currentDocument.title }}</span>
          <span class="document-status" v-if="hasUnsavedChanges">•</span>
        </div>
        <span v-else class="no-document">未选择文档</span>
      </div>

      <div class="header-center">
        <!-- 模式切换 -->
        <el-segmented
          v-model="editorMode"
          :options="modeOptions"
          size="default"
        />
      </div>

      <div class="header-right">
        <!-- 字体大小控制 -->
        <el-dropdown trigger="click" placement="bottom-end">
          <el-button size="small" text>
            <el-icon><Setting /></el-icon>
            字体
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <div class="font-control-panel" @click.stop>
                <div class="font-control-header">
                  <span class="panel-title">字体大小设置</span>
                </div>

                <el-dropdown-item divided @click.stop>
                  <div class="font-control-item" @click.stop>
                    <span>编辑器:</span>
                    <el-input-number
                      v-model="editFontSize"
                      :min="10"
                      :max="24"
                      size="small"
                      style="width: 80px"
                      @click.stop
                    />
                  </div>
                </el-dropdown-item>



                <el-dropdown-item v-if="editorMode === 'mindmap'" @click.stop>
                  <div class="font-control-item" @click.stop>
                    <span>思维导图:</span>
                    <el-input-number
                      v-model="mindmapFontSize"
                      :min="8"
                      :max="20"
                      size="small"
                      style="width: 80px"
                      @click.stop
                    />
                  </div>
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 操作按钮 -->
        <el-button @click="handleSaveClick" :icon="Document" :disabled="!currentDocument" size="default" type="primary">
          保存
        </el-button>
      </div>
    </div>

    <!-- 主要工作区 -->
    <div class="workspace-body">
      <!-- 侧边栏 -->
      <div v-show="showSidebar" class="sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <div class="sidebar-title-area">
            <el-icon class="sidebar-icon"><FolderOpened /></el-icon>
            <span class="sidebar-title" v-show="!sidebarCollapsed">资源管理器</span>
          </div>
          <div class="sidebar-actions" v-show="!sidebarCollapsed">
            
              <el-button
                @click="createNewDocument"
                :icon="Plus"
                size="small"
                text
                class="action-btn"
              />

            

          </div>
        </div>

        <div class="sidebar-content" v-show="!sidebarCollapsed">
          <FileManager
            ref="fileManagerRef"
            :selected-file="selectedFile"
            :last-selected-directory="lastSelectedDirectory"
            @file-selected="onFileSelected"
            @file-opened="onFileOpened"
            @directory-selected="onDirectorySelected"
            @create-document="createNewDocument"
          />
        </div>

        <!-- 折叠状态下的快捷操作 -->
        <div class="sidebar-collapsed-actions" v-show="sidebarCollapsed">
          <div class="collapsed-btn-group">
              <el-button
                @click="createNewDocument"
                :icon="Plus"
                size="small"
                text
                class="expand-btn"
              />

          </div>
        </div>
      </div>

      <!-- 主编辑区 -->
      <div class="main-editor" :class="{
        'full-width': !showSidebar,
        'sidebar-collapsed': showSidebar && sidebarCollapsed
      }">
        <AdvancedMarkdownEditor
          ref="advancedMarkdownEditorRef"
          v-if="currentDocument"
          v-model="currentDocument.content"
          :title="currentDocument.title"
          :mode="editorMode"
          :edit-font-size="editFontSize"
          :mindmap-font-size="mindmapFontSize"
          @save="handleSaveFromEditor"
          @content-change="onContentChange"
        />

        <div v-else class="welcome-screen">
          <div class="welcome-content">
            <div class="welcome-icon">
              <el-icon size="80" color="var(--el-color-primary)">
                <Edit />
              </el-icon>
            </div>

            <h2 class="welcome-title">欢迎使用高级Markdown编辑器</h2>
            <p class="welcome-subtitle">功能强大的Markdown编辑器，支持编辑模式和AI思维导图模式</p>

            <div class="welcome-actions">
              <el-button type="primary" @click="createNewDocument" :icon="Plus" size="large">
                创建新文档
              </el-button>
              <el-button @click="toggleSidebar" v-if="!showSidebar" :icon="FolderOpened" size="large">
                打开文件面板
              </el-button>
            </div>

            <div class="welcome-features">
              <div class="feature-item">
                <el-icon><Edit /></el-icon>
                <span>强大的编辑功能</span>
              </div>
              <div class="feature-item">
                <el-icon><View /></el-icon>
                <span>实时预览</span>
              </div>
              <div class="feature-item">
                <el-icon><Share /></el-icon>
                <span>AI思维导图</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="workspace-footer" v-if="currentDocument">
      <div class="footer-left">
        <span class="status-item">
          <el-icon><Document /></el-icon>
          {{ currentDocument.title }}
        </span>
        <span class="status-item">
          字符数: {{ contentStats.characters }}
        </span>
        <span class="status-item">
          字数: {{ contentStats.words }}
        </span>
      </div>

      <div class="footer-right">
        <span class="status-item">
          {{ editorMode === 'edit' ? '编辑模式' : '思维导图模式' }}
        </span>
        <span class="status-item" v-if="currentDocument.filePath">
          {{ getFileName(currentDocument.filePath) }}
        </span>
      </div>
    </div>

    <!-- 新建文档对话框 -->
    <el-dialog v-model="newDocDialogVisible" title="新建文档" width="400px">
      <el-form :model="newDocForm" label-width="80px">
        <el-form-item label="文档标题" required>
          <el-input 
            v-model="newDocForm.title" 
            placeholder="输入文档标题"
            @keyup.enter="confirmCreateDocument"
          />
        </el-form-item>
        <el-form-item label="初始内容">
          <el-select v-model="newDocForm.template" placeholder="选择模板">
            <el-option label="空白文档" value="blank" />
            <el-option label="技术文档模板" value="tech" />
            <el-option label="项目计划模板" value="project" />
            <el-option label="学习笔记模板" value="notes" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="newDocDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCreateDocument">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  FolderOpened,
  Document,
  Menu,
  Plus,
  Setting
} from '@element-plus/icons-vue'

import FileManager from '@/components/FileManager.vue'
import AdvancedMarkdownEditor from '@/components/AdvancedMarkdownEditor.vue'
import { nanoid } from 'nanoid'
import { useConfigStore } from '@/stores/config'

// Store
const configStore = useConfigStore()

// 响应式数据
const documents = ref([])
const currentDocumentId = ref('')
const newDocDialogVisible = ref(false)
const showSidebar = ref(true)
const sidebarCollapsed = ref(false)
const selectedFile = ref(null)
const editorMode = ref('edit')
const hasUnsavedChanges = ref(false)

// 自动保存相关
const autoSaveTimer = ref(null)
const autoSaveEnabled = ref(true)
const autoSaveInterval = ref(30000) // 默认30秒

// 组件引用
const fileManagerRef = ref(null)
const advancedMarkdownEditorRef = ref(null)

// 配置相关的响应式数据
const editFontSize = ref(14)
const mindmapFontSize = ref(12)
const lastSelectedDirectory = ref('')

const newDocForm = ref({
  title: '',
  template: 'blank'
})

// 模式选项
const modeOptions = [
  { label: '编辑', value: 'edit' },
  { label: '思维导图', value: 'mindmap' }
]

// 计算属性
const isDarkTheme = computed(() => configStore.theme === 'dark')

const currentDocument = computed(() => {
  return documents.value.find(doc => doc.id === currentDocumentId.value)
})

const contentStats = computed(() => {
  if (!currentDocument.value?.content) {
    return { characters: 0, words: 0 }
  }

  const content = currentDocument.value.content
  const characters = content.length
  const words = content.trim() ? content.trim().split(/\s+/).length : 0

  return { characters, words }
})

// 文档模板
const templates = {
  blank: '',
  tech: `# 技术文档

## 概述

简要描述项目或技术的目标和用途。

## 架构设计

### 系统架构

描述整体系统架构。

### 技术栈

- 前端：
- 后端：
- 数据库：

## 实现细节

### 核心功能

#### 功能1

详细描述功能实现。

#### 功能2

详细描述功能实现。

## API文档

### 接口列表

| 接口 | 方法 | 描述 |
|------|------|------|
| /api/example | GET | 示例接口 |

## 部署说明

### 环境要求

- Node.js >= 16
- Python >= 3.8

### 部署步骤

1. 克隆代码
2. 安装依赖
3. 配置环境
4. 启动服务

## 常见问题

### Q: 如何解决问题A？

A: 解决方案描述。
`,
  project: `# 项目计划

## 项目概述

### 项目目标

明确项目要达成的目标。

### 项目范围

定义项目的边界和范围。

## 项目时间线

### 阶段1：需求分析（Week 1-2）

- [ ] 收集用户需求
- [ ] 分析技术可行性
- [ ] 制定技术方案

### 阶段2：设计阶段（Week 3-4）

- [ ] UI/UX设计
- [ ] 系统架构设计
- [ ] 数据库设计

### 阶段3：开发阶段（Week 5-8）

- [ ] 前端开发
- [ ] 后端开发
- [ ] 接口联调

### 阶段4：测试阶段（Week 9-10）

- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户验收测试

### 阶段5：部署上线（Week 11）

- [ ] 生产环境部署
- [ ] 性能优化
- [ ] 监控配置

## 资源分配

### 团队成员

| 姓名 | 角色 | 职责 |
|------|------|------|
| 张三 | 项目经理 | 项目管理 |
| 李四 | 前端开发 | 前端实现 |

### 预算规划

- 人力成本：
- 硬件成本：
- 其他费用：

## 风险管理

### 技术风险

- 风险1：描述及应对措施
- 风险2：描述及应对措施

### 进度风险

- 风险1：描述及应对措施
- 风险2：描述及应对措施
`,
  notes: `# 学习笔记

## 学习目标

明确本次学习要达成的目标。

## 核心概念

### 概念1

详细解释概念1的定义和重要性。

### 概念2

详细解释概念2的定义和重要性。

## 重点知识

### 知识点1

#### 定义

给出准确的定义。

#### 示例

提供具体的示例说明。

#### 应用场景

说明在什么情况下使用。

### 知识点2

#### 定义

给出准确的定义。

#### 示例

提供具体的示例说明。

## 实践练习

### 练习1

描述练习内容和要求。

### 练习2

描述练习内容和要求。

## 总结

### 关键收获

- 收获1
- 收获2
- 收获3

### 待深入学习

- 需要进一步学习的内容1
- 需要进一步学习的内容2

## 参考资料

- [资料1](链接)
- [资料2](链接)
`
}

// 监听器
watch(editFontSize, (newSize) => {
  updateFontSize('edit', newSize)
})

watch(mindmapFontSize, (newSize) => {
  updateFontSize('mindmap', newSize)
})

watch(showSidebar, (newValue) => {
  updatePreferences({ showSidebar: newValue })
})

watch(sidebarCollapsed, (newValue) => {
  updatePreferences({ sidebarCollapsed: newValue })
})

watch(editorMode, (newValue) => {
  updatePreferences({ defaultMode: newValue })

  // 当切换到思维导图模式时，确保有文档数据
  if (newValue === 'mindmap') {
    handleMindmapModeSwitch()
  }
})

// 监听当前文档变化，管理自动保存
watch(currentDocumentId, (newId, oldId) => {
  // 停止旧文档的自动保存
  stopAutoSave()

  // 如果文档发生了切换（不是初始化），确保回到编辑模式并清理数据
  if (oldId && newId !== oldId) {
    console.log('检测到文档切换，从', oldId, '到', newId, '，重置为编辑模式')
    editorMode.value = 'edit'
    hasUnsavedChanges.value = false

    // 清理编辑器数据
    if (advancedMarkdownEditorRef.value) {
      try {
        advancedMarkdownEditorRef.value.clearEditorData()
      } catch (error) {
        console.warn('清理编辑器数据失败:', error)
      }
    }
  }

  // 如果有新文档，启动自动保存
  if (newId && autoSaveEnabled.value) {
    startAutoSave()
  }
})

// 监听自动保存设置变化
watch(autoSaveEnabled, (enabled) => {
  if (enabled && currentDocument.value) {
    startAutoSave()
  } else {
    stopAutoSave()
  }
})

watch(autoSaveInterval, () => {
  if (autoSaveEnabled.value && currentDocument.value) {
    resetAutoSaveTimer()
  }
})

// 键盘快捷键处理
const handleKeyDown = (event) => {
  // Ctrl+S 保存
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    if (currentDocument.value) {
      saveDocument()
    }
  }
}

// 生命周期
onMounted(async () => {
  await loadEditorConfig()
  await loadDocuments()

  // 如果有保存的目录，自动加载
  if (lastSelectedDirectory.value) {
    // 这里可以触发FileManager加载目录
    console.log('自动加载目录:', lastSelectedDirectory.value)
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
})

onBeforeUnmount(() => {
  // 清理自动保存计时器
  stopAutoSave()

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown)
})

// 配置管理方法
const loadEditorConfig = async () => {
  try {
    await configStore.loadConfig()
    const config = configStore.markdownEditor

    if (config) {
      // 加载字体大小配置
      editFontSize.value = config.fontSize?.edit || 14
      mindmapFontSize.value = config.fontSize?.mindmap || 12

      // 加载其他偏好设置
      showSidebar.value = config.preferences?.showSidebar !== false
      sidebarCollapsed.value = config.preferences?.sidebarCollapsed || false
      // 确保模式有效，默认为编辑模式，如果是已移除的 'preview' 模式或思维导图模式则切换到 'edit'
      const defaultMode = config.preferences?.defaultMode || 'edit'
      // 启动时始终使用编辑模式，不自动进入思维导图模式
      editorMode.value = (defaultMode === 'preview' || defaultMode === 'mindmap') ? 'edit' : defaultMode

      // 加载自动保存设置
      autoSaveEnabled.value = config.preferences?.autoSave !== false
      autoSaveInterval.value = config.preferences?.autoSaveInterval || 30000

      // 加载最后选择的目录
      lastSelectedDirectory.value = config.lastSelectedDirectory || ''
    }
  } catch (error) {
    console.error('加载编辑器配置失败:', error)
  }
}

const saveEditorConfig = async (configUpdate) => {
  try {
    await configStore.updateConfigItem('markdownEditor', {
      ...configStore.markdownEditor,
      ...configUpdate
    })
  } catch (error) {
    console.error('保存编辑器配置失败:', error)
  }
}

const updateFontSize = async (area, size) => {
  const config = {
    fontSize: {
      ...configStore.markdownEditor?.fontSize,
      [area]: size
    }
  }
  await saveEditorConfig(config)
}

const updateLastDirectory = async (directory) => {
  await saveEditorConfig({ lastSelectedDirectory: directory })
}

const updatePreferences = async (preferences) => {
  const config = {
    preferences: {
      ...configStore.markdownEditor?.preferences,
      ...preferences
    }
  }
  await saveEditorConfig(config)
}

// 方法
const loadDocuments = async () => {
  try {
    // 这里应该从后端API加载文档列表
    // 暂时使用本地存储模拟
    const savedDocs = localStorage.getItem('advanced-markdown-docs')
    if (savedDocs) {
      documents.value = JSON.parse(savedDocs)

      // 如果有保存的文档，自动打开第一个
      if (documents.value.length > 0) {
        currentDocumentId.value = documents.value[0].id
      }
    } else {
      // 不创建默认文档，保持空状态，显示欢迎界面
      documents.value = []
      currentDocumentId.value = null
    }
  } catch (error) {
    console.error('加载文档失败:', error)
    ElMessage.error('加载文档失败')
    // 出错时也保持空状态
    documents.value = []
    currentDocumentId.value = null
  }
}

const saveDocuments = () => {
  try {
    localStorage.setItem('advanced-markdown-docs', JSON.stringify(documents.value))
  } catch (error) {
    console.error('保存文档失败:', error)
    ElMessage.error('保存文档失败')
  }
}

const loadDocument = (docId) => {
  const doc = documents.value.find(d => d.id === docId)
  if (doc) {
    currentDocumentId.value = docId
  }
}

const createNewDocument = () => {
  newDocForm.value = {
    title: '',
    template: 'blank'
  }
  newDocDialogVisible.value = true
}

const confirmCreateDocument = async () => {
  if (!newDocForm.value.title.trim()) {
    ElMessage.warning('请输入文档标题')
    return
  }

  try {
    // 确保有选择的目录
    if (!lastSelectedDirectory.value) {
      ElMessage.warning('请先选择一个目录来保存文档')
      return
    }

    // 生成文件名（确保以.md结尾）
    let fileName = newDocForm.value.title.trim()
    if (!fileName.toLowerCase().endsWith('.md')) {
      fileName += '.md'
    }

    // 构建完整的文件路径（使用正确的路径分隔符）
    const separator = lastSelectedDirectory.value.includes('\\') ? '\\' : '/'
    let filePath = `${lastSelectedDirectory.value}${separator}${fileName}`

    // 检查文件是否已存在，如果存在则添加数字后缀
    let counter = 1
    let originalFileName = fileName
    while (true) {
      try {
        const checkResponse = await window.pywebview.api.get_file_info(filePath)
        const checkResult = typeof checkResponse === 'string' ? JSON.parse(checkResponse) : checkResponse

        if (checkResult.status === 'error') {
          // 文件不存在，可以使用这个路径
          break
        } else {
          // 文件存在，生成新的文件名
          const nameWithoutExt = originalFileName.replace('.md', '')
          fileName = `${nameWithoutExt}_${counter}.md`
          filePath = `${lastSelectedDirectory.value}${separator}${fileName}`
          counter++
        }
      } catch (error) {
        // 如果检查失败，假设文件不存在
        break
      }
    }

    // 获取模板内容
    const content = templates[newDocForm.value.template] || ''

    // 创建文件到文件系统
    const response = await window.pywebview.api.write_file(filePath, content)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      // 创建文档对象
      const newDoc = {
        id: nanoid(),
        title: fileName.replace('.md', ''),
        content: content,
        filePath: filePath,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // 添加到文档列表
      documents.value.unshift(newDoc)
      // 使用 nextTick 确保 DOM 更新完成后再切换文档
      await nextTick()
      currentDocumentId.value = newDoc.id

      // 确保新文档创建后使用编辑模式
      editorMode.value = 'edit'

      saveDocuments()

      // 刷新文件列表
      if (fileManagerRef.value) {
        await fileManagerRef.value.refreshDirectory()
      }

      newDocDialogVisible.value = false
      ElMessage.success('文档创建成功，已切换到编辑模式')
    } else {
      ElMessage.error('创建文档失败: ' + result.message)
    }
  } catch (error) {
    console.error('创建文档失败:', error)
    ElMessage.error('创建文档失败: ' + error.message)
  }
}

// 处理保存按钮点击
const handleSaveClick = () => {
  if (!currentDocument.value) return

  // 调用保存方法，不传递docData参数，让方法使用当前文档内容
  saveDocument()
}

// 处理来自编辑器组件的保存事件
const handleSaveFromEditor = (docData) => {
  // 编辑器组件传递了文档数据，直接使用
  saveDocument(docData)
}

const saveDocument = async (docData) => {
  if (!currentDocument.value) return

  try {
    // 如果有文件路径，保存到文件系统
    if (currentDocument.value.filePath) {
      // 确保内容不为空
      const contentToSave = docData ? docData.content : currentDocument.value.content
      const finalContent = contentToSave || '' // 如果内容为null/undefined，使用空字符串

      const response = await window.pywebview.api.write_file(
        currentDocument.value.filePath,
        finalContent
      )
      const result = typeof response === 'string' ? JSON.parse(response) : response

      if (result.status === 'success') {
        // 更新文档信息
        if (docData) {
          currentDocument.value.title = docData.title
          currentDocument.value.content = docData.content
        }
        currentDocument.value.updatedAt = new Date()
        hasUnsavedChanges.value = false

        // 同时保存到localStorage作为备份
        saveDocuments()
        ElMessage.success('文件保存成功')
      } else {
        ElMessage.error('保存文件失败: ' + result.message)
      }
    } else {
      // 如果没有文件路径，只保存到localStorage（新建文档的情况）
      if (docData) {
        currentDocument.value.title = docData.title
        currentDocument.value.content = docData.content
      }
      currentDocument.value.updatedAt = new Date()
      hasUnsavedChanges.value = false

      saveDocuments()
      ElMessage.success('文档保存成功')
    }
  } catch (error) {
    console.error('保存文档失败:', error)
    ElMessage.error('保存文档失败: ' + error.message)
  }
}



// 界面控制方法
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}





const onContentChange = () => {
  hasUnsavedChanges.value = true

  // 重置自动保存计时器
  if (autoSaveEnabled.value && currentDocument.value) {
    resetAutoSaveTimer()
  }
}

// 自动保存相关方法
const startAutoSave = () => {
  if (!autoSaveEnabled.value || !currentDocument.value) return

  autoSaveTimer.value = setTimeout(() => {
    if (hasUnsavedChanges.value && currentDocument.value) {
      autoSaveDocument()
    }
  }, autoSaveInterval.value)
}

const resetAutoSaveTimer = () => {
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }
  startAutoSave()
}

const stopAutoSave = () => {
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
    autoSaveTimer.value = null
  }
}

const autoSaveDocument = async () => {
  if (!currentDocument.value || !hasUnsavedChanges.value) return

  try {
    await saveDocument()
    console.log('自动保存完成')
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

// 处理思维导图模式切换
const handleMindmapModeSwitch = () => {
  // 如果没有当前文档，尝试创建一个示例文档
  if (!currentDocument.value) {
    // 如果有文档列表，选择第一个
    if (documents.value.length > 0) {
      currentDocumentId.value = documents.value[0].id
      ElMessage.info('已自动选择第一个文档用于思维导图显示')
      return
    }

    // 如果没有任何文档，创建一个示例文档
    createSampleDocumentForMindmap()
    return
  }

  // 如果当前文档内容为空，提供一些示例内容
  if (!currentDocument.value.content || currentDocument.value.content.trim() === '') {
    ElMessageBox.confirm(
      '当前文档内容为空，思维导图需要有内容才能正常显示。是否要添加一些示例内容？',
      '提示',
      {
        confirmButtonText: '添加示例内容',
        cancelButtonText: '保持空白',
        type: 'info'
      }
    ).then(() => {
      // 添加示例内容
      currentDocument.value.content = getSampleMarkdownContent()
      currentDocument.value.updatedAt = new Date()
      saveDocuments()
      ElMessage.success('已添加示例内容，可以开始使用思维导图功能')
    }).catch(() => {
      // 用户选择保持空白，给出提示
      ElMessage.info('思维导图模式需要有标题结构的内容才能正常显示')
    })
  }
}

// 创建示例文档用于思维导图
const createSampleDocumentForMindmap = () => {
  const sampleDoc = {
    id: Date.now().toString(),
    title: '思维导图示例文档',
    content: getSampleMarkdownContent(),
    createdAt: new Date(),
    updatedAt: new Date()
  }

  documents.value.unshift(sampleDoc)
  currentDocumentId.value = sampleDoc.id
  saveDocuments()
  ElMessage.success('已创建示例文档，可以开始使用思维导图功能')
}

// 获取示例Markdown内容
const getSampleMarkdownContent = () => {
  return `# 思维导图示例

这是一个用于演示思维导图功能的示例文档。

## 主要功能

### 自动解析标题
思维导图会自动解析Markdown中的标题层级，将其转换为可视化的节点结构。

### AI辅助生成
每个节点都支持AI辅助内容生成，帮助您扩展思路。

#### 生成类型
- 扩展内容：为当前节点添加更多详细内容
- 生成子节点：基于当前节点生成下级主题
- 相关内容：生成与当前节点相关的并列内容

## 使用方法

### 基本操作
1. 在编辑模式下编写带有标题结构的内容
2. 切换到思维导图模式查看可视化结构
3. 鼠标悬停在节点上显示AI按钮

### 高级功能
- 支持节点拖拽调整位置
- 支持多种布局样式
- 支持缩放和平移操作

## 开始使用

现在您可以：
- 修改这些内容来创建自己的思维导图
- 使用AI功能扩展节点内容
- 尝试不同的布局样式`
}

const getFileName = (filePath) => {
  return filePath ? filePath.split(/[/\\]/).pop() : ''
}

const onFileSelected = (file) => {
  selectedFile.value = file
  // 自动打开选中的文件
  if (file && !file.is_directory && file.name.toLowerCase().endsWith('.md')) {
    onFileOpened(file)
  }
}

const onDirectorySelected = async (directory) => {
  // 保存选择的目录到配置
  lastSelectedDirectory.value = directory
  await updateLastDirectory(directory)
  console.log('目录已保存到配置:', directory)
}

const onFileOpened = async (file) => {
  if (!file || !file.path) return

  try {
    // 读取文件内容
    const response = await window.pywebview.api.read_file(file.path)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      console.log('开始创建文档对象...')

      // 创建新文档对象
      const newDoc = {
        id: nanoid(),
        title: file.name.replace('.md', ''),
        content: result.data || '',
        filePath: file.path,
        createdAt: new Date(file.modified_time * 1000),
        updatedAt: new Date(file.modified_time * 1000)
      }

    

      // 检查是否已经打开了这个文件
      const existingDoc = documents.value.find(doc => doc.filePath === file.path)
      if (existingDoc) {
        console.log('文件已存在，切换到现有文档')
        currentDocumentId.value = existingDoc.id
        // 确保打开文件时使用编辑模式
        editorMode.value = 'edit'
        // 重置未保存更改标志
        hasUnsavedChanges.value = false
        console.log('文件切换完成，已重置为编辑模式')
        return
      }

      console.log('添加新文档到列表...')
      // 添加到文档列表
      documents.value.unshift(newDoc)
      console.log('文档已添加到列表')

      console.log('设置当前文档ID...')
      // 使用 nextTick 确保 DOM 更新完成后再切换文档
      await nextTick()
      currentDocumentId.value = newDoc.id

      // 确保打开文件时使用编辑模式
      editorMode.value = 'edit'

      // 重置未保存更改标志
      hasUnsavedChanges.value = false

      console.log('当前文档ID已设置:', currentDocumentId.value)
      console.log('文件打开完成，已重置为编辑模式')

      // 不打印整个文档列表，避免控制台卡死
      console.log('文档列表长度:', documents.value.length)
    } else {
      ElMessage.error('读取文件失败: ' + result.message)
    }
  } catch (error) {
    console.error('打开文件失败:', error)
    ElMessage.error('打开文件失败: ' + error.message)
  }
}
</script>

<style lang="scss" scoped>
.markdown-editor-workspace {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 10;

  .header-left {
    .page-title {
      margin: 0 0 4px 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-subtitle {
      margin: 0;
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

.file-panel {
  width: 320px;
  min-width: 280px;
  max-width: 400px;
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  flex-shrink: 0; /* 防止文件面板被压缩 */

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }
}

.editor-area {
  flex: 1;
  min-width: 0; /* 允许编辑器区域收缩 */
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }
}

.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px;

  :deep(.el-empty__description) {
    color: var(--el-text-color-secondary);
  }

  .empty-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .empty-hint {
      margin: 0;
      color: var(--el-text-color-secondary);
      font-size: 14px;
      text-align: center;
      line-height: 1.5;
    }
  }
}

// 深色主题适配
.dark {
  .page-header {
    background: var(--el-bg-color);
    border-bottom-color: var(--el-border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .file-panel,
  .editor-area {
    background: var(--el-bg-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    &:hover {
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
    }
  }

  .page-title {
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-5));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

// 新的工作区样式
.workspace-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  flex-shrink: 0;
  font-size: 14px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    min-width: 0;

    .sidebar-toggle {
      color: var(--el-text-color-regular);
      padding: 8px 12px;
      font-size: 16px;

      &:hover {
        color: var(--el-color-primary);
        background: var(--el-fill-color-light);
      }
    }

    .document-info {
      display: flex;
      align-items: center;
      gap: 6px;
      min-width: 0;

      .document-title {
        font-weight: 500;
        font-size: 15px;
        color: var(--el-text-color-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
      }

      .document-status {
        color: var(--el-color-warning);
        font-weight: bold;
        font-size: 16px;
      }
    }

    .no-document {
      color: var(--el-text-color-placeholder);
      font-style: italic;
    }
  }

  .header-center {
    flex-shrink: 0;

    // 优化分段控制器样式
    :deep(.el-segmented) {
      .el-segmented__item {
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 500;
        min-width: 80px;

        &.is-selected {
          font-weight: 600;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;

    // 优化按钮样式
    :deep(.el-button) {
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 6px;

      &.el-button--primary {
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);

        &:hover {
          box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
        }
      }
    }

    // 优化下拉菜单样式
    :deep(.el-dropdown) {
      .el-button {
        padding: 8px 12px;
        font-size: 14px;
      }
    }
  }
}

.workspace-body {
  flex: 1;
  display: flex;
  overflow: hidden;
  min-height: 0;
}

.sidebar {
  width: 320px;
  min-width: 280px;
  max-width: 400px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.04);

  // 深色主题下的阴影
  .dark & {
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
  }

  &.collapsed {
    width: 48px;
    min-width: 48px;

    .sidebar-header {
      .sidebar-title-area {
        .sidebar-title {
          opacity: 0;
          transform: translateX(-20px);
          transition-delay: 0s;
        }
      }

      .sidebar-actions {
        opacity: 0;
        transform: translateX(20px);
        transition-delay: 0s;
      }
    }

    .sidebar-content {
      opacity: 0;
      transform: translateX(-20px);
      transition-delay: 0s;
    }

    .sidebar-collapsed-actions {
      opacity: 1;
    }
  }

  &:not(.collapsed) {
    .sidebar-header {
      .sidebar-title-area {
        .sidebar-title {
          opacity: 1;
          transform: translateX(0);
          transition-delay: 0.2s;
        }
      }

      .sidebar-actions {
        opacity: 1;
        transform: translateX(0);
        transition-delay: 0.2s;
      }
    }

    .sidebar-content {
      opacity: 1;
      transform: translateX(0);
      transition-delay: 0.2s;
    }

    .sidebar-collapsed-actions {
      opacity: 0;

      .collapsed-btn-group {
        .expand-btn {
          animation: none;
          opacity: 0;
          transform: translateY(10px);
        }
      }
    }
  }

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: linear-gradient(135deg, var(--el-fill-color-blank) 0%, var(--el-fill-color-extra-light) 100%);
    min-height: 56px;
    position: relative;

    // 添加微妙的内阴影
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, var(--el-border-color-light) 50%, transparent 100%);
    }

    // 深色主题优化
    .dark & {
      background: linear-gradient(135deg, var(--el-fill-color-dark) 0%, var(--el-fill-color) 100%);
    }

    .sidebar-title-area {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
      min-width: 0;

      .sidebar-icon {
        color: var(--el-color-primary);
        font-size: 18px;
        flex-shrink: 0;
        transition: all 0.3s ease;
        filter: drop-shadow(0 1px 2px rgba(64, 158, 255, 0.2));
      }

      .sidebar-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        text-transform: uppercase;
        letter-spacing: 0.8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        opacity: 1;
        transform: translateX(0);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .sidebar-actions {
      display: flex;
      align-items: center;
      gap: 6px;
      flex-shrink: 0;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      opacity: 1;
      transform: translateX(0);

      .action-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        color: var(--el-text-color-regular);
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;

        // 添加微妙的背景渐变
        background: linear-gradient(135deg, transparent 0%, var(--el-fill-color-extra-light) 100%);
        border: 1px solid transparent;

        &:hover {
          color: var(--el-color-primary);
          background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
          border-color: var(--el-color-primary-light-7);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
        }

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 1;
    transform: translateX(0);
    padding: 8px;
    background: var(--el-bg-color);

    // 添加内容区域的微妙渐变背景
    background: linear-gradient(180deg, var(--el-bg-color) 0%, var(--el-fill-color-extra-light) 100%);

    // 深色主题优化
    .dark & {
      background: linear-gradient(180deg, var(--el-bg-color) 0%, var(--el-fill-color-dark) 100%);
    }
  }

  .sidebar-collapsed-actions {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 8px;
    width: 48px;
    opacity: 0;
    animation: fadeInCollapsed 0.4s ease 0.2s forwards;
    background: linear-gradient(180deg, var(--el-fill-color-extra-light) 0%, var(--el-bg-color) 100%);

    // 深色主题优化
    .dark & {
      background: linear-gradient(180deg, var(--el-fill-color-dark) 0%, var(--el-bg-color) 100%);
    }

    .collapsed-btn-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      width: 100%;

      .expand-btn {
        width: 36px;
        height: 36px;
        padding: 0;
        color: var(--el-text-color-regular);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        transform: translateY(10px);
        opacity: 0;
        background: linear-gradient(135deg, transparent 0%, var(--el-fill-color-light) 100%);
        border: 1px solid transparent;
        position: relative;
        overflow: hidden;

        &:nth-child(1) {
          animation: slideInUp 0.3s ease 0.3s forwards;
        }

        &:nth-child(2) {
          animation: slideInUp 0.3s ease 0.4s forwards;
        }

        &:hover {
          color: var(--el-color-primary);
          background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
          border-color: var(--el-color-primary-light-7);
          transform: translateY(0) scale(1.1);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }

        &:active {
          transform: translateY(0) scale(1.05);
        }

        .el-icon {
          font-size: 16px;
        }
      }
    }
  }
}

.main-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  background: var(--el-bg-color);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &.full-width {
    width: 100%;
  }

  // 当sidebar折叠时，主编辑区可以使用更多空间
  // flex: 1 会自动处理剩余空间分配，无需额外样式
}

.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .welcome-content {
    text-align: center;
    max-width: 500px;

    .welcome-icon {
      margin-bottom: 24px;
    }

    .welcome-title {
      margin: 0 0 12px 0;
      font-size: 28px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .welcome-subtitle {
      margin: 0 0 32px 0;
      font-size: 16px;
      color: var(--el-text-color-secondary);
      line-height: 1.5;
    }

    .welcome-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-bottom: 40px;
      flex-wrap: wrap;
    }

    .welcome-features {
      display: flex;
      gap: 32px;
      justify-content: center;
      flex-wrap: wrap;

      .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        color: var(--el-text-color-secondary);
        font-size: 14px;

        .el-icon {
          font-size: 24px;
          color: var(--el-color-primary);
        }
      }
    }
  }
}

.workspace-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 28px;
  padding: 0 16px;
  background: var(--el-bg-color-page);
  border-top: 1px solid var(--el-border-color-light);
  font-size: 12px;
  flex-shrink: 0;

  .footer-left,
  .footer-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--el-text-color-secondary);

    .el-icon {
      font-size: 12px;
    }
  }
}

// 字体控制样式
.font-control-panel {
  padding: 8px 0;
  min-width: 200px;

  .font-control-header {
    padding: 8px 16px 4px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    margin-bottom: 4px;

    .panel-title {
      font-size: 13px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.font-control-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 8px 0;
  min-width: 160px;

  span {
    font-size: 13px;
    color: var(--el-text-color-regular);
    white-space: nowrap;
    font-weight: 500;
  }
}

// 动画关键帧
@keyframes fadeInCollapsed {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

// 响应式设计
@media screen and (max-width: 1400px) {
  .sidebar {
    width: 300px;
    min-width: 260px;
  }
}

@media screen and (max-width: 1200px) {
  .sidebar {
    width: 280px;
    min-width: 240px;
  }
}

@media screen and (max-width: 992px) {
  .sidebar {
    width: 260px;
    min-width: 220px;
  }

  .workspace-header {
    .header-left {
      .document-info {
        .document-title {
          max-width: 150px;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .workspace-header {
    height: 44px;
    padding: 0 12px;

    .header-left {
      gap: 8px;

      .document-info {
        .document-title {
          max-width: 120px;
          font-size: 14px;
        }
      }
    }

    .header-center {
      :deep(.el-segmented) {
        .el-segmented__item {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }
  }

  .sidebar {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    transform: translateX(-100%);

    &:not(.collapsed) {
      transform: translateX(0);
    }
  }

  .main-editor {
    width: 100%;
  }

  .welcome-screen {
    padding: 20px;

    .welcome-content {
      .welcome-title {
        font-size: 24px;
      }

      .welcome-subtitle {
        font-size: 14px;
      }

      .welcome-actions {
        flex-direction: column;
        align-items: center;
        gap: 12px;
      }

      .welcome-features {
        gap: 20px;

        .feature-item {
          font-size: 12px;

          .el-icon {
            font-size: 20px;
          }
        }
      }
    }
  }

  .workspace-footer {
    height: 24px;
    font-size: 11px;

    .footer-left,
    .footer-right {
      gap: 12px;
    }
  }
}

@media screen and (max-width: 480px) {
  .workspace-header {
    height: 40px;
    padding: 0 8px;

    .header-left {
      .document-info {
        .document-title {
          max-width: 100px;
          font-size: 13px;
        }
      }
    }

    .header-center {
      :deep(.el-segmented) {
        .el-segmented__item {
          padding: 3px 6px;
          font-size: 11px;
        }
      }
    }

    .header-right {
      .el-button-group {
        .el-button {
          padding: 4px 8px;
          font-size: 11px;
        }
      }
    }
  }

  .welcome-screen {
    padding: 16px;

    .welcome-content {
      .welcome-title {
        font-size: 20px;
      }

      .welcome-subtitle {
        font-size: 13px;
        margin-bottom: 24px;
      }

      .welcome-features {
        gap: 16px;

        .feature-item {
          font-size: 11px;

          .el-icon {
            font-size: 18px;
          }
        }
      }
    }
  }

  .workspace-footer {
    .footer-left,
    .footer-right {
      gap: 8px;
    }

    .status-item {
      font-size: 10px;
    }
  }
}

// 滚动条样式
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color) transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb) {
  background: var(--el-border-color);
  border-radius: 3px;

  &:hover {
    background: var(--el-border-color-dark);
  }
}

// 动画效果
.file-panel,
.editor-area {
  animation: slideInUp 0.3s ease-out;
}



@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
