/**
 * 大模型配置文件
 * 定义不同模型的特殊处理规则和优化参数
 */

export const modelConfigs = {
  // Claude系列配置
  claude: {
    name: '<PERSON>',
    hasThinking: true,
    responseFormat: 'content',
    streamField: 'content',
    maxTokens: 8192,
    temperature: { min: 0.1, max: 1.0, default: 0.7 },
    specialHandling: {
      removeThinking: true,
      cleanPreamble: true,
      standardizeHeaders: true
    },
    promptOptimization: {
      useSystemRole: true,
      preferStructured: true,
      avoidRepetition: true
    }
  },

  // GPT系列配置
  gpt: {
    name: 'GPT',
    hasThinking: false,
    responseFormat: 'choices[0].message.content',
    streamField: 'choices[0].delta.content',
    maxTokens: 8192,
    temperature: { min: 0, max: 2.0, default: 0.7 },
    specialHandling: {
      removeThinking: false,
      cleanPreamble: true,
      standardizeHeaders: true
    },
    promptOptimization: {
      useSystemRole: true,
      preferStructured: true,
      avoidRepetition: false
    }
  },

  // 通义千问配置
  qwen: {
    name: '通义千问',
    hasThinking: false,
    responseFormat: 'output.text',
    streamField: 'output.text',
    maxTokens: 8192,
    temperature: { min: 0.1, max: 2.0, default: 0.8 },
    specialHandling: {
      removeThinking: false,
      cleanPreamble: true,
      standardizeHeaders: true,
      removeChinese: false
    },
    promptOptimization: {
      useSystemRole: false,
      preferStructured: true,
      avoidRepetition: true,
      chineseOptimized: true
    }
  },

  // 文心一言配置
  ernie: {
    name: '文心一言',
    hasThinking: false,
    responseFormat: 'result',
    streamField: 'result',
    maxTokens: 8192,
    temperature: { min: 0.1, max: 1.0, default: 0.7 },
    specialHandling: {
      removeThinking: false,
      cleanPreamble: true,
      standardizeHeaders: true,
      removeChinese: false
    },
    promptOptimization: {
      useSystemRole: false,
      preferStructured: true,
      avoidRepetition: true,
      chineseOptimized: true
    }
  },

  // 智谱GLM配置
  glm: {
    name: '智谱GLM',
    hasThinking: false,
    responseFormat: 'choices[0].message.content',
    streamField: 'choices[0].delta.content',
    maxTokens: 8192,
    temperature: { min: 0.1, max: 0.9, default: 0.7 },
    specialHandling: {
      removeThinking: false,
      cleanPreamble: true,
      standardizeHeaders: true
    },
    promptOptimization: {
      useSystemRole: true,
      preferStructured: true,
      avoidRepetition: true,
      chineseOptimized: true
    }
  },

  // 默认配置
  default: {
    name: '通用模型',
    hasThinking: false,
    responseFormat: 'content',
    streamField: 'content',
    maxTokens: 8192,
    temperature: { min: 0.1, max: 1.0, default: 0.7 },
    specialHandling: {
      removeThinking: true,
      cleanPreamble: true,
      standardizeHeaders: true
    },
    promptOptimization: {
      useSystemRole: true,
      preferStructured: true,
      avoidRepetition: true
    }
  }
}

/**
 * 根据模型ID获取配置
 */
export function getModelConfig(modelId) {
  if (!modelId) return modelConfigs.default
  
  const modelId_lower = modelId.toLowerCase()
  
  // 匹配模型类型
  for (const [key, config] of Object.entries(modelConfigs)) {
    if (key === 'default') continue
    
    const patterns = {
      claude: ['claude', 'anthropic'],
      gpt: ['gpt', 'openai', 'chatgpt'],
      qwen: ['qwen', '通义', 'tongyi'],
      ernie: ['ernie', '文心', 'wenxin', 'baidu'],
      glm: ['glm', '智谱', 'zhipu', 'chatglm']
    }
    
    if (patterns[key]?.some(pattern => modelId_lower.includes(pattern))) {
      return { ...config, type: key }
    }
  }
  
  return { ...modelConfigs.default, type: 'default' }
}

/**
 * 优化提示词
 */
export function optimizePrompt(prompt, modelConfig, context) {
  let optimizedPrompt = prompt
  
  // 中文优化
  if (modelConfig.promptOptimization?.chineseOptimized) {
    optimizedPrompt = optimizedPrompt
      .replace(/Please/g, '请')
      .replace(/Generate/g, '生成')
      .replace(/Create/g, '创建')
  }
  
  // 结构化偏好
  if (modelConfig.promptOptimization?.preferStructured) {
    optimizedPrompt += '\n\n请确保输出格式严格按照markdown标准，使用##作为主标题。'
  }
  
  // 避免重复
  if (modelConfig.promptOptimization?.avoidRepetition && context.siblings?.length > 0) {
    optimizedPrompt += `\n\n注意：避免与已有内容重复：${context.siblings.map(s => s.title).join('、')}`
  }
  
  return optimizedPrompt
}

/**
 * 获取最优参数
 */
export function getOptimalParams(modelConfig, aiForm) {
  const params = {
    temperature: aiForm.creativity || modelConfig.temperature.default,
    max_tokens: Math.min(
      aiForm.maxTokens || modelConfig.maxTokens,
      modelConfig.maxTokens
    )
  }
  
  // 确保参数在允许范围内
  params.temperature = Math.max(
    modelConfig.temperature.min,
    Math.min(modelConfig.temperature.max, params.temperature)
  )
  
  // 根据模型特性调整参数
  if (modelConfig.type === 'claude' && modelConfig.hasThinking) {
    // Claude模型可能需要更多token来处理thinking
    params.max_tokens = Math.round(params.max_tokens * 1.2)
  }
  
  if (modelConfig.type === 'qwen' || modelConfig.type === 'ernie') {
    // 中文模型可能需要调整temperature
    params.temperature = Math.min(params.temperature, 0.8)
  }
  
  return params
}

/**
 * 检测响应质量
 */
export function assessResponseQuality(content, modelConfig) {
  const issues = []
  let score = 100
  
  // 基础检查
  if (!content || content.trim().length === 0) {
    return { score: 0, issues: ['内容为空'] }
  }
  
  // 检查thinking残留（针对Claude）
  if (modelConfig.hasThinking && content.includes('<thinking>')) {
    issues.push('包含thinking标签残留')
    score -= 15
  }
  
  // 检查markdown格式
  const titleCount = (content.match(/^#{1,6}\s+.+$/gm) || []).length
  if (titleCount === 0) {
    issues.push('缺少markdown标题')
    score -= 20
  }
  
  // 检查内容长度
  if (content.length < 100) {
    issues.push('内容过短')
    score -= 10
  }
  
  // 检查重复内容
  const lines = content.split('\n').filter(line => line.trim())
  const uniqueLines = new Set(lines)
  if (lines.length > uniqueLines.size * 1.5) {
    issues.push('内容重复度较高')
    score -= 15
  }
  
  // 检查中文模型的特殊问题
  if (modelConfig.promptOptimization?.chineseOptimized) {
    if (content.includes('抱歉') || content.includes('无法')) {
      issues.push('包含拒绝或道歉内容')
      score -= 10
    }
  }
  
  return {
    score: Math.max(0, score),
    issues,
    titleCount,
    wordCount: content.length,
    uniquenessRatio: uniqueLines.size / lines.length
  }
}

/**
 * 模型能力评估
 */
export const modelCapabilities = {
  claude: {
    reasoning: 95,
    creativity: 90,
    structure: 85,
    chinese: 70,
    speed: 75
  },
  gpt: {
    reasoning: 90,
    creativity: 85,
    structure: 90,
    chinese: 75,
    speed: 85
  },
  qwen: {
    reasoning: 80,
    creativity: 75,
    structure: 80,
    chinese: 95,
    speed: 90
  },
  ernie: {
    reasoning: 75,
    creativity: 70,
    structure: 75,
    chinese: 90,
    speed: 85
  },
  glm: {
    reasoning: 85,
    creativity: 80,
    structure: 85,
    chinese: 90,
    speed: 80
  }
}
