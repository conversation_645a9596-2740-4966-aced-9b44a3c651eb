// vite.config.js
import { defineConfig } from "file:///D:/project/go/pvv/frontend/node_modules/.pnpm/vite@5.4.14_@types+node@24._56e794894b79ab79ebe5e974b549ca75/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/project/go/pvv/frontend/node_modules/.pnpm/@vitejs+plugin-vue@5.2.1_vi_574df16f68f21dd73a72db8a759b03d4/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import AutoImport from "file:///D:/project/go/pvv/frontend/node_modules/.pnpm/unplugin-auto-import@0.18.6_ce430fb7420ea257473c399ea39c2994/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///D:/project/go/pvv/frontend/node_modules/.pnpm/unplugin-vue-components@0.2_03fe1ec59b104fa32a2508608a3ce36b/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver } from "file:///D:/project/go/pvv/frontend/node_modules/.pnpm/unplugin-vue-components@0.2_03fe1ec59b104fa32a2508608a3ce36b/node_modules/unplugin-vue-components/dist/resolvers.js";
import path2 from "path";

// plugins/vue-script-merger.js
import fs from "fs";
import path from "path";
function vueScriptMerger(options = {}) {
  const defaultOptions = {
    scriptPaths: ["views", "scripts", "components"],
    extensions: [".script.js", ".js"],
    aliases: {},
    debug: false,
    rootDir: process.cwd(),
    srcDir: "src",
    injectComment: "// \u81EA\u52A8\u5BFC\u5165\u7684\u5916\u90E8\u811A\u672C: {filename}",
    useSameDir: true,
    vueDirs: ["views", "components"]
  };
  const config = { ...defaultOptions, ...options };
  const scriptToVueMap = /* @__PURE__ */ new Map();
  const vueFileCache = /* @__PURE__ */ new Map();
  const processedVueFiles = /* @__PURE__ */ new Set();
  let allVueFiles = [];
  let allScriptFiles = [];
  function findFiles(dir, extensions, results = []) {
    if (!fs.existsSync(dir)) return results;
    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        if (entry.isDirectory()) {
          findFiles(fullPath, extensions, results);
        } else if (entry.isFile() && extensions.some((ext) => entry.name.endsWith(ext))) {
          results.push(fullPath);
        }
      }
    } catch (err) {
      log(`\u8BFB\u53D6\u76EE\u5F55 ${dir} \u5931\u8D25: ${err.message}`, "error");
    }
    return results;
  }
  function findAllVueFiles() {
    allVueFiles = [];
    for (const vueDir of config.vueDirs) {
      let searchDir;
      if (path.isAbsolute(vueDir)) {
        searchDir = vueDir;
      } else if (vueDir.startsWith("./") || vueDir.startsWith("../")) {
        searchDir = path.join(config.rootDir, vueDir);
      } else {
        searchDir = path.join(config.rootDir, config.srcDir, vueDir);
      }
      searchDir = resolveAlias(searchDir);
      if (!fs.existsSync(searchDir)) {
        log(`Vue\u641C\u7D22\u76EE\u5F55\u4E0D\u5B58\u5728: ${searchDir}`, "warn");
        continue;
      }
      const files = findFiles(searchDir, [".vue"]);
      allVueFiles.push(...files);
    }
    log(`\u5171\u627E\u5230 ${allVueFiles.length} \u4E2AVue\u6587\u4EF6`, "info");
    return allVueFiles;
  }
  function findAllScriptFiles() {
    allScriptFiles = [];
    for (const scriptPath of config.scriptPaths) {
      let searchDir;
      if (path.isAbsolute(scriptPath)) {
        searchDir = scriptPath;
      } else if (scriptPath.startsWith("./") || scriptPath.startsWith("../")) {
        searchDir = path.join(config.rootDir, scriptPath);
      } else {
        searchDir = path.join(config.rootDir, config.srcDir, scriptPath);
      }
      searchDir = resolveAlias(searchDir);
      if (!fs.existsSync(searchDir)) {
        log(`\u811A\u672C\u641C\u7D22\u76EE\u5F55\u4E0D\u5B58\u5728: ${searchDir}`, "warn");
        continue;
      }
      const files = findFiles(searchDir, config.extensions);
      allScriptFiles.push(...files);
    }
    log(`\u5171\u627E\u5230 ${allScriptFiles.length} \u4E2A\u811A\u672C\u6587\u4EF6`, "info");
    return allScriptFiles;
  }
  function buildScriptToVueMapping() {
    scriptToVueMap.clear();
    if (allVueFiles.length === 0) {
      findAllVueFiles();
    }
    if (allScriptFiles.length === 0) {
      findAllScriptFiles();
    }
    for (const scriptFile of allScriptFiles) {
      const scriptBaseName = path.basename(scriptFile, path.extname(scriptFile)).replace(".script", "").replace(".vue", "");
      const matchingVueFiles = [];
      if (config.useSameDir) {
        const scriptDir = path.dirname(scriptFile);
        const sameDirVue = path.join(scriptDir, `${scriptBaseName}.vue`);
        if (fs.existsSync(sameDirVue)) {
          matchingVueFiles.push(sameDirVue);
          log(`\u4E3A\u811A\u672C ${scriptFile} \u627E\u5230\u540C\u76EE\u5F55Vue\u6587\u4EF6: ${sameDirVue}`);
        }
      }
      if (matchingVueFiles.length === 0) {
        for (const vueFile of allVueFiles) {
          const vueBaseName = path.basename(vueFile, ".vue");
          if (vueBaseName === scriptBaseName || vueFile.includes(`/${scriptBaseName}/index.vue`) || vueFile.includes(`\\${scriptBaseName}\\index.vue`) || scriptFile.includes(`/${vueBaseName}/index`) || scriptFile.includes(`\\${vueBaseName}\\index`)) {
            matchingVueFiles.push(vueFile);
            log(`\u4E3A\u811A\u672C ${scriptFile} \u627E\u5230\u5339\u914D\u7684Vue\u6587\u4EF6: ${vueFile}`);
          }
        }
      }
      if (matchingVueFiles.length > 0) {
        scriptToVueMap.set(scriptFile, matchingVueFiles);
      } else {
        log(`\u672A\u627E\u5230\u4E0E\u811A\u672C ${scriptFile} \u5339\u914D\u7684Vue\u6587\u4EF6`, "warn");
      }
    }
    log(`\u5EFA\u7ACB\u4E86 ${scriptToVueMap.size} \u4E2A\u811A\u672C\u5230Vue\u7684\u6620\u5C04\u5173\u7CFB`, "info");
  }
  function resolveAlias(filepath) {
    for (const [alias, target] of Object.entries(config.aliases)) {
      if (filepath.startsWith(alias)) {
        return filepath.replace(alias, target);
      }
    }
    return filepath;
  }
  function log(message, level = "debug") {
    if (!config.debug && level === "debug") return;
    const prefix = `[vue-script-merger] ${level.toUpperCase()}:`;
    switch (level) {
      case "error":
        console.error(prefix, message);
        break;
      case "warn":
        console.warn(prefix, message);
        break;
      case "info":
      case "debug":
      default:
        if (config.debug) console.log(prefix, message);
    }
  }
  function injectScript(vueContent, scriptContent, comment) {
    if (/<script\s+setup[^>]*>/.test(vueContent)) {
      return vueContent.replace(
        /(<script\s+setup[^>]*>)([\s\S]*?)(<\/script>)/,
        (match, openTag, content, closeTag) => {
          return `${openTag}
${comment}
${scriptContent}
${content}${closeTag}`;
        }
      );
    } else {
      return vueContent.replace(
        /<\/template>/,
        `</template>

<script setup>
${comment}
${scriptContent}
</script>`
      );
    }
  }
  return {
    name: "vue-script-merger",
    enforce: "pre",
    configResolved(resolvedConfig) {
      if (resolvedConfig.resolve && resolvedConfig.resolve.alias) {
        config.viteAliases = resolvedConfig.resolve.alias;
      }
      if (config.debug) {
        log("\u63D2\u4EF6\u914D\u7F6E:", "info");
        log(JSON.stringify(config, null, 2), "info");
      }
      buildScriptToVueMapping();
    },
    /**
     * 转换Vue组件文件
     * @param {string} code - 源代码
     * @param {string} id - 文件ID(路径)
     */
    transform(code, id) {
      if (!id.endsWith(".vue")) return null;
      if (processedVueFiles.has(id)) {
        return null;
      }
      try {
        const matchingScripts = [];
        for (const [scriptFile, vueFiles] of scriptToVueMap.entries()) {
          if (vueFiles.includes(id) || vueFiles.some((vueFile) => path.normalize(vueFile) === path.normalize(id))) {
            matchingScripts.push(scriptFile);
          }
        }
        if (matchingScripts.length === 0) {
          return null;
        }
        log(`\u4E3AVue\u6587\u4EF6 ${id} \u627E\u5230 ${matchingScripts.length} \u4E2A\u5339\u914D\u7684\u811A\u672C\u6587\u4EF6`, "info");
        let modifiedCode = code;
        for (const scriptFile of matchingScripts) {
          const scriptContent = fs.readFileSync(scriptFile, "utf-8");
          const comment = config.injectComment.replace(
            "{filename}",
            path.relative(path.dirname(id), scriptFile)
          );
          if (typeof config.transform === "function") {
            modifiedCode = config.transform(modifiedCode, scriptContent, comment, id, scriptFile);
          } else {
            modifiedCode = injectScript(modifiedCode, scriptContent, comment);
          }
        }
        processedVueFiles.add(id);
        return modifiedCode;
      } catch (error) {
        log(`\u5904\u7406\u6587\u4EF6 ${id} \u5931\u8D25: ${error.stack || error.message}`, "error");
        return null;
      }
    },
    // 监听文件变化，更新映射关系
    handleHotUpdate({ file, server }) {
      if (file.endsWith(".js") && config.extensions.some((ext) => file.endsWith(ext))) {
        log(`\u68C0\u6D4B\u5230\u811A\u672C\u6587\u4EF6\u53D8\u5316: ${file}`, "info");
        scriptToVueMap.clear();
        allScriptFiles = [];
        processedVueFiles.clear();
        buildScriptToVueMapping();
      } else if (file.endsWith(".vue")) {
        processedVueFiles.delete(file);
      }
    }
  };
}

// vite.config.js
import { execSync } from "child_process";
import obfuscator from "file:///D:/project/go/pvv/frontend/node_modules/.pnpm/vite-plugin-javascript-obfuscator@3.1.0/node_modules/vite-plugin-javascript-obfuscator/dist/index.cjs.js";
import { AntDesignXVueResolver } from "file:///D:/project/go/pvv/frontend/node_modules/.pnpm/ant-design-x-vue@1.2.4_ant-_cffe4b807ebf37c30691ff1f30a904be/node_modules/ant-design-x-vue/resolver-dist/index.js";
var __vite_injected_original_dirname = "D:\\project\\go\\pvv\\frontend";
var vite_config_default = defineConfig({
  base: "./",
  resolve: {
    alias: {
      "@": path2.resolve(__vite_injected_original_dirname, "src"),
      "~": path2.resolve(__vite_injected_original_dirname, "src"),
      "assets": path2.resolve(__vite_injected_original_dirname, "src/assets"),
      "components": path2.resolve(__vite_injected_original_dirname, "src/components"),
      "views": path2.resolve(__vite_injected_original_dirname, "src/views")
    },
    extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()]
    }),
    Components({
      resolvers: [ElementPlusResolver(), AntDesignXVueResolver()]
    }),
    vueScriptMerger({
      // 从多个位置查找脚本文件
      scriptPaths: [
        "scripts"
        // 相对于src目录
      ],
      // 支持多种扩展名
      extensions: [".script.js", ".vue.js", ".js"],
      // 路径别名(除了vite配置的别名外，可以添加额外的)
      aliases: {
        "@scripts": path2.resolve(__vite_injected_original_dirname, "src/scripts")
      },
      // 调试模式
      debug: true,
      // 自定义注入注释
      injectComment: "// \u4ECE {filename} \u81EA\u52A8\u5BFC\u5165",
      // 是否优先使用组件同目录下的脚本文件
      useSameDir: true
    }),
    // 暂时完全禁用混淆来测试应用功能
    // false && obfuscator({
    //   options: {
    //     compact: false,              // 关闭压缩，避免变量初始化问题
    //     controlFlowFlattening: false,
    //     deadCodeInjection: false,
    //     debugProtection: false,
    //     debugProtectionInterval: 0,
    //     disableConsoleOutput: false,
    //     identifierNamesGenerator: 'hexadecimal', // 使用最简单的标识符生成器
    //     log: false,
    //     numbersToExpressions: false,
    //     renameGlobals: false,        // 保持为 false，避免影响全局变量
    //     renameProperties: false,     // 不重命名属性，避免导入问题
    //     selfDefending: false,
    //     simplify: false,             // 降低混淆强度，减少内存使用
    //     splitStrings: false,
    //     stringArray: false,          // 关闭字符串数组，减少内存消耗
    //     stringArrayCallsTransform: false,
    //     stringArrayEncoding: ['none'],
    //     stringArrayIndexShift: false,
    //     stringArrayRotate: false,
    //     stringArrayShuffle: false,
    //     stringArrayWrappersCount: 1,
    //     stringArrayWrappersChainedCalls: false,
    //     stringArrayWrappersParametersMaxCount: 2,
    //     stringArrayWrappersType: 'variable',
    //     stringArrayThreshold: 0,     // 降低字符串数组阈值
    //     transformObjectKeys: false,  // 不转换对象键，避免导入问题
    //     unicodeEscapeSequence: false,
    //     reservedNames: [
    //       // pywebview 相关
    //       'pywebview',
    //       'pywebview.api',
    //       'pywebview.api.*',  // 保护所有 api 方法
    //       'window.pywebview',
    //       // Vue 相关
    //       'createApp',
    //       'defineComponent',
    //       'ref',
    //       'reactive',
    //       'onMounted',
    //       'computed',
    //       'watch',
    //       'nextTick',
    //       // 路由相关
    //       'createRouter',
    //       'createWebHashHistory',
    //       'useRouter',
    //       'useRoute',
    //       // Pinia 相关
    //       'createPinia',
    //       'defineStore',
    //       // Element Plus 相关
    //       'ElMessage',
    //       'ElMessageBox',
    //       // 其他重要的全局变量
    //       '__webpack_require__',
    //       'define',
    //       'require',
    //       'process',
    //       'global',
    //       'window',
    //       'document',
    //       // 添加路径相关
    //       '@',
    //       '@/',
    //       'path',
    //       'resolve',
    //       '__dirname',
    //       // 添加更多 Vue 相关
    //       'component',
    //       'defineAsyncComponent',
    //       'resolveComponent',
    //       'markRaw',
    //       'toRaw',
    //       'h',
    //       // 添加路由懒加载相关
    //       'import.meta',
    //       'import.meta.url',
    //       'import.meta.glob',
    //       'loadModule',
    //     ],
    //     domainLock: [],
    //     target: 'browser',
    //     sourceMap: false,
    //     seed: 0,
    //     reservedStrings: [],
    //   },
    //   exclude: [
    //     'node_modules/**',         // 排除所有第三方库，避免混淆导致的初始化问题
    //     'dist/**',
    //     '**/*.css',
    //     '**/*.scss',
    //     '**/*.sass',
    //     '**/*.less',
    //     '**/*.vue',
    //     '**/binding.js',
    //     '**/main.js',
    //     '**/stores/**',            // 排除stores，避免状态管理问题
    //     '**/router/**',            // 排除路由配置
    //     '**/utils/**',             // 排除工具函数
    //     '**/composables/**',       // 排除组合式函数
    //   ]
    // }),
    {
      name: "integrity-manifest",
      closeBundle() {
        console.log("\u6B63\u5728\u751F\u6210\u6587\u4EF6\u5B8C\u6574\u6027\u4EE3\u7801...");
        try {
          execSync("python build_integrity_code.py --dir ./statics --output ./PVV.py", {
            stdio: "inherit",
            cwd: path2.resolve(__vite_injected_original_dirname, "../")
            // 回到项目根目录
          });
        } catch (error) {
          console.error("\u751F\u6210\u5B8C\u6574\u6027\u4EE3\u7801\u5931\u8D25:", error);
        }
      }
    }
  ],
  server: {
    port: 13e3,
    // 指定启动端口为3000
    fs: {
      strict: false
      // 允许访问项目根目录之外的文件
    }
  },
  build: {
    outDir: "../statics",
    minify: "esbuild",
    // 使用 esbuild 做基础压缩
    // 针对simple-mind-map的内存优化
    target: "es2020",
    sourcemap: false,
    reportCompressedSize: false,
    rollupOptions: {
      input: {
        main: "./index.html"
        // 指向入口文件
      },
      output: {
        // 保持文件名结构但添加内容哈希
        entryFileNames: "assets/entry-[hash].js",
        chunkFileNames: ({ name }) => {
          const safeChunkName = name ? name.replace(/[^\x00-\x7F]/g, "").replace(/[^a-zA-Z0-9_-]/g, "-").replace(/^-+|-+$/g, "").replace(/-{2,}/g, "-") || "chunk" : "chunk";
          return `assets/${safeChunkName}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          let fileName = assetInfo.name || "";
          const safeName = fileName.replace(/[^\x00-\x7F]/g, "").replace(/[^a-zA-Z0-9_.-]/g, "-").replace(/^-+|-+$/g, "").replace(/-{2,}/g, "-") || "asset";
          const extType = fileName.split(".").pop();
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
            return `assets/images/${safeName}-[hash][extname]`;
          }
          if (/woff|woff2|eot|ttf|otf/i.test(extType)) {
            return `assets/fonts/${safeName}-[hash][extname]`;
          }
          if (/css/i.test(extType)) {
            return `assets/css/${safeName}-[hash][extname]`;
          }
          return `assets/${safeName}-[hash][extname]`;
        }
        // 暂时禁用手动代码分割，使用Vite默认策略
        // manualChunks: undefined
      }
    },
    chunkSizeWarningLimit: 8e3
  },
  publicPath: "./",
  assetsDir: "static"
});
console.log("src\u76EE\u5F55\u7684\u7EDD\u5BF9\u8DEF\u5F84\u662F\uFF1A", path2.resolve(__vite_injected_original_dirname, "src"));
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
