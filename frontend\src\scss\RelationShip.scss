:root {
  --primary-color: #409EFF;
  --primary-gradient: linear-gradient(135deg, #409EFF, #64B5F6);
  --primary-light: rgba(64, 158, 255, 0.15);
  --primary-deep: #3080FF; /* 添加深色主色调用于更高对比度场景 */
  --bg-color: #f8f9fc;
  --bg-gradient: linear-gradient(135deg, #f8f9fc, #f0f2f5);
  --text-color: #333;
  --text-color-secondary: #606266;
  --border-color: rgba(235, 238, 245, 0.9); /* 增加边框不透明度 */
  --panel-bg: rgba(250, 252, 255, 0.90); /* 降低透明度 */
  --card-bg: rgba(255, 255, 255, 0.90); /* 降低透明度 */
  --hover-bg: rgba(245, 247, 250, 0.95); /* 降低透明度 */
  --search-bg: rgba(255, 255, 255, 1); /* 搜索框不透明背景 */
  --button-text: #fff; /* 按钮文字颜色 */
  --shadow-color: rgba(0, 0, 0, 0.08);
  --shadow-light: 0 2px 12px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);
  --header-height: 80px;
  --sidebar-width: 300px;
  --footer-height: 50px;
  --card-radius: 12px;
  --blur-radius: 8px; /* 减小模糊半径 */
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --fixed-sidebar-width: 300px; /* 明确设置一个固定值 */
}

/* 黑暗主题变量 */
html.dark {
  --primary-color: #409EFF;
  --primary-gradient: linear-gradient(135deg, #409EFF, #1B6FF3);
  --primary-light: rgba(64, 158, 255, 0.25);
  --primary-deep: #5B9BFF; /* 黑暗主题下深色主色调 */
  --bg-color: #111827;
  --bg-gradient: linear-gradient(135deg, #111827, #1e293b);
  --text-color: #e0e0e0;
  --text-color-secondary: #a0aec0;
  --border-color: rgba(82, 82, 89, 0.5); /* 增加边框不透明度 */
  --panel-bg: rgba(35, 38, 45, 0.90); /* 降低透明度 */
  --card-bg: rgba(40, 44, 52, 0.90); /* 降低透明度 */
  --hover-bg: rgba(50, 55, 65, 0.95); /* 降低透明度 */
  --search-bg: rgba(30, 34, 40, 1); /* 搜索框不透明背景 */
  --button-text: #fff; /* 按钮文字颜色 */
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-light: 0 2px 12px rgba(0, 0, 0, 0.2);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.3);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.4);
}

/* 基础布局样式 */
.relation-ship {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 60px - 40px);
  background: var(--bg-gradient);
  color: var(--text-color);
  overflow: hidden;
  position: relative;
  margin-bottom: 20px;
}

/* 背景装饰效果 */
.relation-ship::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
          radial-gradient(circle at 10% 10%, rgba(64, 158, 255, 0.15) 0%, transparent 60%),
          radial-gradient(circle at 90% 90%, rgba(103, 194, 58, 0.1) 0%, transparent 60%),
          radial-gradient(circle at 90% 10%, rgba(230, 162, 60, 0.05) 0%, transparent 60%),
          radial-gradient(circle at 10% 90%, rgba(245, 108, 108, 0.07) 0%, transparent 60%);
  z-index: 0;
  pointer-events: none;
  opacity: 0.8;
}

/* 顶部区域 */
.page-header {
  height: var(--header-height);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--card-bg);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  box-shadow: var(--shadow-light);
  position: relative;
  z-index: 10;
  margin-bottom: 1px;
  border-radius: var(--card-radius) var(--card-radius) 0 0;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0.8;
  border-radius: var(--card-radius) var(--card-radius) 0 0;
}

.page-header::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.08), transparent);
  pointer-events: none;
  border-radius: var(--card-radius) var(--card-radius) 0 0;
}

.left-section {
  flex: 1;
  position: relative;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-color);
  display: flex;
  align-items: center;
  letter-spacing: -0.5px;
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.page-title::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 28px;
  background: var(--primary-gradient);
  margin-right: 12px;
  border-radius: 2px;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
}

.subtitle {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin: 0;
  opacity: 0.9;
  max-width: 80%;
  position: relative;
}

.subtitle::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg,
          rgba(64, 158, 255, 0.5),
          transparent);
  opacity: 0.5;
  border-radius: 1px;
}

.right-section {
  display: flex;
  align-items: center;
  position: relative;
}

.right-section::before {
  content: "";
  position: absolute;
  top: 50%;
  left: -20px;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background: linear-gradient(to bottom,
          transparent,
          var(--border-color),
          transparent);
}

.book-selector {
  width: 220px;
  position: relative;
}

.book-selector::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 8px;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.1);
  pointer-events: none;
  opacity: 0;
  transition: var(--transition-normal);
}

.book-selector:hover::after {
  opacity: 1;
}

.book-selector :deep(.el-input__wrapper) {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  border-radius: 8px;
  transition: var(--transition-normal);
}

.book-selector :deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
  transform: translateY(-1px);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
  height: calc(100% - var(--header-height));
  margin-bottom: calc(var(--footer-height) + 20px);
  z-index: 1;
}

.content-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  gap: 2px; /* 增加面板之间的间距 */
}

/* 底部空间 - 修改为相对容器定位 */
.bottom-space {
  height: var(--footer-height);
  background: var(--bg-gradient);
  position: absolute;
  bottom: -50px;
  left: 0;
  right: 0;
  border-top: 1px solid var(--border-color);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  box-shadow: 0 -4px 20px var(--shadow-color);
  z-index: 9;
}

/* 左侧面板 */
.side-panel {
  width: var(--sidebar-width);
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border-color);
  background-color: var(--panel-bg);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  transition: var(--transition-normal);
  position: relative;
  z-index: 5;
  box-shadow: var(--shadow-medium);
  border-radius: var(--card-radius) 0 0 var(--card-radius);
  overflow: hidden;
  margin: 0 16px 0 0; /* 增大右侧边距 */
}

.side-panel::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to bottom,
          rgba(255, 255, 255, 0.8),
          rgba(64, 158, 255, 0.3),
          rgba(255, 255, 255, 0.8));
  opacity: 0.6;
  z-index: 1;
}

.panel-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--card-bg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  position: relative;
}

.panel-header::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 10%;
  right: 10%;
  height: 1px;
  background: var(--primary-gradient);
  opacity: 0.5;
  border-radius: 1px;
}

.panel-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0.7;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.section-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: var(--primary-color);
  background: var(--primary-light);
  padding: 6px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
}

.count-badge {
  padding: 4px 10px;
  background: var(--primary-light);
  color: var(--primary-color);
  border-radius: 20px;
  font-size: 12px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
  font-weight: 500;
}

/* 过滤区域 */
.filter-section {
  padding: 6px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--card-bg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  position: relative;
}

.filter-section::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.05), transparent);
  pointer-events: none;
  opacity: 0.5;
}

.search-input {
  margin-bottom: 12px;
  position: relative;
}

.search-input::before {
  content: "";
  position: absolute;
  top: -22px;
  left: 0;
  font-size: 12px;
  color: var(--text-color-secondary);
  font-weight: 500;
  opacity: 0.8;
}

.search-input :deep(.el-input__wrapper) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  padding: 8px 12px;
  transition: var(--transition-normal);
  border: 1px solid var(--border-color);
  background: var(--search-bg);
  opacity: 1; /* 输入框背景不透明 */
}

.search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px var(--shadow-color);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.search-input :deep(.el-input__wrapper:focus-within) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.search-input :deep(.el-input__prefix-inner i) {
  color: var(--primary-color);
  font-size: 16px;
}

.search-input :deep(.el-input__inner::placeholder) {
  color: #a0aec0;
}

/* 添加关系按钮优化 */
.action-footer {
  padding: 16px;
  border-top: 1px solid var(--border-color);
  background-color: var(--card-bg);
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 5;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
}

.action-footer::before {
  content: "";
  position: absolute;
  top: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
          transparent,
          rgba(64, 158, 255, 0.5),
          rgba(64, 158, 255, 0.3),
          rgba(64, 158, 255, 0.5),
          transparent);
}

.add-relation-btn {
  width: 100%;
  border-radius: 10px;
  height: 42px;
  transition: var(--transition-normal);
  background: var(--primary-gradient);
  border: none;
  box-shadow: 0 3px 10px rgba(64, 158, 255, 0.2);
  font-weight: 600;
  position: relative;
  overflow: hidden;
  color: var(--button-text); /* 确保白色主题下文字也很清晰 */
  letter-spacing: 0.5px;
}

.add-relation-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
  opacity: 0.6;
}

.add-relation-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
}

.add-relation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
  background: linear-gradient(135deg, var(--primary-deep), #409EFF);
}

.add-relation-btn:hover::after {
  animation: shine 1.5s ease-in-out;
}

.add-relation-btn .el-icon {
  font-size: 16px;
  margin-right: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 4px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

@keyframes shine {
  0% { transform: translateX(-100%); }
  20%, 100% { transform: translateX(100%); }
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  position: relative;
  z-index: 1;
  border-radius: 0 var(--card-radius) var(--card-radius) 0;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.right-panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: radial-gradient(
                  circle at 70% 30%,
                  rgba(64, 158, 255, 0.07) 0%,
                  transparent 60%
  );
  z-index: -1;
  pointer-events: none;
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-actions :deep(.el-button) {
  border-radius: 8px;
  transition: var(--transition-normal);
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
}

.panel-actions :deep(.el-button:hover) {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
  background: var(--hover-bg);
}

.panel-actions :deep(.el-button i) {
  transition: var(--transition-normal);
}

.panel-actions :deep(.el-button:hover i) {
  color: var(--primary-color);
  transform: scale(1.1) rotate(10deg);
}

.right-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  padding-bottom: var(--footer-height);
  background-color: var(--panel-bg);
}

/* 图谱展示区 */
.graph-container {
  flex: 1;
  position: relative;
  background-color: var(--panel-bg);
  border-radius: var(--card-radius);
  margin: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  max-height: calc(100% - 32px);
}

.graph-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
          transparent,
          rgba(64, 158, 255, 0.3),
          rgba(64, 158, 255, 0.7),
          rgba(64, 158, 255, 0.3),
          transparent);
  z-index: 2;
}

.graph-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30%;
  background: linear-gradient(to bottom,
          rgba(64, 158, 255, 0.05),
          transparent);
  pointer-events: none;
}

.graph-container:hover {
  box-shadow: var(--shadow-heavy);
  border-color: rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
}

.graph-content {
  width: 100%;
  height: 100%;
}

.empty-graph {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  background: var(--card-bg);
  position: relative;
  overflow: hidden;
}

.empty-graph::before {
  content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: radial-gradient(
                  circle,
                  rgba(64, 158, 255, 0.1) 0%,
                  transparent 70%
  );
  border-radius: 50%;
  z-index: 0;
}

.empty-graph::after {
  content: "";
  position: absolute;
  bottom: -50px;
  left: -50px;
  width: 200px;
  height: 200px;
  background: radial-gradient(
                  circle,
                  rgba(103, 194, 58, 0.1) 0%,
                  transparent 70%
  );
  border-radius: 50%;
  z-index: 0;
}

.empty-icon {
  font-size: 70px;
  color: rgba(144, 147, 153, 0.2);
  margin-bottom: 24px;
  animation: float 3s ease-in-out infinite;
  filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.05));
  position: relative;
  z-index: 1;
}

@keyframes float {
  0% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
  100% { transform: translateY(0px) rotate(0deg); }
}

.empty-text {
  color: var(--text-color-secondary);
  margin-bottom: 30px;
  font-size: 16px;
  text-align: center;
  max-width: 400px;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

.empty-add-btn {
  margin-top: 20px;
  border-radius: 10px;
  padding: 10px 24px;
  background: var(--primary-gradient);
  border: none;
  box-shadow: 0 3px 10px rgba(64, 158, 255, 0.2);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  font-weight: 500;
  z-index: 1;
}

.empty-add-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
}

.empty-add-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

.empty-add-btn:hover::after {
  animation: shine 1.5s ease-in-out;
}

/* 关系列表区域 */
.relations-wrapper {
  padding: 0 16px 16px 16px;
  max-height: 250px;
  display: flex;
  flex-direction: column;
  background-color: var(--card-bg);
  border-radius: var(--card-radius) var(--card-radius) 0 0;
  box-shadow: 0 -8px 24px rgba(0, 0, 0, 0.08);
  z-index: 2;
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  border-top: 1px solid var(--border-color);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.relations-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
          transparent,
          rgba(64, 158, 255, 0.5),
          rgba(64, 158, 255, 0.3),
          rgba(64, 158, 255, 0.5),
          transparent);
  opacity: 0.8;
}

.relations-wrapper::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(to top, var(--card-bg), transparent);
  pointer-events: none;
  border-radius: 0 0 var(--card-radius) var(--card-radius);
  opacity: 0.8;
}

@keyframes slideUp {
  from { transform: translateY(40px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.relation-card {
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 12px;
  background-color: var(--panel-bg);
  box-shadow: var(--shadow-light);
  transition: var(--transition-normal);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.relation-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom,
          rgba(64, 158, 255, 0.7),
          rgba(103, 194, 58, 0.5)
  );
  border-radius: 3px 0 0 3px;
  opacity: 0.6;
  transition: var(--transition-normal);
}

.relation-card:hover::before {
  opacity: 1;
  width: 5px;
}

.relation-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.05), transparent);
  pointer-events: none;
  opacity: 0.5;
}

html.dark .relation-card::after {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.03), transparent);
}

.relation-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: rgba(64, 158, 255, 0.3);
}

/* 表单相关样式 */
.relation-form {
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
}

.relation-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color);
}

.select-row {
  display: flex;
  gap: 12px;
}

.template-select {
  width: 100%;
  margin-bottom: 8px;
}

.template-select :deep(.el-select__tags) {
  overflow: hidden;
  max-height: 30px; /* 固定标签区域高度 */
  transition: none; /* 禁用高度变化的过渡效果 */
}

.template-select :deep(.el-input__wrapper) {
  padding-top: 5px;
  padding-bottom: 5px;
  height: 40px; /* 固定输入框高度 */
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s, transform 0.2s; /* 只对这些属性应用过渡 */
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color) !important;
}

.template-select :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.template-select :deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color) !important;
}

.template-select :deep(.el-select__tags-text) {
  max-width: 80px; /* 控制标签文本最大宽度 */
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 选中标签样式 */
.template-select :deep(.el-tag) {
  background-color: var(--primary-light) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
  border-radius: 4px !important;
  padding: 0 8px !important;
  height: 24px !important;
  line-height: 22px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.template-select :deep(.el-tag .el-tag__close) {
  color: var(--primary-color) !important;
  background-color: transparent !important;
}

.template-select :deep(.el-tag .el-tag__close:hover) {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* 下拉菜单样式 */
.template-select-dropdown {
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid var(--border-color) !important;
}

.template-select-dropdown :deep(.el-select-dropdown__item) {
  padding: 8px 16px !important;
  font-size: 14px !important;
}

.template-select-dropdown :deep(.el-select-dropdown__item.selected) {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
  font-weight: 600 !important;
}

.template-select-dropdown :deep(.el-select-dropdown__item:hover) {
  background-color: rgba(64, 158, 255, 0.1) !important;
}

/* 深色模式适配 */
html.dark .template-select :deep(.el-input__wrapper) {
  background-color: rgba(50, 55, 65, 0.8) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
}

html.dark .template-select :deep(.el-tag) {
  background-color: rgba(64, 158, 255, 0.2) !important;
  border-color: rgba(64, 158, 255, 0.4) !important;
}

html.dark .template-select-dropdown {
  background-color: rgba(40, 44, 52, 0.95) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
}

html.dark .template-select-dropdown :deep(.el-select-dropdown__item.selected) {
  background-color: rgba(64, 158, 255, 0.2) !important;
}

html.dark .template-select-dropdown :deep(.el-select-dropdown__item:hover) {
  background-color: rgba(64, 158, 255, 0.15) !important;
}

/* 模板选项样式 */
.template-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.template-name {
  font-weight: 500;
}

/* 实体选择器样式 */
.entity-select {
  width: 100%;
}

.entity-select :deep(.el-input__wrapper) {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color) !important;
  transition: all 0.3s ease !important;
}

.entity-select :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.entity-select :deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color) !important;
}



.entity-main {
  display: flex;
  align-items: center;
  gap: 8px;
}

.entity-select-dropdown {
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

/* 深色模式适配 */
html.dark .entity-select :deep(.el-input__wrapper) {
  background-color: rgba(50, 55, 65, 0.8) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
}

html.dark .entity-select-dropdown {
  background-color: rgba(40, 44, 52, 0.95) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
}

.entity-select {
  width: 60%;
}

.relation-select {
  width: 100%;
}

.template-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.entity-main {
  display: flex;
  align-items: center;
}

.entity-main .entity-avatar {
  width: 28px;
  height: 28px;
  font-size: 12px;
  margin-right: 10px;
}

/* 自定义对话框样式 */
:deep(.el-dialog) {
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  border: 1px solid var(--border-color);
}

:deep(.el-dialog__header) {
  background-color: var(--panel-bg);
  padding: 18px 20px;
  margin-right: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

:deep(.el-dialog__header::after) {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.05), transparent);
  pointer-events: none;
}

:deep(.el-dialog__body) {
  padding: 20px;
  background-color: var(--bg-color);
}

:deep(.el-dialog__footer) {
  padding: 14px 20px;
  border-top: 1px solid var(--border-color);
  background-color: var(--panel-bg);
}

:deep(.el-dialog__headerbtn) {
  border-radius: 50%;
  background: var(--primary-light);
  width: 28px;
  height: 28px;
  top: 18px;
  right: 20px;
  transition: var(--transition-normal);
}

:deep(.el-dialog__headerbtn:hover) {
  background: var(--primary-color);
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: var(--primary-color);
  transition: var(--transition-normal);
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: white;
  transform: rotate(90deg);
}

/* 下拉菜单深色主题适配 */
:deep(.el-select-dropdown.dark-dropdown) {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-color);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  border-radius: 10px;
  box-shadow: var(--shadow-heavy);
  overflow: hidden;
}

:deep(.el-select-dropdown.dark-dropdown .el-select-dropdown__item) {
  color: var(--text-color);
}

:deep(.el-select-dropdown.dark-dropdown .el-select-dropdown__item.hover) {
  background-color: var(--primary-light);
}

:deep(.el-select-dropdown.dark-dropdown .el-select-dropdown__item.selected) {
  color: var(--primary-color);
  font-weight: bold;
  background-color: var(--primary-light);
}

/* 美化滚动条 */
:deep(::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(::-webkit-scrollbar-thumb) {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 4px;
}

:deep(::-webkit-scrollbar-thumb:hover) {
  background: rgba(144, 147, 153, 0.5);
}

/* 响应式适配 */
@media screen and (max-width: 1200px) {
  .relations-wrapper {
    max-height: 200px;
  }

  .graph-container {
    flex: 2;
  }
}

@media screen and (max-width: 992px) {
  .content-container {
    flex-direction: column;
  }

  .side-panel {
    width: 100%;
    height: 300px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .right-panel {
    height: calc(100% - 300px);
  }
}

@media screen and (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
  }

  .right-section {
    margin-top: 12px;
    width: 100%;
  }

  .book-selector {
    width: 100%;
  }
}

/* 关系列表头部和内容 */
.relations-header {
  padding: 16px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.relations-header::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 10%;
  right: 10%;
  height: 1px;
  background: linear-gradient(90deg,
          transparent,
          rgba(64, 158, 255, 0.7),
          transparent);
  opacity: 0.7;
}

.relations-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
}

.relations-title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 16px;
  background: var(--primary-gradient);
  margin-right: 8px;
  border-radius: 2px;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.relations-count {
  font-size: 12px;
  background-color: var(--primary-light);
  padding: 4px 12px;
  border-radius: 20px;
  color: var(--primary-color);
  border: 1px solid rgba(64, 158, 255, 0.2);
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
}

.relations-scrollbar {
  flex: 1;
  overflow: auto;
  max-height: 180px;
  padding: 4px;
}

.relations-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.relations-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.relations-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.2);
  border-radius: 3px;
}

.relations-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 158, 255, 0.4);
}

.relation-cards {
  padding: 12px 0;
}

.relation-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.relation-entities {
  display: flex;
  align-items: center;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 8px 14px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.relation-entities::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 45%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.08), transparent);
  pointer-events: none;
}

.relation-card:hover .relation-entities {
  border-color: rgba(64, 158, 255, 0.3);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.entity-from, .entity-to {
  font-weight: 600;
  color: var(--text-color);
  background: var(--primary-light);
  padding: 3px 8px;
  border-radius: 6px;
  transition: var(--transition-normal);
}

.relation-card:hover .entity-from,
.relation-card:hover .entity-to {
  background: rgba(64, 158, 255, 0.2);
}

.relation-arrow {
  display: flex;
  align-items: center;
  margin: 0 8px;
  color: var(--primary-color);
  font-size: 14px;
  background: var(--primary-light);
  padding: 4px;
  border-radius: 50%;
  transition: var(--transition-normal);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
}

.relation-card:hover .relation-arrow {
  transform: scale(1.1) rotate(10deg);
  background: var(--primary-color);
  color: white;
}

.relation-type :deep(.el-tag) {
  border-radius: 8px;
  padding: 6px 12px;
  font-weight: 500;
  box-shadow: var(--shadow-light);
  border: 1px solid transparent;
  transition: var(--transition-normal);
}

.relation-card:hover .relation-type :deep(.el-tag) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.relation-desc {
  font-size: 13px;
  color: var(--text-color-secondary);
  margin-bottom: 12px;
  line-height: 1.5;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 10px 14px;
  border-left: 3px solid rgba(64, 158, 255, 0.3);
  position: relative;
  box-shadow: var(--shadow-light);
  transition: var(--transition-normal);
}

.relation-desc::before {
  content: '"';
  position: absolute;
  top: 5px;
  left: 8px;
  font-size: 18px;
  color: var(--primary-color);
  opacity: 0.3;

}

.relation-card:hover .relation-desc {
  border-left-width: 5px;
  background-color: var(--hover-bg);
}

.relation-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 10px;
  border-top: 1px dashed var(--border-color);
}

.relation-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.relation-tag {
  margin-right: 0;
  margin-bottom: 0;
  border-radius: 6px;
  font-size: 11px;
  padding: 4px 8px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
}

.relation-card:hover .relation-tag {
  background: var(--hover-bg);
  border-color: rgba(64, 158, 255, 0.2);
}

.relation-actions {
  display: flex;
  gap: 10px;
}

.relation-actions :deep(.el-button) {
  transition: var(--transition-normal);
  height: 30px;
  padding: 0 12px;
  position: relative;
  overflow: hidden;
}

.relation-actions :deep(.el-button)::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: var(--transition-normal);
}

.relation-actions :deep(.el-button:hover) {
  transform: translateY(-2px);
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.relation-actions :deep(.el-button:hover)::after {
  opacity: 1;
  transform: scale(1);
}

/* 对话框和表单样式优化 */
:deep(.el-dialog) {
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
  backdrop-filter: blur(var(--blur-radius));
  -webkit-backdrop-filter: blur(var(--blur-radius));
  border: 1px solid var(--border-color);
}

:deep(.el-dialog__header) {
  background-color: var(--panel-bg);
  padding: 18px 20px;
  margin-right: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

:deep(.el-dialog__header)::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0.7;
}

:deep(.el-dialog__header::after) {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.05), transparent);
  pointer-events: none;
}

:deep(.el-dialog__body) {
  padding: 20px;
  background-color: var(--bg-color);
  background-image:
          radial-gradient(circle at 90% 10%, rgba(64, 158, 255, 0.03) 0%, transparent 60%),
          radial-gradient(circle at 10% 90%, rgba(103, 194, 58, 0.03) 0%, transparent 60%);
}

:deep(.el-dialog__footer) {
  padding: 14px 24px;
  border-top: 1px solid var(--border-color);
  background-color: var(--panel-bg);
}

:deep(.el-dialog__headerbtn) {
  border-radius: 50%;
  background: var(--primary-light);
  width: 28px;
  height: 28px;
  top: 18px;
  right: 20px;
  transition: var(--transition-normal);
}

:deep(.el-dialog__headerbtn:hover) {
  background: var(--primary-color);
  transform: rotate(90deg);
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: var(--primary-color);
  transition: var(--transition-normal);
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: white;
}

/* 表单相关美化 */
.relation-form {
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
}

.relation-form::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.relation-form::-webkit-scrollbar-track {
  background: transparent;
}

.relation-form::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.2);
  border-radius: 3px;
}

.relation-form::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 158, 255, 0.4);
}

.relation-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color);
  position: relative;
  padding-left: 12px;
}

.relation-form :deep(.el-form-item__label)::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background: var(--primary-gradient);
  border-radius: 2px;
  opacity: 0.7;
}

.select-row {
  display: flex;
  gap: 12px;
}

.template-select {
  width: 100%;
  margin-bottom: 8px;
}

.template-select :deep(.el-select__tags) {
  overflow: hidden;
  max-height: 30px; /* 固定标签区域高度 */
  transition: none; /* 禁用高度变化的过渡效果 */
}

.template-select :deep(.el-input__wrapper) {
  padding-top: 5px;
  padding-bottom: 5px;
  height: 40px; /* 固定输入框高度 */
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s, transform 0.2s; /* 只对这些属性应用过渡 */
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color) !important;
}

.template-select :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.template-select :deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color) !important;
}

.template-select :deep(.el-select__tags-text) {
  max-width: 80px; /* 控制标签文本最大宽度 */
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 选中标签样式 */
.template-select :deep(.el-tag) {
  background-color: var(--primary-light) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
  border-radius: 4px !important;
  padding: 0 8px !important;
  height: 24px !important;
  line-height: 22px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.template-select :deep(.el-tag .el-tag__close) {
  color: var(--primary-color) !important;
  background-color: transparent !important;
}

.template-select :deep(.el-tag .el-tag__close:hover) {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* 下拉菜单样式 */
.template-select-dropdown {
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid var(--border-color) !important;
}

.template-select-dropdown :deep(.el-select-dropdown__item) {
  padding: 8px 16px !important;
  font-size: 14px !important;
}

.template-select-dropdown :deep(.el-select-dropdown__item.selected) {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
  font-weight: 600 !important;
}

.template-select-dropdown :deep(.el-select-dropdown__item:hover) {
  background-color: rgba(64, 158, 255, 0.1) !important;
}

/* 深色模式适配 */
html.dark .template-select :deep(.el-input__wrapper) {
  background-color: rgba(50, 55, 65, 0.8) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
}

html.dark .template-select :deep(.el-tag) {
  background-color: rgba(64, 158, 255, 0.2) !important;
  border-color: rgba(64, 158, 255, 0.4) !important;
}

html.dark .template-select-dropdown {
  background-color: rgba(40, 44, 52, 0.95) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
}

html.dark .template-select-dropdown :deep(.el-select-dropdown__item.selected) {
  background-color: rgba(64, 158, 255, 0.2) !important;
}

html.dark .template-select-dropdown :deep(.el-select-dropdown__item:hover) {
  background-color: rgba(64, 158, 255, 0.15) !important;
}

/* 模板选项样式 */
.template-option {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100% !important;
}

.template-name {
  font-weight: 500 !important;
}

/* 实体选择器样式 */
.entity-select {
  width: 100%;
}

.entity-select :deep(.el-input__wrapper) {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color) !important;
  transition: all 0.3s ease !important;
}

.entity-select :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.entity-select :deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color) !important;
}



.entity-main {
  display: flex;
  align-items: center;
  gap: 8px;
}

.entity-select-dropdown {
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

/* 深色模式适配 */
html.dark .entity-select :deep(.el-input__wrapper) {
  background-color: rgba(50, 55, 65, 0.8) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
}

html.dark .entity-select-dropdown {
  background-color: rgba(40, 44, 52, 0.95) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
}

.entity-select {
  width: 60%;
}

.relation-select {
  width: 100%;
}

.relation-form :deep(.el-input__wrapper),
.relation-form :deep(.el-textarea__inner) {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
}

.relation-form :deep(.el-input__wrapper:hover),
.relation-form :deep(.el-textarea__inner:hover) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.relation-form :deep(.el-slider__runway) {
  background-color: var(--bg-color);
}

.relation-form :deep(.el-slider__bar) {
  background-color: var(--primary-color);
}

.relation-form :deep(.el-slider__button) {
  border: 2px solid var(--primary-color);
  background-color: white;
  transition: var(--transition-normal);
}

.relation-form :deep(.el-slider__button:hover) {
  transform: scale(1.2);
}

.relation-form :deep(.el-slider__marks-text) {
  color: var(--text-color-secondary);
  font-weight: 500;
}

.template-select {
  width: 100%;
}

.template-select :deep(.el-select__wrapper) {
  border-radius: 8px;
  background: var(--search-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 实体列表区域 */
.entity-list-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  height: 100%;
  background-color: var(--panel-bg);
}

.entity-scrollbar {
  flex: 1;
  overflow: auto;
  height: calc(100% - 60px);
  padding-bottom: 20px;
  background-color: var(--panel-bg);
}

.entity-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.entity-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.entity-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.2);
  border-radius: 3px;
}

.entity-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 158, 255, 0.4);
}

.entity-list {
  padding: 16px;
  padding-bottom: 30px;
}

.entity-item {
  display: flex !important;
  align-items: center !important;
  padding: 12px 16px !important;
  border-radius: 10px !important;
  margin-bottom: 8px !important;
  background: var(--card-bg) !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  position: relative !important;
}

.entity-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 45%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), transparent);
  pointer-events: none;
  transition: var(--transition-normal);
}

html.dark .entity-item {
  background-color: rgba(50, 55, 65, 0.9) !important;
  border: 1px solid rgba(60, 70, 80, 0.3) !important;
}

html.dark .entity-item::after {
  display: none;
}

html.dark .entity-item:hover {
  background-color: rgba(55, 60, 70, 0.95) !important;
  border-color: rgba(64, 158, 255, 0.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

html.dark .entity-item.active {
  background-color: rgba(234, 237, 239, 0.15) !important;
  border-color: rgba(64, 158, 255, 0.4) !important;
}

.entity-item.active {
  background-color: rgba(64, 152, 210, 0.15) !important;
  border-color: rgba(64, 158, 255, 0.4) !important;
}

.entity-avatar {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: white !important;
  margin-right: 12px !important;
  flex-shrink: 0 !important;
  position: relative !important;
  overflow: hidden !important;
}

.entity-avatar::after {
  display: none;
}

html.dark .entity-avatar {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3) !important;
}

.entity-item:hover .entity-avatar {
  transform: scale(1.05);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.2);
}

.entity-info {
  flex: 1 !important;
  min-width: 0 !important;
  margin-right: 8px !important;
}

.entity-name {
  font-size: 15px !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
  margin-bottom: 4px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.entity-name::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--primary-gradient);
  transition: width 0.3s ease;
}

.entity-item:hover .entity-name::after {
  width: 100%;
}

.entity-type {
  font-size: 12px !important;
  color: var(--text-color-secondary) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

html.dark .entity-type {
  background: rgba(255, 255, 255, 0.05);
}

.relation-icon {
  color: var(--primary-color);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--primary-light);
  padding: 6px;
  transition: var(--transition-normal);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
  border: 1px solid rgba(64, 158, 255, 0.2);
  font-size: 18px;
}

.entity-item:hover .relation-icon {
  transform: rotate(45deg);
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3);
  border-color: var(--primary-color);
}

.relation-badge {
  height: 18px;
  border-radius: 9px;
  padding: 0 6px;
  background-color: var(--primary-color);
  color: white;
  font-size: 12px;
  font-weight: bold;
  transition: var(--transition-normal);
  position: absolute;
  top: -6px;
  right: -6px;
  z-index: 3;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.relation-badge.hidden {
  display: none;
}

/* 关系弹窗样式 */
.dialog-relations-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 12px 0;
}

.dialog-relations-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.dialog-relations-list::-webkit-scrollbar-track {
  background: transparent;
}

.dialog-relations-list::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.2);
  border-radius: 3px;
}

.dialog-relations-list::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 158, 255, 0.4);
}

/* 修复多选框抖动问题，同时保持适当宽度 */
.template-select {
  width: 100%;
  margin-bottom: 8px;
}

.template-select :deep(.el-select__tags) {
  overflow: hidden;
  max-height: 30px;
  transition: none;
}

.template-select :deep(.el-input__wrapper) {
  padding-top: 5px;
  padding-bottom: 5px;
  height: 40px;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s, transform 0.2s;
}

.template-select :deep(.el-select__tags-text) {
  max-width: 80px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-select-dropdown {
  border-radius: 8px;
  overflow: hidden;
}

/* 确保side-panel保持原来的宽度 */
.side-panel {
  width: var(--sidebar-width);
  flex-shrink: 0;
  min-width: var(--sidebar-width);
  max-width: var(--sidebar-width);
}

/* 在select-row中的模板选择器应该是40% */
.select-row .template-select {
  width: 40%;
  margin-bottom: 0;
}

/* 删除所有现有的template-select相关样式 */
/* 添加一组完全替换的样式定义 */

/* 全局基础样式 - 只保留一个明确的定义 */
:deep(.el-select-dropdown.template-select-dropdown) {
  border-radius: 8px;
  overflow: hidden;
}

/* 筛选区域中的select */
.filter-section .template-select {
  width: 100%;
  margin-bottom: 8px;
  position: relative;
}

/* 表单中行内的select */
.select-row .template-select {
  width: 40%;
  margin-bottom: 0;
}

/* 固定高度和过渡效果解决抖动问题 */
.template-select :deep(.el-select__tags) {
  overflow: hidden;
  max-height: 30px;
  transition: none !important;
}

.template-select :deep(.el-input__wrapper) {
  padding-top: 5px;
  padding-bottom: 5px;
  height: 40px;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s, transform 0.2s;
}

.template-select :deep(.el-select__tags-text) {
  max-width: 80px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 确保面板宽度固定 */
.side-panel {
  width: var(--sidebar-width);
  min-width: var(--sidebar-width);
  max-width: var(--sidebar-width);
  flex: 0 0 var(--sidebar-width);
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: var(--sidebar-width);
  box-sizing: border-box;
  overflow: hidden;
}

/* 保留其他属性不变 */

/* 完全替换所有template-select和side-panel相关的CSS */
/* 在样式部分的最后添加 */

/* 清除之前所有重复定义的样式 */
/* 侧边面板绝对宽度固定 */
.side-panel {
  width: var(--sidebar-width) !important;
  min-width: var(--sidebar-width) !important;
  max-width: var(--sidebar-width) !important;
  flex: 0 0 var(--sidebar-width) !important;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* 保证下拉菜单本身不影响容器宽度 */
.filter-section .template-select {
  width: 100%;
  margin-bottom: 8px;
  position: relative;
}

/* 表单中的选择器保持原来的40%宽度 */
.select-row .template-select {
  width: 40%;
  margin-bottom: 0;
}

/* 关键修复：防止标签区域影响布局 */
.template-select :deep(.el-select__tags) {
  position: absolute !important;
  overflow: hidden !important;
  max-height: 30px !important;
  transition: none !important;
  flex-wrap: nowrap !important;
  white-space: nowrap !important;
}

/* 确保输入区域高度固定 */
.template-select :deep(.el-input__wrapper) {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
  height: 40px !important;
  min-height: 40px !important;
  max-height: 40px !important;
  box-sizing: border-box !important;
  transition: border-color 0.2s, box-shadow 0.2s !important;
}

/* 控制标签文本不撑开容器 */
.template-select :deep(.el-select__tags-text) {
  max-width: 80px !important;
  display: inline-block !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 防止多选标签内容撑开容器 */
.template-select :deep(.el-select__tags-wrapper) {
  max-width: calc(100% - 30px) !important;
  height: 30px !important;
  overflow: hidden !important;
}

/* 下拉菜单样式 */
:deep(.el-select-dropdown.template-select-dropdown) {
  border-radius: 8px;
  overflow: hidden;
}

   /* 在末尾添加新的CSS定义，确保覆盖之前的所有定义 */

   /* 创建一个固定宽度的容器来包装选择器 */
 .template-select-container {
   width: 100%;
   position: relative;
   height: 40px;
   margin-bottom: 8px;
 }

/* 固定侧边栏宽度 - 使用ID选择器提高特异性 */
:deep(#app) .side-panel {
  width: var(--sidebar-width) !important;
  min-width: var(--sidebar-width) !important;
  max-width: var(--sidebar-width) !important;
  flex: 0 0 var(--sidebar-width) !important;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  flex-basis: var(--sidebar-width) !important;
  box-sizing: border-box !important;
}

/* 设置下拉选择器样式 */
.template-select {
  width: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
}

/* 关键修复：强制标签容器绝对定位 */
.template-select :deep(.el-select__tags) {
  position: absolute !important;
  left: 28px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  max-width: calc(100% - 60px) !important;
  height: 24px !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  flex-wrap: nowrap !important;
  transition: none !important;
}

/* 固定输入框宽度和高度 */
.template-select :deep(.el-input__wrapper) {
  height: 40px !important;
  min-height: 40px !important;
  max-height: 40px !important;
  box-sizing: border-box !important;
  padding: 0 8px !important;
  transition: box-shadow 0.2s !important;
}

/* 控制标签文本不溢出 */
.template-select :deep(.el-select__tags-text) {
  max-width: 70px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  font-size: 12px !important;
}

/* 标签容器内部布局控制 */
.template-select :deep(.el-select__tags-wrapper) {
  display: inline-flex !important;
  max-width: 100% !important;
  overflow: hidden !important;
  height: 100% !important;
}

/* 下拉菜单样式 */
:deep(.el-select-dropdown.template-select-dropdown) {
  border-radius: 8px;
  overflow: hidden;
}

/* 表单中的选择器样式 */
.select-row .template-select {
  width: 40% !important;
  position: relative !important;
}

   /* 在全局样式中添加这个，确保边栏宽度绝对固定 */
 :root {
   --fixed-sidebar-width: 300px;
 }

/* 强制固定侧边栏宽度的样式，使用更高优先级选择器 */
.content-container .side-panel,
.content-container > .side-panel,
.relation-ship .content-container > .side-panel,
#app .content-container > .side-panel,
body .content-container > .side-panel {
  width: var(--fixed-sidebar-width) !important;
  min-width: var(--fixed-sidebar-width) !important;
  max-width: var(--fixed-sidebar-width) !important;
  flex: 0 0 var(--fixed-sidebar-width) !important;
  flex-basis: var(--fixed-sidebar-width) !important;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  box-sizing: border-box !important;
  overflow-x: hidden !important;
  position: relative !important;
  z-index: 1 !important;
}

/* 控制模板选择器尺寸 */
.template-select-container {
  width: 100% !important;
  position: relative !important;
  height: 40px !important;
  margin-bottom: 8px !important;
  overflow: hidden !important;
  display: block !important;
}

/* 确保所有下拉菜单组件不影响容器尺寸 */
.template-select,
.template-select-container .el-select,
.filter-section .template-select,
.filter-section > .template-select-container > .template-select {
  width: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1 !important;
}

/* 特别处理表单中的模板选择器 */
.select-row .template-select,
.select-row .template-select-container,
.select-row > .template-select,
.select-row > .template-select-container > .template-select {
  width: 40% !important;
  position: relative !important;
}

/* 防止标签影响布局 */
.el-select__tags,
.template-select .el-select__tags,
.template-select-container .el-select .el-select__tags {
  position: absolute !important;
  left: 28px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  max-width: calc(100% - 60px) !important;
  height: 24px !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  flex-wrap: nowrap !important;
  transition: none !important;
}

/* 控制输入框尺寸 */
.template-select .el-input__wrapper,
.template-select-container .el-select .el-input__wrapper {
  height: 40px !important;
  min-height: 40px !important;
  max-height: 40px !important;
  box-sizing: border-box !important;
  padding: 0 8px !important;
  transition: box-shadow 0.2s !important;
}

/* 限制标签文本宽度 */
.el-select__tags-text,
.template-select .el-select__tags-text,
.template-select-container .el-select .el-select__tags-text {
  max-width: 60px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  font-size: 12px !important;
}

/* 限制标签容器大小 */
.el-select__tags-wrapper,
.template-select .el-select__tags-wrapper,
.template-select-container .el-select .el-select__tags-wrapper {
  display: inline-flex !important;
  max-width: 100% !important;
  overflow: hidden !important;
  height: 100% !important;
}

   /* 美化下拉菜单和选中标签的样式 */

   /* 下拉菜单整体样式 */
 .el-select-dropdown.template-select-dropdown {
   border-radius: 12px !important;
   overflow: hidden !important;
   padding: 8px !important;
   box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15) !important;
   border: 1px solid var(--border-color) !important;
   backdrop-filter: blur(10px) !important;
   -webkit-backdrop-filter: blur(10px) !important;
 }

/* 下拉选项样式 */
.template-select-dropdown .el-select-dropdown__item {
  height: 40px !important;
  line-height: 40px !important;
  padding: 0 16px !important;
  border-radius: 8px !important;
  margin-bottom: 4px !important;
  transition: all 0.2s ease !important;
}

/* 选项悬停效果 */
.template-select-dropdown .el-select-dropdown__item.hover {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

/* 选中项样式 */
.template-select-dropdown .el-select-dropdown__item.selected {
  background-color: var(--primary-color) !important;
  color: white !important;
  font-weight: 600 !important;
  position: relative !important;
}

/* 选中项勾选图标 */
.template-select-dropdown .el-select-dropdown__item.selected::after {
  content: "✓" !important;
  position: absolute !important;
  right: 16px !important;
  font-weight: bold !important;
}

/* 选项内的标签 */
.template-option {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100% !important;
}

.template-name {
  font-weight: 500 !important;
}

.template-option .el-tag {
  background-color: rgba(64, 158, 255, 0.1) !important;
  border-color: rgba(64, 158, 255, 0.2) !important;
  color: var(--primary-color) !important;
  font-weight: 500 !important;
  padding: 2px 8px !important;
  border-radius: 10px !important;
}

/* 选中后的标签样式 */
.template-select .el-tag {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
  border-color: rgba(64, 158, 255, 0.3) !important;
  border-radius: 8px !important;
  padding: 2px 10px !important;
  height: 24px !important;
  line-height: 20px !important;
  margin: 2px !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 5px rgba(64, 158, 255, 0.15) !important;
  transition: all 0.3s ease !important;
}

/* 标签悬停效果 */
.template-select .el-tag:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.template-select .el-tag:hover .el-tag__close {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.1) !important;
}

/* 黑暗模式下的调整 */
html.dark .template-select-dropdown {
  background-color: #2a2a3a !important;
  border-color: #444 !important;
}

html.dark .template-select-dropdown .el-select-dropdown__item.hover {
  background-color: rgba(64, 158, 255, 0.2) !important;
}

html.dark .template-option .el-tag {
  background-color: rgba(64, 158, 255, 0.2) !important;
  border-color: rgba(64, 158, 255, 0.3) !important;
}

html.dark .template-select .el-tag {
  background-color: rgba(64, 158, 255, 0.25) !important;
}

   /* 添加以下样式来隐藏勾选图标 */
 .template-select-dropdown .el-select-dropdown__item.selected::after,
 .el-select-dropdown.is-multiple .el-select-dropdown__item.is-selected:after,
 .el-select-dropdown.template-select-dropdown .el-select-dropdown__item.is-selected:after {
   display: none !important; /* 完全隐藏勾选图标 */
   content: none !important;
   background: none !important;
   mask: none !important;
   -webkit-mask: none !important;
 }

/* 如果需要，可以增强选中项的背景色效果替代勾选标记 */
.template-select-dropdown .el-select-dropdown__item.selected,
.el-select-dropdown.template-select-dropdown .el-select-dropdown__item.is-selected {
  background-color: var(--primary-color) !important;
  color: white !important;
  font-weight: 600 !important;
  position: relative !important;
  /* 添加右侧淡蓝色边框替代勾选标记 */
  border-right: 3px solid rgba(255, 255, 255, 0.5) !important;
}

/* 悬停时的效果 */
.template-select-dropdown .el-select-dropdown__item.selected:hover,
.el-select-dropdown.template-select-dropdown .el-select-dropdown__item.is-selected:hover {
  background-color: var(--primary-deep, #3080FF) !important;
}

   /* 图谱容器区域美化 */
 .graph-container {
   background: var(--card-bg, rgba(255, 255, 255, 0.95)) !important;
   border-radius: 16px !important;
   box-shadow:
           0 4px 20px rgba(0, 0, 0, 0.08),
           0 1px 3px rgba(0, 0, 0, 0.05),
           inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
   border: 1px solid rgba(255, 255, 255, 0.7) !important;
   overflow: hidden !important;
   position: relative !important;
   backdrop-filter: blur(12px) !important;
   -webkit-backdrop-filter: blur(12px) !important;
   transition: all 0.3s ease !important;
 }

/* 添加微妙纹理背景 */
.graph-container::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-image:
          linear-gradient(45deg, rgba(64, 158, 255, 0.01) 25%, transparent 25%, transparent 75%, rgba(64, 158, 255, 0.01) 75%),
          linear-gradient(45deg, rgba(64, 158, 255, 0.01) 25%, transparent 25%, transparent 75%, rgba(64, 158, 255, 0.01) 75%) !important;
  background-size: 60px 60px !important;
  background-position: 0 0, 30px 30px !important;
  opacity: 0.5 !important;
  z-index: 0 !important;
}

/* 图谱内容容器 */
.graph-content {
  height: 100% !important;
  width: 100% !important;
  position: relative !important;
  z-index: 1 !important;
  border-radius: 14px !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent) !important;
}

/* 悬停效果 */
.graph-container:hover {
  box-shadow:
          0 6px 24px rgba(0, 0, 0, 0.12),
          0 2px 5px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
  transform: translateY(-2px) !important;
}

/* 空状态美化 */
.empty-graph {
  position: relative !important;
  padding: 48px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  z-index: 1 !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent) !important;
}

.empty-icon {
  font-size: 64px !important;
  margin-bottom: 20px !important;
  color: var(--primary-light) !important;
  background: -webkit-linear-gradient(45deg, var(--primary-color), #64B5F6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  opacity: 0.8 !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15)) !important;
}

.empty-text {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: var(--text-color-secondary) !important;
  margin-bottom: 30px !important;
  text-align: center !important;
  max-width: 300px !important;
  line-height: 1.6 !important;
}

.empty-add-btn {
  padding: 10px 24px !important;
  font-weight: 600 !important;
  border-radius: 10px !important;
  background: var(--primary-gradient) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.empty-add-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4) !important;
}

/* 深色模式调整 */
html.dark .graph-container {
  background: var(--card-bg, rgba(40, 44, 52, 0.95)) !important;
  border-color: rgba(80, 80, 90, 0.3) !important;
  box-shadow:
          0 4px 20px rgba(0, 0, 0, 0.2),
          0 1px 3px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

html.dark .graph-container::before {
  opacity: 0.15 !important;
}

html.dark .empty-icon {
  opacity: 0.6 !important;
}

html.dark .empty-text {
  color: rgba(255, 255, 255, 0.6) !important;
}

   /* 页面头部美化 - 增强对比度和视觉层次 */
 .page-header {
   background: linear-gradient(135deg, var(--card-bg), rgba(255, 255, 255, 0.98)) !important;
   border-radius: 16px !important;
   margin-bottom: 20px !important;
   padding: 20px 24px !important;
   box-shadow:
           0 4px 16px rgba(0, 0, 0, 0.06),
           0 1px 3px rgba(0, 0, 0, 0.03),
           inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
   border: 1px solid rgba(255, 255, 255, 0.8) !important;
   position: relative !important;
   overflow: hidden !important;
   backdrop-filter: blur(10px) !important;
   -webkit-backdrop-filter: blur(10px) !important;
 }

/* 透明边框装饰 */
.page-header::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 3px !important;
  background: var(--primary-gradient) !important;
  opacity: 0.8 !important;
}

/* 标题样式增强 */
.page-title {
  color: var(--text-color) !important;
  font-weight: 600 !important;
  font-size: 22px !important;
  margin: 0 0 8px 0 !important;
  position: relative !important;
  display: inline-block !important;
  /* 文字阴影提高对比度 */
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8) !important;
}

/* 为标题添加下划线装饰 */
.page-title::after {
  content: "" !important;
  position: absolute !important;
  bottom: -4px !important;
  left: 0 !important;
  width: 36px !important;
  height: 3px !important;
  background: var(--primary-gradient) !important;
  border-radius: 1.5px !important;
}

/* 副标题样式 */
.subtitle {
  color: var(--text-color-secondary) !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  max-width: 520px !important;
  opacity: 0.9 !important;
}

/* 书籍选择器样式 */
.book-selector {
  min-width: 180px !important;
  border-radius: 12px !important;
}

.book-selector :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 0, 0, 0.03) !important;
  transition: all 0.3s ease !important;
  height: 42px !important;
  padding: 0 16px !important;
  border: none !important;
}

.book-selector :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(64, 158, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.book-selector :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px var(--primary-color), 0 4px 12px rgba(64, 158, 255, 0.15) !important;
}

/* 下拉菜单样式 */
.el-select__popper,
.el-popper.is-light {
  border-radius: 12px !important;
  padding: 8px !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(235, 238, 245, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* 下拉菜单项样式 */
.el-select-dropdown__item {
  height: 40px !important;
  line-height: 40px !important;
  padding: 0 16px !important;
  border-radius: 8px !important;
  margin-bottom: 4px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

/* 下拉菜单项悬停效果 */
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

/* 下拉菜单选中项样式 */
.el-select-dropdown__item.selected {
  background-color: var(--primary-color) !important;
  color: white !important;
  font-weight: 600 !important;
}

/* 禁用下拉菜单选中项的勾选图标 */
.el-select-dropdown__item.selected::after,
.el-select-dropdown__item.is-selected::after {
  display: none !important;
}

/* 深色模式适配 */
html.dark .book-selector :deep(.el-input__wrapper) {
  background: rgba(35, 38, 45, 0.9) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.08) !important;
}

html.dark .book-selector :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(64, 158, 255, 0.3) !important;
}

html.dark .el-select__popper,
html.dark .el-popper.is-light {
  background-color: rgba(30, 34, 40, 0.95) !important;
  border-color: rgba(80, 80, 90, 0.4) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

html.dark .el-select-dropdown__item {
  color: rgba(255, 255, 255, 0.85) !important;
}

html.dark .el-select-dropdown__item.hover,
html.dark .el-select-dropdown__item:hover {
  background-color: rgba(64, 158, 255, 0.2) !important;
}

/* 美化输入框中的图标 */
.book-selector :deep(.el-input__prefix),
.book-selector :deep(.el-input__suffix) {
  color: var(--primary-color) !important;
}

html.dark .book-selector :deep(.el-input__prefix),
html.dark .book-selector :deep(.el-input__suffix) {
  color: rgba(64, 158, 255, 0.8) !important;
}

   /* 美化书籍选择器 - 深色主题适配 */
 .book-selector {
   min-width: 180px !important;
   border-radius: 12px !important;
 }

/* 深色主题下的书籍选择器 */
html.dark .book-selector :deep(.el-input__wrapper) {
  background: rgba(30, 34, 43, 0.95) !important;
  border-color: rgba(80, 80, 90, 0.4) !important;
  box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.25),
          0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.95) !important;
}

html.dark .book-selector :deep(.el-select__caret),
html.dark .book-selector :deep(.el-input__inner) {
  color: rgba(255, 255, 255, 0.95) !important;
}

html.dark .book-selector :deep(.el-input__wrapper:hover) {
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.35),
          0 0 0 1px rgba(64, 158, 255, 0.4) !important;
  background: rgba(35, 40, 50, 0.95) !important;
}

html.dark .book-selector :deep(.el-input__wrapper.is-focus) {
  box-shadow:
          0 0 0 1px var(--primary-color),
          0 0 8px rgba(64, 158, 255, 0.35) !important;
  background: rgba(35, 40, 50, 0.98) !important;
}

/* 美化面板头部 */
.panel-header {
  padding: 16px 20px !important;
  border-bottom: 1px solid rgba(235, 238, 245, 0.5) !important;
  background: linear-gradient(135deg, rgba(250, 250, 255, 0.8), rgba(245, 247, 250, 0.9)) !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* 左侧面板标题栏装饰 */
.side-panel .panel-header::before,
.right-panel .panel-header::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 3px !important;
  background: var(--primary-gradient) !important;
  opacity: 0.85 !important;
}

.section-title {
  font-weight: 600 !important;
  font-size: 16px !important;
  color: var(--text-color) !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.section-title .el-icon {
  color: var(--primary-color) !important;
  font-size: 18px !important;
  background: var(--primary-light) !important;
  padding: 6px !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2), 0 0 0 1px rgba(64, 158, 255, 0.1) !important;
}

.entity-count .count-badge {
  background: var(--primary-light) !important;
  color: var(--primary-color) !important;
  font-weight: 500 !important;
  font-size: 13px !important;
  padding: 4px 12px !important;
  border-radius: 10px !important;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1), 0 0 0 1px rgba(64, 158, 255, 0.1) !important;
}

.panel-actions {
  display: flex !important;
  gap: 8px !important;
}

/* 深色模式面板头部适配 */
html.dark .panel-header {
  background: linear-gradient(135deg, rgba(35, 38, 45, 0.95), rgba(30, 34, 40, 0.9)) !important;
  border-bottom-color: rgba(60, 65, 75, 0.5) !important;
}

html.dark .section-title {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .section-title .el-icon {
  background: rgba(64, 158, 255, 0.2) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(64, 158, 255, 0.2) !important;
}

html.dark .entity-count .count-badge {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(64, 158, 255, 0.15) !important;
  background: rgba(64, 158, 255, 0.15) !important;
}

/* 美化面板操作按钮 */
.panel-actions .el-button {
  border-radius: 8px !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.03) !important;
  transition: all 0.2s ease !important;
  background: rgba(255, 255, 255, 0.9) !important;
}

.panel-actions .el-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(64, 158, 255, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
}

html.dark .panel-actions .el-button {
  background: rgba(45, 50, 60, 0.8) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

html.dark .panel-actions .el-button:hover {
  background: rgba(50, 55, 65, 0.9) !important;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(64, 158, 255, 0.2) !important;
}

html.dark .panel-actions .el-button .el-icon {
  color: rgba(255, 255, 255, 0.8) !important;
}

   /* 搜索框美化 */
 .search-input {
   margin-bottom: 12px !important;
   width: 100% !important;
 }

.search-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 12px !important;
  height: 42px !important;
  padding: 0 16px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.03) !important;
  transition: all 0.3s ease !important;
}

.search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(64, 158, 255, 0.15) !important;
  transform: translateY(-1px) !important;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px var(--primary-color), 0 4px 12px rgba(64, 158, 255, 0.1) !important;
  background: white !important;
}

.search-input :deep(.el-input__prefix-inner) {
  color: var(--primary-color) !important;
}

.search-input :deep(.el-input__inner) {
  font-weight: 500 !important;
  color: var(--text-color) !important;
}

.search-input :deep(.el-input__suffix-inner .el-icon) {
  color: var(--text-color-secondary) !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
}

.search-input :deep(.el-input__suffix-inner .el-icon:hover) {
  color: var(--primary-color) !important;
  transform: scale(1.1) !important;
}




.template-select-dropdown .el-select-dropdown__item.selected, .el-select-dropdown.template-select-dropdown .el-select-dropdown__item.is-selected {
  background: rgba(5, 102, 200, 0.1) !important;
  border-color: rgba(64, 158, 255, 0.2) !important;
  color: var(--primary-color) !important;
  font-weight: 500 !important;
  border-radius: 10px !important;
  padding: 0 8px !important;
}
.template-name {
  font-weight: 500 !important;
  font-size: 14px !important;
  color: var(--text-color) !important;
}

.template-option .el-tag {
  background: rgba(64, 158, 255, 0.15) !important;
  border-color: rgba(64, 158, 255, 0.25) !important;
}

/* 深色模式适配 */
html.dark .search-input :deep(.el-input__wrapper) {
  background: rgba(35, 38, 45, 0.9) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.08) !important;
}

html.dark .search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(64, 158, 255, 0.3) !important;
}

html.dark .search-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px var(--primary-color), 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  background: rgba(40, 44, 52, 0.98) !important;
}

html.dark .search-input :deep(.el-input__inner) {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .template-select :deep(.el-input__wrapper) {
  background: rgba(35, 38, 45, 0.9) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.08) !important;
}

html.dark .template-select :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(64, 158, 255, 0.3) !important;
}

html.dark .template-select :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px var(--primary-color), 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  background: rgba(40, 44, 52, 0.98) !important;
}

html.dark .template-select :deep(.el-select__tags-text),
html.dark .template-name {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .template-select :deep(.el-tag) {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: rgba(64, 158, 255, 0.3) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

html.dark .template-option .el-tag {
  background: rgba(64, 158, 255, 0.15) !important;
  border-color: rgba(64, 158, 255, 0.25) !important;
}

html.dark .search-input :deep(.el-input__suffix-inner .el-icon) {
  color: rgba(255, 255, 255, 0.6) !important;
}

html.dark .search-input :deep(.el-input__suffix-inner .el-icon:hover) {
  color: var(--primary-color) !important;
}

   /* 搜索框增强边框 */
 .search-input :deep(.el-input__wrapper) {
   border: 1px solid rgba(210, 215, 220, 0.2) !important;
   box-shadow:
           0 2px 8px rgba(0, 0, 0, 0.08),
           0 0 0 1px rgba(210, 215, 220, 0.25) !important;
 }

.search-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(64, 158, 255, 0.4) !important;
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.12),
          0 0 0 1px rgba(64, 158, 255, 0.4) !important;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-color) !important;
  box-shadow:
          0 4px 12px rgba(64, 158, 255, 0.15),
          0 0 0 2px rgba(64, 158, 255, 0.35) !important;
}

/* 模板选择器增强边框 */
.template-select :deep(.el-input__wrapper) {
  border: 1px solid rgba(210, 215, 220, 0.2) !important;
  box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.08),
          0 0 0 1px rgba(210, 215, 220, 0.25) !important;
}

.template-select :deep(.el-input__wrapper:hover) {
  border-color: rgba(64, 158, 255, 0.4) !important;
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.12),
          0 0 0 1px rgba(64, 158, 255, 0.4) !important;
}

.template-select :deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-color) !important;
  box-shadow:
          0 4px 12px rgba(64, 158, 255, 0.15),
          0 0 0 2px rgba(64, 158, 255, 0.35) !important;
}

/* 深色模式增强边框 */
html.dark .search-input :deep(.el-input__wrapper),
html.dark .template-select :deep(.el-input__wrapper) {
  border: 1px solid rgba(80, 90, 110, 0.5) !important;
  box-shadow:
          0 2px 10px rgba(0, 0, 0, 0.25),
          0 0 0 1px rgba(100, 120, 150, 0.4),
          inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  background: rgba(45, 50, 60, 0.95) !important;
}

html.dark .search-input :deep(.el-input__wrapper:hover),
html.dark .template-select :deep(.el-input__wrapper:hover) {
  border-color: rgba(64, 158, 255, 0.5) !important;
  box-shadow:
          0 4px 15px rgba(0, 0, 0, 0.3),
          0 0 0 1px rgba(64, 158, 255, 0.5),
          inset 0 0 0 1px rgba(255, 255, 255, 0.08) !important;
  background: rgba(50, 55, 65, 0.98) !important;
}

html.dark .search-input :deep(.el-input__wrapper.is-focus),
html.dark .template-select :deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-color) !important;
  box-shadow:
          0 4px 15px rgba(64, 158, 255, 0.25),
          0 0 0 2px rgba(64, 158, 255, 0.5),
          inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  background: rgba(53, 58, 68, 1) !important;
}

/* 增加输入文本对比度 */
html.dark .search-input :deep(.el-input__inner),
html.dark .template-select :deep(.el-input__inner) {
  color: rgba(255, 255, 255, 1) !important;
}

/* 增强图标可见性 */
html.dark .search-input :deep(.el-input__prefix-inner),
html.dark .template-select :deep(.el-select__caret) {
  color: rgba(255, 255, 255, 0.85) !important;
}

/* 为控件添加微妙的高亮边缘 */
html.dark .filter-section {
  position: relative !important;
}

html.dark .filter-section::after {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  pointer-events: none !important;
  background:
          linear-gradient(90deg,
                  rgba(64, 158, 255, 0.15) 0%,
                  transparent 10%,
                  transparent 90%,
                  rgba(64, 158, 255, 0.15) 100%) !important;
  z-index: 1 !important;
  border-radius: 12px !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

html.dark .filter-section:hover::after {
  opacity: 1 !important;
}

 .add-relation-btn {
   background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
   border: none !important;
   color: white !important;
   font-weight: 600 !important;
   font-size: 15px !important;
   height: 48px !important;
   border-radius: 14px !important;
   letter-spacing: 0.5px !important;
   transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
   box-shadow:
           0 4px 15px rgba(37, 99, 235, 0.25),
           0 2px 5px rgba(0, 0, 0, 0.1),
           inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
   position: relative !important;
   overflow: hidden !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   gap: 8px !important;
   padding: 0 24px !important;
   transform: translateZ(0) !important;
 }

/* 背景发光效果 */
.add-relation-btn::before {
  content: "" !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  background: linear-gradient(135deg, #60a5fa40, #3b82f640) !important;
  z-index: -1 !important;
  filter: blur(8px) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
  border-radius: 16px !important;
}

/* 内部纹理 */
.add-relation-btn::after {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background:
          radial-gradient(circle at 20% 35%, rgba(255, 255, 255, 0.2), transparent 50%),
          radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.2), transparent 30%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

/* 悬停效果 */
.add-relation-btn:hover {
  transform: translateY(-2px) translateZ(0) !important;
  box-shadow:
          0 6px 20px rgba(37, 99, 235, 0.35),
          0 3px 8px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  background: linear-gradient(135deg, #4f8df9, #2b6ff4) !important;
}

.add-relation-btn:hover::before {
  opacity: 1 !important;
}

/* 点击效果 */
.add-relation-btn:active {
  transform: translateY(0) translateZ(0) !important;
  box-shadow:
          0 3px 10px rgba(37, 99, 235, 0.2),
          0 1px 3px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  background: linear-gradient(135deg, #3572e5, #1d5ad0) !important;
  transition: all 0.1s ease !important;
}

/* 图标样式 */
.add-relation-btn .el-icon {
  font-size: 18px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
  padding: 4px !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  z-index: 1 !important;
  transition: all 0.3s ease !important;
}

.add-relation-btn:hover .el-icon {
  background: rgba(255, 255, 255, 0.25) !important;
  transform: scale(1.1) !important;
}

.add-relation-btn:active .el-icon {
  transform: scale(0.95) !important;
  background: rgba(255, 255, 255, 0.15) !important;
}

/* 文字样式 */
.add-relation-btn span {
  position: relative !important;
  z-index: 1 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* 深色主题适配 */
html.dark .add-relation-btn {
  background: linear-gradient(135deg, #3b82f6, #1e40af) !important;
  box-shadow:
          0 4px 15px rgba(59, 130, 246, 0.3),
          0 2px 5px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

html.dark .add-relation-btn:hover {
  background: linear-gradient(135deg, #4f8df9, #2563eb) !important;
  box-shadow:
          0 6px 20px rgba(59, 130, 246, 0.4),
          0 3px 8px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

html.dark .add-relation-btn:active {
  background: linear-gradient(135deg, #3572e5, #1e40af) !important;
  box-shadow:
          0 3px 10px rgba(59, 130, 246, 0.25),
          0 1px 3px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

/* 白色主题特别适配 */
@media (prefers-color-scheme: light) {
  .add-relation-btn {
    color: white !important; /* 确保在白色主题下文字始终为白色 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important; /* 增强文字对比度 */
  }
}

html:not(.dark) .add-relation-btn {
  color: white !important; /* 确保在白色主题下文字始终为白色 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important; /* 增强文字对比度 */
}

 .empty-graph {
   display: flex !important;
   flex-direction: column !important;
   align-items: center !important;
   justify-content: center !important;
   min-height: 400px !important;
   padding: 40px !important;
   background: rgba(255, 255, 255, 0.02) !important;
   border-radius: 16px !important;
   border: 1px dashed rgba(200, 210, 220, 0.3) !important;
   position: relative !important;
   overflow: hidden !important;
 }

.empty-graph::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background:
          radial-gradient(circle at 30% 30%, rgba(64, 158, 255, 0.03), transparent 70%),
          radial-gradient(circle at 70% 60%, rgba(103, 194, 58, 0.03), transparent 70%) !important;
  z-index: -1 !important;
}

/* 空状态提示文字 */
.empty-text {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: var(--text-color-secondary) !important;
  margin-bottom: 24px !important;
  text-align: center !important;
  max-width: 460px !important;
  line-height: 1.6 !important;
}

/* 空状态图标 */
.empty-icon {
  font-size: 64px !important;
  margin-bottom: 20px !important;
  color: rgba(64, 158, 255, 0.3) !important;
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.1)) !important;
}

/* 空状态按钮组 */
.empty-actions {
  display: flex !important;
  gap: 16px !important;
  flex-wrap: wrap !important;
  justify-content: center !important;
}

/* 创建角色按钮 */
.create-entity-btn {
  height: 44px !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  font-size: 15px !important;
  padding: 0 20px !important;
  background: var(--primary-gradient) !important;
  color: white !important;
  border: none !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow:
          0 4px 12px rgba(37, 99, 235, 0.2),
          0 2px 4px rgba(0, 0, 0, 0.05),
          inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

.create-entity-btn::after {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background:
          radial-gradient(circle at 20% 35%, rgba(255, 255, 255, 0.15), transparent 40%),
          radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.15), transparent 30%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

.create-entity-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow:
          0 6px 16px rgba(37, 99, 235, 0.3),
          0 3px 6px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.create-entity-btn:active {
  transform: translateY(0) !important;
  box-shadow:
          0 2px 8px rgba(37, 99, 235, 0.15),
          0 1px 3px rgba(0, 0, 0, 0.05),
          inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.1s ease !important;
}

/* 导入数据按钮 - 次要样式 */
.import-data-btn {
  height: 44px !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  font-size: 15px !important;
  padding: 0 20px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  color: var(--primary-color) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.04),
          0 2px 4px rgba(0, 0, 0, 0.02) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

.import-data-btn:hover {
  transform: translateY(-2px) !important;
  background: white !important;
  box-shadow:
          0 6px 16px rgba(0, 0, 0, 0.08),
          0 2px 4px rgba(64, 158, 255, 0.1) !important;
  border-color: rgba(64, 158, 255, 0.5) !important;
}

.import-data-btn:active {
  transform: translateY(0) !important;
  box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.04),
          0 1px 2px rgba(0, 0, 0, 0.02) !important;
  transition: all 0.1s ease !important;
}

/* 按钮图标 */
.empty-actions .el-button .el-icon {
  font-size: 16px !important;
}

/* 深色模式适配 */
html.dark .empty-graph {
  background: rgba(40, 44, 52, 0.3) !important;
  border-color: rgba(80, 85, 95, 0.4) !important;
}

html.dark .empty-text {
  color: rgba(255, 255, 255, 0.6) !important;
}

html.dark .empty-icon {
  color: rgba(64, 158, 255, 0.35) !important;
  filter: drop-shadow(0 3px 10px rgba(64, 158, 255, 0.15)) !important;
}

html.dark .import-data-btn {
  background: rgba(50, 55, 65, 0.8) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(64, 158, 255, 0.3) !important;
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.15),
          0 2px 4px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

html.dark .import-data-btn:hover {
  background: rgba(55, 60, 70, 0.9) !important;
  border-color: rgba(64, 158, 255, 0.5) !important;
  box-shadow:
          0 6px 16px rgba(0, 0, 0, 0.2),
          0 2px 6px rgba(64, 158, 255, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

/* 引导线条装饰 */
.empty-graph::after {
  content: "" !important;
  position: absolute !important;
  width: 80% !important;
  height: 80% !important;
  top: 10% !important;
  left: 10% !important;
  background-image:
          radial-gradient(circle at center, rgba(64, 158, 255, 0.05) 1px, transparent 1px),
          linear-gradient(to right, rgba(64, 158, 255, 0.03) 1px, transparent 1px),
          linear-gradient(to bottom, rgba(64, 158, 255, 0.03) 1px, transparent 1px) !important;
  background-size: 40px 40px, 20px 20px, 20px 20px !important;
  background-position: center !important;
  opacity: 0.6 !important;
  z-index: -1 !important;
  pointer-events: none !important;
}

html.dark .empty-graph::after {
  opacity: 0.3 !important;
}

 .empty-graph .add-relation-btn {
   background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
   border: none !important;
   color: white !important;
   font-weight: 600 !important;
   font-size: 15px !important;
   height: 44px !important;
   border-radius: 12px !important;
   padding: 0 24px !important;
   box-shadow:
           0 4px 12px rgba(37, 99, 235, 0.3),
           0 2px 5px rgba(0, 0, 0, 0.15),
           inset 0 1px 0 rgba(255, 255, 255, 0.15),
           0 0 0 1px rgba(30, 64, 175, 0.5) !important;
   position: relative !important;
   overflow: hidden !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   gap: 8px !important;
   transition: all 0.3s ease !important;
 }

/* 确保文字可见 */
.empty-graph .add-relation-btn span {
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  font-weight: 600 !important;
  position: relative !important;
  z-index: 2 !important;
}

/* 图标样式 */
.empty-graph .add-relation-btn .el-icon {
  color: white !important;
  font-size: 16px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
  padding: 4px !important;
  position: relative !important;
  z-index: 2 !important;
}

/* 悬停效果 */
.empty-graph .add-relation-btn:hover {
  background: linear-gradient(135deg, #4f8df9, #2563eb) !important;
  transform: translateY(-2px) !important;
  box-shadow:
          0 6px 18px rgba(37, 99, 235, 0.4),
          0 3px 8px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 0 0 1px rgba(37, 99, 235, 0.6) !important;
}

/* 点击效果 */
.empty-graph .add-relation-btn:active {
  transform: translateY(0) !important;
  background: linear-gradient(135deg, #3572e5, #1d4ed8) !important;
  box-shadow:
          0 2px 8px rgba(37, 99, 235, 0.3),
          0 1px 3px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.1),
          0 0 0 1px rgba(30, 64, 175, 0.5) !important;
  transition: all 0.1s ease !important;
}

/* 发光边框增强可见性 */
.empty-graph .add-relation-btn::before {
  content: "" !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  background: linear-gradient(135deg, #60a5fa, #3b82f6) !important;
  z-index: -1 !important;
  filter: blur(6px) !important;
  opacity: 0.6 !important;
  border-radius: 14px !important;
}

/* 内部高光 */
.empty-graph .add-relation-btn::after {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background:
          radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.3), transparent 40%),
          radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.2), transparent 30%) !important;
  z-index: 1 !important;
  pointer-events: none !important;
}

/* 特别确保白色主题下的可见性 */
html:not(.dark) .empty-graph .add-relation-btn {
  /* 加深背景色确保白色文字清晰可见 */
  background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
}

html:not(.dark) .empty-graph .add-relation-btn span,
html:not(.dark) .empty-graph .add-relation-btn .el-icon {
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
}

/* 强化深色主题下的效果 */
html.dark .empty-graph .add-relation-btn {
  background: linear-gradient(135deg, #3b82f6, #1e40af) !important;
  box-shadow:
          0 4px 15px rgba(59, 130, 246, 0.4),
          0 2px 5px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1),
          0 0 0 1px rgba(59, 130, 246, 0.5) !important;
}

html.dark .empty-graph .add-relation-btn::before {
  opacity: 0.7 !important;
  filter: blur(8px) !important;
}

   /* empty-add-btn 按钮样式修复 */
 .empty-add-btn {
   background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
   border: none !important;
   color: white !important;
   font-weight: 600 !important;
   font-size: 15px !important;
   height: 44px !important;
   border-radius: 12px !important;
   padding: 0 20px !important;
   box-shadow:
           0 4px 12px rgba(37, 99, 235, 0.3),
           0 2px 5px rgba(0, 0, 0, 0.15),
           inset 0 1px 0 rgba(255, 255, 255, 0.15),
           0 0 0 1px rgba(30, 64, 175, 0.5) !important;
   position: relative !important;
   overflow: hidden !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   gap: 8px !important;
   transition: all 0.3s ease !important;
 }

/* 确保文字可见 */
.empty-add-btn span {
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  font-weight: 600 !important;
  position: relative !important;
  z-index: 2 !important;
}

/* 图标样式 */
.empty-add-btn .el-icon {
  color: white !important;
  font-size: 16px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
  padding: 4px !important;
  position: relative !important;
  z-index: 2 !important;
}

/* 悬停效果 */
.empty-add-btn:hover {
  background: linear-gradient(135deg, #4f8df9, #2563eb) !important;
  transform: translateY(-2px) !important;
  box-shadow:
          0 6px 18px rgba(37, 99, 235, 0.4),
          0 3px 8px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 0 0 1px rgba(37, 99, 235, 0.6) !important;
}

/* 点击效果 */
.empty-add-btn:active {
  transform: translateY(0) !important;
  background: linear-gradient(135deg, #3572e5, #1d4ed8) !important;
  box-shadow:
          0 2px 8px rgba(37, 99, 235, 0.3),
          0 1px 3px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.1),
          0 0 0 1px rgba(30, 64, 175, 0.5) !important;
  transition: all 0.1s ease !important;
}

/* 发光效果 */
.empty-add-btn::before {
  content: "" !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  background: linear-gradient(135deg, #60a5fa, #3b82f6) !important;
  z-index: -1 !important;
  filter: blur(6px) !important;
  opacity: 0.6 !important;
  border-radius: 14px !important;
}

/* 内部高光 */
.empty-add-btn::after {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background:
          radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.3), transparent 40%),
          radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.2), transparent 30%) !important;
  z-index: 1 !important;
  pointer-events: none !important;
}

/* 白色主题下特别处理 */
html:not(.dark) .empty-add-btn {
  background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
}

html:not(.dark) .empty-add-btn span,
html:not(.dark) .empty-add-btn .el-icon {
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
}

/* 深色主题下的优化 */
html.dark .empty-add-btn {
  background: linear-gradient(135deg, #3b82f6, #1e40af) !important;
  box-shadow:
          0 4px 15px rgba(59, 130, 246, 0.4),
          0 2px 5px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1),
          0 0 0 1px rgba(59, 130, 246, 0.5) !important;
}

html.dark .empty-add-btn::before {
  opacity: 0.7 !important;
  filter: blur(8px) !important;
}

   /* 提升输入框和选择器的质感与可见性 */

   /* 搜索框增强 */
 .search-input :deep(.el-input__wrapper) {
   background: rgba(255, 255, 255, 0.9) !important;
   border: 1px solid rgba(200, 210, 220, 0.5) !important;
   border-radius: 12px !important;
   height: 42px !important;
   box-shadow:
           0 4px 12px rgba(0, 0, 0, 0.08),
           0 2px 4px rgba(0, 0, 0, 0.03),
           inset 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
   transition: all 0.3s ease !important;
   padding: 0 16px !important;
 }

.search-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(64, 158, 255, 0.5) !important;
  background: white !important;
  box-shadow:
          0 6px 16px rgba(0, 0, 0, 0.1),
          0 2px 6px rgba(0, 0, 0, 0.05),
          0 0 0 1px rgba(64, 158, 255, 0.3) !important;
  transform: translateY(-1px) !important;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: rgba(64, 158, 255, 0.8) !important;
  background: white !important;
  box-shadow:
          0 6px 18px rgba(64, 158, 255, 0.15),
          0 2px 8px rgba(0, 0, 0, 0.06),
          0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 搜索图标 */
.search-input :deep(.el-input__prefix-inner) {
  color: rgba(64, 158, 255, 0.8) !important;
  margin-right: 8px !important;
}

/* 模板选择器增强 */
.template-select :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(200, 210, 220, 0.5) !important;
  border-radius: 12px !important;
  height: 42px !important;
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.08),
          0 2px 4px rgba(0, 0, 0, 0.03),
          inset 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
  transition: all 0.3s ease !important;
  padding: 4px 16px !important;
}

.template-select :deep(.el-input__wrapper:hover) {
  border-color: rgba(64, 158, 255, 0.5) !important;
  background: white !important;
  box-shadow:
          0 6px 16px rgba(0, 0, 0, 0.08),
          0 2px 6px rgba(0, 0, 0, 0.04),
          0 0 0 1px rgba(64, 158, 255, 0.3) !important;
  transform: translateY(-1px) !important;
}

.template-select :deep(.el-input__wrapper.is-focus) {
  border-color: rgba(64, 158, 255, 0.8) !important;
  background: white !important;
  box-shadow:
          0 6px 18px rgba(64, 158, 255, 0.15),
          0 2px 8px rgba(0, 0, 0, 0.06),
          0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 深色主题适配 */
html.dark .search-input :deep(.el-input__wrapper),
html.dark .template-select :deep(.el-input__wrapper) {
  background: rgba(40, 44, 52, 0.9) !important;
  border: 1px solid rgba(80, 90, 110, 0.6) !important;
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.25),
          0 2px 6px rgba(0, 0, 0, 0.15),
          inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

html.dark .search-input :deep(.el-input__wrapper:hover),
html.dark .template-select :deep(.el-input__wrapper:hover) {
  background: rgba(45, 50, 60, 0.95) !important;
  border-color: rgba(64, 158, 255, 0.6) !important;
  box-shadow:
          0 6px 18px rgba(0, 0, 0, 0.35),
          0 2px 8px rgba(0, 0, 0, 0.2),
          0 0 0 1px rgba(64, 158, 255, 0.4) !important;
}

html.dark .search-input :deep(.el-input__wrapper.is-focus),
html.dark .template-select :deep(.el-input__wrapper.is-focus) {
  background: rgba(50, 55, 65, 1) !important;
  border-color: rgba(64, 158, 255, 0.7) !important;
  box-shadow:
          0 6px 20px rgba(0, 0, 0, 0.4),
          0 2px 8px rgba(64, 158, 255, 0.25),
          0 0 0 2px rgba(64, 158, 255, 0.3) !important;
}

/* 输入文本样式 */
.search-input :deep(.el-input__inner),
.template-select :deep(.el-input__inner) {
  color: var(--text-color) !important;
  font-weight: 500 !important;
}

html.dark .search-input :deep(.el-input__inner),
html.dark .template-select :deep(.el-input__inner) {
  color: rgba(255, 255, 255, 0.95) !important;
}

/* 输入框内部图标 */
html.dark .search-input :deep(.el-input__prefix-inner) {
  color: rgba(64, 158, 255, 0.9) !important;
}

/* 边缘发光效果 - 增强可见性 */
.search-input,
.template-select-container {
  position: relative !important;
}

.search-input::after,
.template-select-container::after {
  content: "" !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2), rgba(103, 194, 58, 0.2)) !important;
  border-radius: 14px !important;
  z-index: -1 !important;
  filter: blur(8px) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.search-input:hover::after,
.template-select-container:hover::after {
  opacity: 0.5 !important;
}

/* 强调显示输入区域容器 */
.filter-section {
  background: rgba(245, 247, 250, 0.4) !important;
  padding: 16px !important;
  border-radius: 14px !important;
  border: 1px solid rgba(200, 210, 220, 0.4) !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03) !important;
  margin-bottom: 16px !important;
}

html.dark .filter-section {
  background: rgba(35, 38, 45, 0.5) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2) !important;
}

   /* 书籍选择器增强样式 */
 .book-selector :deep(.el-input__wrapper) {
   background: rgba(255, 255, 255, 0.9) !important;
   border: 1px solid rgba(200, 210, 220, 0.5) !important;
   border-radius: 12px !important;
   height: 42px !important;
   box-shadow:
           0 4px 12px rgba(0, 0, 0, 0.06),
           0 2px 4px rgba(0, 0, 0, 0.03),
           inset 0 0 0 1px rgba(255, 255, 255, 0.6) !important;
   transition: all 0.3s ease !important;
 }

.book-selector :deep(.el-input__inner) {
  font-weight: 500 !important;
  color: var(--text-color) !important;
}

.book-selector :deep(.el-input__suffix-inner) {
  color: var(--primary-color) !important;
}

.book-selector :deep(.el-input__wrapper:hover) {
  border-color: rgba(64, 158, 255, 0.5) !important;
  background: white !important;
  box-shadow:
          0 6px 16px rgba(0, 0, 0, 0.08),
          0 2px 6px rgba(0, 0, 0, 0.04),
          0 0 0 1px rgba(64, 158, 255, 0.3) !important;
  transform: translateY(-1px) !important;
}

.book-selector :deep(.el-input__wrapper.is-focus) {
  border-color: rgba(64, 158, 255, 0.8) !important;
  background: white !important;
  box-shadow:
          0 6px 18px rgba(64, 158, 255, 0.15),
          0 2px 8px rgba(0, 0, 0, 0.05),
          0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 下拉菜单样式 */
.book-selector :deep(.el-select__popper.el-popper) {
  border-radius: 14px !important;
  box-shadow:
          0 6px 30px rgba(0, 0, 0, 0.12),
          0 16px 24px rgba(0, 0, 0, 0.06),
          0 2px 6px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(210, 220, 230, 0.5) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  overflow: hidden !important;
}

.book-selector :deep(.el-select-dropdown__wrap) {
  max-height: 280px !important;
  padding: 8px !important;
}

/* 下拉选项样式 */
.book-selector :deep(.el-select-dropdown__item) {
  height: 42px !important;
  line-height: 42px !important;
  border-radius: 10px !important;
  margin: 4px 0 !important;
  padding: 0 16px !important;
  font-weight: 500 !important;
  position: relative !important;
  transition: all 0.2s ease !important;
  overflow: hidden !important;
}

.book-selector :deep(.el-select-dropdown__item:hover) {
  background: rgba(64, 158, 255, 0.1) !important;
}

.book-selector :deep(.el-select-dropdown__item.selected) {
  background: rgba(64, 158, 255, 0.15) !important;
  color: var(--primary-color) !important;
  font-weight: 600 !important;
}

.book-selector :deep(.el-select-dropdown__item.selected::before) {
  content: "" !important;
  position: absolute !important;
  left: 0 !important;
  top: 25% !important;
  height: 50% !important;
  width: 4px !important;
  background: var(--primary-color) !important;
  border-radius: 0 4px 4px 0 !important;
}

/* 书名溢出处理 */
.book-selector :deep(.el-select-dropdown__item span) {
  display: block !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

/* 深色主题适配 */
html.dark .book-selector :deep(.el-input__wrapper) {
  background: rgba(40, 44, 52, 0.9) !important;
  border: 1px solid rgba(80, 90, 110, 0.6) !important;
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.2),
          0 2px 6px rgba(0, 0, 0, 0.1),
          inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

html.dark .book-selector :deep(.el-input__inner) {
  color: rgba(255, 255, 255, 0.95) !important;
}

html.dark .book-selector :deep(.el-input__suffix-inner) {
  color: rgba(64, 158, 255, 0.9) !important;
}

html.dark .book-selector :deep(.el-input__wrapper:hover) {
  background: rgba(45, 50, 60, 0.95) !important;
  border-color: rgba(64, 158, 255, 0.6) !important;
  box-shadow:
          0 6px 18px rgba(0, 0, 0, 0.3),
          0 2px 8px rgba(0, 0, 0, 0.15),
          0 0 0 1px rgba(64, 158, 255, 0.4) !important;
}

html.dark .book-selector :deep(.el-input__wrapper.is-focus) {
  background: rgba(50, 55, 65, 1) !important;
  border-color: rgba(64, 158, 255, 0.7) !important;
  box-shadow:
          0 6px 20px rgba(0, 0, 0, 0.4),
          0 2px 8px rgba(64, 158, 255, 0.25),
          0 0 0 2px rgba(64, 158, 255, 0.3) !important;
}

/* 深色主题下拉菜单 */
html.dark .book-selector :deep(.el-select__popper.el-popper) {
  background: rgba(35, 40, 50, 0.95) !important;
  border: 1px solid rgba(80, 90, 110, 0.6) !important;
  box-shadow:
          0 6px 30px rgba(0, 0, 0, 0.35),
          0 16px 24px rgba(0, 0, 0, 0.2),
          0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

html.dark .book-selector :deep(.el-popper__arrow::before) {
  background: rgba(35, 40, 50, 0.95) !important;
  border-color: rgba(80, 90, 110, 0.6) !important;
}

html.dark .book-selector :deep(.el-select-dropdown__item) {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .book-selector :deep(.el-select-dropdown__item:hover) {
  background: rgba(64, 158, 255, 0.15) !important;
}

html.dark .book-selector :deep(.el-select-dropdown__item.selected) {
  background: rgba(64, 158, 255, 0.25) !important;
  color: rgba(64, 158, 255, 0.95) !important;
}

/* 空状态提示 */
.book-selector :deep(.el-select-dropdown__empty) {
  padding: 16px !important;
  color: var(--text-color-secondary) !important;
  text-align: center !important;
  font-style: italic !important;
}

html.dark .book-selector :deep(.el-select-dropdown__empty) {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* 菜单弹出动画优化 */
.book-selector :deep(.el-popper.is-light) {
  animation: dropdown-in 0.25s cubic-bezier(0.16, 1, 0.3, 1) !important;
  transform-origin: top center !important;
}

@keyframes dropdown-in {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

   /* 页面标题栏美化 - 现代简约拟态风格 */
 .page-header {
   background: linear-gradient(to bottom, rgba(255, 255, 255, 0.92), rgba(250, 252, 254, 0.88)) !important;
   backdrop-filter: blur(15px) !important;
   -webkit-backdrop-filter: blur(15px) !important;
   border-radius: 16px !important;
   padding: 28px 32px !important;
   margin-bottom: 24px !important;
   position: relative !important;
   overflow: hidden !important;
   box-shadow:
           0 8px 24px rgba(0, 0, 0, 0.07),
           0 2px 8px rgba(0, 0, 0, 0.04),
           inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
   border: 1px solid rgba(200, 210, 220, 0.4) !important;
   transition: all 0.3s ease !important;
   display: flex !important;
   align-items: center !important;
   justify-content: space-between !important;
 }

/* 页面标题区域 */
.page-header-content {
  flex: 1 !important;
}

/* 标题文本 */
.page-header .page-title {
  font-size: 28px !important;
  font-weight: 700 !important;
  color: var(--text-color) !important;
  margin-bottom: 8px !important;
  position: relative !important;
}

/* 页面描述 */
.page-header .page-description {
  font-size: 16px !important;
  color: var(--text-color-secondary) !important;
  margin: 0 !important;
  line-height: 1.5 !important;
  max-width: 80% !important;
}

/* 右侧书籍选择器容器 */
.book-selector-container {
  min-width: 280px !important;
  position: relative !important;
  z-index: 3 !important;
  margin-left: 30px !important;
  background: rgba(255, 255, 255, 0.6) !important;
  border-radius: 12px !important;
  padding: 8px !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  box-shadow:
          0 6px 16px rgba(0, 0, 0, 0.05),
          0 2px 4px rgba(0, 0, 0, 0.03),
          inset 0 0 0 1px rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(210, 220, 230, 0.4) !important;
  transition: all 0.3s ease !important;
}

.book-selector-container:hover {
  box-shadow:
          0 8px 20px rgba(0, 0, 0, 0.08),
          0 3px 6px rgba(0, 0, 0, 0.04),
          inset 0 0 0 1px rgba(255, 255, 255, 0.8) !important;
  transform: translateY(-2px) !important;
}

/* 书籍选择器样式优化 */
.book-selector-container .book-selector :deep(.el-input__wrapper) {
  background: white !important;
  border: none !important;
  box-shadow: none !important;
}

.book-selector-container .book-selector :deep(.el-input__inner) {
  font-weight: 500 !important;
}

/* 书籍选择标签 */
.book-selector-label {
  display: block !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  color: var(--text-color-secondary) !important;
  margin-bottom: 6px !important;
  padding-left: 4px !important;
}

/* 深色模式适配 */
html.dark .page-header {
  background: linear-gradient(to bottom,
          rgba(40, 44, 52, 0.92),
          rgba(35, 38, 45, 0.88)) !important;
  box-shadow:
          0 8px 24px rgba(0, 0, 0, 0.2),
          0 2px 8px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(60, 70, 80, 0.5) !important;
}

html.dark .page-header .page-title {
  color: rgba(255, 255, 255, 0.95) !important;
}

html.dark .page-header .page-description {
  color: rgba(255, 255, 255, 0.7) !important;
}

html.dark .book-selector-container {
  background: rgba(50, 55, 65, 0.7) !important;
  box-shadow:
          0 6px 16px rgba(0, 0, 0, 0.15),
          0 2px 4px rgba(0, 0, 0, 0.1),
          inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(70, 80, 95, 0.5) !important;
}

html.dark .book-selector-container:hover {
  box-shadow:
          0 8px 20px rgba(0, 0, 0, 0.2),
          0 3px 6px rgba(0, 0, 0, 0.15),
          inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

html.dark .book-selector-container .book-selector :deep(.el-input__wrapper) {
  background: rgba(40, 44, 52, 0.9) !important;
}

html.dark .book-selector-label {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 添加背景装饰 */
.page-header::before {
  content: "" !important;
  position: absolute !important;
  top: -50% !important;
  right: -10% !important;
  width: 60% !important;
  height: 200% !important;
  background:
          radial-gradient(circle at center,
                  rgba(64, 158, 255, 0.08),
                  transparent 60%) !important;
  filter: blur(30px) !important;
  opacity: 0.8 !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

html.dark .page-header::before {
  opacity: 0.3 !important;
  background:
          radial-gradient(circle at center,
                  rgba(64, 158, 255, 0.1),
                  transparent 70%) !important;
}


   /* 添加角色关系弹窗优化 */
.add-relation-dialog {
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   overflow: hidden !important; /* 防止整个弹窗滚动 */
 }

/* 防止背景滚动 */
.add-relation-dialog :deep(.el-overlay) {
  overflow: hidden !important;
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}

/* 弹窗容器样式 */
.add-relation-dialog :deep(.el-dialog) {
  margin: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow:
          0 20px 60px rgba(0, 0, 0, 0.15),
          0 10px 30px rgba(0, 0, 0, 0.1),
          0 0 0 1px rgba(0, 0, 0, 0.05) !important;
  max-width: 90vw !important;
  max-height: 90vh !important;
  width: 700px !important;
  display: flex !important;
  flex-direction: column !important;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.98), rgba(250, 252, 254, 0.95)) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

/* 弹窗标题区域 */
.add-relation-dialog :deep(.el-dialog__header) {
  padding: 20px 24px !important;
  margin: 0 !important;
  border-bottom: 1px solid rgba(200, 210, 220, 0.5) !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

/* 弹窗标题文本 */
.add-relation-dialog :deep(.el-dialog__title) {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
}

/* 关闭按钮 */
.add-relation-dialog :deep(.el-dialog__headerbtn) {
  top: 20px !important;
  right: 20px !important;
}

.add-relation-dialog :deep(.el-dialog__close) {
  font-size: 18px !important;
  color: var(--text-color-secondary) !important;
  transition: all 0.2s ease !important;
}

.add-relation-dialog :deep(.el-dialog__close:hover) {
  color: var(--primary-color) !important;
  transform: rotate(90deg) !important;
}

/* 弹窗内容区域 */
.add-relation-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
  margin: 0 !important;
  overflow-y: auto !important; /* 只允许垂直滚动 */
  overflow-x: hidden !important; /* 禁止水平滚动 */
  max-height: calc(90vh - 140px) !important; /* 减去标题和底部的高度 */
}

/* 内容容器 */
.add-relation-dialog .dialog-content {
  padding: 24px !important;
}

/* 表单项间距 */
.add-relation-dialog .form-item {
  margin-bottom: 20px !important;
}

/* 表单标签 */
.add-relation-dialog .form-label {
  display: block !important;
  font-size: 15px !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
  margin-bottom: 8px !important;
}

/* 选择器样式 */
.add-relation-dialog .entity-select :deep(.el-select) {
  width: 100% !important;
}

.add-relation-dialog .entity-select :deep(.el-input__wrapper) {
  border-radius: 10px !important;
  box-shadow:
          0 2px 6px rgba(0, 0, 0, 0.05),
          0 0 0 1px rgba(200, 210, 220, 0.5) !important;
  transition: all 0.3s ease !important;
}

.add-relation-dialog .entity-select :deep(.el-input__wrapper:hover) {
  box-shadow:
          0 3px 8px rgba(0, 0, 0, 0.08),
          0 0 0 1px rgba(64, 158, 255, 0.3) !important;
}

.add-relation-dialog .entity-select :deep(.el-input__wrapper.is-focus) {
  box-shadow:
          0 3px 10px rgba(64, 158, 255, 0.1),
          0 0 0 1px rgba(64, 158, 255, 0.5) !important;
}

/* 关系类型选择 */
.add-relation-dialog .relation-type-select {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
  gap: 12px !important;
  margin-top: 10px !important;
}

.add-relation-dialog .relation-type-option {
  padding: 12px !important;
  border-radius: 10px !important;
  border: 1px solid rgba(200, 210, 220, 0.5) !important;
  background: white !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  text-align: center !important;
  font-weight: 500 !important;
  color: var(--text-color) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03) !important;
}

.add-relation-dialog .relation-type-option:hover {
  border-color: rgba(64, 158, 255, 0.4) !important;
  background: rgba(64, 158, 255, 0.05) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06) !important;
}

.add-relation-dialog .relation-type-option.active {
  border-color: rgba(64, 158, 255, 0.6) !important;
  background: rgba(64, 158, 255, 0.1) !important;
  color: var(--primary-color) !important;
  font-weight: 600 !important;
  box-shadow:
          0 4px 10px rgba(64, 158, 255, 0.15),
          0 0 0 1px rgba(64, 158, 255, 0.3) !important;
}

/* 弹窗底部区域 */
.add-relation-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px !important;
  border-top: 1px solid rgba(200, 210, 220, 0.5) !important;
  background: rgba(250, 252, 254, 0.9) !important;
}

/* 按钮样式 */
.add-relation-dialog :deep(.el-button) {
  border-radius: 10px !important;
  font-weight: 600 !important;
  padding: 10px 20px !important;
  transition: all 0.3s ease !important;
}

.add-relation-dialog :deep(.el-button--primary) {
  background: var(--primary-gradient) !important;
  border: none !important;
  box-shadow:
          0 4px 12px rgba(64, 158, 255, 0.25),
          0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.add-relation-dialog :deep(.el-button--primary:hover) {
  transform: translateY(-2px) !important;
  box-shadow:
          0 6px 16px rgba(64, 158, 255, 0.35),
          0 2px 6px rgba(0, 0, 0, 0.1) !important;
}

/* 深色模式适配 */
html.dark .add-relation-dialog :deep(.el-dialog) {
  background: linear-gradient(to bottom, rgba(40, 44, 52, 0.98), rgba(35, 38, 45, 0.95)) !important;
  box-shadow:
          0 20px 60px rgba(0, 0, 0, 0.3),
          0 10px 30px rgba(0, 0, 0, 0.2),
          0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

html.dark .add-relation-dialog :deep(.el-dialog__header) {
  background: rgba(45, 50, 60, 0.8) !important;
  border-bottom: 1px solid rgba(80, 90, 110, 0.5) !important;
}

html.dark .add-relation-dialog :deep(.el-dialog__title) {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .add-relation-dialog :deep(.el-dialog__footer) {
  background: rgba(35, 38, 45, 0.9) !important;
  border-top: 1px solid rgba(80, 90, 110, 0.5) !important;
}

html.dark .add-relation-dialog .form-label {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .add-relation-dialog .entity-select :deep(.el-input__wrapper) {
  background: rgba(50, 55, 65, 0.8) !important;
  box-shadow:
          0 2px 6px rgba(0, 0, 0, 0.15),
          0 0 0 1px rgba(80, 90, 110, 0.5) !important;
}

html.dark .add-relation-dialog .relation-type-option {
  background: rgba(50, 55, 65, 0.8) !important;
  border-color: rgba(80, 90, 110, 0.5) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .add-relation-dialog .relation-type-option:hover {
  background: rgba(64, 158, 255, 0.15) !important;
  border-color: rgba(64, 158, 255, 0.4) !important;
}

html.dark .add-relation-dialog .relation-type-option.active {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: rgba(64, 158, 255, 0.6) !important;
  color: rgba(64, 158, 255, 0.95) !important;
}

/* 自定义滚动条 */
.add-relation-dialog :deep(.el-dialog__body)::-webkit-scrollbar {
  width: 8px !important;
}

.add-relation-dialog :deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: transparent !important;
}

.add-relation-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1) !important;
  border-radius: 4px !important;
}

html.dark .add-relation-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 确保弹窗动画不会导致滚动问题 */
.add-relation-dialog :deep(.el-overlay-dialog) {
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}








/* 确保图标清晰可见 */
.relation-icon-container .el-icon svg {
  width: 18px !important;
  height: 18px !important;
  display: block !important; /* 防止SVG内部空白导致的对齐问题 */
}

/* 实体操作图标容器 */
.entity-actions {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  margin-left: auto !important;
}

/* 实体操作按钮基础样式 */
.entity-action-btn {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  position: relative !important;
  border: none !important;
  outline: none !important;
  background: rgba(64, 158, 255, 0.1) !important;
}

/* 查看属性按钮 */
.view-attributes-btn {
  background: rgba(103, 194, 58, 0.1) !important;
  color: #67c23a !important;
}

.view-attributes-btn:hover {
  background: rgba(103, 194, 58, 0.2) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 3px 8px rgba(103, 194, 58, 0.2) !important;
}

/* 管理关系按钮 */
.manage-relations-btn {
  background: rgba(64, 158, 255, 0.1) !important;
  color: var(--primary-color) !important;
}

.manage-relations-btn:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 3px 8px rgba(64, 158, 255, 0.2) !important;
}

/* 按钮图标 */
.entity-action-btn .el-icon {
  font-size: 18px !important;
  transition: transform 0.2s ease !important;
}

.entity-action-btn:hover .el-icon {
  transform: scale(1.1) !important;
}

/* 按钮提示文本 */
.entity-action-btn::after {
  content: attr(data-tooltip) !important;
  position: absolute !important;
  bottom: -30px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: rgba(0, 0, 0, 0.7) !important;
  color: white !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  white-space: nowrap !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.2s ease !important;
  pointer-events: none !important;
  z-index: 10 !important;
}

.entity-action-btn:hover::after {
  opacity: 1 !important;
  visibility: visible !important;
  bottom: -25px !important;
}

/* 深色模式适配 */
html.dark .view-attributes-btn {
  background: rgba(103, 194, 58, 0.15) !important;
}

html.dark .view-attributes-btn:hover {
  background: rgba(103, 194, 58, 0.25) !important;
}

html.dark .manage-relations-btn {
  background: rgba(64, 158, 255, 0.15) !important;
}

html.dark .manage-relations-btn:hover {
  background: rgba(64, 158, 255, 0.25) !important;
}

/* 修复关系管理弹窗 */
.relation-management-dialog {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  overflow: hidden !important;
}

/* 防止背景滚动 */
.relation-management-dialog :deep(.el-overlay) {
  overflow: hidden !important;
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}

/* 弹窗内容区域优化 */
.relation-management-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
  margin: 0 !important;
  overflow-y: auto !important; /* 允许内容区域垂直滚动 */
  overflow-x: hidden !important;
  max-height: calc(90vh - 140px) !important; /* 适应不同屏幕高度 */
}

/* 关系列表容器 */
.relations-list {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important; /* 增加一些内边距 */
}

/* 自定义滚动条样式 */
.relation-management-dialog :deep(.el-dialog__body)::-webkit-scrollbar {
  width: 6px !important;
}

.relation-management-dialog :deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: transparent !important;
}

.relation-management-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1) !important;
  border-radius: 4px !important;
}

html.dark .relation-management-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 确保弹窗动画不会导致滚动问题 */
.relation-management-dialog :deep(.el-overlay-dialog) {
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.relation-management-dialog :deep(.el-dialog) {
  margin: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow:
          0 20px 60px rgba(0, 0, 0, 0.15),
          0 10px 30px rgba(0, 0, 0, 0.1) !important;
  max-width: 90vw !important;
  max-height: 90vh !important;
  width: 800px !important;
  height: 600px !important; /* 添加固定高度 */
  display: flex !important;
  flex-direction: column !important;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.98), rgba(250, 252, 254, 0.95)) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

/* 弹窗内容容器 */
.relation-management-dialog .dialog-content {
  padding: 24px !important;
  height: 100% !important; /* 填满可用空间 */
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important; /* 防止内容溢出 */
}

/* 关系列表容器样式调整 */
.relations-list {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  padding: 16px !important; /* 增加内边距 */
  overflow-y: auto !important; /* 仅允许垂直滚动 */
  overflow-x: hidden !important; /* 防止水平滚动 */
  flex: 1 !important; /* 填满剩余空间 */
  min-height: 200px !important; /* 最小高度确保空间足够 */
}

/* 空状态容器样式优化 */
.relation-management-dialog .el-empty {
  margin: 40px 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  height: 300px !important; /* 固定高度 */
}

/* 修复底部边距问题 */
.relation-management-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px !important;
  border-top: 1px solid rgba(200, 210, 220, 0.5) !important;
  background: rgba(250, 252, 254, 0.9) !important;
  margin-top: auto !important; /* 推至底部 */
}

/* 确保内容区域有正确的大小 */
.relation-management-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
  margin: 0 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  flex: 1 !important; /* 填充剩余空间 */
  display: flex !important;
  flex-direction: column !important;
}

/* 关系列表样式 */
.relations-list {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
}

.relation-item {
  display: flex !important;
  align-items: center !important;
  padding: 16px !important;
  border-radius: 12px !important;
  background: white !important;
  box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.04),
          0 1px 2px rgba(0, 0, 0, 0.02) !important;
  transition: all 0.2s ease !important;
  position: relative !important;
}

.relation-item:hover {
  box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.08),
          0 2px 4px rgba(0, 0, 0, 0.04) !important;
  transform: translateY(-2px) !important;
}

.relation-type {
  font-weight: 600 !important;
  color: var(--primary-color) !important;
  padding: 4px 10px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border-radius: 20px !important;
  font-size: 14px !important;
  margin-right: 12px !important;
}

.relation-entity {
  font-weight: 500 !important;
  color: var(--text-color) !important;
  flex: 1 !important;
}

.relation-actions {
  display: flex !important;
  gap: 8px !important;
}

.relation-action-btn {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  background: rgba(240, 240, 240, 0.8) !important;
  color: var(--text-color-secondary) !important;
}

.relation-action-btn:hover {
  background: rgba(64, 158, 255, 0.1) !important;
  color: var(--primary-color) !important;
}

.relation-action-btn.delete:hover {
  background: rgba(245, 108, 108, 0.1) !important;
  color: #f56c6c !important;
}

/* 实体属性容器 */
.entity-attributes-container {
  padding: 24px !important;
}

.entity-attributes-container h2 {
  font-size: 24px !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
  margin-bottom: 20px !important;
  padding-bottom: 12px !important;
  border-bottom: 1px solid rgba(200, 210, 220, 0.5) !important;
}

/* 深色模式适配 */
html.dark .relation-management-dialog :deep(.el-dialog),
html.dark .attributes-dialog :deep(.el-dialog) {
  background: linear-gradient(to bottom, rgba(40, 44, 52, 0.98), rgba(35, 38, 45, 0.95)) !important;
}

html.dark .relation-management-dialog :deep(.el-dialog__header),
html.dark .attributes-dialog :deep(.el-dialog__header) {
  background: rgba(45, 50, 60, 0.8) !important;
  border-bottom: 1px solid rgba(80, 90, 110, 0.5) !important;
}

html.dark .relation-management-dialog :deep(.el-dialog__footer),
html.dark .attributes-dialog :deep(.el-dialog__footer) {
  background: rgba(35, 38, 45, 0.9) !important;
  border-top: 1px solid rgba(80, 90, 110, 0.5) !important;
}

html.dark .relation-item {
  background: rgba(50, 55, 65, 0.8) !important;
}

html.dark .relation-action-btn {
  background: rgba(60, 65, 75, 0.8) !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

html.dark .relation-entity {
  color: rgba(255, 255, 255, 0.9) !important;
}

html.dark .entity-attributes-container h2 {
  border-bottom: 1px solid rgba(80, 90, 110, 0.5) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 优化角色选择下拉菜单 */
.entity-select-dropdown,
:deep(.el-select-dropdown.entity-select-dropdown) {
  border-radius: 12px !important;
  padding: 6px !important;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  overflow: visible !important; /* 允许内容溢出 */
  max-height: 400px !important; /* 设置最大高度 */
  border: 1px solid rgba(200, 210, 220, 0.4) !important;
}

.entity-select-dropdown .el-scrollbar,
:deep(.el-select-dropdown.entity-select-dropdown) .el-scrollbar {
  max-height: 380px !important;
}

/* 确保下拉项有足够空间 */
.entity-select-dropdown .el-select-dropdown__item,
:deep(.el-select-dropdown.entity-select-dropdown) .el-select-dropdown__item {
  padding: 8px 10px !important;
  height: auto !important;
  min-height: 46px !important;
  line-height: 1.5 !important;
  border-radius: 8px !important;
  margin: 2px 0 !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* 修改实体选项布局 */
.entity-option {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  gap: 10px !important;
}

/* 调整头像和名称容器 */
.entity-option .entity-main {
  display: flex !important;
  align-items: center !important;
  min-width: 0 !important; /* 允许flex缩小 */
  margin-right: auto !important;
  gap: 8px !important;
}

/* 实体类型标签调整 */
.entity-option .entity-type {
  font-size: 12px !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  color: var(--primary-color) !important;
  white-space: nowrap !important;
  font-weight: 500 !important;
  flex-shrink: 0 !important; /* 防止缩小 */
}

/* 深色主题适配 */
html.dark .entity-select-dropdown,
html.dark :deep(.el-select-dropdown.entity-select-dropdown) {
  background-color: rgba(35, 40, 48, 0.95) !important;
  border: 1px solid rgba(80, 90, 110, 0.5) !important;
}

/* 确保角色名称可见 */
.entity-option .entity-name {
  font-size: 14px !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 120px !important; /* 限制最大宽度 */
  color: var(--text-color) !important;
}

/* 确保选中和悬停状态正确显示 */
html.dark .entity-select-dropdown .el-select-dropdown__item.hover,
html.dark :deep(.el-select-dropdown.entity-select-dropdown) .el-select-dropdown__item:hover {
  background-color: rgba(64, 158, 255, 0.15) !important;
}

html.dark .entity-option .entity-type {
  background: rgba(64, 158, 255, 0.2) !important;
  color: rgba(120, 190, 255, 0.95) !important;
}

/* 修复字体颜色 */
html.dark .entity-option .entity-avatar {
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
}

/* 确保实体类型和角色名对齐 */
.entity-select-dropdown .el-select-dropdown__item,
:deep(.el-select-dropdown.entity-select-dropdown) .el-select-dropdown__item {
  display: flex !important;
  align-items: center !important;
}

/* 优化关系管理弹窗 */
.relation-management-dialog {
  :deep(.el-overlay) {
    overflow: hidden !important;
  }
  
  :deep(.el-dialog) {
    display: flex !important;
    flex-direction: column !important;
    max-height: 80vh !important;
    margin: 0 auto !important;
    border-radius: 16px !important;
    overflow: hidden !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
    background: var(--card-bg) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
  }
  
  :deep(.el-dialog__header) {
    padding: 20px !important;
    margin: 0 !important;
    border-bottom: 1px solid var(--border-color) !important;
    background: rgba(var(--card-bg-rgb), 0.9) !important;
    position: relative !important;
    
    &::after {
      content: "" !important;
      position: absolute !important;
      bottom: -1px !important;
      left: 10% !important;
      right: 10% !important;
      height: 1px !important;
      background: var(--primary-gradient) !important;
      opacity: 0.5 !important;
    }
  }
  
  :deep(.el-dialog__body) {
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  :deep(.el-dialog__footer) {
    padding: 16px 20px !important;
    border-top: 1px solid var(--border-color) !important;
    background: rgba(var(--card-bg-rgb), 0.9) !important;
    margin: 0 !important;
  }
  
  /* 弹窗内容容器 */
  .dialog-content {
    padding: 24px !important;
    overflow-y: auto !important;
  }
  
  /* 关系列表 */
  .dialog-relations-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 16px !important;
    padding: 20px !important;
  }
  
  /* 关系卡片样式 */
  .relation-card {
    background: var(--card-bg) !important;
    border-radius: 12px !important;
    padding: 16px !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
    border: 1px solid var(--border-color) !important;
    
    &:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08) !important;
      border-color: rgba(var(--primary-color-rgb), 0.3) !important;
    }
  }
  
  /* 关系项样式 */
  .relation-item {
    display: flex !important;
    align-items: center !important;
    padding: 16px !important;
    margin-bottom: 12px !important;
    border-radius: 12px !important;
    background: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: var(--shadow-light) !important;
    transition: all 0.3s ease !important;
    
    &:hover {
      box-shadow: var(--shadow-medium) !important;
      transform: translateY(-2px) !important;
      border-color: rgba(var(--primary-color-rgb), 0.3) !important;
    }
    
    .relation-type {
      font-weight: 600 !important;
      color: var(--primary-color) !important;
      padding: 4px 12px !important;
      background: var(--primary-light) !important;
      border-radius: 20px !important;
      font-size: 14px !important;
      margin-right: 12px !important;
      white-space: nowrap !important;
    }
    
    .relation-entity {
      font-weight: 500 !important;
      color: var(--text-color) !important;
    flex: 1 !important;
    overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }
    
    .relation-actions {
      display: flex !important;
      gap: 8px !important;
      margin-left: auto !important;
    }
    
    .relation-action-btn {
      width: 36px !important;
      height: 36px !important;
      border-radius: 50% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      cursor: pointer !important;
      transition: all 0.25s ease !important;
      background: rgba(0, 0, 0, 0.05) !important;
      color: var(--text-color-secondary) !important;
      
      &:hover {
        background: var(--primary-light) !important;
        color: var(--primary-color) !important;
      }
      
      &.delete:hover {
        background: rgba(245, 108, 108, 0.1) !important;
        color: #f56c6c !important;
      }
    }
  }
  
  /* 空状态样式 */
  .el-empty {
    padding: 40px 0 !important;
  }
}

/* 适配暗色模式 */
html.dark .relation-management-dialog {
  :deep(.el-dialog) {
    background: var(--panel-bg) !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.25) !important;
  }
  
  :deep(.el-dialog__header),
  :deep(.el-dialog__footer) {
    background: rgba(35, 40, 48, 0.95) !important;
    border-color: rgba(80, 90, 110, 0.5) !important;
  }
  
  .relation-item {
    background: rgba(40, 44, 52, 0.8) !important;
    border-color: rgba(80, 90, 110, 0.4) !important;
    
    &:hover {
      border-color: rgba(64, 158, 255, 0.4) !important;
      background: rgba(45, 50, 60, 0.9) !important;
    }
    
    .relation-action-btn {
      background: rgba(255, 255, 255, 0.05) !important;
      color: rgba(255, 255, 255, 0.7) !important;
    }
  }
  
  .relation-card {
    background: rgba(40, 44, 52, 0.8) !important;
    border-color: rgba(80, 90, 110, 0.4) !important;
    
    &:hover {
      border-color: rgba(64, 158, 255, 0.4) !important;
    }
  }
}

/* 防止弹窗打开时背景滚动 */
body.el-popup-parent--hidden {
  overflow: hidden !important;
  padding-right: var(--el-popup-scrollbar-width) !important;
}

/* 修改关系管理弹窗样式确保内容可滚动 */
.relation-management-dialog {
  :deep(.el-dialog) {
    display: flex !important;
    flex-direction: column !important;
    max-height: 85vh !important;
    margin: 0 auto !important;
    border-radius: 16px !important;
    overflow: hidden !important;
  }
  
  :deep(.el-dialog__body) {
    /* 关键修改: 设置为flex且有明确高度 */
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important; /* 防止双滚动条 */
  }
  
  /* 滚动区域样式 */
  .relation-scrollbar {
    flex: 1 !important;
    height: 100% !important;
    
    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden !important;
    }
    
    :deep(.el-scrollbar__view) {
      padding: 0 !important;
    }
  }
  
  /* 弹窗内容样式 */
  .dialog-content {
    padding: 24px !important;
  }
  
  /* 关系列表容器 */
  .relations-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 16px !important;
  }
}

/* 关系管理弹窗的过滤区域 */
.relation-management-dialog {
  /* 过滤头部 */
  .relation-filter-header {
    padding: 16px 20px 8px !important;
    border-bottom: 1px solid var(--border-color) !important;
    display: flex !important;
    gap: 12px !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    background: rgba(var(--card-bg-rgb), 0.8) !important;
  }

  /* 搜索输入框 */
  .relation-search-input {
    flex: 1 !important;
    min-width: 200px !important;
    
    :deep(.el-input__wrapper) {
      border-radius: 20px !important;
      box-shadow: 0 0 0 1px var(--border-color) !important;
      padding-left: 12px !important;
      transition: all 0.3s ease !important;
      
      &:hover, &.is-focus {
        box-shadow: 0 0 0 1px var(--primary-color) !important;
      }
      
      .el-input__prefix {
        font-size: 16px !important;
        color: var(--text-color-secondary) !important;
      }
    }
  }
  
  /* 类型筛选下拉框 */
  .relation-type-filter {
    width: 160px !important;
    
    :deep(.el-input__wrapper) {
      border-radius: 20px !important;
      box-shadow: 0 0 0 1px var(--border-color) !important;
      transition: all 0.3s ease !important;
      
      &:hover, &.is-focus {
        box-shadow: 0 0 0 1px var(--primary-color) !important;
      }
    }
  }
  
  /* 调整滚动高度，考虑过滤区域高度 */
  .relation-scrollbar {
    /* 高度调整减去过滤区域的高度 */
    height: calc(65vh - 170px) !important;
  }
}

/* 增强选中实体的视觉效果 */
.entity-item {
  &.selected {
    position: relative !important;
    transform: translateY(-2px) !important;
    z-index: 2 !important;
    
    /* 添加亮边框效果 */
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.3), 
                0 8px 16px rgba(0, 0, 0, 0.1) !important;
    
    /* 添加脉冲动画 */
    &::before {
      content: "" !important;
      position: absolute !important;
      top: -4px !important;
      left: -4px !important;
      right: -4px !important;
      bottom: -4px !important;
      border: 2px solid var(--primary-color) !important;
      border-radius: calc(var(--card-radius) + 4px) !important;
      opacity: 0 !important;
      animation: pulse-border 2s infinite !important;
      pointer-events: none !important;
    }
    
    /* 调整内部元素样式 */
    .entity-name {
      color: var(--primary-deep) !important;
      font-weight: 600 !important;
    }
    
    .entity-avatar {
      transform: scale(1.05) !important;
      box-shadow: 0 0 10px rgba(var(--primary-color-rgb), 0.5) !important;
    }
  }
}

/* 添加脉冲动画关键帧 */
@keyframes pulse-border {
  0% { 
    transform: scale(1); 
    opacity: 0;
  }
  50% { 
    opacity: 0.5;
  }
  100% { 
    transform: scale(1.05); 
    opacity: 0;
  }
}

/* 暗色模式下的选中样式 */
html.dark .entity-item.selected {
  background: rgba(40, 48, 70, 0.9) !important;
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.4), 
              0 8px 16px rgba(0, 0, 0, 0.25) !important;
}

/* 优化添加关系弹窗样式 */
.add-relation-dialog {
  :deep(.el-overlay) {
    overflow: hidden !important;
    backdrop-filter: blur(5px) !important;
    -webkit-backdrop-filter: blur(5px) !important;
  }
  
  :deep(.el-dialog) {
    display: flex !important;
    flex-direction: column !important;
    max-height: 85vh !important;
    border-radius: 18px !important;
    overflow: hidden !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(var(--primary-color-rgb), 0.05) !important;
    background: var(--card-bg) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    transform: translateY(0) !important;
    
    /* 动画效果 */
    &.dialog-fade-enter-active {
      animation: dialog-fade-in 0.3s !important;
    }
    
    &.dialog-fade-leave-active {
      animation: dialog-fade-out 0.3s !important;
    }
  }
  
  :deep(.el-dialog__header) {
    padding: 20px 24px !important;
    margin: 0 !important;
    border-bottom: 1px solid var(--border-color) !important;
    background: var(--card-bg) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    
    .el-dialog__title {
      font-size: 18px !important;
      font-weight: 600 !important;
      color: var(--text-color) !important;
    }
    
    .el-dialog__headerbtn {
      position: static !important;
      top: auto !important;
      right: auto !important;
    }
  }
  
  :deep(.el-dialog__body) {
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
    
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 8px !important;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent !important;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(var(--text-color-rgb), 0.2) !important;
      border-radius: 4px !important;
      border: 2px solid transparent !important;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(var(--text-color-rgb), 0.3) !important;
    }
  }
  
  :deep(.el-dialog__footer) {
    padding: 18px 24px !important;
    margin: 0 !important;
    border-top: 1px solid var(--border-color) !important;
    position: sticky !important;
    bottom: 0 !important;
    z-index: 10 !important;
    background: var(--card-bg) !important;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05) !important;
  }
  
  /* 表单内容样式优化 */
  .dialog-content {
    padding: 24px !important;
    
    .form-item {
      margin-bottom: 24px !important;
      
      &:last-child {
        margin-bottom: 8px !important;
      }
      
      .form-label {
        margin-bottom: 10px !important;
        font-weight: 600 !important;
        color: var(--text-color) !important;
        font-size: 14px !important;
        display: flex !important;
        align-items: center !important;
        
        &.required::before {
          content: "*" !important;
          color: #F56C6C !important;
          margin-right: 4px !important;
        }
      }
    }
    
    /* 角色选择框组样式 */
    .entity-select {
      .mb-2 {
        margin-bottom: 10px !important;
      }
      
      :deep(.el-select) {
        width: 100% !important;
        
        .el-input__wrapper {
          border-radius: 8px !important;
        }
      }
    }
    
    /* 关系类型选择器样式 */
    .relation-type-select {
      display: flex !important;
      flex-wrap: wrap !important;
      gap: 10px !important;
      
      .relation-type-option {
        padding: 8px 16px !important;
        border-radius: 20px !important;
        border: 1px solid var(--border-color) !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        background: rgba(var(--card-bg-rgb), 0.8) !important;
        
        &:hover {
          background: var(--hover-bg) !important;
          transform: translateY(-2px) !important;
        }
        
        &.active {
          background: var(--primary-light) !important;
          border-color: var(--primary-color) !important;
          color: var(--primary-color) !important;
          font-weight: 500 !important;
        }
        
        &.custom-type {
          display: flex !important;
          align-items: center !important;
          gap: 4px !important;
          background: rgba(var(--hover-bg-rgb), 0.7) !important;
        }
      }
    }
    
    /* 关系标签容器样式 */
    .relation-tags-container {
      display: flex !important;
      flex-wrap: wrap !important;
      gap: 8px !important;
      
      .el-tag {
        margin-right: 0 !important;
        border-radius: 16px !important;
        padding: 2px 10px !important;
      }
      
      .tag-input {
        width: 120px !important;
        margin-right: 8px !important;
      }
      
      .button-new-tag {
        border-radius: 20px !important;
        padding: 5px 14px !important;
        height: auto !important;
      }
    }
  }
}

/* 动画效果 */
@keyframes dialog-fade-in {
  0% {
    transform: translateY(30px) !important;
    opacity: 0 !important;
  }
  100% {
    transform: translateY(0) !important;
    opacity: 1 !important;
  }
}

@keyframes dialog-fade-out {
  0% {
    transform: translateY(0) !important;
    opacity: 1 !important;
  }
  100% {
    transform: translateY(30px) !important;
    opacity: 0 !important;
  }
}

/* 黑暗模式适配 */
html.dark .add-relation-dialog {
  :deep(.el-dialog) {
    background: var(--panel-bg) !important;
    box-shadow: 0 16px 50px rgba(0, 0, 0, 0.35), 0 0 0 1px rgba(255, 255, 255, 0.03) !important;
  }
  
  :deep(.el-dialog__header),
  :deep(.el-dialog__footer) {
    background: rgba(30, 35, 45, 0.95) !important;
    border-color: rgba(60, 70, 90, 0.5) !important;
  }
  
  .dialog-content {
    .relation-type-option {
      background: rgba(40, 45, 55, 0.6) !important;
      
      &:hover {
        background: rgba(50, 55, 65, 0.8) !important;
      }
      
      &.active {
        background: rgba(64, 158, 255, 0.15) !important;
      }
    }
  }
}

/* 滚动锁定样式 */
body.el-popup-parent--hidden {
  overflow: hidden !important;
  width: 100% !important;
  height: 100% !important;
  position: fixed !important;
}

/* 重新设计添加关系弹窗样式 */
.add-relation-dialog {
  :deep(.el-overlay) {
    overflow: hidden !important;
    backdrop-filter: blur(5px) !important;
    -webkit-backdrop-filter: blur(5px) !important;
  }
  
  :deep(.el-dialog) {
    display: flex !important;
    flex-direction: column !important;
    max-height: 90vh !important;
    height: auto !important;
    border-radius: 18px !important;
    overflow: hidden !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(var(--primary-color-rgb), 0.05) !important;
    background: var(--card-bg) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    transform: translateY(0) !important;
    
    /* 确保主体区域可以滚动 */
    .el-dialog__body {
      padding: 0 !important;
      margin: 0 !important;
      overflow: hidden !important; /* 关键：确保dialog主体不滚动 */
      display: flex !important;
      flex-direction: column !important;
      height: 100% !important;
    }
  }
  
  :deep(.el-dialog__header) {
    padding: 20px 24px !important;
    margin: 0 !important;
    border-bottom: 1px solid var(--border-color) !important;
    background: var(--card-bg) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    flex-shrink: 0 !important; /* 不收缩 */
  }
  
  /* 包装器用于控制滚动区域 */
  .dialog-body-wrapper {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 24px !important;
    max-height: calc(90vh - 140px) !important; /* 计算高度：90vh减去头部和底部的高度 */
    
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 8px !important;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent !important;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(var(--text-color-rgb), 0.2) !important;
      border-radius: 4px !important;
      border: 2px solid transparent !important;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(var(--text-color-rgb), 0.3) !important;
    }
  }
  
  :deep(.el-dialog__footer) {
    padding: 18px 24px !important;
    margin: 0 !important;
    border-top: 1px solid var(--border-color) !important;
    position: sticky !important;
    bottom: 0 !important;
    z-index: 10 !important;
    background: var(--card-bg) !important;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05) !important;
    flex-shrink: 0 !important; /* 不收缩 */
  }
  
  /* 表单内容样式优化 - 改为两列布局 */
  .dialog-content {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 24px !important;
    
    /* 让部分元素占满整行 */
    .form-item.full-width {
      grid-column: span 2 !important;
    }
    
    /* 默认每个表单项占一列 */
    .form-item {
      margin-bottom: 0 !important;
      
      .form-label {
        margin-bottom: 10px !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        
        &.required::before {
          content: "*" !important;
          color: #F56C6C !important;
          margin-right: 4px !important;
        }
      }
    }
    
    /* 关系描述、关系标签和关系类型占据整行 */
    .form-item:nth-child(3),   /* 关系类型 */
    .form-item:nth-child(6),   /* 关系描述 */
    .form-item:nth-child(7) {  /* 关系标签 */
      grid-column: span 2 !important;
    }
  }
}

/* 其余样式保持不变 */

/* 完全禁止整个弹窗滚动 */
.fixed-dialog {
  :deep(.el-overlay) {
    overflow: hidden !important;
    position: fixed !important;
  }
  
  :deep(.el-dialog) {
    display: flex !important;
    flex-direction: column !important;
    max-height: 85vh !important;  /* 降低最大高度 */
    height: auto !important;      /* 改为自适应高度 */
    min-height: 500px !important; /* 设置最小高度 */
    margin: 7vh auto !important;  /* 调整上下边距 */
    position: relative !important;
    overflow: hidden !important;
  }
  
  /* 保持其他样式 */
}

/* 修改内部滚动区域的样式 */
.dialog-body-wrapper {
  padding: 20px 24px !important; /* 减小内边距 */
  max-height: calc(85vh - 120px) !important; /* 调整最大高度计算 */
}

/* 调整关系类型选择的高度 */
.relation-type-select {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 10px !important;
  max-height: 110px !important; /* 限制最大高度 */
  overflow-y: auto !important; /* 允许垂直滚动 */
}

/* 关系强度样式 */
.relation-strength-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 12px; /* 增加底部间距 */
  
  .strength-slider {
    margin-bottom: 16px;
    padding: 0 8px; /* 增加左右内边距 */
    
    :deep(.el-slider__runway) {
      height: 6px;
      border-radius: 3px;
      background: var(--el-fill-color-light);
      margin: 8px 0; /* 增加上下间距 */
    }
    
    :deep(.el-slider__bar) {
      height: 6px;
      border-radius: 3px;
      background: var(--primary-gradient);
    }
    
    :deep(.el-slider__button) {
      width: 18px;
      height: 18px;
      border: 2px solid var(--primary-color);
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      transition: transform 0.3s;
      
      &:hover, &:active {
        transform: scale(1.15);
      }
    }
    
    :deep(.el-slider__marks-text) {
      font-size: 12px;
      font-weight: 500;
      margin-top: 12px; /* 增加与滑块的距离 */
      color: var(--text-color-secondary);
    }
    
    :deep(.el-slider__marks) {
      height: 20px; /* 确保足够高度显示标记文本 */
    }
  }
  
  .strength-indicator {
    display: flex;
    align-items: center;
    margin-top: 8px; /* 增加与滑块的距离 */
    padding: 0 8px; /* 对齐内边距 */
    
    .strength-value {
      min-width: 40px;
      font-size: 14px;
      font-weight: 600;
      color: var(--text-color);
      margin-right: 12px;
    }
    
    .strength-bar-container {
      flex: 1;
      height: 8px;
      background: var(--el-fill-color-lighter);
      border-radius: 4px;
      overflow: hidden;
    }
    
    .strength-bar {
      height: 100%;
      border-radius: 4px;
      transition: width 0.3s ease, background 0.3s ease;
    }
  }
}

/* 在暗色模式下调整对比度 */
html.dark {
  .relation-strength-container {
    .strength-bar-container {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}
