const fs = require('fs-extra');
const path = require('path');
const dayjs = require('dayjs');
const { nanoid } = require('../utils/idGenerator');

class BookController {
  constructor(baseDir) {
    this.baseDir = baseDir;
    this.booksDir = path.join(baseDir, 'books');
  }

  async initialize() {
    try {
      await fs.ensureDir(this.booksDir);
      console.log('BookController 初始化完成');
    } catch (error) {
      console.error('BookController 初始化失败:', error);
      throw error;
    }
  }

  // ==================== 书籍管理 ====================
  async getBooks(projectId) {
    try {
      const projectBooksDir = path.join(this.booksDir, projectId);
      if (!await fs.pathExists(projectBooksDir)) {
        return [];
      }

      const booksFile = path.join(projectBooksDir, 'books.json');
      if (!await fs.pathExists(booksFile)) {
        return [];
      }

      const books = await fs.readJson(booksFile);
      return books.sort((a, b) => new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt));
    } catch (error) {
      console.error('获取书籍列表失败:', error);
      return [];
    }
  }

  async getBook(bookId) {
    try {
      // 遍历所有项目查找书籍
      const projectDirs = await fs.readdir(this.booksDir);
      
      for (const projectId of projectDirs) {
        const projectBooksDir = path.join(this.booksDir, projectId);
        const booksFile = path.join(projectBooksDir, 'books.json');
        
        if (await fs.pathExists(booksFile)) {
          const books = await fs.readJson(booksFile);
          const book = books.find(b => b.id === bookId);
          if (book) {
            return { ...book, projectId };
          }
        }
      }
      
      return null;
    } catch (error) {
      console.error('获取书籍失败:', error);
      return null;
    }
  }

  async createBook(projectId, bookData) {
    try {
      const projectBooksDir = path.join(this.booksDir, projectId);
      await fs.ensureDir(projectBooksDir);
      
      const booksFile = path.join(projectBooksDir, 'books.json');
      let books = [];
      
      if (await fs.pathExists(booksFile)) {
        books = await fs.readJson(booksFile);
      }

      const newBook = {
        id: nanoid(),
        projectId,
        title: bookData.title || '新书籍',
        subtitle: bookData.subtitle || '',
        author: bookData.author || '',
        genre: bookData.genre || '',
        tags: bookData.tags || [],
        summary: bookData.summary || '',
        status: bookData.status || 'draft', // draft, writing, completed, published
        language: bookData.language || 'zh-CN',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        settings: {
          wordCountTarget: bookData.wordCountTarget || 0,
          chapterTarget: bookData.chapterTarget || 0,
          theme: bookData.theme || 'default',
          fontSize: bookData.fontSize || 14,
          lineHeight: bookData.lineHeight || 1.6,
          ...bookData.settings
        },
        statistics: {
          totalWords: 0,
          totalChapters: 0,
          totalCharacters: 0,
          averageWordsPerChapter: 0,
          lastWriteDate: null,
          writingDays: 0,
          readingTime: 0 // 预估阅读时间（分钟）
        },
        metadata: {
          isbn: bookData.isbn || '',
          publisher: bookData.publisher || '',
          publishDate: bookData.publishDate || null,
          copyright: bookData.copyright || '',
          version: '1.0.0'
        }
      };

      books.push(newBook);
      await fs.writeJson(booksFile, books, { spaces: 2 });

      // 创建书籍专用目录结构
      const bookDir = path.join(projectBooksDir, newBook.id);
      await this.createBookDirectories(bookDir);

      return newBook;
    } catch (error) {
      console.error('创建书籍失败:', error);
      throw error;
    }
  }

  async createBookDirectories(bookDir) {
    const directories = [
      'chapters',      // 章节内容
      'characters',    // 角色设定
      'settings',      // 世界设定
      'timelines',     // 时间线
      'outlines',      // 大纲
      'notes',         // 笔记
      'research',      // 资料
      'drafts',        // 草稿
      'exports',       // 导出文件
      'backups'        // 备份
    ];

    for (const dir of directories) {
      await fs.ensureDir(path.join(bookDir, dir));
    }

    // 创建初始文件
    const initialFiles = {
      'settings/book_settings.json': {
        worldBuilding: {},
        plotStructure: {},
        themes: [],
        conflicts: [],
        createdAt: new Date().toISOString()
      },
      'outlines/main_outline.json': {
        title: '主要大纲',
        structure: [],
        createdAt: new Date().toISOString()
      },
      'notes/general_notes.json': {
        title: '通用笔记',
        content: '',
        createdAt: new Date().toISOString()
      }
    };

    for (const [filePath, content] of Object.entries(initialFiles)) {
      const fullPath = path.join(bookDir, filePath);
      await fs.writeJson(fullPath, content, { spaces: 2 });
    }
  }

  async updateBook(bookId, updateData) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        throw new Error('书籍不存在');
      }

      const { projectId } = bookInfo;
      const projectBooksDir = path.join(this.booksDir, projectId);
      const booksFile = path.join(projectBooksDir, 'books.json');
      
      const books = await fs.readJson(booksFile);
      const bookIndex = books.findIndex(b => b.id === bookId);
      
      if (bookIndex === -1) {
        throw new Error('书籍不存在');
      }

      const existingBook = books[bookIndex];
      const updatedBook = {
        ...existingBook,
        ...updateData,
        id: bookId,
        projectId,
        updatedAt: new Date().toISOString(),
        settings: {
          ...existingBook.settings,
          ...updateData.settings
        },
        metadata: {
          ...existingBook.metadata,
          ...updateData.metadata
        }
      };

      books[bookIndex] = updatedBook;
      await fs.writeJson(booksFile, books, { spaces: 2 });

      return updatedBook;
    } catch (error) {
      console.error('更新书籍失败:', error);
      throw error;
    }
  }

  async deleteBook(bookId) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        throw new Error('书籍不存在');
      }

      const { projectId } = bookInfo;
      const projectBooksDir = path.join(this.booksDir, projectId);
      const booksFile = path.join(projectBooksDir, 'books.json');
      
      const books = await fs.readJson(booksFile);
      const filteredBooks = books.filter(b => b.id !== bookId);
      
      await fs.writeJson(booksFile, filteredBooks, { spaces: 2 });

      // 移动书籍目录到删除标记
      const bookDir = path.join(projectBooksDir, bookId);
      if (await fs.pathExists(bookDir)) {
        const deletedDir = path.join(projectBooksDir, `${bookId}_deleted_${Date.now()}`);
        await fs.move(bookDir, deletedDir);
      }

      return true;
    } catch (error) {
      console.error('删除书籍失败:', error);
      throw error;
    }
  }

  // ==================== 章节管理 ====================
  async getChapters(bookId) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        return [];
      }

      const { projectId } = bookInfo;
      const chaptersDir = path.join(this.booksDir, projectId, bookId, 'chapters');
      
      if (!await fs.pathExists(chaptersDir)) {
        return [];
      }

      const chaptersFile = path.join(chaptersDir, 'chapters.json');
      if (!await fs.pathExists(chaptersFile)) {
        return [];
      }

      const chapters = await fs.readJson(chaptersFile);
      return chapters.sort((a, b) => (a.order || 0) - (b.order || 0));
    } catch (error) {
      console.error('获取章节列表失败:', error);
      return [];
    }
  }

  async getChapter(chapterId) {
    try {
      // 遍历所有书籍查找章节
      const projectDirs = await fs.readdir(this.booksDir);
      
      for (const projectId of projectDirs) {
        const projectBooksDir = path.join(this.booksDir, projectId);
        const bookDirs = await fs.readdir(projectBooksDir);
        
        for (const bookId of bookDirs) {
          if (bookId.endsWith('.json')) continue;
          
          const chaptersDir = path.join(projectBooksDir, bookId, 'chapters');
          const chaptersFile = path.join(chaptersDir, 'chapters.json');
          
          if (await fs.pathExists(chaptersFile)) {
            const chapters = await fs.readJson(chaptersFile);
            const chapter = chapters.find(c => c.id === chapterId);
            if (chapter) {
              // 读取章节内容
              const contentFile = path.join(chaptersDir, `${chapterId}.md`);
              if (await fs.pathExists(contentFile)) {
                chapter.content = await fs.readFile(contentFile, 'utf8');
              }
              return { ...chapter, projectId, bookId };
            }
          }
        }
      }
      
      return null;
    } catch (error) {
      console.error('获取章节失败:', error);
      return null;
    }
  }

  async createChapter(bookId, chapterData) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        throw new Error('书籍不存在');
      }

      const { projectId } = bookInfo;
      const chaptersDir = path.join(this.booksDir, projectId, bookId, 'chapters');
      await fs.ensureDir(chaptersDir);

      const chaptersFile = path.join(chaptersDir, 'chapters.json');
      let chapters = [];

      if (await fs.pathExists(chaptersFile)) {
        chapters = await fs.readJson(chaptersFile);
      }

      // 计算章节顺序
      const maxOrder = chapters.length > 0 ? Math.max(...chapters.map(c => c.order || 0)) : 0;

      const newChapter = {
        id: nanoid(),
        bookId,
        title: chapterData.title || `第${maxOrder + 1}章`,
        subtitle: chapterData.subtitle || '',
        order: chapterData.order ?? maxOrder + 1,
        status: chapterData.status || 'draft', // draft, writing, completed, published
        wordCount: 0,
        characterCount: 0,
        readingTime: 0,
        tags: chapterData.tags || [],
        summary: chapterData.summary || '',
        notes: chapterData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: null,
        settings: {
          autoSave: true,
          wordCountTarget: chapterData.wordCountTarget || 0,
          ...chapterData.settings
        }
      };

      chapters.push(newChapter);
      await fs.writeJson(chaptersFile, chapters, { spaces: 2 });

      // 创建章节内容文件
      const contentFile = path.join(chaptersDir, `${newChapter.id}.md`);
      await fs.writeFile(contentFile, chapterData.content || '', 'utf8');

      // 更新书籍统计
      await this.updateBookStatistics(bookId);

      return newChapter;
    } catch (error) {
      console.error('创建章节失败:', error);
      throw error;
    }
  }

  async updateChapter(chapterId, updateData) {
    try {
      const chapterInfo = await this.getChapter(chapterId);
      if (!chapterInfo) {
        throw new Error('章节不存在');
      }

      const { projectId, bookId } = chapterInfo;
      const chaptersDir = path.join(this.booksDir, projectId, bookId, 'chapters');
      const chaptersFile = path.join(chaptersDir, 'chapters.json');

      const chapters = await fs.readJson(chaptersFile);
      const chapterIndex = chapters.findIndex(c => c.id === chapterId);

      if (chapterIndex === -1) {
        throw new Error('章节不存在');
      }

      // 更新章节内容
      if (updateData.content !== undefined) {
        const contentFile = path.join(chaptersDir, `${chapterId}.md`);
        await fs.writeFile(contentFile, updateData.content, 'utf8');

        // 计算字数统计
        const stats = this.calculateTextStatistics(updateData.content);
        updateData.wordCount = stats.wordCount;
        updateData.characterCount = stats.characterCount;
        updateData.readingTime = stats.readingTime;
      }

      const existingChapter = chapters[chapterIndex];
      const updatedChapter = {
        ...existingChapter,
        ...updateData,
        id: chapterId,
        bookId,
        updatedAt: new Date().toISOString(),
        settings: {
          ...existingChapter.settings,
          ...updateData.settings
        }
      };

      chapters[chapterIndex] = updatedChapter;
      await fs.writeJson(chaptersFile, chapters, { spaces: 2 });

      // 更新书籍统计
      await this.updateBookStatistics(bookId);

      return updatedChapter;
    } catch (error) {
      console.error('更新章节失败:', error);
      throw error;
    }
  }

  async deleteChapter(chapterId) {
    try {
      const chapterInfo = await this.getChapter(chapterId);
      if (!chapterInfo) {
        throw new Error('章节不存在');
      }

      const { projectId, bookId } = chapterInfo;
      const chaptersDir = path.join(this.booksDir, projectId, bookId, 'chapters');
      const chaptersFile = path.join(chaptersDir, 'chapters.json');

      const chapters = await fs.readJson(chaptersFile);
      const filteredChapters = chapters.filter(c => c.id !== chapterId);

      await fs.writeJson(chaptersFile, filteredChapters, { spaces: 2 });

      // 备份并删除内容文件
      const contentFile = path.join(chaptersDir, `${chapterId}.md`);
      if (await fs.pathExists(contentFile)) {
        const backupFile = path.join(chaptersDir, '..', 'backups', `${chapterId}_deleted_${Date.now()}.md`);
        await fs.ensureDir(path.dirname(backupFile));
        await fs.move(contentFile, backupFile);
      }

      // 更新书籍统计
      await this.updateBookStatistics(bookId);

      return true;
    } catch (error) {
      console.error('删除章节失败:', error);
      throw error;
    }
  }

  // ==================== 角色管理 ====================
  async getCharacters(bookId) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        return [];
      }

      const { projectId } = bookInfo;
      const charactersFile = path.join(this.booksDir, projectId, bookId, 'characters', 'characters.json');

      if (!await fs.pathExists(charactersFile)) {
        return [];
      }

      const characters = await fs.readJson(charactersFile);
      return characters.sort((a, b) => new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt));
    } catch (error) {
      console.error('获取角色列表失败:', error);
      return [];
    }
  }

  async createCharacter(bookId, characterData) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        throw new Error('书籍不存在');
      }

      const { projectId } = bookInfo;
      const charactersDir = path.join(this.booksDir, projectId, bookId, 'characters');
      await fs.ensureDir(charactersDir);

      const charactersFile = path.join(charactersDir, 'characters.json');
      let characters = [];

      if (await fs.pathExists(charactersFile)) {
        characters = await fs.readJson(charactersFile);
      }

      const newCharacter = {
        id: nanoid(),
        bookId,
        name: characterData.name || '新角色',
        alias: characterData.alias || [],
        role: characterData.role || 'supporting', // main, supporting, minor, antagonist
        importance: characterData.importance || 'medium', // high, medium, low
        description: characterData.description || '',
        appearance: {
          age: characterData.age || '',
          gender: characterData.gender || '',
          height: characterData.height || '',
          build: characterData.build || '',
          hair: characterData.hair || '',
          eyes: characterData.eyes || '',
          features: characterData.features || '',
          clothing: characterData.clothing || '',
          ...characterData.appearance
        },
        personality: {
          traits: characterData.traits || [],
          strengths: characterData.strengths || [],
          weaknesses: characterData.weaknesses || [],
          fears: characterData.fears || [],
          goals: characterData.goals || [],
          motivations: characterData.motivations || [],
          ...characterData.personality
        },
        background: {
          birthplace: characterData.birthplace || '',
          family: characterData.family || '',
          education: characterData.education || '',
          occupation: characterData.occupation || '',
          history: characterData.history || '',
          relationships: characterData.relationships || [],
          ...characterData.background
        },
        abilities: characterData.abilities || [],
        equipment: characterData.equipment || [],
        notes: characterData.notes || '',
        tags: characterData.tags || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      characters.push(newCharacter);
      await fs.writeJson(charactersFile, characters, { spaces: 2 });

      return newCharacter;
    } catch (error) {
      console.error('创建角色失败:', error);
      throw error;
    }
  }

  async updateCharacter(characterId, updateData) {
    try {
      // 查找角色所在的书籍
      const projectDirs = await fs.readdir(this.booksDir);

      for (const projectId of projectDirs) {
        const projectBooksDir = path.join(this.booksDir, projectId);
        const bookDirs = await fs.readdir(projectBooksDir);

        for (const bookId of bookDirs) {
          if (bookId.endsWith('.json')) continue;

          const charactersFile = path.join(projectBooksDir, bookId, 'characters', 'characters.json');

          if (await fs.pathExists(charactersFile)) {
            const characters = await fs.readJson(charactersFile);
            const characterIndex = characters.findIndex(c => c.id === characterId);

            if (characterIndex !== -1) {
              const existingCharacter = characters[characterIndex];
              const updatedCharacter = {
                ...existingCharacter,
                ...updateData,
                id: characterId,
                bookId,
                updatedAt: new Date().toISOString(),
                appearance: {
                  ...existingCharacter.appearance,
                  ...updateData.appearance
                },
                personality: {
                  ...existingCharacter.personality,
                  ...updateData.personality
                },
                background: {
                  ...existingCharacter.background,
                  ...updateData.background
                }
              };

              characters[characterIndex] = updatedCharacter;
              await fs.writeJson(charactersFile, characters, { spaces: 2 });

              return updatedCharacter;
            }
          }
        }
      }

      throw new Error('角色不存在');
    } catch (error) {
      console.error('更新角色失败:', error);
      throw error;
    }
  }

  async deleteCharacter(characterId) {
    try {
      // 查找角色所在的书籍
      const projectDirs = await fs.readdir(this.booksDir);

      for (const projectId of projectDirs) {
        const projectBooksDir = path.join(this.booksDir, projectId);
        const bookDirs = await fs.readdir(projectBooksDir);

        for (const bookId of bookDirs) {
          if (bookId.endsWith('.json')) continue;

          const charactersFile = path.join(projectBooksDir, bookId, 'characters', 'characters.json');

          if (await fs.pathExists(charactersFile)) {
            const characters = await fs.readJson(charactersFile);
            const filteredCharacters = characters.filter(c => c.id !== characterId);

            if (filteredCharacters.length !== characters.length) {
              await fs.writeJson(charactersFile, filteredCharacters, { spaces: 2 });
              return true;
            }
          }
        }
      }

      throw new Error('角色不存在');
    } catch (error) {
      console.error('删除角色失败:', error);
      throw error;
    }
  }

  // ==================== 设定管理 ====================
  async get_settings_data(bookId) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        return null;
      }

      const { projectId } = bookInfo;
      const settingsFile = path.join(this.booksDir, projectId, bookId, 'settings', 'book_settings.json');

      if (!await fs.pathExists(settingsFile)) {
        return {
          worldBuilding: {},
          plotStructure: {},
          themes: [],
          conflicts: [],
          createdAt: new Date().toISOString()
        };
      }

      return await fs.readJson(settingsFile);
    } catch (error) {
      console.error('获取设定数据失败:', error);
      return null;
    }
  }

  async save_settings_data(bookId, settingsData) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        throw new Error('书籍不存在');
      }

      const { projectId } = bookInfo;
      const settingsDir = path.join(this.booksDir, projectId, bookId, 'settings');
      await fs.ensureDir(settingsDir);

      const settingsFile = path.join(settingsDir, 'book_settings.json');

      const updatedSettings = {
        ...settingsData,
        updatedAt: new Date().toISOString()
      };

      await fs.writeJson(settingsFile, updatedSettings, { spaces: 2 });

      return updatedSettings;
    } catch (error) {
      console.error('保存设定数据失败:', error);
      throw error;
    }
  }

  // ==================== 时间线管理 ====================
  async get_timeline(bookId) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        return null;
      }

      const { projectId } = bookInfo;
      const timelineFile = path.join(this.booksDir, projectId, bookId, 'timelines', 'main_timeline.json');

      if (!await fs.pathExists(timelineFile)) {
        return {
          events: [],
          eventTypes: [],
          createdAt: new Date().toISOString()
        };
      }

      return await fs.readJson(timelineFile);
    } catch (error) {
      console.error('获取时间线失败:', error);
      return null;
    }
  }

  async save_timeline(bookId, timelineData) {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        throw new Error('书籍不存在');
      }

      const { projectId } = bookInfo;
      const timelinesDir = path.join(this.booksDir, projectId, bookId, 'timelines');
      await fs.ensureDir(timelinesDir);

      const timelineFile = path.join(timelinesDir, 'main_timeline.json');

      const updatedTimeline = {
        ...timelineData,
        updatedAt: new Date().toISOString()
      };

      await fs.writeJson(timelineFile, updatedTimeline, { spaces: 2 });

      return updatedTimeline;
    } catch (error) {
      console.error('保存时间线失败:', error);
      throw error;
    }
  }

  // ==================== 统计和工具方法 ====================
  async updateBookStatistics(bookId) {
    try {
      const chapters = await this.getChapters(bookId);

      let totalWords = 0;
      let totalCharacters = 0;
      let totalReadingTime = 0;

      for (const chapter of chapters) {
        totalWords += chapter.wordCount || 0;
        totalCharacters += chapter.characterCount || 0;
        totalReadingTime += chapter.readingTime || 0;
      }

      const averageWordsPerChapter = chapters.length > 0 ? Math.round(totalWords / chapters.length) : 0;

      const statistics = {
        totalWords,
        totalChapters: chapters.length,
        totalCharacters,
        averageWordsPerChapter,
        readingTime: totalReadingTime,
        lastUpdated: new Date().toISOString()
      };

      await this.updateBook(bookId, { statistics });

      return statistics;
    } catch (error) {
      console.error('更新书籍统计失败:', error);
      throw error;
    }
  }

  calculateTextStatistics(text) {
    if (!text) {
      return { wordCount: 0, characterCount: 0, readingTime: 0 };
    }

    // 中文字符统计
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;

    // 英文单词统计
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;

    // 总字数（中文字符 + 英文单词）
    const wordCount = chineseChars + englishWords;

    // 字符数（包括空格和标点）
    const characterCount = text.length;

    // 预估阅读时间（分钟）- 中文约300字/分钟，英文约200词/分钟
    const readingTime = Math.ceil((chineseChars / 300 + englishWords / 200));

    return {
      wordCount,
      characterCount,
      readingTime: Math.max(1, readingTime) // 至少1分钟
    };
  }

  // 导出书籍数据
  async exportBook(bookId, exportPath, format = 'json') {
    try {
      const bookInfo = await this.getBook(bookId);
      if (!bookInfo) {
        throw new Error('书籍不存在');
      }

      const chapters = await this.getChapters(bookId);
      const characters = await this.getCharacters(bookId);
      const settings = await this.get_settings_data(bookId);
      const timeline = await this.get_timeline(bookId);

      // 读取所有章节内容
      for (const chapter of chapters) {
        const chapterInfo = await this.getChapter(chapter.id);
        chapter.content = chapterInfo?.content || '';
      }

      const exportData = {
        book: bookInfo,
        chapters,
        characters,
        settings,
        timeline,
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
      };

      switch (format) {
        case 'json':
          await fs.writeJson(exportPath, exportData, { spaces: 2 });
          break;
        case 'markdown':
          await this.exportToMarkdown(exportData, exportPath);
          break;
        case 'txt':
          await this.exportToText(exportData, exportPath);
          break;
        default:
          throw new Error(`不支持的导出格式: ${format}`);
      }

      return exportData;
    } catch (error) {
      console.error('导出书籍失败:', error);
      throw error;
    }
  }

  async exportToMarkdown(exportData, exportPath) {
    const { book, chapters } = exportData;

    let markdown = `# ${book.title}\n\n`;

    if (book.subtitle) {
      markdown += `## ${book.subtitle}\n\n`;
    }

    if (book.author) {
      markdown += `**作者**: ${book.author}\n\n`;
    }

    if (book.summary) {
      markdown += `**简介**: ${book.summary}\n\n`;
    }

    markdown += '---\n\n';

    for (const chapter of chapters) {
      markdown += `## ${chapter.title}\n\n`;
      if (chapter.subtitle) {
        markdown += `### ${chapter.subtitle}\n\n`;
      }
      markdown += `${chapter.content}\n\n`;
      markdown += '---\n\n';
    }

    await fs.writeFile(exportPath, markdown, 'utf8');
  }

  async exportToText(exportData, exportPath) {
    const { book, chapters } = exportData;

    let text = `${book.title}\n`;
    text += '='.repeat(book.title.length) + '\n\n';

    if (book.subtitle) {
      text += `${book.subtitle}\n\n`;
    }

    if (book.author) {
      text += `作者: ${book.author}\n\n`;
    }

    if (book.summary) {
      text += `简介: ${book.summary}\n\n`;
    }

    text += '-'.repeat(50) + '\n\n';

    for (const chapter of chapters) {
      text += `${chapter.title}\n`;
      text += '-'.repeat(chapter.title.length) + '\n\n';
      text += `${chapter.content}\n\n`;
      text += '-'.repeat(50) + '\n\n';
    }

    await fs.writeFile(exportPath, text, 'utf8');
  }
}

module.exports = BookController;
