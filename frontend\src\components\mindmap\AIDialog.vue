<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    height="500px"
    :close-on-click-modal="false"
    @close="handleClose"
    class="ai-dialog native-style"
    :modal="true"
    :append-to-body="true"
    :destroy-on-close="true"
  >
    <div class="ai-dialog-content">
      <!-- 固定头部：当前节点信息 -->
      <div class="dialog-header">
        <div class="current-node">
          <el-icon class="node-icon"><Document /></el-icon>
          <span class="node-title">{{ currentNode?.title || '未选择节点' }}</span>
          <el-tag v-if="nodeDepth > 0" size="small" type="info">{{ nodeDepth }}层</el-tag>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="dialog-body">
        <!-- 左侧：生成类型选择 -->
        <div class="generation-types">
          <div class="section-title">生成类型</div>
          <div class="type-buttons">
            <el-button
              v-for="template in quickTemplates"
              :key="template.id"
              :type="selectedTemplate?.id === template.id ? 'primary' : 'default'"
              :icon="template.icon"
              @click="selectTemplate(template)"
              class="type-btn"
              size="default"
            >
              {{ template.title }}
            </el-button>
          </div>
        </div>

        <!-- 右侧：参数设置 -->
        <div class="generation-params">
          <div class="section-title">生成参数</div>
          <div class="param-controls">
            <div class="param-item">
              <label>数量</label>
              <el-input-number
                v-model="aiForm.count"
                
                :min="1"
                :max="8"
                size="small"
                controls-position="right"
              />
            </div>
            <div class="param-item">
              <label>详细程度</label>
              <el-select v-model="aiForm.detail" size="small">
                <el-option label="简洁" value="brief" />
                <el-option label="适中" value="moderate" />
                <el-option label="详细" value="detailed" />
              </el-select>
            </div>
            <div class="param-item">
              <label>创意度</label>
              <el-slider
                v-model="creativityValue"
                :min="0"
                :max="100"
                :step="10"
                size="small"
                show-stops
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 提示词预览和编辑 -->
      <div class="prompt-section" v-if="showPromptPreview">
        <div class="section-title">
          提示词预览
          <div class="title-actions">
            <el-button @click="refreshPromptPreview" type="text" size="small">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="showPromptPreview = false" type="text" size="small">隐藏</el-button>
          </div>
        </div>

        <!-- 系统提示词 -->
        <div class="prompt-item">
          <div class="prompt-label">
            <el-icon><Setting /></el-icon>
            系统提示词
          </div>
          <el-input
            v-model="previewPrompts.system"
            type="textarea"
            :rows="3"
            readonly
            class="prompt-textarea system-prompt"
          />
        </div>

        <!-- 用户提示词 -->
        <div class="prompt-item">
          <div class="prompt-label">
            <el-icon><Edit /></el-icon>
            用户提示词
            <el-button @click="enablePromptEdit = !enablePromptEdit" type="text" size="small">
              {{ enablePromptEdit ? '锁定' : '编辑' }}
            </el-button>
          </div>
          <el-input
            v-model="previewPrompts.user"
            type="textarea"
            :rows="6"
            :readonly="!enablePromptEdit"
            :class="['prompt-textarea', 'user-prompt', { 'editable': enablePromptEdit }]"
            placeholder="用户提示词将在这里显示..."
          />
        </div>

        <!-- 提示词统计 -->
        <div class="prompt-stats">
          <el-tag size="small" type="info">
            系统: {{ getPromptLength(previewPrompts.system) }} 字符
          </el-tag>
          <el-tag size="small" type="primary">
            用户: {{ getPromptLength(previewPrompts.user) }} 字符
          </el-tag>
          <el-tag size="small" type="success">
            总计: {{ getTotalPromptLength() }} 字符
          </el-tag>
        </div>
      </div>

      <!-- 自定义提示词（可选） -->
      <div class="custom-prompt" v-if="showCustomPrompt">
        <div class="section-title">
          自定义提示词
          <el-button @click="showCustomPrompt = false" type="text" size="small">隐藏</el-button>
        </div>
        <el-input
          v-model="aiForm.customPrompt"
          type="textarea"
          :rows="3"
          placeholder="输入自定义提示词（可选）"
          resize="none"
        />
      </div>

      <!-- 生成状态显示 -->
      <div v-if="generating" class="generation-status">
        <div class="status-header">
          <el-icon class="rotating"><Loading /></el-icon>
          <span class="status-text">AI正在生成内容...</span>
          <span class="status-time">{{ formatGenerationTime() }}</span>
        </div>
        <el-progress :percentage="progress" :stroke-width="6" />

        <!-- 实时预览 -->
        <div class="live-preview" v-if="generatedContent">
          <div class="preview-label">生成预览：</div>
          <div class="preview-text">{{ generatedContent.substring(0, 200) }}...</div>
        </div>
      </div>



      <!-- 简洁的结果预览 -->
      <div v-if="!generating && generatedContent" class="result-preview">
        <div class="result-header">
          <el-icon class="success-icon"><Check /></el-icon>
          <span>生成完成</span>
          <el-button @click="regenerateContent" type="text" size="small">
            <el-icon><Refresh /></el-icon>
            重新生成
          </el-button>
        </div>
        <div class="result-summary">
          <span class="summary-text">已生成 {{ getContentSummary() }}</span>
        </div>
      </div>

      <!-- 底部操作区域 -->
      <div class="dialog-footer">
        <div class="footer-left">
          <el-button @click="showPromptPreview = !showPromptPreview" type="text" size="small">
            <el-icon><View /></el-icon>
            {{ showPromptPreview ? '隐藏' : '预览' }}提示词
          </el-button>
          <el-button @click="showCustomPrompt = !showCustomPrompt" type="text" size="small">
            <el-icon><Setting /></el-icon>
            {{ showCustomPrompt ? '隐藏' : '自定义' }}提示词
          </el-button>
        </div>
        <div class="footer-right">
          <el-button @click="handleClose" size="default">取消</el-button>
          <el-button
            v-if="!generating && !generatedContent"
            type="primary"
            @click="generateContent"
            :disabled="!canGenerate"
            size="default"
          >
            <el-icon><Star /></el-icon>
            开始生成
          </el-button>
          <el-button
            v-if="generating"
            type="danger"
            @click="stopGeneration"
            size="default"
          >
            停止生成
          </el-button>
          <el-button
            v-if="!generating && generatedContent"
            type="success"
            @click="applyGeneratedContent"
            size="default"
          >
            <el-icon><Check /></el-icon>
            应用到思维导图
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>


<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { nanoid } from 'nanoid'
import {
  Document, Position, Collection, Setting, Back, Loading,
  View, Check, Refresh, Star, Plus, Edit, Files
} from '@element-plus/icons-vue'
import { aiResponseProcessor } from './aiResponseProcessor.js'
import { getModelConfig, optimizePrompt, getOptimalParams, assessResponseQuality } from './modelConfigs.js'
import { useAIProvidersStore } from '../../stores/aiProviders'
import { generatePrompts } from './promptTemplates.js'
import { contextBuilder } from './contextBuilder.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentNode: {
    type: Object,
    default: null
  },
  selectedModel: {
    type: String,
    default: ''
  },
  bookTitle: {
    type: String,
    default: '未命名文档'
  },
  mindmapData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits([
  'update:visible',
  'content-generated'
])

// Store
const aiProvidersStore = useAIProvidersStore()

// 响应式数据
const generating = ref(false)
const progress = ref(0)
const generatedContent = ref('')
const showCustomPrompt = ref(false)
const showPromptPreview = ref(false)
const enablePromptEdit = ref(false)
const selectedTemplate = ref(null)
const currentChatId = ref('')
const generationStartTime = ref(0)
const responseTimeout = ref(null)
const maxGenerationTime = 120000 // 2分钟超时
const lastContentLength = ref(0)
const stagnantTime = ref(0)
const maxStagnantTime = 30000 // 30秒无新内容则停止

// 获取AI提供商模型配置的方法
const getAIProviderModelConfig = (modelUniqueId) => {
  try {
    // 从aiProvidersStore获取模型配置
    const model = aiProvidersStore.allAvailableModels.find(m => m.uniqueId === modelUniqueId || m.id === modelUniqueId)
    if (model && model.config) {
      console.log('AIDialog获取到AI提供商模型配置:', model.config)
      return model.config
    }

    // 如果没有找到配置，返回默认配置
    console.log('AIDialog未找到AI提供商模型配置，使用默认配置')
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  } catch (error) {
    console.error('AIDialog获取AI提供商模型配置失败:', error)
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  }
}

// 提示词预览数据
const previewPrompts = ref({
  system: '',
  user: ''
})

const aiForm = ref({
  mode: 'children',
  domain: 'general',
  detail: 'moderate',
  creativity: 0.7,
  count: 1,
  customPrompt: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => {
  if (!props.currentNode) return 'AI内容生成'
  return `AI生成 - ${props.currentNode.title}`
})

const nodePath = computed(() => {
  if (!props.currentNode || !props.mindmapData) return []
  return getNodePath(props.currentNode, props.mindmapData)
})

const nodeDepth = computed(() => nodePath.value.length - 1)

const siblingNodes = computed(() => {
  if (!props.currentNode || !props.mindmapData) return []
  return getSiblingNodes(props.currentNode, props.mindmapData)
})

// 简化的快速模板
const quickTemplates = computed(() => [
  {
    id: 'children',
    title: '要点分解',
    icon: Plus,
    mode: 'children'
  },
  {
    id: 'subtopics',
    title: '子主题',
    icon: Edit,
    mode: 'subtopics'
  },
  {
    id: 'analysis',
    title: '多角度分析',
    icon: View,
    mode: 'analysis'
  },
  {
    id: 'creative',
    title: '创意发散',
    icon: Star,
    mode: 'creative'
  }
])

// 创意度数值转换
const creativityValue = computed({
  get: () => Math.round(aiForm.value.creativity * 100),
  set: (value) => {
    aiForm.value.creativity = value / 100
  }
})

const canGenerate = computed(() => {
  return props.selectedModel && props.currentNode && selectedTemplate.value
})

// 选择模板
const selectTemplate = (template) => {
  selectedTemplate.value = template
  aiForm.value.mode = template.mode

  // 根据模板设置默认参数
  switch (template.id) {
    case 'subtopics':
      aiForm.value.detail = 'brief'
      aiForm.value.creativity = 0.5
      break
    case 'children':
      aiForm.value.detail = 'detailed'
      aiForm.value.creativity = 0.6
      break
    case 'analysis':
      aiForm.value.detail = 'detailed'
      aiForm.value.creativity = 0.6
      break
    case 'creative':
      aiForm.value.detail = 'moderate'
      aiForm.value.creativity = 0.9
      break
  }
}

// 获取内容摘要
const getContentSummary = () => {
  if (!generatedContent.value) return ''

  const lines = generatedContent.value.split('\n').filter(line => line.trim())
  const nodeCount = lines.filter(line => line.match(/^#+\s+/) || line.match(/^[-*+]\s+/) || line.match(/^\d+\.\s+/)).length

  return `${nodeCount} 个节点，约 ${generatedContent.value.length} 字符`
}

// 获取模式描述
const getModeDescription = (mode) => {
  const descriptions = {
    subtopics: '子主题模式：为当前主题生成下级的子主题分类，每个子主题是当前主题的一个细分领域，适合构建主题的层级结构。',
    children: '要点分解模式：深入分析当前主题，提取其核心要点和细节，包括关键组成部分和实施要点，适合细化当前主题内容。',
    analysis: '多角度分析模式：从不同维度分析主题，每个分析角度作为子节点，具体分析内容作为孙节点。',
    creative: '创意发散模式：生成创新性要点，每个创意点作为子节点，详细说明作为孙节点。'
  }
  return descriptions[mode] || '请选择生成模式'
}

// 格式化生成时间
const formatGenerationTime = () => {
  if (!generating.value || generationStartTime.value === 0) return ''

  const elapsed = Math.floor((Date.now() - generationStartTime.value) / 1000)
  const minutes = Math.floor(elapsed / 60)
  const seconds = elapsed % 60

  if (minutes > 0) {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  } else {
    return `${seconds}s`
  }
}

// 辅助函数
const getNodePath = (node, mindmapData) => {
  let current = mindmapData

  const findPath = (data, targetId, currentPath = []) => {
    if (data.id === targetId) {
      return [...currentPath, data.title]
    }

    if (data.children) {
      for (const child of data.children) {
        const result = findPath(child, targetId, [...currentPath, data.title])
        if (result) return result
      }
    }

    return null
  }

  return findPath(current, node.id) || [node.title]
}

const getSiblingNodes = (node, mindmapData) => {
  const findParent = (data, targetId) => {
    if (data.children) {
      for (const child of data.children) {
        if (child.id === targetId) {
          return data
        }
        const result = findParent(child, targetId)
        if (result) return result
      }
    }
    return null
  }

  const parent = findParent(mindmapData, node.id)
  if (parent && parent.children) {
    return parent.children.filter(child => child.id !== node.id)
  }

  return []
}

// 监听节点变化，重置表单
watch(() => props.currentNode, (newNode) => {
  if (newNode) {
    // 根据节点层级智能设置默认模式
    const depth = nodeDepth.value
    let defaultMode = 'children'
    let defaultDomain = 'general'

    if (depth === 0) {
      defaultMode = 'subtopics'
    } else if (depth >= 3) {
      defaultMode = 'children'
    }

    // 根据节点内容推测领域
    const title = newNode.title?.toLowerCase() || ''
    if (title.includes('角色') || title.includes('人物')) {
      defaultDomain = 'character'
    } else if (title.includes('世界') || title.includes('设定')) {
      defaultDomain = 'worldbuilding'
    } else if (title.includes('情节') || title.includes('剧情')) {
      defaultDomain = 'plot'
    }

    aiForm.value = {
      mode: defaultMode,
      domain: defaultDomain,
      strategy: 'auto',
      detail: 'moderate',
      creativity: 0.7,
      count: 1,
      customPrompt: ''
    }

    generatedContent.value = ''
    progress.value = 0

    // 如果提示词预览是打开的，自动刷新
    if (showPromptPreview.value) {
      refreshPromptPreview()
    }
  }
})

// 监听表单变化，自动更新提示词预览
watch([() => aiForm.value.mode, () => aiForm.value.domain, () => aiForm.value.detail], () => {
  if (showPromptPreview.value) {
    refreshPromptPreview()
  }
})

// 监听提示词预览显示状态
watch(showPromptPreview, (newValue) => {
  if (newValue) {
    refreshPromptPreview()
  }
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  // 重置状态
  generating.value = false
  progress.value = 0
  generatedContent.value = ''
  selectedTemplate.value = null
  showCustomPrompt.value = false
  lastContentLength.value = 0
  stagnantTime.value = 0

  // 清理定时器
  if (responseTimeout.value) {
    clearTimeout(responseTimeout.value)
    responseTimeout.value = null
  }
}



const formatCreativity = (value) => {
  const levels = {
    0.1: '保守',
    0.3: '稳妥',
    0.5: '平衡',
    0.7: '创新',
    0.9: '大胆',
    1.0: '极致'
  }
  return levels[value] || `${Math.round(value * 100)}%`
}

const regenerateContent = () => {
  generatedContent.value = ''
  progress.value = 0
  generateContent()
}

// 提示词预览相关方法
const refreshPromptPreview = () => {
  try {
    console.log('=== 提示词预览调试信息 ===')
    console.log('当前节点:', props.currentNode)
    console.log('思维导图数据:', props.mindmapData)
    console.log('领域:', aiForm.value.domain || 'general')

    // 构建完整上下文
    console.log('AI对话 - 当前节点:', props.currentNode?.title)
    console.log('AI对话 - 思维导图根节点:', props.mindmapData?.title)

    const fullContext = contextBuilder.buildFullContext(
      props.currentNode,
      props.mindmapData,
      aiForm.value.domain || 'general'
    )

    // 生成提示词
    const prompts = generatePrompts(aiForm.value.mode, fullContext)

    // 更新预览
    previewPrompts.value.system = prompts.system
    previewPrompts.value.user = prompts.user

    console.log('提示词预览已更新')
  } catch (error) {
    console.error('生成提示词预览失败:', error)
    ElMessage.error('生成提示词预览失败: ' + error.message)
  }
}

const getPromptLength = (prompt) => {
  return prompt ? prompt.length : 0
}

const getTotalPromptLength = () => {
  return getPromptLength(previewPrompts.value.system) + getPromptLength(previewPrompts.value.user)
}

// 停止生成
const stopGeneration = () => {
  console.log('用户手动停止生成')

  // 清理定时器
  if (responseTimeout.value) {
    clearTimeout(responseTimeout.value)
    responseTimeout.value = null
  }

  // 重置状态
  generating.value = false
  progress.value = 100
  lastContentLength.value = 0
  stagnantTime.value = 0

  // 恢复原始处理函数
  if (window.originalReceiveChunk) {
    window.receiveChunk = window.originalReceiveChunk
  }
  if (window.originalOnMessageComplete) {
    window.onMessageComplete = window.originalOnMessageComplete
  }

  // 如果有部分内容，进行处理
  if (generatedContent.value.trim()) {
    console.log('处理部分生成的内容')
    processPartialContent()
  } else {
    ElMessage.warning('生成已停止，未获得有效内容')
  }
}

// 处理部分内容
const processPartialContent = () => {
  try {
    const modelConfig = getModelConfig(props.selectedModel)
    const processResult = aiResponseProcessor.processResponse(
      generatedContent.value,
      modelConfig.type
    )

    generatedContent.value = processResult.content

    if (processResult.content.trim()) {
      ElMessage.success('已处理部分生成的内容')
    } else {
      ElMessage.warning('生成的内容无法解析')
    }
  } catch (error) {
    console.error('处理部分内容失败:', error)
    ElMessage.error('处理部分内容失败')
  }
}

const generateContent = async () => {
  if (!props.selectedModel) {
    ElMessage.warning('请选择AI模型')
    return
  }

  if (!props.currentNode) {
    ElMessage.warning('请选择要生成内容的节点')
    return
  }

  generating.value = true
  progress.value = 0
  generatedContent.value = ''

  try {
    // 获取模型配置
    const modelConfig = getModelConfig(props.selectedModel)
    console.log('使用模型配置:', modelConfig.name, modelConfig.type)

    // 生成或使用编辑后的提示词
    let systemPrompt, userPrompt

    if (enablePromptEdit.value && previewPrompts.value.user) {
      // 使用用户编辑的提示词
      systemPrompt = previewPrompts.value.system
      userPrompt = previewPrompts.value.user
      console.log('使用用户编辑的提示词')
    } else {
      // 生成新的提示词
      const fullContext = contextBuilder.buildFullContext(
        props.currentNode,
        props.mindmapData,
        aiForm.value.domain || 'general'
      )

      console.log('=== AIDialog 上下文调试 ===')
      console.log('构建的完整上下文:', fullContext)
      console.log('上下文是否有层级信息:', !!fullContext.hierarchy)
      console.log('上下文是否有完整路径:', !!fullContext.hierarchy?.fullPath)
      console.log('上下文是否有祖先节点:', !!fullContext.hierarchy?.ancestors?.length)

      const prompts = generatePrompts(aiForm.value.mode, fullContext)
      systemPrompt = prompts.system
      userPrompt = prompts.user

      console.log('=== 生成的提示词调试 ===')
      console.log('系统提示词长度:', prompts.system.length)
      console.log('用户提示词长度:', prompts.user.length)
      console.log('用户提示词预览:', prompts.user.substring(0, 500) + '...')
      console.log('生成新的提示词')
    }

    console.log('最终使用的提示词:', {
      system: systemPrompt.substring(0, 100) + '...',
      user: userPrompt.substring(0, 100) + '...'
    })

    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ]

    const chatId = nanoid()
    currentChatId.value = chatId
    generationStartTime.value = Date.now()
    lastContentLength.value = 0
    stagnantTime.value = 0

    // 检测模型类型
    const modelType = modelConfig.type
    console.log('检测到模型类型:', modelType)

    // 设置超时检测
    responseTimeout.value = setTimeout(() => {
      console.warn('生成超时，自动停止')
      ElMessage.warning('生成超时，已自动停止')
      stopGeneration()
    }, maxGenerationTime)

    // 设置流式响应处理
    const originalReceiveChunk = window.receiveChunk
    window.originalReceiveChunk = originalReceiveChunk
    window.receiveChunk = (chunk) => {
      try {
        const decodedBytes = atob(chunk)
        const decodedChunk = new TextDecoder('utf-8').decode(
          new Uint8Array([...decodedBytes].map(char => char.charCodeAt(0)))
        )

        // 安全解析JSON
        let messageData
        try {
          messageData = JSON.parse(decodedChunk)
        } catch (parseError) {
          console.warn('流式数据JSON解析失败，尝试直接处理:', decodedChunk.substring(0, 50))
          // 如果不是JSON格式，可能是纯文本流
          if (decodedChunk.trim()) {
            const processedChunk = aiResponseProcessor.processStreamChunk(decodedChunk, modelType)
            if (processedChunk) {
              generatedContent.value += processedChunk
              progress.value = Math.min(progress.value + 2, 90)
            }
          }
          return
        }

        if (messageData.chat_id === chatId && messageData.content) {
          // 使用响应处理器处理流式内容
          const processedChunk = aiResponseProcessor.processStreamChunk(
            messageData.content,
            modelType
          )

          if (processedChunk) {
            const oldLength = generatedContent.value.length
            generatedContent.value += processedChunk
            const newLength = generatedContent.value.length

            // 检测内容增长
            if (newLength > oldLength) {
              lastContentLength.value = newLength
              stagnantTime.value = 0
              progress.value = Math.min(progress.value + 3, 90)
            } else {
              // 内容没有增长，检查停滞时间
              const currentTime = Date.now()
              if (stagnantTime.value === 0) {
                stagnantTime.value = currentTime
              } else if (currentTime - stagnantTime.value > maxStagnantTime) {
                console.warn('内容生成停滞，自动停止')
                ElMessage.warning('检测到生成停滞，已自动停止')
                stopGeneration()
                return
              }
            }
          }
        }
      } catch (error) {
        console.error('处理AI响应失败:', error, '原始chunk长度:', chunk.length)
        // 错误时尝试直接添加内容
        if (typeof chunk === 'string' && chunk.trim()) {
          generatedContent.value += chunk
        }
      }
    }

    const originalOnMessageComplete = window.onMessageComplete
    window.originalOnMessageComplete = originalOnMessageComplete
    window.onMessageComplete = (completedChatId) => {
      if (completedChatId === chatId) {
        // 清理超时定时器
        if (responseTimeout.value) {
          clearTimeout(responseTimeout.value)
          responseTimeout.value = null
        }

        progress.value = 100
        generating.value = false

        // 对完整内容进行最终处理
        const processResult = aiResponseProcessor.processResponse(
          generatedContent.value,
          modelType
        )

        // 更新生成的内容
        generatedContent.value = processResult.content

        // 使用模型配置进行质量评估
        const qualityAssessment = assessResponseQuality(processResult.content, modelConfig)

        // 显示质量评估信息
        if (qualityAssessment.score < 70) {
          console.warn('内容质量较低:', qualityAssessment.issues)
          ElMessage.warning(`内容质量提醒: ${qualityAssessment.issues.join(', ')}`)
        } else if (qualityAssessment.score >= 90) {
          console.log('内容质量优秀!')
        }

        console.log('内容处理完成:', {
          模型: modelConfig.name,
          质量评分: qualityAssessment.score,
          标题数量: qualityAssessment.titleCount,
          唯一性: Math.round(qualityAssessment.uniquenessRatio * 100) + '%',
          压缩比: Math.round(processResult.compressionRatio * 100) + '%'
        })

        // 恢复原始处理函数
        window.receiveChunk = window.originalReceiveChunk
        window.onMessageComplete = window.originalOnMessageComplete
      }
    }

    // 获取模型配置
    const mindmapModelConfig = getModelConfig(props.selectedModel)
    const aiProviderConfig = getAIProviderModelConfig(props.selectedModel)

    // 调用AI API，使用优化的参数
    const optimalParams = getOptimalParams(mindmapModelConfig, aiForm.value)
    const apiParams = {
      stream: true,
      temperature: optimalParams.temperature,
      top_p: 0.9,
      max_tokens: optimalParams.max_tokens,
      ...aiProviderConfig  // 应用AI提供商模型配置
    }

    console.log('使用API参数:', apiParams)

    await window.pywebview.api.model_controller.chat(
      chatId,
      props.selectedModel,
      messages,
      apiParams
    )
  } catch (error) {
    console.error('AI生成失败:', error)
    ElMessage.error('AI生成失败: ' + error.message)
    generating.value = false
  }
}

// buildContextInfo 函数已移除，现在直接在 generateContent 中构建上下文

const getMaxTokens = () => {
  const baseTokens = {
    brief: 1024,
    moderate: 2048,
    detailed: 4096
  }

  const strategyMultiplier = {
    auto: 1,
    comprehensive: 1.5,
    focused: 0.8
  }

  const modeMultiplier = {
    subtopics: 0.8,
    children: 1,
    analysis: 1.3,
    creative: 1.1
  }

  return Math.round(
    baseTokens[aiForm.value.detail] *
    strategyMultiplier[aiForm.value.strategy] *
    modeMultiplier[aiForm.value.mode]
  )
}

// 旧的提示词构建函数已移除，现在使用 promptTemplates.js 中的新方案

// 旧的用户提示词构建函数已移除，现在使用 promptTemplates.js 中的新方案

const applyGeneratedContent = () => {
  if (!generatedContent.value || !props.currentNode) {
    ElMessage.warning('没有可应用的内容')
    return
  }

  // 发送生成的内容给父组件处理
  emit('content-generated', {
    node: props.currentNode,
    mode: aiForm.value.mode,
    domain: aiForm.value.domain,
    content: generatedContent.value.trim(),
    count: aiForm.value.count,
    detail: aiForm.value.detail,
    context: {
      nodePath: nodePath.value,
      nodeDepth: nodeDepth.value,
      siblings: siblingNodes.value
    }
  })

  ElMessage.success('内容已应用到思维导图')

  // 关闭对话框
  handleClose()
}

// 格式化预览内容
const formatPreviewContent = (content) => {
  if (!content) return ''

  // 将markdown转换为HTML
  return content
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/^\- (.*$)/gim, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/^(?!<[h|u|l])/gm, '<p>')
    .replace(/(?<![>])$/gm, '</p>')
    .replace(/<p><\/p>/g, '')
}

// 解析内容结构
const parseContentStructure = (content) => {
  if (!content) return []

  const sections = []
  const lines = content.split('\n')
  let currentSection = null

  lines.forEach(line => {
    const trimmedLine = line.trim()

    if (trimmedLine.startsWith('## ')) {
      // 新的二级标题
      if (currentSection) {
        sections.push(currentSection)
      }
      currentSection = {
        title: trimmedLine.replace('## ', ''),
        content: '',
        children: [],
        level: 2
      }
    } else if (trimmedLine.startsWith('### ')) {
      // 三级标题
      if (currentSection) {
        currentSection.children.push({
          title: trimmedLine.replace('### ', ''),
          content: '',
          level: 3
        })
      }
    } else if (trimmedLine && currentSection) {
      // 内容行
      currentSection.content += (currentSection.content ? '\n' : '') + trimmedLine
    }
  })

  if (currentSection) {
    sections.push(currentSection)
  }

  return sections
}


</script>

<style lang="scss" scoped>
// 原生PC应用风格的AI对话框
.ai-dialog.native-style {
  :deep(.el-dialog) {
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  :deep(.el-dialog__header) {
    padding: 16px 20px 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);
  }

  :deep(.el-dialog__title) {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  :deep(.el-dialog__body) {
    padding: 0;
    height: 400px;
    overflow: hidden;
  }
}

.ai-dialog-content {
  height: 100%;
  display: flex;
  flex-direction: column;

  .dialog-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);

    .current-node {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 14px;
      color: var(--el-text-color-primary);

      .node-icon {
        color: var(--el-color-primary);
      }

      .node-title {
        font-weight: 500;
        flex: 1;
      }
    }
  }

  .quick-templates {
    h4 {
      margin: 0 0 16px 0;
      color: var(--el-text-color-primary);
      font-size: 16px;
      font-weight: 500;
    }

    .template-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin-bottom: 16px;

      .template-card {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: var(--el-fill-color-blank);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: var(--el-color-primary-light-7);
          background: var(--el-color-primary-light-9);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .el-icon {
          font-size: 24px;
          color: var(--el-color-primary);
        }

        .template-info {
          flex: 1;

          .template-title {
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }

          .template-desc {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            line-height: 1.4;
          }
        }
      }
    }

    .template-actions {
      text-align: center;
      padding-top: 8px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }

  // 新的原生风格布局
  .dialog-body {
    flex: 1;
    display: flex;
    padding: 20px;
    gap: 20px;
    overflow: hidden;

    .generation-types {
      flex: 1;

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;
      }

      .type-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .type-btn {
          justify-content: flex-start;
          text-align: left;
          height: 40px;
          border-radius: 6px;

          &.el-button--primary {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
          }
        }
      }
    }

    .generation-params {
      flex: 1;

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;
      }

      .param-controls {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .param-item {
          display: flex;
          flex-direction: column;
          gap: 6px;

          label {
            font-size: 13px;
            color: var(--el-text-color-regular);
            font-weight: 500;
          }
        }
      }
    }
  }

  // 提示词预览区域
  .prompt-section {
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);

    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 16px;

      .title-actions {
        display: flex;
        gap: 8px;
      }
    }

    .prompt-item {
      margin-bottom: 16px;

      .prompt-label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);

        .el-icon {
          font-size: 16px;
        }
      }

      .prompt-textarea {
        &.system-prompt {
          :deep(.el-textarea__inner) {
            background: var(--el-fill-color-extra-light);
            border-color: var(--el-border-color-light);
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 12px;
            line-height: 1.4;
            color: var(--el-text-color-regular);
          }
        }

        &.user-prompt {
          &.editable {
            :deep(.el-textarea__inner) {
              background: var(--el-color-warning-light-9);
              border-color: var(--el-color-warning-light-6);
              border-width: 2px;
            }
          }

          :deep(.el-textarea__inner) {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 12px;
            line-height: 1.4;
          }
        }
      }
    }

    .prompt-stats {
      display: flex;
      gap: 8px;
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }

  // 自定义提示词区域
  .custom-prompt {
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);

    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 12px;
    }
  }

  // 生成状态
  .generation-status {
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);

    .status-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-size: 14px;
      color: var(--el-text-color-primary);

      .rotating {
        animation: rotate 1s linear infinite;
        color: var(--el-color-primary);
      }

      .status-text {
        flex: 1;
        font-weight: 500;
      }

      .status-time {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }

    .live-preview {
      margin-top: 12px;
      padding: 12px;
      background: var(--el-fill-color-blank);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 6px;

      .preview-label {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-bottom: 6px;
      }

      .preview-text {
        font-size: 13px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
      }
    }
  }

  // 结果预览
  .result-preview {
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);

    .result-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .success-icon {
        color: var(--el-color-success);
      }

      span {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }

    .result-summary {
      .summary-text {
        font-size: 13px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  // 底部操作区域
  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);

    .footer-left {
      display: flex;
      align-items: center;
    }

    .footer-right {
      display: flex;
      gap: 12px;
    }
  }

  .advanced-settings {
    .el-form {
      .el-form-item {
        margin-bottom: 20px;
      }

      .el-select {
        .el-option-group__title {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }

      .form-help-text {
        margin-top: 8px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        line-height: 1.4;
      }

      .mode-help {
        margin-top: 12px;

        .el-alert {
          --el-alert-padding: 8px 12px;
          --el-alert-border-radius: 6px;

          .el-alert__content {
            font-size: 12px;
            line-height: 1.4;
          }
        }
      }
    }

    .advanced-actions {
      text-align: center;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }

  .ai-generating {
    margin-top: 20px;
    padding: 20px;
    background: var(--el-fill-color-light);
    border-radius: 8px;

    .generating-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .generating-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);

        .generation-time {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          background: var(--el-fill-color);
          padding: 2px 6px;
          border-radius: 4px;
          font-family: monospace;
        }

        .rotating {
          animation: rotate 2s linear infinite;
        }
      }
    }

    .content-preview {
      margin-top: 16px;

      .preview-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .preview-content {
        padding: 12px;
        background: var(--el-fill-color-blank);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 6px;
        max-height: 200px;
        overflow-y: auto;
        white-space: pre-wrap;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        font-size: 13px;
        line-height: 1.5;
        color: var(--el-text-color-primary);
      }
    }
  }

  .generation-result {
    margin-top: 20px;
    padding: 20px;
    background: var(--el-color-success-light-9);
    border: 1px solid var(--el-color-success-light-7);
    border-radius: 8px;

    .result-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 500;
      color: var(--el-color-success);
    }

    .result-content {
      .preview-tabs {
        margin-bottom: 12px;

        .el-radio-group {
          width: 100%;
        }
      }

      .formatted-preview {
        padding: 12px;
        background: var(--el-fill-color-blank);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 6px;
        max-height: 300px;
        overflow-y: auto;
        line-height: 1.6;
        color: var(--el-text-color-primary);

        h1, h2, h3 {
          margin: 16px 0 8px 0;
          color: var(--el-color-primary);
        }

        p {
          margin: 8px 0;
        }

        ul {
          margin: 8px 0;
          padding-left: 20px;
        }

        li {
          margin: 4px 0;
        }
      }

      .raw-preview {
        padding: 12px;
        background: var(--el-fill-color-blank);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 6px;
        max-height: 300px;
        overflow-y: auto;

        pre {
          margin: 0;
          white-space: pre-wrap;
          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
          font-size: 13px;
          line-height: 1.5;
          color: var(--el-text-color-primary);
        }
      }

      .structure-preview {
        padding: 12px;
        background: var(--el-fill-color-blank);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 6px;
        max-height: 300px;
        overflow-y: auto;

        .section-item {
          margin-bottom: 16px;
          padding: 12px;
          background: var(--el-fill-color-light);
          border-radius: 6px;

          .section-title {
            font-weight: 500;
            color: var(--el-color-primary);
            margin-bottom: 8px;
          }

          .section-content {
            color: var(--el-text-color-regular);
            margin-bottom: 8px;
            line-height: 1.5;
          }

          .section-meta {
            display: flex;
            gap: 12px;

            .meta-item {
              font-size: 12px;
              color: var(--el-text-color-secondary);
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 深色主题适配
.dark {
  .ai-dialog.native-style {
    :deep(.el-dialog__header) {
      background: var(--el-fill-color-dark);
      border-bottom-color: var(--el-border-color-dark);
    }

    .dialog-header {
      background: var(--el-fill-color-dark);
      border-bottom-color: var(--el-border-color-dark);
    }

    .prompt-section,
    .custom-prompt,
    .generation-status,
    .result-preview,
    .dialog-footer {
      background: var(--el-fill-color-dark);
      border-top-color: var(--el-border-color-dark);
    }

    .live-preview {
      background: var(--el-fill-color-darker);
      border-color: var(--el-border-color-dark);
    }
  }
}
</style>
