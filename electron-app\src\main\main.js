const { app, BrowserWindow, ipcMain, dialog, shell, Menu } = require('electron');
const { autoUpdater } = require('electron-updater');
const Store = require('electron-store');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');

// 导入服务层
const ApiService = require('../services/ApiService');

// 全局变量
let mainWindow;
let apiService;
const store = new Store();

// 多平台适配配置
const isDev = process.env.NODE_ENV === 'development';
const isWin = process.platform === 'win32';
const isMac = process.platform === 'darwin';
const isLinux = process.platform === 'linux';

// 应用配置
const APP_CONFIG = {
  window: {
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700
  },
  dev: {
    rendererUrl: 'http://localhost:13001',
    devTools: true
  },
  prod: {
    rendererPath: path.join(__dirname, '../../dist/index.html')
  }
};

// 获取用户数据目录（多平台适配）
function getUserDataPath() {
  const appName = 'PVV-Novel-Creator';
  
  if (isWin) {
    return path.join(os.homedir(), 'AppData', 'Roaming', appName);
  } else if (isMac) {
    return path.join(os.homedir(), 'Library', 'Application Support', appName);
  } else if (isLinux) {
    return path.join(os.homedir(), '.config', appName);
  }
  
  // 默认回退
  return path.join(os.homedir(), `.${appName.toLowerCase()}`);
}

// 创建主窗口
function createMainWindow() {
  // 窗口状态恢复
  const windowState = store.get('windowState', {
    width: APP_CONFIG.window.width,
    height: APP_CONFIG.window.height,
    x: undefined,
    y: undefined,
    isMaximized: false
  });

  mainWindow = new BrowserWindow({
    width: windowState.width,
    height: windowState.height,
    x: windowState.x,
    y: windowState.y,
    minWidth: APP_CONFIG.window.minWidth,
    minHeight: APP_CONFIG.window.minHeight,
    show: false,
    icon: getAppIcon(),
    titleBarStyle: isMac ? 'hiddenInset' : 'default',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, '../preload/preload.js'),
      webSecurity: !isDev
    }
  });

  // 窗口状态管理
  if (windowState.isMaximized) {
    mainWindow.maximize();
  }

  // 保存窗口状态
  const saveWindowState = () => {
    if (!mainWindow.isDestroyed()) {
      const bounds = mainWindow.getBounds();
      store.set('windowState', {
        ...bounds,
        isMaximized: mainWindow.isMaximized()
      });
    }
  };

  mainWindow.on('resize', saveWindowState);
  mainWindow.on('move', saveWindowState);
  mainWindow.on('maximize', saveWindowState);
  mainWindow.on('unmaximize', saveWindowState);

  // 加载渲染进程
  if (isDev) {
    mainWindow.loadURL(APP_CONFIG.dev.rendererUrl);

    if (APP_CONFIG.dev.devTools) {
      mainWindow.webContents.openDevTools();
    }
  } else {
    mainWindow.loadFile(APP_CONFIG.prod.rendererPath);
  }

  // 窗口事件处理
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 开发环境下的热重载支持
    if (isDev) {
      mainWindow.webContents.on('did-frame-finish-load', () => {
        mainWindow.webContents.once('dom-ready', () => {
          console.log('Renderer process loaded');
        });
      });
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 外部链接处理
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  return mainWindow;
}

// 获取应用图标（多平台适配）
function getAppIcon() {
  const iconPath = path.join(__dirname, '../../build');
  
  if (isWin) {
    return path.join(iconPath, 'icon.ico');
  } else if (isMac) {
    return path.join(iconPath, 'icon.icns');
  } else if (isLinux) {
    return path.join(iconPath, 'icon.png');
  }
  
  return null;
}

// 创建应用菜单（多平台适配）
function createAppMenu() {
  const template = [];

  // macOS 应用菜单
  if (isMac) {
    template.push({
      label: app.getName(),
      submenu: [
        { role: 'about', label: '关于 PVV小说创作软件' },
        { type: 'separator' },
        { role: 'services', label: '服务' },
        { type: 'separator' },
        { role: 'hide', label: '隐藏 PVV小说创作软件' },
        { role: 'hideothers', label: '隐藏其他' },
        { role: 'unhide', label: '显示全部' },
        { type: 'separator' },
        { role: 'quit', label: '退出 PVV小说创作软件' }
      ]
    });
  }

  // 文件菜单
  template.push({
    label: '文件',
    submenu: [
      {
        label: '新建项目',
        accelerator: 'CmdOrCtrl+N',
        click: () => {
          mainWindow.webContents.send('menu-action', 'new-project');
        }
      },
      {
        label: '打开项目',
        accelerator: 'CmdOrCtrl+O',
        click: () => {
          mainWindow.webContents.send('menu-action', 'open-project');
        }
      },
      {
        label: '保存',
        accelerator: 'CmdOrCtrl+S',
        click: () => {
          mainWindow.webContents.send('menu-action', 'save');
        }
      },
      { type: 'separator' },
      isMac ? { role: 'close', label: '关闭窗口' } : { role: 'quit', label: '退出' }
    ]
  });

  // 编辑菜单
  template.push({
    label: '编辑',
    submenu: [
      { role: 'undo', label: '撤销' },
      { role: 'redo', label: '重做' },
      { type: 'separator' },
      { role: 'cut', label: '剪切' },
      { role: 'copy', label: '复制' },
      { role: 'paste', label: '粘贴' },
      { role: 'selectall', label: '全选' }
    ]
  });

  // 视图菜单
  template.push({
    label: '视图',
    submenu: [
      { role: 'reload', label: '重新加载' },
      { role: 'forceReload', label: '强制重新加载' },
      { role: 'toggleDevTools', label: '开发者工具' },
      { type: 'separator' },
      { role: 'resetZoom', label: '实际大小' },
      { role: 'zoomIn', label: '放大' },
      { role: 'zoomOut', label: '缩小' },
      { type: 'separator' },
      { role: 'togglefullscreen', label: '切换全屏' }
    ]
  });

  // 窗口菜单
  template.push({
    label: '窗口',
    submenu: [
      { role: 'minimize', label: '最小化' },
      { role: 'close', label: '关闭' },
      ...(isMac ? [
        { type: 'separator' },
        { role: 'front', label: '前置全部窗口' }
      ] : [])
    ]
  });

  // 帮助菜单
  template.push({
    label: '帮助',
    submenu: [
      {
        label: '关于',
        click: () => {
          dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: '关于 PVV小说创作软件',
            message: 'PVV小说创作软件',
            detail: `版本: ${app.getVersion()}\n基于 Electron 构建的小说创作工具`
          });
        }
      }
    ]
  });

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 应用初始化
async function initializeApp() {
  try {
    // 确保用户数据目录存在
    const userDataPath = getUserDataPath();
    await fs.ensureDir(userDataPath);

    // 初始化API服务
    apiService = new ApiService(userDataPath);
    await apiService.initialize();

    console.log('应用初始化完成');
  } catch (error) {
    console.error('应用初始化失败:', error);
    dialog.showErrorBox('初始化错误', `应用初始化失败: ${error.message}`);
  }
}

// IPC 事件处理
function setupIpcHandlers() {
  // 通用API调用处理器
  ipcMain.handle('api-call', async (event, method, ...args) => {
    try {
      if (!apiService) {
        throw new Error('API服务未初始化');
      }

      if (typeof apiService[method] !== 'function') {
        throw new Error(`API方法不存在: ${method}`);
      }

      const result = await apiService[method](...args);
      return result;
    } catch (error) {
      console.error(`API调用失败 [${method}]:`, error);
      return {
        status: 'error',
        message: error.message
      };
    }
  });

  // 文件对话框
  ipcMain.handle('show-open-dialog', async (event, options) => {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result;
  });

  ipcMain.handle('show-save-dialog', async (event, options) => {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
  });

  // 应用信息
  ipcMain.handle('get-app-info', () => {
    return {
      version: app.getVersion(),
      platform: process.platform,
      arch: process.arch,
      userDataPath: getUserDataPath()
    };
  });

  // 窗口控制
  ipcMain.handle('window-minimize', () => {
    mainWindow.minimize();
  });

  ipcMain.handle('window-maximize', () => {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  });

  ipcMain.handle('window-close', () => {
    mainWindow.close();
  });

  // 外部链接
  ipcMain.handle('open-external', (event, url) => {
    shell.openExternal(url);
  });

  // 显示文件夹
  ipcMain.handle('show-item-in-folder', (event, fullPath) => {
    shell.showItemInFolder(fullPath);
  });
}

// 自动更新处理
function setupAutoUpdater() {
  if (isDev) return;

  autoUpdater.checkForUpdatesAndNotify();

  autoUpdater.on('update-available', () => {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '更新可用',
      message: '发现新版本，正在后台下载...',
      buttons: ['确定']
    });
  });

  autoUpdater.on('update-downloaded', () => {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '更新就绪',
      message: '更新已下载完成，应用将重启以应用更新。',
      buttons: ['立即重启', '稍后']
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  });
}

// 应用事件处理
app.whenReady().then(async () => {
  await initializeApp();
  createMainWindow();
  createAppMenu();
  setupIpcHandlers();
  setupAutoUpdater();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (!isMac) {
    app.quit();
  }
});

app.on('before-quit', () => {
  // 清理资源
  if (apiService) {
    apiService.cleanup();
  }
});

// 安全设置
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });

  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    if (parsedUrl.origin !== 'http://localhost:13001' && !isDev) {
      event.preventDefault();
    }
  });
});

// 处理协议（深度链接支持）
if (!app.isDefaultProtocolClient('pvv-novel')) {
  app.setAsDefaultProtocolClient('pvv-novel');
}

// 单实例应用
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
