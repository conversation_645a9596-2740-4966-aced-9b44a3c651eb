<template>
  <div class="dashboard">
    <div class="page-title">创作工作台</div>
    
    <!-- 快速统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><Folder /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.projectCount }}</div>
              <div class="stat-label">项目总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.bookCount }}</div>
              <div class="stat-label">书籍总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><EditPen /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalWords }}</div>
              <div class="stat-label">总字数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.todayWords }}</div>
              <div class="stat-label">今日字数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="content-card">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>
      
      <div class="quick-actions">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="action-item" @click="$router.push('/book/写作')">
              <el-icon size="32"><Edit /></el-icon>
              <div class="action-title">开始写作</div>
              <div class="action-desc">进入写作界面</div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="action-item" @click="showComingSoon">
              <el-icon size="32"><FolderAdd /></el-icon>
              <div class="action-title">新建项目</div>
              <div class="action-desc">创建新的写作项目</div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="action-item" @click="showComingSoon">
              <el-icon size="32"><ChatSquare /></el-icon>
              <div class="action-title">AI助手</div>
              <div class="action-desc">智能写作助手</div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="action-item" @click="testAPI">
              <el-icon size="32"><Tools /></el-icon>
              <div class="action-title">API测试</div>
              <div class="action-desc">测试后端连接</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 系统状态 -->
    <el-card class="content-card">
      <template #header>
        <div class="card-header">
          <span>系统状态</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="status-item">
            <span class="status-label">应用版本:</span>
            <span class="status-value">{{ appInfo.version || '获取中...' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">平台:</span>
            <span class="status-value">{{ appInfo.platform || '获取中...' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">API状态:</span>
            <el-tag :type="apiStatus === 'connected' ? 'success' : 'danger'">
              {{ apiStatus === 'connected' ? '已连接' : '未连接' }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="status-item">
            <span class="status-label">最后更新:</span>
            <span class="status-value">{{ lastUpdate }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">数据目录:</span>
            <span class="status-value">{{ appInfo.userDataPath || '获取中...' }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  Folder, Document, EditPen, Clock, Edit, FolderAdd, 
  ChatSquare, Tools
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const stats = ref({
  projectCount: 0,
  bookCount: 0,
  totalWords: 0,
  todayWords: 0
})

const appInfo = ref({})
const apiStatus = ref('disconnected')
const lastUpdate = ref(new Date().toLocaleString())

onMounted(() => {
  loadDashboardData()
})

async function loadDashboardData() {
  try {
    // 加载应用信息
    await loadAppInfo()
    
    // 加载统计数据
    await loadStats()
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

async function loadAppInfo() {
  try {
    const result = await window.electronAPI.invoke('get_app_info')
    if (result.status === 'success') {
      appInfo.value = result.data
      apiStatus.value = 'connected'
    }
  } catch (error) {
    console.error('获取应用信息失败:', error)
    apiStatus.value = 'disconnected'
  }
}

async function loadStats() {
  try {
    // 获取项目统计
    const projectResult = await window.pywebview.api.get_projects()
    if (projectResult.status === 'success') {
      stats.value.projectCount = projectResult.data.length
      
      // 计算书籍总数和字数
      let totalBooks = 0
      let totalWords = 0
      
      for (const project of projectResult.data) {
        const booksResult = await window.pywebview.api.get_books(project.id)
        if (booksResult.status === 'success') {
          totalBooks += booksResult.data.length
          
          for (const book of booksResult.data) {
            totalWords += book.statistics?.totalWords || 0
          }
        }
      }
      
      stats.value.bookCount = totalBooks
      stats.value.totalWords = totalWords
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

async function testAPI() {
  try {
    ElMessage.info('正在测试API...')
    
    const configResult = await window.pywebview.api.get_settings()
    
    if (configResult.status === 'success') {
      ElMessage.success('API测试成功')
      apiStatus.value = 'connected'
    } else {
      ElMessage.error('API测试失败')
      apiStatus.value = 'disconnected'
    }
  } catch (error) {
    ElMessage.error('API测试失败: ' + error.message)
    apiStatus.value = 'disconnected'
  }
}

function showComingSoon() {
  ElMessage.info('功能开发中，敬请期待...')
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 15px;
  color: var(--el-color-primary);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-top: 5px;
}

.content-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  padding: 10px 0;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.action-item:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  transform: translateY(-2px);
}

.action-item .el-icon {
  margin-bottom: 10px;
  color: var(--el-color-primary);
}

.action-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 5px;
}

.action-desc {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.status-value {
  color: var(--el-text-color-primary);
  font-size: 14px;
}
</style>
