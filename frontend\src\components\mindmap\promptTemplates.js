/**
 * 智能提示词模板系统
 * 专为思维导图AI生成设计的提示词引擎
 */

export class PromptTemplateEngine {
  constructor() {
    this.systemPrompts = new Map()
    this.userPrompts = new Map()
    this.setupPrompts()
  }

  setupPrompts() {
    // 系统提示词 - 基础角色设定
    this.systemPrompts.set('base', `你是一位专业的思维导图内容生成专家，专门为用户创建结构化的思维导图节点内容。

核心职责：
1. 根据用户指定的模式生成相应的思维导图节点
2. 确保生成的内容逻辑清晰、结构合理
3. 严格按照指定的输出格式进行回复
4. 避免生成与已有节点重复的内容

输出规范：
- 必须使用标准markdown格式
- 每个节点标题使用## 格式
- 标题下方必须提供具体的实际内容，不要使用占位符
- 不要添加任何解释性文字或额外说明
- 禁止使用"详细描述xxx"、"具体说明xxx"等占位符文本

请严格按照用户的要求执行，直接生成真实的内容。`)

    // 用户提示词模板 - 子主题模式（生成下级子主题分类）
    this.userPrompts.set('subtopics', `请为主题"{{title}}"生成下级的子主题分类。

【完整上下文】
{{contextInfo}}

【生成要求】
- 为当前主题生成下一级的子主题分类
- 每个子主题应该是当前主题的一个细分领域或方面
- 子主题之间应该相互独立，共同构成完整的主题体系
- 充分考虑上级主题的整体框架和已有同级内容
- 每个子主题使用## 标题格式，标题下方只提供简要说明
- 生成准确的子主题分类
- 避免与已有内容重复

【重要约束】
- 只生成一层子主题，不要在子主题下面再创建子层级
- 不要使用###、####等更深层级的标题
- 每个子主题的说明应该简洁明了，不要过于详细
- 不要在说明中包含列表、要点等会被解析为子层级的内容

【输出格式】
## 子主题标题
简要说明该子主题的范围和内容。

请直接输出实际的子主题分类，不要使用占位符或示例文本。`)

    // 用户提示词模板 - 要点分解模式（深入分析当前主题）
    this.userPrompts.set('children', `请深入分析主题"{{title}}"，提取其核心要点和细节。

【完整上下文】
{{contextInfo}}

【生成要求】
- 深入分析当前主题的内在结构和核心要点
- 提取主题的关键组成部分、重要细节和实施要点
- 每个要点都要有详细的描述、说明和具体内容
- 充分考虑整体框架和上下文层级关系
- 确保要点之间逻辑清晰、相互补充
- 内容要有深度和实用性，能够细化当前主题
- 避免与已有内容重复

【输出格式】
每个要点使用## 标题格式，标题下方直接提供具体的详细分析和说明。

请直接输出实际的要点分析内容，不要使用占位符或示例文本。`)

    // 用户提示词模板 - 多角度分析模式
    this.userPrompts.set('analysis', `请从多个角度深入分析主题"{{title}}"。

【完整上下文】
{{contextInfo}}

【生成要求】
- 从3-5个不同维度分析主题
- 每个分析角度都要有独特的见解
- 提供深入的分析和观点
- 充分考虑整体框架和上下文关系
- 确保分析全面而专业
- 避免与已有内容重复

【输出格式】
每个分析角度使用## 标题格式，标题下方直接提供具体的分析内容。

请直接输出实际的分析内容，不要使用占位符或示例文本。`)

    // 用户提示词模板 - 创意发散模式
    this.userPrompts.set('creative', `请为主题"{{title}}"进行创意发散思考。

【完整上下文】
{{contextInfo}}

【生成要求】
- 生成准确的创新性的思路或观点
- 鼓励跳出常规思维，但要符合整体框架
- 每个创意点都要有具体的说明
- 确保创意的可行性和价值
- 避免与已有内容重复

【输出格式】
每个创意点使用## 标题格式，标题下方直接提供具体的创意内容和价值说明。

请直接输出实际的创意内容，不要使用占位符或示例文本。`)
  }

  /**
   * 生成系统提示词
   */
  generateSystemPrompt(domain = 'general') {
    let systemPrompt = this.systemPrompts.get('base')

    // 根据领域添加专业化内容
    const domainEnhancements = {
      novel: '\n\n专业领域：小说创作\n特别注意情节逻辑、人物塑造和世界观构建的一致性。',
      worldbuilding: '\n\n专业领域：世界观构建\n重点关注世界观的一致性、逻辑性和丰富性。',
      character: '\n\n专业领域：角色设定\n专注于角色的立体性、成长弧线和人物关系。',
      technical: '\n\n专业领域：技术内容\n确保技术内容的准确性、实用性和可操作性。',
      business: '\n\n专业领域：商业分析\n关注商业逻辑、市场分析和实施可行性。',
      general: ''
    }

    return systemPrompt + (domainEnhancements[domain] || domainEnhancements.general)
  }

  /**
   * 生成用户提示词
   */
  generateUserPrompt(mode, context) {
    const template = this.userPrompts.get(mode)
    if (!template) {
      throw new Error(`未找到模式 ${mode} 的提示词模板`)
    }

    // 构建完整的上下文信息字符串
    const contextInfo = this.buildContextInfo(context)

    // 替换模板变量
    return this.replaceVariables(template, {
      title: context.title || '未命名主题',
      contextInfo: contextInfo
    })
  }

  /**
   * 构建上下文信息字符串
   */
  buildContextInfo(context) {
    console.log('=== buildContextInfo 调试信息 ===')
    console.log('输入的context:', JSON.stringify(context, null, 2))

    const parts = []

    // 基础信息
    parts.push(`当前主题：${context.title || '未命名主题'}`)
    parts.push(`层级深度：第${context.depth || 0}层`)

    // 层级路径信息
    if (context.hierarchy && context.hierarchy.fullPath) {
      parts.push(`完整路径：${context.hierarchy.fullPath}`)
      console.log('✅ 添加了完整路径:', context.hierarchy.fullPath)
    } else {
      console.log('❌ 缺少完整路径信息')
    }

    // 上级主题信息
    if (context.hierarchy && context.hierarchy.ancestors && context.hierarchy.ancestors.length > 0) {
      const ancestorTitles = context.hierarchy.ancestors.map(a => a.title).join(' → ')
      parts.push(`上级主题：${ancestorTitles}`)
      console.log('✅ 添加了上级主题:', ancestorTitles)
    } else {
      console.log('❌ 缺少上级主题信息')
    }

    // 同级主题信息
    if (context.relationships && context.relationships.siblings && context.relationships.siblings.length > 0) {
      const siblingTitles = context.relationships.siblings.map(s => s.title).join('、')
      const moreText = context.relationships.hasMoreSiblings ? '等' : ''
      parts.push(`同级主题：${siblingTitles}${moreText}`)
    } else {
      parts.push(`同级主题：无`)
    }

    // 已有子主题信息
    if (context.relationships && context.relationships.children && context.relationships.children.length > 0) {
      const childrenTitles = context.relationships.children.map(c => c.title).join('、')
      const moreText = context.relationships.hasMoreChildren ? '等' : ''
      parts.push(`已有子主题：${childrenTitles}${moreText}`)
    } else {
      parts.push(`已有子主题：无`)
    }

    // 当前内容信息
    if (context.content && context.content.hasContent) {
      parts.push(`当前内容：${context.content.currentContent.substring(0, 100)}${context.content.currentContent.length > 100 ? '...' : ''}`)
    } else {
      parts.push(`当前内容：无`)
    }

    const finalContextInfo = parts.join('\n')
    console.log('=== 最终构建的上下文信息 ===')
    console.log(finalContextInfo)
    console.log('=== 上下文信息构建完成 ===')

    return finalContextInfo
  }

  /**
   * 替换模板变量
   */
  replaceVariables(template, variables) {
    let result = template

    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g')
      result = result.replace(regex, String(value || ''))
    }

    return result
  }

  /**
   * 验证生成模式
   */
  validateMode(mode) {
    const validModes = ['subtopics', 'children', 'analysis', 'creative']
    return validModes.includes(mode)
  }

  /**
   * 获取所有可用的生成模式
   */
  getAvailableModes() {
    return [
      { id: 'subtopics', name: '子主题', description: '生成下级子主题分类' },
      { id: 'children', name: '要点分解', description: '深入分析当前主题的核心要点和细节' },
      { id: 'analysis', name: '多角度分析', description: '从不同维度深入分析' },
      { id: 'creative', name: '创意发散', description: '生成创新性思路和观点' }
    ]
  }
}

// 创建全局实例
export const promptEngine = new PromptTemplateEngine()

/**
 * 快速生成提示词的便捷函数
 * @param {string} mode - 生成模式 (subtopics, children, analysis, creative)
 * @param {Object} context - 上下文信息
 * @param {string} context.title - 当前节点标题
 * @param {number} context.depth - 节点深度
 * @param {Array} context.siblings - 同级节点列表
 * @param {string} context.domain - 专业领域
 * @returns {Object} 包含系统提示词和用户提示词的对象
 */
export function generatePrompts(mode, context) {
  // 验证模式
  if (!promptEngine.validateMode(mode)) {
    throw new Error(`不支持的生成模式: ${mode}`)
  }

  // 生成提示词
  const systemPrompt = promptEngine.generateSystemPrompt(context.domain)
  const userPrompt = promptEngine.generateUserPrompt(mode, context)

  return {
    system: systemPrompt,
    user: userPrompt
  }
}

/**
 * 获取模式信息的便捷函数
 */
export function getModeInfo(mode) {
  const modes = promptEngine.getAvailableModes()
  return modes.find(m => m.id === mode) || null
}

/**
 * 获取所有可用模式
 */
export function getAllModes() {
  return promptEngine.getAvailableModes()
}
