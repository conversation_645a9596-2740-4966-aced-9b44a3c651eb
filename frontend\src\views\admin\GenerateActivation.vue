<template>
  <div class="generate-activation">
    <h2>激活码生成工具</h2>
    
    <el-form :model="form" label-width="120px">
      <el-form-item label="机器码">
        <el-input v-model="form.machine_code" placeholder="请输入客户的机器码"></el-input>
      </el-form-item>
      
      <el-form-item label="有效期（天）">
        <el-input-number v-model="form.valid_days" :min="1" :max="3650"></el-input-number>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="generateCode" :loading="loading">生成激活码</el-button>
      </el-form-item>
    </el-form>
    
    <div v-if="result" class="result-box">
      <h3>生成结果</h3>
      <p><strong>激活码:</strong></p>
      <el-input v-model="result.activation_code" readonly>
        <template #append>
          <el-button @click="copyCode">复制</el-button>
        </template>
      </el-input>
      
      <p><strong>到期时间:</strong> {{ result.expires_at_formatted }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const form = ref({
  machine_code: '',
  valid_days: 365
})

const loading = ref(false)
const result = ref(null)

const generateCode = async () => {
  if (!form.value.machine_code) {
    ElMessage.warning('请输入机器码')
    return
  }
  
  loading.value = true
  try {
    const response = await window.pywebview.api.generate_activation_code({
      machine_code: form.value.machine_code,
      valid_days: form.value.valid_days
    })
    
    if (response.status === 'success') {
      result.value = response.data
      ElMessage.success('激活码生成成功')
    } else {
      ElMessage.error(`生成失败: ${response.message}`)
    }
  } catch (error) {
    ElMessage.error(`生成出错: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

const copyCode = () => {
  if (!result.value) return
  
  window.pywebview.api.copy_to_clipboard(result.value.activation_code)
    .then(() => {
      ElMessage.success('激活码已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败，请手动复制')
    })
}
</script>

<style scoped>
.generate-activation {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.result-box {
  margin-top: 30px;
  padding: 15px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background: var(--el-fill-color-light);
}
</style> 