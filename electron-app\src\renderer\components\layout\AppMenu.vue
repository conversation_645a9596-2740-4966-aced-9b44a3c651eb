<template>
  <div class="app-menu" :class="{ 'is-collapsed': isCollapsed }">
    <!-- 菜单头部 -->
    <div class="menu-header">
      <div class="logo">
        <el-icon size="24"><Notebook /></el-icon>
        <span v-show="!isCollapsed" class="logo-text">PVV创作</span>
      </div>
      <el-button 
        text 
        @click="toggleCollapse"
        class="collapse-btn"
      >
        <el-icon><Fold v-if="!isCollapsed" /><Expand v-else /></el-icon>
      </el-button>
    </div>

    <!-- 菜单内容 -->
    <el-scrollbar class="menu-scrollbar">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapsed"
        :unique-opened="true"
        @select="handleMenuSelect"
        class="app-menu-list"
      >
        <template v-for="menu in menuList" :key="menu.path">
          <!-- 有子菜单的项 -->
          <el-sub-menu v-if="menu.children && menu.children.length > 0" :index="menu.path">
            <template #title>
              <el-icon>
                <component :is="getIconComponent(menu.icon)" />
              </el-icon>
              <span>{{ menu.title }}</span>
            </template>
            
            <el-menu-item
              v-for="child in menu.children.filter(c => !c.hidden)"
              :key="child.path"
              :index="`${menu.path}/${child.path}`"
            >
              <el-icon v-if="child.icon">
                <component :is="getIconComponent(child.icon)" />
              </el-icon>
              <span>{{ child.title }}</span>
            </el-menu-item>
          </el-sub-menu>
          
          <!-- 没有子菜单的项 -->
          <el-menu-item v-else :index="menu.path">
            <el-icon>
              <component :is="getIconComponent(menu.icon)" />
            </el-icon>
            <span>{{ menu.title }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </el-scrollbar>

    <!-- 菜单底部 -->
    <div class="menu-footer">
      <el-button 
        text 
        @click="$router.push('/settings')"
        class="footer-btn"
      >
        <el-icon><Setting /></el-icon>
        <span v-show="!isCollapsed">设置</span>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { menuList } from '@/config/menuConfig.js'
import {
  Notebook, Fold, Expand, Setting,
  House, ChatSquare, Box, Platform, Download
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 菜单折叠状态
const isCollapsed = ref(false)

// 图标组件映射
const iconComponents = {
  House,
  Notebook,
  ChatSquare,
  Box,
  Platform,
  Download,
  Setting
}

// 当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path

  // 移除开头的斜杠进行匹配
  const relativePath = path.startsWith('/') ? path.substring(1) : path

  // 精确匹配
  if (menuList.some(menu => menu.path === relativePath)) {
    return relativePath
  }

  // 查找子菜单匹配
  for (const menu of menuList) {
    if (menu.children) {
      for (const child of menu.children) {
        const childFullPath = `${menu.path}/${child.path}`
        if (childFullPath === relativePath) {
          return childFullPath
        }
      }
    }
  }

  // 默认匹配第一级路径
  const firstLevelPath = relativePath.split('/')[0]
  return firstLevelPath
})

// 获取图标组件
function getIconComponent(iconName) {
  return iconComponents[iconName] || House
}

// 切换菜单折叠状态
function toggleCollapse() {
  isCollapsed.value = !isCollapsed.value
}

// 处理菜单选择
function handleMenuSelect(index) {
  // 确保路径以斜杠开头
  const fullPath = index.startsWith('/') ? index : '/' + index
  router.push(fullPath)
}
</script>

<style scoped>
.app-menu {
  width: 240px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.app-menu.is-collapsed {
  width: 64px;
}

.menu-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-color-primary);
  font-weight: 600;
  font-size: 16px;
}

.logo-text {
  transition: opacity 0.3s ease;
}

.collapse-btn {
  padding: 8px;
}

.menu-scrollbar {
  flex: 1;
}

.app-menu-list {
  border: none;
  background: transparent;
}

.app-menu-list:not(.el-menu--collapse) {
  width: 240px;
}

.menu-footer {
  padding: 16px;
  border-top: 1px solid var(--el-border-color);
}

.footer-btn {
  width: 100%;
  justify-content: flex-start;
  padding: 8px 12px;
}

.footer-btn .el-icon {
  margin-right: 8px;
}

/* 菜单项样式调整 */
:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-sub-menu .el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-menu-item.is-active) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

:deep(.el-menu-item:hover) {
  background-color: var(--el-color-primary-light-9);
}

/* 折叠状态下的样式 */
.app-menu.is-collapsed :deep(.el-menu--collapse) {
  width: 64px;
}

.app-menu.is-collapsed :deep(.el-menu-item),
.app-menu.is-collapsed :deep(.el-sub-menu__title) {
  padding: 0 20px;
  text-align: center;
}

.app-menu.is-collapsed .footer-btn {
  justify-content: center;
}

.app-menu.is-collapsed .footer-btn .el-icon {
  margin-right: 0;
}
</style>
