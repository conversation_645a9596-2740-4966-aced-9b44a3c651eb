import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import path from 'path'
import vueScriptMerger from './plugins/vue-script-merger'
import { execSync } from 'child_process'

// https://vite.dev/config/
export default defineConfig({
  base: './',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },

  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
    vueScriptMerger({
      // 从多个位置查找脚本文件
      scriptPaths: [
        'scripts',        // 相对于src目录
      ],
      
      // 支持多种扩展名
      extensions: ['.script.js', '.vue.js', '.js'],
      
      // 路径别名(除了vite配置的别名外，可以添加额外的)
      aliases: {
        '@scripts': path.resolve(__dirname, 'src/scripts')
      },
      
      // 调试模式
      debug: true,
      
      // 自定义注入注释
      injectComment: '// 从 {filename} 自动导入',
      
      // 是否优先使用组件同目录下的脚本文件
      useSameDir: true
    }),
    {
      name: 'integrity-manifest',
      closeBundle() {
        // 在构建完成后生成完整性清单并更新代码
        console.log('正在生成文件完整性代码...');
        try {
          // 下面是用来生成网页hash保证不能被修改，单纯根据文件字符
          execSync('python build_integrity_code.py --dir ./statics --output ./main.py', {
            stdio: 'inherit',
            cwd: path.resolve(__dirname, '../') // 回到项目根目录
          });
        } catch (error) {
          console.error('生成完整性代码失败:', error);
        }
      }
    }
  ],
  server: {
    port: 13000, // 指定启动端口为3000
    fs: {
      strict: false, // 允许访问项目根目录之外的文件
    }
  },

  build: {
    outDir: '../statics', // 输出目录
    // 使用更温和的混淆设置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false,  // 保留console以便调试
        drop_debugger: true,
        pure_funcs: [] // 不移除任何函数调用
      },
      mangle: {
        toplevel: false,     // 不混淆顶级作用域变量
        safari10: true,
        keep_classnames: true, // 保留类名
        keep_fnames: true,     // 保留函数名
        properties: false      // 不混淆属性名
      },
      format: {
        comments: 'some'     // 保留一些注释
      }
    },
    rollupOptions: {
      input: {
        main: './index.html', // 指向入口文件
      },
      output: {
        // 保持文件名结构但添加内容哈希
        entryFileNames: 'assets/entry-[hash].js',
        chunkFileNames: ({name}) => {
          // 将中文名称转换为拼音或使用模块ID
          const safeChunkName = name
            ? name.replace(/[^\x00-\x7F]/g, '') // 移除非ASCII字符
              .replace(/[^a-zA-Z0-9_-]/g, '-')  // 其他非字母数字替换为连字符
              .replace(/^-+|-+$/g, '')          // 移除开头和结尾的连字符
              .replace(/-{2,}/g, '-')           // 连续的连字符替换为单个
              || 'chunk'                         // 如果为空则使用默认名
            : 'chunk';
          
          return `assets/${safeChunkName}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          // 安全的资源文件名处理
          let fileName = assetInfo.name || '';
          const safeName = fileName
            .replace(/[^\x00-\x7F]/g, '')
            .replace(/[^a-zA-Z0-9_.-]/g, '-')
            .replace(/^-+|-+$/g, '')
            .replace(/-{2,}/g, '-')
            || 'asset';
          
          const extType = fileName.split('.').pop();
          
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
            return `assets/images/${safeName}-[hash][extname]`;
          }
          if (/woff|woff2|eot|ttf|otf/i.test(extType)) {
            return `assets/fonts/${safeName}-[hash][extname]`;
          }
          if (/css/i.test(extType)) {
            return `assets/css/${safeName}-[hash][extname]`;
          }
          return `assets/${safeName}-[hash][extname]`;
        },
        // 更简单的代码分割
        manualChunks: {
          'vendor': ['vue', 'vue-router', 'pinia'],
          'element-plus': ['element-plus'],
        }
      }
    },
    chunkSizeWarningLimit: 8000, // 提高警告阈值到8MB
  },
  publicPath: './',
  assetsDir: 'static',
})
// 打印具体路径以进行调试
console.log('src目录的绝对路径是：', path.resolve(__dirname, 'src'));