<template>
  <div class="project-manager">
    <el-card class="project-list">
      <template #header>
        <div class="card-header">
          <span>项目列表</span>
          <el-button type="primary" @click="showAddDialog">添加项目</el-button>
        </div>
      </template>

      <el-table :data="projectList" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="项目名称" />
        <el-table-column prop="path" label="项目路径" show-overflow-tooltip />
        <el-table-column prop="type" label="类型">
          <template #default="{ row }">
            <el-tag :type="getProjectTypeTagType(row.type)">
              {{ getProjectTypeDisplay(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 'running' ? 'success' : 'info'">
              {{ row.status === 'running' ? '运行中' : '已停止' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_run_time" label="最后运行时间" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.last_run_time || '从未运行' }}
          </template>
        </el-table-column>
        <el-table-column prop="run_count" label="运行次数" width="100" />
        <el-table-column label="操作" width="330">
          <template #default="{ row }">
            <el-button
              :type="row.status === 'running' ? 'danger' : 'success'"
              size="small"
              @click="toggleProject(row)"
              :loading="row.loading"
            >
              {{ row.status === 'running' ? '停止' : '启动' }}
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(row)"
              :disabled="row.status === 'running'"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="removeProject(row)"
              :disabled="row.status === 'running'"
            >
              删除
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="viewProjectOutput(row)"
              :disabled="row.status !== 'running'"
            >
              输出
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑项目对话框 -->
    <el-dialog
      v-model="projectDialog.visible"
      :title="projectDialog.isEdit ? '编辑项目' : '添加项目'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="projectFormRef"
        :model="projectForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称" />
        </el-form-item>
        
        <el-form-item label="项目类型" prop="type">
          <el-radio-group v-model="projectForm.type">
            <el-radio label="exe" v-if="isWindowsOS">可执行文件(EXE)</el-radio>
            <el-radio label="py">Python项目</el-radio>
            <el-radio label="sh" v-if="!isWindowsOS">Shell脚本</el-radio>
            <el-radio label="app" v-if="isMacOS">macOS应用</el-radio>
            <el-radio label="bin">二进制可执行文件</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="项目路径" prop="path">
          <div class="path-input">
            <el-input
              v-model="projectForm.path"
              placeholder="请选择项目文件或目录"
              readonly
            />
            <el-button @click="selectPath">选择</el-button>
          </div>
        </el-form-item>

        <el-form-item label="启动参数">
          <el-input
            v-model="projectForm.start_params"
            placeholder="可选，如: --port 3000 --config config.json"
          />
        </el-form-item>

        <el-form-item label="检查方式" prop="check_method">
          <el-select v-model="projectForm.check_method">
            <el-option label="进程检查" value="process" />
            <el-option label="端口检查" value="port" />
          </el-select>
        </el-form-item>

        <el-form-item
          label="检查参数"
          prop="check_param"
          v-if="projectForm.check_method === 'port'"
        >
          <el-input-number
            v-model="projectForm.check_param"
            :min="1"
            :max="65535"
            placeholder="请输入端口号"
          />
        </el-form-item>

        <el-form-item label="启动命令">
          <el-input
            v-model="previewStartCommand"
            type="textarea"
            :rows="2"
            readonly
            placeholder="根据配置自动生成的启动命令"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="projectDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitProject" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 项目输出对话框 -->
    <div v-for="[projectName, dialog] in Array.from(outputDialogs)" :key="projectName">
      <el-dialog
        v-model="dialog.visible"
        :title="`${projectName} 输出`"
        width="80%"
        destroy-on-close
        @closed="closeOutputDialog(projectName)"
      >
        <div class="dialog-header">
          <el-button type="primary" size="small" @click="clearProjectOutput(projectName)">
            清除输出
          </el-button>
        </div>
        <div class="project-output">
          <pre>{{ dialog.content }}</pre>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 系统平台检测
const platformInfo = ref(null)
const isWindowsOS = computed(() => platformInfo.value?.system === 'Windows')
const isMacOS = computed(() => platformInfo.value?.system === 'Darwin')
const isLinuxOS = computed(() => platformInfo.value?.system !== 'Windows' && platformInfo.value?.system !== 'Darwin')

// API 封装
const api = {
  project: window.pywebview.api.inject_project,
  system: window.pywebview.api
}

// 项目列表
const projectList = ref([])
const loading = ref(false)

// 表单相关
const projectFormRef = ref(null)
const projectDialog = ref({
  visible: false,
  isEdit: false
})

const projectForm = ref({
  name: '',
  type: 'py', // 默认为Python项目，跨平台兼容性更好
  path: '',
  start_params: '',
  check_method: 'process',
  check_param: null
})

// 计算预览的启动命令
const previewStartCommand = computed(() => {
  const { path, type, start_params } = projectForm.value
  if (!path) return ''
  
  let command = ''
  
  // 根据项目类型和平台生成预览命令
  if (type === 'exe') {
    command = `"${path}"`
  } else if (type === 'py') {
    // 使用合适的Python解释器
    const pythonCmd = isWindowsOS.value ? 'python' : 'python3'
    command = `${pythonCmd} "${path}"`
  } else if (type === 'sh') {
    command = `bash "${path}"`
  } else if (type === 'app') {
    command = `open "${path}"`
  } else if (type === 'bin') {
    // 二进制可执行文件，平台特定处理
    if (!isWindowsOS.value) {
      const dirName = path.substring(0, path.lastIndexOf('/') + 1)
      const baseName = path.substring(path.lastIndexOf('/') + 1)
      if (dirName) {
        command = `cd "${dirName}" && ./${baseName}`
      } else {
        command = `./${path}`
      }
    } else {
      command = `"${path}"`
    }
  }
  
  // 添加启动参数
  if (start_params) {
    command += ` ${start_params}`
  }
  
  return command
})

const formRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
  path: [{ required: true, message: '请选择项目路径', trigger: 'change' }],
  check_method: [{ required: true, message: '请选择检查方式', trigger: 'change' }],
  check_param: [
    {
      required: true,
      message: '请输入端口号',
      trigger: 'change',
      type: 'number'
    }
  ]
}

const submitting = ref(false)

// 获取系统平台信息
const getPlatformInfo = async () => {
  try {
    const result = await api.system.get_platform_info()
    const parsed = typeof result === 'string' ? JSON.parse(result) : result
    if (parsed && parsed.status === 'success') {
      platformInfo.value = parsed.data
      
      // 根据平台调整默认项目类型
      if (isWindowsOS.value && projectForm.value.type === '') {
        projectForm.value.type = 'exe'
      } else if (isMacOS.value && projectForm.value.type === '') {
        projectForm.value.type = 'py'
      } else if (isLinuxOS.value && projectForm.value.type === '') {
        projectForm.value.type = 'py'
      }
    }
  } catch (error) {
    console.error('获取平台信息失败:', error)
    // 默认假设为通用平台
    platformInfo.value = { system: 'Unknown', release: '' }
  }
}

// 加载项目列表
const loadProjects = async () => {
  loading.value = true
  try {
    const result = await api.project.get_project_list()
    const parsedResult = typeof result === 'string' ? JSON.parse(result) : result
    
    if (parsedResult.status === 'success') {
      projectList.value = Object.entries(parsedResult.data).map(([name, project]) => ({
        name,
        ...project,
        loading: false
      }))
    } else {
      ElMessage.error(parsedResult.message || '加载失败')
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
  } finally {
    loading.value = false
  }
}

// 选择路径
const selectPath = async () => {
  try {
    const result = await api.system.select_file_path()
    const response = typeof result === 'string' ? JSON.parse(result) : result
    if (response) {
      if(response.status === 'success'){
        projectForm.value.path = response.data[0]
      }

    }
  } catch (error) {
    console.error('选择路径失败:', error)
    ElMessage.error('选择路径失败')
  }
}

// 显示添加对话框
const showAddDialog = () => {
  projectDialog.value.isEdit = false
  projectDialog.value.visible = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  projectDialog.value.isEdit = true
  Object.assign(projectForm.value, {
    name: row.name,
    type: row.type,
    path: row.path,
    start_params: row.start_params || '',
    check_method: row.check_method,
    check_param: row.check_param
  })
  projectDialog.value.visible = true
}

// 重置表单
const resetForm = () => {
  if (projectFormRef.value) {
    projectFormRef.value.resetFields()
  }
  projectForm.value = {
    name: '',
    type: isWindowsOS.value ? 'exe' : 'py',
    path: '',
    start_params: '',
    check_method: 'process',
    check_param: null
  }
}

// 提交项目表单
const submitProject = async () => {
  if (!projectFormRef.value) return
  
  await projectFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitting.value = true
    try {
      const { name, ...params } = projectForm.value
      
      const projectData = {
        ...params,
        check_param: params.check_method === 'port' ? Number(params.check_param) : null
      }

      const result = projectDialog.value.isEdit
        ? await api.project.update_project(name, projectData)
        : await api.project.add_project({
          project_name:name,
          project_type:projectData.type,
          project_path:projectData.path,
          start_params:projectData.start_params,
          check_method:projectData.check_method,
          check_param:projectData.check_param
        })
          

      const parsedResult = typeof result === 'string' ? JSON.parse(result) : result

      if (parsedResult.status === 'success') {
        ElMessage.success(projectDialog.value.isEdit ? '更新成功' : '添加成功')
        projectDialog.value.visible = false
        await loadProjects()
      } else {
        ElMessage.error(parsedResult.message || '操作失败')
      }
    } catch (error) {
      console.error('提交项目失败:', error)
      ElMessage.error('提交失败')
    } finally {
      submitting.value = false
    }
  })
}

// 删除项目
const removeProject = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目 "${row.name}" 吗？`,
      '警告',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    const result = await api.project.remove_project(row.name)
    if (result.status === 'success') {
      ElMessage.success('删除成功')
      await loadProjects()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除项目失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 启动/停止项目
const toggleProject = async (row) => {
  row.loading = true
  try {
    const action = row.status === 'running' ? 'stop_project' : 'start_project'
    const result = await api.project[action](row.name)
    const parsedResult = typeof result === 'string' ? JSON.parse(result) : result

    if (parsedResult.status === 'success') {
      ElMessage.success(row.status === 'running' ? '已停止' : '已启动')
      await loadProjects()
    } else {
      ElMessage.error(parsedResult.message || '操作失败')
    }
  } catch (error) {
    console.error('操作项目失败:', error)
    ElMessage.error('操作失败')
  } finally {
    row.loading = false
  }
}

// 项目输出日志相关
const outputDialogs = ref(new Map())  // 使用Map存储多个项目的输出对话框状态

// 处理接收到的项目输出
const receiveProjectOutput = (projectName, chunk) => {
  try {
    const decodedBytes = atob(chunk);
    const decodedChunk = new TextDecoder('utf-8').decode(
      new Uint8Array([...decodedBytes].map(char => char.charCodeAt(0)))
    );
    
    if (!outputDialogs.value.has(projectName)) {
      outputDialogs.value.set(projectName, {
        visible: false,
        content: ''
      });
    }
    
    const dialog = outputDialogs.value.get(projectName);
    dialog.content += decodedChunk;
  } catch (error) {
    console.error('Error processing project output:', error)
  }
}

// 查看项目输出
const viewProjectOutput = async (project) => {
  if (!outputDialogs.value.has(project.name)) {
    outputDialogs.value.set(project.name, {
      visible: false,
      content: ''
    });
  }
  
  const dialog = outputDialogs.value.get(project.name);
  dialog.visible = true;
  
  try {
    await api.project.get_project_output(project.name);
  } catch (error) {
    console.error('获取项目输出失败:', error);
    ElMessage.error('获取项目输出失败');
  }
}

// 清除项目输出
const clearProjectOutput = (projectName) => {
  if (outputDialogs.value.has(projectName)) {
    const dialog = outputDialogs.value.get(projectName);
    dialog.content = '';
  }
}

// 关闭输出对话框
const closeOutputDialog = (projectName) => {
  if (outputDialogs.value.has(projectName)) {
    const dialog = outputDialogs.value.get(projectName);
    dialog.visible = false;
  }
}

// 注册全局函数以接收输出
window.receiveProjectOutput = receiveProjectOutput;

// 自动刷新项目状态
let refreshTimer = null
const startAutoRefresh = () => {
  refreshTimer = setInterval(loadProjects, 5000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 项目类型显示和标签
const getProjectTypeDisplay = (type) => {
  const typeMap = {
    'exe': '可执行文件(EXE)',
    'py': 'Python项目',
    'sh': 'Shell脚本',
    'app': 'macOS应用',
    'bin': '二进制可执行文件'
  }
  return typeMap[type] || type
}

const getProjectTypeTagType = (type) => {
  const tagMap = {
    'exe': 'success',
    'py': 'info',
    'sh': 'warning',
    'app': 'danger',
    'bin': 'primary'
  }
  return tagMap[type] || 'info'
}

onMounted(() => {
  getPlatformInfo()
  loadProjects()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.project-manager {
  padding: 20px;
}

.project-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.path-input {
  display: flex;
  gap: 10px;
}

.path-input .el-input {
  flex: 1;
}

:deep(.el-dialog__body) {
  padding-top: 20px;
}

.project-output {
  max-height: 500px;
  overflow-y: auto;
  background: #1e1e1e;
  color: #fff;
  padding: 10px;
  border-radius: 4px;

}

.project-output pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.dialog-header {
  margin-bottom: 10px;
  text-align: right;
}
</style>
