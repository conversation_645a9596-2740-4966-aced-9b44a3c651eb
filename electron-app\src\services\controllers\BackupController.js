const fs = require('fs-extra');
const path = require('path');
const dayjs = require('dayjs');
const { nanoid } = require('../utils/idGenerator');

class BackupController {
  constructor(baseDir) {
    this.baseDir = baseDir;
    this.backupsDir = path.join(baseDir, 'backups');
    this.autoBackupTimer = null;
    this.isBackupRunning = false;
  }

  async initialize() {
    try {
      await fs.ensureDir(this.backupsDir);
      console.log('BackupController 初始化完成');
    } catch (error) {
      console.error('BackupController 初始化失败:', error);
      throw error;
    }
  }

  // ==================== 手动备份 ====================
  async create_backup(options = {}) {
    try {
      if (this.isBackupRunning) {
        return {
          status: 'error',
          message: '备份正在进行中，请稍后再试'
        };
      }

      this.isBackupRunning = true;

      const backupId = nanoid();
      const timestamp = dayjs().format('YYYY-MM-DD_HH-mm-ss');
      const backupName = options.name || `backup_${timestamp}`;
      const backupDir = path.join(this.backupsDir, `${backupName}_${backupId}`);

      await fs.ensureDir(backupDir);

      const backupInfo = {
        id: backupId,
        name: backupName,
        type: options.type || 'manual', // manual, auto, scheduled
        createdAt: new Date().toISOString(),
        description: options.description || '',
        size: 0,
        fileCount: 0,
        status: 'creating'
      };

      // 备份不同类型的数据
      const backupTasks = [];

      if (options.includeProjects !== false) {
        backupTasks.push(this.backupProjects(backupDir));
      }

      if (options.includeBooks !== false) {
        backupTasks.push(this.backupBooks(backupDir));
      }

      if (options.includeConfig !== false) {
        backupTasks.push(this.backupConfig(backupDir));
      }

      if (options.includeConversations !== false) {
        backupTasks.push(this.backupConversations(backupDir));
      }

      // 执行备份任务
      const results = await Promise.allSettled(backupTasks);
      
      // 计算备份大小和文件数量
      const stats = await this.calculateBackupStats(backupDir);
      backupInfo.size = stats.size;
      backupInfo.fileCount = stats.fileCount;
      backupInfo.status = 'completed';
      backupInfo.completedAt = new Date().toISOString();

      // 保存备份信息
      const backupInfoFile = path.join(backupDir, 'backup_info.json');
      await fs.writeJson(backupInfoFile, backupInfo, { spaces: 2 });

      // 清理旧备份
      await this.cleanupOldBackups();

      this.isBackupRunning = false;

      return {
        status: 'success',
        message: '备份创建成功',
        data: backupInfo
      };
    } catch (error) {
      this.isBackupRunning = false;
      console.error('创建备份失败:', error);
      return {
        status: 'error',
        message: '创建备份失败',
        error: error.message
      };
    }
  }

  async backupProjects(backupDir) {
    const projectsDir = path.join(this.baseDir, 'projects');
    const backupProjectsDir = path.join(backupDir, 'projects');
    
    if (await fs.pathExists(projectsDir)) {
      await fs.copy(projectsDir, backupProjectsDir);
    }
  }

  async backupBooks(backupDir) {
    const booksDir = path.join(this.baseDir, 'books');
    const backupBooksDir = path.join(backupDir, 'books');
    
    if (await fs.pathExists(booksDir)) {
      await fs.copy(booksDir, backupBooksDir);
    }
  }

  async backupConfig(backupDir) {
    const configDir = path.join(this.baseDir, 'config');
    const backupConfigDir = path.join(backupDir, 'config');
    
    if (await fs.pathExists(configDir)) {
      await fs.copy(configDir, backupConfigDir);
    }
  }

  async backupConversations(backupDir) {
    const conversationsDir = path.join(this.baseDir, 'conversations');
    const backupConversationsDir = path.join(backupDir, 'conversations');
    
    if (await fs.pathExists(conversationsDir)) {
      await fs.copy(conversationsDir, backupConversationsDir);
    }
  }

  async calculateBackupStats(backupDir) {
    let totalSize = 0;
    let fileCount = 0;

    const calculateDirStats = async (dir) => {
      const items = await fs.readdir(dir);
      
      for (const item of items) {
        const itemPath = path.join(dir, item);
        const stats = await fs.stat(itemPath);
        
        if (stats.isDirectory()) {
          await calculateDirStats(itemPath);
        } else {
          totalSize += stats.size;
          fileCount++;
        }
      }
    };

    await calculateDirStats(backupDir);
    
    return { size: totalSize, fileCount };
  }

  // ==================== 备份恢复 ====================
  async restore_backup(backupPath, options = {}) {
    try {
      if (!await fs.pathExists(backupPath)) {
        return {
          status: 'error',
          message: '备份文件不存在'
        };
      }

      // 读取备份信息
      const backupInfoFile = path.join(backupPath, 'backup_info.json');
      let backupInfo = {};
      
      if (await fs.pathExists(backupInfoFile)) {
        backupInfo = await fs.readJson(backupInfoFile);
      }

      // 创建恢复前的备份
      if (options.createBackupBeforeRestore !== false) {
        await this.create_backup({
          name: `before_restore_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}`,
          type: 'auto',
          description: '恢复前自动备份'
        });
      }

      // 恢复不同类型的数据
      const restoreTasks = [];

      if (options.restoreProjects !== false) {
        restoreTasks.push(this.restoreProjects(backupPath));
      }

      if (options.restoreBooks !== false) {
        restoreTasks.push(this.restoreBooks(backupPath));
      }

      if (options.restoreConfig !== false) {
        restoreTasks.push(this.restoreConfig(backupPath));
      }

      if (options.restoreConversations !== false) {
        restoreTasks.push(this.restoreConversations(backupPath));
      }

      // 执行恢复任务
      const results = await Promise.allSettled(restoreTasks);
      
      const failedTasks = results.filter(result => result.status === 'rejected');
      
      if (failedTasks.length > 0) {
        console.error('部分恢复任务失败:', failedTasks);
        return {
          status: 'warning',
          message: '备份恢复部分成功',
          data: {
            backupInfo,
            failedTasks: failedTasks.length
          }
        };
      }

      return {
        status: 'success',
        message: '备份恢复成功',
        data: backupInfo
      };
    } catch (error) {
      console.error('恢复备份失败:', error);
      return {
        status: 'error',
        message: '恢复备份失败',
        error: error.message
      };
    }
  }

  async restoreProjects(backupPath) {
    const backupProjectsDir = path.join(backupPath, 'projects');
    const projectsDir = path.join(this.baseDir, 'projects');
    
    if (await fs.pathExists(backupProjectsDir)) {
      await fs.remove(projectsDir);
      await fs.copy(backupProjectsDir, projectsDir);
    }
  }

  async restoreBooks(backupPath) {
    const backupBooksDir = path.join(backupPath, 'books');
    const booksDir = path.join(this.baseDir, 'books');
    
    if (await fs.pathExists(backupBooksDir)) {
      await fs.remove(booksDir);
      await fs.copy(backupBooksDir, booksDir);
    }
  }

  async restoreConfig(backupPath) {
    const backupConfigDir = path.join(backupPath, 'config');
    const configDir = path.join(this.baseDir, 'config');
    
    if (await fs.pathExists(backupConfigDir)) {
      await fs.remove(configDir);
      await fs.copy(backupConfigDir, configDir);
    }
  }

  async restoreConversations(backupPath) {
    const backupConversationsDir = path.join(backupPath, 'conversations');
    const conversationsDir = path.join(this.baseDir, 'conversations');
    
    if (await fs.pathExists(backupConversationsDir)) {
      await fs.remove(conversationsDir);
      await fs.copy(backupConversationsDir, conversationsDir);
    }
  }

  // ==================== 备份列表管理 ====================
  async get_backup_list() {
    try {
      const backupDirs = await fs.readdir(this.backupsDir);
      const backups = [];

      for (const dir of backupDirs) {
        try {
          const backupPath = path.join(this.backupsDir, dir);
          const stats = await fs.stat(backupPath);
          
          if (stats.isDirectory()) {
            const backupInfoFile = path.join(backupPath, 'backup_info.json');
            let backupInfo = {
              id: dir,
              name: dir,
              createdAt: stats.birthtime.toISOString(),
              size: 0,
              fileCount: 0,
              type: 'unknown'
            };
            
            if (await fs.pathExists(backupInfoFile)) {
              const info = await fs.readJson(backupInfoFile);
              backupInfo = { ...backupInfo, ...info };
            }
            
            backupInfo.path = backupPath;
            backups.push(backupInfo);
          }
        } catch (error) {
          console.error(`读取备份信息失败: ${dir}`, error);
        }
      }

      // 按创建时间排序
      backups.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      return {
        status: 'success',
        message: '获取备份列表成功',
        data: backups
      };
    } catch (error) {
      console.error('获取备份列表失败:', error);
      return {
        status: 'error',
        message: '获取备份列表失败',
        error: error.message
      };
    }
  }

  async delete_backup(backupId) {
    try {
      const backupDirs = await fs.readdir(this.backupsDir);
      const targetDir = backupDirs.find(dir => dir.includes(backupId));
      
      if (!targetDir) {
        return {
          status: 'error',
          message: '备份不存在'
        };
      }

      const backupPath = path.join(this.backupsDir, targetDir);
      await fs.remove(backupPath);

      return {
        status: 'success',
        message: '删除备份成功'
      };
    } catch (error) {
      console.error('删除备份失败:', error);
      return {
        status: 'error',
        message: '删除备份失败',
        error: error.message
      };
    }
  }

  // ==================== 自动备份 ====================
  async startAutoBackup(interval = 3600000) { // 默认1小时
    try {
      this.stopAutoBackup(); // 停止现有的自动备份
      
      this.autoBackupTimer = setInterval(async () => {
        try {
          await this.create_backup({
            name: `auto_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}`,
            type: 'auto',
            description: '自动备份'
          });
          console.log('自动备份完成');
        } catch (error) {
          console.error('自动备份失败:', error);
        }
      }, interval);

      return {
        status: 'success',
        message: '自动备份已启动',
        data: { interval }
      };
    } catch (error) {
      console.error('启动自动备份失败:', error);
      return {
        status: 'error',
        message: '启动自动备份失败',
        error: error.message
      };
    }
  }

  stopAutoBackup() {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
      this.autoBackupTimer = null;
    }
  }

  // ==================== 备份清理 ====================
  async cleanupOldBackups(maxBackups = 10) {
    try {
      const backupList = await this.get_backup_list();
      
      if (backupList.status === 'success' && backupList.data.length > maxBackups) {
        const backupsToDelete = backupList.data.slice(maxBackups);
        
        for (const backup of backupsToDelete) {
          await this.delete_backup(backup.id);
        }
        
        console.log(`清理了 ${backupsToDelete.length} 个旧备份`);
      }
    } catch (error) {
      console.error('清理旧备份失败:', error);
    }
  }

  // ==================== 清理资源 ====================
  cleanup() {
    this.stopAutoBackup();
  }
}

module.exports = BackupController;
