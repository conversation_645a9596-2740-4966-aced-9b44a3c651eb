<template>
  <div class="simple-vmd-editor" :class="{ 'dark': isDarkTheme }">
    <div class="editor-content" :class="`mode-${currentMode}`">
      <!-- 编辑模式 -->
      <div v-show="currentMode === 'edit'" class="edit-mode">
        <v-md-editor 
          v-model="content" 
          height="100%" 
          @change="handleContentChange"
        />
      </div>

      <!-- 预览模式 -->
      <div v-show="currentMode === 'preview'" class="preview-mode">
        <v-md-preview 
          :text="content"
          class="preview-component"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useConfigStore } from '@/stores/config'

// 基础编辑器导入
import VueMarkdownEditor from '@kangc/v-md-editor'
import VMdPreview from '@kangc/v-md-editor/lib/preview'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import '@kangc/v-md-editor/lib/style/preview.css'
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'
import Prism from 'prismjs'

// 配置主题
if (!VueMarkdownEditor._configured) {
  VueMarkdownEditor.use(vuepressTheme, {
    Prism,
  })
  VueMarkdownEditor._configured = true
}

if (!VMdPreview._configured) {
  VMdPreview.use(vuepressTheme, {
    Prism,
  })
  VMdPreview._configured = true
}

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '未命名文档'
  },
  mode: {
    type: String,
    default: 'edit',
    validator: (value) => ['edit', 'preview'].includes(value)
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save', 'export'])

// Store
const configStore = useConfigStore()

// 响应式数据
const currentMode = ref(props.mode)
const content = ref(props.modelValue)

// 计算属性
const isDarkTheme = computed(() => configStore.theme === 'dark')

// 监听变化
watch(() => props.modelValue, (newValue) => {
  content.value = newValue
})

watch(() => props.mode, (newMode) => {
  currentMode.value = newMode
})

watch(content, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
const handleContentChange = (text) => {
  content.value = text
}
</script>

<style lang="scss" scoped>
.simple-vmd-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--el-bg-color);

  &.dark {
    background: var(--el-bg-color);
  }
}

.editor-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 100%;

  &.mode-edit .edit-mode,
  &.mode-preview .preview-mode {
    height: 100%;
  }
}

.edit-mode {
  height: 100%;
  position: relative;
}

.preview-mode {
  height: 100%;
  overflow-y: auto;
  background: var(--el-bg-color);

  .preview-component {
    height: 100%;
    overflow-y: auto;
  }
}

/* 深色主题适配 */
.dark {
  :deep(.v-md-editor) {
    background-color: var(--el-bg-color) !important;
    color: var(--el-text-color-primary) !important;
    border-color: var(--el-border-color) !important;

    .v-md-editor__toolbar {
      background-color: var(--el-bg-color-page) !important;
      border-bottom-color: var(--el-border-color) !important;
    }

    .v-md-editor__main {
      background-color: var(--el-bg-color) !important;
    }

    .v-md-editor__main-left {
      background-color: var(--el-bg-color) !important;
      border-right-color: var(--el-border-color) !important;
    }

    .v-md-editor__main-right {
      background-color: var(--el-bg-color) !important;
    }

    .v-md-editor__toolbar-item {
      color: var(--el-text-color-primary) !important;

      &:hover {
        background-color: var(--el-fill-color-light) !important;
      }
    }

    textarea {
      background-color: var(--el-bg-color) !important;
      color: var(--el-text-color-primary) !important;
    }
  }

  :deep(.v-md-preview) {
    background-color: var(--el-bg-color) !important;
    color: var(--el-text-color-primary) !important;

    .v-md-preview__wrapper {
      background-color: var(--el-bg-color) !important;
    }

    h1, h2, h3, h4, h5, h6 {
      color: var(--el-text-color-primary) !important;
      border-bottom-color: var(--el-border-color-light) !important;
    }

    pre {
      background-color: var(--el-fill-color-dark) !important;
      border-color: var(--el-border-color) !important;
    }

    code {
      background-color: var(--el-fill-color-dark) !important;
      color: var(--el-color-danger-light-3) !important;
    }

    blockquote {
      background-color: var(--el-fill-color-dark) !important;
      border-left-color: var(--el-color-primary) !important;
      color: var(--el-text-color-secondary) !important;
    }

    table {
      border-color: var(--el-border-color) !important;
      background-color: var(--el-bg-color) !important;

      th {
        background-color: var(--el-fill-color-dark) !important;
        color: var(--el-text-color-primary) !important;
        border-color: var(--el-border-color) !important;
      }

      td {
        border-color: var(--el-border-color) !important;
        color: var(--el-text-color-primary) !important;
      }

      tr:nth-child(even) {
        background-color: var(--el-fill-color-darker) !important;
      }

      tr:hover {
        background-color: var(--el-fill-color-dark) !important;
      }
    }

    a {
      color: var(--el-color-primary) !important;
    }

    hr {
      border-color: var(--el-border-color) !important;
    }

    ul, ol {
      color: var(--el-text-color-primary) !important;
    }
  }
}
</style>
