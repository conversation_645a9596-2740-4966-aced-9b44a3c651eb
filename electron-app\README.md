# PVV小说创作软件 - Electron版本

## 项目概述

这是PVV小说创作软件的Electron版本，从原有的Vue3+Vite+Python pywebview架构迁移而来。新版本提供了更好的跨平台支持、更简单的部署方式和更统一的技术栈。

## 技术栈

- **前端**: Vue 3 + Vite + Element Plus
- **后端**: Node.js + Electron
- **桌面框架**: Electron
- **数据存储**: JSON文件 + 本地文件系统
- **AI集成**: OpenAI API
- **包管理**: pnpm

## 项目结构

```
electron-app/
├── src/
│   ├── main/                 # 主进程
│   │   └── main.js          # Electron主进程入口
│   ├── preload/             # 预加载脚本
│   │   └── preload.js       # 渲染进程API桥接
│   └── services/            # 后端服务
│       ├── ApiService.js    # API服务主类
│       └── controllers/     # 业务控制器
│           ├── ConfigManager.js      # 配置管理
│           ├── ProjectController.js  # 项目管理
│           ├── BookController.js     # 书籍管理
│           ├── ModelController.js    # AI模型管理
│           ├── FileController.js     # 文件操作
│           ├── BackupController.js   # 备份管理
│           └── UserController.js     # 用户管理
├── renderer/                # 渲染进程（前端代码）
│   └── [复制自原frontend目录]
├── build/                   # 构建资源
│   ├── icon.ico            # Windows图标
│   ├── icon.icns           # macOS图标
│   └── icon.png            # Linux图标
├── resources/              # 应用资源
├── dist/                   # 构建输出
├── package.json           # 项目配置
└── README.md             # 项目说明
```

## 多平台支持

### Windows
- 支持 x64 和 ia32 架构
- 提供 NSIS 安装包和便携版
- 自动创建桌面快捷方式和开始菜单项

### macOS
- 支持 Intel (x64) 和 Apple Silicon (arm64)
- 提供 DMG 安装包和 ZIP 压缩包
- 支持代码签名和公证

### Linux
- 支持 x64 和 arm64 架构
- 提供 AppImage、DEB 和 RPM 包格式
- 遵循 Linux 桌面应用标准

## 开发环境设置

### 前置要求
- Node.js 18+ 
- pnpm 8+
- Git

### 快速开始

#### Windows 用户
```bash
cd electron-app
setup.bat
```

#### macOS/Linux 用户
```bash
cd electron-app
chmod +x setup.sh
./setup.sh
```

#### 手动安装
```bash
cd electron-app
pnpm install
```

### 开发模式
```bash
# 启动开发环境（推荐）
pnpm run dev

# 仅启动主进程（测试模式）
pnpm run dev:test

# 仅启动主进程
pnpm run dev:main

# 仅启动渲染进程
pnpm run dev:renderer
```

### 构建应用
```bash
# 构建当前平台
pnpm run build

# 构建特定平台
pnpm run build:win    # Windows
pnpm run build:mac    # macOS
pnpm run build:linux  # Linux

# 构建所有平台
pnpm run build:all
```

## 配置说明

### 应用配置
应用配置存储在用户数据目录中：
- Windows: `%APPDATA%/PVV-Novel-Creator/`
- macOS: `~/Library/Application Support/PVV-Novel-Creator/`
- Linux: `~/.config/PVV-Novel-Creator/`

### 主要配置文件
- `config/settings.json`: 应用主配置
- `projects/`: 项目数据
- `books/`: 书籍数据
- `backups/`: 自动备份
- `backgrounds/`: 背景图片

## API迁移对照

原有的 `pywebview.api` 调用已迁移为 `window.electronAPI.invoke()` 调用：

```javascript
// 原有方式
const result = await window.pywebview.api.get_projects();

// 新方式
const result = await window.electronAPI.invoke('get_projects');

// 或使用兼容性API（推荐迁移期间使用）
const result = await window.pywebview.api.get_projects();
```

## 功能特性

### 核心功能
- ✅ 项目管理
- ✅ 书籍和章节管理
- ✅ 角色和设定管理
- ✅ 时间线管理
- ✅ Markdown编辑器
- ✅ AI助手集成
- ✅ 思维导图
- ✅ 自动保存和备份

### 新增功能
- ✅ 跨平台原生体验
- ✅ 自动更新支持
- ✅ 更好的文件系统集成
- ✅ 原生菜单和快捷键
- ✅ 系统通知
- ✅ 深度链接支持

## 部署和分发

### 自动更新
应用支持自动更新功能，需要配置更新服务器：

```json
{
  "publish": {
    "provider": "generic",
    "url": "https://your-update-server.com/"
  }
}
```

### 代码签名
生产环境建议配置代码签名：

```json
{
  "win": {
    "certificateFile": "path/to/certificate.p12",
    "certificatePassword": "password"
  },
  "mac": {
    "identity": "Developer ID Application: Your Name"
  }
}
```

## 故障排除

### 常见问题
1. **构建失败**: 检查Node.js版本和依赖安装
2. **启动白屏**: 检查渲染进程是否正常启动
3. **API调用失败**: 检查主进程服务是否正常初始化

### 调试模式
开发环境下会自动打开开发者工具，生产环境可通过菜单开启。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 更新日志

### v1.0.0
- 完成从Python pywebview到Electron的架构迁移
- 实现跨平台支持
- 保持所有原有功能完整性
- 新增自动更新和原生集成功能
