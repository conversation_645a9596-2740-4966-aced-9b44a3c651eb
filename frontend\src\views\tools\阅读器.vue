<template>
  <div class="mhtml-reader">
    <div class="reader-container">
      <div class="sidebar">
        <div class="directory-input">
          <el-input 
            v-model="directoryPath" 
            placeholder="输入MHTML文件目录路径"
            size="small"
          >
            <template #append>
              <el-button @click="loadCustomDirectory">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>

        <h3>MHTML文件目录</h3>
        <el-tree
          :data="directoryTree"
          :props="defaultProps"
          @node-click="handleNodeClick"
          :default-expand-all="true"
          node-key="path"
        >
          <template #default="{ data }">
            <div class="custom-tree-node">
              <span>
                <el-icon v-if="data.type === 'directory'"><Folder /></el-icon>
                <el-icon v-else><Document /></el-icon>
                {{ data.name }}
              </span>
              <span v-if="data.hasNotes" class="note-indicator">
                <el-icon><Edit /></el-icon>
              </span>
            </div>
          </template>
        </el-tree>
      </div>
      
      <div class="content">
        <div v-if="!currentFile" class="placeholder">
          <el-empty description="请选择MHTML文件进行阅读"></el-empty>
        </div>
        <div v-else class="reader-view">
          <div class="reader-header">
            <h3>{{ currentFile?.name }}</h3>
            <div class="actions">
              <el-dropdown v-if="currentFile" @command="switchViewMode">
                <el-button type="primary">
                  {{ getViewModeText() }}
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="normal">正常模式</el-dropdown-item>
                    <el-dropdown-item command="raw">原始内容</el-dropdown-item>
                    <el-dropdown-item command="decoded">解码内容</el-dropdown-item>
                    <el-dropdown-item command="plainhtml">直接HTML</el-dropdown-item>
                    <el-dropdown-item command="simple">简单文本模式</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              
              <el-select v-model="selectedCharset" @change="changeCharset" size="small" style="width: 100px">
                <el-option label="UTF-8" value="utf-8"></el-option>
                <el-option label="GBK" value="gbk"></el-option>
                <el-option label="GB2312" value="gb2312"></el-option>
                <el-option label="Big5" value="big5"></el-option>
              </el-select>
              
              <el-switch
                v-model="editMode"
                active-text="批注模式"
                inactive-text="阅读模式"
                @change="toggleEditMode"
              />
              <el-button type="primary" @click="saveNotes" :disabled="!editMode">
                保存批注
              </el-button>
              <el-button type="warning" @click="emergencyView" size="small">
                应急显示
              </el-button>
              <el-button 
                type="success" 
                @click="extractMHTML" 
                :disabled="!currentFile"
                size="small"
              >
                提取MHTML
              </el-button>
            </div>
          </div>
          
          <div class="reader-content" ref="readerContent">
            <div v-if="loading" class="loading-overlay">
              <el-loading></el-loading>
              <p>正在处理MHTML文件...</p>
            </div>
            
            <iframe 
              v-if="mhtmlContent && !editMode"
              ref="mhtmlFrame"
              class="mhtml-frame"
              :srcdoc="iframeContent"
              sandbox="allow-same-origin allow-scripts allow-forms"
              @load="onIframeLoad"
            ></iframe>
            
            <div v-if="editMode" class="editor-view">
              <div class="mhtml-container" ref="mhtmlContainer">
                <iframe 
                  ref="editFrame"
                  class="mhtml-frame"
                  :srcdoc="iframeContent"
                  sandbox="allow-same-origin allow-scripts allow-forms"
                  @load="onIframeLoad"
                ></iframe>
              </div>
              
              <div class="notes-panel">
                <h4>文档批注</h4>
                <div v-if="notes.length === 0" class="no-notes">
                  <p>尚无批注，点击"添加批注"按钮进行添加</p>
                </div>
                <div v-else class="notes-list">
                  <div v-for="(note, index) in notes" :key="index" class="note-item">
                    <div class="note-header">
                      <span class="note-title">批注 #{{ index + 1 }}</span>
                      <el-button
                        type="danger"
                        size="small"
                        circle
                        @click="removeNote(index)"
                        :icon="Delete"
                      ></el-button>
                    </div>
                    <el-input
                      v-model="note.content"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入批注内容"
                    ></el-input>
                  </div>
                </div>
                <el-button type="primary" @click="addNote" class="add-note-btn">
                  <el-icon><Plus /></el-icon> 添加批注
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="reading-progress">
      <el-slider 
        v-model="readingProgress" 
        :max="100" 
        :min="0"
        @change="scrollToPosition" 
        size="small"
      />
    </div>
    <el-dialog
      v-model="extractDialog.visible"
      title="MHTML提取成功"
      width="500px"
    >
      <div class="extract-dialog-content">
        <p>已成功将MHTML文件提取到以下目录：</p>
        <el-input v-model="extractDialog.outputDir" readonly>
          <template #append>
            <el-button @click="openOutputDir">
              打开目录
            </el-button>
          </template>
        </el-input>
        <p>提取文件数量：{{extractDialog.fileCount}}</p>
        <div class="extract-actions">
          <el-button type="primary" @click="openMainHtml">
            打开主HTML文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick, reactive } from 'vue'
import { Plus, Delete, Document, Folder, Edit, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DOMPurify from 'dompurify'

// 状态变量
const loading = ref(false)
const directoryTree = ref([])
const currentFile = ref(null)
const mhtmlContent = ref(null)
const notes = ref([])
const editMode = ref(false)
const readerContent = ref(null)
const mhtmlFrame = ref(null)
const editFrame = ref(null)
const mhtmlContainer = ref(null)
const currentBlobUrl = ref('')

// 新增HTML内容引用
const htmlWrapper = ref(null)
const editHtmlWrapper = ref(null)
const htmlContent = ref(null)

// 添加显示模式状态
const viewMode = ref('normal');

// 添加字符集选择状态
const selectedCharset = ref('utf-8');

// 添加处理后的HTML内容状态
const processedHtmlContent = ref('');

// 添加目录路径变量
const directoryPath = ref(''); // 默认路径

// 添加阅读进度状态
const readingProgress = ref(0);

// 添加提取状态变量
const extractDialog = reactive({
  visible: false,
  outputDir: '',
  mainHtmlFile: '',
  mainHtmlPath: '',
  fileCount: 0
});

// 计算属性
const mhtmlDataUrl = computed(() => {
  if (!mhtmlContent.value) return '';
  
  // 检查是否存在缓存的Blob URL
  if (currentBlobUrl.value) {
    return currentBlobUrl.value;
  }
  
  try {
    // 正确的MIME类型
    const byteCharacters = atob(mhtmlContent.value);
    const byteArrays = [];
    const sliceSize = 512;
    
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      byteArrays.push(new Uint8Array(byteNumbers));
    }
    
    // 使用正确的MIME类型
    const blob = new Blob(byteArrays, { type: 'message/rfc822' });
    const url = URL.createObjectURL(blob);
    currentBlobUrl.value = url;
    return url;
  } catch (e) {
    console.error('创建MHTML URL失败:', e);
    // 退回到data URL
    return `data:message/rfc822;base64,${mhtmlContent.value}`;
  }
})

const sanitizedHtml = computed(() => {
  if (!processedHtmlContent.value) return '';
  return DOMPurify.sanitize(processedHtmlContent.value);
});

const defaultProps = {
  children: 'children',
  label: 'name'
}

// 生命周期钩子
onMounted(() => {
  // 添加消息事件监听
  window.addEventListener('message', handleIframeMessage);
  
  // 加载目录
  loadDirectory();
});

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('message', handleIframeMessage);
  
  if (currentBlobUrl.value) {
    URL.revokeObjectURL(currentBlobUrl.value);
  }
});

// 方法
const loadCustomDirectory = async () => {
  if (!directoryPath.value) {
    ElMessage.warning('请输入有效的目录路径');
    return;
  }
  
  loading.value = true;
  try {
    const response = await window.pywebview.api.local_controller.load_dir_mhtml(directoryPath.value);
    const data = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (data.status === 'success') {
      directoryTree.value = data.data || [];
      if (data.data && data.data.length > 0) {
        ElMessage.success('加载目录成功');
      } else {
        ElMessage.info('目录为空或不存在MHTML文件');
      }
    } else {
      ElMessage.error('加载目录失败：' + (data?.message || '未知错误'));
    }
  } catch (error) {
    console.error('加载目录出错:', error);
    ElMessage.error('加载目录出错: ' + error.toString());
  } finally {
    loading.value = false;
  }
};

const loadDirectory = async () => {
  if (directoryPath.value) {
    await loadCustomDirectory();
  } else {
    // 使用默认路径
    directoryPath.value = '';
    await loadCustomDirectory();
  }
};

// 简单检测可能的编码问题
function detectEncodingIssues(content) {
  // 判断是否包含Unicode转义序列的特征
  const hasUnicodeEscapes = /\\u[0-9a-f]{4}/i.test(content);
  
  // 判断是否包含quoted-printable编码的特征
  const hasQuotedPrintable = /=[0-9A-F]{2}/i.test(content) && 
                            /Content-Transfer-Encoding:\s*quoted-printable/i.test(content);
  
  // 判断是否包含中文特征的编码字符
  const hasChineseEncoding = /=E\d=\w\d|=D\d=\w\d|=C\d=\w\d|=B\d=\w\d/i.test(content);
  
  return {
    hasUnicodeEscapes,
    hasQuotedPrintable,
    hasChineseEncoding,
    suggestedCharset: hasChineseEncoding ? 'gbk' : 'utf-8'
  };
}

// 修改iframeContent计算属性，确保所有模式都返回有效内容
const iframeContent = computed(() => {
  if (!mhtmlContent.value) return '<html><body><div>无内容可显示</div></body></html>';
  
  if (viewMode.value === 'normal') {
    // 在正常模式下使用清理后的HTML
    return sanitizedHtml.value || '<html><body><div>正在处理内容...</div></body></html>';
  } else if (viewMode.value === 'raw') {
    // 原始模式
    return `<html><body><pre style="white-space: pre-wrap; word-break: break-all;">${mhtmlContent.value}</pre></body></html>`;
  } else if (viewMode.value === 'decoded') {
    // 解码模式
    try {
      const decoded = atob(mhtmlContent.value);
      return `<html><body><pre style="white-space: pre-wrap; word-break: break-all;">${escapeHtml(decoded)}</pre></body></html>`;
    } catch (e) {
      return `<html><body><div>解码失败: ${e.message}</div></body></html>`;
    }
  } else if (viewMode.value === 'plainhtml') {
    // 直接HTML模式
    try {
      const decoded = atob(mhtmlContent.value);
      const htmlMatch = decoded.match(/<html[\s\S]*<\/html>/i);
      if (htmlMatch) {
        return htmlMatch[0];
      } else {
        return `<html><body><div>未找到HTML内容</div></body></html>`;
      }
    } catch (e) {
      return `<html><body><div>解析失败: ${e.message}</div></body></html>`;
    }
  } else if (viewMode.value === 'simple') {
    // 简单文本模式：尝试直接显示文本内容
    try {
      const decoded = atob(mhtmlContent.value);
      let displayText = '';
      
      // 过滤保留可打印字符和中文
      displayText = decoded.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
      
      return `
        <html>
        <head>
          <meta charset="utf-8">
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.5;
              padding: 20px;
              background: #fff;
              color: #333;
            }
            pre {
              white-space: pre-wrap;
              word-break: break-all;
              background: #f8f8f8;
              padding: 15px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
              max-height: none;
              overflow: visible;
            }
          </style>
        </head>
        <body>
          <h2>MHTML文件内容（简单文本模式）</h2>
          <pre>${escapeHtml(displayText)}</pre>
        </body>
        </html>
      `;
    } catch (e) {
      return `<html><body><div>解析文件失败: ${e.message}</div></body></html>`;
    }
  }
  
  // 默认返回一个安全的HTML
  return '<html><body><div>未知视图模式</div></body></html>';
});

// 重新添加HTML转义函数
const escapeHtml = (text) => {
  return text
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
};

// 修改handleNodeClick函数，使用增强型渲染
const handleNodeClick = async (data) => {
  if (data.type === 'file' && data.fileType === 'mhtml') {
    try {
      loading.value = true;
      
      // 获取MHTML内容
      const contentResponse = await window.pywebview.api.local_controller.get_mhtml_content(data.path);
      const contentResult = typeof contentResponse === 'string' ? JSON.parse(contentResponse) : contentResponse;
      
      if (contentResult.status === 'success' && contentResult.data) {
        currentFile.value = data;
        mhtmlContent.value = contentResult.data.content;
        notes.value = contentResult.data.notes || [];
        
        // 使用增强型渲染方法
        const htmlResponse = await window.pywebview.api.local_controller.improve_mhtml_rendering(data.path, selectedCharset.value);
        const htmlResult = typeof htmlResponse === 'string' ? JSON.parse(htmlResponse) : htmlResponse;
        
        if (htmlResult.status === 'success' && htmlResult.data) {
          processedHtmlContent.value = htmlResult.data.html;
          
          // 如果有建议的字符集，更新选择的字符集
          if (htmlResult.data.charset) {
            selectedCharset.value = htmlResult.data.charset;
          }
          
          viewMode.value = 'normal';
          ElMessage.success(`文件加载成功，包含${htmlResult.data.resourceCount}个资源`);
          
          // 刷新iframe内容
          nextTick(() => {
            refreshIframeContent();
          });
        } else {
          console.warn('增强渲染失败，回退到简单模式');
          viewMode.value = 'simple';
          
          // 使用简单模式显示
          nextTick(() => {
            refreshIframeContent();
          });
          
          ElMessage.warning('渲染失败，显示简单内容');
        }
        
        editMode.value = false;
      } else {
        ElMessage.error('加载文件失败：' + (contentResult?.message || '未知错误'));
      }
    } catch (error) {
      console.error('加载文件出错:', error);
      ElMessage.error('加载文件出错: ' + error.toString());
      // 出错时使用简单模式
      viewMode.value = 'simple';
      nextTick(() => {
        refreshIframeContent();
      });
    } finally {
      loading.value = false;
    }
  }
};

const toggleEditMode = (val) => {
  editMode.value = val;
  // 切换模式后重新渲染内容
  nextTick(() => {
    renderHtmlContent();
    if (val) {
      initEditor();
    }
  });
}

const initEditor = () => {
  // 这里可以初始化编辑器，例如为iframe中的元素添加事件监听等
  if (editFrame.value) {
    // 等待iframe加载完成
    editFrame.value.onload = () => {
      try {
        const frameDoc = editFrame.value.contentDocument || editFrame.value.contentWindow.document
        // 这里可以对iframe中的内容进行修改，添加批注按钮等
        console.log('iframe loaded, ready for annotations')
      } catch (e) {
        console.error('无法访问iframe内容:', e)
      }
    }
  }
}

const addNote = () => {
  notes.value.push({
    id: Date.now(),
    content: '',
    position: { x: 0, y: 0 },
    timestamp: new Date().toISOString()
  })
}

const removeNote = (index) => {
  notes.value.splice(index, 1)
}

const saveNotes = async () => {
  if (!currentFile.value) return
  
  try {
    loading.value = true
    // 只保存笔记，不修改原始MHTML内容
    const rawResponse = await window.pywebview.api.local_controller.save_mhtml_with_notes(
      currentFile.value.path,
      null, // 不修改原始内容
      notes.value
    )
    const response = typeof rawResponse === 'string' ? JSON.parse(rawResponse) : rawResponse
    
    if (response.status === 'success') {
      ElMessage.success('笔记保存成功')
      // 更新目录树中的hasNotes标记
      await loadDirectory()
    } else {
      ElMessage.error('保存笔记失败：' + (response?.message || '未知错误'))
    }
  } catch (error) {
    console.error('保存笔记出错:', error)
    ElMessage.error('保存笔记出错: ' + error.toString())
  } finally {
    loading.value = false
  }
}

const onIframeLoad = (event) => {
  console.log('iframe已加载');
  
  try {
    const iframe = event.target;
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    
    // 修复iframe中的链接和图片
    fixIframeLinks(iframe);
    
    // 检查内容是否加载成功
    if (iframeDoc && iframeDoc.body) {
      const bodyContent = iframeDoc.body.innerHTML;
      console.log(`iframe内容加载完成，内容大小: ${bodyContent.length}`);
      
      if (bodyContent.length < 10) {
        console.warn('iframe内容可能为空');
        ElMessage.warning('文档内容可能未正确加载');
      } else {
        ElMessage.success('文档加载成功');
      }
    } else {
      console.warn('iframe文档为空');
      ElMessage.warning('文档可能未正确加载');
      refreshIframeContent(); // 尝试重新加载内容
    }
    
    // 添加滚动跟踪
    setupScrollTracking();
  } catch (e) {
    console.error('访问iframe内容失败:', e);
    refreshIframeContent(); // 尝试重新加载内容
  }
}

const onIframeError = (error) => {
  console.error('iframe加载错误:', error);
  ElMessage.error('MHTML内容加载失败');
}

// 修改refreshIframeContent，确保不再使用src属性
const refreshIframeContent = () => {
  if (editMode.value) {
    // 在编辑模式下更新editFrame
    if (editFrame.value) {
      const doc = editFrame.value.contentDocument || editFrame.value.contentWindow.document;
      doc.open();
      doc.write(iframeContent.value);
      doc.close();
      
      // 修复iframe中的链接和图片
      nextTick(() => {
        fixIframeLinks(editFrame.value);
      });
    }
  } else {
    // 在阅读模式下更新mhtmlFrame
    const iframe = mhtmlFrame.value;
    if (iframe) {
      const doc = iframe.contentDocument || iframe.contentWindow.document;
      doc.open();
      doc.write(iframeContent.value);
      doc.close();
      
      // 修复iframe中的链接和图片
      nextTick(() => {
        fixIframeLinks(iframe);
      });
    }
  }
};

// 修改字符集变更处理
const changeCharset = async () => {
  if (!currentFile.value) return;
  
  try {
    loading.value = true;
    console.log('字符集变更为:', selectedCharset.value);
    
    // 重新处理MHTML
    const htmlResponse = await window.pywebview.api.local_controller.process_mhtml_to_html(
      currentFile.value.path, 
      selectedCharset.value
    );
    const htmlResult = typeof htmlResponse === 'string' ? JSON.parse(htmlResponse) : htmlResponse;
    
    if (htmlResult.status === 'success' && htmlResult.data) {
      processedHtmlContent.value = htmlResult.data.html;
      refreshIframeContent(); // 刷新显示
      ElMessage.success(`已切换到${selectedCharset.value}字符集`);
    } else {
      ElMessage.error('更新内容失败：' + (htmlResult?.message || '未知错误'));
    }
  } catch (error) {
    console.error('更新字符集出错:', error);
    ElMessage.error('更新字符集出错: ' + error.toString());
  } finally {
    loading.value = false;
  }
};

// 获取当前视图模式的文本描述
const getViewModeText = () => {
  const modeTexts = {
    'normal': '正常模式',
    'raw': '原始内容',
    'decoded': '解码内容',
    'plainhtml': '直接HTML',
    'simple': '简单文本模式'
  };
  return modeTexts[viewMode.value] || '正常模式';
};

// 改进切换视图模式函数
const switchViewMode = (mode) => {
  viewMode.value = mode;
  console.log(`切换到${mode}模式`);
  ElMessage.success(`已切换到${getViewModeText()}`);
  refreshIframeContent();
};

// 修复iframe中的相对链接
function fixIframeLinks(iframe) {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    
    // 禁用所有链接的点击行为
    const links = iframeDoc.querySelectorAll('a');
    links.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('链接点击被阻止:', link.href);
      });
    });
    
    // 为图片添加错误处理
    const images = iframeDoc.querySelectorAll('img');
    images.forEach(img => {
      img.onerror = function() {
        this.style.display = 'none';
        console.log('图片加载失败:', this.src);
      };
    });
  } catch (e) {
    console.error('修复iframe链接时出错:', e);
  }
}

// 新增渲染HTML内容的函数
const renderHtmlContent = () => {
  try {
    const wrapper = editMode.value ? editHtmlWrapper.value : htmlWrapper.value;
    if (!wrapper || !htmlContent.value) {
      console.warn('找不到HTML容器或内容为空');
      return;
    }
    
    // 创建一个安全的容器，设置HTML内容
    wrapper.innerHTML = `
      <div class="html-container" style="width:100%; height:100%; overflow:auto;">
        ${htmlContent.value}
      </div>
    `;
    
    console.log('已渲染HTML内容');
    ElMessage.success('文档已加载');
  } catch (e) {
    console.error('渲染HTML内容失败:', e);
    ElMessage.error('显示文档内容失败');
  }
}

// 处理iframe发送的消息
const handleIframeMessage = (event) => {
  if (event.data === 'mhtml-loaded') {
    console.log('MHTML内容加载完成');
    ElMessage.success('文档加载成功');
    
    // 延迟设置滚动跟踪，确保内容已完全渲染
    setTimeout(() => {
      setupScrollTracking();
    }, 500);
  }
};

// 修改setupScrollTracking增加错误处理和重试
const setupScrollTracking = () => {
  if (mhtmlFrame.value) {
    try {
      const iframe = mhtmlFrame.value;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
      
      if (!iframeDoc || !iframeDoc.body) {
        console.warn('iframe文档尚未准备好，稍后重试');
        setTimeout(setupScrollTracking, 500);
        return;
      }
      
      // 监听滚动事件
      iframe.contentWindow.addEventListener('scroll', () => {
        const scrollHeight = iframeDoc.documentElement.scrollHeight - iframeDoc.documentElement.clientHeight;
        const scrollTop = iframeDoc.documentElement.scrollTop || iframeDoc.body.scrollTop;
        readingProgress.value = Math.round((scrollTop / scrollHeight) * 100);
      });
      
      console.log('滚动跟踪设置成功');
    } catch (e) {
      console.error('设置滚动跟踪失败:', e);
    }
  }
};

// 滚动到指定位置
const scrollToPosition = (progress) => {
  if (mhtmlFrame.value) {
    try {
      const iframe = mhtmlFrame.value;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
      
      const scrollHeight = iframeDoc.documentElement.scrollHeight - iframeDoc.documentElement.clientHeight;
      const targetPosition = (progress / 100) * scrollHeight;
      
      iframeDoc.documentElement.scrollTop = targetPosition;
      iframeDoc.body.scrollTop = targetPosition;
    } catch (e) {
      console.error('滚动到位置失败:', e);
    }
  }
};

// 添加应急显示函数
const emergencyView = async () => {
  try {
    if (!currentFile.value) return;
    
    loading.value = true;
    
    // 直接从文件中读取文本内容
    const response = await window.pywebview.api.local_controller.get_mhtml_content(currentFile.value.path);
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.status === 'success' && result.data) {
      // 解码base64内容
      try {
        const decoded = atob(result.data.content);
        // 过滤掉不可打印字符
        const filtered = decoded.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
        
        // 直接创建一个纯文本显示的HTML
        const emergencyHtml = `
          <html>
          <head>
            <meta charset="${selectedCharset.value}">
            <style>
              body { font-family: monospace; white-space: pre-wrap; padding: 20px; line-height: 1.5; }
            </style>
          </head>
          <body>${escapeHtml(filtered)}</body>
          </html>
        `;
        
        processedHtmlContent.value = emergencyHtml;
        viewMode.value = 'normal';
        refreshIframeContent();
        
        ElMessage.success('已使用应急模式显示文件内容');
      } catch (e) {
        ElMessage.error('应急显示失败: ' + e.toString());
      }
    } else {
      ElMessage.error('获取文件内容失败');
    }
  } catch (e) {
    console.error('应急显示出错:', e);
    ElMessage.error('应急显示出错: ' + e.toString());
  } finally {
    loading.value = false;
  }
};

// 提取MHTML方法
const extractMHTML = async () => {
  try {
    if (!currentFile.value) return;
    
    loading.value = true;
    ElMessage.info('正在提取MHTML文件，请稍候...');
    
    const response = await window.pywebview.api.local_controller.extract_mhtml(currentFile.value.path);
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.status === 'success' && result.data) {
      extractDialog.outputDir = result.data.outputDir;
      extractDialog.mainHtmlFile = result.data.mainHtmlFile;
      extractDialog.mainHtmlPath = result.data.mainHtmlPath;
      extractDialog.fileCount = result.data.fileCount;
      extractDialog.visible = true;
      
      ElMessage.success(`MHTML提取成功，共提取了${result.data.fileCount}个文件`);
    } else {
      ElMessage.error('提取MHTML失败：' + (result?.message || '未知错误'));
    }
  } catch (e) {
    console.error('提取MHTML出错:', e);
    ElMessage.error('提取MHTML出错: ' + e.toString());
  } finally {
    loading.value = false;
  }
};

// 打开输出目录
const openOutputDir = async () => {
  try {
    if (!extractDialog.outputDir) return;
    
    // 调用操作系统打开文件夹的功能
    await window.pywebview.api.local_controller.open_directory(extractDialog.outputDir);
  } catch (e) {
    console.error('打开目录失败:', e);
    ElMessage.error('打开目录失败: ' + e.toString());
  }
};

// 打开主HTML文件
const openMainHtml = async () => {
  try {
    if (!extractDialog.mainHtmlPath) return;
    
    // 调用操作系统打开文件的功能
    await window.pywebview.api.local_controller.open_file(extractDialog.mainHtmlPath);
  } catch (e) {
    console.error('打开文件失败:', e);
    ElMessage.error('打开文件失败: ' + e.toString());
  }
};
</script>

<style scoped>
.mhtml-reader {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.reader-container {
  display: flex;
  height: calc(100vh - 120px);
  width: 100%;
}

.sidebar {
  width: 250px;
  border-right: 1px solid #dcdfe6;
  padding: 15px;
  overflow-y: auto;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.reader-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.reader-header {
  padding: 10px 15px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.reader-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.mhtml-frame {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.editor-view {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.mhtml-container {
  flex: 1;
  overflow: hidden;
}

.notes-panel {
  width: 300px;
  border-left: 1px solid #dcdfe6;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.notes-list {
  flex: 1;
  margin-bottom: 15px;
  overflow-y: auto;
}

.no-notes {
  color: #909399;
  text-align: center;
  margin: 20px 0;
}

.note-item {
  margin-bottom: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.note-title {
  font-weight: bold;
}

.add-note-btn {
  margin-top: auto;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.note-indicator {
  color: #409eff;
}

/* 添加HTML容器样式 */
.html-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: white;
}

.html-container {
  background: white;
  padding: 20px;
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 防止内容CSS破坏布局 */
.html-container :deep(*) {
  max-width: 100%;
}

.html-container :deep(img) {
  max-width: 100%;
  height: auto;
}

/* 增加mhtml-viewer样式 */
.mhtml-viewer {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: white;
}

/* 添加加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

/* 添加目录输入框样式 */
.directory-input {
  margin-bottom: 15px;
}

.reading-progress {
  padding: 10px;
}

.extract-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.extract-actions {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}
</style>
