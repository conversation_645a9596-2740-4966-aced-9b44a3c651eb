// 菜单和路由配置 - 统一管理
export const menuList = [
  {
    path: 'dashboard',
    title: '仪表板',
    icon: 'House',
    component: () => import('@/views/dashboard/index.vue')
  },
  {
    path: 'book',
    title: '书籍',
    icon: 'Notebook',
    children: [
      {
        path: '写作',
        title: '写作',
        component: () => import('@/views/book/写作.vue'),
        meta: { keepAlive: true }
      }
    ]
  }
  // 暂时注释掉未实现的功能，后续逐步添加
  /*
  {
    path: '/book',
    title: '书籍',
    icon: 'Notebook',
    children: [
      {
        path: '/book/写作',
        title: '写作',
        component: () => import('@/views/book/写作.vue'),
        meta: { keepAlive: true }
      },
      {
        path: '/book/outline',
        title: '大纲',
        component: () => import('@/views/book/outline.vue')
      }
    ]
  },
  {
    path: '/chat',
    title: '大模型',
    icon: 'ChatSquare',
    children: [
      {
        path: '/chat/list',
        title: '在线',
        component: () => import('@/views/chat/chat.vue')
      }
    ]
  }
  */
]

// 生成路由配置的工具函数
export function generateRoutes(menus) {
  const routes = []

  menus.forEach(menu => {
    const route = {
      path: menu.path,
      name: menu.name || menu.path.replace(/[\/\:]/g, ''),
      component: menu.component,
      meta: {
        title: menu.title,
        icon: menu.icon,
        hidden: menu.hidden,
        ...(menu.meta || {})
      }
    }

    if (menu.children) {
      route.children = generateRoutes(menu.children)
    }

    routes.push(route)
  })

  return routes
}
