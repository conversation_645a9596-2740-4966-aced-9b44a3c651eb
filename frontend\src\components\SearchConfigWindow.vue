<template>
    <!-- 主配置弹窗 -->
    <div v-if="dialogVisible" class="custom-dialog-overlay" @click="handleOverlayClick">
      <!-- 自定义弹窗容器 -->
      <div class="custom-dialog-container" @click.stop>
      <!-- 固定头部 -->
      <div class="dialog-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon><Search /></el-icon>
          </div>
          <div class="header-text">
            <h2 class="dialog-title">网络查询配置</h2>
            <p class="dialog-subtitle">管理您的搜索引擎和查询提供商</p>
          </div>
        </div>
        <button @click="closeDialog" class="close-btn">
          <el-icon><Close /></el-icon>
        </button>
      </div>

      <!-- 固定的操作区域 -->
      <div class="dialog-actions">
        <div class="actions-left">
          <h3 class="section-title">查询提供商</h3>
          <span class="provider-count">{{ providers.length }} 个提供商</span>
        </div>
        <el-button type="primary" @click="addNewProvider" class="add-button">
          <el-icon><Plus /></el-icon>
          <span>添加提供商</span>
        </el-button>
      </div>

      <!-- 可滚动的内容区域 -->
      <div class="dialog-content">
        <div v-if="providers.length === 0" class="empty-state">
          <div class="empty-icon">
            <el-icon><Search /></el-icon>
          </div>
          <h4>暂无搜索提供商</h4>
          <p>添加您常用的搜索引擎，快速进行网络查询</p>
          <el-button type="primary" @click="addNewProvider" class="empty-add-btn">
            <el-icon><Plus /></el-icon>
            添加第一个提供商
          </el-button>
        </div>

        <div class="provider-cards" v-else>
          <div
            v-for="(provider, index) in providers"
            :key="provider.id || index"
            class="provider-item"
            :class="{ 'is-default': provider.isDefault }"
          >
            <div class="provider-content">
              <div class="provider-header-row">
                <div class="provider-info">
                  <div class="provider-name no-select">
                    {{ provider.name }}
                    <el-tag v-if="provider.isDefault" size="small" type="success" class="default-tag">
                      默认
                    </el-tag>
                  </div>
                  <div class="provider-url no-select">{{ formatUrl(provider.url) }}</div>
                </div>
                <div class="provider-actions">
                  <div class="action-buttons">
                    <el-tooltip content="设为默认" placement="top" v-if="!provider.isDefault">
                      <el-button
                        size="small"
                        @click="makeDefault(index)"
                        class="action-btn default-btn"
                      >
                        <el-icon><Star /></el-icon>
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="上移" placement="top">
                      <el-button
                        size="small"
                        :disabled="index === 0"
                        @click="moveProvider(index, 'up')"
                        class="action-btn"
                      >
                        <el-icon><ArrowUp /></el-icon>
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="下移" placement="top">
                      <el-button
                        size="small"
                        :disabled="index === providers.length - 1"
                        @click="moveProvider(index, 'down')"
                        class="action-btn"
                      >
                        <el-icon><ArrowDown /></el-icon>
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="编辑" placement="top">
                      <el-button
                        size="small"
                        @click="editProvider(index)"
                        class="action-btn edit-btn"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="删除" placement="top">
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeProvider(index)"
                        class="action-btn delete-btn"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 固定底部操作区 -->
      <div class="dialog-footer">
        <el-button @click="closeDialog" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="saveConfig" class="save-btn">保存</el-button>
      </div>
    </div>

    <!-- 编辑提供商的弹窗 -->
    <div v-if="editDialogVisible" class="edit-dialog-overlay" @click="handleEditOverlayClick">
      <div class="edit-dialog-container" @click.stop>
      <!-- 编辑弹窗头部 -->
      <div class="edit-dialog-header">
        <div class="edit-header-content">
          <div class="edit-header-icon">
            <el-icon><Edit v-if="isEditing" /><Plus v-else /></el-icon>
          </div>
          <div class="edit-header-text">
            <h3 class="edit-dialog-title">{{ isEditing ? '编辑提供商' : '添加提供商' }}</h3>
            <p class="edit-dialog-subtitle">配置搜索引擎的名称和URL模板</p>
          </div>
        </div>
        <button @click="closeEditDialog" class="edit-close-btn">
          <el-icon><Close /></el-icon>
        </button>
      </div>

      <!-- 编辑表单内容 -->
      <div class="edit-dialog-content">
        <el-form :model="currentProvider" label-width="120px" class="provider-form">
          <el-form-item label="提供商名称" required>
            <el-input
              v-model="currentProvider.name"
              placeholder="例如：Google、百度、知乎等"
              size="large"
            />
          </el-form-item>

          <el-form-item label="URL模板" required>
            <el-input
              v-model="currentProvider.url"
              placeholder="输入搜索引擎的URL地址"
              size="large"
            >
              <template #append>
                <el-tooltip placement="top" :hide-after="0">
                  <template #content>
                    <div class="url-help-content">
                      <h4>URL格式说明</h4>
                      <div class="help-section">
                        <p><strong>使用占位符（推荐）：</strong></p>
                        <ul>
                          <li>使用 <code>{query}</code> 或 <code>{q}</code> 作为搜索词占位符</li>
                          <li>搜索时会自动替换为实际的搜索内容</li>
                        </ul>
                      </div>
                      <div class="help-section">
                        <p><strong>不使用占位符：</strong></p>
                        <ul>
                          <li>直接输入网站URL，如 <code>https://www.google.com</code></li>
                          <li>点击打开时会复制搜索内容到剪贴板，方便手动粘贴</li>
                        </ul>
                      </div>
                    </div>
                  </template>
                  <el-button class="help-btn">
                    <el-icon><InfoFilled /></el-icon>
                  </el-button>
                </el-tooltip>
              </template>
            </el-input>

            <div class="url-examples">
              <div class="examples-header">
                <span class="examples-label">常用模板：</span>
              </div>
              <div class="examples-grid">
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://www.google.com/search?q={query}')"
                >
                  Google
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://www.baidu.com/s?wd={query}')"
                >
                  百度
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://www.bing.com/search?q={query}')"
                >
                  Bing
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://www.zhihu.com/search?q={query}')"
                >
                  知乎
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://github.com/search?q={query}')"
                >
                  GitHub
                </el-tag>
                <el-tag
                  size="small"
                  class="example-tag"
                  @click="applyExample('https://stackoverflow.com/search?q={query}')"
                >
                  Stack Overflow
                </el-tag>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="设为默认">
            <el-switch
              v-model="currentProvider.isDefault"
              active-text="是"
              inactive-text="否"
            />
            <span class="switch-tip">默认提供商将在按回车键时使用</span>
          </el-form-item>

          <el-form-item label="URL预览">
            <div class="preview-container">
              <div class="preview-url">
                {{ previewUrl }}
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 编辑弹窗底部 -->
      <div class="edit-dialog-footer">
        <el-button @click="closeEditDialog" size="large" class="cancel-btn">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="saveProvider"
          :disabled="!isProviderValid"
          size="large"
          class="save-btn"
        >
          {{ isEditing ? '保存更改' : '添加提供商' }}
        </el-button>
      </div>
    </div>
  </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Edit, Delete, InfoFilled, ArrowUp, ArrowDown, Plus, Search, Close, Star } from '@element-plus/icons-vue';
import { useConfigStore } from '@/stores/config';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  bookId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['update:visible', 'config-updated']);
const configStore = useConfigStore();

// Reactive variables
const dialogVisible = ref(false);
const editDialogVisible = ref(false);
const isEditing = ref(false);
const editIndex = ref(-1);
const providers = ref([]);

// Current provider being edited
const currentProvider = ref({
  id: '',
  name: '',
  url: '',
  isDefault: false
});

// Format URL for display (truncate if too long)
const formatUrl = (url) => {
  if (!url) return '';
  if (url.length > 50) {
    return url.substring(0, 47) + '...';
  }
  return url;
};

// Preview URL with example search term
const previewUrl = computed(() => {
  if (!currentProvider.value.url) return '输入URL后将显示预览效果';

  let url = currentProvider.value.url;
  const exampleQuery = '示例搜索词';

  if (url.includes('{query}')) {
    return url.replace('{query}', exampleQuery);
  } else if (url.includes('{q}')) {
    return url.replace('{q}', exampleQuery);
  } else {
    return url + ' (将复制搜索内容到剪贴板)';
  }
});

// Check if current provider is valid for saving
const isProviderValid = computed(() => {
  return (
    currentProvider.value.name.trim() !== '' && 
    currentProvider.value.url.trim() !== ''
    // 移除对{query}的强制检查，支持无占位符的URL
  );
});

// Watch for visibility change from parent component
watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue;
  
  if (newValue) {
    // Load providers when dialog opens
    loadSearchConfig();
  }
});

// Watch for dialog visibility to update parent component
watch(dialogVisible, (newValue) => {
  emit('update:visible', newValue);
});

// 关闭主弹窗
const closeDialog = () => {
  dialogVisible.value = false;
};

// 处理遮罩点击
const handleOverlayClick = () => {
  // 可以选择是否允许点击遮罩关闭
  // closeDialog();
};

// 关闭编辑弹窗
const closeEditDialog = () => {
  editDialogVisible.value = false;
};

// 处理编辑弹窗遮罩点击
const handleEditOverlayClick = () => {
  // 可以选择是否允许点击遮罩关闭
  // closeEditDialog();
};

// Load search config from config store
const loadSearchConfig = () => {
  const searchConfig = configStore.search || { providers: [] };
  providers.value = JSON.parse(JSON.stringify(searchConfig.providers || []));
};

// Save search configuration
const saveConfig = async () => {
  try {
    // 确保至少一个默认提供商是设置
    if (providers.value.length > 0 && !providers.value.some(p => p.isDefault)) {
      providers.value[0].isDefault = true;
    }
    
    // 使用配置存储的updateConfig方法更新配置
    await configStore.updateConfig({
      search: {
        providers: providers.value
      }
    });
    
    emit('config-updated');
    ElMessage.success('搜索配置已保存');
    dialogVisible.value = false;
  } catch (error) {
    console.error('保存搜索配置失败:', error);
    ElMessage.error('保存配置失败: ' + error.message);
  }
};

// Add new provider
const addNewProvider = () => {
  isEditing.value = false;
  editIndex.value = -1;
  currentProvider.value = {
    id: generateId(),
    name: '',
    url: '',
    isDefault: providers.value.length === 0 // First provider is default
  };
  editDialogVisible.value = true;
};

// Edit existing provider
const editProvider = (index) => {
  isEditing.value = true;
  editIndex.value = index;
  currentProvider.value = JSON.parse(JSON.stringify(providers.value[index]));
  editDialogVisible.value = true;
};

// Remove provider
const removeProvider = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该搜索提供商吗？',
      '删除提供商',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const wasDefault = providers.value[index].isDefault;
    providers.value.splice(index, 1);
    
    // If the removed provider was default and we have other providers, set the first as default
    if (wasDefault && providers.value.length > 0) {
      providers.value[0].isDefault = true;
    }
  } catch {
    // User canceled the operation
  }
};

// Save provider (add new or update existing)
const saveProvider = () => {
  if (!isProviderValid.value) return;
  
  if (isEditing.value) {
    // Update existing provider
    providers.value[editIndex.value] = { ...currentProvider.value };
    
    // If this provider is set as default, unset all others
    if (currentProvider.value.isDefault) {
      makeDefault(editIndex.value);
    }
  } else {
    // Add new provider
    providers.value.push({ ...currentProvider.value });
    
    // If this provider is set as default, unset all others
    if (currentProvider.value.isDefault) {
      makeDefault(providers.value.length - 1);
    }
  }
  
  editDialogVisible.value = false;
};

// Set a provider as default and unset others
const makeDefault = (index) => {
  providers.value.forEach((provider, i) => {
    provider.isDefault = i === index;
  });
};

// 移动提供商位置 - 实现列表排序功能
const moveProvider = (index, direction) => {
  const newIndex = direction === 'up' ? index - 1 : index + 1;
  
  // 确保新位置在有效范围内
  if (newIndex < 0 || newIndex >= providers.value.length) {
    return;
  }
  
  // 交换元素位置
  const temp = providers.value[newIndex];
  providers.value[newIndex] = providers.value[index];
  providers.value[index] = temp;
  
  // 如果移动的是默认提供商，保持其默认状态
  if (providers.value[newIndex].isDefault) {
    makeDefault(newIndex);
  } else if (providers.value[index].isDefault) {
    makeDefault(index);
  }
};

// Generate a random ID for new providers
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// Handle dialog closed
const onDialogClosed = () => {
  emit('update:visible', false);
};

// 将示例URL应用到当前提供商
const applyExample = (exampleUrl) => {
  currentProvider.value.url = exampleUrl;
};
</script>

<style lang="scss" scoped>
/* 自定义弹窗遮罩 */
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

/* 自定义弹窗容器 */
.custom-dialog-container {
  width: 760px;
  height: 82vh;
  max-height: 650px;
  background: var(--el-bg-color);
  border-radius: 20px;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid var(--el-border-color-light);
  backdrop-filter: blur(20px);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 弹窗头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 32px;
  background: linear-gradient(135deg,
    var(--el-color-primary) 0%,
    var(--el-color-primary-light-3) 50%,
    var(--el-color-primary-light-5) 100%);
  color: var(--el-color-white);
  flex-shrink: 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%);
    pointer-events: none;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;

    .header-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: var(--el-color-primary-light-3);
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        font-size: 24px;
        color: var(--el-color-white);
      }
    }

    .header-text {
      .dialog-title {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--el-color-white);
      }

      .dialog-subtitle {
        margin: 0;
        font-size: 14px;
        color: var(--el-color-white);
        opacity: 0.85;
      }
    }
  }

  .close-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: var(--el-color-white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    .el-icon {
      font-size: 18px;
    }
  }
}

/* 操作区域 */
.dialog-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(to bottom,
    var(--el-bg-color) 0%,
    var(--el-fill-color-extra-light) 100%);
  border-bottom: 1px solid var(--el-border-color-lighter);
  flex-shrink: 0;

  .actions-left {
    display: flex;
    align-items: baseline;
    gap: 12px;

    .section-title {
      margin: 0;
      color: var(--el-text-color-primary);
      font-weight: 600;
      font-size: 18px;
    }

    .provider-count {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      background: var(--el-fill-color-light);
      padding: 4px 10px;
      border-radius: 12px;
      font-weight: 500;
    }
  }

  .add-button {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

/* 内容区域 */
.dialog-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 28px;
  min-height: 0;
}

/* 底部操作区 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 28px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-lighter);
  flex-shrink: 0;

  .cancel-btn, .save-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .save-btn {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

/* 滚动条样式 */
.dialog-content {
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-dark);
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--el-text-color-secondary);
    }
  }

  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-dark) var(--el-fill-color-lighter);
}

/* 编辑弹窗样式 */
.edit-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2100;
  animation: fadeIn 0.3s ease;
}

.edit-dialog-container {
  width: 640px;
  max-height: 85vh;
  background: var(--el-bg-color);
  border-radius: 20px;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid var(--el-border-color-light);
  backdrop-filter: blur(20px);
}

.edit-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 32px;
  background: linear-gradient(135deg,
    var(--el-color-success) 0%,
    var(--el-color-success-light-3) 50%,
    var(--el-color-success-light-5) 100%);
  color: var(--el-color-white);
  flex-shrink: 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%);
    pointer-events: none;
  }

  .edit-header-content {
    display: flex;
    align-items: center;
    gap: 16px;

    .edit-header-icon {
      width: 48px;
      height: 48px;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);

      .el-icon {
        font-size: 24px;
      }
    }

    .edit-header-text {
      .edit-dialog-title {
        margin: 0 0 6px 0;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: -0.5px;
      }

      .edit-dialog-subtitle {
        margin: 0;
        font-size: 14px;
        opacity: 0.85;
        font-weight: 400;
      }
    }
  }

  .edit-close-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: var(--el-color-white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    .el-icon {
      font-size: 18px;
    }
  }
}

.edit-dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px 28px;
}

.edit-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 28px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-lighter);
  flex-shrink: 0;

  .cancel-btn, .save-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .save-btn {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

  .header-left {
    display: flex;
    align-items: baseline;
    gap: 12px;

    .section-title {
      margin: 0;
      color: var(--el-text-color-primary);
      font-weight: 600;
      font-size: 18px;
    }

    .provider-count {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      background: var(--el-fill-color-light);
      padding: 2px 8px;
      border-radius: 12px;
    }
  }

  .add-button {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 6px;
    border-radius: 8px;
    font-weight: 500;
    box-shadow: var(--el-box-shadow-light);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--el-box-shadow);
    }
  
}

/* 移除旧的样式，使用新的布局 */

.empty-state {
  text-align: center;
  padding: 80px 40px;
  background: linear-gradient(135deg,
    var(--el-fill-color-blank) 0%,
    var(--el-fill-color-extra-light) 100%);
  border-radius: 20px;
  border: 2px dashed var(--el-border-color-light);
  margin: 40px 0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle,
      rgba(var(--el-color-primary-rgb), 0.03) 0%,
      transparent 70%);
    animation: float 6s ease-in-out infinite;
  }

  .empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: var(--el-color-primary-light-9);
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon {
      font-size: 36px;
      color: var(--el-color-primary);
    }
  }

  h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: var(--el-text-color-primary);
    font-weight: 600;
  }

  p {
    margin: 0 0 24px 0;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }

  .empty-add-btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
  }
}

.provider-cards {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  /* 确保不会撑开父容器 */
  min-height: 0 !important;
  flex-shrink: 1 !important;
}

.provider-item {
  background: var(--el-fill-color-blank);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 2px solid var(--el-border-color-light);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
      var(--el-color-primary) 0%,
      var(--el-color-primary-light-3) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.1);
    border-color: var(--el-color-primary-light-5);

    &::before {
      opacity: 1;
    }
  }

  &.is-default {
    border-color: var(--el-color-success);
    background: linear-gradient(135deg,
      var(--el-color-success-light-9) 0%,
      var(--el-fill-color-blank) 100%);

    &::before {
      background: linear-gradient(90deg,
        var(--el-color-success) 0%,
        var(--el-color-success-light-3) 100%);
      opacity: 1;
    }

    .provider-info .provider-name {
      color: var(--el-color-success-dark-2);
    }
  }
}

.provider-content {
  padding: 20px;
}

.provider-header-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;

  .provider-info {
    flex: 1;
    min-width: 0;

    .provider-name {
      font-weight: 600;
      font-size: 16px;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;

      .default-tag {
        font-weight: 500;
      }
    }

    .provider-url {
      color: var(--el-text-color-secondary);
      font-size: 13px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      background: var(--el-fill-color);
      padding: 6px 10px;
      border-radius: 6px;
      word-break: break-all;
      border: 1px solid var(--el-border-color-lighter);
    }
  }

  .provider-actions {
    .action-buttons {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
    }
  }
}

.action-btn {
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid var(--el-border-color-light);

  &:hover {
    transform: translateY(-1px);
  }

  &.default-btn {
    color: var(--el-color-warning);
    border-color: var(--el-color-warning-light-7);

    &:hover {
      background: var(--el-color-warning-light-9);
      border-color: var(--el-color-warning-light-5);
    }
  }

  &.edit-btn {
    color: var(--el-color-primary);
    border-color: var(--el-color-primary-light-7);

    &:hover {
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-5);
    }
  }

  &.delete-btn {
    &:hover {
      transform: translateY(-1px);
    }
  }

  :deep(.el-icon) {
    font-size: 14px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 编辑对话框样式
.provider-edit-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    background: var(--el-bg-color);
  }

  :deep(.el-dialog__header) {
    padding: 0;
    margin: 0;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background: var(--el-bg-color);
  }

  :deep(.el-dialog__footer) {
    padding: 0;
    margin: 0;
  }
}

.edit-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--el-color-success);
  color: var(--el-color-white);
  border-bottom: 1px solid var(--el-border-color-lighter);

  .edit-header-content {
    display: flex;
    align-items: center;
    gap: 16px;

    .edit-header-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: var(--el-color-success-light-3);
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        font-size: 20px;
        color: var(--el-color-white);
      }
    }

    .edit-header-text {
      .edit-dialog-title {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-color-white);
      }

      .edit-dialog-subtitle {
        margin: 0;
        font-size: 13px;
        color: var(--el-color-white);
        opacity: 0.85;
      }
    }
  }

  .edit-close-btn {
    background: var(--el-color-success-light-3);
    border: none;
    color: var(--el-color-white);

    &:hover {
      background: var(--el-color-success-light-5);
      color: var(--el-color-white);
    }
  }
}

.provider-form {
  :deep(.el-form-item__label) {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: var(--el-box-shadow-lighter);
  }
}

.url-examples {
  margin-top: 12px;

  .examples-header {
    margin-bottom: 8px;

    .examples-label {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      font-weight: 500;
    }
  }

  .examples-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .example-tag {
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 6px;

      &:hover {
        transform: translateY(-1px);
        box-shadow: var(--el-box-shadow-light);
      }
    }
  }
}

.help-btn {
  background: var(--el-color-info-light-8);
  border-color: var(--el-color-info-light-6);
  color: var(--el-color-info);

  &:hover {
    background: var(--el-color-info-light-7);
    border-color: var(--el-color-info-light-5);
  }
}

.url-help-content {
  max-width: 380px;
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 12px;
  border: 1px solid var(--el-border-color-light);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

  h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: "💡";
      font-size: 18px;
    }
  }

  .help-section {
    margin-bottom: 16px;
    padding: 12px;
    background: var(--el-fill-color-extra-light);
    border-radius: 8px;
    border-left: 3px solid var(--el-color-primary);

    p {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: var(--el-text-color-regular);

      strong {
        color: var(--el-text-color-primary);
        font-weight: 600;
      }
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 6px;
        font-size: 13px;
        color: var(--el-text-color-secondary);
        line-height: 1.5;
      }
    }

    code {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
      font-size: 12px;
      font-weight: 500;
      border: 1px solid var(--el-color-primary-light-7);
    }

    &:last-child {
      margin-bottom: 0;
    }

    &:nth-child(2) {
      border-left-color: var(--el-color-success);
    }

    &:nth-child(3) {
      border-left-color: var(--el-color-warning);
    }
  }
}

.switch-tip {
  margin-left: 12px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.preview-container {
  .preview-url {
    background: var(--el-fill-color-blank);
    border: 1px solid var(--el-border-color-light);
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 13px;
    color: var(--el-text-color-regular);
    word-break: break-all;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    min-height: 20px;
  }
}

.edit-dialog-footer {
  padding: 16px 24px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 添加禁止选择文本的样式类
.no-select {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}
</style> 