<template>
  <div class="common-layout">
    <el-container>
      <app-header />
      <el-container class="main-container">
        <app-menu />
        <el-main class="main">
          <router-view v-slot="{ Component }">
            <keep-alive>
              <component :is="Component" v-if="$route.meta.keepAlive" />
            </keep-alive>
            <component :is="Component" v-if="!$route.meta.keepAlive" />
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import AppHeader from './AppHeader.vue'
import AppMenu from './AppMenu.vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确认退出登录吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await userStore.logout()
      router.push('/login')
    } catch (error) {
      // 用户取消操作
    }
  }
}
</script>

<style  lang="scss" scoped>
.common-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: var(--el-bg-color);
  position: relative;
}

/* 添加背景装饰 */
.common-layout::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 0% 0%, rgba(var(--el-color-primary-rgb), 0.1), transparent 40%),
    radial-gradient(circle at 100% 0%, rgba(var(--el-color-success-rgb), 0.1), transparent 40%),
    radial-gradient(circle at 100% 100%, rgba(var(--el-color-warning-rgb), 0.1), transparent 40%),
    radial-gradient(circle at 0% 100%, rgba(var(--el-color-danger-rgb), 0.1), transparent 40%);
  pointer-events: none;
  z-index: 0;
}

.main-container {
  height: calc(100vh - 60px);
  margin-top: 60px;
  display: flex;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.main {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  background: rgba(var(--el-bg-color-rgb), 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
  transition: all 0.3s ease;
  max-width: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-header) {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.dark :deep(.el-header) {
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

:deep(.el-aside) {
  height: 100%;
  overflow-y: auto;
  background: rgba(var(--el-bg-color-rgb), 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  box-shadow: 4px 0 8px rgba(0, 0, 0, 0.05);
}

.dark :deep(.el-aside) {
  background: rgba(0, 0, 0, 0.8);
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 4px 0 8px rgba(0, 0, 0, 0.2);
}

/* 自定义滚动条样式 */
.main::-webkit-scrollbar,
:deep(.el-aside::-webkit-scrollbar) {
  width: 6px;
}

.main::-webkit-scrollbar-thumb,
:deep(.el-aside::-webkit-scrollbar-thumb) {
  background: rgba(var(--el-text-color-primary-rgb), 0.2);
  border-radius: 3px;
}

.main::-webkit-scrollbar-track,
:deep(.el-aside::-webkit-scrollbar-track) {
  background: transparent;
}

/* 深色模式下的滚动条 */
.dark .main::-webkit-scrollbar-thumb,
.dark :deep(.el-aside::-webkit-scrollbar-thumb) {
  background: rgba(255, 255, 255, 0.2);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.user-dropdown {
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-primary);
  transition: color 0.3s ease;
}

.el-icon--right {
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.user-dropdown:hover .el-icon--right {
  transform: rotate(180deg);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main {
    padding: 15px;
  }
  
  :deep(.el-aside) {
    position: fixed;
    left: 0;
    z-index: 99;
  }
}
</style>