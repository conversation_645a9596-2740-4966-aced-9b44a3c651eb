import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import path from 'path'
import vueScriptMerger from './plugins/vue-script-merger'
import { execSync } from 'child_process'
import obfuscator from 'vite-plugin-javascript-obfuscator'
// add the following dependencies

import { AntDesignXVueResolver } from 'ant-design-x-vue/resolver';
// https://vite.dev/config/
export default defineConfig({
  base: './',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '~': path.resolve(__dirname, 'src'),
      'assets': path.resolve(__dirname, 'src/assets'),
      'components': path.resolve(__dirname, 'src/components'),
      'views': path.resolve(__dirname, 'src/views'),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },

  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver(),AntDesignXVueResolver()],
    }),
    vueScriptMerger({
      // 从多个位置查找脚本文件
      scriptPaths: [
        'scripts',        // 相对于src目录
      ],
      
      // 支持多种扩展名
      extensions: ['.script.js', '.vue.js', '.js'],
      
      // 路径别名(除了vite配置的别名外，可以添加额外的)
      aliases: {
        '@scripts': path.resolve(__dirname, 'src/scripts')
      },
      
      // 调试模式
      debug: true,
      
      // 自定义注入注释
      injectComment: '// 从 {filename} 自动导入',
      
      // 是否优先使用组件同目录下的脚本文件
      useSameDir: true
    }),
    // 暂时完全禁用混淆来测试应用功能
    // false && obfuscator({
    //   options: {
    //     compact: false,              // 关闭压缩，避免变量初始化问题
    //     controlFlowFlattening: false,
    //     deadCodeInjection: false,
    //     debugProtection: false,
    //     debugProtectionInterval: 0,
    //     disableConsoleOutput: false,
    //     identifierNamesGenerator: 'hexadecimal', // 使用最简单的标识符生成器
    //     log: false,
    //     numbersToExpressions: false,
    //     renameGlobals: false,        // 保持为 false，避免影响全局变量
    //     renameProperties: false,     // 不重命名属性，避免导入问题
    //     selfDefending: false,
    //     simplify: false,             // 降低混淆强度，减少内存使用
    //     splitStrings: false,
    //     stringArray: false,          // 关闭字符串数组，减少内存消耗
    //     stringArrayCallsTransform: false,
    //     stringArrayEncoding: ['none'],
    //     stringArrayIndexShift: false,
    //     stringArrayRotate: false,
    //     stringArrayShuffle: false,
    //     stringArrayWrappersCount: 1,
    //     stringArrayWrappersChainedCalls: false,
    //     stringArrayWrappersParametersMaxCount: 2,
    //     stringArrayWrappersType: 'variable',
    //     stringArrayThreshold: 0,     // 降低字符串数组阈值
    //     transformObjectKeys: false,  // 不转换对象键，避免导入问题
    //     unicodeEscapeSequence: false,
    //     reservedNames: [
    //       // pywebview 相关
    //       'pywebview',
    //       'pywebview.api',
    //       'pywebview.api.*',  // 保护所有 api 方法
    //       'window.pywebview',
    //       // Vue 相关
    //       'createApp',
    //       'defineComponent',
    //       'ref',
    //       'reactive',
    //       'onMounted',
    //       'computed',
    //       'watch',
    //       'nextTick',
    //       // 路由相关
    //       'createRouter',
    //       'createWebHashHistory',
    //       'useRouter',
    //       'useRoute',
    //       // Pinia 相关
    //       'createPinia',
    //       'defineStore',
    //       // Element Plus 相关
    //       'ElMessage',
    //       'ElMessageBox',
    //       // 其他重要的全局变量
    //       '__webpack_require__',
    //       'define',
    //       'require',
    //       'process',
    //       'global',
    //       'window',
    //       'document',
    //       // 添加路径相关
    //       '@',
    //       '@/',
    //       'path',
    //       'resolve',
    //       '__dirname',
    //       // 添加更多 Vue 相关
    //       'component',
    //       'defineAsyncComponent',
    //       'resolveComponent',
    //       'markRaw',
    //       'toRaw',
    //       'h',
    //       // 添加路由懒加载相关
    //       'import.meta',
    //       'import.meta.url',
    //       'import.meta.glob',
    //       'loadModule',
    //     ],
    //     domainLock: [],
    //     target: 'browser',
    //     sourceMap: false,
    //     seed: 0,
    //     reservedStrings: [],
    //   },
    //   exclude: [
    //     'node_modules/**',         // 排除所有第三方库，避免混淆导致的初始化问题
    //     'dist/**',
    //     '**/*.css',
    //     '**/*.scss',
    //     '**/*.sass',
    //     '**/*.less',
    //     '**/*.vue',
    //     '**/binding.js',
    //     '**/main.js',
    //     '**/stores/**',            // 排除stores，避免状态管理问题
    //     '**/router/**',            // 排除路由配置
    //     '**/utils/**',             // 排除工具函数
    //     '**/composables/**',       // 排除组合式函数
    //   ]
    // }),
    {
      name: 'integrity-manifest',
      closeBundle() {
        // 在构建完成后生成完整性清单并更新代码
        console.log('正在生成文件完整性代码...');
        try {
          // 下面是用来生成网页hash保证不能被修改，单纯根据文件字符
          execSync('python build_integrity_code.py --dir ./statics --output ./PVV.py', {
            stdio: 'inherit',
            cwd: path.resolve(__dirname, '../') // 回到项目根目录
          });
        } catch (error) {
          console.error('生成完整性代码失败:', error);
        }
      }
    }
  ],
  server: {
    port: 13000, // 指定启动端口为3000
    fs: {
      strict: false, // 允许访问项目根目录之外的文件
    }
  },

  build: {
    outDir: '../statics',
    minify: 'esbuild',  // 使用 esbuild 做基础压缩

    // 针对simple-mind-map的内存优化
    target: 'es2020',
    sourcemap: false,
    reportCompressedSize: false,
    
    rollupOptions: {
      input: {
        main: './index.html', // 指向入口文件
      },
      output: {
        // 保持文件名结构但添加内容哈希
        entryFileNames: 'assets/entry-[hash].js',
        chunkFileNames: ({name}) => {
          // 将中文名称转换为拼音或使用模块ID
          const safeChunkName = name
            ? name.replace(/[^\x00-\x7F]/g, '') // 移除非ASCII字符
              .replace(/[^a-zA-Z0-9_-]/g, '-')  // 其他非字母数字替换为连字符
              .replace(/^-+|-+$/g, '')          // 移除开头和结尾的连字符
              .replace(/-{2,}/g, '-')           // 连续的连字符替换为单个
              || 'chunk'                         // 如果为空则使用默认名
            : 'chunk';
          
          return `assets/${safeChunkName}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          // 安全的资源文件名处理
          let fileName = assetInfo.name || '';
          const safeName = fileName
            .replace(/[^\x00-\x7F]/g, '')
            .replace(/[^a-zA-Z0-9_.-]/g, '-')
            .replace(/^-+|-+$/g, '')
            .replace(/-{2,}/g, '-')
            || 'asset';
          
          const extType = fileName.split('.').pop();
          
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
            return `assets/images/${safeName}-[hash][extname]`;
          }
          if (/woff|woff2|eot|ttf|otf/i.test(extType)) {
            return `assets/fonts/${safeName}-[hash][extname]`;
          }
          if (/css/i.test(extType)) {
            return `assets/css/${safeName}-[hash][extname]`;
          }
          return `assets/${safeName}-[hash][extname]`;
        },
        // 暂时禁用手动代码分割，使用Vite默认策略
        // manualChunks: undefined
      }
    },
    chunkSizeWarningLimit: 8000,
  },
  publicPath: './',
  assetsDir: 'static',
})
// 打印具体路径以进行调试
console.log('src目录的绝对路径是：', path.resolve(__dirname, 'src'));