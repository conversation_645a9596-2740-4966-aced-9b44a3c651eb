<template>
  <div class="container">
    <div class="layout-container">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 卡片池区域 -->
        <div class="pools-section">
          <div class="pools-container">
            <div v-for="pool in pools"
                 :key="pool.type"
                 class="pool"
                 :data-type="pool.type">
              <div class="pool-header">
                <div class="pool-title">{{ pool.title }}</div>
                <div class="pool-count">{{ pool.cards.length }}张卡片</div>
              </div>
              <div class="pool-cards">
                <div v-for="card in pool.cards"
                     :key="card.title"
                     :ref="el => { if (el) poolCardRefs[`${pool.type}-${card.title}`] = el }"
                     class="pool-card"
                     :class="{ 
                       'hidden': isCardHidden(card, pool.type), 
                       'active': activePoolCards[`${pool.type}-${card.title}`],
                       'selectable': selectedGroupIndex !== -1
                     }"
                     @click="handleCardClick(card, pool.type)">
                  <div class="mini-card-title">{{ card.title }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 抽卡按钮区域 -->
        <div class="control-section">
          <h2>写作技巧抽卡</h2>
          <div class="control-buttons">
            <button class="draw-button" :disabled="!canDraw" @click="drawCardGroups">
              <span class="button-text">{{ cardGroups.length > 0 ? '🎲' : '🎲' }}</span>
            </button>
            <button class="toggle-face-button" @click="toggleDefaultCardFace">
              <span class="button-text">{{ defaultShowBack ? '显示正面' : '显示背面' }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 卡组选择区域 -->
        <div class="card-groups-section" v-if="cardGroups.length > 0">
          <div class="section-header">
            <h3>抽取的卡组</h3>
            <span class="group-count-badge">{{ cardGroups.length }} 组</span>
          </div>
          <div class="card-groups-container">
            <div v-for="(group, groupIndex) in cardGroups"
                 :key="groupIndex"
                 class="group-wrapper">
              <div class="group-info">
                <div class="group-title">
                  <span class="title-number">{{ groupIndex + 1 }}</span>
                  <span class="title-text">号卡组</span>
                  <span class="title-count">{{ group.cards.length }}张卡片</span>
                </div>
              </div>
              <div :ref="el => { if (el) groupRefs[groupIndex] = el }"
                   class="card-group"
                   :class="{ 
                     'active': selectedGroupIndex === groupIndex,
                     'hoverable': selectedGroupIndex === -1 || selectedGroupIndex === groupIndex
                   }"
                   @click="selectCardGroup(groupIndex)">
                <div class="group-content">
                  <div class="group-preview">
                    <div v-for="(card, cardIndex) in group.cards.slice(0, 3)"
                         :key="cardIndex"
                         class="preview-card"
                         :class="card.type"
                         :style="{ 
                           transform: `translateY(${cardIndex * -30}px)`,
                           zIndex: 3 - cardIndex
                         }">
                      <div class="preview-card-content">
                        <div class="card-type-icon">
                          <i :class="getCardTypeIcon(card.type)"></i>
                        </div>
                        <div class="card-title">{{ card.title }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="group-action">
                  <span class="action-text">{{ selectedGroupIndex === groupIndex ? '收起' : '查看' }}</span>
                  <i :class="selectedGroupIndex === groupIndex ? 'el-icon-arrow-up' : 'el-icon-arrow-right'"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 抽到的卡片展示区域 -->
        <div class="cards-display-section">
          <div class="draw-cards-container">
            <div v-for="(card, index) in currentDisplayCards"
                 :key="index"
                 :ref="el => { if (el) drawnCardRefs[card.title] = el }"
                 class="drawn-card"
                 :class="{
                   'drawn': card.isDrawn,
                   'returning': card.isReturning,
                   'manually-selected': card.isManuallySelected,
                   'show-front': showCardFront(card),
                   'show-back': !showCardFront(card)
                 }"
                 :data-type="card.type"
                 @mouseenter="handleCardHover(card, true)"
                 @mouseleave="handleCardHover(card, false)">
              <div class="card-inner" :class="{ 'show-back': !showCardFront(card) }">
                <!-- 卡片正面 -->
                <div class="card-front">
                  <div class="card-header">
                    <div class="card-icon">
                      <svg v-if="card.type === '主题'" class="theme-icon" viewBox="0 0 24 24" width="24" height="24">
                        <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
                              fill="none"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"/>
                      </svg>
                      <svg v-else-if="card.type === '卷级结构'" class="structure-icon" viewBox="0 0 24 24" width="24" height="24">
                        <rect x="3" y="3" width="18" height="18" rx="2" fill="none" stroke="currentColor" stroke-width="2"/>
                        <line x1="3" y1="9" x2="21" y2="9" stroke="currentColor" stroke-width="2"/>
                        <line x1="3" y1="15" x2="21" y2="15" stroke="currentColor" stroke-width="2"/>
                      </svg>
                      <svg v-else class="point-icon" viewBox="0 0 24 24" width="24" height="24">
                        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                        <circle cx="12" cy="12" r="3" fill="currentColor"/>
                      </svg>
                    </div>
                    <div class="card-title">{{ card.title }}</div>
                  </div>
                  <div class="card-content">
                    <div class="card-description" v-html="formatDescription(card.description)"></div>
                    <div class="card-examples">
                      <div v-for="(example, idx) in card.examples" :key="idx" class="example-item">
                        <span class="example-bullet">•</span>
                        <span class="example-text">{{ example }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 卡片背面 -->
                <div class="card-back">
                  <div class="card-back-title">{{ card.title }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 右下预留区域 -->
        <div class="inspiration-section">
          <!-- 预留给灵感记录的区域 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { cardData } from '@/config/plotConfig'

// State
const poolCardRefs = ref({})
const groupRefs = ref({})
const drawnCardRefs = ref({})
const selectedGroupIndex = ref(-1)
const defaultShowBack = ref(false)
const activePoolCards = ref({})
const cardGroups = ref([])

// Initialize pools
const pools = ref([
  {
    type: '主题',
    title: '主题',
    isDrawing: false,
    cards: cardData.主题.map(card => ({
      ...card,
      type: '主题',
      isDrawn: false,
      isFlipped: false
    }))
  },
  {
    type: '卷级结构',
    title: '卷级结构',
    isDrawing: false,
    cards: cardData.卷级结构.map(card => ({
      ...card,
      type: '卷级结构',
      isDrawn: false,
      isFlipped: false
    }))
  },
  {
    type: '关键点',
    title: '关键点',
    isDrawing: false,
    cards: cardData.关键点.map(card => ({
      ...card,
      type: '关键点',
      isDrawn: false,
      isFlipped: false
    }))
  }
])

// Computed
const canDraw = computed(() => {
  return pools.value.some(pool => pool.cards.some(card => !card.isDrawn))
})

const currentDisplayCards = computed(() => {
  if (selectedGroupIndex.value === -1) return []
  return cardGroups.value[selectedGroupIndex.value]?.cards || []
})

// Methods
const formatDescription = (description) => {
  return description.replace(/\n/g, '<br>')
}

const getStackCardStyle = (index, totalCards) => {
  const offset = 2
  const rotation = (index - (totalCards - 1) / 2) * 5
  return {
    transform: `rotate(${rotation}deg) translateY(${index * offset}px)`,
    zIndex: totalCards - index
  }
}

const getReturnStyle = (card) => {
  if (!card.isReturning || !poolCardRefs.value[`${card.type}-${card.title}`]) return {}
  const poolCard = poolCardRefs.value[`${card.type}-${card.title}`]
  const poolCardRect = poolCard.getBoundingClientRect()
  const drawnCard = drawnCardRefs.value[card.title]
  if (!drawnCard) return {}
  const drawnCardRect = drawnCard.getBoundingClientRect()
  
  return {
    transform: `
      translate(
        ${poolCardRect.left - drawnCardRect.left}px,
        ${poolCardRect.top - drawnCardRect.top}px
      )
      scale(0.2)
    `
  }
}

const returnCards = () => {
  const currentGroup = cardGroups.value[selectedGroupIndex.value]
  if (!currentGroup) return

  currentGroup.cards.forEach(card => {
    card.isReturning = true
    setTimeout(() => {
      const poolCard = pools.value.find(p => p.type === card.type)
      if (poolCard) {
        const cardInPool = poolCard.cards.find(c => c.title === card.title)
        if (cardInPool) {
          cardInPool.isDrawn = false
        }
      }
    }, 500)
  })

  setTimeout(() => {
    cardGroups.value.splice(selectedGroupIndex.value, 1)
    selectedGroupIndex.value = -1
  }, 600)
}

const drawCardGroups = async () => {
  if (!canDraw.value) return

  // Reset any existing selection
  selectedGroupIndex.value = -1
  
  // Create a new card group
  const newGroup = {
    cards: []
  }

  // Draw from each pool
  for (const pool of pools.value) {
    pool.isDrawing = true
    const availableCards = pool.cards.filter(card => !card.isDrawn)
    
    if (availableCards.length > 0) {
      // Randomly select a card
      const randomIndex = Math.floor(Math.random() * availableCards.length)
      const selectedCard = availableCards[randomIndex]
      
      // Mark the card as drawn
      const cardInPool = pool.cards.find(c => c.title === selectedCard.title)
      if (cardInPool) {
        cardInPool.isDrawn = true
      }
      
      // Add to the new group
      newGroup.cards.push({
        ...selectedCard,
        isDrawn: true,
        isFlipped: false,
        isManuallySelected: false
      })
    }
    
    // Simulate drawing animation
    await new Promise(resolve => setTimeout(resolve, 300))
    pool.isDrawing = false
  }
  
  // Add the new group to cardGroups
  cardGroups.value.push(newGroup)
  
  // Select the newly added group
  setTimeout(() => {
    selectedGroupIndex.value = cardGroups.value.length - 1
    scrollToDrawnCards()
  }, 100)
}

const handleCardClick = (card, poolType) => {
  const currentGroup = cardGroups.value[selectedGroupIndex.value]
  if (!currentGroup) return

  const existingCardIndex = currentGroup.cards.findIndex(c => 
    c.title === card.title && c.type === poolType
  )

  if (existingCardIndex !== -1) {
    // Remove card if it exists
    currentGroup.cards.splice(existingCardIndex, 1)
    activePoolCards.value[`${poolType}-${card.title}`] = false
    
    const poolCard = pools.value.find(p => p.type === poolType)?.cards.find(c => c.title === card.title)
    if (poolCard) {
      poolCard.isDrawn = false
    }
  } else {
    // Add card if it doesn't exist
    currentGroup.cards.push({
      ...card,
      isDrawn: true,
      isFlipped: false,
      isManuallySelected: true
    })
    activePoolCards.value[`${poolType}-${card.title}`] = true
    
    const poolCard = pools.value.find(p => p.type === poolType)?.cards.find(c => c.title === card.title)
    if (poolCard) {
      poolCard.isDrawn = true
    }
  }
}

const isCardHidden = (card, poolType) => {
  return card.isDrawn && !activePoolCards.value[`${poolType}-${card.title}`]
}

const isCardActive = (card, poolType) => {
  return activePoolCards.value[`${poolType}-${card.title}`]
}

const isCardDrawn = (card, poolType) => {
  return card.isDrawn
}

const showCardFront = (card) => {
  return card.isFlipped ? !defaultShowBack.value : defaultShowBack.value
}

const toggleCardFace = (card) => {
  card.isFlipped = !card.isFlipped
}

const toggleDefaultCardFace = () => {
  defaultShowBack.value = !defaultShowBack.value
  const currentGroup = cardGroups.value[selectedGroupIndex.value]
  if (currentGroup) {
    currentGroup.cards.forEach(card => card.isFlipped = false)
  }
}

const handleCardHover = (card, isHover) => {
  if (isHover) {
    startHoverAnimation(card)
  } else {
    stopHoverAnimation(card)
  }
}

const startHoverAnimation = (card) => {
  if (card.hoverTimeout) {
    clearTimeout(card.hoverTimeout)
  }
  card.hoverTimeout = setTimeout(() => {
    card.isHovering = true
  }, 100)
}

const stopHoverAnimation = (card) => {
  if (card.hoverTimeout) {
    clearTimeout(card.hoverTimeout)
  }
  card.hoverTimeout = setTimeout(() => {
    card.isHovering = false
  }, 100)
}

const selectCardGroup = (index) => {
  if (selectedGroupIndex.value === index) {
    // Deselect if clicking the same group
    selectedGroupIndex.value = -1
    resetManualSelection()
  } else {
    // Select the new group
    selectedGroupIndex.value = index
    resetManualSelection()
    
    // Update activePoolCards based on the selected group
    const selectedGroup = cardGroups.value[index]
    if (selectedGroup) {
      // Reset all active cards
      activePoolCards.value = {}
      
      // Mark cards in the selected group as active
      selectedGroup.cards.forEach(card => {
        activePoolCards.value[`${card.type}-${card.title}`] = true
      })
      
      // Scroll pool cards into view
      selectedGroup.cards.forEach(card => {
        scrollToActivePoolCard(card)
      })
    }
  }
}

const scrollToActivePoolCard = (card) => {
  const poolCardRef = poolCardRefs.value[`${card.type}-${card.title}`]
  if (poolCardRef) {
    const container = poolCardRef.closest('.pool-cards')
    if (container) {
      const cardRect = poolCardRef.getBoundingClientRect()
      const containerRect = container.getBoundingClientRect()
      
      if (cardRect.top < containerRect.top || cardRect.bottom > containerRect.bottom) {
        poolCardRef.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        })
      }
    }
  }
}

const resetManualSelection = () => {
  activePoolCards.value = {}
}

const getCardTypeColor = (type) => {
  switch (type) {
    case '主题': return '#4CAF50'
    case '卷级结构': return '#2196F3'
    case '关键点': return '#FF9800'
    default: return '#9E9E9E'
  }
}

const getCardTypeIcon = (type) => {
  switch (type) {
    case '主题': return 'el-icon-star-on'
    case '卷级结构': return 'el-icon-s-grid'
    case '关键点': return 'el-icon-edit'
    default: return 'el-icon-document'
  }
}

const scrollToDrawnCards = () => {
  setTimeout(() => {
    const container = document.querySelector('.cards-display-section')
    if (container) {
      const cardElements = container.querySelectorAll('.drawn-card')
      if (cardElements.length > 0) {
        const lastCard = cardElements[cardElements.length - 1]
        lastCard.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        })
      }
    }
  }, 100)
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, rgba(18, 18, 32, 0.95), rgba(27, 30, 54, 0.95));
  color: var(--el-text-color-primary);
}

.layout-container {
  display: flex;
  gap: 16px;
  height: 100%;
  overflow: hidden;
  padding: 16px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.left-section {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  overflow: hidden;
}

.pools-section {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 16px;
  max-height: calc(100vh - 200px);
  scroll-behavior: smooth;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.pools-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  padding-right: 4px;
}

.pools-container::-webkit-scrollbar {
  width: 4px;
}

.pools-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.pool {
  background: rgba(255, 255, 255, 0.07);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  scroll-margin: 100px;
  transition: all 0.3s ease;
}

.pool:hover {
  background: rgba(255, 255, 255, 0.09);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.pool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.pool-title {
  font-size: 0.9em;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
}

.pool-count {
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.6);
}

.pool-cards {
  max-height: 120px;
  overflow-y: auto;
  padding-right: 4px;
}

.pool-cards::-webkit-scrollbar {
  width: 4px;
}

.pool-cards::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.pool-card {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 4px;
  margin: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  scroll-margin: 100px;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.pool-card.active {
  background: rgba(64, 158, 255, 0.15);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  position: relative;
  z-index: 1;
}

.pool-card.selectable:hover {
  background: rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border-color: rgba(64, 158, 255, 0.3);
}

.mini-card-title {
  font-size: 0.85em;
  color: rgba(255, 255, 255, 0.8);
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  min-width: 0;
}

.cards-display-section {
  flex: 1;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.draw-cards-container {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  overflow-y: auto;
  padding: 4px;
}

.drawn-card {
  height: 320px;
  background: transparent;
  border-radius: 12px;
  padding: 16px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  cursor: pointer;
  perspective: 1000px;
}

.drawn-card.drawn,
.drawn-card.manually-selected {
  opacity: 1;
  transform: translateY(0);
}

.drawn-card.returning {
  opacity: 0;
  transform: translateY(-20px);
}

.drawn-card.show-back {
  transform: rotateY(180deg);
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.card-inner.show-back {
  transform: rotateY(180deg);
}

.card-front, .card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 12px;
  background: rgba(30, 32, 50, 0.9);
  padding: 16px;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.card-front {
  transform: rotateY(0deg);
  background: linear-gradient(135deg, rgba(30, 32, 50, 0.9), rgba(40, 44, 68, 0.9));
}

.card-back {
  transform: rotateY(180deg);
  background: linear-gradient(135deg, #4a6cf7, #6e8fff);
  color: white;
  justify-content: center;
  align-items: center;
  box-shadow: inset 0 0 60px rgba(255, 255, 255, 0.1);
}

.card-back-title {
  font-size: 2em;
  font-weight: bold;
  text-align: center;
  line-height: 1.4;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
}

[data-type="主题"] .card-back {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
  box-shadow: inset 0 0 60px rgba(255, 255, 255, 0.15);
}

[data-type="卷级结构"] .card-back {
  background: linear-gradient(135deg, #10b981, #34d399);
  box-shadow: inset 0 0 60px rgba(255, 255, 255, 0.15);
}

[data-type="关键点"] .card-back {
  background: linear-gradient(135deg, #6366f1, #818cf8);
  box-shadow: inset 0 0 60px rgba(255, 255, 255, 0.15);
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.drawn-card.drawn:not(.manually-selected) {
  animation: float 3s ease-in-out infinite;
}

@keyframes selectAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.05) translateY(-10px); }
  100% { transform: scale(1); }
}

@keyframes highlightPulse {
  0% { box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(64, 158, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(64, 158, 255, 0); }
}

.control-section {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.control-section h2 {
  margin: 0;
  font-size: 1.1em;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.control-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.draw-button,
.toggle-face-button {
  padding: 10px 24px;
  font-size: 1em;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #4a6cf7, #6e8fff);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(74, 108, 247, 0.3);
}

.draw-button:hover,
.toggle-face-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 108, 247, 0.4);
}

.draw-button:active,
.toggle-face-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(74, 108, 247, 0.2);
}

.draw-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(74, 108, 247, 0.15);
  border-radius: 8px;
  color: #4a6cf7;
  transition: all 0.3s ease;
}

.drawn-card:hover .card-icon {
  transform: scale(1.05);
  background: #4a6cf7;
  color: white;
  box-shadow: 0 0 15px rgba(74, 108, 247, 0.5);
}

.card-title {
  font-size: 1.1em;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
}

.card-content {
  height: calc(100% - 70px);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-description {
  flex: 1;
  font-size: 0.9em;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  position: relative;
  padding: 0 2px;
}

.card-examples {
  flex-shrink: 0;
  max-height: 80px;
  overflow: hidden;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.example-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 6px;
}

.example-bullet {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2em;
  line-height: 1;
}

.example-text {
  flex: 1;
  font-size: 0.85em;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-groups-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h3 {
  margin: 0;
  font-size: 1em;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.card-groups-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 4px;
}

.group-wrapper {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.group-info {
  padding: 0 4px;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.9);
}

.title-number {
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1;
  background: linear-gradient(135deg, #4a6cf7, #6e8fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title-text {
  font-size: 0.9em;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}

.title-count {
  margin-left: auto;
  font-size: 0.8em;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
}

.card-group {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 
    0 2px 10px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.card-group:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 20px rgba(0, 0, 0, 0.15),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  border-color: rgba(74, 108, 247, 0.3);
}

.card-group.active {
  background: rgba(74, 108, 247, 0.1);
  border-color: rgba(74, 108, 247, 0.4);
  box-shadow: 0 0 20px rgba(74, 108, 247, 0.2);
}

.group-content {
  padding: 12px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.group-preview {
  position: relative;
  width: 100%;
  height: 140px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 20px;
}

.preview-card {
  position: absolute;
  width: 140px;
  height: 90px;
  background: rgba(40, 44, 68, 0.9);
  border-radius: 6px;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.preview-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 
    0 8px 20px rgba(0, 0, 0, 0.25),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.preview-card-content {
  height: 100%;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-type-icon {
  text-align: right;
  font-size: 1em;
  opacity: 0.6;
  color: #4a6cf7;
}

.card-title {
  font-size: 0.9em;
  font-weight: 500;
  line-height: 1.3;
  color: rgba(255, 255, 255, 0.9);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.group-action {
  padding: 6px 10px;
  background: rgba(30, 32, 50, 0.8);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.action-text {
  font-size: 0.9em;
}

.group-count-badge {
  margin-left: auto;
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

/* 添加科技感光效 */
.container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(74, 108, 247, 0.03) 0%, transparent 80%);
  animation: rotate 30s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 添加网格线效果 */
.container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: -1;
}
</style>
