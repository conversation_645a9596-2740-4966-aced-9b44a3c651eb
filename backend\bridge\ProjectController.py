import os
import json
from .Base import ResponsePacket
class ProjectController(ResponsePacket):
    def __init__(self, base_dir):
        self.base_dir = base_dir
        if not os.path.exists(self.base_dir):
            os.makedirs(self.base_dir)  # 创建基础目录

    def create_file(self, filename):
        """ 创建一个新的文件 """
        file_path = os.path.join(self.base_dir, filename)
        if not os.path.exists(file_path):
            with open(file_path, 'w',encoding='utf-8') as f:
                pass  # 创建空文件
            return self._success_response(f"文件 '{filename}' 创建成功。")
        else:
            return self._error_response(f"文件 '{filename}' 已存在。")

    def delete_file(self, filename):
        """ 删除指定的文件 """
        file_path = os.path.join(self.base_dir, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return self._success_response(f"文件 '{filename}' 删除成功。")
        else:
            return self._error_response(f"文件 '{filename}' 不存在。")

    def read_file(self, filename):
        """ 读取指定文件的内容 """
        file_path = os.path.join(self.base_dir, filename)
        if os.path.exists(file_path):
            with open(file_path, 'r',encoding='utf-8') as f:
                content = f.readlines()  # 返回文件内容列表
            return self._success_response(f"文件 '{filename}' 读取成功。", [line.strip() for line in content])
        else:
            return self._error_response(f"文件 '{filename}' 不存在。")

    def write_to_file(self, filename, content, append=False):
        """ 向指定文件写入内容 """
        file_path = os.path.join(self.base_dir, filename)
        mode = 'a' if append else 'w'  # 选择写入模式
        with open(file_path, mode,encoding='utf-8') as f:
            f.write(content + '\n')
        return self._success_response(f"内容写入文件 '{filename}' 成功。")

    def list_files(self):
        """ 列出基础目录下的所有文件 """
        files = os.listdir(self.base_dir)
        return self._success_response("文件列表获取成功。", files)

    def create_directory(self, dir_name):
        """ 创建一个新的子目录 """
        dir_path = os.path.join(self.base_dir, dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            return self._success_response(f"目录 '{dir_name}' 创建成功。")
        else:
            return self._error_response(f"目录 '{dir_name}' 已存在。")

    def delete_directory(self, dir_name):
        """ 删除指定的子目录 """
        dir_path = os.path.join(self.base_dir, dir_name)
        if os.path.exists(dir_path):
            os.rmdir(dir_path)  # 仅删除空目录
            return self._success_response(f"目录 '{dir_name}' 删除成功。")
        else:
            return self._error_response(f"目录 '{dir_name}' 不存在。")

    def list_directories(self):
        """ 列出基础目录下的所有子目录 """
        directories = [d for d in os.listdir(self.base_dir) if os.path.isdir(os.path.join(self.base_dir, d))]
        return self._success_response("目录列表获取成功。", directories)

