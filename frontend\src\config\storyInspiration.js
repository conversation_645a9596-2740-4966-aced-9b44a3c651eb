// 剧情创作灵感系统配置
import { cardData } from './plotConfig'
import { plotElementSwapper} from './betterPlot'

export const storyInspirationConfig = {
  // 定义四个分类的配置信息
  categories: {
    theme: {
      name: "主题层",
      description: "故事的核心主题与情感基调",
      icon: "Sunrise",
      color: "primary",
      defaultCount: 2,
      maxCount: 5
    },
    volume: {
      name: "卷级结构",
      description: "故事的大纲架构与发展脉络",
      icon: "Connection",
      color: "success",
      defaultCount: 4,
      maxCount: 8
    },
    keyPoint: {
      name: "关键点",
      description: "故事中的重要转折与关键节点",
      icon: "Key",
      color: "warning",
      defaultCount: 5,
      maxCount: 8
    },
    technique: {
      name: "技法卡",
      description: "用于优化剧情的各种写作技巧",
      icon: "TrendCharts",
      color: "danger",
      defaultCount: 3,
      maxCount: 5
    }
  },

  // 导入元素数据（默认数据，可以被用户自定义覆盖）
  // 主题层
  themes: cardData.主题,

  // 卷级结构
  volumeStructures: cardData.卷级结构,

  // 关键点
  keyPoints: cardData.关键点,

  // 技法卡（新增）
  techniques: [
    {
      title: '伏笔埋设',
      description: '提前安排细节或线索，为后续情节做准备。',
      emotion: '↑',
      examples: [
        '《哈利波特》中的魂器暗示',
        '《无间道》开场警校训练',
        '《白夜行》中的雪穗过去'
      ]
    },
    {
      title: '叙事视角转换',
      description: '通过改变叙事视角，展现不同角色对同一事件的感受。',
      emotion: '↓ | ↑',
      examples: [
        '《冰与火之歌》多视角叙事',
        '《罗生门》多角度讲述同一事件',
        '《无罪之最》警察与嫌犯视角交替'
      ]
    },
    {
      title: '环形结构',
      description: '故事结尾呼应开头，形成首尾相连的结构。',
      emotion: '↑',
      examples: [
        '《百年孤独》家族历史循环',
        '《盗梦空间》梦境与现实的交织',
        '《花千骨》生死轮回'
      ]
    },
    {
      title: '反常规节奏',
      description: '打破传统故事节奏，制造意外效果。',
      emotion: '↓ | ↑',
      examples: [
        '《权力的游戏》主角突然死亡',
        '《记忆碎片》倒叙剧情',
        '《搏击俱乐部》视角欺骗'
      ]
    },
    {
      title: '情感对比',
      description: '通过场景或情节的强烈对比，突出情感冲击。',
      emotion: '↓ | ↑',
      examples: [
        '《肖申克的救赎》黑暗中的希望',
        '《三体》宏大与渺小对比',
        '《楚门的世界》表面幸福与真相残酷'
      ]
    },
    {
      title: '象征隐喻',
      description: '用具象事物表达抽象概念，丰富故事内涵。',
      emotion: '↑',
      examples: [
        '《老人与海》鱼象征追求',
        '《三体》水滴象征科技差距',
        '《红楼梦》大观园象征封建社会'
      ]
    },
    {
      title: '时间操控',
      description: '通过处理时间线，增强叙事效果。',
      emotion: '↓ | ↑',
      examples: [
        '《云图》六段故事跨越时空',
        '《盗梦空间》梦境时间流速',
        '《穿越时空的少女》时间回溯'
      ]
    },
    {
      title: '反预期结局',
      description: '设计出人意料但合乎逻辑的结局，制造惊喜。',
      emotion: '↓ | ↑',
      examples: [
        '《致命ID》真实身份揭露',
        '《搏击俱乐部》主角双重人格',
        '《禁闭岛》患者与医生身份反转'
      ]
    }
  ],

  // 故事原型组合系统
  archetypeCombination: {
    // 启发式问题
    inspirationQuestions: {
      theme: [
        '这个主题在修真世界中如何体现？',
        '主题与人物成长有什么联系？',
        '如何通过具体情节表达主题？',
        '主题对故事走向有什么影响？'
      ],
      volume: [
        '这个结构适合放在故事的哪个阶段？',
        '如何让结构更符合修真小说特点？',
        '结构内部应该包含哪些要素？',
        '如何让结构更吸引读者？'
      ],
      keyPoint: [
        '这个关键点如何推动故事发展？',
        '关键点与人物性格是否匹配？',
        '如何让关键点更出人意料？',
        '关键点会带来什么样的后果？'
      ],
      technique: [
        '如何巧妙应用这个技法？',
        '这个技法能解决什么剧情问题？',
        '如何让技法使用不显刻意？',
        '这个技法能给读者带来什么体验？'
      ]
    }
  },

  // 自己思考创建，通过plotElementSwapper中的各种系统的元素的替换，来寻找剧情灵感
  brainStorm: {
    // 剧情元素替换系统，这个是当无论从哪个剧情创建开始，都会用到用来优化剧情尝试好的感觉的。
    plotElementSwapper: plotElementSwapper,
    description: '通过思维旋转来寻找剧情灵感',
  },
}
