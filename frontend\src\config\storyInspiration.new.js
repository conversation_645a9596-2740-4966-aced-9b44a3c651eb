// 剧情创作灵感系统配置
import { cardData } from './plotConfig'

export const storyInspirationConfig = {
  // 主题层 - 直接使用 pilotConfig 的数据
  themes: cardData.主题,

  // 卷级结构
  volumeStructures: cardData.卷级结构,

  // 关键点
  keyPoints: cardData.关键点,

  // 场景驱动
  sceneBasedPlot: {
    sceneTypes: [
      {
        type: '机缘之地',
        examples: ['上古遗迹', '秘境洞天', '远古战场', '天材地宝之地'],
        plotPoints: [
          '意外发现',
          '争夺机缘',
          '险境求生',
          '获得传承'
        ],
        questions: [
          '为什么主角能得到这个机缘？',
          '获得机缘的过程中会遇到什么阻碍？',
          '这个机缘会带来什么改变？'
        ]
      },
      {
        type: '危机之地',
        examples: ['死亡禁地', '战场前线', '敌对势力地盘', '险恶之境'],
        plotPoints: [
          '陷入危机',
          '寻求生机',
          '转机出现',
          '绝地反击'
        ],
        questions: [
          '主角为什么会来到这里？',
          '如何在危机中求生？',
          '这次经历会带来什么成长？'
        ]
      },
      {
        type: '权力之地',
        examples: ['宗门大殿', '皇宫朝堂', '商会总部', '势力据点'],
        plotPoints: [
          '权力争斗',
          '利益冲突',
          '势力较量',
          '地位变化'
        ],
        questions: [
          '各方势力的诉求是什么？',
          '如何在争斗中取得优势？',
          '权力的获得会带来什么改变？'
        ]
      }
    ]
  },

  // 角色驱动
  characterDrivenPlot: {
    motivations: [
      {
        type: '追求实力',
        drives: ['变强的渴望', '证明自己', '保护他人', '超越极限'],
        questions: [
          '为什么要追求力量？',
          '愿意付出什么代价？',
          '得到力量后会如何使用？'
        ]
      },
      {
        type: '寻求真相',
        drives: ['身世之谜', '阴谋真相', '历史隐秘', '世界真相'],
        questions: [
          '为什么执着于这个真相？',
          '揭开真相可能带来什么后果？',
          '如何面对真相带来的冲击？'
        ]
      },
      {
        type: '守护信念',
        drives: ['坚守道心', '守护亲人', '捍卫正义', '完成使命'],
        questions: [
          '这个信念源自何处？',
          '面对诱惑如何坚持？',
          '信念动摇时如何自处？'
        ]
      }
    ],
    relationships: [
      {
        type: '师徒',
        dynamics: ['传承', '考验', '背叛', '和解'],
        plotPoints: [
          '考验与成长',
          '信任与背叛',
          '超越与认可'
        ]
      },
      {
        type: '对手',
        dynamics: ['竞争', '欣赏', '战斗', '理解'],
        plotPoints: [
          '初次交锋',
          '实力差距',
          '巅峰对决'
        ]
      },
      {
        type: '亲情',
        dynamics: ['守护', '期待', '责任', '牺牲'],
        plotPoints: [
          '血脉羁绊',
          '责任与担当',
          '亲情考验'
        ]
      }
    ]
  },

  // 灵感触发器
  inspirationTriggers: {
    // 剧情转折触发
    plotTwists: [
      {
        type: '身份转变',
        examples: [
          '隐藏身份暴露',
          '意外认主',
          '血脉觉醒',
          '重要传承者'
        ]
      },
      {
        type: '局势变化',
        examples: [
          '暗藏阴谋显露',
          '势力格局改变',
          '盟友突然反目',
          '意外机缘出现'
        ]
      },
      {
        type: '情感转折',
        examples: [
          '信任崩塌',
          '误会解开',
          '情感觉醒',
          '关系破裂'
        ]
      }
    ],
    
    // 视角转换
    perspectiveShifts: {
      viewpoints: [
        '主角视角',
        '对手视角',
        '旁观者视角',
        '局外人视角'
      ],
      questions: [
        '从这个视角看，事情有什么不同？',
        '这个视角能带来什么新发现？',
        '如何利用视角差异制造悬念？'
      ]
    },

    // 情节连接器
    plotConnectors: {
      transitions: [
        '意外发现',
        '阴谋爆发',
        '危机降临',
        '机缘出现',
        '身份暴露',
        '关系转变'
      ],
      questions: [
        '如何让转折更自然？',
        '这个转折会带来什么影响？',
        '如何为下一个情节做铺垫？'
      ]
    },

    // 矛盾冲突生成器
    conflictGenerator: {
      types: [
        {
          name: '内心冲突',
          examples: ['信念动摇', '情感抉择', '立场矛盾']
        },
        {
          name: '人际冲突',
          examples: ['利益之争', '情感纠葛', '信任危机']
        },
        {
          name: '势力冲突',
          examples: ['宗门之争', '势力争霸', '阵营对立']
        }
      ],
      questions: [
        '冲突的根源是什么？',
        '如何让冲突升级？',
        '冲突会如何影响人物关系？'
      ]
    }
  },

  // 问题引导系统
  guidingQuestions: {
    plotDevelopment: [
      '这个情节会如何影响主角？',
      '会带来什么新的冲突？',
      '如何为后续发展埋下伏笔？'
    ],
    characterGrowth: [
      '主角从中学到了什么？',
      '性格会有什么改变？',
      '如何展现内心成长？'
    ],
    worldBuilding: [
      '这个情节如何展现世界观？',
      '会揭示什么隐藏的设定？',
      '如何让世界更有深度？'
    ],
    emotionalImpact: [
      '如何引起读者共鸣？',
      '情感铺垫是否充分？',
      '高潮转折够不够震撼？'
    ]
  }
}
