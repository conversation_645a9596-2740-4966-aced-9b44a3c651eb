import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  const user = ref(null)
  const isLoggedIn = ref(false)
  const error = ref('')
  const expiresAt = ref(0)
  const daysRemaining = ref(0)
  const hoursRemaining = ref(0)
  const minutesRemaining = ref(0)
  const timeRemaining = ref(0) // 剩余秒数
  const machineCode = ref('')
  const rememberLogin = ref(false)
  const lastChecked = ref(0)
  const timeSource = ref('')
  // 添加自动登录状态标志，防止重复自动登录
  const isAutoLoginInProgress = ref(false)
  
  // 计算属性：格式化的剩余时间
  const formattedTimeRemaining = computed(() => {
    // 添加更多防御性检查
    const days = daysRemaining.value || 0;
    const hours = hoursRemaining.value || 0;
    const minutes = minutesRemaining.value || 0;
    
    // 格式化时间，增加更多可读性
    if (days > 365) {
      const years = Math.floor(days / 365);
      const remainingDays = days % 365;
      return remainingDays > 0 ? `${years}年${remainingDays}天` : `${years}年`;
    } else if (days > 30) {
      const months = Math.floor(days / 30);
      const remainingDays = days % 30;
      return remainingDays > 0 ? `${months}个月${remainingDays}天` : `${months}个月`;
    } else if (days > 0) {
      return hours > 0 ? `${days}天${hours}小时` : `${days}天`;
    } else if (hours > 0) {
      return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
    } else if (minutes > 0) {
      return `${minutes}分钟`;
    } else if (timeRemaining.value > 0) {
      return `即将到期`;
    } else {
      return '已过期';
    }
  })
  
  // 计算属性：是否即将过期（少于3天）
  const isExpiringSoon = computed(() => {
    return daysRemaining.value >= 0 && daysRemaining.value < 3
  })
  
  // 计算属性：过期日期的格式化显示
  const formattedExpiryDate = computed(() => {
    if (!expiresAt.value) return ''
    return new Date(expiresAt.value * 1000).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  })
  
  // 计算属性：时间来源的友好显示
  const timeSourceName = computed(() => {
    // 默认显示
    if (!timeSource.value) return '未知时间源';
    
    // 映射时间源到友好名称
    const sourceNames = {
      'ntp': 'NTP服务器',
      'https': 'HTTPS时间服务',
      'api': '时间API',
      'local': '本地时间'
    };
    
    return sourceNames[timeSource.value] || timeSource.value;
  })
  
  // 从后端加载用户设置
  const loadUserSettings = async () => {
    try {
      const result = await window.pywebview.api.get_user_settings()
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response.status === 'success') {
        const settings = response.data
        
        // 更新本地状态
        machineCode.value = settings.machine_code || ''
        rememberLogin.value = settings.auto_login || false
        
        return {
          machineCode: settings.machine_code || '',
          activationCode: settings.activation_code || '',
          autoLogin: settings.auto_login || false
        }
      } else {
        console.error('加载用户设置失败:', response.message)
        return {}
      }
    } catch (e) {
      console.error('加载用户设置失败:', e)
      return {}
    }
  }
  
  // 保存用户设置到后端 - 优化防止不必要的保存
  const saveUserSettings = async ({ activationCode = null, autoLogin = null }) => {
    try {
      // 准备要保存的设置
      const settings = {}
      
      // 只存储提供的参数
      if (machineCode.value) {
        settings.machine_code = machineCode.value
      }
      
      // 只有当提供了激活码且不为空, 或明确要求清除 (传入空字符串) 时才保存
      if (activationCode !== null) {
        settings.activation_code = activationCode
      }
      
      // 只有当明确提供了 autoLogin 值时才保存
      if (autoLogin !== null) {
        settings.auto_login = autoLogin
        rememberLogin.value = autoLogin
      }
      
      // 只有当有要保存的设置时才调用API
      if (Object.keys(settings).length > 0) {
        console.log('保存用户设置:', settings)
        const result = await window.pywebview.api.save_user_settings(settings)
        const response = typeof result === 'string' ? JSON.parse(result) : result
        
        if (response.status !== 'success') {
          console.error('保存用户设置失败:', response.message)
        }
      }
    } catch (e) {
      console.error('保存用户设置失败:', e)
    }
  }
  
  // 修改前端获取机器码方法
  const getMachineCode = async (forceRefresh = true) => {
    try {
      // 如果不强制刷新且已有机器码，直接返回
      if (!forceRefresh && machineCode.value) {
        return machineCode.value
      }
      
      // 检查网络连接
      if (!navigator.onLine) {
        error.value = '网络连接不可用，无法获取机器码'
        return null
      }
      
      // 从后端获取最新的机器码
      const result = await window.pywebview.api.get_machine_code()
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response.status === 'success') {
        machineCode.value = response.data.machine_code
        return machineCode.value
      } else {
        error.value = `获取机器码失败: ${response.message}`
        return null
      }
    } catch (e) {
      console.error('获取机器码出错:', e)
      
      // 增强错误处理
      if (e.message && e.message.includes('NetworkError')) {
        error.value = '网络连接错误，请检查网络后重试'
      } else if (e.message && e.message.includes('Timeout')) {
        error.value = '获取机器码超时，请稍后重试'
      } else if (e.message && e.message.includes('pywebview')) {
        error.value = '与后端通信失败，请重启应用'
      } else {
        error.value = e.message || '获取机器码出错'
      }
      
      // 尝试使用缓存的机器码（如果存在）
      if (machineCode.value) {
        console.log('使用缓存的机器码:', machineCode.value)
        return machineCode.value
      }
      
      return null
    }
  }
  
  // 添加激活检查锁，防止并发调用
  const isCheckingActivation = ref(false)

  // 检查激活状态方法
  const checkActivation = async (forceExit = true, retryCount = 0) => {
    // 防止并发调用
    if (isCheckingActivation.value) {
      console.log('激活检查已在进行中，跳过此次调用')
      return { valid: true, message: '检查中...' }
    }

    isCheckingActivation.value = true

    try {
      const result = await window.pywebview.api.check_activation_status()
      const response = typeof result === 'string' ? JSON.parse(result) : result

      console.log('检查激活状态结果:', response)

      if (response.status === 'success') {
        // 更新状态信息
        const data = response.data
        expiresAt.value = data.expires_at
        daysRemaining.value = data.days_remaining
        hoursRemaining.value = data.hours_remaining
        minutesRemaining.value = data.minutes_remaining
        timeRemaining.value = data.time_remaining
        lastChecked.value = Date.now()
        
        // 如果有时间源信息，也更新
        if (data.time_source) {
          timeSource.value = data.time_source
        }
        
        return { valid: true, message: response.message }
      } else {
        // 激活码无效
        error.value = response.message || '激活状态检查失败'
        
        // 检查是否需要退出应用
        if (forceExit || (response.data && response.data.require_exit)) {
          console.error('激活无效，应用程序将关闭:', response.message)
          
          // 显示错误提示
          ElMessage.error({
            message: `激活已过期或无效: ${response.message}，应用程序将关闭`,
            duration: 5000
          })
          
          // 延迟关闭应用，给用户时间看消息
          setTimeout(async () => {
            try {
              await window.pywebview.api.close_application(`激活无效: ${response.message}`)
            } catch (closeErr) {
              console.error('关闭应用失败:', closeErr)
            }
          }, 5000)
        }
        
        return { valid: false, message: response.message }
      }
    } catch (e) {
      console.error('检查激活状态出错:', e)
      
      // 添加更详细的错误信息
      if (e.message && e.message.includes('NetworkError')) {
        error.value = '网络连接错误，请检查您的网络连接并重试'
      } else if (e.message && e.message.includes('Timeout')) {
        error.value = '网络请求超时，服务器可能暂时无法访问'
      } else {
        error.value = e.message || '检查激活状态过程出错'
      }
      
      // 添加重试机制
      if (retryCount < 2 && navigator.onLine) {
        console.log(`尝试重新检查激活状态，重试次数: ${retryCount + 1}`)
        return new Promise(resolve => {
          setTimeout(() => {
            resolve(checkActivation(forceExit, retryCount + 1))
          }, 1000) // 1秒后重试
        })
      }
      
      return { valid: false, message: error.value }
    } finally {
      // 确保释放锁
      isCheckingActivation.value = false
    }
  }
  
  // 优化自动登录逻辑
  const autoLogin = async () => {
    // 防止重复自动登录，并检查是否有其他登录正在进行
    if (isAutoLoginInProgress.value || isLoggedIn.value) {
      console.log('自动登录已在进行中或用户已登录，跳过')
      return isLoggedIn.value
    }

    isAutoLoginInProgress.value = true

    try {
      const settings = await loadUserSettings()

      // 只有当启用自动登录且有激活码时才尝试
      if (settings.autoLogin && settings.activationCode) {
        console.log('尝试自动登录...')

        // 检查网络连接状态
        if (!navigator.onLine) {
          console.error('自动登录失败: 网络连接不可用')
          return false
        }

        // 先获取机器码，增加重试机制
        let machineCodeResult = null
        for (let i = 0; i < 3; i++) {
          machineCodeResult = await getMachineCode(true) // 强制刷新
          if (machineCodeResult) break

          if (i < 2) {
            console.log(`获取机器码重试 ${i + 1}/3...`)
            await new Promise(resolve => setTimeout(resolve, 2000))
          }
        }

        if (!machineCodeResult) {
          console.error('自动登录失败: 无法获取机器码')
          return false
        }

        // 增加网络时间检查，但不阻塞登录
        try {
          const timeStatus = await checkNetworkTimeStatus()
          if (timeStatus.hasError) {
            console.warn('网络时间同步异常，但继续尝试登录:', timeStatus.message)
          }
        } catch (e) {
          console.warn('网络时间检查失败，但继续尝试登录:', e.message)
        }

        // 直接使用登录方法，增加更多重试次数
        let retryCount = 0
        let loginSuccess = false
        const maxRetries = 5 // 增加重试次数

        while (retryCount < maxRetries && !loginSuccess) {
          if (retryCount > 0) {
            console.log(`自动登录重试 ${retryCount}/${maxRetries - 1}...`)
            // 递增等待时间：1秒、2秒、3秒、4秒
            const waitTime = Math.min(retryCount * 1000, 4000)
            await new Promise(resolve => setTimeout(resolve, waitTime))
          }

          try {
            loginSuccess = await login(
              {
                activation_code: settings.activationCode
              },
              true // 保持记住登录状态
            )

            if (loginSuccess) {
              console.log(`自动登录成功（尝试次数: ${retryCount + 1}）`)
              break
            }
          } catch (loginError) {
            console.warn(`登录尝试 ${retryCount + 1} 失败:`, loginError.message)
          }

          retryCount++
        }

        if (!loginSuccess) {
          console.error(`自动登录失败，已重试 ${retryCount} 次`)

          // 根据错误类型决定是否清除激活码
          const errorMsg = error.value || ''
          if (errorMsg.includes('无效') || errorMsg.includes('过期') || errorMsg.includes('不匹配')) {
            console.log('清除无效的激活码设置')
            await saveUserSettings({
              activationCode: '',
              autoLogin: false
            })
            rememberLogin.value = false
          } else if (errorMsg.includes('网络') || errorMsg.includes('连接') || errorMsg.includes('超时')) {
            console.log('网络问题导致的登录失败，保留激活码设置')
          }
        }

        return loginSuccess
      } else {
        console.log('未启用自动登录或缺少必要信息')
      }

      return false
    } catch (e) {
      console.error('自动登录过程出错:', e)
      if (e.message && e.message.includes('NetworkError')) {
        error.value = '网络连接错误，请检查您的网络连接并重试'
      } else if (e.message && e.message.includes('Timeout')) {
        error.value = '请求超时，请检查网络连接'
      } else {
        error.value = e.message || '自动登录过程出错'
      }
      return false
    } finally {
      isAutoLoginInProgress.value = false
    }
  }
  
  // 登录方法修改 - 增加更好的错误处理和重试机制
  const login = async (profile, rememberMe = false) => {
    try {
      error.value = ''

      // 如果是手动登录，阻止自动登录
      if (!isAutoLoginInProgress.value) {
        // 这是手动登录，设置标志阻止自动登录
        console.log('手动登录开始，阻止自动登录')
      }

      // 检查网络连接状态
      if (!navigator.onLine) {
        error.value = '网络连接不可用，请检查您的网络设置后重试'
        return false
      }

      // 强制获取最新机器码，增加重试
      let machineCodeResult = null
      for (let i = 0; i < 2; i++) {
        machineCodeResult = await getMachineCode(true)
        if (machineCodeResult) break

        if (i < 1) {
          console.log('获取机器码失败，1秒后重试...')
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      if (!machineCodeResult) {
        error.value = '无法获取机器码，请检查网络连接'
        return false
      }

      // 添加机器码到请求
      const loginData = {
        ...profile,
        machine_code: machineCode.value, // 确保使用最新机器码
        remember_login: rememberMe // 添加记住登录参数
      }

      // 登录请求仅包含必要信息
      const result = await window.pywebview.api.login(loginData)
      const response = typeof result === 'string' ? JSON.parse(result) : result
      console.log('登录响应:', response)
      
      // 处理响应
      if (response.status === 'success') {
        // 更新状态
        user.value = {
          isActive: true,
          expiresAt: response.data.expires_at,
          expiresAtFormatted: response.data.expires_at_formatted,
          daysRemaining: response.data.days_remaining,
          machineCode: response.data.system_machine_code // 使用后端返回的机器码
        }
        
        isLoggedIn.value = true
        expiresAt.value = response.data.expires_at
        machineCode.value = response.data.system_machine_code
        rememberLogin.value = rememberMe
        

        
        // 保存用户设置
        await saveUserSettings({
          activationCode: rememberMe ? profile.activation_code : '',
          autoLogin: rememberMe
        })
        
        return true
      } else {
        // 根据错误类型提供更详细的错误信息
        const errorMsg = response.message || '登录失败'

        if (errorMsg.includes('网络时间')) {
          error.value = '无法获取网络时间，请检查网络连接后重试'
        } else if (errorMsg.includes('激活码') && errorMsg.includes('无效')) {
          error.value = '激活码无效或格式错误，请检查激活码是否正确'
        } else if (errorMsg.includes('激活码') && errorMsg.includes('过期')) {
          error.value = '激活码已过期，请联系客服获取新的激活码'
        } else if (errorMsg.includes('机器') && errorMsg.includes('不匹配')) {
          error.value = '激活码与当前设备不匹配，请使用正确的激活码'
        } else if (errorMsg.includes('网络连接')) {
          error.value = '网络连接异常，请检查网络设置后重试'
        } else {
          error.value = errorMsg
        }

        console.error('登录失败:', errorMsg)
        return false
      }
    } catch (e) {
      // 增强错误信息处理
      console.error('登录异常:', e)
      
      if (e.message && e.message.includes('NetworkError')) {
        error.value = '网络连接错误，请检查您的网络连接并重试'
      } else if (e.message && e.message.includes('Timeout')) {
        error.value = '登录请求超时，服务器可能暂时无法访问'
      } else if (e.message && e.message.toLowerCase().includes('invalid')) {
        error.value = '激活码无效或已过期'
      } else {
        error.value = e.message || '登录过程出错，请稍后重试'
      }
      
      return false
    }
  }
  
  // 登出方法
  const logout = async (clearSettings = true) => {
    try {
      // 调用后端登出方法
      const result = await window.pywebview.api.logout()
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      // 重置所有状态
      user.value = null
      isLoggedIn.value = false
      expiresAt.value = 0
      daysRemaining.value = 0
      hoursRemaining.value = 0
      minutesRemaining.value = 0
      timeRemaining.value = 0
      error.value = ''
      
      // 如果需要清除设置
      if (clearSettings) {
        await saveUserSettings({ 
          activationCode: '', 
          autoLogin: false 
        })
        rememberLogin.value = false
      }
      
      // 检查响应状态
      if (response.status !== 'success') {
        console.warn('登出API调用可能不完全成功:', response.message)
      }
      
      console.log('已登出系统')
      return true
    } catch (e) {
      console.error('登出失败:', e)
      error.value = e.message || '登出过程出错'
      
      // 即使API调用失败，也重置前端状态
      user.value = null
      isLoggedIn.value = false
      
      return false
    }
  }
  
  // 定时器引用
  const activationTimer = ref(null)

  // 启动定时检查
  const startActivationTimer = () => {
    // 如果已有定时器，先清理
    if (activationTimer.value) {
      clearInterval(activationTimer.value)
      activationTimer.value = null
    }

    // 每隔30分钟检查一次激活状态
    activationTimer.value = setInterval(async () => {
      if (isLoggedIn.value && !isCheckingActivation.value) {
        console.log('定时检查激活状态...')
        await checkActivation(true)
      }
    }, 30 * 60 * 1000) // 30分钟

    // 返回清理函数
    return () => {
      if (activationTimer.value) {
        clearInterval(activationTimer.value)
        activationTimer.value = null
      }
    }
  }
  
  // 添加新方法
  const setUserState = async (state) => {
    // 更新状态
    if (state.isLoggedIn !== undefined) isLoggedIn.value = state.isLoggedIn
    if (state.user) user.value = state.user
    if (state.expiresAt) expiresAt.value = state.expiresAt
    if (state.daysRemaining) daysRemaining.value = state.daysRemaining
    if (state.lastChecked) lastChecked.value = state.lastChecked || Date.now()
    
    // 更新机器码
    if (state.user && state.user.machine_code) {
      machineCode.value = state.user.machine_code
    }
    
    console.log('用户状态已更新:', { 
      isLoggedIn: isLoggedIn.value,
      expiresAt: expiresAt.value,
      daysRemaining: daysRemaining.value
    })
    
    return true
  }
  
  // 添加获取网络时间信息的方法
  const getNetworkTimeInfo = async () => {
    try {
      // 移除调试断点
      const result = await window.pywebview.api.get_network_time_info()
      console.log('原始网络时间响应:', result)
      
      // 确保我们处理的是对象，而不是字符串
      const response = typeof result === 'string' ? JSON.parse(result) : result
      console.log('解析后的网络时间信息:', response)
      
      if (response.status === 'success') {
        // 更新时间相关状态
        if (response.data && response.data.time_source) {
          timeSource.value = response.data.time_source
          timeSourceName.value = response.data.time_source_name || '未知时间源'
        }
        return response
      } else {
        console.warn('获取网络时间失败:', response.message)
        return response
      }
    } catch (e) {
      console.error('获取网络时间过程出错:', e)
      return {
        status: 'error',
        message: e.message || '网络连接错误',
        data: null
      }
    }
  }
  
  // 检查网络时间状态
  const checkNetworkTimeStatus = async () => {
    try {
      // 移除调试断点
      const result = await getNetworkTimeInfo()
      return {
        hasError: result.status !== 'success',
        message: result.status === 'success' ? '网络时间同步正常' : (result.message || '无法获取网络时间')
      }
    } catch (e) {
      return {
        hasError: true,
        message: '网络时间检查失败，请确保网络连接正常'
      }
    }
  }
  
  // 添加网络连接监听
  const setupNetworkListeners = () => {
    // 定义网络状态变化处理函数
    const handleNetworkChange = async () => {
      const online = navigator.onLine
      console.log(`网络连接状态变化: ${online ? '在线' : '离线'}`)
      
      // 如果重新联网，尝试更新激活状态
      if (online && isLoggedIn.value) {
        console.log('网络已恢复，正在更新激活状态...')
        try {
          await checkActivation(false) // 不强制退出
        } catch (e) {
          console.warn('恢复网络后更新激活状态失败:', e)
        }
      }
    }
    
    // 添加网络状态监听
    window.addEventListener('online', handleNetworkChange)
    window.addEventListener('offline', handleNetworkChange)
    
    // 返回清理函数
    return () => {
      window.removeEventListener('online', handleNetworkChange)
      window.removeEventListener('offline', handleNetworkChange)
    }
  }
  
  return {
    user,
    isLoggedIn,
    error,
    expiresAt,
    daysRemaining,
    hoursRemaining,
    minutesRemaining,
    formattedTimeRemaining,
    formattedExpiryDate,
    isExpiringSoon,
    machineCode,
    rememberLogin,
    isAutoLoginInProgress,
    isCheckingActivation,
    login,
    logout,
    checkActivation,
    getMachineCode,
    autoLogin,
    loadUserSettings,
    saveUserSettings,
    startActivationTimer,
    lastChecked,
    timeSource,
    timeSourceName,
    setUserState,
    getNetworkTimeInfo,
    checkNetworkTimeStatus,
    setupNetworkListeners,
  }
})
