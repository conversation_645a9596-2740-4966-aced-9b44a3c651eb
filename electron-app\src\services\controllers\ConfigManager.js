const fs = require('fs-extra');
const path = require('path');

class ConfigManager {
  constructor(configFile) {
    this.configFile = configFile;
    this.defaultConfig = {
      theme: 'light',
      language: 'zh-CN',
      fontSize: 14,
      autoSave: true,
      autoSaveInterval: 10000, // 10秒
      backup: {
        autoBackup: false,
        backupInterval: 3600000, // 1小时
        maxBackups: 10
      },
      openai: {
        api_key: '',
        base_url: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        max_tokens: 2000
      },
      editor: {
        wordWrap: true,
        lineNumbers: true,
        minimap: true,
        fontSize: 14,
        fontFamily: 'Consolas, Monaco, monospace'
      },
      window: {
        width: 1400,
        height: 900,
        x: null,
        y: null,
        maximized: false
      },
      recent: {
        projects: [],
        files: []
      }
    };
  }

  async initialize() {
    try {
      // 确保配置文件目录存在
      await fs.ensureDir(path.dirname(this.configFile));
      
      // 如果配置文件不存在，创建默认配置
      if (!await fs.pathExists(this.configFile)) {
        await this.saveConfig(this.defaultConfig);
      }
      
      console.log('ConfigManager 初始化完成');
    } catch (error) {
      console.error('ConfigManager 初始化失败:', error);
      throw error;
    }
  }

  async loadConfig() {
    try {
      if (!await fs.pathExists(this.configFile)) {
        return this.defaultConfig;
      }

      const configData = await fs.readJson(this.configFile);
      
      // 合并默认配置和用户配置，确保所有必要字段都存在
      return this.mergeConfig(this.defaultConfig, configData);
    } catch (error) {
      console.error('加载配置失败:', error);
      return this.defaultConfig;
    }
  }

  async saveConfig(config) {
    try {
      await fs.ensureDir(path.dirname(this.configFile));
      await fs.writeJson(this.configFile, config, { spaces: 2 });
      return {
        status: 'success',
        message: '配置保存成功'
      };
    } catch (error) {
      console.error('保存配置失败:', error);
      return {
        status: 'error',
        message: '配置保存失败',
        error: error.message
      };
    }
  }

  async updateConfig(key, value) {
    try {
      const config = await this.loadConfig();
      
      // 支持嵌套键更新，如 'openai.api_key'
      if (key.includes('.')) {
        const keys = key.split('.');
        let current = config;
        
        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) {
            current[keys[i]] = {};
          }
          current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
      } else {
        config[key] = value;
      }

      const result = await this.saveConfig(config);
      
      if (result.status === 'success') {
        return {
          status: 'success',
          message: '配置更新成功',
          data: config
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error('更新配置失败:', error);
      return {
        status: 'error',
        message: '更新配置失败',
        error: error.message
      };
    }
  }

  async getConfig(key) {
    try {
      const config = await this.loadConfig();
      
      if (key.includes('.')) {
        const keys = key.split('.');
        let current = config;
        
        for (const k of keys) {
          if (current && typeof current === 'object' && k in current) {
            current = current[k];
          } else {
            return null;
          }
        }
        
        return current;
      } else {
        return config[key];
      }
    } catch (error) {
      console.error('获取配置失败:', error);
      return null;
    }
  }

  async resetConfig() {
    try {
      const result = await this.saveConfig(this.defaultConfig);
      return result;
    } catch (error) {
      console.error('重置配置失败:', error);
      return {
        status: 'error',
        message: '重置配置失败',
        error: error.message
      };
    }
  }

  // 添加最近使用的项目
  async addRecentProject(projectData) {
    try {
      const config = await this.loadConfig();
      
      if (!config.recent) {
        config.recent = { projects: [], files: [] };
      }
      
      if (!config.recent.projects) {
        config.recent.projects = [];
      }

      // 移除已存在的项目（避免重复）
      config.recent.projects = config.recent.projects.filter(
        p => p.id !== projectData.id
      );

      // 添加到开头
      config.recent.projects.unshift({
        id: projectData.id,
        name: projectData.name,
        path: projectData.path,
        lastAccessed: new Date().toISOString()
      });

      // 限制最近项目数量
      if (config.recent.projects.length > 10) {
        config.recent.projects = config.recent.projects.slice(0, 10);
      }

      return await this.saveConfig(config);
    } catch (error) {
      console.error('添加最近项目失败:', error);
      return {
        status: 'error',
        message: '添加最近项目失败',
        error: error.message
      };
    }
  }

  // 添加最近使用的文件
  async addRecentFile(fileData) {
    try {
      const config = await this.loadConfig();
      
      if (!config.recent) {
        config.recent = { projects: [], files: [] };
      }
      
      if (!config.recent.files) {
        config.recent.files = [];
      }

      // 移除已存在的文件（避免重复）
      config.recent.files = config.recent.files.filter(
        f => f.path !== fileData.path
      );

      // 添加到开头
      config.recent.files.unshift({
        path: fileData.path,
        name: fileData.name,
        type: fileData.type,
        lastAccessed: new Date().toISOString()
      });

      // 限制最近文件数量
      if (config.recent.files.length > 20) {
        config.recent.files = config.recent.files.slice(0, 20);
      }

      return await this.saveConfig(config);
    } catch (error) {
      console.error('添加最近文件失败:', error);
      return {
        status: 'error',
        message: '添加最近文件失败',
        error: error.message
      };
    }
  }

  // 深度合并配置对象
  mergeConfig(defaultConfig, userConfig) {
    const result = { ...defaultConfig };
    
    for (const key in userConfig) {
      if (userConfig.hasOwnProperty(key)) {
        if (
          typeof userConfig[key] === 'object' &&
          userConfig[key] !== null &&
          !Array.isArray(userConfig[key]) &&
          typeof defaultConfig[key] === 'object' &&
          defaultConfig[key] !== null &&
          !Array.isArray(defaultConfig[key])
        ) {
          result[key] = this.mergeConfig(defaultConfig[key], userConfig[key]);
        } else {
          result[key] = userConfig[key];
        }
      }
    }
    
    return result;
  }
}

module.exports = ConfigManager;
