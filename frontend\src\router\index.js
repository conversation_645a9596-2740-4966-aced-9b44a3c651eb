import { createRouter, createWebHashHistory } from 'vue-router'
import AppLayout from '@/components/layout/AppLayout.vue'
import { menuList } from '@/config/menuConfig'
import { useUserStore } from '@/stores/user'
import { useConfigStore } from '@/stores/config'

function generateRoutes(menus) {
  const routes = []

  menus.forEach(menu => {
    const route = {
      path: menu.path,
      name: menu.name || menu.path.replace('/', ''),
      component: menu.component,
      meta: {
        title: menu.title,
        icon: menu.icon,
        hidden: menu.hidden,
        requiresAuth: true,
        ...(menu.meta || {})
      }
    }

    if (menu.children) {
      route.children = generateRoutes(menu.children)
    }

    routes.push(route)
  })

  return routes
}

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/book/editor/:id',
    name: 'bookEditor',
    component: () => import('@/views/book/editor.vue'),
    meta: {
      title: '书籍编辑',
      requiresAuth: true,
      layout: 'editor'
    }
  },
  {
    path: '/',
    component: AppLayout,
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      ...generateRoutes(menuList),
      {
        path: 'book/writing',
        name: 'bookWriting',
        component: () => import('@/views/book/写作.vue'),
        meta: {
          title: '写作',
          keepAlive: true,
          requiresAuth: true
        }
      },
      {
        path: 'book/settings/:id',
        name: 'bookSettings',
        component: () => import('@/views/book/设定.vue'),
        meta: {
          title: '设定管理',
          requiresAuth: true
        }
      },
      {
        path: 'book/timeline/:id',
        name: 'bookTimeline',
        component: () => import('@/views/book/时间线.vue'),
        meta: {
          title: '时间线',
          requiresAuth: true,
          hidden: true
        }
      },
      {
        path: 'book/inspiration',
        name: 'storyInspiration',
        component: () => import('@/views/book/StoryInspiration.vue'),
        meta: {
          title: '故事创作灵感',
          icon: 'BulbOutlined',
          keepAlive: true,
          requiresAuth: true
        }
      },
      {
        path: 'settings/app',
        name: 'settings',
        component: () => import('@/views/settings/app.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting',
          requiresAuth: true
        }
      },
      {
        path: 'book/dictionary',
        name: 'ChineseDictionary',
        component: () => import('@/views/book/ChineseDictionary.vue'),
        meta: {
          title: '汉语词典',
          keepAlive: false
        }
      },
      {
        path: 'test/markdown',
        name: 'MarkdownTest',
        component: () => import('@/components/MarkdownTest.vue'),
        meta: {
          title: 'Markdown测试',
          keepAlive: false
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 全局前置守卫，用于权限控制
router.beforeEach(async (to, from, next) => {
  // 获取用户存储
  const userStore = useUserStore()
  
  // 检查该路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  
  // 如果路由需要认证
  if (requiresAuth) {
    // 检查用户是否已登录
    if (!userStore.isLoggedIn) {
      // 如果未登录，尝试自动登录
      try {
        const autoLoginSuccess = await userStore.autoLogin()
        
        if (!autoLoginSuccess) {
          // 自动登录失败，重定向到登录页
          next({
            path: '/login',
            query: { redirect: to.fullPath, loginError: '请先登录系统' } 
          })
          return
        }
      } catch (error) {
        console.error('路由守卫中的自动登录失败:', error)
        next({
          path: '/login',
          query: { redirect: to.fullPath, loginError: encodeURIComponent('登录验证失败，请重新登录') }
        })
        return
      }
    }
    
    // 如果用户已登录，检查激活状态是否过期
    // 如果上次检查时间超过30分钟，重新检查
    const lastChecked = userStore.lastChecked || 0
    const thirtyMinutes = 30 * 60 * 1000
    const needsRecheck = Date.now() - lastChecked > thirtyMinutes

    if (needsRecheck && !userStore.isCheckingActivation) {
      try {
        console.log('路由守卫：检查激活状态...')
        const activationStatus = await userStore.checkActivation(false) // 不强制退出
        if (!activationStatus || !activationStatus.valid) {
          // 激活状态无效，重定向到登录页
          next({
            path: '/login',
            query: {
              redirect: to.fullPath,
              loginError: encodeURIComponent('登录状态已过期，请重新登录')
            }
          })
          return
        }
      } catch (error) {
        console.error('路由守卫：检查激活状态失败:', error)
        // 网络错误不阻止路由跳转，但记录错误
      }
    }
  }
  
  // 允许访问
  next()
})

export default router
