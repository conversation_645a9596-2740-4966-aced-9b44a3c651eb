<template>
  <div class="entity-card-exporter">
    <div class="exporter-container">
      <!-- 左侧控制面板 -->
      <div class="control-panel">
        <!-- 固定头部 -->
        <div class="panel-header">
          <h3 class="panel-title">卡片设置</h3>
        </div>

        <!-- 固定的主题选择标题 -->
        <div class="theme-section-header">
          <div class="section-title">选择主题风格</div>
          <button
            class="manage-btn"
            @click="showThemeManager = true"
          >
            管理主题
          </button>
        </div>

        <!-- 可滚动的主题列表 -->
        <div class="theme-list-container">
          <div class="theme-list">
            <div
              v-for="theme in availableThemes"
              :key="theme.id"
              class="theme-item"
              :class="{ active: currentTheme === theme.id }"
              @click="currentTheme = theme.id"
            >
              <div class="theme-preview"
                   :style="{
                     background: theme.colors.background,
                     borderColor: currentTheme === theme.id ? theme.colors.accent : 'transparent',
                     position: 'relative',
                     overflow: 'hidden'
                   }">
                <!-- 背景图案 -->
                <div v-if="theme.backgroundPattern"
                     class="theme-pattern"
                     :style="{ opacity: theme.backgroundPattern.opacity || 0.1 }"
                     v-html="theme.backgroundPattern.svg">
                </div>
                <div class="theme-avatar" :style="{ background: theme.colors.avatarBackground, position: 'relative', zIndex: 2 }">A</div>
                <div class="theme-card" :style="{ background: theme.colors.cardBackground, position: 'relative', zIndex: 2 }"></div>
              </div>
              <div class="theme-name">{{ theme.name }}</div>
            </div>
          </div>
        </div>

        <!-- 固定的导出选项 -->
        <div class="export-options-section">
          <div class="section-title">导出选项</div>
          <div class="option-list">
            <label class="option-item">
              <input type="checkbox" v-model="includeRelations" />
              <span class="option-text">包含角色关系</span>
            </label>
            <label class="option-item">
              <input type="checkbox" v-model="includeDimensions" />
              <span class="option-text">包含属性维度</span>
            </label>
            <div class="option-item">
              <span class="option-label">图片质量:</span>
              <select v-model="imageQuality" class="quality-select">
                <option value="1">标准</option>
                <option value="2">高清</option>
                <option value="3">超清</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 固定底部按钮 -->
        <div class="panel-footer">
          <button
            class="generate-btn"
            :class="{ loading: generatingPreview }"
            :disabled="!entity || generatingPreview"
            @click="generatePreview"
          >
            {{ generatingPreview ? '生成中...' : (previewUrl ? '重新生成' : '生成预览') }}
          </button>
        </div>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-panel">
        <!-- 固定头部 -->
        <div class="preview-header">
          <h3 class="panel-title">卡片预览</h3>
          <button
            v-if="previewUrl"
            class="export-btn"
            :class="{ loading: exporting }"
            :disabled="exporting"
            @click="exportCard"
          >
            {{ exporting ? '导出中...' : '导出卡片' }}
          </button>
        </div>

        <!-- 可滚动预览内容 -->
        <div class="preview-content" ref="previewContent">
          <div v-if="previewUrl" class="preview-container">
            <img :src="previewUrl" alt="卡片预览" class="preview-image" />
          </div>
          <div v-else class="empty-state">
            <div class="empty-icon">📄</div>
            <p class="empty-text">点击"生成预览"按钮创建角色卡片</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主题管理对话框 -->
    <el-dialog
      v-model="showThemeManager"
      title="主题管理"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="theme-manager">
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="添加主题" name="add">
            <div class="add-theme-form">
              <el-form :model="newTheme" label-width="100px">
                <el-form-item label="主题名称">
                  <el-input v-model="newTheme.name" placeholder="请输入主题名称" />
                </el-form-item>
                <el-form-item label="主题ID">
                  <el-input v-model="newTheme.id" placeholder="请输入主题ID（英文）" />
                </el-form-item>
                <el-form-item label="主题JSON">
                  <el-input
                    v-model="newTheme.json"
                    type="textarea"
                    :rows="10"
                    placeholder="请粘贴主题JSON配置，格式参考：
{
  &quot;colors&quot;: {
    &quot;background&quot;: &quot;linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%)&quot;,
    &quot;textPrimary&quot;: &quot;#333333&quot;,
    &quot;textSecondary&quot;: &quot;#666666&quot;,
    &quot;cardBackground&quot;: &quot;#ffffff&quot;,
    &quot;accent&quot;: &quot;#409EFF&quot;,
    &quot;avatarBackground&quot;: &quot;#4a93ff&quot;,
    &quot;border&quot;: &quot;#eaeaea&quot;,
    &quot;sectionBackground&quot;: &quot;#f9f9f9&quot;
  },
  &quot;fonts&quot;: {
    &quot;primary&quot;: &quot;'PingFang SC', 'Microsoft YaHei', sans-serif&quot;,
    &quot;title&quot;: &quot;600 32px 'PingFang SC', 'Microsoft YaHei', sans-serif&quot;,
    &quot;subtitle&quot;: &quot;500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif&quot;,
    &quot;body&quot;: &quot;400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif&quot;
  },
  &quot;borderRadius&quot;: &quot;12px&quot;,
  &quot;shadow&quot;: &quot;0 8px 24px rgba(0, 0, 0, 0.12)&quot;
}"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="addTheme">添加主题</el-button>
                  <el-button @click="resetNewTheme">重置</el-button>
                  <el-button type="info" @click="showJsonHelp">JSON格式说明</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="管理主题" name="manage">
            <div class="manage-themes">
              <div v-for="theme in customThemes" :key="theme.id" class="theme-item">
                <div class="theme-info">
                  <h4>{{ theme.name }}</h4>
                  <p>ID: {{ theme.id }}</p>
                </div>
                <div class="theme-actions">
                  <el-button size="small" @click="editTheme(theme)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteTheme(theme.id)">删除</el-button>
                </div>
              </div>
              <div v-if="customThemes.length === 0" class="empty-state">
                暂无自定义主题
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import html2canvas from 'html2canvas';

import { useConfigStore } from '@/stores/config';

const props = defineProps({
  entity: {
    type: Object,
    required: true
  },
  entityRelations: {
    type: Array,
    default: () => []
  },
  templateName: {
    type: String,
    default: ''
  },
  getEntityNameById: {
    type: Function,
    required: true,
    default: (id) => `未知角色 (${id})`
  },
  formatDimensionKey: {
    type: Function,
    default: key => key
  },
  defaultTheme: {
    type: String,
    default: 'classic'
  }
});

const emit = defineEmits(['export-success']);

// 状态变量
const previewUrl = ref('');
const generatingPreview = ref(false);
const exporting = ref(false);
const currentTheme = ref(props.defaultTheme);
const previewContent = ref(null);

// 导出选项
const includeRelations = ref(false);
const includeDimensions = ref(true);
const imageQuality = ref('3');

// 主题管理相关状态
const showThemeManager = ref(false);
const activeTab = ref('add');
const newTheme = ref({
  name: '',
  id: '',
  json: ''
});

// 引入配置存储
const configStore = useConfigStore();

// 获取软件界面主题（检查HTML元素的class）
const isDarkTheme = computed(() => {
  if (typeof document !== 'undefined') {
    return document.documentElement.classList.contains('dark');
  }
  return false;
});

// 定义增强的内置主题
const builtinThemes = [
  {
    id: 'classic',
    name: '经典',
    colors: {
      background: 'linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%)',
      textPrimary: '#333333',
      textSecondary: '#666666',
      textTertiary: '#999999',
      cardBackground: '#ffffff',
      accent: '#409EFF',
      accentLight: '#ecf5ff',
      avatarBackground: '#4a93ff',
      border: '#eaeaea',
      borderLight: '#f0f0f0',
      sectionBackground: '#f9f9f9',
      tagBackground: '#f0f9ff',
      tagText: '#1890ff'
    },
    fonts: {
      primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '24px',
      sectionGap: '20px',
      itemGap: '12px',
      avatarSize: '72px'
    },
    borderRadius: '12px',
    shadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
    cardShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
    backgroundPattern: {
      type: 'geometric',
      opacity: 0.08,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="classicPattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
            <circle cx="30" cy="30" r="20" fill="none" stroke="#409EFF" stroke-width="0.5" opacity="0.3"/>
            <circle cx="30" cy="30" r="10" fill="none" stroke="#409EFF" stroke-width="0.3" opacity="0.5"/>
            <path d="M15,15 L45,45 M45,15 L15,45" stroke="#409EFF" stroke-width="0.2" opacity="0.2"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#classicPattern)"/>
      </svg>`
    }
  },
  {
    id: 'dark',
    name: '暗黑',
    colors: {
      background: 'linear-gradient(135deg, #2d3748 0%, #1a202c 100%)',
      textPrimary: '#e2e8f0',
      textSecondary: '#a0aec0',
      textTertiary: '#718096',
      cardBackground: '#2d3748',
      accent: '#63b3ed',
      accentLight: '#2a4a6b',
      avatarBackground: '#4299e1',
      border: '#4a5568',
      borderLight: '#3a4553',
      sectionBackground: '#283141',
      tagBackground: '#2a4a6b',
      tagText: '#90cdf4'
    },
    fonts: {
      primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '24px',
      sectionGap: '20px',
      itemGap: '12px',
      avatarSize: '72px'
    },
    borderRadius: '12px',
    shadow: '0 8px 24px rgba(0, 0, 0, 0.25)',
    cardShadow: '0 2px 12px rgba(0, 0, 0, 0.15)',
    backgroundPattern: {
      type: 'starry',
      opacity: 0.25,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="starryPattern" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse">
            <circle cx="20" cy="30" r="1" fill="#63b3ed" opacity="0.6"/>
            <circle cx="80" cy="20" r="0.5" fill="#90cdf4" opacity="0.8"/>
            <circle cx="100" cy="70" r="1.5" fill="#63b3ed" opacity="0.4"/>
            <circle cx="40" cy="90" r="0.8" fill="#90cdf4" opacity="0.7"/>
            <circle cx="60" cy="50" r="0.3" fill="#bee3f8" opacity="0.9"/>
            <path d="M70,40 L72,44 L76,44 L73,47 L74,51 L70,49 L66,51 L67,47 L64,44 L68,44 Z" fill="#63b3ed" opacity="0.3"/>
            <path d="M30,80 L31,82 L33,82 L31.5,83.5 L32,85 L30,84 L28,85 L28.5,83.5 L27,82 L29,82 Z" fill="#90cdf4" opacity="0.5"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#starryPattern)"/>
      </svg>`
    }
  },
  {
    id: 'creative',
    name: '灵感',
    colors: {
      background: 'linear-gradient(135deg, #fdf6fd 0%, #f3e7ff 100%)',
      textPrimary: '#4a2a5d',
      textSecondary: '#7b5a8c',
      textTertiary: '#a78baf',
      cardBackground: '#fcf8ff',
      accent: '#9c6bdf',
      accentLight: '#f0e6ff',
      avatarBackground: '#7956b3',
      border: '#e6d8f8',
      borderLight: '#f0e6ff',
      sectionBackground: '#f8f0ff',
      tagBackground: '#f0e6ff',
      tagText: '#8b5cf6'
    },
    fonts: {
      primary: "'PingFang SC', 'Source Han Sans CN', sans-serif",
      title: "600 28px 'PingFang SC', 'Source Han Sans CN', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Source Han Sans CN', sans-serif",
      body: "400 15px 'PingFang SC', 'Source Han Sans CN', sans-serif",
      caption: "400 12px 'PingFang SC', 'Source Han Sans CN', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Source Han Sans CN', sans-serif"
    },
    spacing: {
      cardPadding: '28px',
      sectionGap: '24px',
      itemGap: '14px',
      avatarSize: '76px'
    },
    borderRadius: '20px',
    shadow: '0 12px 35px rgba(156, 107, 223, 0.12)',
    cardShadow: '0 4px 16px rgba(156, 107, 223, 0.08)',
    backgroundPattern: {
      type: 'floral',
      opacity: 0.12,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="floralPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
            <path d="M50,30 Q60,20 70,30 Q60,40 50,30 Q40,20 30,30 Q40,40 50,30" fill="#9c6bdf" opacity="0.15"/>
            <path d="M50,30 Q55,25 60,30 Q55,35 50,30 Q45,25 40,30 Q45,35 50,30" fill="#8b5cf6" opacity="0.2"/>
            <circle cx="50" cy="30" r="3" fill="#7c3aed" opacity="0.3"/>
            <path d="M20,70 Q25,65 30,70 Q25,75 20,70 Q15,65 10,70 Q15,75 20,70" fill="#a855f7" opacity="0.1"/>
            <path d="M80,80 Q85,75 90,80 Q85,85 80,80 Q75,75 70,80 Q75,85 80,80" fill="#9333ea" opacity="0.12"/>
            <path d="M30,20 C35,15 40,20 35,25 C30,30 25,25 30,20" fill="#8b5cf6" opacity="0.08"/>
            <path d="M70,60 C75,55 80,60 75,65 C70,70 65,65 70,60" fill="#7c3aed" opacity="0.1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#floralPattern)"/>
      </svg>`
    }
  },
  {
    id: 'minimalist',
    name: '简约',
    colors: {
      background: '#ffffff',
      textPrimary: '#111111',
      textSecondary: '#555555',
      textTertiary: '#888888',
      cardBackground: '#ffffff',
      accent: '#000000',
      accentLight: '#f5f5f5',
      avatarBackground: '#111111',
      border: '#dddddd',
      borderLight: '#eeeeee',
      sectionBackground: '#f7f7f7',
      tagBackground: '#f0f0f0',
      tagText: '#333333'
    },
    fonts: {
      primary: "'Helvetica Neue', Arial, sans-serif",
      title: "600 28px 'Helvetica Neue', Arial, sans-serif",
      subtitle: "500 16px 'Helvetica Neue', Arial, sans-serif",
      body: "400 14px 'Helvetica Neue', Arial, sans-serif",
      caption: "400 12px 'Helvetica Neue', Arial, sans-serif",
      dimension: "500 15px 'Helvetica Neue', Arial, sans-serif"
    },
    spacing: {
      cardPadding: '20px',
      sectionGap: '16px',
      itemGap: '10px',
      avatarSize: '64px'
    },
    borderRadius: '2px',
    shadow: '0 1px 3px rgba(0, 0, 0, 0.08)',
    cardShadow: '0 1px 6px rgba(0, 0, 0, 0.05)',
    backgroundPattern: {
      type: 'minimal',
      opacity: 0.02,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="minimalPattern" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
            <line x1="0" y1="40" x2="80" y2="40" stroke="#000000" stroke-width="0.5" opacity="0.1"/>
            <line x1="40" y1="0" x2="40" y2="80" stroke="#000000" stroke-width="0.5" opacity="0.1"/>
            <rect x="20" y="20" width="40" height="40" fill="none" stroke="#000000" stroke-width="0.3" opacity="0.08"/>
            <circle cx="40" cy="40" r="15" fill="none" stroke="#000000" stroke-width="0.2" opacity="0.06"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#minimalPattern)"/>
      </svg>`
    }
  },
  {
    id: 'mountain',
    name: '山川',
    colors: {
      background: 'linear-gradient(135deg, #e8f4f8 0%, #d1e7dd 100%)',
      textPrimary: '#2d5016',
      textSecondary: '#52734d',
      textTertiary: '#74a478',
      cardBackground: '#f8fffe',
      accent: '#28a745',
      accentLight: '#d4edda',
      avatarBackground: '#20c997',
      border: '#c3e6cb',
      borderLight: '#e2f3e4',
      sectionBackground: '#f1f8e9',
      tagBackground: '#d1ecf1',
      tagText: '#0c5460'
    },
    fonts: {
      primary: "'Noto Serif SC', 'SimSun', serif",
      title: "600 28px 'Noto Serif SC', 'SimSun', serif",
      subtitle: "500 16px 'Noto Serif SC', 'SimSun', serif",
      body: "400 14px 'Noto Serif SC', 'SimSun', serif",
      caption: "400 12px 'Noto Serif SC', 'SimSun', serif",
      dimension: "500 15px 'Noto Serif SC', 'SimSun', serif"
    },
    spacing: {
      cardPadding: '28px',
      sectionGap: '24px',
      itemGap: '14px',
      avatarSize: '76px'
    },
    borderRadius: '16px',
    shadow: '0 8px 32px rgba(40, 167, 69, 0.15)',
    cardShadow: '0 4px 16px rgba(40, 167, 69, 0.08)',
    backgroundPattern: {
      type: 'mountain',
      opacity: 0.15,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="mountainPattern" x="0" y="0" width="200" height="120" patternUnits="userSpaceOnUse">
            <path d="M0,120 L40,60 L80,80 L120,40 L160,70 L200,50 L200,120 Z" fill="#28a745" opacity="0.1"/>
            <path d="M0,120 L30,80 L60,90 L100,60 L140,85 L180,65 L200,75 L200,120 Z" fill="#20c997" opacity="0.08"/>
            <path d="M0,120 L50,90 L90,100 L130,80 L170,95 L200,85 L200,120 Z" fill="#6f42c1" opacity="0.05"/>
            <circle cx="160" cy="30" r="12" fill="#ffc107" opacity="0.3"/>
            <path d="M150,35 Q160,25 170,35" stroke="#ffc107" stroke-width="1" fill="none" opacity="0.2"/>
            <circle cx="40" cy="25" r="3" fill="#ffffff" opacity="0.4"/>
            <circle cx="120" cy="20" r="2" fill="#ffffff" opacity="0.3"/>
            <circle cx="80" cy="15" r="1.5" fill="#ffffff" opacity="0.5"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#mountainPattern)"/>
      </svg>`
    }
  },
  {
    id: 'moonlight',
    name: '月夜',
    colors: {
      background: 'linear-gradient(135deg, #1a1d29 0%, #2d1b69 100%)',
      textPrimary: '#e2e8f0',
      textSecondary: '#cbd5e0',
      textTertiary: '#a0aec0',
      cardBackground: '#2a2f3a',
      accent: '#805ad5',
      accentLight: '#553c9a',
      avatarBackground: '#667eea',
      border: '#4a5568',
      borderLight: '#3a4553',
      sectionBackground: '#2d3748',
      tagBackground: '#553c9a',
      tagText: '#d6bcfa'
    },
    fonts: {
      primary: "'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'Noto Sans SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '26px',
      sectionGap: '22px',
      itemGap: '13px',
      avatarSize: '74px'
    },
    borderRadius: '18px',
    shadow: '0 12px 40px rgba(128, 90, 213, 0.25)',
    cardShadow: '0 6px 20px rgba(128, 90, 213, 0.15)',
    backgroundPattern: {
      type: 'moonlight',
      opacity: 0.2,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="moonlightPattern" x="0" y="0" width="150" height="150" patternUnits="userSpaceOnUse">
            <circle cx="75" cy="40" r="20" fill="#f7fafc" opacity="0.8"/>
            <path d="M65,35 Q75,25 85,35 Q75,45 65,35" fill="#2d1b69" opacity="0.3"/>
            <circle cx="30" cy="100" r="1" fill="#e2e8f0" opacity="0.6"/>
            <circle cx="120" cy="80" r="0.8" fill="#cbd5e0" opacity="0.7"/>
            <circle cx="100" cy="120" r="1.2" fill="#e2e8f0" opacity="0.5"/>
            <circle cx="20" cy="60" r="0.5" fill="#f7fafc" opacity="0.8"/>
            <circle cx="140" cy="30" r="0.7" fill="#cbd5e0" opacity="0.6"/>
            <path d="M10,130 Q30,120 50,130 Q70,140 90,130 Q110,120 130,130 Q150,140 150,150 L0,150 Z" fill="#4c51bf" opacity="0.1"/>
            <path d="M0,140 Q20,130 40,140 Q60,150 80,140 Q100,130 120,140 Q140,150 150,140 L150,150 L0,150 Z" fill="#553c9a" opacity="0.08"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#moonlightPattern)"/>
      </svg>`
    }
  }
];

// 获取自定义主题
const customThemes = computed(() => {
  const themes = configStore.state.config.customThemes || [];
  console.log('customThemes computed:', themes);
  return themes;
});

// 合并内置主题和自定义主题
const availableThemes = computed(() => {
  const available = [...customThemes.value, ...builtinThemes];
  console.log('availableThemes computed:', available.map(t => ({ id: t.id, name: t.name })));
  return available;
});

// 在组件挂载时加载配置
onMounted(async () => {
  console.log('EntityCardExporter mounted, configStore.state.config.loaded:', configStore.state.config.loaded);
  console.log('Current customThemes:', configStore.state.config.customThemes);

  if (!configStore.state.config.loaded) {
    try {
      console.log('Loading config...');
      await configStore.loadConfig();
      console.log('Config loaded, customThemes:', configStore.state.config.customThemes);
    } catch (error) {
      console.error('加载配置失败:', error);
      ElMessage.error('加载自定义主题失败');
    }
  }
});

// 默认主题字段
const defaultThemeFields = {
  spacing: {
    cardPadding: '24px',
    sectionGap: '20px',
    itemGap: '12px',
    avatarSize: '72px'
  },
  fonts: {
    primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
    title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
  },
  borderRadius: '12px',
  shadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
  cardShadow: '0 2px 12px rgba(0, 0, 0, 0.08)'
};

// 获取当前主题配置，确保所有字段都存在
const theme = computed(() => {
  const baseTheme = availableThemes.value.find(t => t.id === currentTheme.value) || availableThemes.value[0];

  // 合并默认字段，确保不会有undefined
  return {
    ...defaultThemeFields,
    ...baseTheme,
    spacing: {
      ...defaultThemeFields.spacing,
      ...(baseTheme.spacing || {})
    },
    fonts: {
      ...defaultThemeFields.fonts,
      ...(baseTheme.fonts || {})
    }
  };
});

// 主题管理方法
const resetNewTheme = () => {
  newTheme.value = {
    name: '',
    id: '',
    json: ''
  };
};

const addTheme = async () => {
  try {
    console.log('Adding theme:', newTheme.value);

    if (!newTheme.value.name || !newTheme.value.id || !newTheme.value.json) {
      ElMessage.warning('请填写完整的主题信息');
      return;
    }

    // 解析JSON
    let themeData;
    try {
      themeData = JSON.parse(newTheme.value.json);
      console.log('Parsed theme data:', themeData);
    } catch (error) {
      console.error('JSON parse error:', error);
      ElMessage.error('主题JSON格式错误');
      return;
    }

    // 验证主题数据结构
    if (!themeData.colors || !themeData.fonts) {
      ElMessage.error('主题JSON缺少必要的colors或fonts配置');
      return;
    }

    // 构建完整的主题对象
    const themeObject = {
      id: newTheme.value.id,
      name: newTheme.value.name,
      ...themeData
    };

    console.log('Theme object to add:', themeObject);

    // 添加到配置
    await configStore.addCustomTheme(themeObject);

    console.log('Theme added successfully, current customThemes:', configStore.state.config.customThemes);

    ElMessage.success('主题添加成功');
    resetNewTheme();
    activeTab.value = 'manage';
  } catch (error) {
    console.error('添加主题失败:', error);
    ElMessage.error('添加主题失败: ' + error.message);
  }
};

const editTheme = (theme) => {
  newTheme.value = {
    name: theme.name,
    id: theme.id,
    json: JSON.stringify({
      colors: theme.colors,
      fonts: theme.fonts,
      borderRadius: theme.borderRadius,
      shadow: theme.shadow,
      pattern: theme.pattern
    }, null, 2)
  };
  activeTab.value = 'add';
};

const deleteTheme = async (themeId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个主题吗？', '确认删除', {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await configStore.deleteCustomTheme(themeId);
    ElMessage.success('主题删除成功');

    // 如果删除的是当前选中的主题，切换到默认主题
    if (currentTheme.value === themeId) {
      currentTheme.value = 'classic';
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除主题失败:', error);
      ElMessage.error('删除主题失败: ' + error.message);
    }
  }
};

const showJsonHelp = () => {
  ElMessageBox.alert(`
<h3>主题JSON格式说明</h3>
<p>主题JSON需要包含以下必要字段：</p>

<h4>1. colors（颜色配置）</h4>
<ul>
  <li><strong>background</strong>: 卡片背景色（支持渐变）</li>
  <li><strong>textPrimary</strong>: 主要文字颜色</li>
  <li><strong>textSecondary</strong>: 次要文字颜色</li>
  <li><strong>cardBackground</strong>: 内容卡片背景色</li>
  <li><strong>accent</strong>: 强调色（用于按钮、链接等）</li>
  <li><strong>avatarBackground</strong>: 头像背景色</li>
  <li><strong>border</strong>: 边框颜色</li>
  <li><strong>sectionBackground</strong>: 区块背景色</li>
</ul>

<h4>2. fonts（字体配置）</h4>
<ul>
  <li><strong>primary</strong>: 主要字体族</li>
  <li><strong>title</strong>: 标题字体样式（包含大小和粗细）</li>
  <li><strong>subtitle</strong>: 副标题字体样式</li>
  <li><strong>body</strong>: 正文字体样式</li>
</ul>

<h4>3. 可选字段</h4>
<ul>
  <li><strong>borderRadius</strong>: 圆角大小（如："12px"）</li>
  <li><strong>shadow</strong>: 阴影效果</li>
  <li><strong>pattern</strong>: SVG背景图案（可选）</li>
</ul>

<p><strong>提示：</strong>可以参考现有的自定义主题格式，或从settings.json中复制现有主题配置。</p>
  `, '主题JSON格式说明', {
    confirmButtonText: '知道了',
    dangerouslyUseHTMLString: true
  });
};

// 生成预览方法
const generatePreview = async () => {
  if (!props.entity) {
    ElMessage.warning('没有实体数据可供导出');
    return;
  }

  try {
    generatingPreview.value = true;

    // 创建临时卡片容器
    const cardContainer = document.createElement('div');
    cardContainer.className = 'entity-card-export';

    // 基本样式
    let containerStyle = `
      width: 600px;
      padding: 30px;
      background: ${theme.value.colors.background};
      border-radius: ${theme.value.borderRadius};
      box-shadow: ${theme.value.shadow};
      position: fixed;
      top: -9999px;
      left: -9999px;
      color: ${theme.value.colors.textPrimary};
      z-index: -1;
      overflow: hidden;
      position: relative;
    `;

    cardContainer.style.cssText = containerStyle;
    document.body.appendChild(cardContainer);

    // 添加背景图案支持
    if (theme.value.backgroundPattern && theme.value.backgroundPattern.svg) {
      const patternContainer = document.createElement('div');
      patternContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        overflow: hidden;
        opacity: ${theme.value.backgroundPattern.opacity || 0.1};
        pointer-events: none;
      `;
      patternContainer.innerHTML = theme.value.backgroundPattern.svg;
      cardContainer.appendChild(patternContainer);
    }

    // 根据用户选择决定是否包含关系和维度
    const showRelations = includeRelations.value && props.entityRelations.length > 0;
    const showDimensions = includeDimensions.value && props.entity.dimensions &&
                          Object.keys(props.entity.dimensions).length > 0;

    // 根据质量选项设置缩放系数
    const scaleFactors = {
      '1': 1.5,  // 标准
      '2': 2,    // 高清
      '3': 3     // 超清
    };
    const scaleFactor = scaleFactors[imageQuality.value] || 2;

    // 处理维度数据的渲染，将换行符转换为<br>
    const renderDimensionValue = (value) => {
      return value.toString()
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;')
        .replace(/\n/g, '<br>');
    };

    // 重构卡片HTML模板生成
    const contentDiv = document.createElement('div');
    contentDiv.style.cssText = `
      position: relative;
      z-index: 10;
    `;

    // 获取主题配置的便捷访问
    const t = theme.value;

    contentDiv.innerHTML = `
      <style>
        /* 限定作用域的样式重置和基础设置 */
        .entity-card-generated * {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }

        .entity-card-generated {
          font-family: ${t.fonts.primary};
          line-height: 1.6;
          letter-spacing: 0.015em;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          color: ${t.colors.textPrimary};
        }

        /* 标题样式 */
        .entity-card-generated .card-title {
          font: ${t.fonts.title};
          color: ${t.colors.textPrimary};
          margin: 0;
          line-height: 1.3;
          text-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .entity-card-generated .card-subtitle {
          font: ${t.fonts.subtitle};
          color: ${t.colors.textSecondary};
          margin: 4px 0 0 0;
          opacity: 0.9;
        }

        /* 头像样式 */
        .entity-card-generated .avatar {
          width: ${t.spacing.avatarSize};
          height: ${t.spacing.avatarSize};
          border-radius: 50%;
          background: ${t.colors.avatarBackground};
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: calc(${t.spacing.avatarSize} * 0.4);
          font-weight: 700;
          color: white;
          margin-right: 20px;
          box-shadow: ${t.cardShadow};
          position: relative;
        }

        .entity-card-generated .avatar::before {
          content: '';
          position: absolute;
          inset: -2px;
          border-radius: 50%;
          background: linear-gradient(45deg, ${t.colors.accent}40, transparent);
          z-index: -1;
        }

        /* 卡片容器样式 - 优化背景融合 */
        .entity-card-generated .card-section {
          background: ${t.colors.cardBackground}f0; /* 添加透明度 */
          backdrop-filter: blur(10px); /* 毛玻璃效果 */
          -webkit-backdrop-filter: blur(10px);
          border-radius: ${t.borderRadius};
          padding: ${t.spacing.cardPadding};
          margin-bottom: ${t.spacing.sectionGap};
          box-shadow: ${t.cardShadow}, inset 0 1px 0 rgba(255,255,255,0.1);
          border: 1px solid ${t.colors.border}80; /* 半透明边框 */
          position: relative;
          overflow: hidden;
        }

        .entity-card-generated .card-section::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, ${t.colors.accent}, ${t.colors.accent}80);
          opacity: 0.8;
        }

        .entity-card-generated .card-section::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            ${t.colors.cardBackground}20 0%,
            transparent 50%,
            ${t.colors.accent}05 100%);
          pointer-events: none;
        }

        /* 区块标题样式 */
        .entity-card-generated .section-title {
          font: ${t.fonts.subtitle};
          color: ${t.colors.textPrimary};
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          border-bottom: 2px solid ${t.colors.borderLight};
          position: relative;
        }

        .entity-card-generated .section-title::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 30px;
          height: 2px;
          background: ${t.colors.accent};
        }

        /* 维度项样式 - 优化背景融合 */
        .entity-card-generated .dimension-item {
          margin-bottom: ${t.spacing.itemGap};
          border-radius: calc(${t.borderRadius} * 0.6);
          overflow: hidden;
          border: 1px solid ${t.colors.borderLight}60;
          transition: all 0.2s ease;
          background: ${t.colors.cardBackground}40; /* 半透明背景 */
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
        }

        .entity-card-generated .dimension-item:hover {
          border-color: ${t.colors.accent}60;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.08);
          background: ${t.colors.cardBackground}60;
        }

        .entity-card-generated .dimension-label {
          font: ${t.fonts.dimension};
          color: ${t.colors.textPrimary};
          background: ${t.colors.sectionBackground}80; /* 半透明背景 */
          padding: 8px 12px;
          border-bottom: 1px solid ${t.colors.borderLight}40;
          font-weight: 600;
          position: relative;
        }

        .entity-card-generated .dimension-label::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, ${t.colors.accent}10, transparent);
          pointer-events: none;
        }

        .entity-card-generated .dimension-value {
          font: ${t.fonts.body};
          color: ${t.colors.textPrimary};
          padding: 12px;
          line-height: 1.7;
          white-space: pre-wrap;
          word-break: break-word;
          background: ${t.colors.cardBackground}20; /* 更透明的背景 */
          position: relative;
        }

        /* 关系项样式 - 优化背景融合 */
        .entity-card-generated .relation-item {
          display: flex;
          align-items: center;
          padding: 10px 12px;
          margin-bottom: 8px;
          background: ${t.colors.sectionBackground}60; /* 半透明背景 */
          backdrop-filter: blur(3px);
          -webkit-backdrop-filter: blur(3px);
          border-radius: calc(${t.borderRadius} * 0.6);
          border: 1px solid ${t.colors.borderLight}50;
          transition: all 0.2s ease;
          position: relative;
          overflow: hidden;
        }

        .entity-card-generated .relation-item::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 3px;
          height: 100%;
          background: ${t.colors.accent};
          opacity: 0.6;
          transition: opacity 0.2s ease;
        }

        .entity-card-generated .relation-item:hover {
          background: ${t.colors.accentLight}80;
          border-color: ${t.colors.accent}80;
          transform: translateX(4px);
        }

        .entity-card-generated .relation-item:hover::before {
          opacity: 1;
        }

        .entity-card-generated .relation-arrow {
          font-size: 16px;
          color: ${t.colors.accent};
          margin-right: 12px;
          font-weight: bold;
        }

        .entity-card-generated .relation-name {
          flex: 1;
          font: ${t.fonts.body};
          color: ${t.colors.textPrimary};
          font-weight: 500;
        }

        .entity-card-generated .relation-tag {
          padding: 4px 10px;
          background: ${t.colors.tagBackground}90; /* 半透明背景 */
          backdrop-filter: blur(3px);
          -webkit-backdrop-filter: blur(3px);
          color: ${t.colors.tagText};
          border-radius: 12px;
          font: ${t.fonts.caption};
          font-weight: 500;
          border: 1px solid ${t.colors.accent}40;
          position: relative;
          overflow: hidden;
        }

        .entity-card-generated .relation-tag::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, ${t.colors.accent}10, transparent);
          pointer-events: none;
        }

        /* 描述样式 - 优化背景融合 */
        .entity-card-generated .description {
          font: ${t.fonts.body};
          color: ${t.colors.textPrimary};
          background: ${t.colors.sectionBackground}70; /* 半透明背景 */
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          padding: 16px;
          border-radius: calc(${t.borderRadius} * 0.8);
          line-height: 1.7;
          border-left: 4px solid ${t.colors.accent};
          margin: 8px 0;
          position: relative;
          overflow: hidden;
        }

        .entity-card-generated .description::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 60px;
          height: 100%;
          background: linear-gradient(90deg, transparent, ${t.colors.accent}08);
          pointer-events: none;
        }

        /* 页脚样式 */
        .entity-card-generated .footer {
          text-align: center;
          margin-top: 24px;
          font: ${t.fonts.caption};
          color: ${t.colors.textTertiary};
          opacity: 0.8;
        }
      </style>

      <div class="entity-card-generated">
        <!-- 头部区域 -->
        <div style="display: flex; align-items: center; margin-bottom: 28px;">
          <div class="avatar">
            ${props.entity.name?.substring(0, 1) || '?'}
          </div>
          <div style="flex: 1;">
            <h1 class="card-title">${props.entity.name || '未命名角色'}</h1>
            ${props.templateName ? `<div class="card-subtitle">${props.templateName}</div>` : ''}
          </div>
        </div>

      <!-- 基本信息区域 -->
      <div class="card-section">
        <h2 class="section-title">基本信息</h2>
        ${props.entity.description ? `
          <div class="description">${props.entity.description}</div>
        ` : `
          <div style="color: ${t.colors.textTertiary}; font-style: italic; text-align: center; padding: 20px;">
            暂无描述信息
          </div>
        `}
      </div>

      ${showDimensions ? `
        <!-- 属性维度区域 -->
        <div class="card-section">

          <div style="display: grid; gap: ${t.spacing.itemGap};">
            ${Object.entries(props.entity.dimensions).map(([key, value]) => `
              <div class="dimension-item">
                <div class="dimension-label">${props.formatDimensionKey(key)}</div>
                <div class="dimension-value">${renderDimensionValue(value)}</div>
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}

      ${showRelations ? `
        <!-- 相关关系区域 -->
        <div class="card-section">
          <h2 class="section-title">相关关系 (${props.entityRelations.length})</h2>
          ${props.entityRelations.slice(0, 5).map(relation => `
            <div class="relation-item">
              <div class="relation-arrow">${relation.source === props.entity.id ? '→' : '←'}</div>
              <div class="relation-name">${props.getEntityNameById(relation.source === props.entity.id ? relation.target : relation.source)}</div>
              <div class="relation-tag">${relation.type}</div>
            </div>
          `).join('')}
          ${props.entityRelations.length > 5 ? `
            <div style="text-align: center; color: ${t.colors.textSecondary}; margin-top: 16px; font: ${t.fonts.caption};">
              <span style="background: ${t.colors.sectionBackground}; padding: 6px 12px; border-radius: 16px; border: 1px solid ${t.colors.borderLight};">
                + ${props.entityRelations.length - 5} 项更多关系
              </span>
            </div>
          ` : ''}
        </div>
      ` : ''}

        <!-- 页脚 -->
        <div class="footer">
          <div style="display: inline-flex; align-items: center; gap: 8px;">
            <span>✨ Powered By PVV</span>
            <span>•</span>
            <span>${new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' })}</span>
          </div>
        </div>
      </div>
    `;

    cardContainer.appendChild(contentDiv);

    // 等待DOM更新
    await new Promise(resolve => setTimeout(resolve, 100));

    // 使用html2canvas时应用选择的缩放系数
    const canvas = await html2canvas(cardContainer, {
      scale: scaleFactor,
      useCORS: true,
      backgroundColor: null
    });

    // 转换canvas为base64图片数据
    const imageData = canvas.toDataURL('image/png');
    previewUrl.value = imageData;

    // 从DOM中移除临时元素
    document.body.removeChild(cardContainer);

    // 生成预览后自动滚动到顶部
    if (previewContent.value) {
      previewContent.value.scrollTop = 0;
    }

  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('生成预览失败：' + error.message);
  } finally {
    generatingPreview.value = false;
  }
};

// 完善卡片导出功能
const exportCard = async () => {
  try {
    if (!previewUrl.value) {
      ElMessage.warning('请先生成预览');
      return;
    }

    exporting.value = true;

    // 获取文件名（使用角色名称）
    const fileName = `${props.entity?.name || '角色'}.png`;

    // 调用后端API获取保存目录
    const response = await window.pywebview.api.select_directory();

    const result = typeof response === 'string' ? JSON.parse(response) : response;

    // 如果用户取消，则退出
    if (!result || result.status !== 'success' || !result.data) {
      exporting.value = false;
      ElMessage.error("取消导出");
      return;
    }

    // 构建完整的文件路径
    const directory = result.data;
    const filePath = `${directory}/${fileName}`;

    // 使用已有的save_entity_card方法保存卡片
    const saveResponse = await window.pywebview.api.save_entity_card({
      file_path: filePath,
      image_data: previewUrl.value
    });

    // 处理后端返回的JSON字符串
    const saveResult = typeof saveResponse === 'string' ? JSON.parse(saveResponse) : saveResponse;

    if (saveResult && saveResult.status === 'success') {
      ElMessage.success(`卡片已成功导出至: ${filePath}`);

      // 确保通过事件通知父组件
      emit('export-success', filePath);

      // 可选：打开文件所在位置
      try {
        await window.pywebview.api.open_directory(directory);
      } catch (error) {
        console.warn('无法打开文件目录:', error);
      }
    } else {
      ElMessage.error('导出失败：' + (saveResult?.message || '未知错误'));
    }
  } catch (error) {
    console.error('导出卡片时出错:', error);
    ElMessage.error('导出失败：' + (error.message || '未知错误'));
  } finally {
    exporting.value = false;
  }
};

// 监听主题变化，自动更新预览
watch(currentTheme, () => {
  if (previewUrl.value) {
    // 如果已经有预览，切换主题时自动重新生成
    generatePreview();
  }
});
</script>

<style lang="scss" scoped>
.entity-card-exporter {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  user-select: none;

  .exporter-container {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden; /* 确保容器本身不滚动 */
    min-height: 0; /* 重要：允许flex子项收缩 */
  }
}

// 左侧控制面板
.control-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid;
  min-height: 0; /* 重要：允许flex子项收缩 */

  .panel-header {
    padding: 20px;
    border-bottom: 1px solid;
    flex-shrink: 0; /* 头部不收缩 */

    .panel-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }

  // 固定的主题选择标题
  .theme-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px 20px;
    flex-shrink: 0; /* 固定不滚动 */

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin: 0;
    }

    .manage-btn {
      background: none;
      border: 1px solid;
      font-size: 12px;
      cursor: pointer;
      padding: 6px 12px;
      border-radius: 6px;
      transition: all 0.2s;
      font-weight: 500;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  // 可滚动的主题列表容器
  .theme-list-container {
    flex: 1;
    overflow-y: auto; /* 只有这个区域滚动 */
    min-height: 0;
    padding: 0 20px;

    .theme-list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
      padding: 4px;

      .theme-item {
        cursor: pointer;
        text-align: center;
        transition: all 0.2s;
        border-radius: 6px;
        padding: 6px;
        border: 1px solid transparent;

        &:hover {
          transform: translateY(-1px);
        }

        &.active {
          border-color: currentColor;

          .theme-preview {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
        }

        .theme-preview {
          width: 100%;
          height: 50px;
          border-radius: 4px;
          border: 1px solid rgba(0, 0, 0, 0.1);
          position: relative;
          overflow: hidden;
          margin-bottom: 6px;
          transition: all 0.2s;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          .theme-avatar {
            position: absolute;
            top: 4px;
            left: 4px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 8px;
            font-weight: bold;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
          }

          .theme-card {
            position: absolute;
            bottom: 4px;
            right: 4px;
            width: 20px;
            height: 12px;
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }
        }

        .theme-name {
          font-size: 11px;
          font-weight: 500;
          transition: color 0.2s;
          line-height: 1.2;
        }

        .theme-pattern {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          pointer-events: none;

          svg {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  // 固定的导出选项区域
  .export-options-section {
    padding: 16px 20px 20px 20px;
    flex-shrink: 0; /* 固定不滚动 */
    border-top: 1px solid;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin: 0 0 16px 0;
    }

    .option-list {
      .option-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        cursor: pointer;

        input[type="checkbox"] {
          margin-right: 8px;
          width: 16px;
          height: 16px;
        }

        .option-text {
          font-size: 14px;
          flex: 1;
        }

        .option-label {
          font-size: 14px;
          margin-right: 12px;
        }

        .quality-select {
          padding: 4px 8px;
          border-radius: 4px;
          border: 1px solid;
          font-size: 14px;
          min-width: 80px;
        }
      }
    }
  }

  .panel-footer {
    padding: 20px;
    border-top: 1px solid;
    flex-shrink: 0; /* 底部不收缩 */

    .generate-btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;

      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }

      &.loading {
        cursor: wait;
      }

      &:not(:disabled):hover {
        transform: translateY(-1px);
      }
    }
  }
}

// 右侧预览区域
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 重要：允许flex子项收缩 */
  min-height: 0; /* 重要：允许flex子项收缩 */

  .preview-header {
    padding: 20px;
    border-bottom: 1px solid;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0; /* 头部不收缩 */

    .panel-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .export-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;

      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }

      &.loading {
        cursor: wait;
      }

      &:not(:disabled):hover {
        transform: translateY(-1px);
      }
    }
  }

  .preview-content {
    flex: 1;
    overflow: auto; /* 只有这个区域可以滚动 */
    padding: 20px;
    min-height: 0; /* 重要：允许内容区域收缩 */

    .preview-container {
      display: flex;
      justify-content: center;
      min-height: 100%; /* 确保容器至少占满可视区域 */

      .preview-image {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        display: block; /* 确保图片是块级元素 */
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 300px; /* 给空状态一个最小高度 */

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 14px;
        margin: 0;
        opacity: 0.7;
      }
    }
  }
}

// 浅色主题
.light .entity-card-exporter {
  .control-panel {
    background: #ffffff;
    border-right-color: #e4e7ed;
    color: #303133;

    .panel-header {
      background: #ffffff;
      border-bottom-color: #e4e7ed;

      .panel-title {
        color: #303133;
      }
    }

    .section-title {
      color: #303133;
    }

    .manage-btn {
      color: #409eff;
      background: rgba(64, 158, 255, 0.1);
      border-color: rgba(64, 158, 255, 0.3);

      &:hover {
        background: rgba(64, 158, 255, 0.2);
        border-color: #409eff;
        color: #409eff;
      }
    }

    .theme-item {
        &:hover {
          background: rgba(0, 0, 0, 0.04);
        }

        &.active {
          background: rgba(64, 158, 255, 0.1);
        }

        .theme-name {
          color: #606266;
        }

        &.active .theme-name {
          color: #409eff;
          font-weight: 600;
        }
      }

      .option-item {
        color: #303133;

        input[type="checkbox"] {
          accent-color: #409eff;
        }

        .quality-select {
          background: #ffffff;
          border-color: #dcdfe6;
          color: #303133;

          &:focus {
            border-color: #409eff;
            outline: none;
          }
        }
      }
    }

    .panel-footer {
      background: #ffffff;
      border-top-color: #e4e7ed;

      .generate-btn {
        background: #409eff;
        color: #ffffff;

        &:hover:not(:disabled) {
          background: #66b1ff;
        }

        &:active:not(:disabled) {
          background: #3a8ee6;
        }
      }
    }
  }

  .preview-panel {
    background: #f5f7fa;

    .preview-header {
      background: #ffffff;
      border-bottom-color: #e4e7ed;

      .panel-title {
        color: #303133;
      }

      .export-btn {
        background: #67c23a;
        color: #ffffff;

        &:hover:not(:disabled) {
          background: #85ce61;
        }

        &:active:not(:disabled) {
          background: #5daf34;
        }
      }
    }

    .preview-content {
      .empty-state {
        color: #909399;
      }
    }

}

// 深色主题
.dark .entity-card-exporter {
  .control-panel {
    background: #1a1a1a;
    border-right-color: #333333;
    color: #e0e0e0;

    .panel-header {
      background: #1a1a1a;
      border-bottom-color: #333333;

      .panel-title {
        color: #e0e0e0;
      }
    }

    .section-title {
      color: #e0e0e0;
    }

    .manage-btn {
      color: #4080ff;
      background: rgba(64, 128, 255, 0.15);
      border-color: rgba(64, 128, 255, 0.4);

      &:hover {
        background: rgba(64, 128, 255, 0.25);
        border-color: #4080ff;
        color: #4080ff;
      }
    }

    .theme-item {
        &:hover {
          background: rgba(255, 255, 255, 0.06);
        }

        &.active {
          background: rgba(64, 128, 255, 0.15);
        }

        .theme-name {
          color: #b0b0b0;
        }

        &.active .theme-name {
          color: #4080ff;
          font-weight: 600;
        }
      }

      .option-item {
        color: #e0e0e0;

        input[type="checkbox"] {
          accent-color: #4080ff;
        }

        .quality-select {
          background: #2a2a2a;
          border-color: #474747;
          color: #e0e0e0;

          &:focus {
            border-color: #4080ff;
            outline: none;
          }

          option {
            background: #2a2a2a;
            color: #e0e0e0;
          }
        }
      }
    }

    .panel-footer {
      background: #1a1a1a;
      border-top-color: #333333;

      .generate-btn {
        background: #4080ff;
        color: #ffffff;

        &:hover:not(:disabled) {
          background: #5c93ff;
        }

        &:active:not(:disabled) {
          background: #3366cc;
        }
      }
    }
  }

  .preview-panel {
    background: #242424;

    .preview-header {
      background: #1a1a1a;
      border-bottom-color: #333333;

      .panel-title {
        color: #e0e0e0;
      }

      .export-btn {
        background: #67c23a;
        color: #ffffff;

        &:hover:not(:disabled) {
          background: #85ce61;
        }

        &:active:not(:disabled) {
          background: #5daf34;
        }
      }
    }

    .preview-content {
      .empty-state {
        color: #808080;
      }
    }

}

// 主题管理对话框样式
.theme-manager {
  .add-theme-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .manage-themes {
    .theme-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border: 1px solid;
      border-radius: 8px;
      margin-bottom: 12px;
      transition: all 0.2s;

      .theme-info {
        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
        }

        p {
          margin: 0;
          font-size: 12px;
          font-family: 'Courier New', monospace;
          opacity: 0.7;
        }
      }

      .theme-actions {
        display: flex;
        gap: 8px;
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px;
      font-size: 14px;
      border: 1px dashed;
      border-radius: 8px;
      opacity: 0.7;
    }
  }
}

// 浅色主题对话框 - 使用更强的选择器
.light {
  :deep(.el-dialog),
  :deep(.el-overlay-dialog .el-dialog) {
    background-color: #ffffff !important;
    border: 1px solid #e4e7ed !important;

    .el-dialog__header {
      background-color: #ffffff !important;
      border-bottom: 1px solid #e4e7ed !important;

      .el-dialog__title {
        color: #303133 !important;
      }

      .el-dialog__headerbtn .el-dialog__close {
        color: #909399 !important;

        &:hover {
          color: #303133 !important;
        }
      }
    }

    .el-dialog__body {
      background-color: #ffffff !important;
      color: #303133 !important;
    }
  }

  .theme-manager {
    background-color: #ffffff;
    color: #303133;

    :deep(.el-tabs) {
      .el-tabs__header {
        background-color: #ffffff;

        .el-tabs__nav {
          background-color: #ffffff;
          border: 1px solid #e4e7ed;

          .el-tabs__item {
            background-color: #f5f7fa;
            color: #606266;
            border-right: 1px solid #e4e7ed;

            &.is-active {
              background-color: #ffffff;
              color: #303133;
            }

            &:hover {
              color: #303133;
            }
          }
        }
      }

      .el-tabs__content {
        background-color: #ffffff;
        color: #303133;
      }
    }

    .add-theme-form {
      :deep(.el-form-item__label) {
        color: #303133;
      }

      :deep(.el-input__wrapper) {
        background-color: #ffffff;
        border: 1px solid #dcdfe6;

        .el-input__inner {
          color: #303133;

          &::placeholder {
            color: #c0c4cc;
          }
        }
      }

      :deep(.el-textarea__inner) {
        background-color: #ffffff;
        border: 1px solid #dcdfe6;
        color: #303133;

        &::placeholder {
          color: #c0c4cc;
        }
      }
    }

    .manage-themes {
      .theme-item {
        background-color: #ffffff;
        border-color: #e4e7ed;

        &:hover {
          background-color: #f5f7fa;
          border-color: #c6e2ff;
        }

        .theme-info {
          h4 {
            color: #303133;
          }

          p {
            color: #606266;
          }
        }
      }

      .empty-state {
        background-color: #ffffff;
        border-color: #e4e7ed;
        color: #909399;
      }
    }
  }
}

// 深色主题对话框 - 使用更强的选择器
.dark {
  :deep(.el-dialog),
  :deep(.el-overlay-dialog .el-dialog) {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;

    .el-dialog__header {
      background-color: #1a1a1a !important;
      border-bottom: 1px solid #333333 !important;

      .el-dialog__title {
        color: #e0e0e0 !important;
      }

      .el-dialog__headerbtn .el-dialog__close {
        color: #b0b0b0 !important;

        &:hover {
          color: #e0e0e0 !important;
        }
      }
    }

    .el-dialog__body {
      background-color: #1a1a1a !important;
      color: #e0e0e0 !important;
    }
  }

  .theme-manager {
    background-color: #1a1a1a;
    color: #e0e0e0;

    :deep(.el-tabs) {
      .el-tabs__header {
        background-color: #1a1a1a;

        .el-tabs__nav {
          background-color: #1a1a1a;
          border: 1px solid #333333;

          .el-tabs__item {
            background-color: #2a2a2a;
            color: #b0b0b0;
            border-right: 1px solid #333333;

            &.is-active {
              background-color: #1a1a1a;
              color: #e0e0e0;
            }

            &:hover {
              color: #e0e0e0;
            }
          }
        }
      }

      .el-tabs__content {
        background-color: #1a1a1a;
        color: #e0e0e0;
      }
    }

    .add-theme-form {
      :deep(.el-form-item__label) {
        color: #e0e0e0;
      }

      :deep(.el-input__wrapper) {
        background-color: #2a2a2a;
        border: 1px solid #474747;

        .el-input__inner {
          color: #e0e0e0;
          background-color: transparent;

          &::placeholder {
            color: #808080;
          }
        }
      }

      :deep(.el-textarea__inner) {
        background-color: #2a2a2a;
        border: 1px solid #474747;
        color: #e0e0e0;

        &::placeholder {
          color: #808080;
        }
      }
    }

    .manage-themes {
      .theme-item {
        background-color: #2a2a2a;
        border-color: #474747;

        &:hover {
          background-color: #363636;
          border-color: #79a6ff;
        }

        .theme-info {
          h4 {
            color: #e0e0e0;
          }

          p {
            color: #b0b0b0;
          }
        }
      }

      .empty-state {
        background-color: #2a2a2a;
        border-color: #474747;
        color: #808080;
      }
    }
  }
}

// 深色主题消息框样式
.dark :deep(.el-message-box) {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;

  .el-message-box__header {
    background-color: #1a1a1a !important;
    border-bottom-color: #333333 !important;

    .el-message-box__title {
      color: #e0e0e0 !important;
    }
  }

  .el-message-box__content {
    background-color: #1a1a1a !important;
    color: #e0e0e0 !important;

    h3, h4 {
      color: #e0e0e0 !important;
    }

    p, li {
      color: #b0b0b0 !important;
    }

    strong {
      color: #e0e0e0 !important;
    }

    ul li {
      margin-bottom: 4px;
    }
  }

  .el-message-box__btns {
    background-color: #1a1a1a !important;
    border-top-color: #333333 !important;
  }
}
</style>

<!-- 全局样式，确保对话框主题正确应用 -->
<style lang="scss">
// 浅色主题全局对话框样式
.light {
  .el-dialog {
    background-color: #ffffff !important;
    border: 1px solid #e4e7ed !important;

    .el-dialog__header {
      background-color: #ffffff !important;
      border-bottom: 1px solid #e4e7ed !important;

      .el-dialog__title {
        color: #303133 !important;
      }

      .el-dialog__headerbtn .el-dialog__close {
        color: #909399 !important;

        &:hover {
          color: #303133 !important;
        }
      }
    }

    .el-dialog__body {
      background-color: #ffffff !important;
      color: #303133 !important;
    }
  }

  // Element Plus 标签页样式
  .el-tabs {
    .el-tabs__header {
      background-color: #ffffff !important;

      .el-tabs__nav {
        background-color: #ffffff !important;
        border: 1px solid #e4e7ed !important;

        .el-tabs__item {
          background-color: #f5f7fa !important;
          color: #606266 !important;
          border-right: 1px solid #e4e7ed !important;

          &.is-active {
            background-color: #ffffff !important;
            color: #303133 !important;
          }

          &:hover {
            color: #303133 !important;
          }
        }
      }
    }

    .el-tabs__content {
      background-color: #ffffff !important;
      color: #303133 !important;
    }
  }

  // Element Plus 表单样式
  .el-form-item__label {
    color: #303133 !important;
  }

  .el-input__wrapper {
    background-color: #ffffff !important;
    border: 1px solid #dcdfe6 !important;

    .el-input__inner {
      color: #303133 !important;

      &::placeholder {
        color: #c0c4cc !important;
      }
    }
  }

  .el-textarea__inner {
    background-color: #ffffff !important;
    border: 1px solid #dcdfe6 !important;
    color: #303133 !important;

    &::placeholder {
      color: #c0c4cc !important;
    }
  }
}

// 深色主题全局对话框样式
.dark {
  .el-dialog {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;

    .el-dialog__header {
      background-color: #1a1a1a !important;
      border-bottom: 1px solid #333333 !important;

      .el-dialog__title {
        color: #e0e0e0 !important;
      }

      .el-dialog__headerbtn .el-dialog__close {
        color: #b0b0b0 !important;

        &:hover {
          color: #e0e0e0 !important;
        }
      }
    }

    .el-dialog__body {
      background-color: #1a1a1a !important;
      color: #e0e0e0 !important;
    }
  }

  // Element Plus 标签页样式
  .el-tabs {
    .el-tabs__header {
      background-color: #1a1a1a !important;

      .el-tabs__nav {
        background-color: #1a1a1a !important;
        border: 1px solid #333333 !important;

        .el-tabs__item {
          background-color: #2a2a2a !important;
          color: #b0b0b0 !important;
          border-right: 1px solid #333333 !important;

          &.is-active {
            background-color: #1a1a1a !important;
            color: #e0e0e0 !important;
          }

          &:hover {
            color: #e0e0e0 !important;
          }
        }
      }
    }

    .el-tabs__content {
      background-color: #1a1a1a !important;
      color: #e0e0e0 !important;
    }
  }

  // Element Plus 表单样式
  .el-form-item__label {
    color: #e0e0e0 !important;
  }

  .el-input__wrapper {
    background-color: #2a2a2a !important;
    border: 1px solid #474747 !important;

    .el-input__inner {
      color: #e0e0e0 !important;
      background-color: transparent !important;

      &::placeholder {
        color: #808080 !important;
      }
    }
  }

  .el-textarea__inner {
    background-color: #2a2a2a !important;
    border: 1px solid #474747 !important;
    color: #e0e0e0 !important;

    &::placeholder {
      color: #808080 !important;
    }
  }

  // Element Plus 按钮样式
  .el-button {
    &--primary {
      background-color: #4080ff !important;
      border-color: #4080ff !important;

      &:hover {
        background-color: #5c93ff !important;
        border-color: #5c93ff !important;
      }
    }

    &--danger {
      background-color: #f56c6c !important;
      border-color: #f56c6c !important;

      &:hover {
        background-color: #f78989 !important;
        border-color: #f78989 !important;
      }
    }
  }
}
</style>
