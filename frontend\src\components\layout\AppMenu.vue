<template>
  <el-aside :width="isCollapse ? '64px' : '200px'" class="app-menu native-app-style" :class="{ 'menu-collapsed': isCollapse }" @contextmenu.prevent>
    <div class="menu-header" @contextmenu.prevent>
      <el-button link @click="toggleCollapse" class="collapse-btn" @contextmenu.prevent>
        <el-icon :size="20">
          <Fold v-if="!isCollapse"/>
          <Expand v-else/>
        </el-icon>
      </el-button>
    </div>
    <div class="menu-content" @contextmenu.prevent>
      <el-menu
        :collapse="isCollapse"
        :default-active="activeMenu"
        router
        :unique-opened="true"
        :collapse-transition="false"
        @select="handleSelect"
        @open="handleSubmenuOpen"
        @close="handleSubmenuClose"
        @contextmenu.prevent
        class="native-app-style"
      >
        <template v-for="item in menuList" :key="item.path">
          <template v-if="item.children">
            <el-sub-menu 
              :index="item.path"
              popper-class="custom-submenu-popup"
            >
              <template #title>
                <el-icon><component :is="item.icon" /></el-icon>
                <span>{{ item.title }}</span>
              </template>
              <el-menu-item
                v-for="child in item.children.filter(child => !child.hidden)"
                :key="child.path"
                :index="child.path"
              >
                <template #default>
                  <span>{{ child.title }}</span>
                </template>
              </el-menu-item>
            </el-sub-menu>
          </template>

          <el-menu-item v-else-if="!item?.hidden" :index="item.path">
            <template #default>
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.title }}</span>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
    <div class="menu-footer" @contextmenu.prevent>
      <el-menu
        :collapse="isCollapse"
        :collapse-transition="false"
        :default-active="activeMenu"
        @contextmenu.prevent
        class="native-app-style"
      >
        <el-menu-item @click="toggleTheme">
          <el-icon>
            <Moon v-if="isDarkTheme"/>
            <Sunny v-else/>
          </el-icon>
          <template #title>{{ isDarkTheme ? '深色模式' : '浅色模式' }}</template>
        </el-menu-item>
        <el-menu-item index="/settings/app" @click="router.push('/settings/app')">
          <el-icon><Setting /></el-icon>
          <template #title>设置</template>
        </el-menu-item>
        <el-menu-item @click="handleLogout" class="danger">
          <el-icon><SwitchButton /></el-icon>
          <template #title>退出登录</template>
        </el-menu-item>
      </el-menu>
    </div>
  </el-aside>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useConfigStore } from '@/stores/config'
import { Fold, Expand, Setting, Moon, Sunny, SwitchButton } from '@element-plus/icons-vue'
import { menuList } from '@/config/menuConfig'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const configStore = useConfigStore()

const isCollapse = ref(true)

const activeMenu = computed(() => {
  return route.path
})

const isDarkTheme = computed(() => configStore.theme === 'dark')

const expandedMenus = ref(new Set())

const toggleSubmenu = async (menuKey) => {
  const isExpanded = expandedMenus.value.has(menuKey)
  if (isExpanded) {
    expandedMenus.value.delete(menuKey)
  } else {
    expandedMenus.value.add(menuKey)
  }
  
  // 获取子菜单容器
  const submenuContent = document.querySelector(`[data-menu="${menuKey}"] .submenu-content`)
  if (!submenuContent) return
  
  if (!isExpanded) {
    // 展开时，先设置实际高度
    submenuContent.style.height = 'auto'
    const height = submenuContent.offsetHeight
    submenuContent.style.height = '0'
    
    // 强制重绘
    submenuContent.offsetHeight
    
    // 设置目标高度以触发动画
    submenuContent.style.height = height + 'px'
  } else {
    // 收起时，先设置当前高度
    const height = submenuContent.offsetHeight
    submenuContent.style.height = height + 'px'
    
    // 强制重绘
    submenuContent.offsetHeight
    
    // 设置高度为0以触发动画
    submenuContent.style.height = '0'
  }
}

// 判断子菜单是否展开
const isSubmenuExpanded = (menuKey) => {
  return expandedMenus.value.has(menuKey)
}

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 处理菜单选择
const handleSelect = async (path) => {
  try {
    if (path) {
      await router.push(path)
    }
  } catch (error) {
    console.error('Navigation error:', error)
  }
}

const toggleTheme = async () => {
  const newTheme = isDarkTheme.value ? 'light' : 'dark'
  await configStore.updateConfigItem('theme', newTheme)
  document.querySelector('html').className = newTheme
}

const handleLogout = async () => {
  await userStore.logout()
  router.push('/login')
}

// 优化子菜单展开/收起的处理
const handleSubmenuOpen = (index) => {
  nextTick(() => {
    const submenu = document.querySelector(`.el-sub-menu[data-index="${index}"]`)
    if (submenu) {
      const popup = submenu.querySelector('.el-menu--popup')
      if (popup) {
        // 使用 class 来控制动画
        requestAnimationFrame(() => {
          popup.classList.add('visible')
        })
      }
    }
  })
}

const handleSubmenuClose = (index) => {
  const submenu = document.querySelector(`.el-sub-menu[data-index="${index}"]`)
  if (submenu) {
    const popup = submenu.querySelector('.el-menu--popup')
    if (popup) {
      popup.classList.remove('visible')
    }
  }
}
</script>

<style>
/* 原生应用通用样式 */
.native-app-style {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 禁用图片和媒体元素拖拽 */
.native-app-style img,
.native-app-style svg,
.native-app-style canvas,
.native-app-style video,
.native-app-style audio {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
}

/* 按钮和交互元素恢复指针事件 */
.native-app-style button,
.native-app-style .el-button,
.native-app-style input,
.native-app-style textarea,
.native-app-style select,
.native-app-style .el-input,
.native-app-style .el-select,
.native-app-style .el-textarea {
  pointer-events: auto;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 输入框内容可以选择 */
.native-app-style input,
.native-app-style textarea,
.native-app-style .el-input__inner,
.native-app-style .el-textarea__inner {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.app-menu {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  display: flex;
  flex-direction: column;
  height: 100vh;
  border-right: 1px solid var(--el-border-color);
  transition: width 0.3s;
  background-color: var(--el-menu-bg-color);
}

/* 修复子菜单弹出框样式 */
:deep(.el-menu--popup) {
  min-width: 160px;
  margin-top: 0 !important;
  padding: 5px 0;
  background-color: var(--el-menu-bg-color) !important;
  border: 1px solid var(--el-border-color);
  z-index: 2000;
}

:deep(.el-menu--popup-container) {
  padding: 0;
}

:deep(.el-sub-menu__title) {
  padding-left: 20px !important;
  color: var(--el-menu-text-color);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

/* 菜单项样式 - 应用原生应用样式 */
:deep(.el-menu-item) {
  color: var(--el-menu-text-color);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

:deep(.el-menu-item:hover) {
  background-color: var(--el-menu-hover-bg-color);
}

:deep(.el-menu-item.is-active) {
  color: var(--el-menu-active-color);
  background-color: var(--el-menu-active-bg-color);
}

/* 子菜单项样式 - 应用原生应用样式 */
:deep(.el-menu--popup .el-menu-item) {
  padding: 0 20px !important;
  min-width: 160px;
  color: var(--el-menu-text-color);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

:deep(.el-menu--popup .el-menu-item:hover) {
  color: var(--el-menu-hover-text-color);
  background-color: var(--el-menu-hover-bg-color);
}

:deep(.el-menu--popup .el-menu-item.is-active) {
  color: var(--el-menu-active-color);
  background-color: var(--el-menu-active-bg-color);
}

.menu-collapsed :deep(.el-menu--popup) {
  margin-left: 0;
}

/* 自定义气泡子菜单样式 */
.custom-submenu-popup {
  min-width: 160px !important;
  border-radius: 8px !important;
  background: var(--el-bg-color) !important;
  border: 1px solid var(--el-border-color) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  padding: 4px !important;
  margin: 4px !important;
  z-index: 2000 !important;
}

.dark .custom-submenu-popup {
  background: var(--el-bg-color) !important;
  border-color: var(--el-border-color) !important;
}

.custom-submenu-popup .el-menu {
  background: transparent !important;
  border: none !important;
}

.custom-submenu-popup .el-menu-item {
  height: 36px !important;
  line-height: 36px !important;
  margin: 2px 0 !important;
  padding: 0 12px !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  color: var(--el-text-color-primary) !important;
  transition: all 0.2s ease !important;
}

.custom-submenu-popup .el-menu-item:hover {
  color: var(--el-color-primary) !important;
  background: var(--el-menu-hover-bg-color) !important;
}

.custom-submenu-popup .el-menu-item.is-active {
  color: var(--el-color-primary) !important;
  background: var(--el-menu-active-bg-color) !important;
}

/* 确保文本内容可见 */
.custom-submenu-popup .el-menu-item > * {
  position: relative;
  z-index: 2;
}

/* 底部菜单样式 */
.menu-footer .el-menu {
  background: transparent !important;
  border: none !important;
}

.menu-footer .el-menu-item {
  height: 40px !important;
  line-height: 40px !important;
  margin: 2px 0 !important;
  padding: 0 16px !important;
  border-radius: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.menu-footer .el-menu-item:hover {
  color: var(--el-color-primary) !important;
  background: rgba(var(--el-color-primary-rgb), 0.1) !important;
}

.dark .menu-footer .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.menu-footer .el-menu-item.danger:hover {
  color: var(--el-color-danger) !important;
  background: rgba(var(--el-color-danger-rgb), 0.1) !important;
}

.app-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(var(--el-bg-color-rgb), 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-right: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .app-menu {
  background: rgba(0, 0, 0, 0.4);
  border-right-color: rgba(255, 255, 255, 0.05);
}

.menu-header {
  flex-shrink: 0;
  height: var(--app-header-height, 50px);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(var(--el-bg-color-rgb), 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  position: relative;
  z-index: 1;
}

.dark .menu-header {
  background: rgba(0, 0, 0, 0.4);
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

.collapse-btn {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--el-text-color-regular);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.collapse-btn:hover {
  background: rgba(var(--el-color-primary-rgb), 0.1);
  color: var(--el-color-primary);
}

.dark .collapse-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.collapse-btn .el-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
}

.menu-collapsed .collapse-btn .el-icon {
  transform: rotate(180deg);
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 重要：确保flex布局正确计算高度 */
}

.menu-content :deep(.el-menu) {
  border-right: none;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.menu-content :deep(.el-menu--collapse) {
  width: 64px;
}

.menu-footer {
  flex-shrink: 0;
  border-top: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  padding: 4px;
}

.menu-footer :deep(.el-menu) {
  border-right: none;
}

/* 确保菜单项高度合适 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  height: 44px;
  line-height: 44px;
}

/* 优化折叠状态下的图标对齐 */
:deep(.el-menu--collapse .el-menu-item),
:deep(.el-menu--collapse .el-sub-menu__title) {
  padding: 0 !important;
  justify-content: center;
}

:deep(.el-menu--collapse .el-menu-item .el-icon),
:deep(.el-menu--collapse .el-sub-menu__title .el-icon) {
  margin: 0;
}

/* 确保内容不会溢出 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 优化菜单项过渡效果 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.el-menu--collapse .el-menu-item),
:deep(.el-menu--collapse .el-sub-menu__title) {
  padding: 0 !important;
  justify-content: center;
}

:deep(.el-menu-item.is-active) {
  background-color: var(--el-menu-active-bg-color);
}

/* 确保文字不可选择但保持可点击 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

/* 所有图标元素应用原生应用样式 */
:deep(.el-icon),
:deep(.el-menu-item .el-icon),
:deep(.el-sub-menu__title .el-icon),
:deep(.el-button .el-icon) {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
}

/* SVG 元素特殊处理 */
:deep(svg) {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
}
</style>

<style scoped>
:deep(.el-menu .el-sub-menu__title) {
  position: relative;
  z-index: 1;
}

:deep(.el-sub-menu__hide-arrow .el-sub-menu__icon-arrow) {
  display: none !important;
}
</style>