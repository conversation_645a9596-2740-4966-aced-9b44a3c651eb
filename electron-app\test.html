<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PVV Electron 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007acc;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .info {
            margin-bottom: 15px;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 4px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PVV Electron 应用测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong> 这是一个简单的测试页面，用于验证 Electron 应用的基本功能是否正常工作。
        </div>

        <div class="test-section">
            <h3>📱 平台信息</h3>
            <button onclick="testPlatformInfo()">获取平台信息</button>
            <div id="platform-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>⚙️ 应用信息</h3>
            <button onclick="testAppInfo()">获取应用信息</button>
            <div id="app-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔧 配置管理</h3>
            <button onclick="testConfig()">测试配置</button>
            <button onclick="testUpdateConfig()">更新配置</button>
            <div id="config-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📁 项目管理</h3>
            <button onclick="testProjects()">获取项目列表</button>
            <button onclick="testCreateProject()">创建测试项目</button>
            <div id="project-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🤖 AI 功能</h3>
            <button onclick="testAIRoles()">获取AI角色</button>
            <button onclick="testAIProviders()">获取AI服务商</button>
            <div id="ai-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💾 备份功能</h3>
            <button onclick="testBackupList()">获取备份列表</button>
            <button onclick="testCreateBackup()">创建备份</button>
            <div id="backup-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>👤 用户功能</h3>
            <button onclick="testActivationStatus()">检查激活状态</button>
            <button onclick="testMachineCode()">获取机器码</button>
            <div id="user-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 工具函数
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }

        function showError(elementId, error) {
            showResult(elementId, `错误: ${error.message || error}`, true);
        }

        // 平台信息测试
        async function testPlatformInfo() {
            try {
                const info = {
                    platform: window.platform,
                    isDev: window.isDev,
                    userAgent: navigator.userAgent
                };
                showResult('platform-result', info);
            } catch (error) {
                showError('platform-result', error);
            }
        }

        // 应用信息测试
        async function testAppInfo() {
            try {
                const result = await window.electronAPI.invoke('get_app_info');
                showResult('app-result', result);
            } catch (error) {
                showError('app-result', error);
            }
        }

        // 配置测试
        async function testConfig() {
            try {
                const result = await window.pywebview.api.get_settings();
                showResult('config-result', result);
            } catch (error) {
                showError('config-result', error);
            }
        }

        async function testUpdateConfig() {
            try {
                const result = await window.pywebview.api.update_config('test.timestamp', new Date().toISOString());
                showResult('config-result', result);
            } catch (error) {
                showError('config-result', error);
            }
        }

        // 项目测试
        async function testProjects() {
            try {
                const result = await window.pywebview.api.get_projects();
                showResult('project-result', result);
            } catch (error) {
                showError('project-result', error);
            }
        }

        async function testCreateProject() {
            try {
                const projectData = {
                    name: '测试项目 ' + Date.now(),
                    description: '这是一个测试项目',
                    author: '测试用户'
                };
                const result = await window.pywebview.api.create_project(projectData);
                showResult('project-result', result);
            } catch (error) {
                showError('project-result', error);
            }
        }

        // AI功能测试
        async function testAIRoles() {
            try {
                const result = await window.pywebview.api.get_ai_roles();
                showResult('ai-result', result);
            } catch (error) {
                showError('ai-result', error);
            }
        }

        async function testAIProviders() {
            try {
                const result = await window.pywebview.api.get_ai_providers();
                showResult('ai-result', result);
            } catch (error) {
                showError('ai-result', error);
            }
        }

        // 备份功能测试
        async function testBackupList() {
            try {
                const result = await window.pywebview.api.get_backup_list();
                showResult('backup-result', result);
            } catch (error) {
                showError('backup-result', error);
            }
        }

        async function testCreateBackup() {
            try {
                const result = await window.pywebview.api.create_backup({
                    name: '测试备份',
                    description: '手动创建的测试备份'
                });
                showResult('backup-result', result);
            } catch (error) {
                showError('backup-result', error);
            }
        }

        // 用户功能测试
        async function testActivationStatus() {
            try {
                const result = await window.pywebview.api.check_activation_status();
                showResult('user-result', result);
            } catch (error) {
                showError('user-result', error);
            }
        }

        async function testMachineCode() {
            try {
                const result = await window.pywebview.api.get_machine_code();
                showResult('user-result', result);
            } catch (error) {
                showError('user-result', error);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成');
            console.log('可用的API:', {
                electronAPI: !!window.electronAPI,
                pywebview: !!window.pywebview,
                platform: !!window.platform
            });
        });
    </script>
</body>
</html>
