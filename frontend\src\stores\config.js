import { defineStore } from 'pinia'
import { ref, computed, reactive, readonly } from 'vue'

// 这里的配置对象和后台的json文件是对应的，如果后台修改backup/config/settings.json，这里就需要修改了
export const useConfigStore = defineStore('config', () => {
  // 状态定义
  const state = reactive({
    config: {
      theme: 'light',
      loaded: false,
      customThemes: [],
      editor: {
        fontFamily: '汉仪旗黑',
        fontSize: 16,
        lineHeight: 1.6,
        contentWidth: 60,
        lastState: {
          bookId: null,
          volumeId: null,
          chapterId: null
        },
        bgEnabled: false,
        bgImage: '',
        bgOpacity: 58,
        volumeSortOrder: 'desc',
        chapterSortOrder: 'asc',
        contextMenus: [],
        aiAssistant: {
          position: { x: 100, y: 100 },
          size: { width: 400, height: 600 }
        },
        entitySelection: {
          enabled: false,
          selectedTemplateId: null,
          selectedEntityId: null
        }
      },
      // 添加搜索配置
      search: {
        providers: [
          {
            id: 'baidu',
            name: '百度搜索',
            url: 'https://www.baidu.com/s?wd={query}',
            isDefault: true
          },
          {
            id: 'google',
            name: 'Google搜索',
            url: 'https://www.google.com/search?q={query}',
            isDefault: false
          },
          {
            id: 'bing',
            name: '必应搜索',
            url: 'https://cn.bing.com/search?q={query}',
            isDefault: false
          },
          {
            id: 'wiki',
            name: '维基百科',
            url: 'https://zh.wikipedia.org/wiki/{query}',
            isDefault: false
          }
        ]
      },
      // 添加故事灵感系统配置
      storyInspiration: {
        categories: {
          theme: {
            name: "主题层",
            description: "故事的核心主题与情感基调",
            icon: "Sunrise",
            color: "primary",
            defaultCount: 2,
            maxCount: 5
          },
          volume: {
            name: "卷级结构",
            description: "故事的大纲架构与发展脉络",
            icon: "Connection",
            color: "success",
            defaultCount: 4,
            maxCount: 8
          },
          keyPoint: {
            name: "关键点",
            description: "故事中的重要转折与关键节点",
            icon: "Key",
            color: "warning",
            defaultCount: 5,
            maxCount: 8
          },
          technique: {
            name: "技法卡",
            description: "用于优化剧情的各种写作技巧",
            icon: "TrendCharts",
            color: "danger",
            defaultCount: 3,
            maxCount: 5
          }
        },
        // 其他元素集合会在初始化时从storyInspirationConfig填充
        theme: [],
        volume: [],
        keyPoint: [],
        technique: []
      },
      // 添加聊天界面配置
      chat: {
        fontSize: 14,
        fontFamily: '微软雅黑, sans-serif',
        codeBlockTheme: 'auto', // auto, light, dark
        codeHighlightStyle: 'tomorrow', // 代码高亮主题：tomorrow, tomorrow-night, atom-one-light, atom-one-dark, github, vs2015
        codeBlockFontSize: 15, // 代码块字体大小
      },
      // 添加Markdown编辑器配置
      markdownEditor: {
        // 持久化的目录选择
        lastSelectedDirectory: '',
        // 各区域的文本大小配置
        fontSize: {
          edit: 14,      // 编辑区域字体大小
          preview: 14,   // 预览区域字体大小
          mindmap: 12    // 思维导图区域字体大小
        },
        // 编辑器偏好设置
        preferences: {
          showSidebar: true,           // 是否显示侧边栏
          sidebarCollapsed: false,     // 侧边栏是否折叠
          defaultMode: 'edit',         // 默认模式：edit, preview, mindmap
          autoSave: true,              // 是否自动保存
          autoSaveInterval: 30000,     // 自动保存间隔（毫秒）
          wordWrap: true,              // 是否自动换行
          lineNumbers: true,           // 是否显示行号
          theme: 'auto'                // 编辑器主题：auto, light, dark
        },
        // 最近打开的文件
        recentFiles: [],
        // 窗口布局
        layout: {
          sidebarWidth: 320,           // 侧边栏宽度
          editorSplit: 50              // 编辑器分割比例（预览模式下）
        }
      },
      tts: {
        voice: 'zh-CN-XiaoxiaoNeural',
        rate: '+0%',
        volume: '+0%',
        pitch: '+0Hz'
      },
      chrome: {
        default_path: '',
        downloadDir: '',
        userDataDirs: []
      },
      oneapi:{
        exe:"",
        port:""
      },
      openai: {
        api_key: "",
        base_url: ""
      },
      feishu: {
        app_id: "",
        app_secret: "",
        encrypt_key: "",  // 可选
        verification_token: ""  // 可选
      },
      novel: {
        rules: {},
        download: {
          chapterCount: 20,
          intervalTime: 2,
          downloadPath: '',
          chromeConfig: {
            chromePath: '',
            userDataDir: '',
            proxy: ''
          }
        }
      },
      backup: {
        backupDir: '',  // 备份源目录
        targetDir: '',  // 备份目标目录
        autoBackup: false,  // 是否自动备份
        backupInterval: 'daily',  // 备份周期：daily/weekly/monthly
        keepBackups: 7,  // 保留备份数量
        lastBackupTime: null  // 上次备份时间
      },
      models: [],
      isModelsLoaded: false,
      selectedModel: '',
      // 添加 Git 配置部分
      git: {
        repoUrl: '',
        authType: 'token',  // 默认使用 token 认证方式
        username: '',
        password: '',
        token: '',
        tokenUsername: '',
        localPath: '',
        backupDir: '',
        autoBackup: false,
        backupInterval: 60,
        lastBackupTime: null
      }
    }
  })

  const loading = ref(false)
  const error = ref(null)

  // Getters
  const theme = computed(() => state.config.theme)
  const loaded = computed(() => state.config.loaded)
  const chrome = computed(() => state.config.chrome)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)
  const openai = computed(() => state.config.openai)
  const editor = computed(() => state.config.editor)
  const feishu = computed(() => state.config.feishu)
  const novel = computed(() => state.config.novel)
  const backup = computed(() => state.config.backup)
  const tts = computed(() => state.config.tts)
  const models = computed(() => state.config.models)
  const isModelsLoaded = computed(() => state.config.isModelsLoaded)
  const selectedModel = computed(() => state.config.selectedModel)
  const chat = computed(() => state.config.chat)
  const git = computed(() => state.config.git)
  const customThemes = computed(() => state.config.customThemes || [])
  const search = computed(() => state.config.search || { providers: [] })
  const markdownEditor = computed(() => state.config.markdownEditor)

  // Actions
  async function loadConfig() {
    try {
      loading.value = true
      error.value = null
      const result = await window.pywebview.api.get_settings()
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        // 确保config缓存是完整的
        state.config = {
          ...state.config,  // 保留默认值作为兜底
          ...response.data, // 将后端配置覆盖到默认值上
          loaded: true
        }
      } else {
        console.error('加载配置失败:', response.message || '未知错误')
      }
    } catch (error) {
      console.error('加载配置失败:', error)
      error.value = error.message
    } finally {
      loading.value = false
    }
  }

  // 安全递归合并工具 - 确保数组和对象都能正确处理
  function safeRecursiveMerge(target, source) {
    // 如果target或source不是对象，或者是null，直接返回source
    if (!target || !source || typeof target !== 'object' || typeof source !== 'object') {
      return source;
    }
    
    // 如果source是数组，直接返回source的拷贝
    if (Array.isArray(source)) {
      return [...source];
    }
    
    // 如果target是数组但source不是，转换target为对象
    const result = Array.isArray(target) ? {} : {...target};
    
    // 递归合并所有属性
    for (const key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        const sourceValue = source[key];
        const targetValue = result[key];
        
        // 如果是对象，则递归合并
        if (
          sourceValue !== null && 
          typeof sourceValue === 'object' &&
          targetValue !== null && 
          typeof targetValue === 'object' &&
          !Array.isArray(sourceValue)
        ) {
          result[key] = safeRecursiveMerge(targetValue, sourceValue);
        } else {
          // 否则直接替换
          result[key] = sourceValue !== undefined ? sourceValue : targetValue;
        }
      }
    }
    
    return result;
  }

  // 完全重写的saveConfig函数 - 确保安全合并
  async function saveConfig(partialConfig) {
    if (!partialConfig) {
      throw new Error('配置参数不能为空');
    }
    
    try {
      // 1. 从后端获取当前完整配置
      const result = await window.pywebview.api.get_settings();
      const response = typeof result === 'string' ? JSON.parse(result) : result;
      
      let completeConfig;
      
      if (response.status === 'success' && response.data) {
        // 2. 安全合并配置 - 将新配置合并到完整配置中
        completeConfig = safeRecursiveMerge(response.data, partialConfig);
      } else {
        // 如果无法获取后端配置，则将新配置合并到当前状态
        completeConfig = safeRecursiveMerge(state.config, partialConfig);
      }
      
      // 3. 保存完整配置到后端
      const saveResult = await window.pywebview.api.save_settings(completeConfig);
      const saveResponse = typeof saveResult === 'string' ? JSON.parse(saveResult) : saveResult;
      
      if (saveResponse.status === 'success') {
        // 4. 更新本地状态
        state.config = completeConfig;
        return true;
      } else {
        throw new Error(saveResponse.message || '保存配置失败');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      throw error;
    }
  }

  // 重写的updateConfigItem函数 - 最小化更新
  async function updateConfigItem(path, value) {
    try {
      if (!path) {
        throw new Error('配置路径不能为空');
      }
      
      // 构建只包含变更路径的配置对象
      const partialConfig = {};
      const pathParts = path.split('.');
      let current = partialConfig;
      
      // 构建嵌套结构
      for (let i = 0; i < pathParts.length - 1; i++) {
        current[pathParts[i]] = {};
        current = current[pathParts[i]];
      }
      
      // 设置最终值
      current[pathParts[pathParts.length - 1]] = value;
      
      // 调用saveConfig保存
      return await saveConfig(partialConfig);
    } catch (error) {
      console.error(`更新配置项 ${path} 失败:`, error);
      throw error;
    }
  }

  // 原有的deepMerge函数保留但不再使用，以避免破坏其他可能的引用
  function deepMerge(target, source) {
    return safeRecursiveMerge(target, source);
  }

  // 加载小说规则
  async function loadNovelRules() {
    try {
      loading.value = true
      const result = await window.pywebview.api.drssion_controller.get_novel_rules()
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        // 确保 rules 是对象而不是字符串
        const rules = typeof response.rules === 'string' ? JSON.parse(response.rules) : response.rules
        state.config.novel.rules = rules || {}
      } else {
        throw new Error(response.message || '加载规则失败')
      }
    } catch (error) {
      console.error('加载规则失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 保存小说规则
  async function saveNovelRule(rule) {
    try {

      const result = await window.pywebview.api.drssion_controller.save_novel_rule(rule)
      const response = typeof result === 'string' ? JSON.parse(result) : result
      if (response.status === 'success') {
        await loadNovelRules()

      } else {
        throw new Error(response.message || '保存规则失败')
      }
    } catch (error) {
      console.error('保存规则失败:', error)
      throw error
    }
  }

  // 删除小说规则
  async function deleteNovelRule(ruleId) {
    try {
      const result = await window.pywebview.api.drssion_controller.delete_novel_rule(ruleId)
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        await loadNovelRules()
        return response
      } else {
        throw new Error(response.message || '删除规则失败')
      }
    } catch (error) {
      console.error('删除规则失败:', error)
      throw error
    }
  }

  // 测试小说规则
  async function testNovelRule(data) {
    try {
      const result = await window.pywebview.api.drssion_controller.test_novel_rule(data)
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        return response

      } else {
        throw new Error(response.message || '测试规则失败')
      }
    } catch (error) {
      console.error('测试规则失败:', error)
      throw error
    }
  }

  // 强制重新加载模型列表
  async function reloadModels() {
    console.log('强制重新加载模型列表...')
    state.config.isModelsLoaded = false
    state.config.models = []
    return await loadModels()
  }

  // 加载模型列表
  async function loadModels() {
    try {
      loading.value = true
      error.value = null

      console.log('ConfigStore: 开始加载模型列表...')

      // 确保先加载AI提供商配置
      try {
        const { useAIProvidersStore } = await import('./aiProviders')
        const aiProvidersStore = useAIProvidersStore()

        if (!aiProvidersStore.initialized) {
          console.log('ConfigStore: AI提供商配置未初始化，先加载提供商配置')

          // 添加带超时的提供商加载
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('加载AI提供商超时')), 5000);
          });

          try {
            await Promise.race([
              aiProvidersStore.loadProviders(),
              timeoutPromise
            ]);
            console.log('ConfigStore: AI提供商配置加载完成')
          } catch (timeoutErr) {
            console.warn('ConfigStore: AI提供商加载超时，继续加载模型', timeoutErr);
          }
        } else {
          console.log('ConfigStore: AI提供商配置已初始化')
        }
      } catch (err) {
        console.warn('ConfigStore: 加载AI提供商配置失败:', err)
      }
      
      // 添加重试机制
      let retryCount = 0
      const maxRetries = 3
      let success = false
      let result = null
      
      while (!success && retryCount < maxRetries) {
        try {
          // 添加超时处理
          const apiPromise = window.pywebview.api.model_controller.get_models();
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('获取模型列表超时')), 5000);
          });
          
          // 调用后端API获取模型列表（带超时）
          const response = await Promise.race([apiPromise, timeoutPromise]);
          result = typeof response === 'string' ? JSON.parse(response) : response
          
          if (result && result.status === 'success' && Array.isArray(result.data)) {
            success = true
            break
          } else {
            throw new Error(result?.message || '获取模型列表失败')
          }
        } catch (e) {
          retryCount++
          console.warn(`加载模型失败 (尝试 ${retryCount}/${maxRetries}):`, e)
          
          if (retryCount >= maxRetries) {
            console.error('达到最大重试次数，使用备用模型列表')
            return useFallbackModels()
          }
          
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }
      
      if (success && result) {
        // 更新状态
        state.config.models = Array.isArray(result.data) ? result.data : []
        state.config.isModelsLoaded = true
        
        // 如果没有选择模型且有可用模型，选择第一个
        if (!state.config.selectedModel && state.config.models.length > 0) {
          state.config.selectedModel = state.config.models[0]
        }
        
        return state.config.models
      } else {
        // 使用备用模型列表
        console.warn('无法获取模型列表，使用备用模型')
        return useFallbackModels()
      }
    } catch (e) {
      console.error('加载模型出错:', e)
      error.value = e.message
      
      // 使用备用模型列表
      return useFallbackModels()
    } finally {
      loading.value = false
    }
  }
  
  // 使用备用模型列表
  function useFallbackModels() {
    const fallbackModels = [
      'gpt-4',
      'gpt-4-turbo',
      'gpt-4o',
      'gpt-3.5-turbo',
      'claude-3-opus',
      'claude-3-sonnet',
      'claude-3-haiku'
    ]
    
    state.config.models = fallbackModels
    state.config.isModelsLoaded = true
    
    // 如果没有选择模型，选择第一个
    if (!state.config.selectedModel && fallbackModels.length > 0) {
      state.config.selectedModel = fallbackModels[0]
    }
    
    return fallbackModels
  }

  // TTS相关方法
  async function updateTTSConfig(config) {
    try {
      const result = await window.pywebview.api.update_tts_config(config)
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response.status === 'success') {
        state.config.tts = { ...state.config.tts, ...config }
        return response.data
      }
      throw new Error(response.message)
    } catch (e) {
      error.value = e.message
      throw e
    }
  }

  async function getTTSConfig() {
    try {
      const result = await window.pywebview.api.get_tts_config()
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response.status === 'success') {
        state.config.tts = { ...state.config.tts, ...response.data }
        return response.data
      }
      throw new Error(response.message)
    } catch (e) {
      error.value = e.message
      throw e
    }
  }

  async function getVoices() {
    try {
      const result = await window.pywebview.api.get_voices()
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response.status === 'success') {
        return response.data
      }
      throw new Error(response.message)
    } catch (e) {
      error.value = e.message
      throw e
    }
  }

  // 设置选中的模型
  function setSelectedModel(model) {
    state.config.selectedModel = model
  }

  // 自定义主题相关方法
  async function addCustomTheme(theme) {
    try {
      console.log('addCustomTheme called with:', theme);
      console.log('Current customThemes:', state.config.customThemes);

      if (!theme || !theme.id || !theme.name || !theme.colors) {
        throw new Error('主题格式不正确');
      }

      // 检查主题ID是否已存在
      const existingTheme = state.config.customThemes.find(t => t.id === theme.id);
      if (existingTheme) {
        throw new Error(`主题ID "${theme.id}" 已存在`);
      }

      // 添加到自定义主题列表
      const newCustomThemes = [...(state.config.customThemes || []), theme];
      console.log('New customThemes array:', newCustomThemes);

      // 更新配置并保存到后端
      const newConfig = { ...state.config, customThemes: newCustomThemes };
      console.log('Saving config with customThemes:', newConfig.customThemes);

      await saveConfig(newConfig);

      console.log('Config saved, current state.config.customThemes:', state.config.customThemes);

      return true;
    } catch (error) {
      console.error('添加自定义主题失败:', error);
      throw error;
    }
  }

  async function updateCustomTheme(themeId, themeData) {
    try {
      const customThemes = [...(state.config.customThemes || [])];
      const themeIndex = customThemes.findIndex(t => t.id === themeId);
      
      if (themeIndex === -1) {
        throw new Error(`未找到ID为 "${themeId}" 的主题`);
      }
      
      // 更新主题
      customThemes[themeIndex] = { ...customThemes[themeIndex], ...themeData };
      
      // 更新配置并保存到后端
      const newConfig = { ...state.config, customThemes };
      await saveConfig(newConfig);
      
      return true;
    } catch (error) {
      console.error('更新自定义主题失败:', error);
      throw error;
    }
  }

  async function deleteCustomTheme(themeId) {
    try {
      const customThemes = (state.config.customThemes || []).filter(t => t.id !== themeId);
      
      // 更新配置并保存到后端
      const newConfig = { ...state.config, customThemes };
      await saveConfig(newConfig);
      
      return true;
    } catch (error) {
      console.error('删除自定义主题失败:', error);
      throw error;
    }
  }

  async function loadCustomThemes() {
    try {
      // 如果配置已加载，直接返回自定义主题
      if (state.config.loaded) {
        return state.config.customThemes || [];
      }
      
      // 否则加载配置
      await loadConfig();
      return state.config.customThemes || [];
    } catch (error) {
      console.error('加载自定义主题失败:', error);
      throw error;
    }
  }

  // 更新搜索配置
  async function updateConfig(configUpdate) {
    try {
      if (!configUpdate || typeof configUpdate !== 'object') {
        throw new Error('配置更新参数必须是对象');
      }
      
      // 直接使用重构后的saveConfig方法，而不是构建完整配置
      // 因为saveConfig已经实现了安全的合并逻辑
      return await saveConfig(configUpdate);
    } catch (error) {
      console.error('更新配置失败:', error);
      throw error;
    }
  }

  return {
    // 状态
    state,
    loading: readonly(loading),
    error: readonly(error),
    models,
    isModelsLoaded,
    selectedModel,
    chat,
    git,

    // Getters
    theme,
    loaded,
    chrome,
    isLoading,
    hasError,
    openai,
    editor,
    feishu,
    novel,
    backup,
    tts,
    customThemes,
    search,
    markdownEditor,

    // Actions
    loadConfig,
    saveConfig,
    updateConfigItem,
    loadNovelRules,
    saveNovelRule,
    deleteNovelRule,
    testNovelRule,
    loadModels,
    reloadModels,
    setSelectedModel,
    updateTTSConfig,
    getTTSConfig,
    getVoices,
    addCustomTheme,
    updateCustomTheme,
    deleteCustomTheme,
    loadCustomThemes,
    updateConfig,
  }
})
