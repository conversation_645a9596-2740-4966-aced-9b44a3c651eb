<template>
  <div v-show="visible"
       ref="menuRef"
       class="context-menu"
       :style="{ left: `${x}px`, top: `${y}px` }"
       @click.stop>
    <div v-for="item in menuItems"
         :key="item.id"
         class="menu-item"
         :class="{ disabled: item.disabled }"
         @click.stop="handleItemClick(item)">
      <span class="menu-item-icon" v-if="item.icon">
        <component :is="item.icon" />
      </span>
      <span class="menu-item-text">{{ item.name }}</span>
      <span class="menu-item-shortcut" v-if="item.shortcut">{{ item.shortcut }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  menuItems: {
    type: Array,
    required: true,
    default: () => []
  },
  x: {
    type: Number,
    required: true
  },
  y: {
    type: Number,
    required: true
  },
  visible: {
    type: <PERSON><PERSON><PERSON>,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'select', 'close'])
const menuRef = ref(null)

const handleClickOutside = (event) => {
  if (menuRef.value && !menuRef.value.contains(event.target)) {
    emit('close')
  }
}




const handleKeydown = (event) => {
  if (event.key === 'Escape') {
    emit('close')
  }
}

const handleItemClick = (item) => {
  if (!item.disabled) {
    emit('select', item)
    emit('close')
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.context-menu {
  position: fixed;
  min-width: 200px;
  background: rgba(var(--el-bg-color-rgb), 0.85);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(var(--el-border-color-light-rgb), 0.3);
  border-radius: 12px;
  padding: 6px;
  box-shadow: 
    0 4px 16px -2px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(var(--el-border-color-light-rgb), 0.1);
  z-index: 3000;
  font-size: 14px;
  transform-origin: top left;
  animation: menuAppear 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  @keyframes menuAppear {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-8px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 12px;
    padding: 1px;
    background: linear-gradient(
      to bottom right,
      rgba(var(--el-color-primary-rgb), 0.15),
      rgba(var(--el-color-primary-rgb), 0.05)
    );
    mask: linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
                 linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 9px 12px;
  margin: 2px;
  border-radius: 8px;
  cursor: pointer;
  user-select: none;
  color: var(--el-text-color-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  gap: 10px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      120deg,
      transparent,
      rgba(var(--el-color-primary-rgb), 0.1),
      transparent
    );
    transform: translateX(-100%);
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &:hover {
    background: rgba(var(--el-color-primary-rgb), 0.1);
    color: var(--el-color-primary);

    &::before {
      transform: translateX(100%);
    }
  }

  &.disabled {
    color: var(--el-text-color-disabled);
    cursor: not-allowed;
    opacity: 0.7;

    &:hover {
      background: transparent;
      color: var(--el-text-color-disabled);

      &::before {
        transform: translateX(-100%);
      }
    }
  }
}

.menu-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  opacity: 0.9;
  flex-shrink: 0;
  
  svg {
    transition: transform 0.2s ease;
  }

  .menu-item:hover & svg {
    transform: scale(1.1);
  }
}

.menu-item-text {
  flex: 1;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.menu-item-shortcut {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  opacity: 0.8;

  padding-left: 8px;
}
</style>
