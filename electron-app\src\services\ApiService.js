const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto-js');
const dayjs = require('dayjs');

// 导入各个控制器
const ConfigManager = require('./controllers/ConfigManager');
const ProjectController = require('./controllers/ProjectController');
const BookController = require('./controllers/BookController');
const ModelController = require('./controllers/ModelController');
const FileController = require('./controllers/FileController');
const BackupController = require('./controllers/BackupController');
const UserController = require('./controllers/UserController');

class ApiService {
  constructor(userDataPath) {
    this.userDataPath = userDataPath;
    this.backupDir = path.join(userDataPath, 'backup');
    this.configFile = path.join(this.backupDir, 'config', 'settings.json');
    
    // 初始化控制器
    this.configManager = null;
    this.projectController = null;
    this.bookController = null;
    this.modelController = null;
    this.fileController = null;
    this.backupController = null;
    this.userController = null;
  }

  async initialize() {
    try {
      // 确保必要目录存在
      await this.ensureDirectories();
      
      // 初始化配置管理器
      this.configManager = new ConfigManager(this.configFile);
      await this.configManager.initialize();
      
      // 初始化其他控制器
      this.projectController = new ProjectController(this.backupDir);
      await this.projectController.initialize();

      this.bookController = new BookController(this.backupDir);
      await this.bookController.initialize();

      this.fileController = new FileController(this.backupDir);
      await this.fileController.initialize();

      this.backupController = new BackupController(this.backupDir);
      await this.backupController.initialize();

      this.userController = new UserController(this.backupDir);
      await this.userController.initialize();
      
      // 获取AI配置并初始化模型控制器
      const config = await this.configManager.loadConfig();
      const openaiConfig = config.openai || {};
      
      this.modelController = new ModelController({
        apiKey: openaiConfig.api_key || '',
        baseUrl: openaiConfig.base_url || '',
        baseDir: this.backupDir
      });
      
      console.log('ApiService 初始化完成');
    } catch (error) {
      console.error('ApiService 初始化失败:', error);
      throw error;
    }
  }

  async ensureDirectories() {
    const dirs = [
      this.backupDir,
      path.join(this.backupDir, 'config'),
      path.join(this.backupDir, 'projects'),
      path.join(this.backupDir, 'books'),
      path.join(this.backupDir, 'backups'),
      path.join(this.backupDir, 'backgrounds'),
      path.join(this.backupDir, 'local')
    ];

    for (const dir of dirs) {
      await fs.ensureDir(dir);
    }
  }

  // 通用响应格式化
  _successResponse(message, data = null) {
    return {
      status: 'success',
      message,
      data
    };
  }

  _errorResponse(message, error = null) {
    return {
      status: 'error',
      message,
      error: error ? error.toString() : null
    };
  }

  // ==================== 配置管理 ====================
  async get_settings() {
    try {
      const config = await this.configManager.loadConfig();
      return this._successResponse('获取配置成功', config);
    } catch (error) {
      return this._errorResponse('获取配置失败', error);
    }
  }

  async update_config(key, value) {
    try {
      const result = await this.configManager.updateConfig(key, value);
      return result;
    } catch (error) {
      return this._errorResponse('更新配置失败', error);
    }
  }

  async load_config() {
    try {
      const config = await this.configManager.loadConfig();
      return this._successResponse('加载配置成功', config);
    } catch (error) {
      return this._errorResponse('加载配置失败', error);
    }
  }

  // ==================== 项目管理 ====================
  async get_projects() {
    try {
      const projects = await this.projectController.getProjects();
      return this._successResponse('获取项目列表成功', projects);
    } catch (error) {
      return this._errorResponse('获取项目列表失败', error);
    }
  }

  async create_project(projectData) {
    try {
      const project = await this.projectController.createProject(projectData);
      return this._successResponse('创建项目成功', project);
    } catch (error) {
      return this._errorResponse('创建项目失败', error);
    }
  }

  async update_project(projectId, projectData) {
    try {
      const project = await this.projectController.updateProject(projectId, projectData);
      return this._successResponse('更新项目成功', project);
    } catch (error) {
      return this._errorResponse('更新项目失败', error);
    }
  }

  async delete_project(projectId) {
    try {
      await this.projectController.deleteProject(projectId);
      return this._successResponse('删除项目成功');
    } catch (error) {
      return this._errorResponse('删除项目失败', error);
    }
  }

  // ==================== 书籍管理 ====================
  async get_books(projectId) {
    try {
      const books = await this.bookController.getBooks(projectId);
      return this._successResponse('获取书籍列表成功', books);
    } catch (error) {
      return this._errorResponse('获取书籍列表失败', error);
    }
  }

  async create_book(projectId, bookData) {
    try {
      const book = await this.bookController.createBook(projectId, bookData);
      return this._successResponse('创建书籍成功', book);
    } catch (error) {
      return this._errorResponse('创建书籍失败', error);
    }
  }

  async update_book(bookId, bookData) {
    try {
      const book = await this.bookController.updateBook(bookId, bookData);
      return this._successResponse('更新书籍成功', book);
    } catch (error) {
      return this._errorResponse('更新书籍失败', error);
    }
  }

  async delete_book(bookId) {
    try {
      await this.bookController.deleteBook(bookId);
      return this._successResponse('删除书籍成功');
    } catch (error) {
      return this._errorResponse('删除书籍失败', error);
    }
  }

  // ==================== 章节管理 ====================
  async get_chapters(bookId) {
    try {
      const chapters = await this.bookController.getChapters(bookId);
      return this._successResponse('获取章节列表成功', chapters);
    } catch (error) {
      return this._errorResponse('获取章节列表失败', error);
    }
  }

  async create_chapter(bookId, chapterData) {
    try {
      const chapter = await this.bookController.createChapter(bookId, chapterData);
      return this._successResponse('创建章节成功', chapter);
    } catch (error) {
      return this._errorResponse('创建章节失败', error);
    }
  }

  async update_chapter(chapterId, chapterData) {
    try {
      const chapter = await this.bookController.updateChapter(chapterId, chapterData);
      return this._successResponse('更新章节成功', chapter);
    } catch (error) {
      return this._errorResponse('更新章节失败', error);
    }
  }

  async delete_chapter(chapterId) {
    try {
      await this.bookController.deleteChapter(chapterId);
      return this._successResponse('删除章节成功');
    } catch (error) {
      return this._errorResponse('删除章节失败', error);
    }
  }

  async get_chapter_content(chapterId) {
    try {
      const chapter = await this.bookController.getChapter(chapterId);
      if (!chapter) {
        return this._errorResponse('章节不存在');
      }
      return this._successResponse('获取章节内容成功', chapter);
    } catch (error) {
      return this._errorResponse('获取章节内容失败', error);
    }
  }

  async save_chapter_content(chapterId, contentData) {
    try {
      const chapter = await this.bookController.updateChapter(chapterId, contentData);
      return this._successResponse('保存章节内容成功', chapter);
    } catch (error) {
      return this._errorResponse('保存章节内容失败', error);
    }
  }

  // ==================== 角色管理 ====================
  async get_characters(bookId) {
    try {
      const characters = await this.bookController.getCharacters(bookId);
      return this._successResponse('获取角色列表成功', characters);
    } catch (error) {
      return this._errorResponse('获取角色列表失败', error);
    }
  }

  async create_character(bookId, characterData) {
    try {
      const character = await this.bookController.createCharacter(bookId, characterData);
      return this._successResponse('创建角色成功', character);
    } catch (error) {
      return this._errorResponse('创建角色失败', error);
    }
  }

  async update_character(characterId, characterData) {
    try {
      const character = await this.bookController.updateCharacter(characterId, characterData);
      return this._successResponse('更新角色成功', character);
    } catch (error) {
      return this._errorResponse('更新角色失败', error);
    }
  }

  async delete_character(characterId) {
    try {
      await this.bookController.deleteCharacter(characterId);
      return this._successResponse('删除角色成功');
    } catch (error) {
      return this._errorResponse('删除角色失败', error);
    }
  }

  // ==================== AI模型相关 ====================
  async get_ai_roles() {
    try {
      const roles = await this.modelController.get_ai_roles();
      return this._successResponse('获取AI角色成功', roles);
    } catch (error) {
      return this._errorResponse('获取AI角色失败', error);
    }
  }

  async add_ai_role(roleData) {
    try {
      const role = await this.modelController.add_ai_role(roleData);
      return this._successResponse('添加AI角色成功', role);
    } catch (error) {
      return this._errorResponse('添加AI角色失败', error);
    }
  }

  async update_ai_role(roleId, roleData) {
    try {
      const role = await this.modelController.update_ai_role(roleId, roleData);
      return this._successResponse('更新AI角色成功', role);
    } catch (error) {
      return this._errorResponse('更新AI角色失败', error);
    }
  }

  async delete_ai_role(roleId) {
    try {
      await this.modelController.delete_ai_role(roleId);
      return this._successResponse('删除AI角色成功');
    } catch (error) {
      return this._errorResponse('删除AI角色失败', error);
    }
  }

  async chat_with_ai(messages, config) {
    try {
      const result = await this.modelController.chat_with_ai(messages, config);
      return result;
    } catch (error) {
      return this._errorResponse('AI对话失败', error);
    }
  }

  async get_ai_providers() {
    try {
      const providers = await this.modelController.get_ai_providers();
      return this._successResponse('获取AI服务商成功', providers);
    } catch (error) {
      return this._errorResponse('获取AI服务商失败', error);
    }
  }

  async save_ai_providers(providersData) {
    try {
      const result = await this.modelController.save_ai_providers(providersData);
      return result;
    } catch (error) {
      return this._errorResponse('保存AI服务商失败', error);
    }
  }

  async test_api_key(params) {
    try {
      const result = await this.modelController.test_api_key(params);
      return result;
    } catch (error) {
      return this._errorResponse('测试API密钥失败', error);
    }
  }

  async fetch_models(params) {
    try {
      const result = await this.modelController.fetch_models(params);
      return result;
    } catch (error) {
      return this._errorResponse('获取模型列表失败', error);
    }
  }

  // ==================== 文件操作 ====================
  async read_file(filePath) {
    try {
      const result = await this.fileController.read_file(filePath);
      return result;
    } catch (error) {
      return this._errorResponse('读取文件失败', error);
    }
  }

  async write_file(filePath, content, options) {
    try {
      const result = await this.fileController.write_file(filePath, content, options);
      return result;
    } catch (error) {
      return this._errorResponse('写入文件失败', error);
    }
  }

  async delete_file(filePath) {
    try {
      const result = await this.fileController.delete_file(filePath);
      return result;
    } catch (error) {
      return this._errorResponse('删除文件失败', error);
    }
  }

  async copy_file(sourcePath, targetPath) {
    try {
      const result = await this.fileController.copy_file(sourcePath, targetPath);
      return result;
    } catch (error) {
      return this._errorResponse('复制文件失败', error);
    }
  }

  async move_file(sourcePath, targetPath) {
    try {
      const result = await this.fileController.move_file(sourcePath, targetPath);
      return result;
    } catch (error) {
      return this._errorResponse('移动文件失败', error);
    }
  }

  async list_directory(dirPath) {
    try {
      const result = await this.fileController.list_directory(dirPath);
      return result;
    } catch (error) {
      return this._errorResponse('获取目录列表失败', error);
    }
  }

  async create_directory(dirPath) {
    try {
      const result = await this.fileController.create_directory(dirPath);
      return result;
    } catch (error) {
      return this._errorResponse('创建目录失败', error);
    }
  }

  async search_files(searchOptions) {
    try {
      const result = await this.fileController.search_files(searchOptions);
      return result;
    } catch (error) {
      return this._errorResponse('搜索文件失败', error);
    }
  }

  // ==================== 备份管理 ====================
  async create_backup(options) {
    try {
      const result = await this.backupController.create_backup(options);
      return result;
    } catch (error) {
      return this._errorResponse('创建备份失败', error);
    }
  }

  async restore_backup(backupPath, options) {
    try {
      const result = await this.backupController.restore_backup(backupPath, options);
      return result;
    } catch (error) {
      return this._errorResponse('恢复备份失败', error);
    }
  }

  async get_backup_list() {
    try {
      const result = await this.backupController.get_backup_list();
      return result;
    } catch (error) {
      return this._errorResponse('获取备份列表失败', error);
    }
  }

  async delete_backup(backupId) {
    try {
      const result = await this.backupController.delete_backup(backupId);
      return result;
    } catch (error) {
      return this._errorResponse('删除备份失败', error);
    }
  }

  // ==================== 用户管理 ====================
  async login(userData) {
    try {
      const result = await this.userController.login(userData);
      return result;
    } catch (error) {
      return this._errorResponse('用户登录失败', error);
    }
  }

  async check_activation_status() {
    try {
      const result = await this.userController.check_activation_status();
      return result;
    } catch (error) {
      return this._errorResponse('检查激活状态失败', error);
    }
  }

  async verify_activation_code(activationData) {
    try {
      const result = await this.userController.verify_activation_code(activationData);
      return result;
    } catch (error) {
      return this._errorResponse('验证激活码失败', error);
    }
  }

  async get_machine_code() {
    try {
      const result = await this.userController.get_machine_code();
      return result;
    } catch (error) {
      return this._errorResponse('获取机器码失败', error);
    }
  }

  async generate_activation_code_with_params(params) {
    try {
      const result = await this.userController.generate_activation_code_with_params(params);
      return result;
    } catch (error) {
      return this._errorResponse('生成激活码失败', error);
    }
  }

  // ==================== 设定和时间线 ====================
  async get_settings_data(bookId) {
    try {
      const settings = await this.bookController.get_settings_data(bookId);
      return this._successResponse('获取设定数据成功', settings);
    } catch (error) {
      return this._errorResponse('获取设定数据失败', error);
    }
  }

  async save_settings_data(bookId, settingsData) {
    try {
      const settings = await this.bookController.save_settings_data(bookId, settingsData);
      return this._successResponse('保存设定数据成功', settings);
    } catch (error) {
      return this._errorResponse('保存设定数据失败', error);
    }
  }

  async get_timeline(bookId) {
    try {
      const timeline = await this.bookController.get_timeline(bookId);
      return this._successResponse('获取时间线成功', timeline);
    } catch (error) {
      return this._errorResponse('获取时间线失败', error);
    }
  }

  async save_timeline(bookId, timelineData) {
    try {
      const timeline = await this.bookController.save_timeline(bookId, timelineData);
      return this._successResponse('保存时间线成功', timeline);
    } catch (error) {
      return this._errorResponse('保存时间线失败', error);
    }
  }

  // ==================== 导入导出 ====================
  async export_data(exportConfig) {
    try {
      const { type, bookId, format, outputPath } = exportConfig;

      if (type === 'book' && bookId) {
        const result = await this.bookController.exportBook(bookId, outputPath, format);
        return this._successResponse('导出书籍成功', result);
      } else if (type === 'project' && exportConfig.projectId) {
        const result = await this.projectController.exportProject(exportConfig.projectId, outputPath);
        return this._successResponse('导出项目成功', result);
      } else {
        return this._errorResponse('不支持的导出类型');
      }
    } catch (error) {
      return this._errorResponse('导出数据失败', error);
    }
  }

  async import_data(importConfig) {
    try {
      const { type, importPath } = importConfig;

      if (type === 'project') {
        const result = await this.projectController.importProject(importPath);
        return this._successResponse('导入项目成功', result);
      } else {
        return this._errorResponse('不支持的导入类型');
      }
    } catch (error) {
      return this._errorResponse('导入数据失败', error);
    }
  }

  // ==================== 系统功能 ====================
  async open_directory(dirPath) {
    try {
      const { shell } = require('electron');
      await shell.openPath(dirPath);
      return this._successResponse('打开目录成功');
    } catch (error) {
      return this._errorResponse('打开目录失败', error);
    }
  }

  async get_app_info() {
    try {
      const { app } = require('electron');
      const appInfo = {
        version: app.getVersion(),
        name: app.getName(),
        platform: process.platform,
        arch: process.arch,
        userDataPath: this.userDataPath,
        nodeVersion: process.version
      };
      return this._successResponse('获取应用信息成功', appInfo);
    } catch (error) {
      return this._errorResponse('获取应用信息失败', error);
    }
  }

  // 清理资源
  cleanup() {
    // 清理定时器、连接等资源
    if (this.backupController) {
      this.backupController.cleanup();
    }
    if (this.modelController) {
      this.modelController.cleanup();
    }
    if (this.fileController) {
      this.fileController.cleanup();
    }
    if (this.userController) {
      this.userController.cleanup();
    }
  }
}

module.exports = ApiService;
