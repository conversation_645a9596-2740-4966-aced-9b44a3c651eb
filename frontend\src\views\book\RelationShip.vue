<template>
  <!-- 添加一个外部容器来控制整体位置 -->
  <div class="relation-ship">
    <!-- 顶部导航 -->
    <div class="page-header">
      <div class="left-section">
        <h1 class="page-title">人物关系图谱</h1>
        <p class="subtitle">可视化角色之间的关系网络，直观展示故事架构与人物互动</p>
      </div>
      <div class="right-section">
        <el-select
            v-model="selectedBookId"
            placeholder="选择书籍"
            class="book-selector"
            @change="handleBookChange"
        >
          <el-option
              v-for="book in bookStore.bookList"
              :key="book.id"
              :label="book.title"
              :value="book.id"
          />
        </el-select>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" v-loading="loading">
      <div class="content-container">
        <!-- 左侧面板 - 固定布局 -->
        <div class="side-panel">
          <!-- 角色管理标题区 -->
          <div class="panel-header">
            <div class="section-title">
              <el-icon><User /></el-icon>
              <span>角色管理</span>
            </div>
            <div class="entity-count">
              <div class="count-badge">{{ entityList.length }}个角色</div>
            </div>
          </div>

          <!-- 搜索和筛选区域 -->
          <div class="filter-section">
            <el-input
                v-model="searchKeyword"
                placeholder="搜索角色"
                :prefix-icon="Search"
                clearable
                class="search-input"
            />

            <!-- 为下拉框添加一个固定容器 -->
            <div class="template-select-container">
              <el-select
                  v-model="selectedTemplateIds"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  placeholder="按模板筛选角色"
                  class="template-select"
                  popper-class="template-select-dropdown"
              >
                <el-option
                    v-for="template in templates"
                    :key="template.id"
                    :label="template.name"
                    :value="template.id"
                >
                  <span class="template-option">
                    <span class="template-name">{{ template.name }}</span>
                    <el-tag size="small" type="info" effect="plain">
                      {{ getEntityCountByTemplate(template.id) }}个
                    </el-tag>
                  </span>
                </el-option>
              </el-select>
            </div>
          </div>

          <!-- 角色列表区域 - 固定布局与内部滚动 -->
          <div class="entity-list-wrapper">
            <el-scrollbar class="entity-scrollbar">
              <div class="entity-list">
                <template v-if="filteredEntities.length > 0">
                  <div
                      v-for="entity in filteredEntities"
                      :key="entity.id"
                      class="entity-item"
                      :class="{ active: selectedEntityId === entity.id }"
                      @click="selectEntity(entity)"
                  >
                    <div class="entity-avatar" :style="{ background: getEntityColor(entity) }">
                      {{ entity.name.substring(0, 1) }}
                    </div>
                    <div class="entity-info">
                      <div class="entity-name">{{ entity.name }}</div>

                    </div>

                    <!-- 实体操作按钮组 -->
                    <div class="entity-actions">
                      <!-- 查看属性按钮 -->
                      <div
                          class="entity-action-btn view-attributes-btn"
                          @click.stop="showEntityAttributes(entity)"
                          data-tooltip="查看属性"
                      >
                        <el-icon><Document /></el-icon>
                      </div>

                      <!-- 管理关系按钮 -->
                      <div
                          class="entity-action-btn manage-relations-btn"
                          @click.stop="showRelationManager(entity)"
                          data-tooltip="管理关系"
                      >
                        <el-icon><Connection /></el-icon>
                      </div>
                    </div>

                  </div>
                </template>

                <el-empty
                    v-else
                    description="未找到角色，请选择其他模板或清除搜索条件"
                    :image-size="100"
                />
              </div>
            </el-scrollbar>

            <!-- 添加关系按钮 - 固定在底部 -->
            <div class="action-footer">
              <el-button
                  type="primary"
                  icon="Plus"
                  class="add-relation-btn"
                  @click="addNewRelation"
                  :disabled="!selectedEntityId"
              >
                添加角色关系
              </el-button>
            </div>
          </div>
        </div>

        <!-- 右侧内容面板 - 固定布局 -->
        <div class="right-panel">
          <!-- 图谱标题区 -->
          <div class="panel-header">
            <div class="section-title">
              <el-icon><Connection /></el-icon>
              <span>关系图谱</span>
            </div>
            <div class="panel-actions">
              <el-button-group>
                <el-tooltip content="重置视图">
                  <el-button
                      @click="resetGraph"
                      :disabled="!hasRelations"
                      type="default"
                      size="small"
                  >
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="全屏查看">
                  <el-button
                      @click="toggleFullScreen"
                      :disabled="!hasRelations"
                      type="default"
                      size="small"
                  >
                    <el-icon><FullScreen /></el-icon>
                  </el-button>
                </el-tooltip>
              </el-button-group>
            </div>
          </div>

          <!-- 右侧内容区域 - 固定布局与内部滚动 -->
          <div class="right-content-wrapper">
            <!-- 图谱展示区 -->
            <div class="graph-container">
              <div
                  v-if="hasRelations"
                  class="graph-content"
                  ref="graphContainer"
              ></div>

              <div v-else class="empty-graph">
                <el-icon class="empty-icon"><Connection /></el-icon>
                <div class="empty-text">
                  暂无关系数据，请添加人物关系
                </div>
                <el-button
                    type="primary"
                    @click="showAddRelationDialog"
                    class="empty-add-btn"
                >
                  <el-icon><Plus /></el-icon>
                  添加人物关系
                </el-button>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>

    <!-- 底部空间 -->
    <div class="bottom-space"></div>

    <!-- 关系表单对话框（复用添加和编辑） -->
    <el-dialog
        :title="isEditMode ? '编辑角色关系' : '添加角色关系'"
        v-model="relationDialog"
        width="600px"
        append-to-body
        destroy-on-close
    >
      <el-form
          :model="relationForm"
          label-position="top"
          class="relation-form"
      >
        <!-- 源角色和目标角色选择放到同一行 -->
        <div class="form-item full-width">
          <div class="roles-row">
            <!-- 源角色选择 -->
            <div class="role-select-container">
              <div class="form-label required">源角色</div>
              <div class="entity-select source-entity-select">
                <el-select
                    v-model="selectedSourceTemplateId"
                    placeholder="选择角色模板"
                    class="source-template-select mb-2"
                    :disabled="isEditMode"
                    @change="handleSourceTemplateChange"
                >
                  <el-option
                      v-for="template in templates"
                      :key="template.id"
                      :label="template.name"
                      :value="template.id"
                  />
                </el-select>
                
                <el-select
                    v-model="relationForm.source"
                    filterable
                    placeholder="选择源角色"
                    popper-class="entity-select-dropdown"
                    :disabled="isEditMode || !selectedSourceTemplateId"
                    class="entity-select"
                >
                  <el-option
                      v-for="entity in filteredSourceEntities"
                      :key="entity.id"
                      :label="entity.name"
                      :value="entity.id"
                  >
                    <div class="entity-option">
                      <div class="entity-main">
                        <div class="entity-avatar" :style="{ background: getEntityColor(entity) }">
                          {{ entity.name.charAt(0) }}
                        </div>
                        <span class="entity-name">{{ entity.name }}</span>
                      </div>

                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>
            
            <!-- 目标角色选择 -->
            <div class="role-select-container">
              <div class="form-label required">目标角色</div>
              <div class="entity-select target-entity-select">
                <el-select
                    v-model="selectedTargetTemplateId"
                    placeholder="选择角色模板"
                    class="target-template-select mb-2"
                    clearable
                    @change="handleTargetTemplateChange"
                >
                  <el-option
                      v-for="template in templates"
                      :key="template.id"
                      :label="template.name"
                      :value="template.id"
                  />
                </el-select>

                <el-select
                    v-model="relationForm.target"
                    filterable
                    placeholder="选择目标角色"
                    popper-class="entity-select-dropdown"
                    :disabled="isEditMode || !selectedTargetTemplateId"
                    class="entity-select"
                >
                  <el-option
                      v-for="entity in filteredTargetEntities"
                      :key="entity.id"
                      :label="entity.name"
                      :value="entity.id"
                      :disabled="entity.id === relationForm.source"
                  >
                    <div class="entity-option">
                      <div class="entity-main">
                        <div class="entity-avatar" :style="{ background: getEntityColor(entity) }">
                          {{ entity.name.charAt(0) }}
                        </div>
                        <span class="entity-name">{{ entity.name }}</span>
                      </div>

                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>
          </div>
        </div>

        <!-- 关系类型和双向关系 -->
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="关系类型">
              <el-select
                  v-model="relationForm.type"
                  placeholder="选择关系类型"
                  filterable
                  class="relation-select"
              >
                <el-option
                    v-for="type in relationTypeOptions"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                >
                  <span class="relation-type-option">
                    <el-tag size="small" :type="type.color">{{ type.label }}</el-tag>
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="双向关系">
              <el-switch
                  v-model="relationForm.bidirectional"
                  active-text="是"
                  inactive-text="否"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 关系描述 -->
        <el-form-item label="关系描述">
          <el-input
              v-model="relationForm.description"
              type="textarea"
              :rows="3"
              placeholder="输入关系描述（可选）"
          />
        </el-form-item>

        <!-- 关系强度 - 优化样式 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="form-item full-width">
              <div class="form-label">关系强度</div>
              <div class="relation-strength-container">
                <el-slider
                    v-model="relationForm.strength"
                    :min="1"
                    :max="5"
                    :show-tooltip="false"
                    :format-tooltip="formatStrengthTooltip"
                    :marks="strengthMarks"
                    class="strength-slider"
                />
                
                <div class="strength-indicator">
                  <div class="strength-value">{{ formatStrengthTooltip(relationForm.strength) }}</div>
                  <div class="strength-bar-container">
                    <div 
                      class="strength-bar" 
                      :style="{ 
                        width: `${relationForm.strength * 20}%`,
                        background: getStrengthGradient(relationForm.strength)
                      }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 关系标签 -->
        <el-form-item label="关系标签">
          <el-select
              v-model="relationForm.tags"
              multiple
              allow-create
              filterable
              default-first-option
              placeholder="添加标签（可选）"
              class="relation-select"
          >
            <el-option
                v-for="tag in relationTags"
                :key="tag"
                :label="tag"
                :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 对话框底部按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="relationDialog = false">取消</el-button>
          <el-button type="primary" @click="submitRelationForm">
            {{ isEditMode ? '保存修改' : '添加关系' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加实体关系弹窗 -->
    <el-dialog
        :title="`${currentEntityForRelations?.name || ''}的所有关系`"
        v-model="entityRelationsDialog"
        width="600px"
        destroy-on-close
        :lock-scroll="true"
        append-to-body
        class="relation-management-dialog"
    >
      <div v-if="currentEntityRelations.length > 0">
        <div class="dialog-relations-list">
          <div
              v-for="relation in currentEntityRelations"
              :key="relation.id"
              class="relation-card"
          >
            <div class="relation-info">
              <div class="relation-entities">
                <span class="entity-from">
                  {{ getEntityNameById(relation.source) }}
                </span>
                <span class="relation-arrow">
                  <el-icon v-if="relation.bidirectional"><ArrowRight /></el-icon>
                  <el-icon v-else><Right /></el-icon>
                </span>
                <span class="entity-to">
                  {{ getEntityNameById(relation.target) }}
                </span>
              </div>

              <div class="relation-type">
                <el-tag
                    size="small"
                    :type="getRelationTypeColor(relation.type) || undefined"
                >
                  {{ relation.type }}
                </el-tag>
              </div>
            </div>

            <div class="relation-desc" v-if="relation.description">
              {{ relation.description }}
            </div>

            <div class="relation-footer">
              <div class="relation-tags" v-if="relation.tags && relation.tags.length > 0">
                <el-tag
                    v-for="tag in relation.tags"
                    :key="tag"
                    size="small"
                    effect="plain"
                    class="relation-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <div class="relation-actions">
                <el-button
                    type="primary"
                    size="small"
                    text
                    @click="showEditRelationDialog(relation)"
                >
                  编辑
                </el-button>
                <el-button
                    type="danger"
                    size="small"
                    text
                    @click="confirmDeleteRelation(relation.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-empty
          v-else
          description="该角色暂无关系"
          :image-size="100"
      >
        <template #extra>
          <el-button
              type="primary"
              @click="showAddRelationWithEntity(currentEntityForRelations)"
          >
            添加关系
          </el-button>
        </template>
      </el-empty>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="entityRelationsDialog = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 实体属性全屏弹窗 -->
    <EntityAttributesDialog
        v-model="attributesDialogVisible"
        :entity="currentAttributesEntity"
        :entityList="entityList"
        :templates="templates"
        :relations="relations"
    />

    <!-- 关系管理弹窗 -->
    <el-dialog
        v-model="relationManagerVisible"
        :title="`${currentEntity?.name || ''}的关系管理`"
        class="relation-management-dialog"
        width="800px"
        :lock-scroll="true"
        destroy-on-close
        append-to-body
    >
      <!-- 添加搜索过滤区域 -->
      <div class="relation-filter-header">
        <el-input
            v-model="relationSearchKeyword"
            placeholder="搜索关系类型或角色名称"
            clearable
            prefix-icon="Search"
            class="relation-search-input"
            @input="filterRelations"
        />
        <el-select
            v-model="relationTypeFilter"
            placeholder="按关系类型筛选"
            clearable
            class="relation-type-filter"
            @change="filterRelations"
        >
          <el-option
              v-for="type in availableRelationTypes"
              :key="type"
              :label="type"
              :value="type"
          />
        </el-select>
      </div>

      <!-- 将内容区域包装在el-scrollbar中 -->
      <el-scrollbar height="calc(65vh - 170px)" class="relation-scrollbar">
        <div class="dialog-content">
          <div v-if="filteredRelationsList.length > 0" class="relations-list">
            <div
                v-for="relation in filteredRelationsList"
                :key="relation.id"
                class="relation-item"
            >
              <span class="relation-type">{{ relation.type }}</span>
              <span class="relation-entity">
                {{ relation.source === currentEntity?.id ? getEntityNameById(relation.target) : getEntityNameById(relation.source) }}
              </span>
              <div class="relation-actions">
                <div
                    class="relation-action-btn"
                    @click="editRelation(relation)"
                >
                  <el-icon><Edit /></el-icon>
                </div>
                <div
                    class="relation-action-btn delete"
                    @click="deleteRelation(relation)"
                >
                  <el-icon><Delete /></el-icon>
                </div>
              </div>
            </div>
          </div>
          <el-empty
              v-else
              description="没有找到符合条件的关系"
              :image-size="100"
          >
            <template #extra>
              <el-button @click="clearRelationFilters">清除筛选</el-button>
              <el-button
                  type="primary"
                  @click="addNewRelation"
              >
                添加关系
              </el-button>
            </template>
          </el-empty>
        </div>
      </el-scrollbar>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="relationManagerVisible = false">关闭</el-button>

        </span>
      </template>
    </el-dialog>

    <!-- 关系编辑弹窗 -->
    <el-dialog
        v-model="relationEditVisible"
        :title="isEditingRelation ? '编辑关系' : '添加角色关系'"
        class="add-relation-dialog fixed-dialog"
        width="800px"
        destroy-on-close
        append-to-body
        :close-on-click-modal="false"
        :show-close="true"
        :lock-scroll="true"
        :fullscreen="false"
        top="5vh"
    >
      <div class="dialog-body-wrapper">
        <div class="dialog-content">
          <!-- 源角色和目标角色选择放到同一行 -->
          <div class="form-item full-width">
            <div class="roles-row">
              <!-- 源角色选择 -->
              <div class="role-select-container">
                <div class="form-label required">源角色</div>
                <div class="entity-select source-entity-select">
                  <el-select
                      v-model="selectedSourceTemplateId"
                      placeholder="选择角色模板"
                      class="source-template-select mb-2"
                      :disabled="isEditingRelation"
                      @change="handleSourceTemplateChange"
                  >
                    <el-option
                        v-for="template in templates"
                        :key="template.id"
                        :label="template.name"
                        :value="template.id"
                    />
                  </el-select>
                  
                  <el-select
                      v-model="currentRelation.source"
                      placeholder="选择源角色"
                      filterable
                      :disabled="!selectedSourceTemplateId || isEditingRelation"
                  >
                    <el-option
                        v-for="entity in filteredSourceEntities"
                        :key="entity.id"
                        :label="entity.name"
                        :value="entity.id"
                    />
                  </el-select>
                </div>
              </div>
              
              <!-- 目标角色选择 -->
              <div class="role-select-container">
                <div class="form-label required">目标角色</div>
                <div class="entity-select target-entity-select">
                  <el-select
                      v-model="selectedTargetTemplateId"
                      placeholder="选择角色模板"
                      class="target-template-select mb-2"
                      clearable
                      @change="handleTargetTemplateChange"
                  >
                    <el-option
                        v-for="template in templates"
                        :key="template.id"
                        :label="template.name"
                        :value="template.id"
                    />
                  </el-select>

                  <el-select
                      v-model="currentRelation.target"
                      placeholder="选择目标角色"
                      filterable
                      class="target-entity-select"
                      :disabled="!selectedTargetTemplateId"
                  >
                    <el-option
                        v-for="entity in filteredTargetEntities"
                        :key="entity.id"
                        :label="entity.name"
                        :value="entity.id"
                    />
                  </el-select>
                </div>
              </div>
            </div>
          </div>

          <!-- 关系类型选择 -->
          <div class="form-item full-width">
            <div class="form-label required">关系类型</div>
            <div class="relation-type-select">
              <div
                  v-for="type in relationTypeOptions"
                  :key="type.value"
                  :class="['relation-type-option', currentRelation.type === type.value ? 'active' : '']"
                  @click="currentRelation.type = type.value"
              >
                {{ type.label }}
              </div>
              <!-- 自定义关系类型输入 -->
              <el-input
                  v-if="showCustomRelationType"
                  v-model="customRelationType"
                  placeholder="输入自定义关系类型"
                  @blur="addCustomRelationType"
              />
              <div
                  class="relation-type-option custom-type"
                  @click="showCustomRelationType = true"
              >
                <el-icon><Plus /></el-icon> 自定义
              </div>
            </div>
          </div>

          <!-- 双向关系 -->
          <div class="form-item">
            <div class="form-label">双向关系</div>
            <el-switch
                v-model="currentRelation.bidirectional"
                active-text="是"
                inactive-text="否"
            />
          </div>

          <!-- 关系强度 - 优化样式 -->
          <div class="form-item full-width">
            <div class="form-label">关系强度</div>
            <div class="relation-strength-container">
              <el-slider
                  v-model="currentRelation.strength"
                  :min="1"
                  :max="5"
                  :show-tooltip="false"
                  :format-tooltip="formatStrengthTooltip"
                  :marks="strengthMarks"
                  class="strength-slider"
              />
              
              <div class="strength-indicator">
                <div class="strength-value">{{ formatStrengthTooltip(currentRelation.strength) }}</div>
                <div class="strength-bar-container">
                  <div 
                    class="strength-bar" 
                    :style="{ 
                      width: `${currentRelation.strength * 20}%`,
                      background: getStrengthGradient(currentRelation.strength)
                    }"
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 关系描述 -->
          <div class="form-item full-width">
            <div class="form-label">关系描述</div>
            <el-input
                v-model="currentRelation.description"
                type="textarea"
                :rows="3"
                placeholder="请输入关系描述（可选）"
            />
          </div>

          <!-- 关系标签 -->
          <div class="form-item full-width">
            <div class="form-label">关系标签</div>
            <div class="relation-tags-container">
              <el-tag
                  v-for="tag in currentRelation.tags"
                  :key="tag"
                  closable
                  @close="removeTag(tag)"
              >
                {{ tag }}
              </el-tag>
              <el-input
                  v-if="tagInputVisible"
                  ref="tagInput"
                  v-model="tagInputValue"
                  class="tag-input"
                  size="small"
                  @keyup.enter="addTag"
                  @blur="addTag"
              />
              <el-button
                  v-else
                  class="button-new-tag"
                  size="small"
                  @click="showTagInput"
              >
                + 添加标签
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="relationEditVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRelation">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 全屏关系图谱弹窗 -->
    <FullscreenGraphDialog
        :visible="fullScreenGraphVisible"
        @update:visible="fullScreenGraphVisible = $event"
        :relations="relations"
        :entityList="entityList"
        :bookId="selectedBookId"
        :templates="templates"
        @node-click="handleFullscreenNodeClick"
        @edge-click="handleFullscreenEdgeClick"
        @add-relation="handleAddRelationFromGraph"
    />
  </div>
</template>

<script setup>
// ...其他代码...

// 添加此方法
const handleAddRelationFromGraph = (sourceId) => {
  // 从图谱中添加关系，传递源节点ID
  showAddRelationDialog(sourceId);
};
</script>

<style lang="scss" scoped>
@import url("@/scss/RelationShip.scss");

/* 添加全局禁止选择文本样式 */
.relation-ship {
  user-select: none;
}

/* 保留以下元素可选 */
.search-input :deep(input),
.entity-select :deep(input),
.relation-form :deep(textarea),
.relation-desc,
.relation-type-option,
.relation-tags-container :deep(input) {
  user-select: text;
}

/* 确保关系描述可选 */
.relation-desc {
  user-select: text;
}

/* 确保搜索输入框可选 */
.filter-section :deep(input),
.relation-filter-header :deep(input) {
  user-select: text;
}
</style>

