/* 原生应用通用样式 */
.native-app-style {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;

  /* 禁用右键菜单 */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;

  /* 禁用图片和媒体元素拖拽 */
  img, svg, canvas, video, audio {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    pointer-events: none;
  }

  /* 按钮和交互元素恢复指针事件 */
  button, .el-button, input, textarea, select, .el-input, .el-select, .el-textarea {
    pointer-events: auto;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 输入框内容可以选择 */
  input, textarea, .el-input__inner, .el-textarea__inner {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }

  /* 可选择的文本内容 */
  .selectable-text, .book-description, .book-meta, .book-stats {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }
}

/* 写作管理器主容器 */
.writing-manager {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
  height: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(var(--el-color-primary-rgb), 0.02) 0%,
    rgba(var(--el-bg-color-rgb), 1) 20%,
    rgba(var(--el-bg-color-rgb), 1) 80%,
    rgba(var(--el-color-primary-rgb), 0.02) 100%);
  position: relative;

  /* 应用原生应用样式 */
  @extend .native-app-style;

  /* 添加微妙的背景纹理 */
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(var(--el-color-primary-rgb), 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(var(--el-color-primary-rgb), 0.03) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(var(--el-color-primary-rgb), 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }

  :deep(.el-card) {
    border-radius: 24px;
    border: none;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.06),
      0 4px 16px rgba(0, 0, 0, 0.04),
      0 2px 8px rgba(0, 0, 0, 0.02);
    overflow: visible;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    background: rgba(var(--el-bg-color-rgb), 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    position: relative;

    /* 卡片光效 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.05) 100%);
      border-radius: 24px;
      pointer-events: none;
      z-index: 1;
    }



    .el-card__header {
      padding: 24px 32px;
      border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.08);
      background: linear-gradient(to bottom,
        rgba(var(--el-bg-color-rgb), 0.8),
        rgba(var(--el-bg-color-overlay-rgb), 0.6));
      border-radius: 24px 24px 0 0;
      backdrop-filter: blur(12px);
      position: relative;
      z-index: 2;
    }

    .el-card__body {
      padding: 32px;
      position: relative;
      z-index: 2;
    }
  }

  /* 暗色主题适配 */
  html.dark & {
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.05) 0%,
      rgba(var(--el-bg-color-rgb), 1) 20%,
      rgba(var(--el-bg-color-rgb), 1) 80%,
      rgba(var(--el-color-primary-rgb), 0.05) 100%);

    &::before {
      background:
        radial-gradient(circle at 20% 20%, rgba(var(--el-color-primary-rgb), 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(var(--el-color-primary-rgb), 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(var(--el-color-primary-rgb), 0.05) 0%, transparent 50%);
    }

    :deep(.el-card) {
      background: rgba(var(--el-bg-color-rgb), 0.9);
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08);
      border-color: rgba(var(--el-border-color-rgb), 0.15);

      &::before {
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.05) 0%,
          transparent 50%,
          rgba(255, 255, 255, 0.02) 100%);
      }

      &:hover {
        box-shadow:
          0 20px 60px rgba(0, 0, 0, 0.2),
          0 10px 30px rgba(0, 0, 0, 0.15),
          0 5px 15px rgba(0, 0, 0, 0.1);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
      }

      .el-card__header {
        background: linear-gradient(to bottom,
          rgba(var(--el-bg-color-rgb), 0.9),
          rgba(var(--el-bg-color-overlay-rgb), 0.7));
        border-bottom-color: rgba(var(--el-border-color-rgb), 0.12);
      }
    }
  }
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  /* 添加标题样式 */
  .el-card__header & {
    &::before {
      content: '';
      position: absolute;
      left: -32px;
      right: -32px;
      top: -24px;
      bottom: -24px;
      background: linear-gradient(135deg,
        rgba(var(--el-color-primary-rgb), 0.03) 0%,
        transparent 50%);
      border-radius: 24px 24px 0 0;
      z-index: -1;
    }
  }

  .header-actions {
    display: flex;
    gap: 16px;

    .el-button {
      border-radius: 16px;
      padding: 12px 24px;
      height: auto;
      font-weight: 700;
      font-size: 14px;
      border: none;
      box-shadow:
        0 4px 12px rgba(var(--el-color-primary-rgb), 0.25),
        0 2px 6px rgba(var(--el-color-primary-rgb), 0.15);
      transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
      background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
      position: relative;
      overflow: hidden;

      /* 按钮光效 */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
      }

      &:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow:
          0 8px 24px rgba(var(--el-color-primary-rgb), 0.35),
          0 4px 12px rgba(var(--el-color-primary-rgb), 0.25);
        background: linear-gradient(135deg, var(--el-color-primary-light-3), var(--el-color-primary-light-5));

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(-1px) scale(1.02);
        box-shadow:
          0 4px 12px rgba(var(--el-color-primary-rgb), 0.25),
          0 2px 6px rgba(var(--el-color-primary-rgb), 0.15);
        background: linear-gradient(135deg, var(--el-color-primary-dark-2), var(--el-color-primary));
      }

      /* 按钮图标动画 */
      .el-icon {
        transition: transform 0.2s ease;
      }

      &:hover .el-icon {
        transform: scale(1.1);
      }
    }
  }
}

/* 书籍列表容器 */
.books-container {
  width: 100%;
  position: relative;

  /* 添加容器装饰效果 */
  &::before {
    content: '';
    position: absolute;
    top: -16px;
    left: -16px;
    right: -16px;
    bottom: -16px;
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.02) 0%,
      transparent 30%,
      transparent 70%,
      rgba(var(--el-color-primary-rgb), 0.02) 100%);
    border-radius: 32px;
    z-index: -1;
    opacity: 0.5;
  }

  .books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 32px;
    width: 100%;
    padding: 16px;

    /* 网格动画 */
    .book-card {
      animation: fadeInUp 0.6s ease-out;
      animation-fill-mode: both;

      @for $i from 1 through 12 {
        &:nth-child(#{$i}) {
          animation-delay: #{($i - 1) * 0.1}s;
        }
      }
    }
  }
}

/* 淡入上升动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 书籍卡片 */
.book-card {
  position: relative;
  background: linear-gradient(145deg, var(--el-bg-color-overlay), var(--el-bg-color));
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.08),
    0 6px 16px rgba(0, 0, 0, 0.04);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  flex-direction: column;
  min-height: 280px;
  border: 2px solid transparent;
  backdrop-filter: blur(10px);

  /* 应用原生应用样式 */
  @extend .native-app-style;
  
  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.12),
      0 8px 20px rgba(0, 0, 0, 0.08);
  }
  
  /* 书籍内容 */
  .book-content {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
    border-radius: 18px;
    background: rgba(var(--el-bg-color-rgb), 0.6);
    backdrop-filter: blur(8px);
    margin: 2px;

    /* 只有书籍标题可以选择复制 */
    .book-header h3 {
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
    }

    /* 其他内容和按钮都不能选择 */
    .book-description,
    .book-meta,
    .book-stats,
    .settings-btn {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
  }
  
  /* 书籍头部 */
  .book-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 700;
      color: var(--el-text-color-primary);
      line-height: 1.3;
      word-break: break-word;
      flex: 1;
      font-style: normal; /* 确保不是斜体 */
      letter-spacing: 0.5px; /* 增加字母间距 */
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 添加微妙阴影 */
      transition: all 0.2s ease; /* 添加过渡效果 */

      /* 选择时的样式 */
      &::selection {
        background: rgba(var(--el-color-primary-rgb), 0.2);
        color: var(--el-color-primary);
      }

      &::-moz-selection {
        background: rgba(var(--el-color-primary-rgb), 0.2);
        color: var(--el-color-primary);
      }

      /* 悬停时的微妙效果 */
      &:hover {
        transform: translateY(-1px);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
    
    .settings-btn {
      padding: 8px;
      height: auto;
      width: auto;
      border-radius: 8px;
      background: transparent;
      border: none;
      color: var(--el-text-color-secondary);
      transition: all 0.2s ease;
      margin-left: 8px;
      
      &:hover {
        background: rgba(var(--el-color-primary-rgb), 0.08);
        color: var(--el-color-primary);
        transform: rotate(30deg);
      }
    }
  }
  
  /* 书籍元数据 */
  .book-meta {
    display: flex;
    gap: 12px;
    margin-bottom: 14px;
    position: relative;

    span {
      font-size: 14px;
      color: var(--el-text-color-secondary);
      background: rgba(var(--el-bg-color-rgb), 0.8);
      padding: 6px 12px;
      border-radius: 12px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(var(--el-border-color-rgb), 0.12);
      font-weight: 500;
      backdrop-filter: blur(4px);
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
  
  /* 书籍描述 */
  .book-description {
    margin: 0 0 16px;
    color: var(--el-text-color-regular);
    font-size: 14px;
    line-height: 1.6;
    flex: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    word-break: break-word;
  }
  
  /* 书籍统计 */
  .book-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    position: relative;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      color: var(--el-text-color-secondary);

      .el-icon {
        font-size: 15px;
        color: var(--el-color-primary-light-5);
      }
    }
  }
  
  /* 书籍操作 */
  .book-actions {
    padding: 16px;
    background: linear-gradient(to bottom, transparent, rgba(var(--el-bg-color-rgb), 0.9));
    border-top: 1px solid rgba(var(--el-border-color-rgb), 0.12);
    display: flex;
    flex-direction: column;
    gap: 12px;
    border-radius: 0 0 18px 18px;
    backdrop-filter: blur(6px);
    
    .action-group {
      display: flex;
      gap: 10px;
      
      &.primary {
        margin-bottom: 6px;
        
        .action-btn {
          flex: 1;
          font-weight: 700;
          padding: 12px 16px;
          height: auto;
          border-radius: 14px;
          border: none;
          box-shadow:
            0 4px 12px rgba(var(--el-color-primary-rgb), 0.2),
            0 2px 6px rgba(var(--el-color-primary-rgb), 0.1);
          transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          font-size: 15px;
          background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
          }

          .el-icon {
            font-size: 16px;
            transition: transform 0.2s ease;
          }

          &:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow:
              0 8px 20px rgba(var(--el-color-primary-rgb), 0.3),
              0 4px 10px rgba(var(--el-color-primary-rgb), 0.2);

            &::before {
              left: 100%;
            }

            .el-icon {
              transform: scale(1.1);
            }
          }

          &:active {
            transform: translateY(-1px) scale(0.98);
            box-shadow:
              0 4px 12px rgba(var(--el-color-primary-rgb), 0.2),
              0 2px 6px rgba(var(--el-color-primary-rgb), 0.15);
          }
        }
      }
      
      &.secondary {
        display: grid;
        grid-template-columns: 1fr 40px 40px 40px;
        gap: 10px;
        
        .action-btn {
          padding: 10px;
          height: 44px;
          border-radius: 12px;
          font-size: 13px;
          font-weight: 600;
          border: none;
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
          transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
          backdrop-filter: blur(4px);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            opacity: 0;
            transition: opacity 0.2s ease;
          }

          &.icon-btn {
            width: 44px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .el-icon {
              font-size: 16px;
              transition: all 0.25s ease;
            }
          }

          &:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);

            &::before {
              opacity: 1;
            }

            .el-icon {
              transform: scale(1.1);
            }
          }

          &:active {
            transform: translateY(0) scale(1.02);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
  
  /* 新建书籍卡片 */
  &.new-book {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 280px;
    background: linear-gradient(145deg, rgba(var(--el-color-primary-rgb), 0.03), rgba(var(--el-color-primary-rgb), 0.1));
    border: 3px dashed rgba(var(--el-color-primary-rgb), 0.25);
    cursor: pointer;
    box-shadow:
      0 8px 24px rgba(var(--el-color-primary-rgb), 0.08),
      0 4px 12px rgba(var(--el-color-primary-rgb), 0.05);
    gap: 20px;
    transform-origin: center;
    backdrop-filter: blur(8px);
    position: relative;

    /* 新建卡片文字不能选择 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(var(--el-color-primary-rgb), 0.1), transparent);
      transition: left 0.6s ease;
    }

    .el-icon {
      font-size: 36px;
      color: var(--el-color-primary);
      background: rgba(var(--el-bg-color-rgb), 0.8);
      padding: 20px;
      border-radius: 24px;
      box-shadow:
        0 8px 20px rgba(var(--el-color-primary-rgb), 0.15),
        0 4px 10px rgba(var(--el-color-primary-rgb), 0.1);
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      backdrop-filter: blur(6px);
      position: relative;
      z-index: 1;
    }

    span {
      font-size: 18px;
      font-weight: 700;
      color: var(--el-color-primary);
      text-shadow: 0 2px 4px rgba(var(--el-color-primary-rgb), 0.1);
      position: relative;
      z-index: 1;
    }
    
    &:hover {
      border-color: rgba(var(--el-color-primary-rgb), 0.5);
      background: linear-gradient(145deg, rgba(var(--el-color-primary-rgb), 0.08), rgba(var(--el-color-primary-rgb), 0.15));
      transform: scale(1.05) translateY(-8px);
      box-shadow:
        0 12px 32px rgba(var(--el-color-primary-rgb), 0.15),
        0 6px 16px rgba(var(--el-color-primary-rgb), 0.1);

      &::before {
        left: 100%;
      }

      .el-icon {
        transform: scale(1.15) rotate(15deg);
        box-shadow:
          0 12px 28px rgba(var(--el-color-primary-rgb), 0.25),
          0 6px 14px rgba(var(--el-color-primary-rgb), 0.15);
        color: var(--el-color-primary-light-3);
      }

      span {
        color: var(--el-color-primary-light-3);
        transform: translateY(-2px);
      }
    }
    
    &:active {
      transform: scale(1.02) translateY(-4px);
      transition: all 0.15s ease;
      box-shadow:
        0 6px 16px rgba(var(--el-color-primary-rgb), 0.12),
        0 3px 8px rgba(var(--el-color-primary-rgb), 0.08);

      .el-icon {
        transform: scale(1.05) rotate(5deg);
      }

      span {
        transform: translateY(0);
      }
    }
  }
  
  /* 暗色主题适配 */
  html.dark & {
    box-shadow: 
      0 8px 20px rgba(0, 0, 0, 0.2),
      0 4px 8px rgba(0, 0, 0, 0.15);
    
    &:hover {
      box-shadow: 
        0 14px 30px rgba(0, 0, 0, 0.25),
        0 6px 12px rgba(0, 0, 0, 0.2);
    }
    
    .book-meta span {
      background: rgba(255, 255, 255, 0.05);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    &.new-book {
      background: linear-gradient(145deg, rgba(var(--el-color-primary-rgb), 0.05), rgba(var(--el-color-primary-rgb), 0.1));
      
      .el-icon {
        background: rgba(255, 255, 255, 0.03);
      }
      
      &:hover {
        background: linear-gradient(145deg, rgba(var(--el-color-primary-rgb), 0.08), rgba(var(--el-color-primary-rgb), 0.15));
      }
    }
  }
}

/* 写作区域 */
.writing-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .editor-container {
    height: 100%;
    
    :deep(.el-textarea) {
      height: 100%;
      
      .el-textarea__inner {
        height: 100%;
        min-height: 300px;
        resize: none;
        padding: 20px;
        border-radius: 12px;
        border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
        background: var(--el-bg-color-overlay);
        font-size: 16px;
        line-height: 1.8;
        color: var(--el-text-color-primary);
        transition: all 0.3s ease;
        box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.03);
        
        &:focus {
          border-color: var(--el-color-primary-light-7);
          box-shadow: 
            0 0 0 3px rgba(var(--el-color-primary-rgb), 0.1),
            inset 0 2px 6px rgba(0, 0, 0, 0.02);
        }
      }
    }
  }
  
  .writing-actions {
    display: flex;
    gap: 12px;
    
    .el-button {
      border-radius: 10px;
      padding: 10px 20px;
      height: auto;
      font-weight: 600;
      transition: all 0.25s cubic-bezier(0.34, 1.56, 0.64, 1);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
      }
    }
  }
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 10px 20px rgba(0, 0, 0, 0.08);
  
  .el-dialog__header {
    padding: 20px 24px;
    background: linear-gradient(to bottom, var(--el-bg-color), var(--el-bg-color-overlay));
    margin-right: 0;
    border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    
    .el-dialog__title {
      font-weight: 700;
      font-size: 18px;
      color: var(--el-text-color-primary);
    }
  }
  
  .el-dialog__body {
    padding: 24px;
  }
  
  .el-dialog__footer {
    padding: 16px 24px 24px;
    border-top: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  }
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .writing-manager {
    max-width: 1200px;
    padding: 20px;
  }

  .books-container .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 28px;
  }
}

@media (max-width: 1200px) {
  .writing-manager {
    padding: 18px;
    gap: 28px;
  }

  .books-container .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    padding: 12px;
  }

  .card-header .header-actions {
    gap: 12px;

    .el-button {
      padding: 10px 18px;
      font-size: 13px;
    }
  }
}

@media (max-width: 768px) {
  .writing-manager {
    padding: 16px;
    gap: 24px;

    &::before {
      display: none; /* 移动端隐藏背景效果以提升性能 */
    }

    :deep(.el-card) {
      border-radius: 20px;

      .el-card__header {
        padding: 20px 24px;
        border-radius: 20px 20px 0 0;
      }

      .el-card__body {
        padding: 24px;
      }
    }
  }

  .books-container {
    &::before {
      display: none;
    }

    .books-grid {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 20px;
      padding: 8px;
    }
  }

  .book-card {
    min-height: 240px;

    .book-content {
      padding: 20px;
    }

    .book-header h3 {
      font-size: 18px;
    }

    .book-description {
      -webkit-line-clamp: 2;
      font-size: 13px;
    }

    .book-actions {
      padding: 14px;

      .action-group.primary .action-btn {
        padding: 10px 14px;
        font-size: 14px;
      }

      .action-group.secondary .action-btn {
        height: 40px;

        &.icon-btn {
          width: 40px;
        }
      }
    }
  }

  .card-header {
    .header-actions {
      gap: 8px;
      flex-wrap: wrap;

      .el-button {
        padding: 8px 16px;
        font-size: 12px;
        border-radius: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .writing-manager {
    padding: 12px;
    gap: 20px;
  }

  .books-container .books-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 4px;
  }

  .book-card {
    min-height: 220px;

    .book-content {
      padding: 16px;
    }

    .book-actions {
      padding: 12px;

      .action-group.secondary {
        grid-template-columns: 1fr 36px 36px 36px;
        gap: 8px;

        .action-btn {
          height: 36px;
          font-size: 12px;

          &.icon-btn {
            width: 36px;
          }
        }
      }
    }
  }

  .card-header .header-actions .el-button {
    padding: 6px 12px;
    font-size: 11px;
  }
}

/* 书籍风格样式 */
.book-card .book-content {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 18px;
    opacity: 0.1;
    z-index: -1;
    transition: all 0.3s ease;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120px;
    border-radius: 18px 18px 0 0;
    opacity: 0.05;
    z-index: -1;
    transition: all 0.3s ease;
  }

  /* 经典蓝调 */
  &.style-classic-blue {
    border-left: 4px solid #4a90e2;

    &::before {
      background: linear-gradient(135deg, #4a90e2, #357abd);
    }

    &::after {
      background: linear-gradient(180deg, #4a90e2, transparent);
    }

    .book-header h3 {
      color: #4a90e2;
    }

    .book-meta span {
      background: rgba(74, 144, 226, 0.1);
      color: #357abd;
      border-color: rgba(74, 144, 226, 0.2);
    }
  }

  /* 温暖橙光 */
  &.style-warm-orange {
    border-left: 4px solid #ff8c42;

    &::before {
      background: linear-gradient(135deg, #ff8c42, #e67e22);
    }

    &::after {
      background: linear-gradient(180deg, #ff8c42, transparent);
    }

    .book-header h3 {
      color: #ff8c42;
    }

    .book-meta span {
      background: rgba(255, 140, 66, 0.1);
      color: #e67e22;
      border-color: rgba(255, 140, 66, 0.2);
    }
  }

  /* 清新绿意 */
  &.style-fresh-green {
    border-left: 4px solid #2ecc71;

    &::before {
      background: linear-gradient(135deg, #2ecc71, #27ae60);
    }

    &::after {
      background: linear-gradient(180deg, #2ecc71, transparent);
    }

    .book-header h3 {
      color: #2ecc71;
    }

    .book-meta span {
      background: rgba(46, 204, 113, 0.1);
      color: #27ae60;
      border-color: rgba(46, 204, 113, 0.2);
    }
  }

  /* 优雅紫韵 */
  &.style-elegant-purple {
    border-left: 4px solid #9b59b6;

    &::before {
      background: linear-gradient(135deg, #9b59b6, #8e44ad);
    }

    &::after {
      background: linear-gradient(180deg, #9b59b6, transparent);
    }

    .book-header h3 {
      color: #9b59b6;
    }

    .book-meta span {
      background: rgba(155, 89, 182, 0.1);
      color: #8e44ad;
      border-color: rgba(155, 89, 182, 0.2);
    }
  }

  /* 神秘深邃 */
  &.style-mysterious-dark {
    border-left: 4px solid #34495e;

    &::before {
      background: linear-gradient(135deg, #34495e, #2c3e50);
    }

    &::after {
      background: linear-gradient(180deg, #34495e, transparent);
    }

    .book-header h3 {
      color: #34495e;
    }

    .book-meta span {
      background: rgba(52, 73, 94, 0.1);
      color: #2c3e50;
      border-color: rgba(52, 73, 94, 0.2);
    }
  }

  /* 简约灰调 */
  &.style-minimal-gray {
    border-left: 4px solid #95a5a6;

    &::before {
      background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    }

    &::after {
      background: linear-gradient(180deg, #95a5a6, transparent);
    }

    .book-header h3 {
      color: #95a5a6;
    }

    .book-meta span {
      background: rgba(149, 165, 166, 0.1);
      color: #7f8c8d;
      border-color: rgba(149, 165, 166, 0.2);
    }
  }

  /* 樱花粉韵 */
  &.style-sakura-pink {
    border-left: 4px solid #ff6b9d;

    &::before {
      background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    }

    &::after {
      background: linear-gradient(180deg, #ff6b9d, transparent);
    }

    .book-header h3 {
      color: #ff6b9d;
    }

    .book-meta span {
      background: rgba(255, 107, 157, 0.1);
      color: #e91e63;
      border-color: rgba(255, 107, 157, 0.2);
    }
  }

  /* 深海蓝调 */
  &.style-deep-ocean {
    border-left: 4px solid #1e3a8a;

    &::before {
      background: linear-gradient(135deg, #1e3a8a, #1e40af);
    }

    &::after {
      background: linear-gradient(180deg, #1e3a8a, transparent);
    }

    .book-header h3 {
      color: #3b82f6;
    }

    .book-meta span {
      background: rgba(30, 58, 138, 0.1);
      color: #2563eb;
      border-color: rgba(30, 58, 138, 0.2);
    }
  }

  /* 翡翠绿洲 */
  &.style-emerald-oasis {
    border-left: 4px solid #059669;

    &::before {
      background: linear-gradient(135deg, #059669, #047857);
    }

    &::after {
      background: linear-gradient(180deg, #059669, transparent);
    }

    .book-header h3 {
      color: #10b981;
    }

    .book-meta span {
      background: rgba(5, 150, 105, 0.1);
      color: #047857;
      border-color: rgba(5, 150, 105, 0.2);
    }
  }

  /* 夕阳红霞 */
  &.style-sunset-glow {
    border-left: 4px solid #dc2626;

    &::before {
      background: linear-gradient(135deg, #dc2626, #ef4444);
    }

    &::after {
      background: linear-gradient(180deg, #dc2626, transparent);
    }

    .book-header h3 {
      color: #ef4444;
    }

    .book-meta span {
      background: rgba(220, 38, 38, 0.1);
      color: #dc2626;
      border-color: rgba(220, 38, 38, 0.2);
    }
  }

  /* 薰衣草紫 */
  &.style-lavender-dream {
    border-left: 4px solid #7c3aed;

    &::before {
      background: linear-gradient(135deg, #7c3aed, #8b5cf6);
    }

    &::after {
      background: linear-gradient(180deg, #7c3aed, transparent);
    }

    .book-header h3 {
      color: #8b5cf6;
    }

    .book-meta span {
      background: rgba(124, 58, 237, 0.1);
      color: #7c3aed;
      border-color: rgba(124, 58, 237, 0.2);
    }
  }

  /* 琥珀金辉 */
  &.style-amber-gold {
    border-left: 4px solid #d97706;

    &::before {
      background: linear-gradient(135deg, #d97706, #f59e0b);
    }

    &::after {
      background: linear-gradient(180deg, #d97706, transparent);
    }

    .book-header h3 {
      color: #f59e0b;
    }

    .book-meta span {
      background: rgba(217, 119, 6, 0.1);
      color: #d97706;
      border-color: rgba(217, 119, 6, 0.2);
    }
  }

  /* 草稿类型样式 */
  &.draft-type {
    &::before {
      opacity: 0.06;
    }

    .book-header h3 {
      opacity: 0.9;
      font-style: normal; /* 草稿也不使用斜体 */
      font-weight: 600; /* 稍微减轻字重以区分草稿 */
    }

    .book-meta {
      &::after {
        content: '草稿';
        position: absolute;
        top: -6px;
        right: 0;
        background: rgba(var(--el-color-warning-rgb), 0.15);
        color: var(--el-color-warning);
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 500;
        border: 1px solid rgba(var(--el-color-warning-rgb), 0.25);
        box-shadow: 0 1px 3px rgba(var(--el-color-warning-rgb), 0.2);
        backdrop-filter: blur(4px);
        font-style: normal; /* 确保标识不是斜体 */
      }
    }
  }

  /* 作品类型样式 */
  &.work-type {
    .book-meta {
      &::after {
        content: '作品';
        position: absolute;
        top: -8px;
        right: 0;
        background: rgba(var(--el-color-success-rgb), 0.1);
        color: var(--el-color-success);
        padding: 2px 8px;
        border-radius: 8px;
        font-size: 11px;
        font-weight: 600;
        border: 1px solid rgba(var(--el-color-success-rgb), 0.2);
      }
    }
  }

  /* 加密书籍样式 */
  &.encrypted-book {
    position: relative;

    &::before {
      box-shadow: inset 0 0 20px rgba(255, 193, 7, 0.1);
    }

    /* 移除了书名前的锁图标样式 */

    /* 在book-stats区域添加优雅的加密状态指示 */
    .book-stats {
      &::after {
        content: '🔒 已加密';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg,
          rgba(255, 193, 7, 0.1) 0%,
          rgba(255, 193, 7, 0.05) 100%);
        color: #ffc107;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        border: 1px solid rgba(255, 193, 7, 0.2);
        backdrop-filter: blur(8px);
        box-shadow:
          0 2px 8px rgba(255, 193, 7, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        gap: 4px;
        font-style: normal;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg,
            rgba(255, 193, 7, 0.15) 0%,
            rgba(255, 193, 7, 0.08) 100%);
          transform: translateY(-50%) scale(1.05);
          box-shadow:
            0 4px 12px rgba(255, 193, 7, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }
      }
    }
  }
}

/* 动画效果 */
/* 移除了不需要的呼吸动画效果 */

/* 暗色主题下的书籍风格调整 */
html.dark .book-card .book-content {
  /* 经典蓝调 - 暗色主题 */
  &.style-classic-blue {
    .book-header h3 {
      color: #64b5f6;
    }

    .book-meta span {
      background: rgba(100, 181, 246, 0.15);
      color: #64b5f6;
    }
  }

  /* 温暖橙光 - 暗色主题 */
  &.style-warm-orange {
    .book-header h3 {
      color: #ffab40;
    }

    .book-meta span {
      background: rgba(255, 171, 64, 0.15);
      color: #ffab40;
    }
  }

  /* 清新绿意 - 暗色主题 */
  &.style-fresh-green {
    .book-header h3 {
      color: #4caf50;
    }

    .book-meta span {
      background: rgba(76, 175, 80, 0.15);
      color: #4caf50;
    }
  }

  /* 优雅紫韵 - 暗色主题 */
  &.style-elegant-purple {
    .book-header h3 {
      color: #ba68c8;
    }

    .book-meta span {
      background: rgba(186, 104, 200, 0.15);
      color: #ba68c8;
    }
  }

  /* 神秘深邃 - 暗色主题 */
  &.style-mysterious-dark {
    .book-header h3 {
      color: #78909c;
    }

    .book-meta span {
      background: rgba(120, 144, 156, 0.15);
      color: #78909c;
    }
  }

  /* 简约灰调 - 暗色主题 */
  &.style-minimal-gray {
    .book-header h3 {
      color: #b0bec5;
    }

    .book-meta span {
      background: rgba(176, 190, 197, 0.15);
      color: #b0bec5;
    }
  }

  /* 樱花粉韵 - 暗色主题 */
  &.style-sakura-pink {
    .book-header h3 {
      color: #f48fb1;
    }

    .book-meta span {
      background: rgba(244, 143, 177, 0.15);
      color: #f48fb1;
    }
  }

  /* 深海蓝调 - 暗色主题 */
  &.style-deep-ocean {
    .book-header h3 {
      color: #60a5fa;
    }

    .book-meta span {
      background: rgba(96, 165, 250, 0.15);
      color: #60a5fa;
    }
  }

  /* 翡翠绿洲 - 暗色主题 */
  &.style-emerald-oasis {
    .book-header h3 {
      color: #34d399;
    }

    .book-meta span {
      background: rgba(52, 211, 153, 0.15);
      color: #34d399;
    }
  }

  /* 夕阳红霞 - 暗色主题 */
  &.style-sunset-glow {
    .book-header h3 {
      color: #f87171;
    }

    .book-meta span {
      background: rgba(248, 113, 113, 0.15);
      color: #f87171;
    }
  }

  /* 薰衣草紫 - 暗色主题 */
  &.style-lavender-dream {
    .book-header h3 {
      color: #a78bfa;
    }

    .book-meta span {
      background: rgba(167, 139, 250, 0.15);
      color: #a78bfa;
    }
  }

  /* 琥珀金辉 - 暗色主题 */
  &.style-amber-gold {
    .book-header h3 {
      color: #fbbf24;
    }

    .book-meta span {
      background: rgba(251, 191, 36, 0.15);
      color: #fbbf24;
    }
  }
}

/* 书籍创建弹窗样式 */
.book-create-dialog {
  :deep(.el-overlay) {
    overflow: hidden !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
  }

  :deep(.el-dialog) {
    border-radius: 24px;
    overflow: hidden;
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.15),
      0 10px 30px rgba(0, 0, 0, 0.1);
    max-height: 85vh;
    height: auto;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  :deep(.el-dialog__header) {
    padding: 24px 32px;
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    color: white;
    border-bottom: none;
    flex-shrink: 0;

    .el-dialog__title {
      font-size: 20px;
      font-weight: 700;
      color: white;
    }

    .el-dialog__headerbtn {
      top: 24px;
      right: 32px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .el-dialog__close {
        color: white;
        font-size: 18px;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0;
    flex: 1;
    overflow: hidden;
    min-height: 0;
  }

  :deep(.el-dialog__footer) {
    padding: 24px 32px;
    border-top: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color-overlay);
    flex-shrink: 0;

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 16px;

      .el-button {
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
}

.book-create-content {
  background: var(--el-bg-color);
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.create-scroll-container {
  flex: 1;
  min-height: 0;

  :deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
  }

  :deep(.el-scrollbar__view) {
    padding: 0;
  }
}

.create-form-wrapper {
  padding: 24px 32px 40px 32px;
}

.create-form {
  .settings-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 24px;
    }
  }

  .section-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .form-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-item {
    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }

    .styled-input,
    .styled-textarea,
    .styled-select {
      width: 100%;

      :deep(.el-input__wrapper),
      :deep(.el-textarea__wrapper) {
        border-radius: 12px;
        border: 1px solid var(--el-border-color-light);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary-light-7);
        }

        &.is-focus {
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
        }
      }

      :deep(.el-input__inner) {
        font-size: 14px;
        padding: 12px 16px;
      }

      :deep(.el-textarea__inner) {
        font-size: 14px;
        padding: 12px 16px;
        line-height: 1.6;
      }
    }
  }

  .style-selector {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 12px;

    .style-card {
      border: 2px solid var(--el-border-color-light);
      border-radius: 12px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: var(--el-bg-color-overlay);

      &:hover {
        border-color: var(--el-color-primary-light-5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      &.selected {
        border-color: var(--el-color-primary);
        background: rgba(var(--el-color-primary-rgb), 0.05);
        box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.15);
      }

      .style-preview {
        .preview-header {
          height: 8px;
          border-radius: 4px 4px 0 0;
          margin-bottom: 12px;
        }

        .preview-content {
          .preview-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
          }

          .preview-description {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            line-height: 1.4;
          }
        }
      }

      .style-check {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: var(--el-color-primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }
    }
  }

  .warning-text {
    color: var(--el-color-warning);
    font-size: 13px;
    display: flex;
    align-items: center;
    margin-top: 12px;
    padding: 12px 16px;
    background: rgba(var(--el-color-warning-rgb), 0.1);
    border-radius: 8px;
    border: 1px solid rgba(var(--el-color-warning-rgb), 0.2);

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

/* 暗色主题适配 */
html.dark {
  .book-create-dialog {
    :deep(.el-dialog) {
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color);
    }
  }

  .style-card {
    background: var(--el-fill-color-light);
    border-color: var(--el-border-color);

    &:hover {
      border-color: var(--el-color-primary-light-5);
    }

    &.selected {
      background: rgba(var(--el-color-primary-rgb), 0.1);
    }
  }
}

/* 响应式调整 - 书籍创建对话框 */
@media (max-height: 800px) {
  .book-create-dialog {
    :deep(.el-dialog) {
      max-height: 80vh;
    }

    .create-scroll-container {
      max-height: 400px !important;
    }
  }
}

@media (max-height: 600px) {
  .book-create-dialog {
    :deep(.el-dialog) {
      max-height: 90vh;
    }

    .create-scroll-container {
      max-height: 300px !important;
    }

    .create-form-wrapper {
      padding: 16px 24px 32px 24px;
    }
  }
}

@media (max-width: 768px) {
  .book-create-dialog {
    :deep(.el-dialog) {
      width: 95vw !important;
      margin: 2.5vh auto !important;
    }

    .create-form-wrapper {
      padding: 20px 24px 32px 24px;
    }

    .style-selector {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }
}