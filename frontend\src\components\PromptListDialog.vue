<template>
  <el-dialog
    v-model="visible"
    :title="`${ruleName} - 提示词列表`"
    width="85%"
    class="prompts-dialog"
    :close-on-press-escape="true"
    :destroy-on-close="false"
    :modal-append-to-body="true"
    :fullscreen="true"
  >
    <div class="prompt-list-container">
      <!-- 搜索和过滤 -->
      <div class="prompt-list-header">
        <div class="header-left">
          <el-button @click="visible = false" class="close-button">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <div class="divider"></div>
          <h2 class="dialog-title">{{ ruleName }}</h2>
        </div>
        <el-input
          v-model="searchQuery"
          placeholder="搜索提示词..."
          class="prompt-search"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <!-- 左右布局主内容 -->
      <div class="prompt-list-content">
        <!-- 左侧列表 -->
        <div class="prompt-sidebar">
          <div class="sidebar-content">
            <div class="prompt-list">
              <div 
                v-for="(prompt, index) in filteredPromptsList" 
                :key="prompt.id"
                class="prompt-list-item"
                :class="{ 'active': selectedPromptIndex === index }"
                @click="selectPrompt(index)"
              >
                <div class="prompt-list-item-content">
                  <div class="prompt-list-item-title">
                    {{ prompt.name || '未命名提示词' }}
                  </div>
                  <div class="prompt-list-item-preview">
                    {{ getPromptPreview(prompt.content) }}
                  </div>
                  <div class="prompt-list-item-time">
                    {{ formatDate(prompt.timestamp) }}
                  </div>
                </div>
              </div>
              <el-empty v-if="filteredPromptsList.length === 0" description="暂无提示词" />
            </div>
          </div>
        </div>
        
        <!-- 右侧内容 -->
        <div class="prompt-content-view">
          <template v-if="selectedPrompt">
            <div class="prompt-view-header">
              <h3 class="prompt-view-title">{{ selectedPrompt.name || '未命名提示词' }}</h3>
              <div class="prompt-view-actions">
                <el-button 
                  type="success" 
                  size="large"
                  @click="copyPromptContent(selectedPrompt.content)"
                  class="copy-button"
                  :class="{ 'copied': isCopied }"
                >
                  <template #icon>
                    <el-icon :class="{ 'copy-icon': true, 'copied': isCopied }">
                      <component :is="isCopied ? Check : Document" />
                    </el-icon>
                  </template>
                  {{ isCopied ? '已复制' : '复制到剪贴板' }}
                </el-button>
              </div>
            </div>
            <el-divider />
            <div class="prompt-view-content">
              <div class="content-wrapper">
                <pre class="prompt-content-text">{{ selectedPrompt.content }}</pre>
              </div>
            </div>
          </template>
          <el-empty 
            v-else 
            description="请从左侧列表选择一个提示词" 
            class="prompt-view-empty"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Check, ArrowLeft, Search } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ruleName: {
    type: String,
    required: true
  },
  prompts: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'use-prompt'])

// 内部状态
const visible = ref(false)
const searchQuery = ref('')
const selectedPromptIndex = ref(0)
const isCopied = ref(false)

// 监听visible变化
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    // 打开时重置状态
    searchQuery.value = ''
    selectedPromptIndex.value = props.prompts.length > 0 ? 0 : -1
  }
})

// 筛选后的提示词列表
const filteredPromptsList = computed(() => {
  if (!props.prompts) return []
  
  if (!searchQuery.value) return props.prompts
  
  const query = searchQuery.value.toLowerCase()
  return props.prompts.filter(prompt => {
    return (
      (prompt.name && prompt.name.toLowerCase().includes(query)) ||
      (prompt.content && prompt.content.toLowerCase().includes(query))
    )
  })
})

// 当前选中的提示词
const selectedPrompt = computed(() => {
  if (filteredPromptsList.value.length === 0) return null
  if (selectedPromptIndex.value < 0 || selectedPromptIndex.value >= filteredPromptsList.value.length) {
    return filteredPromptsList.value[0]
  }
  return filteredPromptsList.value[selectedPromptIndex.value]
})

// 选择提示词
const selectPrompt = (index) => {
  selectedPromptIndex.value = index
}

// 获取提示词预览
const getPromptPreview = (content) => {
  if (!content) return '无内容'
  const trimmed = content.replace(/\s+/g, ' ').trim()
  return trimmed.length > 60 ? trimmed.substring(0, 60) + '...' : trimmed
}

// 复制提示词内容
const copyPromptContent = (content) => {
  window.pywebview.api.copy_to_clipboard(content)
    .then(() => {
      isCopied.value = true
      setTimeout(() => {
        isCopied.value = false
      }, 2000)
      ElMessage({
        message: '内容已复制到剪贴板',
        type: 'success',
        duration: 2000,
        offset: 80
      })
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style lang="scss" scoped>
.prompts-dialog {
  /* 全局字体设置 */
  :deep(*) {
    
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
  }

  /* 添加全局用户选择控制 */
  :deep(.el-dialog__header),
  :deep(.el-dialog__title),
  .prompt-list-header,
  .header-left,
  .prompt-list-item-time {
    user-select: none;
  }

  :deep(.el-dialog) {
    margin: 0 !important;
    position: fixed;
    top: 20px;
    right: 20px;
    bottom: 20px;
    left: 20px;
    height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    background-color: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    max-height: calc(100vh - 40px);
    
    .el-dialog__header {
      display: none;
    }
    
    .el-dialog__body {
      flex: 1;
      padding: 0;
      margin: 0;
      overflow: hidden;
      height: 100%;
    }
  }
  
  .prompt-list-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .prompt-list-header {
      height: 64px;
      padding: 0 24px;
      border-bottom: 1px solid var(--el-border-color-light);
      background-color: var(--el-bg-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        user-select: none;
        
        .close-button {
          font-size: 15px;
          height: 40px;
          padding: 0 16px;
        }
        
        .divider {
          width: 1px;
          height: 24px;
          background-color: var(--el-border-color-light);
        }
        
        .dialog-title {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          user-select: none;
        }
      }
      
      .prompt-search {
        width: 300px;
      }
    }
    
    .prompt-list-content {
      display: flex;
      position: absolute;
      top: 64px;
      left: 0;
      right: 0;
      bottom: 0;
      overflow: hidden;
      
      .prompt-sidebar {
        width: 25%;
        min-width: 260px;
        max-width: 360px;
        border-right: 1px solid var(--el-border-color-light);
        background-color: var(--el-bg-color-page);
        position: relative;
        overflow: hidden;
        
        .sidebar-content {
          height: 100%;
          overflow-y: auto;
          
          .prompt-list {
            padding-bottom: 20px;
            .prompt-list-item {
              padding: 20px 24px;
              transition: all 0.25s ease;
              border-bottom: 1px solid var(--el-border-color-light);
              cursor: pointer;
              
              &:hover {
                background-color: var(--el-fill-color-light);
                transform: translateX(2px);
              }
              
              &.active {
                background-color: var(--el-color-primary-light-9);
                border-left: 3px solid var(--el-color-primary);
                border-right: none;
                position: relative;
                
                &::before {
                  content: '';
                  position: absolute;
                  left: -3px;
                  top: 0;
                  bottom: 0;
                  width: 3px;
                  background: linear-gradient(to bottom, 
                    var(--el-color-primary-light-3),
                    var(--el-color-primary));
                }
              }
              
              .prompt-list-item-content {
                .prompt-list-item-title {
                  font-weight: 500;
                  margin-bottom: 10px;
                  font-size: 17px;
                  letter-spacing: -0.3px;
                  color: #2c3e50;
                  display: -webkit-box;
                  -webkit-line-clamp: 1;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                  user-select: none;
                }
                
                .prompt-list-item-preview {
                  font-size: 15px;
                  font-weight: 400;
                  letter-spacing: -0.2px;
                  line-height: 1.7;
                  color: var(--el-text-color-regular);
                  margin-bottom: 8px;
                  opacity: 0.9;
                  display: -webkit-box;
                  -webkit-line-clamp: 1;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                }
                
                .prompt-list-item-time {
                  font-size: 13px;
                  font-weight: 400;
                  color: var(--el-text-color-secondary);
                  user-select: none;
                }
              }
            }
          }
        }
      }
      
      .prompt-content-view {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 28px 32px;
        
        .prompt-view-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1px;
          padding-bottom: 1px;
          border-bottom: 1px solid var(--el-border-color-light);
          flex-shrink: 0;
          user-select: none;
          
          .prompt-view-title {
            font-size: 26px;
            font-weight: 500;
            color: #2c3e50;
            letter-spacing: -0.3px;
            user-select: none;
          }
        }
        
        .prompt-view-content {
          flex: 1;
          position: relative;
          overflow: hidden;
          
          .content-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            padding: 32px;
            
            .prompt-content-text {
              white-space: pre-wrap;
              
              line-height: 1.8;
              font-size: 19px;
              letter-spacing: -0.2px;

              padding: 32px;
              border-radius: 8px;
              color: #5e9363;
              margin-bottom: 20px;
              box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
              
              /* 添加代码块样式 */
              code {
                background-color: rgba(0, 0, 0, 0.04);
                padding: 0.2em 0.4em;
                border-radius: 3px;
                font-size: 1em;
              }
              
              /* 优化选中文本的样式 */
              &::selection {
                background-color: var(--el-color-primary-light-8);
              }
            }
          }
        }
        
        .prompt-view-empty {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          color: var(--el-text-color-secondary);
          font-size: 16px;
          user-select: none;
        }
      }
    }
  }
}

.prompt-content-view {
  .prompt-view-header {
    .prompt-view-actions {
      .copy-button {
        min-width: 160px;
        height: 48px;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        &:active {
          transform: translateY(0);
        }
        
        &.copied {
          background-color: var(--el-color-success-dark-2);
          border-color: var(--el-color-success-dark-2);
        }
        
        .copy-icon {
          font-size: 18px;
          margin-right: 8px;
          transition: all 0.3s ease;
          
          &.copied {
            transform: scale(1.2);
            animation: checkmark 0.5s ease-in-out;
          }
        }
        
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          transition: width 0.3s ease, height 0.3s ease;
        }
        
        &:active::after {
          width: 200px;
          height: 200px;
          opacity: 0;
        }
      }
    }
  }
}

@keyframes checkmark {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.4);
  }
  100% {
    transform: scale(1.2);
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-text-color-secondary);
  }
}

/* 搜索框样式优化 */
.prompt-search {
  :deep(.el-input__inner) {
    font-size: 15px;
    height: 40px;
    line-height: 40px;
  }
}
</style> 