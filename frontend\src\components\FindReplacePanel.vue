<template>
  <div 
    class="find-replace-panel" 
    :class="{ 'is-active': visible }"
    v-show="visible"
    ref="panel"
  >
    <div class="panel-content" @mousedown="startDrag">
      <div class="search-row">
        <el-input
          v-model="findTerm"
          placeholder="查找内容"
          clearable
          @input="updateSearch"
          @keydown.enter="findNext"
          ref="findInput"
          class="search-input"
          @mousedown.stop
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-tooltip content="区分大小写" placement="top" :hide-after="1000">
              <el-button
                class="case-button"
                :class="{ 'is-active': caseSensitive }"
                @click.stop="toggleCaseSensitivity"
                link
              >
                <span class="case-icon">Aa</span>
              </el-button>
            </el-tooltip>
            <el-button
              :disabled="!hasMatches"
              @click.stop="findPrevious"
            >
              <el-icon><ArrowUp /></el-icon>
            </el-button>
            <el-button
              :disabled="!hasMatches"
              @click.stop="findNext"
            >
              <el-icon><ArrowDown /></el-icon>
            </el-button>
          </template>
        </el-input>
        <el-button
          class="close-button"
          link
          @click.stop="close"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="replace-row">
        <el-input
          v-model="replaceTerm"
          placeholder="替换为"
          clearable
          @keydown.enter="replaceAndFindNext"
          class="replace-input"
          @mousedown.stop
        >
          <template #prefix>
            <el-icon><Edit /></el-icon>
          </template>
          <template #append>
            <el-button
              :disabled="!hasMatches"
              @click.stop="replaceAndFindNext"
            >
              替换
            </el-button>
            <el-button
              :disabled="!hasMatches"
              @click.stop="replaceAll"
            >
              全部替换
            </el-button>
          </template>
        </el-input>
      </div>
      
      <div class="options-row" v-if="findTerm && hasMatches" @mousedown.stop>
        <div class="match-stats">
          {{ currentIndex + 1 }}/{{ totalMatches }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { Search, Close, ArrowUp, ArrowDown, Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editor: {
    type: Object,
    required: false,
    default: null
  }
})

const emit = defineEmits(['close', 'update:visible'])

// 搜索状态
const findTerm = ref('')
const replaceTerm = ref('')
const caseSensitive = ref(false)
const findInput = ref(null)
const panel = ref(null)

// 拖拽相关状态
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const position = ref({ x: null, y: null })

// 同步caseSensitive状态
watch(() => props.editor?.storage?.findAndReplace?.isCaseSensitive, (newVal) => {
  if (newVal !== undefined) {
    caseSensitive.value = newVal
  }
}, { immediate: true })

// 启用面板拖拽功能
const startDrag = (event) => {
  // 如果点击的是关闭按钮或其他操作按钮，不启用拖拽
  if (event.target.closest('button') || event.target.closest('input')) return
  
  isDragging.value = true
  
  // 计算鼠标在面板中的相对位置
  const rect = panel.value.getBoundingClientRect()
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  
  // 如果位置还未初始化，设置初始位置
  if (position.value.x === null) {
    position.value = {
      x: rect.left,
      y: rect.top
    }
  }
  
  // 添加移动和停止拖拽事件监听
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

const handleDrag = (event) => {
  if (!isDragging.value) return
  
  // 计算新位置
  position.value = {
    x: event.clientX - dragOffset.value.x,
    y: event.clientY - dragOffset.value.y
  }
  
  // 应用新位置
  if (panel.value) {
    panel.value.style.right = 'auto'
    panel.value.style.left = `${position.value.x}px`
    panel.value.style.top = `${position.value.y}px`
  }
}

const stopDrag = () => {
  isDragging.value = false
  
  // 移除事件监听
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 监听可见性变化，当显示面板时，聚焦到搜索框并初始化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      findInput.value?.focus()
      if (props.editor?.commands) {
        props.editor.commands.startFindAndReplace()
      }
      
      // 当面板显示时，重置位置为初始位置
      if (position.value.x !== null && panel.value) {
        panel.value.style.right = 'auto'
        panel.value.style.left = `${position.value.x}px`
        panel.value.style.top = `${position.value.y}px`
      } else {
        // 如果还没有定位，使用默认值
        position.value = { x: null, y: null }
        if (panel.value) {
          panel.value.style.right = '20px'
          panel.value.style.top = '70px'
          panel.value.style.left = 'auto'
        }
      }
    })
  } else {
    if (props.editor?.commands) {
      props.editor.commands.endFindAndReplace()
    }
  }
}, { immediate: true })

// 监听编辑器中的findTerm变化
watch(() => props.editor?.storage?.findAndReplace?.findText, (newVal) => {
  if (newVal !== undefined && newVal !== findTerm.value) {
    findTerm.value = newVal
    // 如果有查找词，自动执行搜索
    if (newVal && props.visible) {
      nextTick(() => {
        updateSearch()
      })
    }
  }
}, { immediate: true })

// 清理拖拽相关事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
})

// 从编辑器存储中获取状态
const currentIndex = computed(() => props.editor?.storage?.findAndReplace?.currentIndex || 0)
const totalMatches = computed(() => props.editor?.storage?.findAndReplace?.totalMatches || 0)
const hasMatches = computed(() => totalMatches.value > 0)

// 方法
const close = () => {
  emit('update:visible', false)
  emit('close')
}

const updateSearch = () => {
  if (!props.editor?.commands) return
  
  try {
    // 设置查找词
    props.editor.commands.setFindTerm(findTerm.value)
    
    // 显式更新搜索结果
    props.editor.commands.updateFindResults()
  } catch (error) {
    console.error('更新搜索时出错:', error)
  }
}

const findNext = () => {
  if (!props.editor?.commands) return
  
  try {
    // 先更新搜索结果
    props.editor.commands.updateFindResults()
    
    // 然后查找下一个
    props.editor.commands.findNext()
  } catch (error) {
    console.error('查找下一项时发生错误:', error)
  }
}

const findPrevious = () => {
  if (!props.editor?.commands) return
  
  try {
    // 先更新搜索结果
    props.editor.commands.updateFindResults()
    
    // 然后查找上一个
    props.editor.commands.findPrevious()
  } catch (error) {
    console.error('查找上一项时发生错误:', error)
  }
}

const replaceAndFindNext = () => {
  if (!hasMatches.value || !props.editor?.commands) return
  
  try {
    // 首先设置替换文本
    props.editor.commands.setReplaceTerm(replaceTerm.value)
    
    // 尝试执行替换，但不处理错误，让它自然抛出
    props.editor.commands.replaceCurrentMatch()
    
    // 延迟恢复查找，因为替换操作可能已经成功即使有错误
    setTimeout(() => {
      try {
        if (findTerm.value && props.editor?.commands) {
          // 尝试重新设置查找词，这将刷新匹配项
          props.editor.commands.setFindTerm(findTerm.value)
        }
      } catch (e) {
        console.warn('恢复查找失败', e)
      }
    }, 100)
  } catch (error) {
    console.error('替换时发生错误:', error)
    
    // 尽管有错误，替换操作可能已经成功
    // 延迟恢复查找
    setTimeout(() => {
      try {
        if (findTerm.value && props.editor?.commands) {
          // 尝试重新设置查找词，这将刷新匹配项
          props.editor.commands.setFindTerm(findTerm.value)
        }
      } catch (e) {
        console.warn('恢复查找失败', e)
      }
    }, 100)
  }
}

const replaceAll = () => {
  if (!hasMatches.value || !props.editor?.commands) return
  
  // 获取初始匹配数量，用于反馈
  const initialMatchCount = totalMatches.value
  
  try {
    // 首先确保替换文本已设置
    props.editor.commands.setReplaceTerm(replaceTerm.value)
    
    // 尝试执行全部替换，但不处理错误，让它自然抛出
    props.editor.commands.replaceAllMatches()
    
    // 如果没有抛出错误，显示成功消息
    ElMessage({
      message: `已替换 ${initialMatchCount} 处匹配内容`,
      type: 'success',
      duration: 2000
    })
    
    // 延迟恢复查找
    setTimeout(() => {
      try {
        if (findTerm.value && props.editor?.commands) {
          // 尝试重新设置查找词，这将刷新匹配项
          props.editor.commands.setFindTerm(findTerm.value)
        }
      } catch (e) {
        console.warn('恢复查找失败', e)
      }
    }, 100)
  } catch (error) {
    console.error('替换所有匹配项时发生错误:', error)
    
    // 尽管有错误，替换操作可能已经成功
    // 显示可能成功的消息
    ElMessage({
      message: `替换操作已执行，可能已替换了 ${initialMatchCount} 处匹配内容`,
      type: 'info',
      duration: 2000
    })
    
    // 延迟恢复查找
    setTimeout(() => {
      try {
        if (findTerm.value && props.editor?.commands) {
          // 尝试重新设置查找词，这将刷新匹配项
          props.editor.commands.setFindTerm(findTerm.value)
        }
      } catch (e) {
        console.warn('恢复查找失败', e)
      }
    }, 100)
  }
}

const toggleCaseSensitivity = () => {
  if (props.editor?.commands) {
    // 将UI状态与后端状态同步
    caseSensitive.value = !caseSensitive.value
    
    // 触发后端命令
    props.editor.commands.toggleCaseSensitivity()
    
    // 重新触发搜索以更新结果
    setTimeout(() => {
      if (findTerm.value) {
        updateSearch()
      }
    }, 10)
  }
}

// 监听编辑器的变化以保持状态同步
watch(() => props.editor, (newEditor) => {
  if (newEditor?.commands && props.visible) {
    newEditor.commands.startFindAndReplace()
  }
}, { immediate: true })

// 清理
onMounted(() => {
  if (props.visible && props.editor?.commands) {
    props.editor.commands.startFindAndReplace()
  }
})
</script>

<style lang="scss">
/* 全局查找结果高亮样式 */
:global(.find-result) {
  background-color: rgba(255, 230, 0, 0.4) !important;
  border-radius: 2px !important;
  padding: 0 !important;
  margin: 0 !important;
  border-bottom: 1px dashed rgba(255, 150, 0, 0.6) !important;
}

:global(.find-result-active) {
  background-color: rgba(255, 150, 0, 0.6) !important;
  border-radius: 2px !important;
  box-shadow: 0 0 0 1px rgba(255, 150, 0, 0.7) !important;
  border-bottom: 1px solid rgba(255, 100, 0, 0.8) !important;
}
</style>

<style lang="scss" scoped>
.find-replace-panel {
  position: fixed;
  top: 70px;
  right: 20px;
  width: 350px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 2600;
  transform: translateY(-10px);
  opacity: 0;
  pointer-events: none;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
  transition: transform 0.25s ease, opacity 0.25s ease;
  backdrop-filter: blur(10px);
  cursor: move; /* 使整个面板可拖动 */
  
  &.is-active {
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
  }
  
  .panel-content {
    padding: 10px;
    cursor: default; /* 恢复内容区域的默认光标 */
    
    .search-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      gap: 4px;
      
      .search-input {
        flex: 1;
        
        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px var(--el-border-color) inset;
          
          &.is-focus {
            box-shadow: 0 0 0 1px var(--el-color-primary) inset;
          }
        }
      }
      
      .close-button {
        padding: 5px;
        margin-left: 4px;
        border-radius: 4px;
        height: 28px;
        width: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:hover {
          background-color: var(--el-fill-color);
        }
        
        .el-icon {
          font-size: 16px;
          color: var(--el-text-color-secondary);
          
          &:hover {
            color: var(--el-color-danger);
          }
        }
      }
    }
    
    .replace-row {
      margin-bottom: 8px;
      
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px var(--el-border-color) inset;
        
        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
    }
  }
  
  :deep(.el-input-group__append) {
    padding: 0;
    display: flex;
    
    .el-button {
      border-radius: 0;
      border: none;
      margin: 0;
      height: 100%;
      padding: 0 8px;
      font-size: 12px;
      
      &:not(:last-child) {
        border-right: 1px solid var(--el-border-color-light);
      }
      
      &:hover {
        background-color: var(--el-fill-color-light);
      }
      
      &:disabled {
        color: var(--el-text-color-placeholder);
        background-color: var(--el-fill-color);
      }
    }
    
    .case-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      color: var(--el-text-color-secondary);
      transition: all 0.2s ease;
      
      &.is-active {
        color: var(--el-color-primary);
        background-color: rgba(var(--el-color-primary-rgb), 0.1);
      }
      
      .case-icon {
        font-size: 14px;
        font-weight: 600;
        letter-spacing: -1px;
      }
    }
  }
}

.options-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  min-height: 22px;
  
  .match-stats {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    padding: 2px 8px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
  }
}
</style> 