import lark_oapi as lark
from lark_oapi.api.drive.v1 import *


# 使用示例
app_id = "cli_a7ec84e097b9900e"
app_secret = "xkqpfDJ320Xj17DTr3zvUf4H57ap11sE"
client = lark.Client.builder() \
    .app_id(app_id) \
    .app_secret(app_secret) \
    .log_level(lark.LogLevel.DEBUG) \
    .build()

class FeiShuDriveController:
    def __init__(self):
        pass

    def 列出文件夹列表(self):
        """
        列出知识空间列表
        """
        # 构造请求对象
        request: ListFileRequest = ListFileRequest.builder() \
            .order_by("EditedTime") \
            .direction("DESC") \
            .build()

        response: ListFileResponse = client.drive.v1.file.list(request)
        return lark.JSON.marshal(response.data, indent=4)

# 列出文件夹列表
print(FeiShuDriveController().列出文件夹列表())
