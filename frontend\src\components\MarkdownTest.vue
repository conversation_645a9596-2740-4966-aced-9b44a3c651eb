<template>
  <div class="markdown-test">
    <h2>Vue Markdown Editor 测试</h2>

    <div class="test-section">
      <h3>预览组件测试</h3>
      <VMdPreview :text="text3"></VMdPreview>
    </div>

    <div class="test-section">
      <h3>基础编辑器测试</h3>
      <v-md-editor v-model="text1" height="300px"></v-md-editor>
    </div>

    <div class="test-section">
      <h3>CodeMirror 编辑器测试</h3>
      <VMdEditor v-model="text2" height="300px"></VMdEditor>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const text1 = ref(`# 基础编辑器测试

这是一个**粗体**文本和*斜体*文本。

## 代码块

\`\`\`javascript
console.log('Hello World!')
\`\`\`

## 列表

- 项目 1
- 项目 2
- 项目 3

## 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
`)

const text2 = ref(`# CodeMirror 编辑器测试

这是使用 **CodeMirror** 的高级编辑器。

## 特性

- 语法高亮
- 代码折叠
- 智能提示

\`\`\`python
def hello_world():
    print("Hello, World!")
\`\`\`
`)

const text3 = ref(`# 预览组件测试

这是一个纯预览组件，用于显示已经编写好的 Markdown 内容。

## 格式化测试

### 文本样式
- **粗体文本**
- *斜体文本*
- ~~删除线文本~~
- \`行内代码\`

### 引用
> 这是一个引用块
> 可以包含多行内容

### 代码块
\`\`\`css
.example {
  color: #333;
  font-size: 16px;
}
\`\`\`

### 链接和图片
[Vue.js](https://vuejs.org/)

### 数学公式（如果支持）
$E = mc^2$
`)
</script>

<style scoped>
.markdown-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}
</style>
