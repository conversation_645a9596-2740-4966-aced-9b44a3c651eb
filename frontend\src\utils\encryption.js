/**
 * 书籍加密解密工具
 * 使用AES-256-CBC算法进行内容加密
 */

import CryptoJS from 'crypto-js'

export default class EncryptionService {
  /**
   * 从密码派生加密密钥
   * @param {string} password - 用户输入的密码
   * @param {string} salt - 盐值，用于增强密钥安全性
   * @returns {Object} 派生的密钥
   */
  static deriveKey(password, salt) {
    const key = CryptoJS.PBKDF2(password, salt || CryptoJS.lib.WordArray.random(128/8), {
      keySize: 256/32,
      iterations: 1000
    })
    return key
  }
  
  /**
   * 加密内容
   * @param {string} content - 要加密的内容
   * @param {string} password - 用户密码
   * @param {string} salt - 盐值
   * @param {string} iv - 初始化向量，如果不提供则随机生成
   * @returns {Object} 加密结果，包含密文、iv和salt
   */
  static encrypt(content, password, salt, iv) {
    // 确保有盐值和IV
    const usedSalt = salt || CryptoJS.lib.WordArray.random(128/8).toString(CryptoJS.enc.Hex)
    const usedIv = iv || CryptoJS.lib.WordArray.random(128/8).toString(CryptoJS.enc.Hex)
    
    // 从密码派生密钥
    const key = this.deriveKey(password, usedSalt)
    
    // 加密内容
    const encrypted = CryptoJS.AES.encrypt(content, key, {
      iv: CryptoJS.enc.Hex.parse(usedIv),
      padding: CryptoJS.pad.Pkcs7,
      mode: CryptoJS.mode.CBC
    })
    
    return {
      content: encrypted.toString(),
      iv: usedIv,
      salt: usedSalt
    }
  }
  
  /**
   * 解密内容
   * @param {string} encryptedContent - 加密的内容
   * @param {string} password - 用户密码
   * @param {string} salt - 盐值
   * @param {string} iv - 初始化向量
   * @returns {string|null} 解密后的内容，解密失败返回null
   */
  static decrypt(encryptedContent, password, salt, iv) {
    try {
      // 从密码派生密钥
      const key = this.deriveKey(password, salt)
      
      // 解密内容
      const decrypted = CryptoJS.AES.decrypt(encryptedContent, key, {
        iv: CryptoJS.enc.Hex.parse(iv),
        padding: CryptoJS.pad.Pkcs7,
        mode: CryptoJS.mode.CBC
      })
      
      // 转换为UTF-8字符串
      return decrypted.toString(CryptoJS.enc.Utf8)
    } catch (e) {
      console.error('解密失败:', e)
      return null
    }
  }
  
  /**
   * 生成用于验证密码的校验和
   * @param {string} password - 用户密码
   * @param {string} salt - 盐值
   * @returns {Object} 包含校验和、盐值和IV的对象
   */
  static generateChecksum(password) {
    // 生成盐值和IV
    const salt = CryptoJS.lib.WordArray.random(128/8).toString(CryptoJS.enc.Hex)
    const iv = CryptoJS.lib.WordArray.random(128/8).toString(CryptoJS.enc.Hex)
    
    // 创建固定的验证字符串
    const verificationString = "BOOK_PASSWORD_VERIFICATION"
    
    // 加密验证字符串
    const { content: checksum } = this.encrypt(verificationString, password, salt, iv)
    
    return {
      checksum,
      salt,
      iv
    }
  }
  
  /**
   * 验证密码是否正确
   * @param {string} password - 待验证的密码
   * @param {string} checksum - 校验和
   * @param {string} salt - 盐值
   * @param {string} iv - 初始化向量
   * @returns {boolean} 密码是否正确
   */
  static verifyPassword(password, checksum, salt, iv) {
    try {
      const decrypted = this.decrypt(checksum, password, salt, iv)
      return decrypted === "BOOK_PASSWORD_VERIFICATION"
    } catch (e) {
      return false
    }
  }
} 