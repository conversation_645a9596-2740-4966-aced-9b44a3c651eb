const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const os = require('os');
const { nanoid } = require('../utils/idGenerator');

class UserController {
  constructor(baseDir) {
    this.baseDir = baseDir;
    this.userDataFile = path.join(baseDir, 'config', 'user_data.json');
    this.activationFile = path.join(baseDir, 'config', 'activation.json');
    this.sessionsDir = path.join(baseDir, 'sessions');
  }

  async initialize() {
    try {
      await fs.ensureDir(path.dirname(this.userDataFile));
      await fs.ensureDir(path.dirname(this.activationFile));
      await fs.ensureDir(this.sessionsDir);
      console.log('UserController 初始化完成');
    } catch (error) {
      console.error('UserController 初始化失败:', error);
      throw error;
    }
  }

  // ==================== 用户认证 ====================
  async login(userData) {
    try {
      const { username, password, rememberMe = false } = userData;
      
      if (!username || !password) {
        return {
          status: 'error',
          message: '用户名和密码不能为空'
        };
      }

      // 验证用户凭据（这里可以集成真实的认证系统）
      const isValid = await this.validateCredentials(username, password);
      
      if (!isValid) {
        return {
          status: 'error',
          message: '用户名或密码错误'
        };
      }

      // 创建会话
      const session = await this.createSession(username, rememberMe);
      
      // 更新用户数据
      await this.updateUserData({
        username,
        lastLoginAt: new Date().toISOString(),
        loginCount: (await this.getUserData())?.loginCount + 1 || 1
      });

      return {
        status: 'success',
        message: '登录成功',
        data: {
          user: {
            username,
            sessionId: session.id,
            expiresAt: session.expiresAt
          }
        }
      };
    } catch (error) {
      console.error('用户登录失败:', error);
      return {
        status: 'error',
        message: '登录失败',
        error: error.message
      };
    }
  }

  async validateCredentials(username, password) {
    try {
      // 这里可以实现真实的用户验证逻辑
      // 目前返回简单的验证（实际应用中应该连接到用户数据库）
      
      const userData = await this.getUserData();
      
      if (!userData || !userData.username) {
        // 首次登录，创建用户
        const hashedPassword = this.hashPassword(password);
        await this.updateUserData({
          username,
          passwordHash: hashedPassword,
          createdAt: new Date().toISOString()
        });
        return true;
      }

      // 验证现有用户
      const hashedPassword = this.hashPassword(password);
      return userData.username === username && userData.passwordHash === hashedPassword;
    } catch (error) {
      console.error('验证用户凭据失败:', error);
      return false;
    }
  }

  async createSession(username, rememberMe = false) {
    try {
      const sessionId = nanoid();
      const expiresAt = new Date();
      
      if (rememberMe) {
        expiresAt.setDate(expiresAt.getDate() + 30); // 30天
      } else {
        expiresAt.setHours(expiresAt.getHours() + 24); // 24小时
      }

      const session = {
        id: sessionId,
        username,
        createdAt: new Date().toISOString(),
        expiresAt: expiresAt.toISOString(),
        rememberMe,
        lastAccessAt: new Date().toISOString()
      };

      const sessionFile = path.join(this.sessionsDir, `${sessionId}.json`);
      await fs.writeJson(sessionFile, session, { spaces: 2 });

      return session;
    } catch (error) {
      console.error('创建会话失败:', error);
      throw error;
    }
  }

  async validateSession(sessionId) {
    try {
      const sessionFile = path.join(this.sessionsDir, `${sessionId}.json`);
      
      if (!await fs.pathExists(sessionFile)) {
        return null;
      }

      const session = await fs.readJson(sessionFile);
      const now = new Date();
      const expiresAt = new Date(session.expiresAt);

      if (now > expiresAt) {
        // 会话已过期，删除
        await fs.remove(sessionFile);
        return null;
      }

      // 更新最后访问时间
      session.lastAccessAt = now.toISOString();
      await fs.writeJson(sessionFile, session, { spaces: 2 });

      return session;
    } catch (error) {
      console.error('验证会话失败:', error);
      return null;
    }
  }

  async logout(sessionId) {
    try {
      if (sessionId) {
        const sessionFile = path.join(this.sessionsDir, `${sessionId}.json`);
        if (await fs.pathExists(sessionFile)) {
          await fs.remove(sessionFile);
        }
      }

      return {
        status: 'success',
        message: '退出登录成功'
      };
    } catch (error) {
      console.error('退出登录失败:', error);
      return {
        status: 'error',
        message: '退出登录失败',
        error: error.message
      };
    }
  }

  // ==================== 激活管理 ====================
  async check_activation_status() {
    try {
      if (!await fs.pathExists(this.activationFile)) {
        return {
          status: 'success',
          message: '获取激活状态成功',
          data: {
            isActivated: false,
            activationType: 'trial',
            trialDaysLeft: 30,
            features: this.getTrialFeatures()
          }
        };
      }

      const activationData = await fs.readJson(this.activationFile);
      const now = new Date();
      
      if (activationData.type === 'permanent') {
        return {
          status: 'success',
          message: '获取激活状态成功',
          data: {
            isActivated: true,
            activationType: 'permanent',
            activatedAt: activationData.activatedAt,
            features: this.getPermanentFeatures()
          }
        };
      }

      if (activationData.type === 'trial') {
        const expiresAt = new Date(activationData.expiresAt);
        const daysLeft = Math.max(0, Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24)));
        
        return {
          status: 'success',
          message: '获取激活状态成功',
          data: {
            isActivated: daysLeft > 0,
            activationType: 'trial',
            trialDaysLeft: daysLeft,
            expiresAt: activationData.expiresAt,
            features: daysLeft > 0 ? this.getTrialFeatures() : this.getExpiredFeatures()
          }
        };
      }

      return {
        status: 'success',
        message: '获取激活状态成功',
        data: {
          isActivated: false,
          activationType: 'unknown',
          features: this.getExpiredFeatures()
        }
      };
    } catch (error) {
      console.error('检查激活状态失败:', error);
      return {
        status: 'error',
        message: '检查激活状态失败',
        error: error.message
      };
    }
  }

  async verify_activation_code(activationData) {
    try {
      const { code, machineCode } = activationData;
      
      if (!code) {
        return {
          status: 'error',
          message: '激活码不能为空'
        };
      }

      // 验证激活码（这里应该连接到激活服务器）
      const isValid = await this.validateActivationCode(code, machineCode);
      
      if (!isValid) {
        return {
          status: 'error',
          message: '激活码无效或已过期'
        };
      }

      // 保存激活信息
      const activation = {
        code,
        machineCode,
        type: 'permanent',
        activatedAt: new Date().toISOString(),
        features: this.getPermanentFeatures()
      };

      await fs.writeJson(this.activationFile, activation, { spaces: 2 });

      return {
        status: 'success',
        message: '激活成功',
        data: activation
      };
    } catch (error) {
      console.error('验证激活码失败:', error);
      return {
        status: 'error',
        message: '验证激活码失败',
        error: error.message
      };
    }
  }

  async validateActivationCode(code, machineCode) {
    try {
      // 这里应该实现真实的激活码验证逻辑
      // 可以连接到激活服务器或使用本地验证算法
      
      // 简单的示例验证（实际应用中应该更复杂）
      const validCodes = [
        'PVV-NOVEL-2024-PREMIUM',
        'PVV-TRIAL-EXTENDED-2024'
      ];
      
      return validCodes.includes(code.toUpperCase());
    } catch (error) {
      console.error('验证激活码失败:', error);
      return false;
    }
  }

  async generate_activation_code_with_params(params) {
    try {
      // 这里可以实现激活码生成逻辑
      // 通常这个功能在服务器端实现
      
      const { type = 'trial', duration = 30, features = [] } = params;
      
      const code = `PVV-${type.toUpperCase()}-${Date.now().toString(36).toUpperCase()}`;
      
      return {
        status: 'success',
        message: '生成激活码成功',
        data: {
          code,
          type,
          duration,
          features,
          generatedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('生成激活码失败:', error);
      return {
        status: 'error',
        message: '生成激活码失败',
        error: error.message
      };
    }
  }

  // ==================== 机器码管理 ====================
  async get_machine_code() {
    try {
      const machineId = this.generateMachineId();
      
      return {
        status: 'success',
        message: '获取机器码成功',
        data: machineId
      };
    } catch (error) {
      console.error('获取机器码失败:', error);
      return {
        status: 'error',
        message: '获取机器码失败',
        error: error.message
      };
    }
  }

  generateMachineId() {
    try {
      // 收集机器特征信息
      const features = [
        os.hostname(),
        os.platform(),
        os.arch(),
        os.cpus()[0]?.model || 'unknown',
        os.totalmem().toString()
      ];
      
      // 生成机器ID
      const hash = crypto.createHash('sha256');
      hash.update(features.join('|'));
      
      return hash.digest('hex').substring(0, 32).toUpperCase();
    } catch (error) {
      console.error('生成机器ID失败:', error);
      return 'UNKNOWN-MACHINE-ID';
    }
  }

  // ==================== 用户数据管理 ====================
  async getUserData() {
    try {
      if (!await fs.pathExists(this.userDataFile)) {
        return null;
      }
      return await fs.readJson(this.userDataFile);
    } catch (error) {
      console.error('获取用户数据失败:', error);
      return null;
    }
  }

  async updateUserData(userData) {
    try {
      const existingData = await this.getUserData() || {};
      const updatedData = {
        ...existingData,
        ...userData,
        updatedAt: new Date().toISOString()
      };
      
      await fs.writeJson(this.userDataFile, updatedData, { spaces: 2 });
      return updatedData;
    } catch (error) {
      console.error('更新用户数据失败:', error);
      throw error;
    }
  }

  // ==================== 功能权限管理 ====================
  getTrialFeatures() {
    return {
      maxProjects: 3,
      maxBooksPerProject: 5,
      maxChaptersPerBook: 50,
      aiChatLimit: 100,
      exportFormats: ['txt', 'markdown'],
      backupEnabled: true,
      cloudSync: false,
      advancedFeatures: false
    };
  }

  getPermanentFeatures() {
    return {
      maxProjects: -1, // 无限制
      maxBooksPerProject: -1,
      maxChaptersPerBook: -1,
      aiChatLimit: -1,
      exportFormats: ['txt', 'markdown', 'pdf', 'epub', 'docx'],
      backupEnabled: true,
      cloudSync: true,
      advancedFeatures: true
    };
  }

  getExpiredFeatures() {
    return {
      maxProjects: 1,
      maxBooksPerProject: 1,
      maxChaptersPerBook: 10,
      aiChatLimit: 0,
      exportFormats: ['txt'],
      backupEnabled: false,
      cloudSync: false,
      advancedFeatures: false
    };
  }

  // ==================== 工具方法 ====================
  hashPassword(password) {
    return crypto.createHash('sha256').update(password).digest('hex');
  }

  async cleanupExpiredSessions() {
    try {
      const sessionFiles = await fs.readdir(this.sessionsDir);
      const now = new Date();
      let cleanedCount = 0;

      for (const file of sessionFiles) {
        try {
          const sessionFile = path.join(this.sessionsDir, file);
          const session = await fs.readJson(sessionFile);
          const expiresAt = new Date(session.expiresAt);

          if (now > expiresAt) {
            await fs.remove(sessionFile);
            cleanedCount++;
          }
        } catch (error) {
          console.error(`清理会话文件失败: ${file}`, error);
        }
      }

      console.log(`清理了 ${cleanedCount} 个过期会话`);
    } catch (error) {
      console.error('清理过期会话失败:', error);
    }
  }

  // ==================== 清理资源 ====================
  cleanup() {
    // 清理过期会话
    this.cleanupExpiredSessions().catch(error => {
      console.error('清理过期会话失败:', error);
    });
  }
}

module.exports = UserController;
