import json
import os
import subprocess
import threading
import time
import logging
import platform
import sys
import shlex
from pathlib import Path

class InjectProjectController:
    def __init__(self):
        self.base_dir = os.path.abspath(os.curdir)
        self.config_dir = os.path.join(self.base_dir, "inject_project")
        Path(self.config_dir).mkdir(parents=True, exist_ok=True)
        
        self.config_file = os.path.join(self.config_dir, "projects_config.json")
        self.processes = {}
        
        # 当前系统类型
        self.system = platform.system()
        
        # 初始化日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.config_dir, 'project.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        # 确保配置文件存在
        if not os.path.exists(self.config_file):
            self._write_config({})

    def _read_config(self):
        """读取配置文件"""
        try:
            if not os.path.exists(self.config_file):
                return {}
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"读取配置文件失败: {str(e)}")
            return {}

    def _write_config(self, config):
        """写入配置文件"""
        try:
            # 创建临时文件
            temp_file = self.config_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            # 重命名临时文件为正式文件（原子操作）
            os.replace(temp_file, self.config_file)
            return True
        except Exception as e:
            logging.error(f"写入配置文件失败: {str(e)}")
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            return False

    def load_projects(self):
        """加载所有项目配置"""
        try:
            projects = self._read_config()
            return {'status': 'success', 'message': '加载成功', 'data': projects}
        except Exception as e:
            logging.error(f"加载项目配置失败: {str(e)}")
            return {'status': 'error', 'message': f'加载失败: {str(e)}'}

    def add_project(self, profile_data={}):
        """添加新项目
        Args:
            project_name: 项目名称
            project_path: 项目路径
            project_type: 项目类型 (exe/py/sh/app/bin)
            start_params: 启动参数，可选
            check_method: 检查方式 (process/port)
            check_param: 检查参数（端口号）
        """
        project_name = profile_data.get('project_name')
        project_path = profile_data.get('project_path')
        project_type = profile_data.get('project_type')
        start_params = profile_data.get('start_params')
        check_method = profile_data.get('check_method', 'process')
        check_param = profile_data.get('check_param')
        
        try:
            # 读取当前配置
            projects = self._read_config()
            
            # 检查项目是否已存在
            if project_name in projects:
                return {'status': 'error', 'message': f'项目 {project_name} 已存在'}
            
            # 验证项目路径
            if not os.path.exists(project_path):
                return {'status': 'error', 'message': f'项目路径不存在: {project_path}'}
            
            # 生成启动命令（跨平台支持）
            start_command = self._generate_start_command(project_path, project_type, start_params)
            if isinstance(start_command, dict) and start_command.get('status') == 'error':
                return start_command
            
            # 创建项目配置
            project_config = {
                'path': project_path,
                'type': project_type,
                'status': 'stopped',
                'check_method': check_method,
                'check_param': int(check_param) if check_param and check_method == 'port' else None,
                'start_command': start_command,
                'start_params': start_params,  # 保存原始启动参数
                'create_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'last_run_time': None,
                'run_count': 0
            }
            
            # 更新配置文件
            projects[project_name] = project_config
            if not self._write_config(projects):
                return {'status': 'error', 'message': '保存配置失败'}
            
            return {'status': 'success', 'message': '添加成功', 'data': project_config}
            
        except Exception as e:
            logging.error(f"添加项目失败: {str(e)}")
            return {'status': 'error', 'message': f'添加失败: {str(e)}'}

    def _generate_start_command(self, project_path, project_type, start_params=None):
        """跨平台生成启动命令
        
        Args:
            project_path: 项目文件路径
            project_type: 项目类型
            start_params: 启动参数
            
        Returns:
            str: 启动命令字符串或错误信息
        """
        # 统一路径格式
        project_path = os.path.normpath(project_path)
        
        # 根据不同的项目类型和平台生成启动命令
        if project_type == 'exe':
            # .exe文件 (Windows特有)
            if self.system != 'Windows':
                return {'status': 'error', 'message': f'EXE文件仅支持Windows系统'}
                
            if not project_path.lower().endswith('.exe'):
                return {'status': 'error', 'message': f'无效的EXE文件: {project_path}'}
                
            start_command = f'"{project_path}"'
            
        elif project_type == 'py':
            # Python文件 (跨平台)
            if not os.path.isfile(project_path) or not project_path.lower().endswith('.py'):
                return {'status': 'error', 'message': f'无效的Python文件: {project_path}'}
            
            # 根据平台选择Python解释器
            python_cmd = self._get_python_command()
            start_command = f'{python_cmd} "{project_path}"'
            
        elif project_type == 'sh':
            # Shell脚本 (主要用于macOS/Linux)
            if self.system == 'Windows':
                return {'status': 'error', 'message': 'Shell脚本在Windows上需要特殊环境，请选择其他类型'}
                
            if not os.path.isfile(project_path) or not project_path.lower().endswith('.sh'):
                return {'status': 'error', 'message': f'无效的Shell脚本: {project_path}'}
                
            # 确保有执行权限
            try:
                os.chmod(project_path, 0o755)  # rwxr-xr-x
            except:
                pass
                
            start_command = f'bash "{project_path}"'
            
        elif project_type == 'app':
            # macOS应用程序
            if self.system != 'Darwin':  # Darwin是macOS的内核名
                return {'status': 'error', 'message': 'APP文件仅支持macOS系统'}
                
            if not os.path.isdir(project_path) or not project_path.lower().endswith('.app'):
                return {'status': 'error', 'message': f'无效的macOS应用程序: {project_path}'}
                
            start_command = f'open "{project_path}"'
            
        elif project_type == 'bin':
            # 二进制可执行文件 (主要用于Linux/macOS)
            if self.system == 'Windows' and not project_path.lower().endswith('.exe'):
                return {'status': 'error', 'message': '在Windows上，二进制可执行文件应为EXE格式'}
                
            if not os.path.isfile(project_path):
                return {'status': 'error', 'message': f'无效的可执行文件: {project_path}'}
                
            # 确保有执行权限 (对Linux/macOS有效)
            if self.system != 'Windows':
                try:
                    os.chmod(project_path, 0o755)  # rwxr-xr-x
                except:
                    pass
                    
            # 在Linux/macOS上，需要添加./前缀来执行当前目录的文件
            if self.system != 'Windows':
                dir_name = os.path.dirname(project_path)
                base_name = os.path.basename(project_path)
                # 如果是相对路径，确保添加./
                if dir_name:
                    start_command = f'cd "{dir_name}" && ./{base_name}'
                else:
                    start_command = f'./{project_path}'
            else:
                start_command = f'"{project_path}"'
        else:
            return {'status': 'error', 'message': f'不支持的项目类型: {project_type}'}
        
        # 添加启动参数
        if start_params:
            start_command += f' {start_params}'
            
        return start_command

    def _get_python_command(self):
        """根据平台获取合适的Python命令"""
        # 优先使用当前Python解释器
        if hasattr(sys, 'executable') and sys.executable:
            return f'"{sys.executable}"'
        
        # 根据平台选择备选命令
        if self.system == 'Windows':
            return 'python'
        else:
            # 在macOS和Linux上，优先使用python3
            try:
                # 检查python3命令是否可用
                result = subprocess.run(
                    ['which', 'python3'], 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                if result.returncode == 0:
                    return 'python3'
                else:
                    return 'python'
            except:
                return 'python'  # 默认使用python命令

    def update_project(self, project_name, updates):
        """更新项目配置"""
        try:
            projects = self._read_config()
            
            if project_name not in projects:
                return {'status': 'error', 'message': f'项目 {project_name} 不存在'}
            
            project = projects[project_name]
            
            # 验证并更新配置
            if 'path' in updates:
                if not os.path.exists(updates['path']):
                    return {'status': 'error', 'message': f'项目路径不存在: {updates["path"]}'}
                project['path'] = updates['path']
            
            if 'type' in updates:
                project['type'] = updates['type']
            
            # 更新启动参数和命令
            if 'path' in updates or 'type' in updates or 'start_params' in updates:
                # 如果关键参数有变化，重新生成启动命令
                project_path = updates.get('path', project['path'])
                project_type = updates.get('type', project['type'])
                start_params = updates.get('start_params', project.get('start_params', ''))
                
                start_command = self._generate_start_command(project_path, project_type, start_params)
                if isinstance(start_command, dict) and start_command.get('status') == 'error':
                    return start_command
                
                project['start_command'] = start_command
                if 'start_params' in updates:
                    project['start_params'] = updates['start_params']
            
            # 更新其他参数
            for key in ['check_method', 'check_param']:
                if key in updates:
                    if key == 'check_param' and updates.get('check_method', project['check_method']) == 'port':
                        project[key] = int(updates[key]) if updates[key] else None
                    else:
                        project[key] = updates[key]
            
            # 保存更新
            if not self._write_config(projects):
                return {'status': 'error', 'message': '保存配置失败'}
            
            return {'status': 'success', 'message': '更新成功', 'data': project}
            
        except Exception as e:
            logging.error(f"更新项目失败: {str(e)}")
            return {'status': 'error', 'message': f'更新失败: {str(e)}'}

    def remove_project(self, project_name):
        """删除项目配置"""
        try:
            projects = self._read_config()
            
            if project_name not in projects:
                return {'status': 'error', 'message': f'项目 {project_name} 不存在'}
            
            # 如果项目正在运行，先停止它
            if project_name in self.processes:
                self.stop_project(project_name)
            
            # 删除配置
            del projects[project_name]
            
            if not self._write_config(projects):
                return {'status': 'error', 'message': '保存配置失败'}
            
            return {'status': 'success', 'message': '删除成功'}
            
        except Exception as e:
            logging.error(f"删除项目失败: {str(e)}")
            return {'status': 'error', 'message': f'删除失败: {str(e)}'}

    def start_project(self, project_name):
        """启动项目"""
        try:
            projects = self._read_config()
            
            if project_name not in projects:
                return {'status': 'error', 'message': f'项目 {project_name} 不存在'}
            
            project = projects[project_name]
            
            # 检查是否已在运行
            if project_name in self.processes:
                return {'status': 'error', 'message': f'项目 {project_name} 已在运行'}
            
            # 检查文件是否存在
            if not os.path.exists(project['path']):
                return {'status': 'error', 'message': f'项目文件不存在: {project["path"]}'}
            
            # 启动进程
            try:
                process = self._start_process(project_name, project['start_command'], os.path.dirname(project['path']))
                
                self.processes[project_name] = process
                
                # 更新配置
                project['status'] = 'running'
                project['last_run_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
                project['run_count'] += 1
                
                if not self._write_config(projects):
                    return {'status': 'error', 'message': '保存配置失败'}
                
                # 启动监控线程
                threading.Thread(
                    target=self._monitor_process,
                    args=(project_name, process),
                    daemon=True
                ).start()
                
                return {'status': 'success', 'message': '启动成功', 'data': project}
                
            except Exception as e:
                return {'status': 'error', 'message': f'启动失败: {str(e)}'}
            
        except Exception as e:
            logging.error(f"启动项目失败: {str(e)}")
            return {'status': 'error', 'message': f'启动失败: {str(e)}'}

    def stop_project(self, project_name):
        """停止项目"""
        if project_name not in self.processes:
            return {'status': 'error', 'message': f'项目 {project_name} 未在运行'}
        
        process = self.processes[project_name]
        
        # 首先检查进程是否已经结束
        if process.poll() is not None:
            if project_name in self.processes:
                del self.processes[project_name]
            self._update_project_status(project_name, 'stopped')
            return {'status': 'success', 'message': '进程已停止'}
        
        # 尝试终止进程
        try:
            import platform
            system = platform.system()
            
            if system == 'Windows':
                # Windows下优先使用taskkill终止进程树
                try:
                    # 设置startupinfo隐藏命令行窗口
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = 0  # SW_HIDE
                    
                    # 使用/F强制终止，/T终止整个进程树
                    subprocess.run(
                        ['taskkill', '/F', '/T', '/PID', str(process.pid)], 
                        check=True, 
                        capture_output=True,
                        startupinfo=startupinfo
                    )
                except subprocess.CalledProcessError as e:
                    logging.warning(f"taskkill 命令失败: {e}")
                    # 使用终止信号作为备选方案
                    process.terminate()
                
                # 等待进程结束（更长的超时时间）
                for _ in range(20):  # 最多等待2秒
                    if process.poll() is not None:
                        break
                    time.sleep(0.1)
            elif system == 'Darwin':
                # macOS下的处理
                try:
                    # 首先尝试使用terminate温柔终止
                    process.terminate()
                    process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    # 如果超时，尝试更强力的SIGKILL信号
                    process.kill()
                    try:
                        process.wait(timeout=1)
                    except subprocess.TimeoutExpired:
                        pass
            else:
                # Linux系统下的处理
                try:
                    # 使用进程组ID获取所有子进程
                    try:
                        import signal
                        # 发送SIGTERM到进程组
                        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                        process.wait(timeout=3)
                    except (ImportError, AttributeError, ProcessLookupError):
                        # 回退到普通终止
                        process.terminate()
                        process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    # 如果仍然失败，使用SIGKILL
                    try:
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                    except:
                        process.kill()
                    try:
                        process.wait(timeout=1)
                    except subprocess.TimeoutExpired:
                        pass
            
            # 最后检查进程是否确实已经结束
            if process.poll() is None:
                return {'status': 'error', 'message': '无法完全停止进程'}
            
            # 进程已经确认停止，进行清理工作
            if project_name in self.processes:
                del self.processes[project_name]
            self._update_project_status(project_name, 'stopped')
            
            return {'status': 'success', 'message': '停止成功'}
            
        except Exception as e:
            logging.error(f"停止进程时发生异常: {str(e)}")
            # 即使发生异常，也要检查进程是否已经停止
            if process.poll() is not None:
                if project_name in self.processes:
                    del self.processes[project_name]
                self._update_project_status(project_name, 'stopped')
                return {'status': 'success', 'message': '进程已停止'}
            return {'status': 'error', 'message': '停止进程失败'}

    def _update_project_status(self, project_name, status):
        """更新项目状态并记录日志"""
        try:
            # 更新配置
            projects = self._read_config()
            if project_name in projects:
                projects[project_name]['status'] = status
                self._write_config(projects)
            
            # 记录停止事件
            with open(os.path.join(self.config_dir, f"{project_name}_output.txt"), 'a', encoding='utf-8') as f:
                f.write(f"\n[System] Project {status}\n")
        except Exception as e:
            logging.error(f"更新项目状态失败: {str(e)}")

    def _monitor_process(self, project_name, process):
        """监控进程状态"""
        try:
            process.wait()
        finally:
            if project_name in self.processes:
                del self.processes[project_name]
                
            # 更新配置
            try:
                projects = self._read_config()
                if project_name in projects:
                    projects[project_name]['status'] = 'stopped'
                    self._write_config(projects)
            except:
                pass

    def get_project_list(self):
        """获取项目列表"""
        try:
            projects = self._read_config()
            
            # 更新运行状态
            for name, project in projects.items():
                if name in self.processes:
                    if self.processes[name].poll() is not None:
                        del self.processes[name]
                        project['status'] = 'stopped'
                else:
                    project['status'] = 'stopped'
            
            if not self._write_config(projects):
                return {'status': 'error', 'message': '保存配置失败'}
            
            return {'status': 'success', 'message': '获取成功', 'data': projects}
            
        except Exception as e:
            logging.error(f"获取项目列表失败: {str(e)}")
            return {'status': 'error', 'message': f'获取失败: {str(e)}'}

    def get_running_projects(self):
        """获取运行中的项目"""
        try:
            projects = self._read_config()
            running_projects = {}
            
            for name, project in projects.items():
                if name in self.processes and self.processes[name].poll() is None:
                    running_projects[name] = project
            
            return {'status': 'success', 'message': '获取成功', 'data': running_projects}
            
        except Exception as e:
            logging.error(f"获取运行中项目失败: {str(e)}")
            return {'status': 'error', 'message': f'获取失败: {str(e)}'}

    def get_project_output(self, project_name):
        """获取项目的输出流"""
        import webview
        if project_name not in self.processes:
            return {"status": "error", "message": "项目未运行"}
            
        process = self.processes[project_name]
        if not process or process.poll() is not None:
            return {"status": "error", "message": "项目未在运行状态"}

        # 获取输出文件内容
        output_file = os.path.join(self.config_dir, f"{project_name}_output.txt")
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Base64编码发送
                import base64
                b64content = base64.b64encode(content.encode()).decode()
                webview.windows[0].evaluate_js(f"receiveProjectOutput('{project_name}', '{str(b64content)}')")
        
        return {"status": "success"}

    def _start_process(self, project_name, command, cwd):
        """启动进程并记录输出
        Args:
            project_name: 项目名称
            command: 启动命令
            cwd: 工作目录（项目所在目录）
        """
        import webview
        import base64
        
        # 标准化工作路径，确保跨平台兼容性
        cwd = os.path.normpath(cwd)
        
        # 输出文件使用项目名称作为前缀
        output_file = os.path.join(self.config_dir, f"{project_name}_output.txt")
        
        # 清空或创建输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            start_msg = f"Starting project: {project_name}\nCommand: {command}\nWorking directory: {cwd}\nPlatform: {platform.system()} {platform.release()}\n\n"
            f.write(start_msg)
            # 发送初始消息到前端
            b64content = base64.b64encode(start_msg.encode()).decode()
            webview.windows[0].evaluate_js(f"receiveProjectOutput('{project_name}', '{str(b64content)}')")
        
        # 创建进程，确保工作目录设置正确，并根据平台设置不同参数
        kwargs = {
            'cwd': cwd,  # 使用项目目录作为工作目录
            'stdout': subprocess.PIPE,
            'stderr': subprocess.STDOUT,
            'text': True,
            'encoding': 'utf-8',
            'errors': 'replace'  # 处理非UTF-8编码的输出
        }
        
        # 根据平台确定是否使用shell和其他特殊处理
        use_shell = True  # 默认使用shell执行
        
        # 在Windows上，使用shell并隐藏窗口
        if self.system == 'Windows':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = 0  # SW_HIDE
            kwargs['startupinfo'] = startupinfo
            # 添加CREATE_NO_WINDOW标志
            if hasattr(subprocess, 'CREATE_NO_WINDOW'):
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
        
        # 在Linux/macOS上，某些命令可能需要特殊处理
        else:
            # 如果命令包含重定向、管道等shell特性，必须使用shell
            if any(char in command for char in '|&;<>()$`\\"\' \t\n*?[#~=%'):
                use_shell = True
            else:
                # 尝试将命令分割为参数列表，避免使用shell
                try:
                    cmd_args = shlex.split(command)
                    command = cmd_args
                    use_shell = False
                except:
                    # 如果分割失败，回退到使用shell
                    use_shell = True
        
        kwargs['shell'] = use_shell
        
        # 记录执行方式
        with open(output_file, 'a', encoding='utf-8') as f:
            shell_msg = f"[System] Execute mode: {'shell' if use_shell else 'direct'}\n"
            f.write(shell_msg)
            b64shell = base64.b64encode(shell_msg.encode()).decode()
            webview.windows[0].evaluate_js(f"receiveProjectOutput('{project_name}', '{str(b64shell)}')")
        
        process = subprocess.Popen(command, **kwargs)
        
        def output_reader():
            with open(output_file, 'a', encoding='utf-8') as f:
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        try:
                            f.write(output)
                            f.flush()  # 确保立即写入文件
                            # 实时发送输出到前端
                            b64content = base64.b64encode(output.encode()).decode()
                            webview.windows[0].evaluate_js(f"receiveProjectOutput('{project_name}', '{str(b64content)}')")
                        except Exception as e:
                            logging.error(f"处理输出失败: {str(e)}")
                            continue
        
        threading.Thread(target=output_reader, daemon=True).start()
        return process
