import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useRelationshipStore = defineStore('relationship', () => {
  // 状态 - 存储完整的关系数据文件
  const relationData = ref({ relations: [] }) 
  const loading = ref(false)
  const error = ref(null)
  const currentBookId = ref(null)
  
  // 计算属性 - 从完整数据中提取关系数组
  const relations = computed(() => {
    if (!relationData.value || !Array.isArray(relationData.value.relations)) {
      return []
    }
    return relationData.value.relations
  })
  
  const hasRelations = computed(() => relations.value.length > 0)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => error.value !== null)
  
  // 获取实体关系
  const getEntityRelations = (entityId) => {
    if (!entityId) return []
    return relations.value.filter(r => 
      r.source === entityId || r.target === entityId
    )
  }
  
  // 根据关系ID获取关系
  const getRelationById = (relationId) => {
    return relations.value.find(r => r.id === relationId) || null
  }
  
  // 获取关系方法 - 添加此方法作为loadRelations的别名，返回关系数组
  async function getRelations(bookId) {
    await loadRelations(bookId)
    return relations.value
  }
  
  // 加载书籍关系数据 - 获取完整的JSON文件
  async function loadRelations(bookId) {
    if (!bookId) {
      error.value = "书籍ID不能为空"
      return
    }
    
    try {
      loading.value = true
      error.value = null
      currentBookId.value = bookId
      
      const response = await window.pywebview.api.book_controller.get_book_relations(bookId)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result.status === 'success') {
        // 保存完整的关系数据对象
        relationData.value = result.data || { relations: [] }
        
        // 确保relations字段存在并且是数组
        if (!relationData.value.relations) {
          relationData.value.relations = []
        }
        
        return relations.value
      } else {
        throw new Error(result.message || '加载关系数据失败')
      }
    } catch (err) {
      console.error('加载关系数据出错:', err)
      error.value = err.message || '加载关系数据时发生错误'
      relationData.value = { relations: [] }
    } finally {
      loading.value = false
    }
  }
  
  // 保存关系数据 - 保存整个关系数据对象
  async function saveRelations() {
    if (!currentBookId.value) {
      error.value = "没有选中的书籍";
      return false;
    }
    
    try {
      loading.value = true;
      error.value = null;
      
      // 确保数据结构正确
      if (!relationData.value) {
        relationData.value = { relations: [] };
      }
      
      if (!Array.isArray(relationData.value.relations)) {
        relationData.value.relations = [];
      }
      
      // 深拷贝数据，避免引用问题
      const dataToSave = JSON.parse(JSON.stringify(relationData.value));
      
      // 使用 save_relation 保存整个关系数据对象
      const response = await window.pywebview.api.book_controller.save_relation(
        currentBookId.value,
        dataToSave  // 发送整个对象
      );
      
      const result = typeof response === 'string' ? JSON.parse(response) : response;
      
      if (result.status !== 'success') {
        throw new Error(result.message || '保存关系数据失败');
      }
      
      // 如果后端返回了更新后的关系数据，更新本地状态
      if (result.data) {
        relationData.value = result.data;
      }
      
      console.log('关系数据保存成功', relationData.value.relations.length);
      return true;
    } catch (err) {
      console.error('保存关系数据出错:', err);
      error.value = err.message || '保存关系数据时发生错误';
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 添加关系 - 修改为与其他方法一致的实现方式
  async function addRelation(relation) {
    try {
      console.log('Store: 添加关系:', JSON.stringify(relation));
      
      // 确保所需字段都存在
      if (!relation.source || !relation.target || !relation.type) {
        throw new Error('关系缺少必要字段');
      }
      
      if (!relation.bookId) {
        throw new Error('缺少书籍ID');
      }
      
      // 设置当前书籍ID
      currentBookId.value = relation.bookId;
      
      // 确保关系数组存在
      if (!relationData.value) {
        relationData.value = { relations: [] };
      }
      
      if (!Array.isArray(relationData.value.relations)) {
        relationData.value.relations = [];
      }
      
      // 生成一个临时ID
      const newRelationId = `temp_${Date.now()}`;
      
      // 创建完整的关系对象
      const newRelation = {
        ...relation,
        id: newRelationId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      console.log('准备添加新关系到内存:', newRelation);
      
      // 添加到内存中的关系数组
      relationData.value.relations.push(newRelation);
      
      // 保存整个关系数据对象到后端
      const saved = await saveRelations();
      
      if (!saved) {
        // 保存失败时回滚操作
        const index = relationData.value.relations.findIndex(r => r.id === newRelationId);
        if (index !== -1) {
          relationData.value.relations.splice(index, 1);
        }
        throw new Error('保存关系数据失败');
      }
      
      console.log('关系添加成功并已保存');
      return newRelation;
    } catch (error) {
      console.error('Store: 添加关系失败:', error);
      throw error;
    }
  }
  
  // 更新关系 - 在本地更新后保存整个关系数据
  async function updateRelation(relationId, updatedData) {
    try {
      if (!currentBookId.value) {
        throw new Error("没有选中的书籍");
      }
      
      // 确保关系数组存在
      if (!relationData.value || !Array.isArray(relationData.value.relations)) {
        throw new Error('关系数据无效');
      }
      
      const index = relationData.value.relations.findIndex(r => r.id === relationId);
      
      if (index === -1) {
        throw new Error(`未找到ID为${relationId}的关系`);
      }
      
      // 保存原始关系用于回滚
      const originalRelation = { ...relationData.value.relations[index] };
      
      // 更新关系 - 确保保留原始关系的某些字段
      relationData.value.relations[index] = {
        ...originalRelation,  // 保留原始数据
        ...updatedData,       // 应用更新的数据
        id: relationId,       // 确保ID不变
        updated_at: new Date().toISOString(),
        created_at: originalRelation.created_at || new Date().toISOString()
      };
      
      // 保存整个关系数据对象
      const saved = await saveRelations();
      
      if (!saved) {
        // 保存失败时回滚操作
        relationData.value.relations[index] = originalRelation;
        throw new Error('更新关系失败');
      }
      
      return relationData.value.relations[index];
    } catch (err) {
      console.error('更新关系出错:', err);
      error.value = err.message || '更新关系时发生错误';
      throw err;
    }
  }
  
  // 删除关系 - 在本地删除后保存整个关系数据
  async function deleteRelation(relationId) {
    try {
      if (!currentBookId.value) {
        throw new Error("没有选中的书籍")
      }
      
      // 确保关系数组存在
      if (!relationData.value || !Array.isArray(relationData.value.relations)) {
        throw new Error('关系数据无效')
      }
      
      const index = relationData.value.relations.findIndex(r => r.id === relationId)
      
      if (index === -1) {
        throw new Error(`未找到ID为${relationId}的关系`)
      }
      
      // 保存原始关系用于回滚
      const deletedRelation = relationData.value.relations[index]
      
      // 从数组中删除关系
      relationData.value.relations.splice(index, 1)
      
      // 使用 delete_relation API，传递整个更新后的关系数据
      const response = await window.pywebview.api.book_controller.delete_relation(
        currentBookId.value,
        relationData.value  // 发送更新后的整个关系数据
      )
      
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result.status !== 'success') {
        // 删除失败时回滚操作
        relationData.value.relations.splice(index, 0, deletedRelation)
        throw new Error(result.message || '删除关系失败')
      }
      
      // 如果后端返回了更新后的关系数据，更新本地状态
      if (result.data) {
        relationData.value = result.data
      }
      
      return true
    } catch (err) {
      console.error('删除关系出错:', err)
      error.value = err.message || '删除关系时发生错误'
      throw err
    }
  }
  
  // 重置状态
  function resetState() {
    relationData.value = { relations: [] }
    loading.value = false
    error.value = null
    currentBookId.value = null
  }
  
  return {
    // 状态
    relations,  // 计算属性 - 关系数组
    relationData,  // 原始数据对象
    loading,
    error,
    currentBookId,
    
    // 计算属性
    hasRelations,
    isLoading,
    hasError,
    
    // 方法
    getEntityRelations,
    getRelationById,
    getRelations,
    loadRelations,
    saveRelations,
    addRelation,
    updateRelation,
    deleteRelation,
    resetState
  }
}) 