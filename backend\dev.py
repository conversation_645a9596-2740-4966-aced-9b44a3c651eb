import re
import time
import os
import sys
import subprocess
import threading
import argparse

import webview



from backend.bridge.API import API
from backend.pvvruntime.Runtime import WebViewApp

import os
import webbrowser
from functools import wraps

# 全局变量来存储 Vite URL
vite_url = None
api = API()


def remove_ansi_escape_sequences(text):
    """ 移除 ANSI 转义字符 """
    ansi_escape_pattern = re.compile(r'\x1B\[[0-?9;]*[mK]')
    return ansi_escape_pattern.sub('', text)


def run_process(command, cwd):
    """ 启动子进程并返回进程对象 """
    return subprocess.Popen(command, cwd=cwd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding='utf-8',
                            text=True, shell=True)


def run_dev_mode():
    vite_path = os.path.join(os.path.abspath(os.pardir), "frontend")
    print("Starting Vite development server...")
    return run_process(["pnpm", "run", "dev"], vite_path)


def run_build():
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(project_root)  # 回到项目根目录
    vite_path = os.path.join(project_root, "frontend")
    print(vite_path)
    print("Building the project...")
    # This is crucial because the subprocess needs existing paths (like the one to find 'pnpm'). [1]
    env = os.environ.copy()

    # 2. Set/update the NODE_OPTIONS variable to increase the memory limit.
    # This tells any Node.js process started by this subprocess to use up to 32GB of heap space.
    env["NODE_OPTIONS"] = "--max-old-space-size=32768 --max-semi-space-size=4096"

    # 设置额外的环境变量来优化构建
    env["NODE_MAX_MEMORY"] = "32768"
    env["UV_THREADPOOL_SIZE"] = "128"  # 增加线程池大小

    # 使用带内存限制的构建命令
    result = subprocess.run(["pnpm", "run", "build"], cwd=vite_path, shell=True, env=env)

    if result.returncode == 0:
        print("Build completed successfully.")
    else:
        print("Build failed.")


def read_output(process, url_found_event):
    """ 读取 Vite 进程的输出并查找 URL """
    global vite_url  # 使用全局变量
    while True:
        output = process.stdout.readline()
        if output == "" and process.poll() is not None:
            break
        if output:
            clean_output = remove_ansi_escape_sequences(output.strip())
            print(clean_output)  # 打印到控制台

            # 查找包含 URL 的行
            match = re.search(r'Local:\s+(http://\S+)', clean_output)
            if match:
                vite_url = match.group(1)  # 提取 URL
                print(f"Vite server is running at: {vite_url}")
                url_found_event.set()  # 通知主线程 URL 已找到


def start_webview(url, js_api):
    app = WebViewApp()
    app.run(url, js_api)

