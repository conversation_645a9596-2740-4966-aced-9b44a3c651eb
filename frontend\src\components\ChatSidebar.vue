<template>
  <div 
    class="chat-sidebar-container" 
    :class="{ collapsed: !visible }"
    ref="sidebarElement"
  >
    <!-- 添加调整宽度的把手 -->
    <div 
      class="resize-handle" 
      :class="{ dragging: isDragging }"
      @mousedown="startResize"
      @touchstart="startResize"
    ></div>
    
    <div class="chat-sidebar-content">
      <transition name="chat-slide">
        <div class="chat-sidebar" v-if="visible">
          <!-- 会话列表或聊天面板 -->
          <div v-if="!selectedChatId" class="chat-content">
            <!-- 搜索和操作区域 -->
            <div class="chat-search">
              <el-input
                v-model="searchQuery"
                placeholder="搜索会话..."
                prefix-icon="Search"
                clearable
              />
              <el-button 
                type="primary" 
                @click="createNewChat"
                class="new-chat-btn"
              >
                <el-icon><Plus /></el-icon>
                <span>新建会话</span>
              </el-button>
            </div>
            
            <!-- 会话列表 -->
            <div class="chat-list" v-loading="loading">
              <div v-if="filteredChats.length === 0 && !loading" class="empty-chats">
                <el-empty description="暂无会话" />
                <el-button type="primary" @click="createNewChat">创建第一个会话</el-button>
              </div>
              
              <div 
                v-for="chat in filteredChats" 
                :key="chat.chat_id || chat.id"
                class="chat-item"
                :class="{ active: selectedChatId === (chat.chat_id || chat.id), has_error: chat.has_error }"
                @click="selectChat(chat)"
              >
                <!-- 会话项内容 -->
                <div class="chat-item-content">
                  <div class="chat-icon">
                    <el-icon><ChatDotRound /></el-icon>
                  </div>
                  <div class="chat-details">
                    <div class="chat-title">{{ chat.title || '未命名会话' }}</div>
                    <div class="chat-info">
                      <span class="chat-date">{{ formatDate(chat.last_updated) }}</span>
                      <span class="chat-model">{{ chat.model_id||'' }}</span>
                    </div>
                  </div>
                </div>
                
                <!-- 会话操作 -->
                <div class="chat-actions" @click.stop>
                  <el-dropdown trigger="click" @command="handleCommand($event, chat)">
                    <el-button link>
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="rename">重命名</el-dropdown-item>
                        <el-dropdown-item command="delete" divided>
                          <span style="color: var(--el-color-danger)">删除</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 聊天界面 -->
          <chat-panel
            v-else
            :chat-id="selectedChatId"
            :book-id="bookId"
            :selected-text="selectedText"
            :editor="editor"
            @insert-text="$emit('insert-text', $event)"
            @chat-updated="handleChatUpdated"
            @back="selectedChatId = null"
            class="chat-panel-container"
          />
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ChatLineRound, 
  ChatDotRound, 
  Search, 
  Close, 
  Plus, 
  MoreFilled,
  ArrowLeft
} from '@element-plus/icons-vue'
import ChatPanel from './ChatPanel.vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { nanoid } from 'nanoid'
import { useConfigStore } from '@/stores/config'

// 获取配置存储
const configStore = useConfigStore()

// 添加 models 计算属性
const models = computed(() => configStore.models)

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  bookId: {
    type: String,
    required: true
  },
  selectedText: {
    type: String,
    default: ''
  },
  editor: {
    type: Object,
    default: null
  },
  initialChatId: {
    type: String,
    default: null
  },
  initialDetailView: {  // 新增：是否应该直接显示聊天详情
    type: Boolean,
    default: false
  }
})

// 组件事件
const emit = defineEmits(['close', 'insert-text', 'chat-loaded', 'select-chat', 'chat-close'])

// 响应式状态
const searchQuery = ref('')
const chats = ref([])
const loading = ref(false)
const selectedChatId = ref(null)

// 新增：为当前图书保存一个上次访问的会话ID
const getLastChatKey = computed(() => `last_chat_${props.bookId}`)

// 计算属性: 当前选中的聊天标题
const getCurrentChatTitle = computed(() => {
  if (!selectedChatId.value) return ''
  const chat = chats.value.find(c => c.id === selectedChatId.value)
  return chat ? (chat.title || '未命名会话') : '聊天'
})

// 计算属性: 过滤后的会话列表
const filteredChats = computed(() => {
  if (!searchQuery.value) return chats.value
  
  const query = searchQuery.value.toLowerCase()
  return chats.value.filter(chat => 
    (chat.title || '未命名会话').toLowerCase().includes(query)
  )
})

// 加载所有聊天数据
const loadChats = async () => {
  try {
    loading.value = true
    const response = await window.pywebview.api.model_controller.get_all_chats()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result && result.status === 'success' && Array.isArray(result.data)) {
      chats.value = result.data.sort((a, b) => {
        return (b.last_updated || 0) - (a.last_updated || 0)
      })
      
      // 只有在没有初始聊天ID时，才尝试从localStorage恢复
      if (!props.initialDetailView && !props.initialChatId) {
        const lastChatId = localStorage.getItem(getLastChatKey.value)
        if (lastChatId && chats.value.find(chat => (chat.chat_id || chat.id) === lastChatId)) {
          selectedChatId.value = lastChatId
        }
      }
      
      emit('chat-loaded', selectedChatId.value)
    } else {
      throw new Error(result?.message || '加载聊天列表失败')
    }
  } catch (error) {
    console.error('加载聊天列表失败:', error)
    ElMessage.error('加载聊天列表失败')
  } finally {
    loading.value = false
  }
}

// 侧边栏宽度状态 - 修改代码
const sidebarWidth = ref(400) // 默认宽度
const minWidth = 300 // 最小宽度
const maxWidth = 800 // 最大宽度
const isDragging = ref(false)
const sidebarElement = ref(null)
const startX = ref(0)
const startWidth = ref(0)

// 在组件挂载时设置初始宽度 - 添加这段代码
onMounted(() => {
  // 设置初始宽度CSS变量
  document.documentElement.style.setProperty('--chat-sidebar-width', `${sidebarWidth.value}px`)
  
  // 其他onMounted代码保持不变...
})

// 修改选择聊天方法，确保不会改变侧边栏宽度
const selectChat = (chat) => {
  const chatId = chat.chat_id || chat.id
  selectedChatId.value = chatId
  // 保存当前宽度到localStorage，确保一致性
  localStorage.setItem('chat-sidebar-width', document.documentElement.style.getPropertyValue('--chat-sidebar-width') || '400px')
  emit('select-chat', chat)
}

// 创建新会话 - 参考chat.vue中的实现，添加递增标题逻辑
const createNewChat = async () => {
  try {
    loading.value = true
    
    // 生成一个唯一的聊天ID
    const timestamp = Date.now()
    const chatId = `chat_${timestamp}`
    
    // 获取当前选中的模型
    const model = configStore.selectedModel || 
                 (configStore.models?.length > 0 ? configStore.models[0].id || configStore.models[0] : 'chatglm3-6b')
    
    // 生成递增的对话标题
    let maxIndex = 0
    const titleRegex = /^新对话(\d+)$/
    
    // 遍历现有对话查找最大索引
    chats.value.forEach(chat => {
      const title = chat.title || ''
      const match = title.match(titleRegex)
      if (match && match[1]) {
        const index = parseInt(match[1])
        maxIndex = Math.max(maxIndex, index)
      }
    })
    
    // 创建新会话（使用递增标题）- 修复model_id问题
    const newChat = {
      id: chatId,
      chat_id: chatId,
      title: `新对话${maxIndex + 1}`,
      model: model,
      model_id: model,  // 添加model_id确保后端正确保存模型信息
      messages: [],
      timestamp: timestamp,
      last_updated: Date.now() / 1000,  // 使用秒级时间戳，与Python后端一致
      updatedAt: new Date().toISOString()
    }
    
    // 保存到后端
    const response = await window.pywebview.api.model_controller.save_chat(chatId, newChat)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result && result.status === 'success') {
      // 将新聊天添加到列表开头
      chats.value.unshift(newChat)
      
      // 选择新创建的聊天
      selectedChatId.value = chatId
      
      // 保存最后访问的聊天ID
      localStorage.setItem(getLastChatKey.value, chatId)
      
      // 通知父组件
      emit('select-chat', newChat)
    } else {
      throw new Error(result?.message || '创建对话失败')
    }
  } catch (error) {
    console.error('创建对话失败:', error)
    ElMessage.error('创建对话失败')
  } finally {
    loading.value = false
  }
}

// 处理会话更新
const handleChatUpdated = (chatData) => {
  const chatId = chatData.chat_id || chatData.id
  const index = chats.value.findIndex(c => (c.chat_id || c.id) === chatId)
  if (index !== -1) {
    chats.value[index] = { ...chats.value[index], ...chatData }
  }
}

// 处理会话操作
const handleCommand = async (command, chat) => {
  const chatId = chat.chat_id || chat.id
  
  if (command === 'rename') {
    try {
      const { value } = await ElMessageBox.prompt(
        '请输入新名称',
        '重命名会话',
        {
          inputValue: chat.title || '未命名会话',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      )
      
      if (value && value.trim()) {
        const chatIndex = chats.value.findIndex(c => (c.chat_id || c.id) === chatId)
        if (chatIndex !== -1) {
          chats.value[chatIndex].title = value.trim()
          chats.value[chatIndex].last_updated = Date.now() / 1000
          // 保存更新
          const updatedChat = {...chats.value[chatIndex]}
          await window.pywebview.api.model_controller.save_chat(chatId, updatedChat)
          ElMessage.success('重命名成功')
        }
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('重命名失败:', error)
        ElMessage.error('重命名失败')
      }
    }
  } else if (command === 'delete') {
    try {
      await ElMessageBox.confirm(
        '确定要删除这个会话吗？删除后将无法恢复。',
        '删除会话',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // 调用后端删除接口
      const response = await window.pywebview.api.model_controller.delete_chat(chatId)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      
      if (result && result.status === 'success') {
        // 从前端列表中移除
        const chatIndex = chats.value.findIndex(c => (c.chat_id || c.id) === chatId)
        if (chatIndex !== -1) {
          chats.value.splice(chatIndex, 1)
        }
        
        // 如果删除的是当前选中的聊天，清除选择
        if (selectedChatId.value === chatId) {
          selectedChatId.value = null
        }
        
        ElMessage.success('删除成功')
      } else {
        throw new Error(result?.message || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error(typeof error === 'string' ? error : '删除失败')
      }
    }
  }
}

// 格式化日期 - 修复Python和JavaScript时间戳不兼容问题
const formatDate = (timestamp) => {
  try {
    if (!timestamp) return '未知时间'
    
    // 处理Python秒级时间戳转换为JavaScript毫秒级时间戳
    const milliseconds = typeof timestamp === 'number' ? 
      // 简单判断：如果时间戳小于10^13，认为是秒级时间戳，需要转换
      (timestamp < 10000000000 ? timestamp * 1000 : timestamp) : 
      timestamp
    
    const date = new Date(milliseconds)
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '未知时间'
    }
    
    return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
  } catch (e) {
    console.error('时间格式化错误:', e)
    return '未知时间'
  }
}

// 获取模型名称
const getModelLabel = (modelId) => {
  const modelMap = {
    'gpt-3.5-turbo': 'GPT-3.5',
    'gpt-4': 'GPT-4o',
    'gemini-1.5-flash': 'Gemini'
  }
  return modelMap[modelId] || modelId
}

// 监听可见性变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 加载会话列表
    loadChats().then(() => {
      // 如果应该直接显示聊天详情且存在初始聊天ID
      if (props.initialDetailView && props.initialChatId) {
        const chatExists = chats.value.some(
          chat => (chat.chat_id || chat.id) === props.initialChatId
        )
        if (chatExists) {
          selectedChatId.value = props.initialChatId
        }
      }
    })
  }
})

// 修改 closeCurrentChat 函数
const closeCurrentChat = () => {
  selectedChatId.value = null
  emit('chat-close')  // 通知父组件聊天已关闭
}

// 开始拖动
const startResize = (e) => {
  e.preventDefault()
  
  // 获取初始位置和宽度
  startX.value = e.clientX || (e.touches && e.touches[0].clientX) || 0
  startWidth.value = parseInt(getComputedStyle(sidebarElement.value).width, 10)
  
  // 设置拖动状态
  isDragging.value = true
  document.body.classList.add('chat-sidebar-resizing')
  
  // 添加事件监听
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.addEventListener('touchmove', handleResize)
  document.addEventListener('touchend', stopResize)
}

// 处理拖动
const handleResize = (e) => {
  if (!isDragging.value) return
  
  // 计算新宽度 (注意这里是从右向左拖动，所以是减法)
  const clientX = e.clientX || (e.touches && e.touches[0].clientX) || 0
  const deltaX = startX.value - clientX
  let newWidth = startWidth.value + deltaX
  
  // 限制最小和最大宽度
  newWidth = Math.max(300, Math.min(newWidth, window.innerWidth * 0.6))
  
  // 更新CSS变量
  document.documentElement.style.setProperty('--chat-sidebar-width', `${newWidth}px`)
}

// 停止拖动
const stopResize = () => {
  isDragging.value = false
  document.body.classList.remove('chat-sidebar-resizing')
  
  // 移除事件监听
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.removeEventListener('touchmove', handleResize)
  document.removeEventListener('touchend', stopResize)
}

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.removeEventListener('touchmove', handleResize)
  document.removeEventListener('touchend', stopResize)
  document.body.classList.remove('chat-sidebar-resizing')
})
</script>

<style lang="scss" scoped>
/* 美化聊天管理界面 */
.chat-sidebar-container {
  position: absolute;
  top: 0;
  right: 0;
  height: calc(100% - 28px); /* 减去底部状态栏的高度 */
  z-index: 100;
  display: flex;
  flex-direction: column;
  will-change: width, transform;
  width: var(--chat-sidebar-width, 400px); /* 添加默认宽度变量 */
  max-width: 100%; /* 确保不超出屏幕宽度 */
  user-select: none; /* 添加禁止选择文本 */
  
  &.collapsed {
    transform: translateX(100%);
  }
  
  /* 调整拖动把手的位置 */
  .resize-handle {
    position: absolute;
    left: -5px;
    top: 0;
    width: 10px;
    height: 100%;
    cursor: col-resize;
    z-index: 10;
    background: transparent;
    touch-action: none;
  }
}

/* 确保聊天内容不会超出容器 */
.chat-sidebar-content {
  height: 100%;
  overflow: hidden;
}

.chat-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color);
  border-left: 1px solid var(--el-border-color-light);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chat-slide-enter-active,
.chat-slide-leave-active {
  transition: transform 0.3s ease;
}

.chat-slide-enter-from,
.chat-slide-leave-to {
  transform: translateX(100%);
}

/* 聊天头部美化 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 18px;
  border-bottom: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);
  background: rgba(var(--el-bg-color-rgb), 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  user-select: none; /* 添加禁止选择文本 */
}

.chat-header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
  color: var(--el-text-color-primary);
  
  .el-icon {
    background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-3));
    color: white;
    border-radius: 8px;
    padding: 6px;
    margin-right: 10px;
    box-shadow: 0 2px 6px rgba(var(--el-color-primary-rgb), 0.3);
  }
}

.back-button {
  padding: 4px;
  margin-right: 4px;
  transform: translateX(-4px);
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateX(-6px);
  }
  
  .el-icon {
    background: none;
    box-shadow: none;
    color: var(--el-color-primary);
  }
}

.chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

/* 搜索区域美化 */
.chat-search {
  padding: 16px;
  background: rgba(var(--el-bg-color-rgb), 0.5);
  border-bottom: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);
  display: flex;
  gap: 12px;
  user-select: none; /* 添加禁止选择文本 */
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 14px;
  background: transparent;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(var(--el-text-color-secondary-rgb), 0.2);
    border-radius: 6px;
    transition: background-color 0.3s ease;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--el-text-color-secondary-rgb), 0.4);
  }
  
  .chat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 14px;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.25s ease;
    cursor: pointer;
    background-color: rgba(var(--el-fill-color-light-rgb), 0.4);
    border: 1px solid transparent;
    position: relative;
    
    &:hover {
      background-color: rgba(var(--el-fill-color-light-rgb), 0.8);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    &.active {
      background-color: rgba(var(--el-color-primary-rgb), 0.1);
      border: 1px solid rgba(var(--el-color-primary-rgb), 0.2);
      
      .chat-title {
        color: var(--el-color-primary);
        font-weight: 600;
      }
      
      .chat-icon {
        background: var(--el-color-primary);
        color: white;
      }
    }
    
    &.has-error::after {
      content: "⚠️";
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 14px;
      color: var(--el-color-danger);
    }
    
    .chat-item-content {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0; /* 防止内容溢出 */
    }
    
    .chat-icon {
      width: 36px;
      height: 36px;
      min-width: 36px;
      border-radius: 6px;
      background: rgba(var(--el-text-color-secondary-rgb), 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      transition: all 0.2s ease;
      
      .el-icon {
        font-size: 16px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .chat-details {
      flex: 1;
      min-width: 0; /* 防止文本溢出 */
    }
    
    .chat-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--el-text-color-primary);
      user-select: none; /* 修改为禁止选择文本 */
    }
    
    .chat-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      user-select: none; /* 修改为禁止选择文本 */
      
      .chat-date {
        color: var(--el-text-color-secondary);
        white-space: nowrap;
      }
      
      .chat-model {
        background: rgba(var(--el-color-info-rgb), 0.1);
        color: var(--el-text-color-secondary);
        padding: 1px 6px;
        border-radius: 10px;
        font-size: 11px;
        white-space: nowrap;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .chat-actions {
      opacity: 0.7;
      transition: opacity 0.2s ease;
      user-select: none; /* 添加禁止选择文本 */
      
      &:hover {
        opacity: 1;
      }
      
      .el-dropdown {
        margin-left: 4px;
      }
    }
  }
}

/* 空状态美化 */
.empty-chats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: var(--el-text-color-secondary);
  margin-top: 30px;
  user-select: none; /* 添加禁止选择文本 */
  
  .el-empty {
    padding: 20px;
    
    :deep(.el-empty__image) {
      opacity: 0.8;
    }
    
    :deep(.el-empty__description) {
      margin-top: 15px;
      font-size: 14px;
    }
  }
  
  .el-button {
    margin-top: 15px;
    padding: 10px 20px;
    border-radius: 24px;
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(var(--el-color-primary-rgb), 0.2);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(var(--el-color-primary-rgb), 0.3);
    }
  }
}

/* 暗色模式适配 */
html.dark {
  .chat-sidebar-container {
    box-shadow: -3px 0 20px rgba(0, 0, 0, 0.3);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .chat-header {
    background: rgba(var(--el-bg-color-rgb), 0.7);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .chat-search {
    background: rgba(var(--el-bg-color-rgb), 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    
    .el-input {
      &:deep(.el-input__wrapper) {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  .chat-item {
    background-color: rgba(var(--el-fill-color-dark-rgb), 0.3);
    
    &:hover {
      background-color: rgba(var(--el-fill-color-dark-rgb), 0.6);
    }
    
    &.active {
      background-color: rgba(var(--el-color-primary-rgb), 0.15);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
    }
    
    .chat-icon {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

/* 动画效果 */
.chat-slide-enter-active {
  transition: transform 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

.chat-slide-leave-active {
  transition: transform 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

/* 添加这个新样式 - 确保聊天面板不会改变父容器尺寸 */
.chat-panel-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style> 