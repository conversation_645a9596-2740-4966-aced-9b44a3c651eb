<template>
  <div class="login-container" :class="{ 'dark': isDarkTheme }">
    <app-header></app-header>
    
    <div class="login-content">
      <div class="login-box">
        <div class="login-header">
          <img src="@/assets/logo.png" alt="Logo" class="logo">
          <h2>Nukita PVV</h2>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="rules"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="machine_code">
            <div class="machine-code-input-group">
              <el-input
                v-model="loginForm.machine_code"
                placeholder="机器码"
                prefix-icon="UserFilled"
                :disabled="isLoading"
                readonly
              />
              <el-button 
                class="get-machine-code-btn" 
                type="primary"
                plain
                @click="fetchMachineCode" 
                :disabled="isLoading"
              >
                获取机器码
              </el-button>
            </div>
          </el-form-item>

          <el-form-item prop="activation_code">
            <el-input
              v-model="loginForm.activation_code"
              placeholder="输入激活码"
              prefix-icon="Key"
              :disabled="isLoading"
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <div class="login-options">
            <el-checkbox 
              v-model="rememberMe" 
              @change="handleRememberMeChange">
              记住激活码并自动登录
            </el-checkbox>
            <el-link type="primary" :underline="false" @click="showContactInfo">获取激活码</el-link>
          </div>

          <el-form-item>
            <el-button
              type="primary"
              :loading="isLoading"
              @click="handleLogin"
              style="width: 100%"
            >
              登录
            </el-button>
          </el-form-item>

          <div v-if="error" class="login-error">
            <el-icon><Warning /></el-icon>
            {{ error }}
          </div>
          
          <!-- 修改网络时间提示 -->
          <div class="network-time-notice" @click="getNetworkTimeInfo">
            <el-icon :class="{ 'error-icon': timeError }"><InfoFilled /></el-icon>
            <span>本软件需要网络连接才能正常登录</span>
          </div>
        </el-form>
      </div>
    </div>
    
    <el-dialog
      v-model="contactDialogVisible"
      title="获取激活码"
      width="400px"
    >
      <div class="contact-info">
        <p>请联系管理员获取激活码：</p>
        <p><strong>邮箱：</strong><EMAIL></p>

        <p class="tip">请提供您的机器码，以便生成对应的激活码</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="contactDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyContactInfo">
            复制联系信息
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 修改网络时间状态对话框为网络连接状态对话框 -->
    <el-dialog
      v-model="timeDialogVisible"
      title="网络连接状态"
      width="500px"
    >
      <div v-if="timeInfo">
        <el-descriptions border :column="1">
          <el-descriptions-item label="连接状态">
            <el-tag :type="Math.abs(timeInfo.time_diff) < 60 ? 'success' : 'warning'">
              {{ Math.abs(timeInfo.time_diff) < 60 ? '网络连接正常' : '网络连接异常' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="连接质量">
            {{ Math.abs(timeInfo.time_diff) < 10 ? '优' : 
               Math.abs(timeInfo.time_diff) < 30 ? '良' : 
               Math.abs(timeInfo.time_diff) < 60 ? '一般' : '较差' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后检测时间">
            {{ new Date().toLocaleString() }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="time-notice" v-if="Math.abs(timeInfo.time_diff) > 60">
          <el-alert
            title="网络连接异常，可能影响登录"
            type="warning"
            :closable="false"
            show-icon
          >
            <div>网络连接异常可能导致登录问题，请检查您的网络设置或稍后再试。</div>
          </el-alert>
        </div>
      </div>
      <div v-else class="loading-time">
        <el-skeleton :rows="4" animated />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="timeDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="refreshTimeInfo" :loading="timeLoading">
            重新检测网络
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <div class="background-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useConfigStore } from '@/stores/config'
import { Key, Lock, CopyDocument, Warning, InfoFilled } from '@element-plus/icons-vue'
import AppHeader from '@/components/layout/AppHeader.vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const configStore = useConfigStore()

const loginFormRef = ref(null)
const isLoading = ref(false)
const error = computed(() => userStore.error)
const rememberMe = ref(false)
const contactDialogVisible = ref(false)
const copyLoading = ref(false)
const timeDialogVisible = ref(false)
const timeInfo = ref(null)
const timeLoading = ref(false)
const timeError = ref(false)
const timeStatusText = ref('正在检查网络时间...')
// 添加登录状态标志，防止重复登录
const isLoginInProgress = ref(false)

const isDarkTheme = computed(() => configStore.theme === 'dark')

const loginForm = ref({
  machine_code: '',
  activation_code: ''
})

const rules = {
  machine_code: [{ required: true, message: '请输入机器码', trigger: 'blur' }],
  activation_code: [{ required: true, message: '请输入激活码', trigger: 'blur' }]
}

const handleLogin = async () => {
  // 防止重复登录，包括检查自动登录状态
  if (isLoginInProgress.value || isLoading.value) {
    console.log('手动登录已在进行中，跳过重复登录')
    return
  }

  // 如果自动登录正在进行，等待它完成
  if (userStore.isAutoLoginInProgress) {
    console.log('自动登录正在进行中，等待完成...')
    ElMessage.info('正在自动登录，请稍候...')

    // 等待自动登录完成，最多等待10秒
    let waitTime = 0
    const maxWaitTime = 10000 // 10秒
    const checkInterval = 100 // 100毫秒检查一次

    while (userStore.isAutoLoginInProgress && waitTime < maxWaitTime) {
      await new Promise(resolve => setTimeout(resolve, checkInterval))
      waitTime += checkInterval
    }

    // 如果自动登录完成且用户已登录，直接返回
    if (userStore.isLoggedIn) {
      console.log('自动登录已完成，用户已登录')
      return
    }

    // 如果自动登录超时或失败，继续手动登录
    if (waitTime >= maxWaitTime) {
      console.log('自动登录超时，继续手动登录')
    }
  }

  isLoginInProgress.value = true
  isLoading.value = true

  try {
    // 检查网络连接
    if (!navigator.onLine) {
      ElMessage.error('网络连接不可用，请检查您的网络设置后重试')
      isLoading.value = false
      isLoginInProgress.value = false
      return
    }
    
    // 检查网络时间状态
    await checkNetworkTimeStatus()
    if (timeError.value) {
      ElMessage.warning('网络时间同步异常，可能导致激活码验证失败，请确保网络正常')
    }

    // 强制获取最新机器码
    const machineCode = await userStore.getMachineCode(true)

    if (!machineCode) {
      ElMessage.error('无法获取机器码，请检查网络连接')
      isLoading.value = false
      isLoginInProgress.value = false
      return
    }

    if (!loginForm.value.activation_code) {
      ElMessage.warning('请输入激活码')
      isLoading.value = false
      isLoginInProgress.value = false
      return
    }
    
    // 继续登录流程
    const success = await userStore.login({
      activation_code: loginForm.value.activation_code,
    }, rememberMe.value)
    
    if (success) {
      // 登录成功逻辑...
      ElMessage.success('登录成功')
      
      // 保存用户设置
      userStore.saveUserSettings({
        activationCode: rememberMe.value ? loginForm.value.activation_code : '',
        autoLogin: rememberMe.value
      })
      userStore.startActivationTimer()
      // 跳转到主页
      router.push('/')
    } else {
      console.error('登录失败:', userStore.error)
      // 如果有错误信息，显示错误消息
      if (userStore.error) {
        ElMessage.error(userStore.error)
      }
    }
  } catch (error) {
    console.error('登录错误:', error)
    // 处理各种错误情况
    if (error.message && error.message.includes('NetworkError')) {
      ElMessage.error('网络连接错误，请检查您的网络连接')
    } else if (error.message && error.message.includes('Timeout')) {
      ElMessage.error('请求超时，服务器可能暂时无法访问')
    } else {
      ElMessage.error(error.message || '登录过程中发生错误')
    }
  } finally {
    isLoading.value = false
    isLoginInProgress.value = false
  }
}

const handleRememberMeChange = async (val) => {
  if (val && loginForm.value.activation_code) {
    await userStore.saveUserSettings({ 
      autoLogin: val,
      activationCode: loginForm.value.activation_code
    })
  } else if (!val) {
    await userStore.saveUserSettings({ 
      autoLogin: false,
      activationCode: ''
    })
  }
}

const fetchMachineCode = async () => {
  try {
    const machineCode = await userStore.getMachineCode()
    if (machineCode) {
      loginForm.value.machine_code = machineCode
    }
  } catch (e) {
    ElMessage.error('获取机器码失败')
  }
}

const showContactInfo = () => {
  contactDialogVisible.value = true
}

const loadSavedActivationCode = async () => {
  try {
    const settings = await userStore.loadUserSettings()
    if (settings.activationCode) {
      loginForm.value.activation_code = settings.activationCode
      rememberMe.value = settings.autoLogin || false
    }
  } catch (e) {
    console.error('加载保存的激活码失败:', e)
  }
}

// 获取网络状态信息并打开对话框
const getNetworkTimeInfo = async () => {
  timeDialogVisible.value = true
  await refreshTimeInfo()
}

// 刷新网络状态信息
const refreshTimeInfo = async () => {
  timeLoading.value = true
  timeInfo.value = null
  
  try {
    // 检查网络连接状态
    const result = await userStore.getNetworkTimeInfo()
    
    // 确保处理的是对象
    const response = typeof result === 'string' ? JSON.parse(result) : result
    
    if (response.status === 'success') {
      timeInfo.value = response.data
      timeError.value = false
      timeStatusText.value = '网络连接正常'
    } else {
      const errorMsg = response.message || '网络连接异常'
      ElMessage.warning(errorMsg)
      timeError.value = true
      timeStatusText.value = '网络连接异常'
      console.warn('网络连接检查失败:', errorMsg)
    }
  } catch (e) {
    console.error('检测网络状态失败:', e)
    ElMessage.error('无法检测网络状态')
    timeError.value = true
    timeStatusText.value = '网络连接异常'
  } finally {
    timeLoading.value = false
  }
}

onMounted(async () => {
  try {
    // 首先获取机器码
    await fetchMachineCode()
    
    // 检查网络连接状态
    if (!navigator.onLine) {
      console.warn('网络连接不可用，自动登录可能失败')
      timeError.value = true
      timeStatusText.value = '网络连接不可用'
    }
    
    // 加载保存的设置
    await loadSavedActivationCode()
    
    try {
      // 检查网络时间状态
      await checkNetworkTimeStatus()
      
      if (timeError.value) {
        console.warn('网络时间同步异常，可能影响激活码验证')
      } else {
        console.log('网络时间同步正常')
      }
    } catch (e) {
      console.warn('网络时间检查失败:', e)
      timeError.value = true
      timeStatusText.value = '网络时间检查失败，请确保网络连接正常'
    }
    
    // 检查是否已经登录，避免重复自动登录
    if (!userStore.isLoggedIn && rememberMe.value && loginForm.value.activation_code) {
      console.log('检测到已保存的激活码，等待App.vue自动登录完成...')
      // 等待App.vue的自动登录完成，然后检查是否需要在登录页面进行自动登录
      setTimeout(async () => {
        // 三重检查：确保用户未登录、没有手动登录正在进行、没有自动登录正在进行
        if (!userStore.isLoggedIn && !isLoginInProgress.value && !userStore.isAutoLoginInProgress) {
          console.log('App.vue自动登录未成功，启动登录页面的自动登录')
          await handleLogin()
        } else {
          console.log('跳过登录页面自动登录：用户已登录或有其他登录正在进行中')
        }
      }, 2000) // 给App.vue的自动登录更多时间
    }
  } catch (e) {
    console.error('初始化失败:', e)
    ElMessage.warning('应用初始化失败，请检查网络连接')
  }
})

watch(() => loginForm.value.activation_code, async (newValue) => {
  if (rememberMe.value && newValue) {
    await userStore.saveUserSettings({ activationCode: newValue })
  }
})


const copyContactInfo = () => {
  const contactText = `
机器码：${loginForm.value.machine_code}

联系邮箱：<EMAIL>

  `.trim()
  
  window.pywebview.api.copy_to_clipboard(contactText)
    .then(() => {
      ElMessage.success({
        message: '联系信息已成功复制到剪贴板',
        duration: 2000
      })
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败，请手动复制')
    })
}

// 检查网络时间状态方法
const checkNetworkTimeStatus = async () => {
  try {
    // 使用store方法检查网络时间状态
    const status = await userStore.checkNetworkTimeStatus()
    timeError.value = status.hasError
    timeStatusText.value = status.message
  } catch (e) {
    timeError.value = true
    timeStatusText.value = '网络时间检查失败，请确保网络连接正常'
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--el-bg-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  position: relative;
  margin-top: 60px;
}

.background-shapes {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(50px);
  opacity: 0.5;
  animation: float 10s infinite ease-in-out;
}

.shape-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, var(--el-color-primary-light-5), var(--el-color-success-light-5));
  top: -100px;
  left: -100px;
}

.shape-2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(45deg, var(--el-color-warning-light-5), var(--el-color-danger-light-5));
  bottom: -150px;
  right: -150px;
  animation-delay: -3s;
}

.shape-3 {
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, var(--el-color-info-light-5), var(--el-color-primary-light-5));
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -6s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

.login-box {
  width: 100%;
  max-width: 400px;
  padding: 2.5rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .login-box {
  background: rgba(30, 30, 30, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.05);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.login-header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.login-form {
  margin-top: 2rem;
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-radius: 8px;
  height: 44px;
}

.dark :deep(.el-input__wrapper) {
  background-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-input__wrapper:hover),
:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.dark :deep(.el-input__wrapper:hover),
.dark :deep(.el-input__wrapper.is-focus) {
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.el-input__inner) {
  height: 44px;
  font-size: 0.9375rem;
}

:deep(.el-input__prefix) {
  font-size: 1.25rem;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0;
}

.remember-text {
  color: var(--el-text-color-regular);
  font-size: 0.875rem;
}

.forgot-link {
  font-size: 0.875rem;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.forgot-link:hover {
  opacity: 1;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 1rem;
  border-radius: 8px;
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-3));
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
}

.login-button:hover::before {
  left: 100%;
}

.login-error {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
  font-size: 0.875rem;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.login-error .el-icon {
  font-size: 1rem;
}

/* 添加网络时间提示样式 */
.network-time-notice {
  margin-top: 1.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info-dark-2);
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.network-time-notice:hover {
  background-color: var(--el-color-info-light-8);
}

.dark .network-time-notice {
  background-color: rgba(var(--el-color-info-rgb), 0.2);
  color: var(--el-color-info);
}

.time-notice {
  margin-top: 16px;
}

.loading-time {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.machine-code-input-group {
  display: flex;
  gap: 0;
}

.machine-code-input-group .el-input {
  flex: 1;
}

.get-machine-code-btn {
  margin-left: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .login-content {
    padding: 1rem;
  }

  .login-box {
    padding: 2rem;
  }

  .shape {
    opacity: 0.3;
  }
}

/* 添加错误状态样式 */
.error-icon {
  color: var(--el-color-danger);
}

.network-time-notice.error {
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}
</style>
