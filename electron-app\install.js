const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

async function install() {
  console.log('🚀 开始安装 PVV Electron 应用...\n');
  
  try {
    // 1. 检查 Node.js 版本
    console.log('📋 检查环境...');
    const nodeVersion = process.version;
    console.log(`Node.js 版本: ${nodeVersion}`);
    
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 16) {
      throw new Error('需要 Node.js 16 或更高版本');
    }
    
    // 2. 检查 pnpm
    try {
      await runCommand('pnpm', ['--version']);
      console.log('✅ pnpm 已安装');
    } catch (error) {
      console.log('📦 安装 pnpm...');
      await runCommand('npm', ['install', '-g', 'pnpm']);
    }
    
    // 3. 安装主进程依赖
    console.log('\n📦 安装主进程依赖...');
    await runCommand('pnpm', ['install'], { cwd: __dirname });
    
    // 4. 创建基础前端结构
    console.log('\n📁 创建基础前端结构...');
    await createBasicFrontend();
    
    console.log('\n✅ 安装完成！');
    console.log('\n🎯 使用以下命令启动应用:');
    console.log('   pnpm run dev     # 开发模式');
    console.log('   pnpm run build   # 构建应用');
    
  } catch (error) {
    console.error('\n❌ 安装失败:', error.message);
    process.exit(1);
  }
}

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`命令执行失败: ${command} ${args.join(' ')}`));
      }
    });
    
    child.on('error', reject);
  });
}

// 如果直接运行此脚本
if (require.main === module) {
  install();
}

module.exports = install;
