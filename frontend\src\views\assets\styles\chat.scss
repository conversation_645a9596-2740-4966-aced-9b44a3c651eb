
.chat-container {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  background-color: var(--el-bg-color-page);
}




/* 全局可选元素样式 */
:deep(.el-textarea__inner), 
:deep(.el-input__inner), 
.message-content, 
.code-block, 
pre, 
code {
  user-select: text !important;
}

/* 全局不可选元素样式 */
:deep(.el-button), 
:deep(.el-select),
:deep(.el-dropdown),
:deep(.el-menu),
:deep(.el-tabs__item),
:deep(.el-radio),
:deep(.el-checkbox),
:deep(.el-icon) {
  user-select: none !important;
}

.chat-sidebar {
  width: 250px;
  height: 100%;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--el-border-color-light);
  
  /* 更精致的渐变背景 */
  background: linear-gradient(135deg, 
    var(--el-bg-color) 0%, 
    var(--el-bg-color-page) 50%, 
    var(--el-bg-color) 100%
  );
  
  /* 微妙的纹理效果 */
  background-image: 
    linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-page) 50%, var(--el-bg-color) 100%),
    radial-gradient(at 20% 25%, rgba(255, 255, 255, 0.4) 0, transparent 60%),
    radial-gradient(at 80% 80%, rgba(0, 0, 0, 0.02) 0, transparent 50%);
    
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 
    4px 0 16px rgba(0, 0, 0, 0.03),
    1px 0 3px rgba(0, 0, 0, 0.02);
  position: relative;
  z-index: 3;
  border-radius: 0 16px 16px 0;
}

.chat-sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  gap: 8px;
  flex-shrink: 0;
  border-bottom: 1px solid var(--el-border-color-light);
  
  /* 精致的顶部渐变 */
  background: linear-gradient(180deg, 
    var(--el-bg-color) 0%, 
    var(--el-bg-color-page) 100%
  );
  
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  z-index: 2;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: calc(100% - 60px); /* 减去header高度 */
}

.chat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 10px;
  
  /* 更微妙的渐变背景 */
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.7) 0%, 
    rgba(255, 255, 255, 0.5) 100%
  );
  
  box-shadow: 
    1px 1px 4px rgba(0, 0, 0, 0.03),
    -1px -1px 4px rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  min-height: 24px;
  flex-shrink: 0;
  backdrop-filter: blur(5px);
}

.chat-item:hover {
  transform: translateY(-2px);
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.8) 0%, 
    rgba(255, 255, 255, 0.6) 100%
  );
  box-shadow: 
    2px 2px 8px rgba(0, 0, 0, 0.06),
    -1px -1px 4px rgba(255, 255, 255, 0.5);
}

.chat-item.active {
  background: linear-gradient(135deg, 
    var(--el-color-primary-light-8) 0%, 
    var(--el-color-primary-light-9) 100%
  );
  border: 1px solid var(--el-color-primary-light-5);
  box-shadow: 
    1px 1px 5px rgba(0, 0, 0, 0.05),
    -1px -1px 4px rgba(255, 255, 255, 0.6);
}

.chat-item:hover .chat-actions {
  opacity: 1; /* 悬停时显示 */
}

.delete-icon {
  color: var(--el-color-danger);
  padding: 4px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.delete-icon:hover {
  background-color: var(--el-color-danger-light-9);
  transform: scale(1.1);
}

.chat-sidebar.collapsed .chat-title,
.chat-sidebar.collapsed .chat-actions {
  display: none;
}

.chat-content {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(145deg, var(--el-bg-color), var(--el-bg-color-page));
  overflow: hidden;
  border-radius: 16px;
  margin: 10px 10px 10px 0;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.chat-header {
  display: flex;
  padding: 16px;
  background: linear-gradient(145deg, var(--el-bg-color), var(--el-bg-color-page));
  border-bottom: 1px solid var(--el-border-color-light);
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.selector-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  width: 100%;
}

.selector {
  flex: 1;
  min-width: 150px;
}

/* 暗色模式适配 */
html.dark {
  .chat-header {
    background: linear-gradient(145deg, #1a1a1a, #242424);
    box-shadow: 
      0 4px 6px -1px rgba(0, 0, 0, 0.2),
      0 2px 4px -1px rgba(0, 0, 0, 0.1);

    &::before {
      background: linear-gradient(
        to bottom right,
        rgba(255, 255, 255, 0.02),
        rgba(255, 255, 255, 0)
      );
    }
  }

  .selector-group {
    background: #1e1e1e;
    box-shadow: 
      inset 1px 1px 2px rgba(0, 0, 0, 0.2),
      1px 1px 1px rgba(255, 255, 255, 0.02);

    .selector {
      :deep(.el-input__wrapper) {
        background: linear-gradient(145deg, #242424, #1a1a1a);
        box-shadow: 
          2px 2px 4px rgba(0, 0, 0, 0.2),
          -1px -1px 2px rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(255, 255, 255, 0.03);
      }

      &:hover :deep(.el-input__wrapper) {
        box-shadow: 
          3px 3px 6px rgba(0, 0, 0, 0.25),
          -1px -1px 3px rgba(255, 255, 255, 0.03);
      }

      &:focus-within :deep(.el-input__wrapper) {
        box-shadow: 
          inset 2px 2px 4px rgba(0, 0, 0, 0.25),
          inset -1px -1px 2px rgba(255, 255, 255, 0.02),
          0 0 0 1px var(--el-color-primary-light-3);
      }

      :deep(.el-tag) {
        background: linear-gradient(145deg, #2a2a2a, #242424);
        border: 1px solid rgba(255, 255, 255, 0.05);
        box-shadow: 
          1px 1px 3px rgba(0, 0, 0, 0.2),
          -1px -1px 2px rgba(255, 255, 255, 0.02);
      }
    }
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  padding-bottom: 40px;
  user-select: text;
  scroll-behavior: smooth;
  position: relative;
  
  /* 拟态设计 - 亮色主题 */
  background: linear-gradient(145deg, var(--el-bg-color-page), #f8f9fc);
  background-image: 
    linear-gradient(145deg, var(--el-bg-color-page), #f8f9fc),
    radial-gradient(at 20% 30%, rgba(255, 255, 255, 0.7) 0, transparent 70%),
    radial-gradient(at 80% 70%, rgba(0, 0, 0, 0.03) 0, transparent 50%);
  
  /* 微妙纹理和阴影效果 */
  border-radius: 0 0 16px 16px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  
  /* 消息进入动画 */
  & .message {
    animation: message-fade-in 0.3s ease forwards;
  }
}

/* 聊天消息容器滚动条优化 */
.chat-messages::-webkit-scrollbar {
  width: 8px; /* 增加宽度 */
  background-color: transparent;
  transition: all 0.3s ease;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
  min-height: 50px; /* 确保滚动块有足够高度 */
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.25);
  border-width: 1px; /* 悬停时减少边框，增大滑块视觉尺寸 */
}

.chat-messages::-webkit-scrollbar-thumb:active {
  background-color: rgba(0, 0, 0, 0.35); /* 点击时更深的颜色 */
}

.chat-messages::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.03); /* 轻微的轨道背景 */
  border-radius: 8px;
  margin: 4px 0;
}

.chat-messages:hover::-webkit-scrollbar-thumb {
  /* 当鼠标悬停在消息区域时，使滚动条更明显 */
  background-color: rgba(0, 0, 0, 0.2);
}

/* 暗色模式滚动条 */
html.dark .chat-messages::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.15);
}

html.dark .chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.25);
}

html.dark .chat-messages::-webkit-scrollbar-thumb:active {
  background-color: rgba(255, 255, 255, 0.35);
}

html.dark .chat-messages::-webkit-scrollbar-track {
  background-color: rgba(255, 255, 255, 0.03);
}

html.dark .chat-messages:hover::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

@keyframes message-fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 暗色主题下的聊天消息区域样式 */
html.dark .chat-messages {
  /* 拟态设计 - 暗色主题 */
  background: linear-gradient(145deg, #1d2027, #262b36);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.4),
    inset 0 0 3px rgba(0, 0, 0, 0.3);
  
  /* 微妙纹理 */
  background-image: 
    linear-gradient(145deg, #1d2027, #262b36),
    radial-gradient(at 20% 30%, rgba(255, 255, 255, 0.05) 0, transparent 60%),
    radial-gradient(at 80% 70%, rgba(0, 0, 0, 0.2) 0, transparent 40%);
  
  /* 暗色主题下的滚动条样式 */
  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.15);
  }
}

.message {
  margin-bottom: 20px;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease;
}

.message-row {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.message.user .message-row {
  justify-content: flex-end;
}

/* 消息内容区域 */
.message-inner {
  border-radius: 12px;
  padding: 16px 20px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  position: relative;
  overflow-wrap: break-word;
  word-break: break-word;
  width: fit-content;
  max-width: 85%;
  
  /* 拟态设计增强 */
  box-shadow: 
    0 2px 6px rgba(0, 0, 0, 0.04),
    0 0 1px rgba(0, 0, 0, 0.03);
  transition: transform 0.2s ease, box-shadow 0.3s ease;
}

.message-inner:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.04);
}

/* 用户消息样式 */
.message.user .message-inner {
  margin-left: auto;
  background: linear-gradient(145deg, var(--user-message-bg), #f5f9ff);
  border-color: var(--user-message-border);
  border-radius: 16px 4px 16px 16px;
  box-shadow: 
    0 2px 6px rgba(66, 153, 225, 0.08),
    0 0 2px rgba(66, 153, 225, 0.04);
}

/* AI助手消息样式 */
.message.assistant .message-inner {
  margin-right: auto;
  background: linear-gradient(145deg, var(--assistant-message-bg), #fbfbfc);
  border-radius: 4px 16px 16px 16px;
  box-shadow: 
    0 2px 6px rgba(0, 0, 0, 0.04),
    0 0 2px rgba(0, 0, 0, 0.02);
}

/* 系统消息样式 */
.message.system .message-inner {
  background: linear-gradient(145deg, var(--el-fill-color-light), var(--el-fill-color));
  margin: 0 auto 16px;
  border: 1px solid var(--el-border-color-lighter);
  font-size: 14px;
  color: var(--el-text-color-secondary);
  max-width: 80%;
  text-align: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
}

/* 美化头像样式 */
.message-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 12px;
  font-weight: bold;
  font-size: 16px;
  flex-shrink: 0;
  user-select: none;
  
  /* 拟态风格头像 */
  background: linear-gradient(145deg, var(--el-color-primary-light-8), var(--el-color-primary-light-9));
  color: var(--el-color-primary);
  box-shadow: 
    2px 2px 6px rgba(0, 0, 0, 0.1),
    -1px -1px 4px rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.message.user .message-avatar {
  background: linear-gradient(145deg, var(--el-color-success-light-8), var(--el-color-success-light-9));
  color: var(--el-color-success);
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  user-select: none; /* 消息头部不可选 */
}

.message-role {
  font-weight: 500;
  color: var(--el-text-color-secondary);
  margin-right: auto;
  user-select: none; /* 消息角色标签不可选 */
}

.message-content {
  margin: 8px 0;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  overflow-wrap: break-word;
  word-break: break-word;
  font-size: 15px;
  user-select: text; /* 确保消息内容可选 */
}

.message-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  user-select: none; /* 操作按钮不可选 */
}

.message-inner:hover .message-actions {
  opacity: 1;
}

.copy-button {
  padding: 6px 10px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  user-select: none; /* 复制按钮不可选 */
  
  .el-icon {
    font-size: 14px;
  }
}

.message-inner:hover .message-actions .copy-button {
  opacity: 1;
  transform: translateY(0);
}

.copy-button:hover {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary-light-3);
  background-color: var(--el-color-primary-light-9);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.copy-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 系统消息特殊样式 */
.message.system .message-row {
  justify-content: center;
}

.message.system .message-avatar {
  display: none;
}

.message.system .message-actions {
  top: 8px;
  right: 8px;
}

.markdown-body {
  padding: 0;
  overflow-x: auto;
  max-width: 100%;
  
}

/* 代码高亮主题优化 */
.hljs {
  background: transparent !important;
  padding: 0 !important;
  
}

.language-javascript .hljs-keyword,
.language-python .hljs-keyword,
.language-java .hljs-keyword {
  color: var(--el-color-primary);
  font-weight: 500;
}

.language-javascript .hljs-string,
.language-python .hljs-string,
.language-java .hljs-string {
  color: var(--el-color-success);
}

.language-javascript .hljs-function,
.language-python .hljs-function,
.language-java .hljs-function {
  color: var(--el-color-warning);
  font-weight: 500;
}

.hljs-comment {
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.hljs-number {
  color: var(--el-color-danger);
}

.hljs-class {
  color: var(--el-color-info);
  font-weight: 500;
}

.message-content.markdown-body {
  user-select: text;
  
  /* 确保某些元素是不可选择的 */
  :deep(.code-header), 
  :deep(.btns-wrapper), 
  :deep(.code-btn), 
  :deep(.lang-label) {
    user-select: none !important;
  }
  
  /* 确保内容是可选择的 */
  p, h1, h2, h3, h4, h5, h6, span, em, strong, 
  blockquote, ul, ol, li, table, pre, code, .hljs {
    user-select: text !important;
  }
  
  :deep(.code-wrapper) {
    margin: 1em 0;
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--el-bg-color-overlay, #ffffff);
    border: 1px solid rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
    display: block;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .code-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      background-color: var(--el-bg-color-page);
      border-bottom: 1px solid rgba(0, 0, 0, 0.07);
      height: 32px;
      user-select: none; /* 代码头部不可选 */

      .lang-label {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        text-transform: lowercase;
        
        opacity: 0.8;
        font-weight: 600;
      }
      
      .btns-wrapper {
        display: flex;
        gap: 8px;
        align-items: center;
      }

      .code-btn {
        height: 30px;
        padding: 0 10px;
        font-size: 12px;
        border: none;
        background: transparent;
        color: var(--el-text-color-secondary);
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        border-radius: 4px;
        user-select: none; /* 代码按钮不可选 */
        
        &:hover {
          color: var(--el-color-primary);
          background-color: rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          transform: translateY(-1px);
        }
        
        &:active {
          transform: translateY(1px);
        }
        
        &.active {
          color: var(--el-color-primary);
          background-color: rgba(0, 0, 0, 0.08);
        }
        
        span {
          font-size: 14px;
          font-weight: 600;
        }
      }
    }

    .code-block-container {
      position: relative;
      transition: max-height 0.5s ease, opacity 0.3s ease;
    }

    .code-block {
      margin: 0;
      padding: 12px 16px;
      background-color: var(--el-bg-color-overlay, #ffffff);
      overflow-x: auto;
      
      font-size: var(--code-font-size, 15px);
      line-height: 1.6;
      display: block;
      user-select: text; /* 代码块内容可选 */
    }
  }

  /* 确保hljs样式生效 */
  pre code.hljs {
    display: block;
    overflow-x: auto;
    color: var(--el-text-color-primary);
    background: transparent !important;
    
    font-size: var(--code-font-size, 15px);
  }
  
  /* 处理代码块外部直接的pre标签 */
  pre {
    background-color: var(--el-bg-color-overlay, #ffffff);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 18px;
    overflow-x: auto;
    margin: 1em 0;
    
    code {
      
      font-size: 15px;
      line-height: 1.6;
      background: transparent !important;
      padding: 0 !important;
      border: none !important;
      display: block;
    }
  }
  
  /* 内联代码 */
  code:not([class*="language-"]) {
    
    padding: 0.2em 0.4em;
    margin: 0 0.2em;
    font-size: 90%;
    background-color: var(--el-fill-color-light);
    border-radius: 3px;
    color: #d73a49;
    border: 1px solid var(--el-border-color-light);
  }

  /* 其他 Markdown 元素样式优化 */
  p {
    margin: 0.8em 0;
    line-height: 1.6;
  }

  ul, ol {
    padding-left: 1.5em;
    margin: 0.8em 0;
  }

  li {
    margin: 0.4em 0;
  }

  blockquote {
    margin: 1em 0;
    padding: 0.5em 1em;
    border-left: 4px solid var(--el-color-primary-light-7);
    background-color: var(--el-color-primary-light-9);
    color: var(--el-text-color-regular);
    border-radius: 4px;
  }

  table {
    border-collapse: collapse;
    margin: 1em 0;
    width: 100%;
    font-size: 14px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  th, td {
    border: 1px solid var(--el-border-color-light);
    padding: 0.5em 1em;
  }

  th {
    background-color: var(--el-fill-color-light);
    font-weight: 500;
  }

  img {
    max-width: 100%;
    border-radius: 4px;
    margin: 1em 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  hr {
    border: none;
    border-top: 1px solid var(--el-border-color-light);
    margin: 1.5em 0;
  }
}

.copy-button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message:hover .copy-button {
  opacity: 0.8;
}

.copy-button:hover {
  opacity: 1;
}

.button-group {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  justify-content: flex-end;
  user-select: none; /* 按钮组不可选 */
}

.clear-all-btn {
  margin-left: 8px;
}

.delete-btn, .clear-btn {
  opacity: 0.6;
}

.delete-btn:hover, .clear-btn:hover {
  opacity: 1;
}

/* 输入框区域 */
.chat-input {
  border-top: 1px solid var(--el-border-color-light);
  padding: 16px;
  background: linear-gradient(145deg, 
    var(--el-bg-color-page) 0%, 
    var(--el-bg-color) 100%
  );
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 200px;
  overflow: hidden;
  transform-origin: bottom;
  opacity: 1;
  border-radius: 0 0 16px 16px;
  position: relative;
  box-shadow: 
    inset 0 2px 3px rgba(0, 0, 0, 0.02),
    0 -1px 2px rgba(255, 255, 255, 0.1);
  z-index: 2;
}

/* 输入框隐藏状态 */
.chat-input.input-hidden {
  max-height: 0;
  padding: 0 16px;
  border-top: none;
  opacity: 0;
}

/* 文本域样式优化 */
.chat-input textarea {
  border-radius: 12px;
  resize: none;
  transition: all 0.3s ease;
  border: 1px solid var(--el-border-color-light);
  
  /* 拟态风格文本框 */
  background: linear-gradient(145deg, #f8f9fc, #ffffff);
  box-shadow: 
    inset 1px 1px 3px rgba(0, 0, 0, 0.05),
    inset -1px -1px 3px rgba(255, 255, 255, 0.5),
    0 2px 6px rgba(0, 0, 0, 0.03);
  
  padding: 15px;
  font-size: 16px !important;
  line-height: 1.6;
  color: var(--el-text-color-primary);
}

.chat-input textarea:focus {
  border-color: var(--el-color-primary-light-3);
  box-shadow: 
    0 0 0 1px var(--el-color-primary-light-5),
    inset 1px 1px 3px rgba(0, 0, 0, 0.05),
    inset -1px -1px 3px rgba(255, 255, 255, 0.5),
    0 4px 12px rgba(0, 0, 0, 0.06);
  outline: none;
  transform: translateY(-2px);
}

/* 按钮组样式优化 */
.button-group {
  display: flex;
  gap: 10px;
  margin-top: 12px;
  justify-content: flex-end;
  user-select: none;
}

.button-group .el-button {
  border-radius: 10px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
  
  /* 拟态风格按钮 */
  background: linear-gradient(145deg, var(--el-color-primary), var(--el-color-primary-light-3));
  border: none;
  box-shadow: 
    2px 2px 5px rgba(0, 0, 0, 0.1),
    -1px -1px 3px rgba(255, 255, 255, 0.05);
}

.button-group .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    3px 3px 8px rgba(0, 0, 0, 0.15),
    -1px -1px 3px rgba(255, 255, 255, 0.1);
}

.button-group .el-button:active {
  transform: translateY(1px);
  box-shadow: 
    1px 1px 3px rgba(0, 0, 0, 0.1),
    -1px -1px 2px rgba(255, 255, 255, 0.05);
}

/* 发送/停止生成按钮状态样式 */
.button-group .el-button--primary {
  background: linear-gradient(145deg, var(--el-color-primary), var(--el-color-primary-light-3));
  color: white;
}

.button-group .el-button--warning {
  background: linear-gradient(145deg, var(--el-color-warning), var(--el-color-warning-light-3));
  box-shadow: 
    2px 2px 5px rgba(0, 0, 0, 0.1),
    -1px -1px 3px rgba(255, 255, 255, 0.05),
    0 0 8px rgba(230, 162, 60, 0.3);
}

/* 禁用状态 */
.button-group .el-button.is-disabled {
  background: linear-gradient(145deg, var(--el-fill-color), var(--el-fill-color-light));
  color: var(--el-text-color-placeholder);
  cursor: not-allowed;
  box-shadow: 
    1px 1px 3px rgba(0, 0, 0, 0.05),
    -1px -1px 2px rgba(255, 255, 255, 0.5);
  transform: none;
}

/* 暗色模式适配 */
html.dark .chat-input {
  background: linear-gradient(145deg, 
    #1a1c23 0%, 
    #21242c 100%
  );
  border-top: 1px solid rgba(255, 255, 255, 0.03);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.2),
    0 -1px 2px rgba(0, 0, 0, 0.2);
}

html.dark .chat-input textarea {
  background: linear-gradient(145deg, #21242c, #1a1c23);
  color: var(--el-text-color-primary);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 0 2px rgba(0, 0, 0, 0.4),
    0 1px 1px rgba(255, 255, 255, 0.02);
}

html.dark .chat-input textarea:focus {
  border-color: var(--el-color-primary-light-7);
  box-shadow: 
    0 0 0 1px var(--el-color-primary-light-7),
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 4px 8px rgba(0, 0, 0, 0.4);
}

html.dark .button-group .el-button {
  box-shadow: 
    2px 2px 6px rgba(0, 0, 0, 0.2),
    -1px -1px 2px rgba(255, 255, 255, 0.02);
}

html.dark .button-group .el-button:hover {
  box-shadow: 
    3px 3px 8px rgba(0, 0, 0, 0.3),
    -1px -1px 2px rgba(255, 255, 255, 0.03);
}

html.dark .button-group .el-button--warning {
  box-shadow: 
    2px 2px 6px rgba(0, 0, 0, 0.2),
    -1px -1px 2px rgba(255, 255, 255, 0.02),
    0 0 10px rgba(230, 162, 60, 0.15);
}

html.dark .button-group .el-button.is-disabled {
  background: linear-gradient(145deg, #2a2a2a, #333333);
  color: var(--el-text-color-disabled);
  box-shadow: 
    1px 1px 3px rgba(0, 0, 0, 0.2),
    -1px -1px 2px rgba(255, 255, 255, 0.01);
}

/* 输入框切换按钮样式 */
.input-toggle-btn {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  user-select: none;
  
  /* 基础样式 */
  background-color: var(--el-color-primary);
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  animation: bounceIn 1s;
}

.input-toggle-btn:hover {
  transform: translateX(-50%) translateY(-50%) scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* 状态变化样式 */
.input-toggle-btn.sending {
  background-color: var(--el-color-warning);
  animation: pulse 2s infinite;
}

.input-toggle-btn.receiving {
  background-color: var(--el-color-success);
  animation: breathe 2s infinite;
}

/* 动画定义 */
@keyframes breathe {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
  50% {
    box-shadow: 0 0 0 15px rgba(103, 194, 58, 0);
    transform: translateX(-50%) translateY(-50%) scale(1.15);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.7);
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
  50% {
    box-shadow: 0 0 0 15px rgba(230, 162, 60, 0);
    transform: translateX(-50%) translateY(-50%) scale(1.15);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
}

@keyframes bounceIn {
  0% { 
    opacity: 0;
    transform: translateX(-50%) translateY(-50%) scale(0.3);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-50%) scale(1.1);
  }
  70% {
    transform: translateX(-50%) translateY(-50%) scale(0.9);
  }
  100% {
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
}

/* 图标样式 */
.input-toggle-btn svg {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.input-toggle-btn .is-rotated {
  transform: rotate(180deg);
}

/* 发送状态下按钮样式 */
.input-toggle-btn.sending {
  background-color: var(--el-color-warning);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(230, 162, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
  }
}

.input-toggle-btn .el-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.input-toggle-btn .is-rotated {
  transform: rotate(180deg);
}

/* 确保在暗色模式下按钮也有足够的对比度 */
html.dark .input-toggle-btn {
  background-color: var(--el-color-primary);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

html.dark .input-toggle-btn:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

html.dark .input-toggle-btn.sending {
  background-color: var(--el-color-warning);
}

.chat-input textarea {
  border-radius: 8px;
  resize: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chat-input textarea:focus {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 滚动条美化 */
.chat-messages::-webkit-scrollbar {
  width: 18px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color);
  border-radius: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 消息打字机效果（可选） */
.typing-effect {
  overflow: hidden;
  border-right: 2px solid var(--el-color-primary);
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: var(--el-color-primary) }
}

/* 确保系统消息居中 */
.message.system {
  margin: 12px auto;
  max-width: 85%;
}

/* 暗色模式适配 */
html.dark {
  .message-content.markdown-body {
    :deep(.code-wrapper) {
      background-color: #1e1e1e;
      border-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }

      .code-header {
        background-color: #252525;
        border-color: rgba(255, 255, 255, 0.1);

        .lang-label {
          color: #aaa;
          font-size: 14px;
          font-weight: 600;
        }

        .code-btn {
          color: #aaa;
          
          &:hover {
            color: var(--el-color-primary-light-3);
          }
          
          &.active {
            color: var(--el-color-primary-light-3);
          }
        }
      }

      .code-block {
        background-color: #1e1e1e;
      }
    }
  }

  /* 定义暗色模式下的用户消息颜色 */
  --user-message-bg: var(--el-fill-color-dark);
  --user-message-border: var(--el-border-color-light);
  --assistant-message-bg: var(--el-bg-color-darker);
  --assistant-message-border: var(--el-border-color);
  
  // ... existing code ...
}

/* 代码高亮优化 */
:deep(.hljs) {
  background: transparent !important;
  color: var(--el-text-color-primary);
  display: block;
  padding: 0;
  
  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-built_in,
  .hljs-name,
  .hljs-tag {
    color: #d73a49;
    font-weight: 600;
  }

  .hljs-string,
  .hljs-title,
  .hljs-section,
  .hljs-attribute,
  .hljs-literal,
  .hljs-template-tag,
  .hljs-template-variable,
  .hljs-type,
  .hljs-addition {
    color: #22863a;
  }

  .hljs-deletion,
  .hljs-selector-attr,
  .hljs-selector-pseudo,
  .hljs-meta {
    color: #e36209;
  }

  .hljs-doctag,
  .hljs-attr {
    color: #6f42c1;
  }

  .hljs-symbol,
  .hljs-bullet,
  .hljs-link {
    color: #005cc5;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: bold;
  }

  .hljs-comment,
  .hljs-quote {
    color: #6a737d;
    font-style: italic;
  }

  .hljs-params {
    color: #24292e;
  }

  .hljs-variable,
  .hljs-template-variable {
    color: #e36209;
  }

  .hljs-number {
    color: #005cc5;
  }

  .hljs-regexp {
    color: #032f62;
  }

  .hljs-function {
    color: #6f42c1;
    .hljs-title {
      color: #6f42c1;
      font-weight: 600;
    }
  }

  .hljs-class {
    .hljs-title {
      color: #6f42c1;
      font-weight: 600;
    }
  }
}

pre {
  margin: 0;
  padding: 0;
  background-color: transparent;
}

/* 在现有样式部分之后添加以下样式 */
.message-timestamp {
  margin-left: 8px;
  font-size: 10px;
  color: #aaa;
  font-weight: normal;
}

/* 增强消息样式，添加过渡效果 */
.message {
  transition: all 0.3s ease;
}

.message.user, .message.assistant, .message.system {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* 暗色模式调整 */
html.dark {
  /* 消息时间戳 */
  .message-timestamp {
    color: #777;
  }
  
  /* 代码块样式覆盖 */
  .message-content {
    :not(pre) > code {
      background-color: #2d2d2d !important;
      color: #e0e0e0 !important;
      border-color: #444 !important;
    }
    
    pre {
      background-color: #1e1e1e !important;
      border-color: #333 !important;
    }
    
    pre code {
      background-color: #1e1e1e !important;
      color: #e0e0e0 !important;
    }
    
    /* 确保语法高亮生效 */
    .hljs {
      background-color: #1e1e1e !important;
      color: #e0e0e0 !important;
    }
    
    /* 代码块头部 */
    .code-header {
      background-color: #2d2d2d !important;
      border-color: #333 !important;
      
      .lang-label {
        color: #aaa !important;
        font-size: 14px;
        font-weight: 600;
      }
      
      .code-btn {
        color: #aaa !important;
        
        &:hover {
          color: var(--el-color-primary-light-3) !important;
          background-color: rgba(255, 255, 255, 0.1) !important;
        }
      }
    }
    
    /* 代码块容器 */
    .code-block-container {
      background-color: #1e1e1e !important;
    }
    
    /* 滚动条 */
    ::-webkit-scrollbar-track {
      background: #333 !important;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #666 !important;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #888 !important;
    }
  }
}

/* 增加全局CSS变量定义 */
:root {
  --code-bg-color: #f6f8fa;
  --code-text-color: #24292e;
  --code-border-color: #e8e8e8;
  --code-header-bg: #f6f8fa;
  --code-font-size: 15px;
  --user-message-bg: var(--el-fill-color-light);
  --user-message-border: var(--el-border-color-lighter);
  --assistant-message-bg: var(--el-bg-color);
  --assistant-message-border: var(--el-border-color);
}

/* 修改代码块样式，使用变量 */
:deep(.markdown-body) {
  .code-wrapper {
    background-color: var(--code-bg-color) !important;
    border-color: var(--code-border-color) !important;
    
    .code-header {
      background-color: var(--code-header-bg) !important;
      border-color: var(--code-border-color) !important;
    }
    
    .code-block-container,
    .code-block,
    pre,
    code {
      background-color: var(--code-bg-color) !important;
      color: var(--code-text-color) !important;
    }
  }
}

/* 移除时间戳相关样式 */
.message-timestamp {
  display: none;
}

html.dark {
  .markdown-body {
    // ... existing code ...
    
    .code-wrapper {
      // ... existing code ...
      
      .code-header {
        background-color: #2d2d2d !important;
        border-color: #333 !important;
        
        .lang-label {
          color: #aaa !important;
        }
        
        .code-btn {
          color: #aaa !important;
          
          span {
            color: #ccc !important;
          }
          
          &:hover {
            color: var(--el-color-primary-light-3) !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
          }
          
          &.active {
            color: var(--el-color-primary-light-3) !important;
            background-color: rgba(255, 255, 255, 0.15) !important;
          }
        }
      }
      
      // ... existing code ...
    }
  }
}

// ... existing code ...

/* 添加操作指引的样式 */
.input-guide {
  position: absolute;
  bottom: 60px;
  left: 0;
  right: 0;
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  padding: 10px;
  pointer-events: none; /* 确保点击能穿透到下方元素 */
  opacity: 0.7;
  animation: fadeIn 0.5s ease-in-out 1s forwards;
  user-select: none; /* 操作指引提示不可选 */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 0.7; transform: translateY(0); }
}

// ... existing code ...

.chat-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ccc;
  margin-right: 4px;
}

.chat-dot.active-dot {
  background-color: #409eff;
}

// 优化聊天列表样式
.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 4px;
  user-select: none;
}

// 折叠状态下的聊天项目样式
.chat-sidebar.collapsed .chat-item {
  justify-content: center;
  padding: 8px 0;
  min-height: 32px;
}

// 添加聊天圆点样式（折叠状态下显示）
.chat-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--el-text-color-secondary);
  transition: all 0.3s ease;
}

.chat-dot.active-dot {
  width: 14px;
  height: 14px;
  background-color: var(--el-color-primary);
  box-shadow: 0 0 6px var(--el-color-primary-light-3);
}

// 优化删除按钮样式
.delete-btn {
  border-radius: 4px;
  padding: 4px 8px;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  .el-icon {
    font-size: 18px;
  }
  
  &:hover {
    transform: scale(1.05);
    background-color: var(--el-color-danger);
  }
}

// 调整聊天操作区域样式
.chat-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chat-item:hover .chat-actions {
  opacity: 1;
}

// 不再隐藏标题和操作区，而是使用模板条件渲染
.chat-sidebar.collapsed .chat-indicator {
  position: static;
  transform: none;
  margin: 0 auto;
}

// ... 现有代码 ...

// 添加推理内容的样式
.message-reasoning-container {
  margin-bottom: 16px;
  border-radius: 12px;
  border: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color-page);
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  width: 100%; // 确保容器始终占据全宽
  
  &:hover {
    border-color: var(--el-border-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  }
}

// 新增内容包装器
.reasoning-content-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden; // 隐藏溢出内容
}

// 美化标题栏
.reasoning-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(to right, var(--el-color-info-light-9), var(--el-bg-color));
  border-bottom: 1px solid var(--el-border-color-light);
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    background: linear-gradient(to right, var(--el-color-info-light-8), var(--el-bg-color));
  }
  
  &:active {
    background: linear-gradient(to right, var(--el-color-info-light-7), var(--el-bg-color));
  }
}

// 美化图标
.reasoning-icon {
  margin-right: 10px;
  color: var(--el-color-info);
  display: flex;
  align-items: center;
  
  svg {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
}

// 美化标题文字
.reasoning-title {
  flex: 1;
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
  display: flex;
  align-items: center;
}

// 美化时间标签
.reasoning-time {
  font-size: 12px;
  color: var(--el-color-info);
  margin-left: 8px;
  font-weight: 400;
  background-color: var(--el-fill-color-light);
  padding: 2px 6px;
  border-radius: 10px;
  transition: all 0.3s ease;
  
  // 添加思考中状态的样式
  &.thinking {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    animation: pulse 1.5s infinite;
  }
}

// 美化操作按钮
.reasoning-action {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: var(--el-color-info);
  
  &:hover {
    background-color: var(--el-fill-color-light);
    color: var(--el-color-primary);
  }
}

// 美化箭头动画
.reasoning-arrow {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  
  &.is-collapsed {
    transform: rotate(180deg);
  }
}

// 美化思考内容区域
.message-reasoning {
  padding: 16px;
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
  line-height: 1.6;
  border-top: 1px solid var(--el-border-color-lighter);
  width: 100%;
  max-height: 400px;
  transition: all 0.6s ease;
  
  &.collapsed {
    max-height: 0;
    padding: 0;
    border-top: none;
    opacity: 0;
  }
  
  // 内容样式
  :deep(p) {
    margin: 0.5em 0;
  }
  
  :deep(ul), :deep(ol) {
    margin: 0.5em 0;
    padding-left: 1.5em;
  }
  
  :deep(li) {
    margin: 0.3em 0;
  }
  
  :deep(pre), :deep(code) {
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
    padding: 0.2em 0.4em;

    font-size: 0.9em;
  }
  
  :deep(pre code) {
    padding: 0;
    background-color: transparent;
  }
}

// 暗黑模式样式优化
html.dark {
  .message-reasoning-container {
    background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 
      -8px -8px 15px rgba(255, 255, 255, 0.02),
      8px 8px 15px rgba(0, 0, 0, 0.4),
      inset 1px 1px 1px rgba(255, 255, 255, 0.02);
    
    &:hover {
      box-shadow: 
        -10px -10px 20px rgba(255, 255, 255, 0.03),
        10px 10px 20px rgba(0, 0, 0, 0.5),
        inset 1px 1px 1px rgba(255, 255, 255, 0.03);
      border-color: rgba(255, 255, 255, 0.08);
    }
  }
  
  .reasoning-header {
    background: linear-gradient(145deg, #252525, #202020);
    border-bottom: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    
    &:hover {
      background: linear-gradient(145deg, #282828, #232323);
    }
    
    &:active {
      background: linear-gradient(145deg, #232323, #282828);
    }
  }
  
  .reasoning-icon {
    color: var(--el-color-primary-light-5);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    
    svg {
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }
  }
  
  .reasoning-title {
    color: var(--el-color-info-light-3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
  
  .reasoning-time {
    background: linear-gradient(145deg, #2a2a2a, #252525);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 
      inset 1px 1px 1px rgba(255, 255, 255, 0.02),
      1px 1px 3px rgba(0, 0, 0, 0.2);
    color: var(--el-color-info-light-5);
    padding: 3px 8px;
  }
  
  .reasoning-action {
    background: linear-gradient(145deg, #2d2d2d, #252525);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 
      -2px -2px 5px rgba(255, 255, 255, 0.02),
      2px 2px 5px rgba(0, 0, 0, 0.2);
    color: var(--el-color-primary-light-3);
    
    &:hover {
      background: linear-gradient(145deg, #303030, #282828);
      color: var(--el-color-primary-light-5);
      box-shadow: 
        -3px -3px 7px rgba(255, 255, 255, 0.03),
        3px 3px 7px rgba(0, 0, 0, 0.3);
    }
    
    .reasoning-arrow {
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }
  }
  
  .message-reasoning {
    background: linear-gradient(145deg, #1e1e1e, #242424);
    border-top: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    color: var(--el-text-color-regular);
    
    // 代码块样式优化
    :deep(pre), :deep(code) {
      background: linear-gradient(145deg, #252525, #202020);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 
        inset 1px 1px 2px rgba(0, 0, 0, 0.2),
        1px 1px 3px rgba(255, 255, 255, 0.02);
    }
    
    // 滚动条样式优化
    &::-webkit-scrollbar {
      width: 10px;
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: linear-gradient(90deg, #1a1a1a, #242424);
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(145deg, #333, #2a2a2a);
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 
        inset 1px 1px 2px rgba(255, 255, 255, 0.02),
        1px 1px 3px rgba(0, 0, 0, 0.2);
        
      &:hover {
        background: linear-gradient(145deg, #383838, #2f2f2f);
      }
    }
  }
}

// 添加思考中的动画效果
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

// 暗色模式适配
html.dark {
  .reasoning-time {
    background: linear-gradient(145deg, #2a2a2a, #252525);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 
      inset 1px 1px 1px rgba(255, 255, 255, 0.02),
      1px 1px 3px rgba(0, 0, 0, 0.2);
    color: var(--el-color-info-light-5);
    
    &.thinking {
      background: linear-gradient(145deg, 
        var(--el-color-primary-light-9), 
        var(--el-color-primary-light-8)
      );
      color: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-7);
    }
  }
}

/* 为思考过程中的代码块添加特殊样式 */
.message-reasoning {
  :deep(.reasoning-code) {
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;
    padding: 12px;
    margin: 10px 0;

    white-space: pre-wrap;
    word-break: break-word;
    font-size: 13px;
    line-height: 1.5;
    overflow-x: auto;
  }
  
  :deep(.reasoning-error) {
    background-color: var(--el-color-danger-lighter);
    border: 1px solid var(--el-color-danger-light);
    color: var(--el-color-danger);
    padding: 10px;
    border-radius: 4px;
  }
}

/* 暗色模式适配 */
html.dark {
  /* 聊天内容区域 */
  .chat-content {
    background: linear-gradient(145deg, #1a1c23, #20242c);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.03);
  }
  
  /* 聊天头部 */
  .chat-header {
    background: linear-gradient(145deg, #1a1a1a, #242424);
    border-bottom: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  /* 聊天消息区域 */
  .chat-messages {
    background: linear-gradient(145deg, #1d2027, #262b36);
    background-image: 
      linear-gradient(145deg, #1d2027, #262b36),
      radial-gradient(at 20% 30%, rgba(255, 255, 255, 0.05) 0, transparent 60%),
      radial-gradient(at 80% 70%, rgba(0, 0, 0, 0.2) 0, transparent 40%);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  /* 消息卡片 */
  .message-inner {
    border-color: rgba(255, 255, 255, 0.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  .message.user .message-inner {
    background: linear-gradient(145deg, #2a3042, #232736);
    box-shadow: 
      0 3px 8px rgba(0, 0, 0, 0.25),
      0 0 2px rgba(0, 0, 0, 0.2);
  }
  
  .message.assistant .message-inner {
    background: linear-gradient(145deg, #272a34, #21242c);
    box-shadow: 
      0 3px 8px rgba(0, 0, 0, 0.2),
      0 0 2px rgba(0, 0, 0, 0.15);
  }
  
  /* 头像 */
  .message-avatar {
    background: linear-gradient(145deg, #2b446b, #1e3050);
    box-shadow: 
      2px 2px 6px rgba(0, 0, 0, 0.3),
      -1px -1px 3px rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .message.user .message-avatar {
    background: linear-gradient(145deg, #294d37, #1e3a28);
  }
  
  /* 输入框 */
  .chat-input {
    background: linear-gradient(145deg, #1a1a1a, #242424);
    border-top: 1px solid rgba(255, 255, 255, 0.03);
  }
  
  .chat-input textarea {
    background: linear-gradient(145deg, #242424, #1e1e1e);
    color: var(--el-text-color-primary);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 
      inset 0 2px 4px rgba(0, 0, 0, 0.2),
      0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  /* 按钮样式 */
  .input-toggle-btn {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  }
  
  .input-toggle-btn.sending {
    box-shadow: 0 0 15px rgba(230, 162, 60, 0.5);
  }
  
  .input-toggle-btn.receiving {
    box-shadow: 0 0 15px rgba(103, 194, 58, 0.5);
  }
}

/* 聊天列表滚动优化 */
.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: calc(100% - 60px); /* 减去header高度 */
}

/* 聊天项目样式优化，确保不被压缩 */
.chat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 10px;
  background: linear-gradient(145deg, var(--el-bg-color), var(--el-bg-color-page));
  box-shadow: 
    1px 1px 4px rgba(0, 0, 0, 0.03),
    -1px -1px 4px rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  min-height: 24px; /* 确保最小高度 */
  flex-shrink: 0; /* 防止项目被压缩 */
}

/* 折叠状态下的聊天项目 */
.chat-sidebar.collapsed .chat-item {
  padding: 12px;
  justify-content: center;
  min-height: 24px; /* 确保最小高度 */
}

/* 聊天标题 - 添加缺失的样式 */
.chat-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  padding-right: 8px; /* 为操作按钮留空间 */
  user-select: none;
}

/* 聊天项操作按钮 */
.chat-actions {
  display: flex;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.chat-item:hover .chat-actions {
  opacity: 1;
}

.delete-btn {
  padding: 4px;
  height: auto;
  font-size: 14px;
}

/* 聊天点状态指示器 */
.chat-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--el-color-info-light-5);
  transition: all 0.3s ease;
}

.chat-dot.active-dot {
  background-color: var(--el-color-primary);
  box-shadow: 0 0 8px var(--el-color-primary-light-3);
}

/* 侧边栏滚动条样式 */
.chat-list::-webkit-scrollbar {
  width: 5px;
  background: transparent;
}

.chat-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.chat-list::-webkit-scrollbar-track {
  background: transparent;
}

/* 侧边栏操作按钮优化 */
.new-chat-btn, .clear-all-btn {
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  flex-grow: 1;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* 拟态按钮设计 */
  background: linear-gradient(135deg, 
    var(--el-bg-color) 0%, 
    var(--el-bg-color-page) 100%
  );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow: 
    2px 2px 6px rgba(0, 0, 0, 0.06),
    -1px -1px 4px rgba(255, 255, 255, 0.6);
}

.new-chat-btn:hover, .clear-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 
    3px 3px 8px rgba(0, 0, 0, 0.08),
    -2px -2px 5px rgba(255, 255, 255, 0.7);
}

.new-chat-btn:active, .clear-all-btn:active {
  transform: translateY(1px);
  box-shadow: 
    1px 1px 3px rgba(0, 0, 0, 0.05),
    -1px -1px 2px rgba(255, 255, 255, 0.5);
}

.new-chat-btn {
  background: linear-gradient(135deg, 
    var(--el-color-primary-light-9) 0%, 
    var(--el-color-primary-light-8) 100%
  );
  color: var(--el-color-primary-dark-2);
  border: 1px solid var(--el-color-primary-light-5);
}

.clear-all-btn {
  background: linear-gradient(135deg, 
    var(--el-color-info-light-9) 0%, 
    var(--el-color-info-light-8) 100%
  );
  color: var(--el-color-info-dark-2);
  border: 1px solid var(--el-color-info-light-5);
}

/* 折叠按钮优化 */
.collapse-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    var(--el-bg-color) 0%, 
    var(--el-bg-color-page) 100%
  );
  box-shadow: 
    2px 2px 5px rgba(0, 0, 0, 0.05),
    -1px -1px 3px rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-left: auto;
  flex-shrink: 0;
}

.collapse-btn:hover {
  transform: translateY(-2px);
  box-shadow: 
    3px 3px 7px rgba(0, 0, 0, 0.08),
    -2px -2px 5px rgba(255, 255, 255, 0.6);
}

.collapse-btn:active {
  transform: translateY(1px);
  box-shadow: 
    1px 1px 3px rgba(0, 0, 0, 0.04),
    -1px -1px 2px rgba(255, 255, 255, 0.5);
}

.collapse-btn .el-icon {
  transition: transform 0.3s ease;
  font-size: 18px;
  color: var(--el-text-color-secondary);
}

.collapse-btn .is-collapsed {
  transform: rotate(180deg);
}

/* 暗色模式适配 */
html.dark .new-chat-btn, html.dark .clear-all-btn {
  background: linear-gradient(135deg, 
    #232323 0%, 
    #1e1e1e 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 
    2px 2px 6px rgba(0, 0, 0, 0.2),
    -1px -1px 3px rgba(255, 255, 255, 0.02);
}

html.dark .new-chat-btn:hover, html.dark .clear-all-btn:hover {
  box-shadow: 
    3px 3px 8px rgba(0, 0, 0, 0.3),
    -1px -1px 3px rgba(255, 255, 255, 0.03);
}

html.dark .new-chat-btn {
  background: linear-gradient(135deg, 
    #1c3050 0%, 
    #15253e 100%
  );
  color: #a3c5f0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

html.dark .clear-all-btn {
  background: linear-gradient(135deg, 
    #2c2c2c 0%, 
    #232323 100%
  );
  color: #c0c0c0;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

html.dark .collapse-btn {
  background: linear-gradient(135deg, 
    #232323 0%, 
    #1e1e1e 100%
  );
  box-shadow: 
    2px 2px 5px rgba(0, 0, 0, 0.25),
    -1px -1px 2px rgba(255, 255, 255, 0.02);
}

html.dark .collapse-btn:hover {
  box-shadow: 
    3px 3px 7px rgba(0, 0, 0, 0.3),
    -1px -1px 2px rgba(255, 255, 255, 0.03);
}

/* 侧边栏折叠状态下的按钮调整 */
.chat-sidebar.collapsed .sidebar-header {
  padding: 10px 8px;
  justify-content: center;
}

.chat-sidebar.collapsed .new-chat-btn,
.chat-sidebar.collapsed .clear-all-btn {
  width: 36px;
  padding: 8px;
  border-radius: 50%;
}

.chat-sidebar.collapsed .new-chat-btn span,
.chat-sidebar.collapsed .clear-all-btn span {
  display: none;
}

.chat-sidebar.collapsed .collapse-btn {
  margin: 0;
  margin-top: 8px;
}

/* 全局样式，避免被scoped限制 */
.markdown-body {
  /* ... existing code ... */
}

/* ... existing code ... */

/* 代码块预览按钮样式 */
.code-btn svg {
  margin-right: 4px;
}

/* 预览模态框中的代码区域样式 */
.code-preview-modal .code-panel pre {
  margin: 0;
  background-color: #f5f7fa;
}

.code-preview-modal .code-panel code {

  white-space: pre-wrap;
}