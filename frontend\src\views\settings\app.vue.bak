<template>
  <div class="app-settings scroll-container">
    <div class="settings-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="Chrome设置" name="chrome">
          <div class="settings-panel chrome-settings">
            <div class="section-header">
              <h2 class="section-title">Chrome 设置</h2>
            </div>

            <el-form label-width="120px">
              <el-form-item label="Chrome路径">
                <div class="path-input-group">
                  <el-input
                      v-model="settings.chrome.path"
                      placeholder="请选择 Chrome 浏览器路径"
                  />
                  <el-button-group>
                    <el-button type="primary" @click="selectDirectory('chromePath')">
                      <el-icon><Folder /></el-icon>
                      选择路径
                    </el-button>
                    <el-button type="success" @click="detectChromePath">
                      <el-icon><Search /></el-icon>
                      自动检测
                    </el-button>
                  </el-button-group>
                </div>
              </el-form-item>

              <el-form-item label="下载目录">
                <div class="path-input-group">
                  <el-input
                      v-model="settings.chrome.downloadDir"
                      placeholder="请选择下载目录"
                  />
                  <el-button type="primary" @click="selectDirectory('downloadDir')">
                    <el-icon><Folder /></el-icon>
                    选择路径
                  </el-button>
                </div>
              </el-form-item>
            </el-form>

            <div class="section-header">
              <h2 class="section-title">用户数据目录</h2>
              <div class="header-actions">
                <el-button type="primary" @click="addUserDataDir">
                  <el-icon><Plus /></el-icon>
                  添加目录
                </el-button>
              </div>
            </div>

            <el-table :data="settings.chrome.userDataDirs" style="width: 100%" border>
              <el-table-column prop="name" label="名称" min-width="120" />
              <el-table-column prop="path" label="路径" min-width="200" show-overflow-tooltip />
              <el-table-column prop="port" label="端口" width="100" align="center" />
              <el-table-column label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="runningStatus[row.id] ? 'success' : 'info'">
                    {{ runningStatus[row.id] ? '运行中' : '已停止' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="默认" width="80" align="center">
                <template #default="{ row }">
                  <el-switch
                      v-model="row.isDefault"
                      @change="(val) => handleDefaultChange(row, val)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="280" align="center">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button
                        :type="runningStatus[row.id] ? 'danger' : 'success'"
                        size="small"
                        @click="toggleChrome(row)"
                    >
                      {{ runningStatus[row.id] ? '停止' : '启动' }}
                    </el-button>
                    <el-button
                        type="primary"
                        size="small"
                        @click="editUserDataDir(row)"
                    >
                      编辑
                    </el-button>
                    <el-button
                        type="danger"
                        size="small"
                        @click="deleteUserDataDir(row)"
                    >
                      删除
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="AI角色" name="ai">
          <div class="settings-panel ai-roles-container">
            <div class="section-header">
              <h2 class="section-title">AI角色管理</h2>
              <div class="header-actions">
                <el-button-group>
                  <el-button type="primary" @click="aiRoleImportDialog.visible = true">
                    <el-icon><Upload /></el-icon>
                    导入角色
                  </el-button>
                  <el-button type="success" @click="exportAllAIRoles">
                    <el-icon><Download /></el-icon>
                    导出角色
                  </el-button>
                  <el-button type="primary" @click="addAIRole">
                    <el-icon><Plus /></el-icon>
                    添加角色
                  </el-button>
                </el-button-group>
              </div>
            </div>

            <el-table :data="settings.ai_roles" style="width: 100%" border>
              <el-table-column type="expand">
                <template #default="{ row }">
                  <div class="detail-preview">
                    <pre class="code-content"><code>{{ row.prompt }}</code></pre>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="名称" min-width="120" />
              <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip />
              <el-table-column label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-switch
                      v-model="row.enabled"
                      @change="handleAIRoleStatusChange(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button
                        type="primary"
                        size="small"
                        @click="editAIRole(row)"
                    >
                      编辑
                    </el-button>
                    <el-button
                        type="danger"
                        size="small"
                        @click="deleteAIRole(row)"
                        :disabled="row.id === 'default'"
                    >
                      删除
                    </el-button>
                    <el-button
                        type="success"
                        size="small"
                        @click="exportSingleAIRole(row)"
                    >
                      导出
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- AI角色编辑对话框 -->
          <el-dialog
              v-model="aiRoleDialog.visible"
              :title="aiRoleDialog.isEdit ? '编辑AI角色' : '添加AI角色'"
              width="60%"
          >
            <el-form
                ref="aiRoleFormRef"
                :model="aiRoleDialog.form"
                :rules="aiRoleDialog.rules"
                label-width="100px"
            >
              <el-form-item label="名称" prop="name">
                <el-input v-model="aiRoleDialog.form.name" />
              </el-form-item>
              <el-form-item label="描述" prop="description">
                <el-input
                    v-model="aiRoleDialog.form.description"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入AI角色的描述..."
                />
              </el-form-item>
              <el-form-item label="提示词" prop="prompt">
                <el-input
                    v-model="aiRoleDialog.form.prompt"
                    type="textarea"
                    :rows="10"
                    placeholder="请输入AI角色的提示词..."
                />
              </el-form-item>
              <el-form-item label="启用" prop="enabled">
                <el-switch v-model="aiRoleDialog.form.enabled" />
              </el-form-item>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="aiRoleDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="saveAIRole">确定</el-button>
              </span>
            </template>
          </el-dialog>

          <!-- AI角色导入对话框 -->
          <el-dialog
              v-model="aiRoleImportDialog.visible"
              title="导入AI角色"
              width="600px"
          >
            <el-form>
              <el-form-item label="导入内容">
                <el-input
                    v-model="aiRoleImportDialog.content"
                    type="textarea"
                    :rows="10"
                    placeholder="请粘贴要导入的JSON内容"
                />
              </el-form-item>
            </el-form>
            <template #footer>
              <el-button @click="aiRoleImportDialog.visible = false">取消</el-button>
              <el-button type="primary" @click="importAIRoles">导入</el-button>
            </template>
          </el-dialog>
        </el-tab-pane>

        <el-tab-pane label="代码块" name="code">
          <div class="settings-panel code-blocks-container">
            <div class="section-header">
              <h2 class="section-title">代码块管理</h2>
              <div class="header-actions">
                <el-button-group>
                  <el-button type="primary" @click="codeBlockImportDialog.visible = true">
                    <el-icon><Upload /></el-icon>
                    导入代码块
                  </el-button>
                  <el-button type="success" @click="exportAllCodeBlocks">
                    <el-icon><Download /></el-icon>
                    导出代码块
                  </el-button>
                  <el-button type="primary" @click="addCodeBlock">
                    <el-icon><Plus /></el-icon>
                    添加代码块
                  </el-button>
                </el-button-group>
              </div>
            </div>

            <el-table :data="settings.code_blocks" style="width: 100%" border>
              <el-table-column type="expand">
                <template #default="{ row }">
                  <div class="detail-preview">
                    <pre class="code-content"><code>{{ row.code }}</code></pre>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="名称" min-width="120" />
              <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip />
              <el-table-column label="用户数据目录" min-width="180">
                <template #default="{ row }">
                  <el-select
                      v-model="row.userDataDirId"
                      placeholder="选择用户数据目录"
                      @change="handleUserDataDirChange(row)"
                      style="width: 100%"
                  >
                    <el-option
                        v-for="dir in settings.chrome.userDataDirs"
                        :key="dir.id"
                        :label="dir.name"
                        :value="dir.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="codeBlockStatus[row.id] ? 'success' : 'info'">
                    {{ codeBlockStatus[row.id] ? '运行中' : '已停止' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="280" align="center">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button
                        :type="codeBlockStatus[row.id] ? 'danger' : 'success'"
                        size="small"
                        @click="toggleCodeBlock(row)"
                    >
                      {{ codeBlockStatus[row.id] ? '停止' : '启动' }}
                    </el-button>
                    <el-button
                        type="primary"
                        size="small"
                        @click="editCodeBlock(row)"
                    >
                      编辑
                    </el-button>
                    <el-button
                        type="danger"
                        size="small"
                        @click="deleteCodeBlock(row)"
                    >
                      删除
                    </el-button>
                    <el-button
                        type="success"
                        size="small"
                        @click="exportSingleCodeBlock(row)"
                    >
                      导出
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 代码块导入对话框 -->
          <el-dialog
              v-model="codeBlockImportDialog.visible"
              title="导入代码块"
              width="600px"
          >
            <el-form>
              <el-form-item label="导入内容">
                <el-input
                    v-model="codeBlockImportDialog.content"
                    type="textarea"
                    :rows="10"
                    placeholder="请粘贴要导入的JSON内容"
                />
              </el-form-item>
            </el-form>
            <template #footer>
              <el-button @click="codeBlockImportDialog.visible = false">取消</el-button>
              <el-button type="primary" @click="importCodeBlocks">导入</el-button>
            </template>
          </el-dialog>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 添加/编辑角色对话框 -->
    <el-dialog
        v-model="roleDialog.visible"
        :title="roleDialog.isEdit ? '编辑角色' : '新建角色'"
        width="600px"
        destroy-on-close
    >
      <el-form :model="roleDialog.form" label-position="top">
        <el-form-item label="角色名称" required>
          <el-input v-model="roleDialog.form.name" placeholder="给这个角色起个名字" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
              v-model="roleDialog.form.description"
              type="textarea"
              :rows="2"
              placeholder="简短描述这个角色的特点和用途"
          />
        </el-form-item>
        <el-form-item label="提示词" required>
          <el-input
              v-model="roleDialog.form.prompt"
              type="textarea"
              :rows="6"
              placeholder="详细描述这个角色的行为、知识领域和特殊指令"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
              v-model="roleDialog.form.enabled"
              active-text="启用"
              inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveRole">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 用户数据目录对话框 -->
    <el-dialog
        v-model="userDataDirDialog.visible"
        :title="userDataDirDialog.isEdit ? '编辑用户数据目录' : '新建用户数据目录'"
        width="500px"
        destroy-on-close
    >
      <el-form :model="userDataDirDialog.form" label-position="top">
        <el-form-item label="名称" required>
          <el-input v-model="userDataDirDialog.form.name" placeholder="给这个配置起个名字" />
        </el-form-item>
        <el-form-item label="路径" required>
          <div class="path-input-group">
            <el-input v-model="userDataDirDialog.form.path" />
            <el-button type="primary" @click="selectDirectory('userDataDir')">
              <el-icon><Folder /></el-icon>
              选择路径
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="端口" required>
          <el-input-number
              v-model="userDataDirDialog.form.port"
              :min="1"
              :max="65535"
              placeholder="请输入端口号"
          />
        </el-form-item>
        <el-form-item label="启用扩展">
          <el-switch v-model="userDataDirDialog.form.enableExtensions" />
        </el-form-item>
        <el-form-item label="扩展路径" v-if="userDataDirDialog.form.enableExtensions">
          <div class="path-input-group">
            <el-input v-model="userDataDirDialog.form.extensionsPath" placeholder="请选择Chrome扩展所在目录" />
            <el-button type="primary" @click="selectDirectory('extensionsDir')">
              <el-icon><Folder /></el-icon>
              选择路径
            </el-button>
          </div>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="userDataDirDialog.form.isDefault">设为默认</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDataDirDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveUserDataDir">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加/编辑代码块对话框 -->
    <el-dialog
        v-model="codeBlockDialog.visible"
        :title="codeBlockDialog.isEdit ? '编辑代码块' : '添加代码块'"
        width="60%"
    >
      <el-form :model="codeBlockDialog.form" label-width="100px">
        <el-form-item label="名称" required>
          <el-input v-model="codeBlockDialog.form.name" placeholder="请输入代码块名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
              v-model="codeBlockDialog.form.description"
              type="textarea"
              placeholder="请输入代码块描述"
          />
        </el-form-item>
        <el-form-item label="代码" required>
          <el-input
              v-model="codeBlockDialog.form.code"
              type="textarea"
              :rows="10"
              placeholder="请输入Python代码"
          />
        </el-form-item>
        <el-form-item label="用户数据目录">
          <el-select v-model="codeBlockDialog.form.userDataDirId" placeholder="选择用户数据目录">
            <el-option
                v-for="dir in settings.chrome.userDataDirs"
                :key="dir.id"
                :label="dir.name"
                :value="dir.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="codeBlockDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveCodeBlock">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { Folder, VideoPlay, VideoPause, Delete, Plus, Star, Edit, Search, Upload, Download } from '@element-plus/icons-vue'

// 设置状态
const settings = ref({
  chrome: {
    path: '',
    port: 12999,
    downloadDir: '',
    userDataDirs: [
      {
        id: 'default',
        name: '默认配置',
        path: '',
        isDefault: true
      }
    ]
  },
  ai_roles: [],
  code_blocks: []
})

const selectedRole = ref(null)
const roleDialog = ref({
  visible: false,
  isEdit: false,
  editingId: null,
  form: {
    name: '',
    description: '',
    prompt: '',
    enabled: true
  }
})

const userDataDirDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    name: '',
    path: '',
    port: 9222,
    enableExtensions: false,
    extensionsPath: '',
    isDefault: false
  }
})

const codeBlockDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    id: '',
    name: '',
    description: '',
    code: '',
    userDataDirId: ''
  }
})

// Python代码块列表
const pythonCodeBlocks = ref([
  {
    id: 'login_gitee',
    name: 'Gitee登录',
    code: `
# 跳转到登录页面
tab = chrome.latest_tab
tab.get('https://gitee.com/login')
# 定位到账号文本框
ele = tab.ele('#user_login')
    `
  },
  {
    id: 'open_google',
    name: '打开Google',
    code: `
tab = chrome.latest_tab
tab.get('https://www.google.com')
    `
  }
])

// 用户数据目录的运行状态
const runningStatus = ref({})

// 代码块运行状态
const codeBlockStatus = ref({})

// 检查Chrome实例状态
const checkChromeStatus = async (dir) => {
  try {
    const response = await window.pywebview.api.check_chrome_status({
      path: `${dir.path}/${dir.name}`,
      port: dir.port
    })
    const data = typeof response === 'string' ? JSON.parse(response) : response
    if (data.status === 'success') {
      runningStatus.value[dir.id] = data.data.running
    }
  } catch (error) {
    console.error('检查Chrome状态失败:', error)
    runningStatus.value[dir.id] = false
  }
}

// 定期检查所有Chrome实例状态
const startStatusCheck = async () => {
  try {
    // 检查所有用户数据目录的状态
    for (const dir of settings.value.chrome.userDataDirs) {
      await checkChromeStatus(dir)
    }

    // 更新代码块状态
    for (const block of settings.value.code_blocks) {
      if (block.userDataDirId) {
        const dir = settings.value.chrome.userDataDirs.find(d => d.id === block.userDataDirId)
        if (dir) {
          codeBlockStatus.value[block.id] = runningStatus.value[dir.id] || false
        }
      }
    }
  } catch (error) {
    console.error('检查状态失败:', error)
  }
}

// 启动状态检查定时器
onMounted(() => {
  loadSettings()
  // 每5秒检查一次状态
  setInterval(startStatusCheck, 1000)
  // 初始检查
  startStatusCheck()
})

// 启动/停止Chrome
const toggleChrome = async (dir) => {

  try {
    if (!runningStatus.value[dir.id]) {
      // 启动Chrome

      const response = await window.pywebview.api.start_chrome_with_profile({
        path: `${dir.path}/${dir.name}`,
        port: dir.port,
        extensions_enabled:dir.enableExtensions,
        extensions_path:dir.extensionsPath,
        codeId: dir.selectedCode,
      })
      const data = typeof response === 'string' ? JSON.parse(response) : response

      if (data.status === 'success') {
        // 立即检查状态
        await checkChromeStatus(dir)
        ElMessage.success('Chrome启动成功')
      } else {
        ElMessage.error(data.message || '启动失败')
      }
    } else {
      // 停止Chrome
      const response = await window.pywebview.api.stop_chrome_profile({
        path: `${dir.path}/${dir.name}`,
        port: dir.port
      })
      const data = typeof response === 'string' ? JSON.parse(response) : response

      if (data.status === 'success') {
        // 立即更新状态
        runningStatus.value[dir.id] = false
        ElMessage.success('Chrome已停止')
      } else {
        // 重新检查状态
        await checkChromeStatus(dir)
        ElMessage.error(data.message || '停止失败')
      }
    }
  } catch (error) {
    // 发生错误时重新检查状态
    await checkChromeStatus(dir)
    ElMessage.error('操作失败: ' + error.message)
  }
}

// 启动/停止代码块
const toggleCodeBlock = async (block) => {
  try {

    if (!block.userDataDirId) {
      ElMessage.warning('请先选择用户数据目录')
      return
    }

    // 查找对应的用户数据目录
    const userDataDir = settings.value.chrome.userDataDirs.find(dir => dir.id === block.userDataDirId)
    if (!userDataDir) {
      ElMessage.error('未找到对应的用户数据目录')
      return
    }

    const isRunning = codeBlockStatus.value[block.id]
    if (!isRunning) {
      // 启动代码块
      const response = await window.pywebview.api.start_chrome_with_profile({
        path: `${userDataDir.path}/${userDataDir.name}`,
        port: userDataDir.port,
        codeId: block.id,
        extensions_enabled:userDataDir.enableExtensions,
        extensions_path:userDataDir.extensionsPath,
      })
      const result = typeof response === 'string' ? JSON.parse(response) : response

      if (result.status === 'success') {
        // 更新状态
        codeBlockStatus.value[block.id] = true
        runningStatus.value[userDataDir.id] = true
        ElMessage.success('代码块启动成功')
      } else {
        ElMessage.error(result.message || '启动失败')
      }
    } else {
      // 停止代码块
      const response = await window.pywebview.api.stop_chrome_profile({
        path: `${userDataDir.path}/${userDataDir.name}`,
        port: userDataDir.port
      })
      const result = typeof response === 'string' ? JSON.parse(response) : response

      if (result.status === 'success') {
        // 更新状态
        codeBlockStatus.value[block.id] = false
        runningStatus.value[userDataDir.id] = false
        ElMessage.success('代码块已停止')
      } else {
        ElMessage.error(result.message || '停止失败')
      }
    }

    // 更新状态
    await startStatusCheck()
  } catch (error) {
    ElMessage.error('操作失败：' + error.message)
    // 重新检查状态
    await startStatusCheck()
  }
}

// 加载设置
const loadSettings = async () => {
  try {
    const response = await window.pywebview.api.get_settings()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success' && result.data) {
      // 确保设置对象的结构完整
      settings.value = {
        chrome: {
          path: '',
          port: 12999,
          downloadDir: '',
          userDataDirs: [
            {
              id: 'default',
              name: '默认配置',
              path: '',
              isDefault: true
            }
          ],
          ...(result.data.chrome || {})
        },
        ai_roles: result.data.ai_roles || [],
        code_blocks: result.data.code_blocks || []
      }

      // 如果没有设置Chrome路径，尝试自动检测
      if (!settings.value.chrome.path) {
        const chromeResponse = await window.pywebview.api.detect_chrome_path()
        const chromeResult = typeof chromeResponse === 'string' ? JSON.parse(chromeResponse) : chromeResponse

        if (chromeResult.status === 'success' && chromeResult.data) {
          const chromePath = typeof chromeResult.data === 'object' ? chromeResult.data.path : chromeResult.data
          settings.value.chrome.path = chromePath
          await saveSettings()
        }
      }

      // 初始化运行状态
      if (settings.value.chrome?.userDataDirs) {
        settings.value.chrome.userDataDirs.forEach(dir => {
          runningStatus.value[dir.id] = false
          checkChromeStatus(dir)
        })
      }

      return true
    } else {
      throw new Error(result.message || '加载失败')
    }
  } catch (error) {
    console.error('Load settings error:', error)
    ElMessage.error('设置加载失败：' + error.message)
    return false
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    // 确保所有必要的字段都存在且格式正确
    const settingsToSave = {
      chrome: {
        path: settings.value.chrome?.path || '',
        port: settings.value.chrome?.port || 12999,
        downloadDir: settings.value.chrome?.downloadDir || '',
        userDataDirs: settings.value.chrome?.userDataDirs?.map(dir => ({
          id: dir.id,
          name: dir.name,
          path: dir.path,
          port: dir.port || 12999,
          isDefault: !!dir.isDefault,
          enableExtensions: !!dir.enableExtensions,
          extensionsPath: dir.extensionsPath || ''
        })) || []
      },
      ai_roles: settings.value.ai_roles?.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description || '',
        prompt: role.prompt || '',
        enabled: !!role.enabled
      })) || [],
      code_blocks: settings.value.code_blocks?.map(block => ({
        id: block.id,
        name: block.name,
        description: block.description || '',
        code: block.code || '',
        userDataDirId: block.userDataDirId
      })) || []
    }

    // 调用后端 API 保存设置
    const response = await window.pywebview.api.save_settings(settingsToSave)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('设置保存成功')
      return true
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('Save settings error:', error)
    ElMessage.error('设置保存失败：' + error.message)
    return false
  }
}

// 选择目录
const selectDirectory = async (type) => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success' && result.data) {
      if (type === 'chromePath') {
        settings.value.chrome.path = result.data
      } else if (type === 'downloadDir') {
        settings.value.chrome.downloadDir = result.data
      } else if (type === 'userDataDir') {
        userDataDirDialog.value.form.path = result.data
      } else if (type === 'extensionsDir') {
        userDataDirDialog.value.form.extensionsPath = result.data
      }
      await saveSettings()
      ElMessage.success('路径选择成功')
    }
  } catch (error) {
    ElMessage.error('选择失败：' + error.message)
  }
}

// 自动检测Chrome路径
const detectChromePath = async () => {
  try {
    const response = await window.pywebview.api.detect_chrome_path()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success' && result.data) {
      const chromePath = typeof result.data === 'object' ? result.data.path : result.data
      settings.value.chrome.path = chromePath
      await saveSettings()
      ElMessage.success('已自动检测到Chrome路径：' + chromePath)
    } else {
      ElMessage.warning('未检测到Chrome路径，请手动选择')
    }
  } catch (error) {
    ElMessage.error('检测失败：' + error.message)
  }
}

// 选择角色
const selectRole = (role) => {
  selectedRole.value = role
}

// 添加新角色
const addNewRole = () => {
  roleDialog.value = {
    visible: true,
    isEdit: false,
    editingId: null,
    form: {
      name: '',
      description: '',
      prompt: '',
      enabled: true
    }
  }
}

// 编辑选中的角色
const editSelectedRole = () => {
  if (!selectedRole.value) return
  roleDialog.value = {
    visible: true,
    isEdit: true,
    editingId: selectedRole.value.id,
    form: { ...selectedRole.value }
  }
}

// 保存角色
const saveRole = async () => {
  try {
    const { form, isEdit, editingId } = roleDialog.value

    // 表单验证
    if (!form.name?.trim()) {
      ElMessage.warning('请输入角色名称')
      return
    }
    if (!form.prompt?.trim()) {
      ElMessage.warning('请输入提示词')
      return
    }

    let response
    if (isEdit) {
      // 更新现有角色
      response = await window.pywebview.api.update_ai_role(editingId, form)
    } else {
      // 添加新角色
      response = await window.pywebview.api.add_ai_role(form)
    }

    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success(isEdit ? '角色更新成功' : '角色添加成功')
      roleDialog.value.visible = false
      await loadSettings() // 重新加载设置以获取最新数据
    } else {
      ElMessage.error(result.message || (isEdit ? '更新角色失败' : '添加角色失败'))
    }
  } catch (error) {
    ElMessage.error('保存角色失败：' + error.message)
  }
}

// 删除角色
const deleteRole = async (role) => {
  if (!role || role.id === 'default') return

  try {
    await ElMessageBox.confirm(
        `确定要删除角色"${role.name}"吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await window.pywebview.api.delete_ai_role(role.id)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('角色删除成功')
      selectedRole.value = null
      await loadSettings() // 重新加载设置以获取最新数据
    } else {
      ElMessage.error(result.message || '删除角色失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除角色失败：' + error.message)
    }
  }
}

// 监听角色启用状态变化
const handleRoleEnableChange = async (role) => {
  try {
    const currentRoles = settings.value.ai_roles || []
    const index = currentRoles.findIndex(r => r.id === role.id)
    if (index !== -1) {
      currentRoles[index].enabled = role.enabled
      settings.value.ai_roles = currentRoles
      await saveSettings()
      ElMessage.success(`角色已${role.enabled ? '启用' : '禁用'}`)
    }
  } catch (e) {
    ElMessage.error('状态更新失败：' + e.message)
    // 回滚状态
    role.enabled = !role.enabled
  }
}

// 添加用户数据目录
const addUserDataDir = () => {
  userDataDirDialog.value.visible = true
  userDataDirDialog.value.isEdit = false
  userDataDirDialog.value.form = {
    name: '',
    path: '',
    port: 9222,
    enableExtensions: false,
    extensionsPath: '',
    isDefault: false
  }
}

// 启动用户数据目录并执行代码
const startChromeWithCode = async (dir) => {
  try {
    console.log({
      // path: dir.path 组合 dir.name,
      path: `${dir.path}/${dir.name}`,
      port: dir.port,
      codeId: dir.selectedCode,
      extensions_enabled:dir.enableExtensions,
      extensions_path:dir.extensionsPath,

    })

    const response = await window.pywebview.api.start_chrome_with_profile({
      // path: dir.path 组合 dir.name,
      path: `${dir.path}/${dir.name}`,
      port: dir.port,
      codeId: dir.selectedCode,
      extensions_enabled:dir.enableExtensions,
      extensions_path:dir.extensionsPath,

    })
    if (response.status === 'success') {
      ElMessage.success('Chrome启动成功')
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    ElMessage.error('启动失败: ' + error.message)
  }
}

// 编辑用户数据目录
const editUserDataDir = (dir) => {
  userDataDirDialog.value.visible = true
  userDataDirDialog.value.isEdit = true
  userDataDirDialog.value.form = { ...dir }
}

// 保存用户数据目录
const saveUserDataDir = async () => {
  try {
    const formData = { ...userDataDirDialog.value.form }

    // 如果禁用了扩展，清空扩展路径
    if (!formData.enableExtensions) {
      formData.extensionsPath = ''
    }

    if (userDataDirDialog.value.isEdit) {
      const dirId = formData.id
      delete formData.id
      await window.pywebview.api.update_user_data_dir(dirId, formData)
    } else {
      await window.pywebview.api.add_user_data_dir(formData)
    }

    await loadSettings()
    userDataDirDialog.value.visible = false
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
}

// 删除用户数据目录
const deleteUserDataDir = async (dir) => {
  try {
    await ElMessageBox.confirm(
        `确定要删除用户数据目录"${dir.name}"吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await window.pywebview.api.delete_user_data_dir(dir.id)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('用户数据目录删除成功')
      await loadSettings()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 设置默认用户数据目录
const setDefaultUserDataDir = async (dir) => {
  try {
    const response = await window.pywebview.api.set_default_user_data_dir(dir.id)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('默认用户数据目录设置成功')
      await loadSettings()
    } else {
      ElMessage.error(result.message || '设置默认目录失败')
    }
  } catch (error) {
    ElMessage.error('设置默认目录失败：' + error.message)
  }
}

// 执行代码块
const executeCodeBlock = async (codeBlock) => {
  try {
    const response = await window.pywebview.api.execute_code_block(codeBlock.id)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('代码块执行成功')
    } else {
      ElMessage.error(result.message || '代码块执行失败')
    }
  } catch (error) {
    ElMessage.error('代码块执行失败：' + error.message)
  }
}

// 添加代码块
const addCodeBlock = () => {
  codeBlockDialog.value.isEdit = false
  codeBlockDialog.value.form = {
    id: '',
    name: '',
    description: '',
    code: '',
    userDataDirId: ''
  }
  codeBlockDialog.value.visible = true
}

// 编辑代码块
const editCodeBlock = (block) => {
  codeBlockDialog.value.isEdit = true
  codeBlockDialog.value.form = { ...block }
  codeBlockDialog.value.visible = true
}

// 保存代码块
const saveCodeBlock = async () => {
  try {
    const form = codeBlockDialog.value.form
    if (!form.name?.trim()) {
      ElMessage.warning('请输入名称')
      return
    }
    if (!form.code?.trim()) {
      ElMessage.warning('请输入代码')
      return
    }

    const response = await window.pywebview.api[codeBlockDialog.value.isEdit ? 'update_code_block' : 'add_code_block'](form)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success(codeBlockDialog.value.isEdit ? '代码块更新成功' : '代码块添加成功')
      codeBlockDialog.value.visible = false
      await loadSettings()
    } else {
      ElMessage.error(result.message || (codeBlockDialog.value.isEdit ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
}

// 删除代码块
const deleteCodeBlock = async (block) => {
  try {
    await ElMessageBox.confirm(
        `确定要删除代码块"${block.name}"吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await window.pywebview.api.delete_code_block(block.id)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('代码块删除成功')
      await loadSettings()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 处理用户数据目录选择变化
const handleUserDataDirChange = async (codeBlock) => {
  try {
    const response = await window.pywebview.api.update_code_block({
      id: codeBlock.id,
      name: codeBlock.name,
      description: codeBlock.description,
      code: codeBlock.code,
      userDataDirId: codeBlock.userDataDirId
    })
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('用户数据目录更新成功')
      await loadSettings()
    } else {
      ElMessage.error(result.message || '更新失败')
      // 如果更新失败，回滚选择
      await loadSettings()
    }
  } catch (error) {
    ElMessage.error('更新用户数据目录失败：' + error.message)
    // 发生错误时也回滚选择
    await loadSettings()
  }
}

// 导出单个AI角色
const exportSingleAIRole = (role) => {
  try {
    // 创建导出数据
    const exportData = {
      ai_roles: [{
        id: role.id,
        name: role.name,
        description: role.description || '',
        prompt: role.prompt || '',
        enabled: !!role.enabled
      }]
    }

    // 转换为格式化的字符串
    const exportString = JSON.stringify(exportData, null, 2)

    // 复制到剪贴板
    window.pywebview.api.copy_to_clipboard(exportString).then(() => {
      ElMessage.success('已复制到剪贴板')
    })
  } catch (error) {
    console.error('Export AI role error:', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

// 导出所有AI角色
const exportAllAIRoles = () => {
  try {
    const roles = settings.value.ai_roles || []
    if (roles.length === 0) {
      ElMessage.warning('没有可导出的AI角色')
      return
    }

    // 创建导出数据
    const exportData = {
      ai_roles: roles.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description || '',
        prompt: role.prompt || '',
        enabled: !!role.enabled
      }))
    }

    // 转换为格式化的字符串
    const exportString = JSON.stringify(exportData, null, 2)

    // 复制到剪贴板
    window.pywebview.api.copy_to_clipboard(exportString).then(() => {
      ElMessage.success('已复制到剪贴板')
    })
  } catch (error) {
    console.error('Export AI roles error:', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

// 导入AI角色
const importAIRoles = async () => {
  try {
    if (!aiRoleImportDialog.value.content.trim()) {
      ElMessage.warning('请输入导入内容')
      return
    }

    const importedData = JSON.parse(aiRoleImportDialog.value.content)

    // 验证导入的数据格式
    if (!importedData.ai_roles || !Array.isArray(importedData.ai_roles)) {
      throw new Error('导入的内容格式不正确')
    }

    await ElMessageBox.confirm(
        `确定导入 ${importedData.ai_roles.length} 个AI角色吗？`,
        '导入确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const currentRoles = settings.value.ai_roles || []
    const existingIds = new Set(currentRoles.map(r => r.id))

    // 检查重复项
    const duplicates = []
    const newRoles = []

    importedData.ai_roles.forEach(importRole => {
      if (existingIds.has(importRole.id)) {
        duplicates.push(importRole)
      } else {
        newRoles.push(importRole)
      }
    })

    if (newRoles.length > 0) {
      // 更新设置
      settings.value.ai_roles = [...currentRoles, ...newRoles]
      await saveSettings()
    }

    // 显示导入结果
    let message = []
    if (newRoles.length > 0) {
      message.push(`成功导入 ${newRoles.length} 个新角色`)
    }
    if (duplicates.length > 0) {
      message.push(`已过滤 ${duplicates.length} 个重复角色`)
    }
    ElMessage.success(message.join('，'))

    aiRoleImportDialog.value.visible = false
    aiRoleImportDialog.value.content = ''
  } catch (error) {
    console.error('Import AI roles error:', error)
    ElMessage.error('导入失败：' + error.message)
  }
}

// 导出单个代码块
const exportSingleCodeBlock = (block) => {
  try {
    // 创建导出数据
    const exportData = {
      code_blocks: [{
        id: block.id,
        name: block.name,
        description: block.description || '',
        code: block.code || '',
        userDataDirId: block.userDataDirId
      }]
    }

    // 转换为格式化的字符串
    const exportString = JSON.stringify(exportData, null, 2)

    // 复制到剪贴板
    window.pywebview.api.copy_to_clipboard(exportString).then(() => {
      ElMessage.success('已复制到剪贴板')
    })
  } catch (error) {
    console.error('Export code block error:', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

// 导出所有代码块
const exportAllCodeBlocks = () => {
  try {
    const blocks = settings.value.code_blocks || []
    if (blocks.length === 0) {
      ElMessage.warning('没有可导出的代码块')
      return
    }

    // 创建导出数据
    const exportData = {
      code_blocks: blocks.map(block => ({
        id: block.id,
        name: block.name,
        description: block.description || '',
        code: block.code || '',
        userDataDirId: block.userDataDirId
      }))
    }

    // 转换为格式化的字符串
    const exportString = JSON.stringify(exportData, null, 2)

    // 复制到剪贴板
    window.pywebview.api.copy_to_clipboard(exportString).then(() => {
      ElMessage.success('已复制到剪贴板')
    })
  } catch (error) {
    console.error('Export code blocks error:', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

// 导入代码块
const importCodeBlocks = async () => {
  try {
    if (!codeBlockImportDialog.value.content.trim()) {
      ElMessage.warning('请输入导入内容')
      return
    }

    const importedData = JSON.parse(codeBlockImportDialog.value.content)

    // 验证导入的数据格式
    if (!importedData.code_blocks || !Array.isArray(importedData.code_blocks)) {
      throw new Error('导入的内容格式不正确')
    }

    await ElMessageBox.confirm(
        `确定导入 ${importedData.code_blocks.length} 个代码块吗？`,
        '导入确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const currentBlocks = settings.value.code_blocks || []
    const existingIds = new Set(currentBlocks.map(b => b.id))

    // 检查重复项
    const duplicates = []
    const newBlocks = []

    importedData.code_blocks.forEach(importBlock => {
      if (existingIds.has(importBlock.id)) {
        duplicates.push(importBlock)
      } else {
        newBlocks.push(importBlock)
      }
    })

    if (newBlocks.length > 0) {
      // 更新设置
      settings.value.code_blocks = [...currentBlocks, ...newBlocks]
      await saveSettings()
    }

    // 显示导入结果
    let message = []
    if (newBlocks.length > 0) {
      message.push(`成功导入 ${newBlocks.length} 个新代码块`)
    }
    if (duplicates.length > 0) {
      message.push(`已过滤 ${duplicates.length} 个重复代码块`)
    }
    ElMessage.success(message.join('，'))

    codeBlockImportDialog.value.visible = false
    codeBlockImportDialog.value.content = ''
  } catch (error) {
    console.error('Import code blocks error:', error)
    ElMessage.error('导入失败：' + error.message)
  }
}

// AI角色导入对话框
const aiRoleImportDialog = ref({
  visible: false,
  content: ''
})

// 代码块导入对话框
const codeBlockImportDialog = ref({
  visible: false,
  content: ''
})

// AI角色对话框状态
const aiRoleDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    id: '',
    name: '',
    description: '',
    prompt: '',
    enabled: true
  },
  rules: {
    name: [
      { required: true, message: '请输入角色名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '请输入角色描述', trigger: 'blur' },
      { max: 200, message: '不能超过 200 个字符', trigger: 'blur' }
    ],
    prompt: [
      { required: true, message: '请输入提示词', trigger: 'blur' }
    ]
  }
})

// 添加AI角色
const addAIRole = () => {
  aiRoleDialog.value.isEdit = false
  aiRoleDialog.value.form = {
    id: '',
    name: '',
    description: '',
    prompt: '',
    enabled: true
  }
  aiRoleDialog.value.visible = true
}

// 编辑AI角色
const editAIRole = (role) => {
  aiRoleDialog.value.isEdit = true
  aiRoleDialog.value.form = { ...role }
  aiRoleDialog.value.visible = true
}

// 保存AI角色
const aiRoleFormRef = ref(null)
const saveAIRole = async () => {
  if (!aiRoleFormRef.value) return

  await aiRoleFormRef.value.validate(async (valid) => {
    if (valid) {
      const currentRoles = settings.value.ai_roles || []

      if (aiRoleDialog.value.isEdit) {
        // 编辑现有角色
        const index = currentRoles.findIndex(r => r.id === aiRoleDialog.value.form.id)
        if (index !== -1) {
          currentRoles[index] = { ...aiRoleDialog.value.form }
        }
      } else {
        // 添加新角色
        const newRole = {
          ...aiRoleDialog.value.form,
          id: `role_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }
        currentRoles.push(newRole)
      }

      // 更新设置
      settings.value.ai_roles = currentRoles
      await saveSettings()

      aiRoleDialog.value.visible = false
      ElMessage.success(aiRoleDialog.value.isEdit ? '角色更新成功' : '角色添加成功')
    }
  })
}

// 删除AI角色
const deleteAIRole = async (role) => {
  try {
    await ElMessageBox.confirm(
        '确定要删除这个AI角色吗？此操作不可恢复。',
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const currentRoles = settings.value.ai_roles || []
    const index = currentRoles.findIndex(r => r.id === role.id)
    if (index !== -1) {
      currentRoles.splice(index, 1)
      settings.value.ai_roles = currentRoles
      await saveSettings()
      ElMessage.success('角色删除成功')
    }
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error('删除失败：' + e.message)
    }
  }
}

// 处理AI角色状态变化
const handleAIRoleStatusChange = async (role) => {
  try {
    const currentRoles = settings.value.ai_roles || []
    const index = currentRoles.findIndex(r => r.id === role.id)
    if (index !== -1) {
      currentRoles[index].enabled = role.enabled
      settings.value.ai_roles = currentRoles
      await saveSettings()
      ElMessage.success(`角色已${role.enabled ? '启用' : '禁用'}`)
    }
  } catch (e) {
    ElMessage.error('状态更新失败：' + e.message)
    // 回滚状态
    role.enabled = !role.enabled
  }
}

// 处理默认目录变更
const handleDefaultChange = (currentDir, newValue) => {
  if (newValue) {
    // 将其他目录的默认状态设为false
    settings.value.chrome.userDataDirs.forEach(dir => {
      if (dir.id !== currentDir.id) {
        dir.isDefault = false
      }
    })
  } else {
    // 如果当前是唯一的默认目录，不允许取消
    const hasOtherDefault = settings.value.chrome.userDataDirs.some(dir =>
        dir.id !== currentDir.id && dir.isDefault
    )
    if (!hasOtherDefault) {
      currentDir.isDefault = true
      ElMessage.warning('必须保持一个默认的用户数据目录')
      return
    }
  }
  saveSettings()
}

// 组件挂载时加载设置
const activeTab = ref('chrome')
const route = useRoute()

onMounted(async () => {
  await loadSettings()
})
</script>

<style lang="scss" scoped>
.app-settings {
  padding: 20px;

  :deep(.el-tabs__content) {
    padding: 20px;
  }

  .settings-panel {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;

      .section-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 10px;
        margin-left: auto;

        .el-button {
          display: flex;
          align-items: center;
          gap: 5px;

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }

    // Chrome 设置特有样式
    &.chrome-settings {
      .path-input-group {
        display: flex;
        gap: 10px;
        align-items: center;

        .el-input {
          flex: 1;
        }

        .el-button-group {
          flex-shrink: 0;
          display: flex;

          .el-button {
            display: flex;
            align-items: center;
            gap: 5px;

            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .el-form {
        margin-bottom: 30px;
      }
    }

    .detail-preview {
      padding: 15px;
      background: #f8f9fa;

      .code-content {
        margin: 0;
        padding: 15px;
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        color: #606266;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
        font-size: 14px;
        line-height: 1.6;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 300px;
        overflow-y: auto;
      }
    }

    :deep(.el-table) {
      margin-top: 10px;

      .el-button-group {
        .el-button {
          margin: 0;

          &:not(:first-child) {
            margin-left: -1px;
          }
        }
      }
    }
  }
}
</style>
