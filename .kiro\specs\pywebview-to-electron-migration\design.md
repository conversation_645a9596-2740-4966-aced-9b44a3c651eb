# PVV小说创作软件 - Electron迁移设计文档

## 概述

本设计文档详细描述了将PVV小说创作软件从 pywebview + Python 架构迁移到 Electron 架构的技术方案。设计目标是保持前端界面的完整复用，重新构建后端服务层，实现高效的 IPC 通信机制。

## 架构设计

### 整体架构对比

#### 原架构 (pywebview)
```
┌─────────────────┐    ┌──────────────────┐
│   Vue3 Frontend │◄──►│  Python Backend  │
│   (Renderer)    │    │   (pywebview)    │
└─────────────────┘    └──────────────────┘
        │                       │
        ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│ window.pywebview│    │   API Controllers│
│     .api        │    │   - BookController│
└─────────────────┘    │   - ModelController│
                       │   - ConfigManager │
                       └──────────────────┘
```

#### 新架构 (Electron)
```
┌─────────────────┐    ┌──────────────────┐    ┌──────────────────┐
│   Vue3 Frontend │◄──►│  Electron Main   │◄──►│  Service Layer   │
│  (Renderer)     │    │    Process       │    │  (Node.js)       │
└─────────────────┘    └──────────────────┘    └──────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌──────────────────┐
│window.electronAPI│    │   IPC Handlers   │    │   API Services   │
│window.pywebview │    │   - api-call     │    │   - BookService  │
│     (compat)    │    │   - file-dialog  │    │   - ModelService │
└─────────────────┘    └──────────────────┘    │   - ConfigService│
                                               └──────────────────┘
```

### 核心组件设计

#### 1. 主进程 (Main Process)
**职责：** 应用生命周期管理、窗口管理、IPC通信协调

**核心模块：**
- `main.js` - 应用入口和窗口管理
- `ipcHandlers.js` - IPC事件处理器
- `menuManager.js` - 应用菜单管理
- `updateManager.js` - 自动更新管理

#### 2. 预加载脚本 (Preload Script)
**职责：** 安全的API桥接，兼容性适配

**核心功能：**
- 暴露 `electronAPI` 接口
- 提供 `pywebview` 兼容层
- 实现安全的上下文隔离

#### 3. 服务层 (Service Layer)
**职责：** 业务逻辑处理，数据管理

**服务模块：**
- `BookService` - 书籍和章节管理
- `ModelService` - AI模型和对话管理
- `ConfigService` - 配置管理
- `FileService` - 文件系统操作
- `ProjectService` - 项目管理
- `BackupService` - 备份和恢复

## 通信机制设计

### IPC通信流程

```mermaid
sequenceDiagram
    participant R as Renderer Process
    participant P as Preload Script
    participant M as Main Process
    participant S as Service Layer

    R->>P: window.electronAPI.invoke('method', args)
    P->>M: ipcRenderer.invoke('api-call', method, args)
    M->>S: serviceInstance[method](...args)
    S->>M: return result
    M->>P: return result
    P->>R: return result
```

### API调用适配

#### 原调用方式
```javascript
// 原 pywebview 调用
const response = await window.pywebview.api.model_controller.get_all_chats()
const result = typeof response === 'string' ? JSON.parse(response) : response
```

#### 新调用方式
```javascript
// Electron 调用 (兼容层)
const response = await window.pywebview.api.model_controller.get_all_chats()
// 内部实际调用: window.electronAPI.invoke('model_controller.get_all_chats')

// 或直接使用新API
const result = await window.electronAPI.invoke('get_all_chats')
```

### 数据格式标准化

#### 统一响应格式
```typescript
interface ApiResponse<T = any> {
  status: 'success' | 'error'
  message: string
  data?: T
  error?: string
}
```

#### 错误处理机制
```javascript
// 统一错误处理
try {
  const result = await window.electronAPI.invoke('some_method', params)
  if (result.status === 'error') {
    throw new Error(result.message)
  }
  return result.data
} catch (error) {
  console.error('API调用失败:', error)
  ElMessage.error(error.message)
}
```

## 数据存储设计

### 存储架构

```
用户数据目录/
├── config/
│   ├── app-settings.json      # 应用配置
│   ├── ai-providers.json      # AI提供商配置
│   └── user-preferences.json  # 用户偏好设置
├── projects/
│   └── [project-id]/
│       ├── project.json       # 项目元数据
│       ├── books/
│       │   └── [book-id]/
│       │       ├── book.json  # 书籍信息
│       │       └── chapters/  # 章节内容
│       └── assets/            # 项目资源
├── backups/                   # 备份文件
├── cache/                     # 缓存数据
└── logs/                      # 日志文件
```

### 数据管理策略

#### 1. 配置管理
- 使用 `electron-store` 进行配置持久化
- 支持配置的实时同步和验证
- 提供配置迁移和版本兼容

#### 2. 项目数据
- JSON文件存储，便于版本控制和备份
- 支持增量保存和自动备份
- 实现数据完整性检查

#### 3. 缓存策略
- 智能缓存常用数据
- 支持缓存清理和更新
- 内存使用优化

## 服务层详细设计

### BookService - 书籍管理服务

```javascript
class BookService {
  constructor(userDataPath) {
    this.projectsPath = path.join(userDataPath, 'projects')
    this.cache = new Map()
  }

  // 获取所有项目
  async getProjects() {
    // 实现项目列表获取
  }

  // 创建新书籍
  async createBook(projectId, bookData) {
    // 实现书籍创建逻辑
  }

  // 获取书籍章节
  async getChapters(bookId) {
    // 实现章节获取逻辑
  }

  // 保存章节内容
  async saveChapter(chapterId, content) {
    // 实现章节保存逻辑
  }
}
```

### ModelService - AI模型服务

```javascript
class ModelService {
  constructor(configService) {
    this.configService = configService
    this.chatHistory = new Map()
  }

  // 获取AI角色列表
  async getAiRoles() {
    // 实现AI角色管理
  }

  // 发送聊天消息
  async sendMessage(chatId, message, config) {
    // 实现AI对话逻辑
  }

  // 管理对话历史
  async getChatHistory(chatId) {
    // 实现对话历史管理
  }
}
```

### FileService - 文件服务

```javascript
class FileService {
  // 文件选择对话框
  async showOpenDialog(options) {
    return dialog.showOpenDialog(mainWindow, options)
  }

  // 目录选择对话框
  async showDirectoryDialog() {
    return dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory']
    })
  }

  // 文件读写操作
  async readFile(filePath) {
    // 实现安全的文件读取
  }

  async writeFile(filePath, content) {
    // 实现安全的文件写入
  }
}
```

## 前端适配设计

### 兼容性适配层

#### Preload脚本兼容层
```javascript
// preload.js 中的兼容性实现
contextBridge.exposeInMainWorld('pywebview', {
  api: {
    // 直接映射的方法
    get_settings: () => window.electronAPI.invoke('get_settings'),
    save_settings: (settings) => window.electronAPI.invoke('save_settings', settings),
    
    // 控制器命名空间映射
    model_controller: {
      get_all_chats: () => window.electronAPI.invoke('model_controller.get_all_chats'),
      save_chat: (id, chat) => window.electronAPI.invoke('model_controller.save_chat', id, chat),
      delete_chat: (id) => window.electronAPI.invoke('model_controller.delete_chat', id)
    },
    
    book_controller: {
      get_books: (projectId) => window.electronAPI.invoke('book_controller.get_books', projectId),
      create_book: (projectId, bookData) => window.electronAPI.invoke('book_controller.create_book', projectId, bookData)
    }
  }
})
```

### 组件迁移策略

#### 1. 无需修改的组件
- 纯展示组件
- 不涉及API调用的工具组件
- 静态配置组件

#### 2. 需要适配的组件
- 包含 `window.pywebview.api` 调用的组件
- 文件操作相关组件
- 系统交互组件

#### 3. 迁移步骤
1. 识别API调用点
2. 替换为兼容层调用
3. 测试功能完整性
4. 优化性能和用户体验

## 构建和部署设计

### 开发环境配置

```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"",
    "dev:vite": "vite --port 13001",
    "dev:electron": "cross-env NODE_ENV=development electron src/main/main.js",
    "build": "npm run build:vite && npm run build:electron",
    "build:vite": "vite build",
    "build:electron": "electron-builder"
  }
}
```

### 打包配置

```javascript
// electron-builder 配置
{
  "appId": "com.pvv.novel-creator",
  "productName": "PVV小说创作软件",
  "directories": {
    "output": "dist",
    "buildResources": "build"
  },
  "files": [
    "src/main/**/*",
    "src/services/**/*",
    "src/preload/**/*",
    "dist/**/*"
  ],
  "win": {
    "target": ["nsis", "portable"],
    "icon": "build/icon.ico"
  },
  "mac": {
    "target": ["dmg", "zip"],
    "icon": "build/icon.icns"
  },
  "linux": {
    "target": ["AppImage", "deb", "rpm"],
    "icon": "build/icon.png"
  }
}
```

## 性能优化设计

### 启动优化
- 延迟加载非关键模块
- 预加载常用数据
- 优化窗口创建流程

### 内存管理
- 实现智能缓存策略
- 及时释放不用的资源
- 监控内存使用情况

### IPC通信优化
- 批量处理API调用
- 实现请求去重
- 优化数据传输格式

## 错误处理和日志

### 错误处理策略
```javascript
// 全局错误处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error)
  // 优雅关闭应用
})

// IPC错误处理
ipcMain.handle('api-call', async (event, method, ...args) => {
  try {
    const result = await apiService[method](...args)
    return { status: 'success', data: result }
  } catch (error) {
    logger.error(`API调用失败 [${method}]:`, error)
    return { status: 'error', message: error.message }
  }
})
```

### 日志系统
- 分级日志记录
- 日志文件轮转
- 性能监控日志
- 用户操作日志

## 测试策略

### 单元测试
- 服务层逻辑测试
- 工具函数测试
- 数据处理测试

### 集成测试
- IPC通信测试
- 文件操作测试
- 配置管理测试

### 端到端测试
- 用户流程测试
- 跨平台兼容性测试
- 性能基准测试

## 迁移计划

### 阶段1：基础架构搭建
- 搭建Electron项目结构
- 实现基础IPC通信
- 创建服务层框架

### 阶段2：核心功能迁移
- 迁移配置管理
- 迁移文件操作
- 迁移数据存储

### 阶段3：业务功能迁移
- 迁移书籍管理功能
- 迁移AI对话功能
- 迁移工具箱功能

### 阶段4：优化和测试
- 性能优化
- 兼容性测试
- 用户体验优化

### 阶段5：部署和发布
- 构建配置优化
- 自动更新实现
- 发布流程建立leService extends BaseService {
  // 文件对话框
  async showOpenDialog(options) {}
  async showSaveDialog(options) {}
  
  // 文件操作
  async readFile(filePath) {}
  async writeFile(filePath, content) {}
  async deleteFile(filePath) {}
  async copyFile(srcPath, destPath) {}
  
  // 目录操作
  async createDirectory(dirPath) {}
  async listDirectory(dirPath) {}
  async openDirectory(dirPath) {}
}
```

#### 2.5 配置服务 (ConfigService)
```javascript
// 负责应用配置管理（基于数据库）
class ConfigService extends BaseService {
  constructor(userDataPath, dbService) {
    super(userDataPath);
    this.db = dbService;
  }
  
  // 获取应用设置
  async getSetting(key) {
    const result = await this.db.get(
      'SELECT value, type FROM app_settings WHERE key = ?', 
      [key]
    );
    if (!result) return null;
    
    // 根据类型转换值
    switch (result.type) {
      case 'number': return Number(result.value);
      case 'boolean': return result.value === 'true';
      case 'json': return JSON.parse(result.value);
      default: return result.value;
    }
  }
  
  // 设置应用设置
  async setSetting(key, value, type = 'string') {
    const stringValue = type === 'json' ? JSON.stringify(value) : String(value);
    await this.db.run(`
      INSERT OR REPLACE INTO app_settings (key, value, type, updated_at) 
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
    `, [key, stringValue, type]);
  }
  
  // 获取用户偏好
  async getUserPreference(category, key) {
    const result = await this.db.get(
      'SELECT value FROM user_preferences WHERE category = ? AND key = ?',
      [category, key]
    );
    return result ? result.value : null;
  }
  
  // 设置用户偏好
  async setUserPreference(category, key, value) {
    await this.db.run(`
      INSERT OR REPLACE INTO user_preferences (category, key, value, updated_at)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
    `, [category, key, String(value)]);
  }
  
  // 获取所有配置
  async getAllSettings() {
    const settings = await this.db.all('SELECT key, value, type FROM app_settings');
    const result = {};
    
    settings.forEach(setting => {
      switch (setting.type) {
        case 'number': 
          result[setting.key] = Number(setting.value);
          break;
        case 'boolean': 
          result[setting.key] = setting.value === 'true';
          break;
        case 'json': 
          result[setting.key] = JSON.parse(setting.value);
          break;
        default: 
          result[setting.key] = setting.value;
      }
    });
    
    return result;
  }
}
```

### 3. 数据存储设计

#### 3.1 SQLite 数据库结构（全面数据库化设计）

```sql
-- 项目表
CREATE TABLE projects (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 书籍表
CREATE TABLE books (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  project_id INTEGER NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  cover_image TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- 章节表
CREATE TABLE chapters (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  book_id INTEGER NOT NULL,
  title TEXT NOT NULL,
  content TEXT,
  order_index INTEGER DEFAULT 0,
  word_count INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (book_id) REFERENCES books(id)
);

-- 角色表
CREATE TABLE characters (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  book_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  avatar TEXT,
  age INTEGER,
  gender TEXT,
  occupation TEXT,
  personality TEXT,
  background TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (book_id) REFERENCES books(id)
);

-- 角色关系表
CREATE TABLE character_relationships (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  character_id INTEGER NOT NULL,
  related_character_id INTEGER NOT NULL,
  relationship_type TEXT NOT NULL, -- 朋友、敌人、恋人、亲属等
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (character_id) REFERENCES characters(id),
  FOREIGN KEY (related_character_id) REFERENCES characters(id)
);

-- 大纲表
CREATE TABLE outlines (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  book_id INTEGER NOT NULL,
  title TEXT NOT NULL,
  content TEXT,
  order_index INTEGER DEFAULT 0,
  parent_id INTEGER, -- 支持层级结构
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (book_id) REFERENCES books(id),
  FOREIGN KEY (parent_id) REFERENCES outlines(id)
);

-- 场景卡表
CREATE TABLE scene_cards (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  book_id INTEGER NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  location TEXT,
  time_period TEXT,
  characters TEXT, -- 参与角色ID列表，逗号分隔
  mood TEXT,
  purpose TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (book_id) REFERENCES books(id)
);

-- AI角色表
CREATE TABLE ai_roles (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  system_prompt TEXT,
  model_provider TEXT, -- openai, claude, gemini等
  model_name TEXT,
  temperature REAL DEFAULT 0.7,
  max_tokens INTEGER DEFAULT 2000,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- AI对话历史表
CREATE TABLE ai_conversations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  ai_role_id INTEGER NOT NULL,
  title TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (ai_role_id) REFERENCES ai_roles(id)
);

-- AI消息表
CREATE TABLE ai_messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  conversation_id INTEGER NOT NULL,
  role TEXT NOT NULL, -- user, assistant, system
  content TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id)
);

-- AI提示词模板表
CREATE TABLE ai_prompts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT, -- 写作、角色、情节等分类
  template TEXT NOT NULL,
  variables TEXT, -- 模板变量，JSON格式
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 应用配置表
CREATE TABLE app_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  key TEXT NOT NULL UNIQUE,
  value TEXT NOT NULL,
  type TEXT DEFAULT 'string', -- string, number, boolean, json
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户偏好表
CREATE TABLE user_preferences (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  category TEXT NOT NULL, -- theme, editor, ai等
  key TEXT NOT NULL,
  value TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(category, key)
);

-- 备份记录表
CREATE TABLE backup_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  backup_path TEXT NOT NULL,
  backup_type TEXT NOT NULL, -- auto, manual
  file_size INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  description TEXT
);

-- 下载任务表
CREATE TABLE download_tasks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  url TEXT NOT NULL,
  title TEXT,
  type TEXT NOT NULL, -- novel, zhihu, other
  status TEXT DEFAULT 'pending', -- pending, downloading, completed, failed
  progress INTEGER DEFAULT 0,
  file_path TEXT,
  error_message TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 自定义卡池表
CREATE TABLE custom_pools (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT, -- story, character, plot等
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 卡池项目表
CREATE TABLE pool_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  pool_id INTEGER NOT NULL,
  content TEXT NOT NULL,
  weight INTEGER DEFAULT 1, -- 抽取权重
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (pool_id) REFERENCES custom_pools(id)
);

-- 词典缓存表（汉语词典）
CREATE TABLE dictionary_cache (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  word TEXT NOT NULL UNIQUE,
  definition TEXT NOT NULL,
  pronunciation TEXT,
  examples TEXT,
  cached_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2 文件系统结构（数据库优先设计）
```
用户数据目录/
├── database.sqlite          # 主数据库（存储所有结构化数据）
├── assets/                 # 资源文件
│   ├── covers/            # 书籍封面
│   ├── avatars/           # 角色头像
│   ├── backgrounds/       # 背景图片
│   └── attachments/       # 其他附件
├── exports/               # 导出文件
│   ├── books/            # 导出的书籍
│   ├── outlines/         # 导出的大纲
│   └── reports/          # 统计报告
├── backups/               # 备份文件
│   ├── auto/             # 自动备份
│   └── manual/           # 手动备份
├── downloads/             # 下载文件
│   ├── novels/           # 下载的小说
│   └── articles/         # 下载的文章
├── temp/                  # 临时文件
├── logs/                  # 日志文件
└── cache/                 # 缓存文件
    ├── ai_responses/     # AI响应缓存
    └── thumbnails/       # 缩略图缓存
```

**数据库优先原则：**
- 所有结构化数据存储在SQLite数据库中
- 文件系统仅用于存储二进制文件（图片、导出文件等）
- 配置信息存储在数据库的app_settings表中
- 用户偏好存储在数据库的user_preferences表中
- 不再使用JSON配置文件

### 4. IPC通信设计

#### 4.1 IPC处理器 (IPC Handlers)
```javascript
// 统一的IPC处理器
class IPCHandler {
  constructor(serviceManager) {
    this.serviceManager = serviceManager;
  }
  
  setupHandlers() {
    // 数据相关
    ipcMain.handle('data:createProject', this.handleCreateProject.bind(this));
    ipcMain.handle('data:getProjects', this.handleGetProjects.bind(this));
    
    // AI相关
    ipcMain.handle('ai:chat', this.handleAIChat.bind(this));
    ipcMain.handle('ai:getRoles', this.handleGetAIRoles.bind(this));
    
    // 文件相关
    ipcMain.handle('file:showOpenDialog', this.handleShowOpenDialog.bind(this));
    ipcMain.handle('file:readFile', this.handleReadFile.bind(this));
    
    // 配置相关
    ipcMain.handle('config:get', this.handleGetConfig.bind(this));
    ipcMain.handle('config:set', this.handleSetConfig.bind(this));
  }
  
  async handleCreateProject(event, projectData) {
    const dataService = this.serviceManager.getService('data');
    return await dataService.createProject(projectData);
  }
}
```

#### 4.2 Preload脚本设计
```javascript
// preload.js - 安全的API暴露
const { contextBridge, ipcRenderer } = require('electron');

// 现代化的Electron API
contextBridge.exposeInMainWorld('electronAPI', {
  // 数据操作
  data: {
    createProject: (projectData) => ipcRenderer.invoke('data:createProject', projectData),
    getProjects: () => ipcRenderer.invoke('data:getProjects'),
    // ... 其他数据操作
  },
  
  // AI操作
  ai: {
    chat: (messages, config) => ipcRenderer.invoke('ai:chat', messages, config),
    getRoles: () => ipcRenderer.invoke('ai:getRoles'),
    // ... 其他AI操作
  },
  
  // 文件操作
  file: {
    showOpenDialog: (options) => ipcRenderer.invoke('file:showOpenDialog', options),
    readFile: (filePath) => ipcRenderer.invoke('file:readFile', filePath),
    // ... 其他文件操作
  },
  
  // 配置操作
  config: {
    get: (key) => ipcRenderer.invoke('config:get', key),
    set: (key, value) => ipcRenderer.invoke('config:set', key, value),
    // ... 其他配置操作
  }
});

// 兼容层API - 模拟pywebview.api
contextBridge.exposeInMainWorld('pywebview', {
  api: {
    // 项目管理 - 映射到新的API
    get_projects: () => window.electronAPI.data.getProjects(),
    create_project: (projectData) => window.electronAPI.data.createProject(projectData),
    
    // 配置管理
    get_settings: () => window.electronAPI.config.get(),
    save_settings: (settings) => window.electronAPI.config.set('app', settings),
    
    // AI功能
    get_ai_roles: () => window.electronAPI.ai.getRoles(),
    chat_with_ai: (messages, config) => window.electronAPI.ai.chat(messages, config),
    
    // 文件操作
    select_directory: () => window.electronAPI.file.showOpenDialog({ 
      properties: ['openDirectory'] 
    }),
    read_file: (filePath) => window.electronAPI.file.readFile(filePath),
    
    // ... 其他兼容API
  }
});
```

## 渐进式迁移策略

### 第一阶段：首页仪表板迁移

#### 迁移步骤
1. **建立基础架构**
   - 创建Electron主进程框架
   - 实现基础的服务管理器
   - 设置IPC通信机制

2. **实现核心服务**
   - ConfigService：配置管理
   - DataService：基础数据操作
   - FileService：文件系统操作

3. **迁移首页组件**
   - 复制现有首页Vue组件
   - 适配新的API调用方式
   - 实现项目列表显示功能

4. **测试验证**
   - 功能测试：确保所有首页功能正常
   - 性能测试：对比原系统性能
   - 用户体验测试：确保操作流畅

### 后续阶段迁移计划

#### 第二阶段：书籍写作界面
- 实现章节管理功能
- 文本编辑器集成
- 自动保存机制

#### 第三阶段：AI对话界面
- AI服务集成
- 对话历史管理
- 多模型支持

#### 第四阶段：其他界面
- 按优先级逐个迁移
- 每个界面独立测试
- 渐进式功能完善

## 错误处理和日志

### 错误处理策略
```javascript
// 统一错误处理
class ErrorHandler {
  static handle(error, context) {
    // 记录错误日志
    logger.error(`Error in ${context}:`, error);
    
    // 返回用户友好的错误信息
    return {
      status: 'error',
      message: this.getUserFriendlyMessage(error),
      code: error.code || 'UNKNOWN_ERROR'
    };
  }
  
  static getUserFriendlyMessage(error) {
    // 将技术错误转换为用户可理解的消息
    const errorMap = {
      'ENOENT': '文件或目录不存在',
      'EACCES': '没有访问权限',
      'SQLITE_BUSY': '数据库正忙，请稍后重试'
    };
    
    return errorMap[error.code] || '操作失败，请重试';
  }
}
```

### 日志系统
```javascript
// 使用winston进行日志管理
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: path.join(userDataPath, 'logs', 'error.log'), 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: path.join(userDataPath, 'logs', 'combined.log') 
    })
  ]
});
```

## 性能优化

### 1. 数据库优化
- 使用索引优化查询性能
- 实现连接池管理
- 批量操作优化

### 2. 内存管理
- 及时释放不用的资源
- 使用流式处理大文件
- 实现缓存机制

### 3. IPC优化
- 减少不必要的IPC调用
- 批量传输数据
- 使用异步操作

## 安全考虑

### 1. Electron安全最佳实践
- 禁用Node.js集成在渲染进程
- 启用上下文隔离
- 验证所有IPC消息

### 2. 数据安全
- 敏感数据加密存储
- 安全的文件路径处理
- 输入验证和清理

## 测试策略

### 1. 单元测试
- 每个服务类的单元测试
- 使用Jest测试框架
- 模拟外部依赖

### 2. 集成测试
- IPC通信测试
- 数据库操作测试
- 文件系统操作测试

### 3. 端到端测试
- 使用Spectron进行E2E测试
- 模拟用户操作流程
- 跨平台兼容性测试

这个设计为渐进式迁移提供了坚实的基础，每个阶段都可以独立开发和测试，确保迁移过程的可控性和质量。