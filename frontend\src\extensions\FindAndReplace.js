import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'
import { Decoration, DecorationSet } from '@tiptap/pm/view'

export class FindAndReplaceState {
  constructor() {
    this.findText = ''
    this.replaceText = ''
    this.isActive = false
    this.isCaseSensitive = false
    this.currentIndex = 0
    this.totalMatches = 0
    this.decorationSet = DecorationSet.empty
    this.results = []
  }
}

// 创建用于处理查找匹配的辅助函数
function findTextMatches(doc, findText, isCaseSensitive, findClass) {
  const results = []
  const decorations = []
  
  if (!findText || findText.length === 0) {
    return { results, decorations, decorationSet: DecorationSet.empty }
  }
  
  // 转换查找文本，根据大小写敏感设置
  const searchTerm = isCaseSensitive ? findText : findText.toLowerCase()
  
  doc.descendants((node, pos) => {
    if (!node.isText) return
    
    const text = node.text || ''
    
    // 在非大小写敏感模式下，将节点文本转为小写进行比较
    // 在大小写敏感模式下，保持原始文本
    const nodeText = isCaseSensitive ? text : text.toLowerCase()
    
    let index = 0
    
    while ((index = nodeText.indexOf(searchTerm, index)) !== -1) {
      const from = pos + index
      const to = from + searchTerm.length
      
      // 保存匹配项，使用原始文本而不是小写版本
      results.push({
        from,
        to,
        text: text.slice(index, index + searchTerm.length)
      })
      
      decorations.push(
        Decoration.inline(from, to, {
          class: findClass
        })
      )
      
      index += searchTerm.length
    }
  })
  
  return {
    results,
    decorations,
    decorationSet: DecorationSet.create(doc, decorations)
  }
}

// 创建用于更新装饰的辅助函数
function updateFindDecorations(editor, results, currentIndex, findClass, findActiveClass) {
  if (!results || results.length === 0) {
    return DecorationSet.empty
  }
  
  const decorations = []
  
  results.forEach((result, index) => {
    const isActive = index === currentIndex
    
    decorations.push(
      Decoration.inline(result.from, result.to, {
        class: isActive 
          ? `${findClass} ${findActiveClass}` 
          : findClass
      })
    )
  })
  
  return DecorationSet.create(editor.state.doc, decorations)
}

export const FindAndReplace = Extension.create({
  name: 'findAndReplace',

  addOptions() {
    return {
      findClass: 'find-result',
      findActiveClass: 'find-result-active',
    }
  },

  addStorage() {
    return new FindAndReplaceState()
  },

  addCommands() {
    return {
      startFindAndReplace: () => ({ editor }) => {
        try {
          this.storage.isActive = true
          this.storage.findText = ''
          this.storage.replaceText = ''
          this.storage.currentIndex = 0
          this.storage.totalMatches = 0
          this.storage.results = []
          
          editor.view.dispatch(editor.state.tr)
        } catch (error) {
          console.error('Error in startFindAndReplace:', error)
        }
        
        return true
      },

      endFindAndReplace: () => ({ editor }) => {
        try {
          this.storage.isActive = false
          this.storage.findText = ''
          this.storage.decorationSet = DecorationSet.empty
          this.storage.results = []
          
          editor.view.dispatch(editor.state.tr)
        } catch (error) {
          console.error('Error in endFindAndReplace:', error)
        }
        
        return true
      },

      setFindTerm: (term) => ({ editor }) => {
        try {
          // 确保term是字符串
          const searchTerm = term || ''
          this.storage.findText = searchTerm
          
          // 使用独立函数而不是方法
          const result = findTextMatches(
            editor.state.doc, 
            searchTerm, 
            this.storage.isCaseSensitive,
            this.options.findClass
          )
          
          this.storage.results = result.results
          this.storage.totalMatches = result.results.length
          
          // 重置当前索引，如果有结果但索引无效
          if (this.storage.results.length > 0 && 
              (this.storage.currentIndex < 0 || 
               this.storage.currentIndex >= this.storage.results.length)) {
            this.storage.currentIndex = 0
          }
          
          // 更新装饰
          this.storage.decorationSet = updateFindDecorations(
            editor,
            this.storage.results,
            this.storage.currentIndex,
            this.options.findClass,
            this.options.findActiveClass
          )
          
          editor.view.dispatch(editor.state.tr)
        } catch (error) {
          console.error('Error in setFindTerm:', error)
        }
        
        return true
      },

      setReplaceTerm: (term) => ({ editor }) => {
        try {
          this.storage.replaceText = term || ''
        } catch (error) {
          console.error('Error in setReplaceTerm:', error)
        }
        
        return true
      },

      findNext: () => ({ editor }) => {
        try {
          if (this.storage.results.length === 0) {
            return false
          }
          
          this.storage.currentIndex = (this.storage.currentIndex + 1) % this.storage.results.length
          
          // 获取当前匹配项
          const currentMatch = this.storage.results[this.storage.currentIndex]
          if (!currentMatch) return false
          
          // 更新装饰
          this.storage.decorationSet = updateFindDecorations(
            editor,
            this.storage.results,
            this.storage.currentIndex,
            this.options.findClass,
            this.options.findActiveClass
          )
          
          // 创建一个事务
          const tr = editor.state.tr
          
          // 设置选择区域到当前匹配项
          tr.setSelection(
            editor.state.selection.constructor.near(
              editor.state.doc.resolve(currentMatch.from)
            )
          )
          
          // 设置scrollIntoView标志为true，这会让ProseMirror自动滚动到当前位置
          // 同时添加一个自定义元数据，指示这是查找操作
          tr.scrollIntoView()
            .setMeta('findOperation', true)
          
          // 执行事务
          editor.view.dispatch(tr)
          
          // 额外处理：确保内容在视图中央
          // 这是一个更直接的方法，查找元素后立即滚动调整
          setTimeout(() => {
            try {
              // 获取编辑器DOM和匹配元素
              const editorDOM = editor.view.dom
              const editorContainer = editorDOM.closest('.editor-content') || editorDOM.parentElement
              
              // 找到当前高亮的元素
              const activeElement = editorDOM.querySelector(`.${this.options.findActiveClass}`)
              
              if (activeElement && editorContainer) {
                // 计算位置
                const containerRect = editorContainer.getBoundingClientRect()
                const elementRect = activeElement.getBoundingClientRect()
                
                // 计算元素中心与容器中心的偏移
                const containerCenter = containerRect.top + containerRect.height / 2
                const elementCenter = elementRect.top + elementRect.height / 2
                const offset = elementCenter - containerCenter
                
                // 滚动调整
                editorContainer.scrollBy({
                  top: offset,
                  behavior: 'smooth'
                })
              }
            } catch (e) {
              console.error('额外滚动调整失败:', e)
            }
          }, 50)
          
          return true
        } catch (error) {
          console.error('Error in findNext:', error)
          return false
        }
      },

      findPrevious: () => ({ editor }) => {
        try {
          if (this.storage.results.length === 0) {
            return false
          }
          
          this.storage.currentIndex = (this.storage.currentIndex - 1 + this.storage.results.length) % this.storage.results.length
          
          // 获取当前匹配项
          const currentMatch = this.storage.results[this.storage.currentIndex]
          if (!currentMatch) return false
          
          // 更新装饰
          this.storage.decorationSet = updateFindDecorations(
            editor,
            this.storage.results,
            this.storage.currentIndex,
            this.options.findClass,
            this.options.findActiveClass
          )
          
          // 创建一个事务
          const tr = editor.state.tr
          
          // 设置选择区域到当前匹配项
          tr.setSelection(
            editor.state.selection.constructor.near(
              editor.state.doc.resolve(currentMatch.from)
            )
          )
          
          // 设置scrollIntoView标志为true，这会让ProseMirror自动滚动到当前位置
          // 同时添加一个自定义元数据，指示这是查找操作
          tr.scrollIntoView()
            .setMeta('findOperation', true)
          
          // 执行事务
          editor.view.dispatch(tr)
          
          // 额外处理：确保内容在视图中央
          // 这是一个更直接的方法，查找元素后立即滚动调整
          setTimeout(() => {
            try {
              // 获取编辑器DOM和匹配元素
              const editorDOM = editor.view.dom
              const editorContainer = editorDOM.closest('.editor-content') || editorDOM.parentElement
              
              // 找到当前高亮的元素
              const activeElement = editorDOM.querySelector(`.${this.options.findActiveClass}`)
              
              if (activeElement && editorContainer) {
                // 计算位置
                const containerRect = editorContainer.getBoundingClientRect()
                const elementRect = activeElement.getBoundingClientRect()
                
                // 计算元素中心与容器中心的偏移
                const containerCenter = containerRect.top + containerRect.height / 2
                const elementCenter = elementRect.top + elementRect.height / 2
                const offset = elementCenter - containerCenter
                
                // 滚动调整
                editorContainer.scrollBy({
                  top: offset,
                  behavior: 'smooth'
                })
              }
            } catch (e) {
              console.error('额外滚动调整失败:', e)
            }
          }, 50)
          
          return true
        } catch (error) {
          console.error('Error in findPrevious:', error)
          return false
        }
      },

      replaceCurrentMatch: () => ({ editor, chain }) => {
        try {
          if (this.storage.results.length === 0 || 
              this.storage.currentIndex < 0 || 
              this.storage.currentIndex >= this.storage.results.length) {
            return false
          }
          
          // 获取当前匹配项
          const match = this.storage.results[this.storage.currentIndex]
          if (!match) return false
          
          // 获取替换文本
          const replaceText = this.storage.replaceText || ''
          
          // 直接使用 chain 方法进行原子操作
          // 这会在单个事务中完成，避免状态不匹配
          return chain()
            .insertContentAt({ from: match.from, to: match.to }, replaceText)
            .run()
        } catch (error) {
          console.error('替换时发生错误:', error)
          return false
        }
      },

      replaceAllMatches: () => ({ editor, chain, commands }) => {
        try {
          if (this.storage.results.length === 0) {
            return 0
          }
          
          // 获取当前所有匹配项和替换文本
          const matches = [...this.storage.results].sort((a, b) => b.from - a.from) // 创建副本并按位置倒序排序
          const replaceText = this.storage.replaceText || ''
          
          // 对于大量替换，逐个执行可能更稳定
          let successCount = 0
          
          // 使用链式命令一次性执行所有替换
          let command = chain()
          
          // 从后向前添加替换操作，防止位置偏移
          for (const match of matches) {
            command = command.insertContentAt(
              { from: match.from, to: match.to }, 
              replaceText
            )
          }
          
          // 执行链式命令
          const success = command.run()
          
          return success ? matches.length : 0
        } catch (error) {
          console.error('批量替换时发生错误:', error)
          
          // 失败时尝试逐个替换
          console.log('尝试逐个替换...')
          let successCount = 0
          
          // 获取匹配项和替换文本的副本
          const matches = [...this.storage.results].sort((a, b) => b.from - a.from)
          const replaceText = this.storage.replaceText || ''
          
          // 逐个替换
          for (const match of matches) {
            try {
              const success = editor.chain()
                .insertContentAt({ from: match.from, to: match.to }, replaceText)
                .run()
              
              if (success) successCount++
            } catch (e) {
              console.error('单个替换失败:', e)
            }
          }
          
          return successCount
        }
      },

      toggleCaseSensitivity: () => ({ editor }) => {
        try {
          this.storage.isCaseSensitive = !this.storage.isCaseSensitive
          
          // 重新查找匹配项
          const result = findTextMatches(
            editor.state.doc,
            this.storage.findText,
            this.storage.isCaseSensitive,
            this.options.findClass
          )
          
          this.storage.results = result.results
          this.storage.totalMatches = result.results.length
          
          // 重置当前索引，如果有结果但索引无效
          if (this.storage.results.length > 0 && 
              (this.storage.currentIndex < 0 || 
               this.storage.currentIndex >= this.storage.results.length)) {
            this.storage.currentIndex = 0
          }
          
          // 更新装饰
          this.storage.decorationSet = updateFindDecorations(
            editor,
            this.storage.results,
            this.storage.currentIndex,
            this.options.findClass,
            this.options.findActiveClass
          )
          
          editor.view.dispatch(editor.state.tr)
        } catch (error) {
          console.error('Error in toggleCaseSensitivity:', error)
        }
        
        return true
      },

      updateFindResults: () => ({ editor, chain }) => {
        try {
          // 验证编辑器状态
          if (!editor || !editor.state || !editor.state.doc) {
            console.warn('编辑器状态无效，无法更新查找结果')
            return false
          }
          
          // 重新查找匹配项
          const result = findTextMatches(
            editor.state.doc,
            this.storage.findText,
            this.storage.isCaseSensitive,
            this.options.findClass
          )
          
          this.storage.results = result.results
          this.storage.totalMatches = result.results.length
          
          // 更新当前索引，保持在有效范围内
          if (this.storage.results.length > 0) {
            // 尽量保持当前位置，但确保在有效范围内
            this.storage.currentIndex = Math.min(
              Math.max(0, this.storage.currentIndex),
              this.storage.results.length - 1
            )
          } else {
            this.storage.currentIndex = -1
          }
          
          // 更新装饰
          this.storage.decorationSet = updateFindDecorations(
            editor,
            this.storage.results,
            this.storage.currentIndex,
            this.options.findClass,
            this.options.findActiveClass
          )
          
          // 使用链式命令触发视图更新，而不是直接dispatch
          chain().focus(false).run()
          
          return true
        } catch (error) {
          console.error('更新查找结果时出错:', error)
          return false
        }
      },
      
      // 添加重置方法
      resetFindState: () => ({ editor }) => {
        try {
          // 完全重置查找状态
          this.storage.findText = ''
          this.storage.replaceText = ''
          this.storage.results = []
          this.storage.totalMatches = 0
          this.storage.currentIndex = -1
          this.storage.decorationSet = DecorationSet.empty
          
          // 发送一个空事务以触发重绘
          const tr = editor.state.tr
          editor.view.dispatch(tr)
          
          return true
        } catch (error) {
          console.error('重置查找状态时出错:', error)
          return false
        }
      }
    }
  },

  addProseMirrorPlugins() {
    const extensionThis = this

    return [
      new Plugin({
        key: new PluginKey('findAndReplace'),
        state: {
          init() {
            return DecorationSet.empty
          },
          apply(tr, oldSet) {
            try {
              // 如果功能未激活，返回空装饰集
              if (!extensionThis.storage.isActive) {
                return DecorationSet.empty
              }
              
              // 始终使用存储中的装饰集，忽略事务映射
              return extensionThis.storage.decorationSet || DecorationSet.empty
            } catch (error) {
              console.error('装饰处理出错:', error)
              return DecorationSet.empty
            }
          }
        },
        props: {
          decorations(state) {
            try {
              return this.getState(state)
            } catch (error) {
              console.error('获取装饰时出错:', error)
              return DecorationSet.empty
            }
          }
        }
      })
    ]
  }
}) 