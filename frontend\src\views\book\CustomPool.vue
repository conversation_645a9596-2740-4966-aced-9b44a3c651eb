<template>
  <div class="custom-pool-container">
    <div class="app-header">
      <h2>自定义卡池</h2>
      <div class="header-actions">
        <el-button type="primary" @click="createPool" size="small">
          <el-icon><Plus /></el-icon> 新建卡池
        </el-button>
        <el-button type="warning" @click="importPoolData" size="small">
          <el-icon><Document /></el-icon> 导入卡池
        </el-button>
        
        <!-- 修改抽取按钮逻辑 -->
        <el-button 
          type="success" 
          @click="drawFromSelectedCards" 
          :disabled="!hasActivePoolsWithCards" 
          size="small"
          class="draw-button-top"
        >
          <el-icon><Pointer /></el-icon> 抽取
        </el-button>
        
        <!-- 添加复制按钮 -->
        <el-button 
          type="primary" 
          @click="copyDrawnResults" 
          :disabled="drawnCards.length === 0"
          size="small"
          plain
        >
          <el-icon><CopyDocument /></el-icon> 复制结果
        </el-button>
        
        <!-- 修改清空按钮始终显示但根据结果禁用 -->
        <el-button 
          @click="clearDrawnCards" 
          size="small"
          type="info"
          :disabled="drawnCards.length === 0"
        >
          <el-icon><Delete /></el-icon> 清空结果
        </el-button>
      </div>
    </div>

    <div class="app-content" v-loading="isLoading">
      <!-- 顶部：卡池容器区域 -->
      <div class="pools-container" ref="poolsContainer">
        <!-- 卡池列表 - 水平排列 -->
        <div class="pools-wrapper" ref="poolsWrapper" @wheel="handlePoolsWheelEvent">
          <div v-for="pool in pools" :key="pool.id" class="pool-column">
            <div class="pool-header" :class="{'pool-active': pool.active}">
              <!-- 左侧：卡池标题 -->
              <div class="pool-title-wrapper">
                <span class="pool-title" :title="pool.name">{{ pool.name }}</span>
              <span class="pool-count">{{ pool.cards?.length || 0 }}</span>
              </div>
              
              <!-- 右侧：功能区 -->
              <div class="pool-functions">
                <!-- 激活开关（改为图标） -->
                <el-tooltip content="激活/禁用卡池" placement="top">
                  <div class="pool-active-icon" :class="{'is-active': pool.active}" @click="togglePoolActive(pool.id, !pool.active)">
                    <el-icon><Select /></el-icon>
                  </div>
                </el-tooltip>
                
                <!-- 抽卡数量 -->
                <el-select 
                  v-model="pool.drawCount" 
                  size="small" 
                  class="draw-count-select"
                  @change="updatePoolDrawCount(pool.id, pool.drawCount)"
                  placeholder="抽取数量"
                >
                  <el-option 
                    v-for="n in Math.min(10, pool.cards?.length || 1)" 
                    :key="n" 
                    :label="n" 
                    :value="n" 
                  />
                </el-select>
                
                <!-- 导出卡池按钮 -->
                <el-tooltip content="导出卡池所有卡片" placement="top">
                  <el-button
                    type="success"
                    text
                    circle
                    size="small"
                    @click="exportPoolCards(pool)"
                    class="action-btn"
                    :disabled="!pool.cards || pool.cards.length === 0"
                  >
                    <el-icon><Download /></el-icon>
                  </el-button>
                </el-tooltip>

                <!-- 编辑按钮 -->
                <el-button type="primary" text circle size="small" @click="editPool(pool)" class="action-btn">
                  <el-icon><Edit /></el-icon>
                </el-button>

                <!-- 删除按钮已移除 -->
              </div>
            </div>
            
            <!-- 卡片列表 - 使用原生滚动 -->
            <div class="pool-items-wrapper">
              <div class="pool-items-container native-scroll">
              <template v-if="pool.cards && pool.cards.length > 0">
                <div 
                  v-for="card in pool.cards" 
                  :key="card.id" 
                  class="pool-item"
                  :class="{ 'item-selected': card.selected }"
                  @click="toggleCardSelection(pool.id, card)"
                >
                  <el-checkbox 
                    :model-value="card.selected"
                    @update:model-value="(val) => updateCardSelection(pool.id, card, val)"
                    @click.stop
                  ></el-checkbox>
                  <span class="item-title">{{ card.title }}</span>
                </div>
              </template>
              <el-empty v-else description="暂无内容" :image-size="50"></el-empty>
              </div>
            </div>
          </div>
          
          <!-- 空状态提示 -->
          <el-empty v-if="pools.length === 0" description="暂无卡池，点击右上角按钮创建"></el-empty>
        </div>
      </div>

      <!-- 下部分：抽卡结果区域 - 参考StoryInspiration风格 -->
      <div class="drawing-section tech-panel">
        <div class="drawing-header">
          <div class="section-title">
            <div class="tech-lines"></div>
            <!-- 添加旋转星星图标 -->
            <el-icon class="result-icon"><Star /></el-icon>
            <h3>抽卡结果</h3>
          </div>
          <div class="interaction-tips">
            <!-- 添加固定元素指示器 -->
            <div class="fixed-elements-indicator" v-if="hasFixedElements">
              <span class="fixed-count">已固定: {{ totalFixedElements }}个</span>
              <el-tooltip content="点击元素可固定/解除固定，固定的元素在随机时将被保留">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>

            <!-- 导出抽取结果按钮 -->
            <el-button
              v-if="drawnCards.length > 0"
              @click="exportDrawnResults"
              size="small"
              class="export-results-btn"
            >
              <div class="export-btn-content">
                <el-icon class="export-icon"><Download /></el-icon>
                <span class="export-text">导出结果</span>
                <span class="export-count">{{ drawnCards.length }}</span>
              </div>
            </el-button>

            <el-tooltip placement="top" effect="light">
              <template #content>
                <div style="text-align: left;">
                  <div><el-icon><Mouse /></el-icon> <b>点击</b>: 固定/解除固定元素</div>
                  <div><el-icon><DCaret /></el-icon> <b>双击</b>: 查看元素详情</div>
                </div>
              </template>
              <el-icon class="tips-icon" :size="20"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </div>

        <!-- 修改结果展示区域为水平滚动布局 -->
        <div class="result-scroll-container">
          <div class="result-container">
            <!-- 按卡池分组显示结果 -->
            <div v-for="(groupCards, poolId) in groupedDrawnCards" :key="poolId" class="result-group">
              <div class="result-group-header">
                <div class="group-icon" :style="getPoolIconStyle(poolId)">
                  {{ getPoolIcon(poolId) }}
                  </div>
                <div class="group-title">{{ getPoolName(poolId) }}</div>
                <div class="group-count">{{ groupCards.length }}个元素</div>
                    </div>
              
              <div class="result-items">
                <div
                  v-for="card in getVisibleCards(groupCards, poolId)"
                  :key="`${card.poolId}-${card.id}`"
                  class="result-item"
                  :class="{ 'fixed-element': fixedElements[`${card.poolId}-${card.id}`] }"
                  :style="getCardStyle(card.poolId, card.id)"
                  @click="(event) => toggleCardFixed(card, event)"
                >
                  <el-icon v-if="fixedElements[`${card.poolId}-${card.id}`]" class="fixed-icon"><Lock /></el-icon>
                  {{ card.title }}
                </div>

                <!-- 显示更多按钮 -->
                <div
                  v-if="groupCards.length > maxVisibleCards && !showAllCards.has(poolId)"
                  class="show-more-btn"
                  @click="toggleShowAllCards(poolId, groupCards.length)"
                >
                  <el-icon><ArrowDown /></el-icon>
                  显示全部 {{ groupCards.length }} 张卡片
                </div>

                <!-- 收起按钮 -->
                <div
                  v-if="groupCards.length > maxVisibleCards && showAllCards.has(poolId)"
                  class="show-less-btn"
                  @click="toggleShowAllCards(poolId, groupCards.length)"
                >
                  <el-icon><ArrowUp /></el-icon>
                  收起卡片
                </div>
              </div>
            </div>
            
            <!-- 空状态提示 -->
            <div v-if="Object.keys(groupedDrawnCards).length === 0" class="empty-result">
              <el-empty description="点击顶部「抽取」按钮开始抽卡" :image-size="100">
                <template #image>
                  <el-icon class="empty-icon"><Pointer /></el-icon>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片详情对话框 - 增强版 -->
    <el-dialog
      v-model="cardDetailVisible"
      width="600px"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      
      :center="true"
      :lock-scroll="true"
      :show-close="false"
      class="card-detail-dialog tech-card enhanced-dialog"
      destroy-on-close
      :before-close="handleDialogClose"
    >
      <template #header>
        <div class="detail-header">
          <div class="tech-lines"></div>
          <div class="detail-title">
            <span class="detail-category-badge">
              {{ getPoolName(currentDetailCard?.poolId) }}
            </span>
            <h3>{{ currentDetailCard?.title }}</h3>
          </div>
        </div>
      </template>
      
      <div 
        class="detail-content-wrapper" 
        v-if="currentDetailCard"
        @mouseenter="handleDetailMouseEnter"
        @mouseleave="handleDetailMouseLeave"
      >
        <el-scrollbar height="420px" class="detail-scrollbar">
          <div class="detail-content">
            <!-- 循环显示卡片的所有属性 -->
            <div 
              v-for="(value, key) in currentDetailCard.properties" 
              :key="key" 
              class="detail-section"
            >
              <h4 class="detail-section-title">

                {{ getDimensionName(currentDetailCard.poolId, key) }}
              </h4>
              
              <!-- 根据属性类型显示不同的内容 -->
              <div class="detail-property-value">
                <!-- 布尔值 -->
                <el-tag v-if="typeof value === 'boolean'" size="small" :type="value ? 'success' : 'info'">
                  {{ value ? '是' : '否' }}
                </el-tag>
                
                <!-- 数组 -->
                <ul v-else-if="Array.isArray(value)" class="detail-array-list">
                  <li v-for="(item, index) in value" :key="index">
                    <div class="item-bullet"></div>
                    <span>{{ item }}</span>
                  </li>
                </ul>
                
                <!-- 颜色 -->
                <div v-else-if="isColorValue(value)" class="detail-color">
                  <div class="color-preview" :style="{backgroundColor: value}"></div>
                  <span class="color-code">{{ value }}</span>
                </div>
                
                <!-- 默认文本显示 -->
                <p v-else class="detail-text">{{ value }}</p>
              </div>
            </div>
            
            <div class="tech-decoration">
              <svg viewBox="0 0 100 100" class="corner-decoration top-left">
                <path d="M0,0 L40,0 C30,0 20,0 20,20 L20,40 L0,40 Z" />
                <path d="M0,15 L15,15" />
                <path d="M15,0 L15,15" />
              </svg>
              <svg viewBox="0 0 100 100" class="corner-decoration top-right">
                <path d="M0,0 L40,0 C30,0 20,0 20,20 L20,40 L0,40 Z" />
                <path d="M0,15 L15,15" />
                <path d="M15,0 L15,15" />
              </svg>
              <svg viewBox="0 0 100 100" class="corner-decoration bottom-left">
                <path d="M0,0 L40,0 C30,0 20,0 20,20 L20,40 L0,40 Z" />
                <path d="M0,15 L15,15" />
                <path d="M15,0 L15,15" />
              </svg>
              <svg viewBox="0 0 100 100" class="corner-decoration bottom-right">
                <path d="M0,0 L40,0 C30,0 20,0 20,20 L20,40 L0,40 Z" />
                <path d="M0,15 L15,15" />
                <path d="M15,0 L15,15" />
              </svg>
            </div>
          </div>
          <!-- 添加自动滚动指示器 -->
          <div class="auto-scroll-indicator"></div>
        </el-scrollbar>
      </div>
      
      <template #footer>
        <div class="detail-footer">
          <div class="tech-pulse"></div>
          <div class="detail-actions">
            <el-switch
              v-model="autoScrollEnabled"
              active-text="自动滚动"
              inactive-text="手动浏览"
              class="auto-scroll-switch"
            />
            <el-button
              type="success"
              @click="exportCurrentCard"
              class="tech-button"
              round
              size="small">
              <el-icon><Download /></el-icon> 导出卡片
            </el-button>
            <el-button
              @click="cardDetailVisible = false"
              class="tech-button"
              round>
              关闭
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 卡池编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isNewPool ? '新建卡池' : '编辑卡池'"
      width="500px"
      :destroy-on-close="true"
      :lock-scroll="true"
      class="pool-edit-dialog"
      @open="lockBodyScroll"
      @closed="unlockBodyScroll"
    >
      <div class="dialog-header-line" :class="{ 'new-pool': isNewPool }"></div>
      
      <div class="pool-edit-container">
        <el-form :model="editingPool" label-position="top" class="edit-form">
          <div class="pool-basic-info">
            <el-form-item label="卡池名称" class="required-field">
              <el-input 
                v-model="editingPool.name" 
                placeholder="请输入卡池名称"
                maxlength="50"
                show-word-limit
                autofocus
              ></el-input>
          </el-form-item>
          <el-form-item label="卡池描述">
            <el-input 
              v-model="editingPool.description" 
                placeholder="请简要描述这个卡池的用途和内容"
              type="textarea"
                :rows="4"
                maxlength="200"
                show-word-limit
            ></el-input>
          </el-form-item>
          </div>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false" plain>取消</el-button>
          <el-button type="danger" @click="confirmDeletePool(editingPool)" class="delete-pool-btn">
            <el-icon><Delete /></el-icon> 删除卡池
          </el-button>
          <el-button type="primary" @click="saveEditingPool" :disabled="!editingPool?.name">
            保存
          </el-button>
              </div>
      </template>
    </el-dialog>

    <!-- 卡池实体管理对话框 -->
    <el-dialog
      v-model="entityManagerVisible"
      :title="`管理卡池: ${currentEditingPool?.name || ''}`"
      width="80%"
      :destroy-on-close="false"
      :lock-scroll="true"
      class="entity-manager-dialog"
      @open="lockBodyScroll"
      @closed="unlockBodyScroll"
    >
      <div class="manager-header-line"></div>
      
      <!-- 将对话框内容包装在一个具有滚动能力的容器中 -->
      <div class="manager-container dialog-content-wrapper">
        <!-- 工具栏 -->
        <div class="editor-toolbar">
          <el-tabs v-model="managerActiveTab" class="manager-tabs">
            <el-tab-pane label="卡片管理" name="cards">
              <template #label>
                <el-icon class="tab-icon"><Document /></el-icon> 卡片管理
              </template>
            </el-tab-pane>
            <el-tab-pane label="维度管理" name="dimensions">
              <template #label>
                <el-icon class="tab-icon"><SetUp /></el-icon> 维度管理
              </template>
            </el-tab-pane>
          </el-tabs>
          
          <div class="toolbar-actions">
            <el-button type="primary" @click="addNewCardOrDimension">
              <el-icon><Plus /></el-icon> {{ managerActiveTab === 'cards' ? '添加卡片' : '添加维度' }}
            </el-button>
            <el-button type="success" @click="savePoolEntities">
              <el-icon><Check /></el-icon> 保存修改
            </el-button>
            <el-button @click="exportPoolData">
              <el-icon><CopyDocument /></el-icon> 导出配置
            </el-button>
              </div>
            </div>
        
        <!-- 卡片管理部分 -->
        <div v-if="managerActiveTab === 'cards'" class="entity-editor">
          <el-table :data="editingCards" style="width: 100%" max-height="450px" border>
            <!-- 添加标题列 -->
            <el-table-column label="标题" min-width="150">
              <template #default="{ row }">
                <span class="card-title">{{ row.title }}</span>
              </template>
            </el-table-column>
            
            <!-- 动态生成维度列 -->
            <el-table-column 
              v-for="dim in editingDimensions" 
              :key="dim.id" 
              :label="dim.name" 
              :min-width="120"
            >
              <template #default="{ row }">
                <!-- 根据不同类型的属性显示不同的内容 -->
                <template v-if="row.properties && row.properties[dim.id] !== undefined">
                  <!-- 布尔值显示 -->
                  <el-tag v-if="dim.type === 'boolean'" size="small" :type="row.properties[dim.id] ? 'success' : 'info'">
                    {{ row.properties[dim.id] ? '是' : '否' }}
                  </el-tag>
                  
                  <!-- 数组显示 -->
                  <span v-else-if="dim.type === 'array' && Array.isArray(row.properties[dim.id])">
                    {{ row.properties[dim.id].join(', ') }}
                  </span>
                  
                  <!-- 颜色显示 -->
                  <div v-else-if="dim.type === 'color'" class="color-preview" :style="{backgroundColor: row.properties[dim.id]}"></div>
                  
                  <!-- 其他类型 -->
                  <span v-else class="property-value">{{ row.properties[dim.id] }}</span>
                </template>
                <span v-else class="empty-property">-</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ $index }">
                <el-button type="primary" @click="editCardProperties(editingCards[$index])" size="small" circle>
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button type="danger" @click="removeCard($index)" size="small" circle>
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <el-empty v-if="editingCards.length === 0" description="请添加卡片"></el-empty>
          </div>

        <!-- 维度管理部分 -->
        <div v-if="managerActiveTab === 'dimensions'" class="dimension-editor">
          <el-table :data="editingDimensions" style="width: 100%" max-height="450px" border>
            <el-table-column label="名称" width="180">
              <template #default="{ row }">
                <el-input v-model="row.name" placeholder="输入维度名称" size="default" />
              </template>
            </el-table-column>
            
            <el-table-column label="描述">
              <template #default="{ row }">
                <el-input v-model="row.description" type="textarea" placeholder="输入维度描述" :rows="2" size="default" />
              </template>
            </el-table-column>
            
            <el-table-column label="类型" width="150">
              <template #default="{ row }">
                <el-select v-model="row.type" placeholder="选择类型" size="default">
                  <el-option label="文本" value="text" />
                  <el-option label="数字" value="number" />
                  <el-option label="选项" value="select" />
                  <el-option label="数组" value="array" />
                  <el-option label="布尔" value="boolean" />
                  <el-option label="日期" value="date" />
                  <el-option label="颜色" value="color" />
                </el-select>
              </template>
            </el-table-column>
            
            <el-table-column label="选项值" width="180">
              <template #default="{ row }">
                <el-button 
                  size="default" 
                  :disabled="row.type !== 'select'"
                  @click="editDimensionOptions(row)">
                  编辑选项 ({{ row.options?.length || 0 }})
                </el-button>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="{ $index }">
                <el-button type="danger" @click="removeDimension($index)" size="small" circle>
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <el-empty v-if="editingDimensions.length === 0" description="请添加维度"></el-empty>
            </div>
      </div>
      

      
      <!-- 维度选项编辑弹窗 -->
      <el-dialog
        v-model="optionsDialogVisible"
        title="编辑选项"
        width="500px"
        append-to-body
        :lock-scroll="true"
        @open="lockBodyScroll"
        @closed="unlockBodyScroll"
      >
        <div class="options-editor">
          <div v-for="(option, index) in currentOptions" :key="index" class="option-item">
            <el-input v-model="currentOptions[index]" placeholder="输入选项值" size="default" />
            <el-button type="danger" @click="removeOption(index)" size="small" circle>
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
          <el-button type="primary" @click="addOption" size="default">
            <el-icon><Plus /></el-icon> 添加选项
          </el-button>
        </div>
        
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="optionsDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveOptions">确定</el-button>
                    </div>
                  </template>
      </el-dialog>
    </el-dialog>

    <!-- 卡片属性编辑对话框 - 简化版 -->
    <el-dialog
      v-model="propertiesDialogVisible"
      title="编辑卡片属性"
      width="600px"
      append-to-body
      :lock-scroll="true"
      @open="lockBodyScroll"
      @closed="unlockBodyScroll"
    >
      <div class="properties-editor dialog-content-wrapper">
        <!-- 添加标题编辑区域 -->
        <div class="card-title-section">
          <div class="section-header">
            <h4>卡片标题</h4>
          </div>
                    <el-input
            v-model="currentEditingCard.title" 
            placeholder="请输入卡片标题"
            maxlength="50"
            show-word-limit
          />
        </div>

        <!-- 原有的属性列表 -->
        <div class="properties-list">
          <div v-for="dim in editingDimensions" :key="dim.id" class="property-item">
            <div class="property-item-header">
              <div class="property-name">
                <span class="property-title">{{ dim.name }}</span>
                <span class="property-type-badge">{{ getDimensionTypeLabel(dim.type) }}</span>
                <el-tooltip v-if="dim.description" :content="dim.description">
                  <el-icon class="info-icon"><InfoFilled /></el-icon>
                </el-tooltip>
              </div>
              <!-- 移除删除按钮 -->
            </div>
            
            <div class="property-item-editor">
              <!-- 文本类型 -->
                    <el-input
                v-if="dim.type === 'text'"
                v-model="currentCardProperties[dim.id]" 
                type="textarea"
                :rows="3"
                placeholder="请输入文本"
              />
              
              <!-- 数字类型 -->
              <el-input-number 
                v-else-if="dim.type === 'number'"
                v-model="currentCardProperties[dim.id]" 
                :controls="true"
                style="width: 100%"
              />
              
              <!-- 选项类型 -->
              <el-select 
                v-else-if="dim.type === 'select'"
                v-model="currentCardProperties[dim.id]" 
                placeholder="请选择" 
                style="width: 100%">
                <el-option 
                  v-for="opt in dim.options || []" 
                  :key="opt" 
                  :label="opt" 
                  :value="opt" 
                />
              </el-select>
              
              <!-- 数组类型 - 保留添加/删除数组项功能 -->
              <div v-else-if="dim.type === 'array'" class="array-editor">
                <div v-for="(item, index) in currentCardProperties[dim.id] || []" :key="index" class="array-item">
                  <el-input v-model="currentCardProperties[dim.id][index]" placeholder="数组项" />
                  <el-button type="danger" @click="removeArrayItem(dim.id, index, 'edit')" size="small" circle>
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-button type="primary" @click="addArrayItem(dim.id, 'edit')" size="small">
                  <el-icon><Plus /></el-icon> 添加项目
                </el-button>
              </div>
              
              <!-- 布尔类型 -->
              <el-switch 
                v-else-if="dim.type === 'boolean'"
                v-model="currentCardProperties[dim.id]" 
                active-text="是" 
                inactive-text="否" 
              />
              
              <!-- 日期类型 -->
              <el-date-picker 
                v-else-if="dim.type === 'date'"
                v-model="currentCardProperties[dim.id]" 
                type="date" 
                placeholder="选择日期"
                style="width: 100%" 
              />
              
              <!-- 颜色类型 -->
              <el-color-picker 
                v-else-if="dim.type === 'color'"
                v-model="currentCardProperties[dim.id]" 
                show-alpha
              />
              
              <!-- 默认文本输入 -->
              <el-input 
                v-else
                v-model="currentCardProperties[dim.id]" 
                placeholder="请输入值"
              />
            </div>
          </div>
        </div>
        
        <!-- 没有属性时显示空状态 -->
        <el-empty v-if="editingDimensions.length === 0" description="暂无可编辑的属性"></el-empty>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="propertiesDialogVisible = false" plain>取消</el-button>
          <el-button type="primary" @click="saveCardProperties">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加卡片对话框 - 移除ID显示 -->
    <el-dialog
      v-model="addCardDialogVisible"
      title="添加新卡片"
      width="600px"
      :lock-scroll="true"
      :destroy-on-close="false"
      class="add-card-dialog"
      @open="lockBodyScroll"
      @closed="unlockBodyScroll"
    >
      <div class="add-card-header-line"></div>
      
      <div class="add-card-container dialog-content-wrapper">
        <!-- 添加卡片标题输入区域 -->
        <div class="card-title-section">
          <div class="section-header">
            <h4>卡片标题</h4>
          </div>
          <el-input
            v-model="newCard.title"
            placeholder="请输入卡片标题"
            maxlength="50"
            show-word-limit
          />
        </div>
        
        <!-- 卡片属性部分 -->
        <div class="card-properties-section">
          <div class="section-header">
            <h4>卡片属性</h4>
          </div>
          
          <el-empty v-if="editingDimensions.length === 0" description="请先添加维度"></el-empty>
          
          <!-- 移除el-scrollbar，使用对话框自身的滚动功能 -->
          <div class="property-form" v-if="editingDimensions.length > 0">
            <div v-for="dim in editingDimensions" :key="dim.id" class="property-input-group">
              <div class="property-label">
                <span>{{ dim.name }}</span>
                <el-tooltip v-if="dim.description" :content="dim.description">
                  <el-icon class="info-icon"><InfoFilled /></el-icon>
                </el-tooltip>
                <span class="property-type-badge">{{ getDimensionTypeLabel(dim.type) }}</span>
              </div>
              
              <!-- 根据维度类型显示不同的编辑控件 -->
              <div class="property-input">
                <!-- 文本类型 -->
                <el-input 
                  v-if="dim.type === 'text'"
                  v-model="newCard.properties[dim.id]" 
                  placeholder="请输入文本"
                  type="textarea"
                  :rows="2"
                />
                
                <!-- 数字类型 -->
                <el-input-number 
                  v-else-if="dim.type === 'number'"
                  v-model="newCard.properties[dim.id]" 
                  :controls="true"
                  :precision="2"
                  style="width: 100%"
                />
                
                <!-- 选项类型 -->
                <el-select 
                  v-else-if="dim.type === 'select'"
                  v-model="newCard.properties[dim.id]" 
                  placeholder="请选择" 
                  style="width: 100%">
                  <el-option 
                    v-for="opt in dim.options || []" 
                    :key="opt" 
                    :label="opt" 
                    :value="opt" 
                  />
                </el-select>
                
                <!-- 数组类型 -->
                <div v-else-if="dim.type === 'array'" class="array-editor">
                  <div v-for="(item, index) in newCard.properties[dim.id] || []" :key="index" class="array-item">
                    <el-input v-model="newCard.properties[dim.id][index]" placeholder="数组项" />
                    <el-button type="danger" @click="removeArrayItem(dim.id, index, 'new')" size="small" circle>
                      <el-icon><Delete /></el-icon>
                    </el-button>
                    </div>
                  <el-button type="primary" @click="addArrayItem(dim.id, 'new')" size="small">
                    <el-icon><Plus /></el-icon> 添加项目
                  </el-button>
                  </div>
                
                <!-- 布尔类型 -->
                <el-switch 
                  v-else-if="dim.type === 'boolean'"
                  v-model="newCard.properties[dim.id]" 
                  active-text="是" 
                  inactive-text="否" 
                />
                
                <!-- 日期类型 -->
                <el-date-picker 
                  v-else-if="dim.type === 'date'"
                  v-model="newCard.properties[dim.id]" 
                  type="date" 
                  placeholder="选择日期"
                  style="width: 100%" 
                />
                
                <!-- 颜色类型 -->
                <el-color-picker 
                  v-else-if="dim.type === 'color'"
                  v-model="newCard.properties[dim.id]" 
                  show-alpha
                />
                
                <!-- 默认文本输入 -->
                <el-input 
                  v-else
                  v-model="newCard.properties[dim.id]" 
                  placeholder="请输入值"
                />
                </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addCardDialogVisible = false" plain>取消</el-button>
          <el-button type="primary" @click="confirmAddCard">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入配置弹窗 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入卡池配置"
      width="600px"
      :lock-scroll="true"
      :destroy-on-close="false"
      @open="lockBodyScroll"
      @closed="unlockBodyScroll"
    >
      <div class="import-dialog-content">
        <p class="import-tip">请将卡池配置的JSON字符串粘贴到下方文本框中:</p>
        <el-input
          v-model="importJsonText"
          type="textarea"
          :rows="10"
          placeholder="粘贴JSON配置文本..."
          resize="none"
        ></el-input>

        <div v-if="importError" class="import-error">
          <el-alert
            :title="importError"
            type="error"
            show-icon
            :closable="false"
          ></el-alert>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false" plain>取消</el-button>
          <el-button type="primary" @click="processImportData" :loading="importLoading">
            导入
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 卡片导出对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      :title="exportDialogTitle"
      width="90%"
      :destroy-on-close="true"
      :lock-scroll="true"
      class="export-dialog"
      @open="lockBodyScroll"
      @closed="unlockBodyScroll"
    >
      <CustomPoolCardExporter
        v-if="exportDialogVisible"
        :cards="exportCards"
        :pool-name="exportPoolName"
        :pool-info="exportPoolInfo"
        :dimensions="exportDimensions"
        @export-success="handleExportSuccess"
        @close="exportDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, reactive, onUnmounted } from 'vue'
import { useCustomPoolStore } from '@/stores/customPool'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Plus, Edit, Delete, Pointer, Document, SetUp, Check, Download, Upload, CopyDocument, InfoFilled, Select, Mouse, Star, Lock, DCaret, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import CustomPoolCardExporter from '@/components/CustomPoolCardExporter.vue'

// 初始化 store
const customPoolStore = useCustomPoolStore()
const pools = computed(() => {
  return customPoolStore.pools.map(pool => {
    // 为每个池的卡片添加selected属性，确保drawCount属性存在
    const poolWithSelectedCards = {
      ...pool,
      drawCount: pool.drawCount || 1, // 默认抽取数量为1
      cards: pool.cards ? pool.cards.map(card => ({
        ...card,
        selected: false
      })) : []
    }
    return poolWithSelectedCards
  })
})
const isLoading = computed(() => customPoolStore.isLoading)

// 选中的卡片
const selectedCards = ref([])
const cardDetailVisible = ref(false)
const currentDetailCard = ref(null)
// 抽卡相关
const drawnCards = ref([])
const canDraw = computed(() => selectedCards.value.length > 0)

// 添加固定元素功能
const fixedElements = reactive({}) // 存储固定的元素
const hasFixedElements = computed(() => {
  // 检查是否有任何固定元素
  return Object.keys(fixedElements).length > 0
})
const totalFixedElements = computed(() => {
  // 计算固定元素的总数
  return Object.keys(fixedElements).length
})

// 添加点击计时器和状态变量
const clickTimer = ref(null);
const isDoubleClick = ref(false);

// 编辑相关
const editDialogVisible = ref(false)
const editingPool = ref(null)
const isNewPool = ref(false)

// 引用DOM元素
const poolsWrapper = ref(null)
const poolsContainer = ref(null)

// 卡池实体管理相关变量
const entityManagerVisible = ref(false)
const currentEditingPool = ref(null)
const managerActiveTab = ref('cards')
const editingCards = ref([])
const editingDimensions = ref([])
const optionsDialogVisible = ref(false)
const currentEditingDimension = ref(null)
const currentOptions = ref([])

// 卡片属性编辑相关
const propertiesDialogVisible = ref(false)
const currentEditingCard = ref(null)
const currentCardProperties = ref({})
const selectedDimensionId = ref('')

// 可用维度
const availableDimensionsForCard = computed(() => {
  if (!currentEditingCard.value) return []
  
  const existingDimIds = Object.keys(currentCardProperties.value)
  return editingDimensions.value.filter(dim => !existingDimIds.includes(dim.id))
})

// 设置自动滚动相关变量
const autoScrollEnabled = ref(true);
const autoScrollDirection = ref(1);
const autoScrollSpeed = ref(2.5); // 增加默认滚动速度
const scrollLastPosition = ref(null);
let autoScrollTimer = null;
let isUserHovering = false;
const isTransitioning = ref(false);

// 存储清理函数
let cleanupFunctions = []

// 性能优化相关
const cardStyleCache = new Map() // 缓存卡片样式
const maxVisibleCards = ref(50) // 每个组最多显示的卡片数量
const showAllCards = ref(new Set()) // 记录哪些组显示全部卡片

// 导出相关状态
const exportDialogVisible = ref(false)
const exportCards = ref([])
const exportPoolName = ref('')
const exportPoolInfo = ref({})
const exportDimensions = ref([])
const exportDialogTitle = computed(() => {
  return exportCards.value.length > 0 ?
    `导出卡片 - ${exportPoolName.value} (${exportCards.value.length}张)` :
    '导出卡片'
})

// 初始化数据
onMounted(() => {
  customPoolStore.init()

  // 等待DOM更新后初始化鼠标拖动功能
  nextTick(() => {
    const dragCleanup = initDragScroll()
    const resultDragCleanup = initResultDragScroll()
    const mutationCleanup = setupMutationObserver()

    // 收集清理函数
    if (dragCleanup) cleanupFunctions.push(dragCleanup)
    if (resultDragCleanup) cleanupFunctions.push(resultDragCleanup)
    if (mutationCleanup) cleanupFunctions.push(mutationCleanup)
  })

  // 添加事件监听辅助方法
  addNativeScrollListeners()

  // 为结果滚动容器添加滚轮事件监听
  const resultContainer = document.querySelector('.result-scroll-container');
  if (resultContainer) {
    resultContainer.addEventListener('wheel', handleResultWheelEvent, { passive: false });

    // 添加清理函数
    cleanupFunctions.push(() => {
      resultContainer.removeEventListener('wheel', handleResultWheelEvent);
    })
  }

  // 增强结果项的滚轮滚动体验
  enhanceResultItemsScroll();

  // 监听卡池数据变化，在数据加载完成后再次应用滚动增强
  watch(() => drawnCards.value.length, () => {
    nextTick(() => {
      enhanceResultItemsScroll();
    });
  });
})

// 导出抽取结果
function exportDrawnResults() {
  if (drawnCards.value.length === 0) {
    ElMessage.warning('没有抽取结果可供导出');
    return;
  }

  // 准备导出数据
  exportCards.value = drawnCards.value.map(card => ({
    title: card.title,
    properties: card.properties || {}
  }));

  exportPoolName.value = '抽取结果';
  exportPoolInfo.value = {
    description: `包含来自 ${Object.keys(groupedDrawnCards.value).length} 个卡池的抽取结果`,
    totalCards: drawnCards.value.length
  };

  // 合并所有相关卡池的维度信息
  const allDimensions = new Map();
  drawnCards.value.forEach(card => {
    const pool = pools.value.find(p => p.id === card.poolId);
    if (pool && pool.dimensions) {
      pool.dimensions.forEach(dim => {
        allDimensions.set(dim.id, dim);
      });
    }
  });
  exportDimensions.value = Array.from(allDimensions.values());

  exportDialogVisible.value = true;
}

// 导出单张卡片（从详情弹窗）
function exportCurrentCard() {
  if (!currentDetailCard.value) {
    ElMessage.warning('没有选中的卡片');
    return;
  }

  // 准备导出数据
  exportCards.value = [{
    title: currentDetailCard.value.title,
    properties: currentDetailCard.value.properties || {}
  }];

  exportPoolName.value = getPoolName(currentDetailCard.value.poolId);
  exportPoolInfo.value = {
    description: `来自卡池: ${exportPoolName.value}`,
    totalCards: 1
  };

  // 获取对应卡池的维度信息
  const pool = pools.value.find(p => p.id === currentDetailCard.value.poolId);
  exportDimensions.value = pool && pool.dimensions ? pool.dimensions : [];

  exportDialogVisible.value = true;
  cardDetailVisible.value = false; // 关闭详情弹窗
}

// 导出卡池所有卡片
function exportPoolCards(pool) {
  if (!pool.cards || pool.cards.length === 0) {
    ElMessage.warning('该卡池没有卡片可供导出');
    return;
  }

  // 准备导出数据
  exportCards.value = pool.cards.map(card => ({
    title: card.title,
    properties: card.properties || {}
  }));

  exportPoolName.value = pool.name;
  exportPoolInfo.value = {
    description: pool.description || '',
    totalCards: pool.cards.length,
    dimensions: pool.dimensions || []
  };

  // 传递卡池的维度信息
  exportDimensions.value = pool.dimensions || [];

  exportDialogVisible.value = true;
}

// 处理导出成功
function handleExportSuccess(result) {
  ElMessage.success(`成功导出 ${result.count} 张卡片`);
  exportDialogVisible.value = false;
}

// 获取可见的卡片（虚拟滚动优化）
function getVisibleCards(groupCards, poolId) {
  if (!groupCards || groupCards.length === 0) return [];

  // 如果卡片数量少于阈值，直接返回全部
  if (groupCards.length <= maxVisibleCards.value) {
    return groupCards;
  }

  // 如果该组设置为显示全部，返回全部
  if (showAllCards.value.has(poolId)) {
    return groupCards;
  }

  // 否则只返回前N张卡片
  return groupCards.slice(0, maxVisibleCards.value);
}

// 切换显示全部卡片
function toggleShowAllCards(poolId, totalCount) {
  if (showAllCards.value.has(poolId)) {
    showAllCards.value.delete(poolId);
  } else {
    showAllCards.value.add(poolId);
  }
}

// 清理样式缓存（在重新抽取时调用）
function clearStyleCache() {
  cardStyleCache.clear();
}

// 优化的拖拽滚动功能 - 更跟手的实现
function initDragScroll() {
  if (!poolsWrapper.value) return

  let isDown = false
  let startX = 0
  let scrollLeft = 0
  let animationId = null

  const wrapper = poolsWrapper.value

  // 优化的鼠标按下处理
  const handleMouseDown = (e) => {
    const clickedElement = e.target
    const isInteractiveElement =
      clickedElement.tagName === 'BUTTON' ||
      clickedElement.tagName === 'INPUT' ||
      clickedElement.closest('.el-button') ||
      clickedElement.closest('.el-checkbox') ||
      clickedElement.closest('.el-select') ||
      clickedElement.closest('.action-btn')

    if (!isInteractiveElement) {
      isDown = true
      wrapper.classList.add('grabbing')
      startX = e.pageX - wrapper.offsetLeft
      scrollLeft = wrapper.scrollLeft
      wrapper.style.scrollBehavior = 'auto' // 禁用平滑滚动以提升拖拽性能

      // 立即设置用户选择为none，防止拖拽时选中文本
      document.body.style.userSelect = 'none'
    }
  }

  const handleMouseUp = () => {
    if (isDown) {
      isDown = false
      wrapper.classList.remove('grabbing')
      wrapper.style.scrollBehavior = 'smooth' // 恢复平滑滚动

      // 恢复用户选择
      document.body.style.userSelect = ''

      if (animationId) {
        cancelAnimationFrame(animationId)
        animationId = null
      }
    }
  }

  const handleMouseMove = (e) => {
    if (!isDown) return

    e.preventDefault()

    // 直接更新滚动位置，确保跟手性
    const x = e.pageX - wrapper.offsetLeft
    const walk = (x - startX) * 1.2
    const newScrollLeft = scrollLeft - walk

    // 边界检查，防止滚动超出范围
    const maxScrollLeft = wrapper.scrollWidth - wrapper.clientWidth
    wrapper.scrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft))
  }

  // 添加事件监听器
  wrapper.addEventListener('mousedown', handleMouseDown)
  document.addEventListener('mouseup', handleMouseUp)
  document.addEventListener('mouseleave', handleMouseUp)
  document.addEventListener('mousemove', handleMouseMove)

  // 返回清理函数
  return () => {
    wrapper.removeEventListener('mousedown', handleMouseDown)
    document.removeEventListener('mouseup', handleMouseUp)
    document.removeEventListener('mouseleave', handleMouseUp)
    document.removeEventListener('mousemove', handleMouseMove)
    if (animationId) {
      cancelAnimationFrame(animationId)
    }
  }
}

// 修改水平滚动处理函数，更有针对性地处理滚动
function handleHorizontalScroll(event) {
  // 判断事件是否来自卡池内容滚动区域
  if (event.target.closest('.native-scroll')) {
    // 如果是来自卡池内容，阻止事件冒泡，避免触发水平滚动
    event.stopPropagation();
    return;
  }
  
  // 处理水平滚动
  const container = poolsWrapper.value;
  if (container) {
    event.preventDefault(); // 防止页面默认滚动
    container.scrollLeft += event.deltaY;
  }
}

// 切换卡片选择
function toggleCardSelection(poolId, card) {
  // 直接调用updateCardSelection来处理状态更新
  updateCardSelection(poolId, card, !card.selected);
}

// 添加新的方法来处理复选框状态更新
function updateCardSelection(poolId, card, value) {
  // 获取卡片在卡池中的引用
  const pool = pools.value.find(p => p.id === poolId);
  if (!pool || !pool.cards) return;
  
  const cardInPool = pool.cards.find(c => c.id === card.id);
  if (!cardInPool) return;
  
  // 更新选中状态
  cardInPool.selected = value;
  
  // 更新selectedCards数组和抽取结果
  if (value) {
    // 添加到selectedCards
    if (!selectedCards.value.some(sc => sc.id === card.id && sc.poolId === poolId)) {
      selectedCards.value.push({
        id: card.id,
        poolId: poolId,
        ...card
      });
    }
    
    // 添加到抽取结果
    if (!drawnCards.value.some(c => c.id === card.id && c.poolId === poolId)) {
      drawnCards.value.push({
        ...card,
        poolId: poolId
      });
    }
  } else {
    // 从selectedCards移除
    selectedCards.value = selectedCards.value.filter(sc => !(sc.id === card.id && sc.poolId === poolId));
    
    // 从抽取结果中移除（如果不是固定的卡片）
    const compositeKey = `${poolId}-${card.id}`;
    if (!fixedElements[compositeKey]) {
      drawnCards.value = drawnCards.value.filter(dc => !(dc.id === card.id && dc.poolId === poolId));
    }
  }
}

// 获取卡池名称
function getPoolName(poolId) {
  const pool = pools.value.find(p => p.id === poolId)
  return pool ? pool.name : '未知卡池'
}

// 清空抽卡结果
function clearDrawnCards() {
  if (drawnCards.value.length === 0) {
    return;
  }
  
  // 清除所有卡池中卡片的勾选状态，除了固定的卡片
  pools.value.forEach(pool => {
    if (pool.cards) {
      pool.cards.forEach(card => {
        const compositeKey = `${pool.id}-${card.id}`;
        if (!fixedElements[compositeKey]) {
          card.selected = false;
        }
      });
    }
  });
  
  // 清空已抽取卡片，但保留固定的卡片
  drawnCards.value = drawnCards.value.filter(card => {
    const compositeKey = `${card.poolId}-${card.id}`;
    return fixedElements[compositeKey];
  });
  
  // 重置selectedCards，只保留固定的卡片
  selectedCards.value = selectedCards.value.filter(card => {
    const compositeKey = `${card.poolId}-${card.id}`;
    return fixedElements[compositeKey];
  });
}

// 更新卡池抽取数量
function updatePoolDrawCount(poolId, count) {
  // 找到要更新的卡池
  const pool = pools.value.find(p => p.id === poolId)
  if (pool) {
    // 创建一个副本以避免直接修改原对象
    const updatedPool = { ...pool, drawCount: count }
    // 使用现有的updatePool方法更新卡池
    customPoolStore.updatePool(poolId, updatedPool)
  }
}

// 切换卡池激活状态
async function togglePoolActive(poolId, active) {
  try {
    // 确保使用正确的 store 实例
    const store = useCustomPoolStore()
    await store.updatePoolActive(poolId, active)
    ElMessage.success(`卡池${active ? '激活' : '停用'}成功`)
  } catch (error) {
    console.error('切换卡池激活状态失败:', error)
    ElMessage.error(`操作失败: ${error.message || '未知错误'}`)
    // 恢复UI状态
    const pool = pools.value.find(p => p.id === poolId)
    if (pool) {
      pool.active = !active
    }
  }
}


// 显示卡片详情
function showCardDetail(card, event) {
  // 阻止事件冒泡
  event?.stopPropagation();
  
  // 设置当前查看的卡片并显示详情对话框
  currentDetailCard.value = card;
  cardDetailVisible.value = true;
  
  // 设置滚动到顶部
  setTimeout(() => {
    const scrollEl = document.querySelector('.detail-scrollbar .el-scrollbar__wrap');
    if (scrollEl) {
      scrollEl.scrollTop = 0;
    }
    
    // 启动自动滚动
    startAutoScroll();
  }, 300);
}

// 优化自动滚动实现，解决回滚速度过快的问题
function startAutoScroll() {
  if (autoScrollTimer) {
    clearInterval(autoScrollTimer);
    autoScrollTimer = null;
  }
  
  const scrollEl = document.querySelector('.detail-scrollbar .el-scrollbar__wrap');
  if (!scrollEl) return;
  
  // 初始化滚动到顶部
  scrollEl.scrollTop = 0;
  
  // 重置滚动方向和状态
  autoScrollDirection.value = 1;
  
  // 创建速度变量，用于平滑过渡
  let currentSpeedFactor = 1.0;  // 当前速度因子
  let targetSpeedFactor = 1.0;   // 目标速度因子
  
  // 获取滚动容器
  const scrollContainer = document.querySelector('.detail-scrollbar');
  if (scrollContainer) {
    // 移除所有可能的类
    scrollContainer.classList.remove('user-hovering');
    scrollContainer.classList.remove('changing-direction');
    // 添加自动滚动类
    scrollContainer.classList.add('auto-scrolling');
    scrollContainer.classList.add('high-performance');
  }
  
  // 基本滚动速度 - 每秒像素数
  const BASE_SPEED = 120; 
  
  // 向下和向上滚动的速度比例
  const DOWN_SPEED_RATIO = 1.0;  // 向下滚动速度比例
  const UP_SPEED_RATIO = 0.65;   // 向上滚动速度比例（降低到65%）
  
  // 使用循环定时器
  autoScrollTimer = setInterval(() => {
    // 检查是否应该继续滚动
    if (!cardDetailVisible.value || !autoScrollEnabled.value || isUserHovering) {
      return;
    }
    
    // 获取当前滚动状态
    const { scrollTop, scrollHeight, clientHeight } = scrollEl;
    
    // 到达边界时改变方向
    if (scrollTop >= scrollHeight - clientHeight - 2 && autoScrollDirection.value > 0) {
      // 到达底部，改变方向
      autoScrollDirection.value = -1;
      targetSpeedFactor = UP_SPEED_RATIO;  // 设置向上滚动的目标速度
      scrollContainer?.classList.add('changing-direction');
      
      // 方向变化时添加平滑过渡
      scrollEl.style.scrollBehavior = 'smooth';
      
      // 一段时间后移除过渡效果
      setTimeout(() => {
        scrollContainer?.classList.remove('changing-direction');
        scrollEl.style.scrollBehavior = 'auto';
      }, 800);
      
    } else if (scrollTop <= 2 && autoScrollDirection.value < 0) {
      // 到达顶部，改变方向
      autoScrollDirection.value = 1;
      targetSpeedFactor = DOWN_SPEED_RATIO;  // 设置向下滚动的目标速度
      scrollContainer?.classList.add('changing-direction');
      
      // 方向变化时添加平滑过渡
      scrollEl.style.scrollBehavior = 'smooth';
      
      // 一段时间后移除过渡效果
      setTimeout(() => {
        scrollContainer?.classList.remove('changing-direction');
        scrollEl.style.scrollBehavior = 'auto';
      }, 800);
    }
    
    // 平滑过渡到目标速度（每次调整10%）
    if (Math.abs(currentSpeedFactor - targetSpeedFactor) > 0.01) {
      currentSpeedFactor += (targetSpeedFactor - currentSpeedFactor) * 0.1;
    } else {
      currentSpeedFactor = targetSpeedFactor;
    }
    
    // 应用滚动 - 使用当前速度因子
    const adjustedSpeed = BASE_SPEED * currentSpeedFactor;
    
    // 应用滚动
    scrollEl.scrollTop += autoScrollDirection.value * (adjustedSpeed / 120);
    
  }, 8); // 约120fps的更新频率
}

// 判断是否为颜色值
function isColorValue(value) {
  if (typeof value !== 'string') return false
  
  // 检查是否是十六进制颜色格式
  const hexColorRegex = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6}|[0-9A-Fa-f]{8})$/
  if (hexColorRegex.test(value)) return true
  
  // 检查是否是rgb/rgba格式
  const rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/
  const rgbaRegex = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/
  if (rgbRegex.test(value) || rgbaRegex.test(value)) return true
  
  return false
}

// 修改抽取逻辑，支持固定元素
function drawFromSelectedCards() {
  // 获取所有激活的卡池
  const activePools = pools.value.filter(p => p.active && p.cards && p.cards.length > 0);

  if (activePools.length === 0) {
    ElMessage.warning('没有激活的卡池可用于抽取');
    return;
  }

  // 清理样式缓存和显示状态
  clearStyleCache();
  showAllCards.value.clear();

  // 先保存当前所有固定的卡片
  const fixedCards = [];
  
  // 遍历所有已经抽取的卡片，查找并保存那些被固定的卡片
  drawnCards.value.forEach(card => {
    const compositeKey = `${card.poolId}-${card.id}`;
    if (fixedElements[compositeKey]) {
      fixedCards.push({...card});
      
      // 确保固定卡片在对应卡池中显示为已选中
      const pool = activePools.find(p => p.id === card.poolId);
      if (pool && pool.cards) {
        const cardInPool = pool.cards.find(c => c.id === card.id);
        if (cardInPool) {
          cardInPool.selected = true;
        }
      }
    }
  });
  
  // 重置所有非固定卡片的选中状态
  activePools.forEach(pool => {
    pool.cards.forEach(card => {
      const compositeKey = `${pool.id}-${card.id}`;
      // 如果不是固定卡片，重置勾选状态
      if (!fixedElements[compositeKey]) {
        updateCardSelection(pool.id, card, false);
      }
    });
  });
  
  // 准备新的抽取结果数组
  const results = [];
  
  // 添加所有固定卡片到结果中
  results.push(...fixedCards);
  
  // 计算各个池中还需要抽取的卡片数量（扣除已固定的卡片）
  activePools.forEach(pool => {
    const count = pool.drawCount || 1;
    
    // 计算这个池中已经固定的卡片数量
    const fixedCountInThisPool = fixedCards.filter(card => card.poolId === pool.id).length;
    
    // 计算还需要抽取的数量
    const remainingCount = Math.max(0, count - fixedCountInThisPool);
    
    if (remainingCount > 0) {
      // 获取未固定的卡片
      const unfixedCards = pool.cards.filter(card => {
        const compositeKey = `${pool.id}-${card.id}`;
        return !fixedElements[compositeKey];
      });
      
      // 随机抽取指定数量的卡片
      const poolCards = customPoolStore.drawRandomCards(unfixedCards, remainingCount);
      
      // 为抽出的卡片添加池ID信息并更新卡池中的选中状态
      poolCards.forEach(card => {
        // 更新卡池中卡片的勾选状态
        const cardInPool = pool.cards.find(c => c.id === card.id);
        if (cardInPool) {
          updateCardSelection(pool.id, cardInPool, true);
        }
        
        results.push({
          ...card,
          poolId: pool.id
        });
      });
    }
  });
  
  // 更新抽取结果
  drawnCards.value = results;
}

// 打乱数组顺序
function shuffleArray(array) {
  const newArray = [...array]
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]]
  }
  return newArray
}

// 获取维度名称
function getDimensionName(poolId, dimId) {
  const pool = pools.value.find(p => p.id === poolId)
  if (!pool || !pool.dimensions) return dimId
  
  const dimension = pool.dimensions.find(d => d.id === dimId)
  return dimension ? dimension.name : dimId
}

// 创建新卡池
function createPool() {
  editingPool.value = customPoolStore.createNewPool()
  isNewPool.value = true
  editDialogVisible.value = true
}

// 编辑卡池
function editPool(pool) {
  // 判断是普通编辑还是实体管理
  ElMessageBox.confirm(
    '您希望编辑什么内容？',
    '编辑卡池',
    {
      confirmButtonText: '卡池内容',
      cancelButtonText: '基本信息',
      distinguishCancelAndClose: true,
      type: 'info'
    }
  ).then(() => {
    // 打开实体管理器
    openEntityManager(pool)
  }).catch(action => {
    if (action === 'cancel') {
      // 打开基本信息编辑
  editingPool.value = JSON.parse(JSON.stringify(pool))
      isNewPool.value = false
      editDialogVisible.value = true
    }
  })
}

// 修复删除卡池后未关闭弹窗的问题
const confirmDeletePool = (pool) => {
  if (!pool) return
  
  ElMessageBox.confirm(
    `确定要删除卡池"${pool.name}"吗？此操作不可恢复！`,
    '删除确认',
    {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'warning',
      closeOnClickModal: false
    }
  ).then(() => {
    customPoolStore.deletePool(pool.id).then(() => {
        ElMessage.success('删除成功')
      // 如果删除的是当前正在编辑的卡池，关闭对话框
      if (editDialogVisible) {
        editDialogVisible.value = false
      }
    }).catch(err => {
      ElMessage.error(`删除失败: ${err.message || '未知错误'}`)
    })
  }).catch(() => {
    // 用户取消删除，不做处理
  })
}

// 添加这些函数用于锁定背景滚动
function lockBodyScroll() {
  document.body.style.overflow = 'hidden'
}

function unlockBodyScroll() {
  document.body.style.overflow = 'auto'
}

// 保存编辑中的卡池
async function saveEditingPool() {
  if (!editingPool.value.name) {
    ElMessage.warning('卡池名称不能为空')
    return
  }
  
  try {
    // 确保新创建的卡池有基本结构
    if (isNewPool.value) {
      editingPool.value.cards = []
      editingPool.value.dimensions = []
    }
    
    const savedPool = await customPoolStore.savePool(editingPool.value)
    if (savedPool) {
      editDialogVisible.value = false
      ElMessage.success(isNewPool.value ? '卡池创建成功' : '卡池更新成功')
    }
  } catch (error) {
    ElMessage.error(`保存卡池失败: ${error.message || '未知错误'}`)
  }
}

// 打开卡池实体管理器
function openEntityManager(pool) {
  currentEditingPool.value = { ...pool }
  // 创建深拷贝以防止直接修改原始数据
  editingCards.value = pool.cards ? JSON.parse(JSON.stringify(pool.cards)) : []
  editingDimensions.value = pool.dimensions ? JSON.parse(JSON.stringify(pool.dimensions)) : []
  
  // 立即同步卡片属性与维度（处理历史数据）
  syncCardProperties()
  
  managerActiveTab.value = 'cards'
  entityManagerVisible.value = true
}

// 修改添加新卡片的逻辑，根据现有维度初始化属性
function addNewCardOrDimension() {
  if (managerActiveTab.value === 'cards') {
    openAddCardDialog()
  } else {
    // 添加新维度
    const newDimension = {
      id: `dim_${Date.now()}`,
      name: '',
      description: '',
      type: 'text',
      options: []
    }
    editingDimensions.value.push(newDimension);
    
    // 将新维度添加到所有现有卡片
    // 注意：我们先不添加，等维度保存时再处理，避免用户取消操作
  }
}

// 处理维度变更时同步更新卡片属性
function syncCardProperties() {
  // 获取所有维度ID
  const dimensionIds = editingDimensions.value.map(dim => dim.id);
  
  // 遍历所有卡片
  editingCards.value.forEach(card => {
    if (!card.properties) {
      card.properties = {};
    }
    
    // 1. 移除不存在的维度属性
    for (const propId in card.properties) {
      if (!dimensionIds.includes(propId)) {
        delete card.properties[propId];
      }
    }
    
    // 2. 添加缺失的维度属性
    editingDimensions.value.forEach(dim => {
      if (card.properties[dim.id] === undefined) {
        // 根据类型设置默认值
        let defaultValue = '';
        switch (dim.type) {
          case 'number':
            defaultValue = 0;
            break;
          case 'select':
            defaultValue = dim.options?.length > 0 ? dim.options[0] : '';
            break;
          case 'array':
            defaultValue = [];
            break;
          case 'boolean':
            defaultValue = false;
            break;
          default:
            defaultValue = '';
        }
        
        card.properties[dim.id] = defaultValue;
      }
    });
  });
}

// 在维度编辑后，特别是保存前调用此方法
function handleDimensionChanges() {
  const previousDimensions = currentEditingPool.value?.dimensions || [];
  const currentDimensions = editingDimensions.value;
  
  // 检查是否有维度变更（添加、删除或修改类型）
  const needSync = previousDimensions.length !== currentDimensions.length ||
    previousDimensions.some(prev => {
      const curr = currentDimensions.find(c => c.id === prev.id);
      return !curr || curr.type !== prev.type;
    });
  
  if (needSync) {
    // 同步卡片属性
    syncCardProperties();
    
    // 可选：显示提示
    ElMessage.info('卡片属性已根据维度变更自动调整');
  }
}


// 移除维度时也需要清理相关属性
function removeDimension(index) {
  const dimId = editingDimensions.value[index].id;
  
  // 从所有卡片中删除该维度的属性
  editingCards.value.forEach(card => {
    if (card.properties && card.properties[dimId]) {
      delete card.properties[dimId];
    }
  });
  
  // 然后删除维度
  editingDimensions.value.splice(index, 1);
  
  // 提示用户
  ElMessage.info('已从所有卡片中移除相关属性');
}

// 编辑维度选项
function editDimensionOptions(dimension) {
  currentEditingDimension.value = dimension
  currentOptions.value = dimension.options ? [...dimension.options] : []
  optionsDialogVisible.value = true
}

// 添加选项
function addOption() {
  currentOptions.value.push('')
}

// 移除选项
function removeOption(index) {
  currentOptions.value.splice(index, 1)
}

// 保存选项
function saveOptions() {
  // 过滤掉空选项
  currentEditingDimension.value.options = currentOptions.value.filter(option => option.trim() !== '')
  optionsDialogVisible.value = false
}

// 导出卡池数据到剪贴板
async function exportPoolData() {
  if (!currentEditingPool.value) return
  
  try {
    // 创建要导出的数据对象
    const exportData = {
      name: currentEditingPool.value.name,
      description: currentEditingPool.value.description,
      cards: editingCards.value,
      dimensions: editingDimensions.value,
      version: '1.0'
    }
    
    // 转换为格式化的JSON字符串
    const jsonStr = JSON.stringify(exportData, null, 2)
    
    // 复制到剪贴板
    await window.pywebview.api.copy_to_clipboard(jsonStr)
    
    // 显示成功消息
    ElMessage({
      message: '卡池配置已复制到剪贴板',
      type: 'success',
      duration: 2000
    })
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('复制到剪贴板失败，请检查浏览器权限')
  }
}

// 从剪贴板导入卡池数据
async function importPoolData() {
  importJsonText.value = ''
  importError.value = ''
  importDialogVisible.value = true
}

// 编辑卡片属性
function editCardProperties(card) {
  currentEditingCard.value = JSON.parse(JSON.stringify(card)) // 深拷贝整个卡片对象
  currentCardProperties.value = JSON.parse(JSON.stringify(card.properties || {}))
  
  // 确保所有维度的属性都存在
  editingDimensions.value.forEach(dim => {
    if (currentCardProperties.value[dim.id] === undefined) {
      // 根据维度类型设置默认值
      let defaultValue = ''
      switch (dim.type) {
        case 'number':
          defaultValue = 0
          break
        case 'select':
          defaultValue = dim.options?.length > 0 ? dim.options[0] : ''
          break
        case 'array':
          defaultValue = []
          break
        case 'boolean':
          defaultValue = false
          break
        case 'date':
          defaultValue = ''
          break
        case 'color':
          defaultValue = '#409EFF'
          break
        default:
          defaultValue = ''
      }
      currentCardProperties.value[dim.id] = defaultValue
    }
  })
  
  propertiesDialogVisible.value = true
}

// 保存卡片属性
function saveCardProperties() {
  if (currentEditingCard.value) {
    // 找到要编辑的卡片
    const cardIndex = editingCards.value.findIndex(c => c.id === currentEditingCard.value.id)
    if (cardIndex !== -1) {
      // 更新标题和属性
      editingCards.value[cardIndex].title = currentEditingCard.value.title
      editingCards.value[cardIndex].properties = JSON.parse(JSON.stringify(currentCardProperties.value))
    }
    
    propertiesDialogVisible.value = false
    ElMessage.success('卡片已更新')
  }
}

// 添加属性到卡片
function addPropertyToCard() {
  if (!selectedDimensionId.value) return
  
  const dim = getDimensionById(selectedDimensionId.value)
  if (!dim) return
  
  // 根据类型初始化属性值
  let defaultValue = ''
  switch (dim.type) {
    case 'number':
      defaultValue = 0
      break
    case 'select':
      defaultValue = dim.options?.length > 0 ? dim.options[0] : ''
      break
    case 'array':
      defaultValue = []
      break
    case 'boolean':
      defaultValue = false
      break
    case 'date':
      defaultValue = ''
      break
    case 'color':
      defaultValue = '#409EFF'
      break
    default:
      defaultValue = ''
  }
  
  currentCardProperties.value[selectedDimensionId.value] = defaultValue
  selectedDimensionId.value = ''
}

// 从卡片中移除属性
function removePropertyFromCard(dimId) {
  delete currentCardProperties.value[dimId]
}

// 修改添加数组项的处理函数
function addArrayItem(dimId, context = 'new') {
  // 根据context选择正确的目标对象
  const targetProperties = context === 'new' ? 
    newCard.value.properties : 
    currentCardProperties.value;

  // 确保目标属性是数组
  if (!Array.isArray(targetProperties[dimId])) {
    targetProperties[dimId] = [];
  }
  
  // 添加新的空字符串项
  targetProperties[dimId].push('');
}

// 修改移除数组项的处理函数
function removeArrayItem(dimId, index, context = 'new') {
  // 根据context选择正确的目标对象
  const targetProperties = context === 'new' ? 
    newCard.value.properties : 
    currentCardProperties.value;

  if (Array.isArray(targetProperties[dimId])) {
    targetProperties[dimId].splice(index, 1);
  }
}

// 根据ID获取维度
function getDimensionById(id) {
  return editingDimensions.value.find(dim => dim.id === id)
}

// 获取维度类型的显示标签
function getDimensionTypeLabel(type) {
  const typeMap = {
    'text': '文本',
    'number': '数字',
    'select': '选项',
    'array': '数组',
    'boolean': '布尔',
    'date': '日期',
    'color': '颜色'
  }
  return typeMap[type] || type
}

// 添加卡片对话框
const addCardDialogVisible = ref(false)
const newCard = ref({
  id: '',
  title: '',
  description: '',
  properties: {}
})

// 初始化新卡片
function initNewCard() {
  const cardId = `card_${Date.now()}`
  newCard.value = {
    id: cardId,
    title: `卡片 ${editingCards.value.length + 1}`,
    description: '',
    properties: {}
  }
  
  // 根据维度类型设置合适的默认值
  editingDimensions.value.forEach(dim => {
    let defaultValue;
    switch (dim.type) {
      case 'text':
        defaultValue = '';
        break;
      case 'number':
        defaultValue = 0;
        break;
      case 'select':
        // 如果有选项，使用第一个选项作为默认值
        defaultValue = dim.options?.length > 0 ? dim.options[0] : '';
        break;
      case 'array':
        defaultValue = [];
        break;
      case 'boolean':
        defaultValue = false;
        break;
      case 'date':
        // 使用当前日期作为默认值
        defaultValue = new Date().toISOString().split('T')[0];
        break;
      case 'color':
        defaultValue = '#409EFF'; // Element Plus 默认主题色
        break;
      default:
        defaultValue = '';
    }
    
    // 如果维度有自己的默认值设置，优先使用维度的默认值
    if (dim.defaultValue !== undefined) {
      defaultValue = dim.defaultValue;
    }
    
    newCard.value.properties[dim.id] = defaultValue;
  });
}

// 打开添加卡片弹窗
function openAddCardDialog() {
  // 如果没有维度，提示先添加维度
  if (editingDimensions.value.length === 0) {
    ElMessage.warning('请先添加维度再添加卡片')
    managerActiveTab.value = 'dimensions'
    return
  }
  
  initNewCard()
  addCardDialogVisible.value = true
  // 不再需要手动调用 lockBodyScroll，由对话框的 @open 事件处理
}

// 确认添加卡片
async function confirmAddCard() {
  // 验证卡片数据
  const validationErrors = validateCard(newCard.value);
  
  if (validationErrors.length > 0) {
    // 显示验证错误
    ElMessage({
      type: 'warning',
      message: validationErrors.join('\n'),
      duration: 5000,
      showClose: true
    });
    return;
  }
  
  try {
    // 添加到卡片列表
    editingCards.value.push(JSON.parse(JSON.stringify(newCard.value)));
    
    // 关闭对话框
    addCardDialogVisible.value = false;
    
    // 提示成功
    ElMessage({
      type: 'success',
      message: '卡片添加成功'
    });
    
    // 可以选择是否立即保存
    // await savePoolEntities();
  } catch (error) {
    ElMessage({
      type: 'error',
      message: '添加卡片失败：' + (error.message || '未知错误')
    });
  }
}

// 确认添加卡片前的验证
function validateCard(card) {
  const errors = [];
  
  // 验证必填属性
  editingDimensions.value.forEach(dim => {
    const value = card.properties[dim.id];
    
    // 根据不同类型进行验证
    switch (dim.type) {
      case 'text':
      case 'select':
        if (dim.required && (!value || value.trim() === '')) {
          errors.push(`${dim.name} 不能为空`);
        }
        break;
      case 'number':
        if (dim.required && (value === null || value === undefined || isNaN(value))) {
          errors.push(`${dim.name} 必须是有效数字`);
        }
        if (dim.min !== undefined && value < dim.min) {
          errors.push(`${dim.name} 不能小于 ${dim.min}`);
        }
        if (dim.max !== undefined && value > dim.max) {
          errors.push(`${dim.name} 不能大于 ${dim.max}`);
        }
        break;
      case 'array':
        if (dim.required && (!Array.isArray(value) || value.length === 0)) {
          errors.push(`${dim.name} 至少需要一个值`);
        }
        break;
      case 'date':
        if (dim.required && !value) {
          errors.push(`${dim.name} 必须选择日期`);
        }
        break;
      case 'color':
        if (dim.required && !value) {
          errors.push(`${dim.name} 必须选择颜色`);
        }
        break;
    }
  });
  
  return errors;
}

// 导入相关变量
const importDialogVisible = ref(false)
const importJsonText = ref('')
const importError = ref('')
const importLoading = ref(false)

// 添加一个生成唯一ID的函数
function generateUniqueId() {
  return 'pool_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
}

// 处理导入数据
async function processImportData() {
  importError.value = ''
  importLoading.value = true
  
  try {
    // 验证输入
    if (!importJsonText.value.trim()) {
      importError.value = '请输入JSON配置'
      importLoading.value = false
      return
    }
    
    // 解析JSON
    const importData = JSON.parse(importJsonText.value)
    
    // 验证导入数据格式
    if (!importData.cards || !importData.dimensions) {
      importError.value = '导入的数据格式不正确，缺少卡片或维度信息'
      importLoading.value = false
      return
    }
    
    // 处理导入逻辑
    if (!currentEditingPool.value) {
      // 如果没有当前编辑池，创建一个新的
      editingPool.value = {
        name: importData.name || '导入的卡池',
        description: importData.description || '',
        id: generateUniqueId() // 使用添加的函数生成ID
      }
      
      try {
        // 保存基本信息
        await saveEditingPool()
        
        // 设置当前编辑池
        currentEditingPool.value = editingPool.value
  } catch (err) {
        importError.value = '创建新卡池失败: ' + err.message
        importLoading.value = false
        return
      }
    }
    
    // 更新卡片和维度
    editingCards.value = importData.cards
    editingDimensions.value = importData.dimensions
    
    // 更新名称和描述
    if (importData.name && currentEditingPool.value) {
      currentEditingPool.value.name = importData.name
    }
    
    if (importData.description && currentEditingPool.value) {
      currentEditingPool.value.description = importData.description
    }
    
    // 同步卡片属性与维度
    syncCardProperties()
    
    // 保存到后端
    try {
      await savePoolEntities(true)
      
      // 关闭导入对话框
      importLoading.value = false
      importDialogVisible.value = false
      
      // 成功消息
      ElMessage.success('卡池配置导入成功并已保存')
      
      // 刷新卡池列表数据
      await refreshPoolsData()
      
      // 如果当前有编辑窗口打开，也关闭它
      entityManagerVisible.value = false
    } catch (err) {
      importError.value = '保存导入数据失败: ' + err.message
      importLoading.value = false
    }
  } catch (error) {
    console.error('导入失败:', error)
    importError.value = error.message || '无法解析JSON或保存数据'
    importLoading.value = false
  }
}

// 改进刷新卡池列表数据方法
async function refreshPoolsData() {
  try {
    // 直接使用store的fetchPools方法刷新数据
    await customPoolStore.fetchPools()
    
    // 更新当前组件的pools引用，确保视图更新
    pools.value = customPoolStore.pools
  } catch (error) {
    console.error('刷新卡池数据失败:', error)
    ElMessage.error('刷新数据失败: ' + error.message)
  }
}


// 加载卡池数据
async function loadPools() {
  pools.value = await customPoolStore.getPools()
}

// 确保滚动条在动态内容加载后更新
function updateScrollbars() {
  // 让Vue完成更新周期后执行
  nextTick(() => {
    // 找到所有的el-scrollbar元素并更新
    const scrollbars = document.querySelectorAll('.el-scrollbar');
    scrollbars.forEach(scrollbar => {
      const scrollbarComponent = scrollbar.__vue__;
      if (scrollbarComponent && typeof scrollbarComponent.update === 'function') {
        scrollbarComponent.update();
      }
    });
  });
}

// 在数据加载完成后调用
watch(() => pools.value.length, () => {
  updateScrollbars();
});

// 在卡池切换或内容更新时调用
watch(() => pools.value.map(p => p.cards?.length || 0).join(','), () => {
  updateScrollbars();
});

// 优化的滚轮事件处理函数 - 使用节流和更简洁的逻辑
let wheelThrottleTimer = null;
function handlePoolsWheelEvent(event) {
  // 使用节流减少事件处理频率，提升性能
  if (wheelThrottleTimer) return;

  wheelThrottleTimer = setTimeout(() => {
    wheelThrottleTimer = null;
  }, 16); // 约60fps的更新频率

  const targetEl = event.target;
  const isInsideScrollContainer = targetEl.closest('.native-scroll');

  if (isInsideScrollContainer) {
    const scrollContainer = targetEl.closest('.native-scroll');
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;

    // 简化边界检测
    const canScrollDown = scrollTop < scrollHeight - clientHeight - 1;
    const canScrollUp = scrollTop > 1;

    // 只有在垂直滚动到边界时才触发水平滚动
    if ((event.deltaY > 0 && !canScrollDown) || (event.deltaY < 0 && !canScrollUp)) {
      event.preventDefault();
      // 使用requestAnimationFrame确保滚动的流畅性
      requestAnimationFrame(() => {
        if (poolsWrapper.value) {
          poolsWrapper.value.scrollLeft += event.deltaY * 0.8; // 降低滚动速度
        }
      });
    }
  } else {
    // 非滚动容器区域直接水平滚动
    event.preventDefault();
    requestAnimationFrame(() => {
      if (poolsWrapper.value) {
        poolsWrapper.value.scrollLeft += event.deltaY * 0.8;
      }
    });
  }
}

// 添加事件监听辅助方法
function addNativeScrollListeners() {
  // 在组件挂载后为所有原生滚动容器添加事件监听
  nextTick(() => {
    const scrollContainers = document.querySelectorAll('.native-scroll');
    scrollContainers.forEach(container => {
      container.addEventListener('wheel', handleScrollContainerWheel, { passive: false });
    });
  });
}

// 原生滚动容器的滚轮事件处理
function handleScrollContainerWheel(event) {
  const container = event.currentTarget;
  const { scrollTop, scrollHeight, clientHeight } = container;
  
  // 检查是否可以继续在当前方向滚动
  const canScrollDown = scrollTop < scrollHeight - clientHeight;
  const canScrollUp = scrollTop > 0;
  
  // 如果可以在请求的方向上滚动，阻止事件传播到父容器
  if ((event.deltaY > 0 && canScrollDown) || (event.deltaY < 0 && canScrollUp)) {
    event.stopPropagation();
  }
}

// 优化的结果区域滚动处理
let resultWheelThrottleTimer = null;
function handleResultWheelEvent(event) {
  // 使用节流减少事件处理频率
  if (resultWheelThrottleTimer) return;

  resultWheelThrottleTimer = setTimeout(() => {
    resultWheelThrottleTimer = null;
  }, 16); // 约60fps的更新频率

  // 阻止默认行为
  event.preventDefault();

  // 获取结果滚动容器
  const resultContainer = document.querySelector('.result-scroll-container');
  if (!resultContainer) return;

  // 使用requestAnimationFrame优化滚动性能
  requestAnimationFrame(() => {
    // 平滑滚动效果
    resultContainer.style.scrollBehavior = 'auto';
    resultContainer.scrollLeft += event.deltaY * 0.8; // 降低滚动速度

    // 滚动后恢复平滑滚动
    setTimeout(() => {
      resultContainer.style.scrollBehavior = 'smooth';
    }, 50);
  });
}

// 在组件挂载后添加事件监听
onMounted(() => {
  // 其他初始化代码...
  
  // 为结果滚动容器添加滚轮事件监听
  const resultContainer = document.querySelector('.result-scroll-container');
  if (resultContainer) {
    resultContainer.addEventListener('wheel', handleResultWheelEvent, { passive: false });
  }
});

// 修改抽取按钮逻辑
const hasActivePoolsWithCards = computed(() => {
  return pools.value.some(pool => 
    pool.active && pool.cards && pool.cards.length > 0
  );
});

// 复制卡片内容
function copyCardContent(card) {
  let content = `${card.title}\n`;
  if (card.description) {
    content += `${card.description}\n`;
  }
  
  if (card.properties && Object.keys(card.properties).length > 0) {
    content += '\n属性:\n';
    for (const [key, value] of Object.entries(card.properties)) {
      const dimensionName = getDimensionName(card.poolId, key);
      content += `${dimensionName}: ${value}\n`;
    }
  }
  
  window.pywebview.api.copy_to_clipboard(content)
    .then(() => {
      ElMessage.success('已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
      ElMessage.error('复制失败');
    });
}

// 从结果中移除卡片
function removeCardFromResult(index) {
  drawnCards.value.splice(index, 1);
}

// 将抽取的卡片按卡池分组
const groupedDrawnCards = computed(() => {
  const grouped = {};
  
  drawnCards.value.forEach(card => {
    if (!grouped[card.poolId]) {
      grouped[card.poolId] = [];
    }
    grouped[card.poolId].push(card);
  });
  
  return grouped;
});

// 获取卡池图标
function getPoolIcon(poolId) {
  const pool = pools.value.find(p => p.id === poolId);
  if (!pool) return '?';
  
  // 使用卡池名称的第一个字符作为图标
  return pool.name.charAt(0);
}

// 获取卡池图标样式
function getPoolIconStyle(poolId) {
  // 为不同卡池生成不同的背景色
  const colors = [
    'rgba(var(--el-color-primary-rgb), 0.7)',
    'rgba(var(--el-color-success-rgb), 0.7)',
    'rgba(var(--el-color-warning-rgb), 0.7)',
    'rgba(var(--el-color-danger-rgb), 0.7)',
    'rgba(var(--el-color-info-rgb), 0.7)'
  ];
  
  // 使用poolId的哈希值来选择颜色
  const colorIndex = poolId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
  
  return {
    backgroundColor: colors[colorIndex]
  };
}

// 获取缓存的卡片样式
function getCardStyle(poolId, cardId) {
  const key = `${poolId}-${cardId}`;

  if (!cardStyleCache.has(key)) {
    const colors = [
      'var(--el-color-primary)',
      'var(--el-color-success)',
      'var(--el-color-warning)',
      'var(--el-color-danger)',
      '#3498db',
      '#9b59b6',
      '#1abc9c',
      '#e67e22',
      '#e74c3c'
    ];

    // 使用卡片ID作为种子，确保同一张卡片总是相同的样式
    const seed = cardId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const colorIndex = seed % colors.length;

    const style = {
      backgroundColor: colors[colorIndex]
    };

    cardStyleCache.set(key, style);
  }

  return cardStyleCache.get(key);
}

// 为结果项生成随机样式（保持向后兼容）
function getRandomStyle() {
  const colors = [
    'var(--el-color-primary)',
    'var(--el-color-success)',
    'var(--el-color-warning)',
    'var(--el-color-danger)',
    '#3498db',
    '#9b59b6',
    '#1abc9c',
    '#e67e22',
    '#e74c3c'
  ];

  const randomColor = colors[Math.floor(Math.random() * colors.length)];

  return {
    backgroundColor: randomColor
  };
}

// 删除卡片
async function removeCard(index) {
  try {
    // 添加确认对话框
    await ElMessageBox.confirm(
      '确定要删除这张卡片吗？此操作不可恢复。',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 从编辑中的卡片数组中移除指定索引的卡片
    editingCards.value.splice(index, 1)
    
    // 提示用户删除成功
    ElMessage({
      type: 'success',
      message: '卡片已删除'
    })
    
    // 如果需要立即保存更改，可以调用保存方法
    // await savePoolEntities()
  } catch (err) {
    if (err !== 'cancel') { // 用户取消删除不显示错误提示
      ElMessage({
        type: 'error',
        message: '删除卡片失败：' + (err.message || '未知错误')
      })
    }
  }
}

// 修复卡池实体保存功能
async function savePoolEntities() {
  if (!currentEditingPool.value) return
  
  try {
    // 验证维度名称
    if (editingDimensions.value.some(dim => !dim.name)) {
      ElMessage.warning('有维度缺少名称，请补充完整')
      return
    }
    
    // 在保存前同步卡片属性与维度
    handleDimensionChanges()
    
    // 更新当前编辑池
    const updatedPool = {
      ...currentEditingPool.value,
      cards: editingCards.value,
      dimensions: editingDimensions.value
    }
    
    // 调用store方法保存
    await customPoolStore.savePool(updatedPool)
    
    // 显示成功消息
    ElMessage.success('保存成功')
    
    return true
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败: ' + (error.message || '未知错误'))
    return false
  }
}

// 优化的结果区域拖拽滚动功能
function initResultDragScroll() {
  const container = document.querySelector('.result-container');
  if (!container) return;

  let isDown = false;
  let startX = 0;
  let scrollLeft = 0;
  let animationId = null;

  const handleMouseDown = (e) => {
    // 检查是否点击在结果项上，如果是则不启动拖拽
    if (e.target.closest('.result-item')) return;

    isDown = true;
    container.classList.add('dragging');
    startX = e.pageX - container.offsetLeft;
    scrollLeft = container.scrollLeft;
    container.style.scrollBehavior = 'auto';

    // 防止拖拽时选中文本
    document.body.style.userSelect = 'none';
  };

  const handleMouseUp = () => {
    if (isDown) {
      isDown = false;
      container.classList.remove('dragging');
      container.style.scrollBehavior = 'smooth';

      // 恢复用户选择
      document.body.style.userSelect = '';

      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
    }
  };

  const handleMouseMove = (e) => {
    if (!isDown) return;
    e.preventDefault();

    // 直接更新滚动位置，确保跟手性
    const x = e.pageX - container.offsetLeft;
    const walk = (x - startX) * 1.5;
    const newScrollLeft = scrollLeft - walk;

    // 边界检查，防止滚动超出范围
    const maxScrollLeft = container.scrollWidth - container.clientWidth;
    container.scrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));
  };

  container.addEventListener('mousedown', handleMouseDown);
  document.addEventListener('mouseup', handleMouseUp);
  document.addEventListener('mouseleave', handleMouseUp);
  document.addEventListener('mousemove', handleMouseMove);

  // 返回清理函数
  return () => {
    container.removeEventListener('mousedown', handleMouseDown);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('mouseleave', handleMouseUp);
    document.removeEventListener('mousemove', handleMouseMove);
    if (animationId) {
      cancelAnimationFrame(animationId);
    }
  };
}

// 增强结果项滚动体验的函数
function enhanceResultItemsScroll() {
  // 等待元素渲染完成
  setTimeout(() => {
    // 获取所有结果项容器
    const resultItemsContainers = document.querySelectorAll('.result-items');
    
    console.log('找到结果项容器:', resultItemsContainers.length); // 调试信息
    
    resultItemsContainers.forEach(container => {
      // 防止重复绑定事件
      if (container.getAttribute('data-scroll-enhanced')) return;
      container.setAttribute('data-scroll-enhanced', 'true');
      
      // 监听滚轮事件
      container.addEventListener('wheel', (e) => {
        // 检查是否需要阻止冒泡
        const { scrollTop, scrollHeight, clientHeight } = container;
        const isAtBottom = scrollTop + clientHeight >= scrollHeight - 5;
        const isAtTop = scrollTop <= 0;
        
        // 只有在内容足够滚动的情况下才处理
        if (scrollHeight > clientHeight) {
          // 如果向下滚动且已在底部，或向上滚动且已在顶部，让父容器处理
          if ((e.deltaY > 0 && isAtBottom) || (e.deltaY < 0 && isAtTop)) {
            // 允许冒泡到父容器
          } else {
            // 否则阻止冒泡，让当前容器处理滚动
            e.stopPropagation();
          }
        }
        
        // 添加滚动动画类
        container.classList.add('scrolling');
        
        // 移除滚动动画类的计时器
        if (container.scrollTimer) {
          clearTimeout(container.scrollTimer);
        }
        
        // 设置计时器，滚动停止后移除动画类
        container.scrollTimer = setTimeout(() => {
          container.classList.remove('scrolling');
        }, 1000);
      });
      
      // 监听鼠标进入和离开
      container.addEventListener('mouseenter', () => {
        container.classList.add('hover');
      });
      
      container.addEventListener('mouseleave', () => {
        container.classList.remove('hover');
        container.classList.remove('scrolling');
      });
    });
  }, 500); // 延迟确保DOM已完全加载
}

// 同时监听DOM变更以处理动态添加的元素
function setupMutationObserver() {
  // 创建一个观察器实例
  const observer = new MutationObserver(() => {
    // 如果DOM有变化，重新检查并增强滚动效果
    enhanceResultItemsScroll();
  });

  // 开始观察文档中的变化
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 返回清理函数
  return () => {
    observer.disconnect();
  };
}

// 切换卡片固定状态
function toggleCardFixed(card, event) {
  // 阻止事件冒泡
  event?.stopPropagation();
  
  // 使用计时器判断是单击还是双击
  if (clickTimer.value) {
    // 如果计时器存在，说明是双击
    clearTimeout(clickTimer.value);
    clickTimer.value = null;
    isDoubleClick.value = true;
    
    // 调用双击处理函数
    showCardDetail(card, event);
    
    // 设置一个短暂的标志，防止单击事件误触发
    setTimeout(() => {
      isDoubleClick.value = false;
    }, 300);
    
    return;
  }
  
  // 设置单击计时器
  isDoubleClick.value = false;
  clickTimer.value = setTimeout(() => {
    // 如果计时器到期，说明是单击，执行锁定/解锁操作
    if (!isDoubleClick.value) {
      // 复合键：卡池ID + 卡片ID
      const compositeKey = `${card.poolId}-${card.id}`;
      
      // 切换固定状态
      if (fixedElements[compositeKey]) {
        // 解除固定
        delete fixedElements[compositeKey];
      } else {
        // 设置固定
        fixedElements[compositeKey] = true;
      }
    }
    
    // 重置计时器
    clickTimer.value = null;
  }, 250); // 单击延迟时间，可以根据需要调整
}

// 添加复制结果的方法
async function copyDrawnResults() {
  try {
    // 按卡池分组结果
    const groupedResults = {};
    drawnCards.value.forEach(card => {
      const poolName = getPoolName(card.poolId);
      if (!groupedResults[poolName]) {
        groupedResults[poolName] = [];
      }
      groupedResults[poolName].push(card);
    });

    // 格式化复制文本
    let copyText = '抽卡结果：\n\n';
    
    // 遍历每个卡池的结果
    for (const [poolName, cards] of Object.entries(groupedResults)) {
      copyText += `【${poolName}】\n`;
      
      // 遍历卡池中的每张卡
      cards.forEach((card, index) => {
        copyText += `${index + 1}. ${card.title}\n`;
        
        // 如果卡片有属性，添加属性信息
        if (card.properties) {
          for (const [key, value] of Object.entries(card.properties)) {
            // 获取维度名称
            const dimensionName = getDimensionName(card.poolId, key);
            
            // 根据值的类型格式化显示
            let displayValue = '';
            if (Array.isArray(value)) {
              displayValue = value.join('、');
            } else if (typeof value === 'boolean') {
              displayValue = value ? '是' : '否';
            } else {
              displayValue = value;
            }
            
            copyText += `   ${dimensionName}: ${displayValue}\n`;
          }
        }
        
        // 在每张卡片之间添加空行
        copyText += '\n';
      });
      
      // 在不同卡池之间添加分隔线
      copyText += '------------------------\n\n';
    }
    
    // 复制到剪贴板
    await window.pywebview.api.copy_to_clipboard(copyText);
    
    // 显示成功提示
    ElMessage({
      message: '抽卡结果已复制到剪贴板',
      type: 'success',
      duration: 2000
    });
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败，请检查浏览器权限');
  }
}

// 修改鼠标进入处理函数 - 简化版本
function handleDetailMouseEnter() {
  isUserHovering = true;
  const scrollContainer = document.querySelector('.detail-scrollbar');
  if (scrollContainer) {
    scrollContainer.classList.add('user-hovering');
    scrollContainer.classList.remove('auto-scrolling');
  }
}

// 修改鼠标离开处理函数 - 简化版本
function handleDetailMouseLeave() {
  isUserHovering = false;
  const scrollContainer = document.querySelector('.detail-scrollbar');
  if (scrollContainer) {
    scrollContainer.classList.remove('user-hovering');
    if (autoScrollEnabled.value) {
      scrollContainer.classList.add('auto-scrolling');
    }
  }
}

// 组件卸载时清理
onUnmounted(() => {
  // 清理自动滚动计时器
  if (autoScrollTimer) {
    clearInterval(autoScrollTimer);
    autoScrollTimer = null;
  }

  // 清理节流计时器
  if (wheelThrottleTimer) {
    clearTimeout(wheelThrottleTimer);
    wheelThrottleTimer = null;
  }

  if (resultWheelThrottleTimer) {
    clearTimeout(resultWheelThrottleTimer);
    resultWheelThrottleTimer = null;
  }

  // 执行所有收集的清理函数
  cleanupFunctions.forEach(cleanup => {
    if (typeof cleanup === 'function') {
      cleanup();
    }
  });

  // 清空清理函数数组
  cleanupFunctions = [];
});

// 处理对话框关闭 - 增强版
function handleDialogClose(done) {
  // 添加关闭动画
  const dialog = document.querySelector('.card-detail-dialog .el-dialog');
  if (dialog) {
    dialog.style.animation = 'dialogSlideOut 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)';
    setTimeout(() => {
      done();
    }, 400);
  } else {
    done();
  }
}

// 监听卡片详情对话框关闭
watch(cardDetailVisible, (newVal) => {
  if (!newVal && autoScrollTimer) {
    clearInterval(autoScrollTimer);
    autoScrollTimer = null;
  }
});





</script>

<style lang="scss" scoped>
@import url("@/scss/custompool.scss");

/* 导出对话框样式 */
:deep(.export-dialog) {
  .el-dialog__body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }

  .el-dialog__header {
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid var(--el-border-color);
  }
}

/* 显示更多/收起按钮样式 */
.show-more-btn,
.show-less-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  margin: 8px 4px;
  background: var(--el-fill-color-light);
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;

  &:hover {
    background: var(--el-fill-color);
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
    transform: translateY(-1px);
  }

  .el-icon {
    font-size: 14px;
  }
}

.show-less-btn {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-7);
  color: var(--el-color-primary);

  &:hover {
    background: var(--el-color-primary-light-8);
    border-color: var(--el-color-primary);
  }
}

/* 导出结果按钮美化样式 */
.export-results-btn {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  color: white;
  font-weight: 500;
  border-radius: 8px;
  padding: 8px 16px;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(103, 194, 58, 0.4);
    background: linear-gradient(135deg, #5daf34 0%, #7bc95a 100%);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  }

  .export-btn-content {
    display: flex;
    align-items: center;
    gap: 6px;
    position: relative;
    z-index: 1;

    .export-icon {
      font-size: 14px;
      animation: bounce 2s infinite;
    }

    .export-text {
      font-size: 13px;
      font-weight: 500;
    }

    .export-count {
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 11px;
      font-weight: 600;
      min-width: 18px;
      text-align: center;
      backdrop-filter: blur(4px);
    }
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* 深色主题适配 */
.dark .export-results-btn {
  background: linear-gradient(135deg, #529b2e 0%, #67c23a 100%);
  box-shadow: 0 2px 8px rgba(82, 155, 46, 0.4);

  &:hover {
    background: linear-gradient(135deg, #4a8929 0%, #5daf34 100%);
    box-shadow: 0 4px 16px rgba(82, 155, 46, 0.5);
  }
}
</style>