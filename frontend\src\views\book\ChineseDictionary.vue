<template>
  <div class="chinese-dictionary">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-gradient"></div>
      <div class="bg-circles"></div>
    </div>

    <!-- 主内容区域 - 采用左右分栏 -->
    <div class="app-container">
      <!-- 页面标题区域 -->
      <div class="page-header">
        <div class="header-content">
        <h2 class="title">
          <el-icon><Collection /></el-icon>
            灵感词典
        </h2>
          <p class="subtitle">在词汇与歇后语的海洋中遨游，激发无限创作灵感</p>
        </div>
        <div class="dictionary-type-selector-wrapper">
          <div class="dictionary-type-selector">
            <el-radio-group v-model="activeDictionaryType" size="large" :disabled="isLoadingSpecificDictionary">
              <el-radio-button value="word_entry">
                现代词汇 ({{ wordEntryDataLoaded ? wordEntryDictionary.length : '未加载' }})
                <el-icon v-if="activeDictionaryType === 'word_entry' && isLoadingSpecificDictionary" class="el-icon--right is-loading"><Loading /></el-icon>
              </el-radio-button>
              <el-radio-button value="xiehouyu">
                歇后语 ({{ xiehouyuDataLoaded ? xiehouyuDictionary.length : '未加载' }})
                <el-icon v-if="activeDictionaryType === 'xiehouyu' && isLoadingSpecificDictionary" class="el-icon--right is-loading"><Loading /></el-icon>
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>

      <!-- 加载状态显示 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
        <div class="loading-text">
          <el-icon class="loading-icon"><Loading /></el-icon>
          正在加载词典数据，请稍候...
        </div>
      </div>

      <template v-else>
        <div class="main-content">
          <!-- 左侧设置面板 -->
          <div class="settings-panel">
            <div class="glass-card">
              <!-- 搜索功能区域 -->
              <div class="card-header">
                <h3><el-icon><Search /></el-icon> {{ activeDictionaryType === 'xiehouyu' ? '歇后语搜索' : '词汇搜索' }}</h3>
              </div>
              <div class="settings search-section">
                <div class="search-box">
                  <el-input
                    v-model="searchKeyword"
                    :placeholder="activeDictionaryType === 'xiehouyu' ? '搜索谜面或谜底' : '搜索词语、拼音或释义'"
                    clearable
                    :prefix-icon="Search"
                    @keyup.enter="searchDictionary"
                    :disabled="isLoadingSpecificDictionary || (activeDictionaryType === 'word_entry' && !wordEntryDataLoaded) || (activeDictionaryType === 'xiehouyu' && !xiehouyuDataLoaded)"
                  >
                    <template #append>
                      <el-button @click="searchDictionary" :loading="isSearching" :disabled="isLoadingSpecificDictionary || (activeDictionaryType === 'word_entry' && !wordEntryDataLoaded) || (activeDictionaryType === 'xiehouyu' && !xiehouyuDataLoaded)">
                        搜索
                      </el-button>
                    </template>
                  </el-input>
                </div>
                <div class="search-options">
                  <el-radio-group v-model="searchMode" size="small" :disabled="isLoadingSpecificDictionary || (activeDictionaryType === 'word_entry' && !wordEntryDataLoaded) || (activeDictionaryType === 'xiehouyu' && !xiehouyuDataLoaded)">
                    <template v-if="activeDictionaryType === 'word_entry'">
                    <el-radio-button value="word">词语</el-radio-button>
                    <el-radio-button value="pinyin">拼音</el-radio-button>
                    <el-radio-button value="definition">释义</el-radio-button>
                    </template>
                    <template v-else-if="activeDictionaryType === 'xiehouyu'">
                      <el-radio-button value="riddle">谜面</el-radio-button>
                      <el-radio-button value="answer">谜底</el-radio-button>
                    </template>
                  </el-radio-group>
                </div>
              </div>
              
              <!-- 分隔符 -->
              <div class="separator">
                <div class="separator-line"></div>
                <div class="separator-text">或者</div>
                <div class="separator-line"></div>
              </div>

              <!-- 随机抽取设置 -->
              <div class="card-header">
                <h3>
                  <el-icon><SelectIcon /></el-icon>
                  随机抽取
                </h3>
                <div class="dictionary-info">
                  <el-tooltip :content="`当前词典 (${activeDictionaryType === 'word_entry' ? '词汇' : '歇后语'}) 总条目数`">
                    <div class="count-badge">
                      <el-icon><Document /></el-icon>
                      <span>{{ (activeDictionaryType === 'word_entry' && wordEntryDataLoaded) || (activeDictionaryType === 'xiehouyu' && xiehouyuDataLoaded) ? totalWords : '-' }} 条</span>
                    </div>
                  </el-tooltip>
                </div>
              </div>

              <!-- 设置项 -->
              <div class="settings">
                <!-- 词语长度 (针对词语和歇后语谜面) -->
                <div class="setting-item">
                  <div class="setting-label">
                    <el-icon><SortUp /></el-icon>
                    <span>{{ activeDictionaryType === 'xiehouyu' ? '谜面长度' : '词语长度' }}：</span>
                  </div>
                  <div class="setting-control">
                    <el-checkbox-group v-model="selectedLengths" :disabled="isLoadingSpecificDictionary || (activeDictionaryType === 'word_entry' && !wordEntryDataLoaded) || (activeDictionaryType === 'xiehouyu' && !xiehouyuDataLoaded)">
                      <el-checkbox-button v-for="len in (activeDictionaryType === 'xiehouyu' ? [3,4,5,6,7,8,9,10] : [1,2,3,4,5,6])" :key="len" :value="len">
                        {{len}}字
                      </el-checkbox-button>
                    </el-checkbox-group>
                  </div>
                </div>

                <!-- 抽取数量 -->
                <div class="setting-item">
                  <div class="setting-label">
                    <el-icon><Odometer /></el-icon>
                    <span>抽取数量：</span>
                  </div>
                  <div class="setting-control">
                    <el-slider
                      v-model="extractCount"
                      :min="1"
                      :max="50" 
                      :step="1"
                      show-stops
                      show-input
                      :marks="{1: '1', 10: '10', 25: '25', 50: '50'}"
                      :disabled="isLoadingSpecificDictionary || (activeDictionaryType === 'word_entry' && !wordEntryDataLoaded) || (activeDictionaryType === 'xiehouyu' && !xiehouyuDataLoaded)"
                    />
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="actions">
                  <div class="button-group">
                    <el-button 
                      type="primary" 
                      class="extract-button"
                      :icon="SelectIcon" 
                      @click="extractRandomWords" 
                      :disabled="isLoadingSpecificDictionary || isExtracting || totalWords === 0 || (activeDictionaryType === 'word_entry' && !wordEntryDataLoaded) || (activeDictionaryType === 'xiehouyu' && !xiehouyuDataLoaded)">
                      随机抽取
                    </el-button>
                    <el-button 
                      type="success" 
                      class="copy-button"
                      :icon="CopyDocument" 
                      @click="copyToClipboard" 
                      :disabled="!selectedWords.length">
                      复制结果
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧结果区域 -->
          <div class="results-panel">
            <!-- 按需加载时的加载提示 -->
            <div v-if="isLoadingSpecificDictionary && activeDictionaryType === 'word_entry' && !wordEntryDataLoaded" class="loading-container specific-loader">
                 <el-icon class="loading-icon"><Loading /></el-icon> <span>正在加载现代词汇数据...</span>
            </div>
            <div v-else-if="isLoadingSpecificDictionary && activeDictionaryType === 'xiehouyu' && !xiehouyuDataLoaded" class="loading-container specific-loader">
                 <el-icon class="loading-icon"><Loading /></el-icon> <span>正在加载歇后语数据...</span>
            </div>

            <!-- 结果展示 -->
            <div v-else-if="selectedWords.length > 0" class="glass-card result-card">
              <div class="card-header">
                <h3>{{ isSearchResult ? '搜索结果' : '抽取结果' }}</h3>
                <div class="result-count">{{ selectedWords.length }} 条</div>
              </div>

              <div class="result-grid" :class="{'large-grid': selectedWords.length <= 15, 'medium-grid': selectedWords.length > 15 && selectedWords.length <= 24, 'small-grid': selectedWords.length > 24}">
                <div
                  v-for="(item, index) in selectedWords"
                  :key="item.id || index" 
                  class="word-card"
                  @click="showWordDetail(item)"
                >
                  <div class="word-content">
                    <template v-if="item.type === 'xiehouyu'">
                      <div class="word-text riddle-text">{{ item.riddle }}</div>
                      <div class="word-pinyin xiehouyu-answer">{{ item.answer }}</div>
                    </template>
                    <template v-else-if="item.type === 'word_entry'">
                      <div class="word-text">{{ item.word }}</div>
                      <div class="word-pinyin" v-if="item.pinyin" v-html="formatPinyin(item.pinyin)"></div>
                    </template>
                  </div>
                  <div class="word-footer">
                    <span class="word-category">{{ getCategoryLabel(item) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 初始提示 -->
            <div v-else-if="!isLoadingSpecificDictionary && ((activeDictionaryType === 'word_entry' && wordEntryDataLoaded && totalWords > 0) || (activeDictionaryType === 'xiehouyu' && xiehouyuDataLoaded && totalWords > 0))" class="glass-card empty-tip">
              <el-icon><SelectIcon /></el-icon>
              <p>请在上方操作面板中搜索或随机抽取条目</p>
            </div>
            <div v-else-if="!isLoadingSpecificDictionary && activeDictionaryType === 'word_entry' && wordEntryDataLoaded && totalWords === 0" class="glass-card empty-tip">
              <el-icon><Document /></el-icon>
              <p>现代词汇数据为空，但已加载。</p>
            </div>
            <div v-else-if="!isLoadingSpecificDictionary && activeDictionaryType === 'xiehouyu' && xiehouyuDataLoaded && totalWords === 0" class="glass-card empty-tip">
              <el-icon><Document /></el-icon>
              <p>歇后语数据为空，但已加载。</p>
            </div>
            <div v-else-if="!isLoadingSpecificDictionary && activeDictionaryType === 'word_entry' && !wordEntryDataLoaded" class="glass-card empty-tip">
              <el-icon><WarningFilled /></el-icon>
              <p>现代词汇数据加载失败或尚未加载。请尝试切换词典类型或刷新页面。</p>
            </div>
            <div v-else-if="!isLoadingSpecificDictionary && activeDictionaryType === 'xiehouyu' && !xiehouyuDataLoaded" class="glass-card empty-tip">
              <el-icon><WarningFilled /></el-icon>
              <p>歇后语数据加载失败或尚未加载。请尝试切换词典类型或刷新页面。</p>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 词语详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      :title="currentWord?.type === 'xiehouyu' ? '歇后语详情' : (currentWord?.word || '词条详情')"
      width="600px" 
      :close-on-click-modal="true"
      :modal-class="'detail-dialog-bg'"
      :show-close="true"
      destroy-on-close
      top="8vh"
    >
      <div v-if="currentWord" class="word-detail glass-effect">
        <!-- 歇后语详情 -->
        <template v-if="currentWord.type === 'xiehouyu'">
        <div class="detail-header">
          <div class="word-title">
              <h3>{{ currentWord.riddle }}</h3>
          </div>
            <div class="detail-category">{{ getCategoryLabel(currentWord) }}</div>
        </div>
          <div class="detail-content">
            <div class="section">
              <div class="section-title">谜底</div>
              <div class="section-content">
                <p :class="{'dialog-xiehouyu-answer': currentWord.type === 'xiehouyu'}">{{ currentWord.answer }}</p>
              </div>
            </div>
          </div>
        </template>
        
        <!-- 词语/词汇详情 -->
        <template v-else>
          <div class="detail-header">
            <div class="word-title">
              <h3>{{ currentWord.word }}</h3>
              <div class="word-pinyin" v-if="currentWord.pinyin" v-html="formatPinyin(currentWord.pinyin)"></div>
            </div>
            <div class="detail-category">{{ getCategoryLabel(currentWord) }}</div>
          </div>
        <div class="detail-content">
          <div class="section">
              <div class="section-title">释义</div>
            <div class="section-content">
                <template v-if="currentWord.definition && typeof currentWord.definition === 'string' && currentWord.definition.includes('\n')">
                  <p v-for="(def, idx) in currentWord.definition.split('\n').filter(d => d.trim())" 
                     :key="idx" 
                     class="definition-item">
                    {{ def }}
                  </p>
                </template>
                <p v-else>{{ currentWord.definition || '暂无释义' }}</p>
            </div>
            </div>
             <!-- 显示其他信息，如 abbr -->
            <div class="section" v-if="currentWord.type === 'word_entry' && currentWord.abbr">
                <div class="section-title">缩写</div>
                <div class="section-content">
                    <p>{{ currentWord.abbr }}</p>
          </div>
        </div>
          </div>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  Collection, Document, Filter, Select as SelectIcon, CopyDocument, 
  SortUp, Odometer, Loading, Search, WarningFilled
} from '@element-plus/icons-vue'

// --- 数据存储 --- 
const wordEntryDictionary = ref([]) 
const xiehouyuDictionary = ref([]) 

// --- 数据加载状态 --- 
const wordEntryDataLoaded = ref(false)
const xiehouyuDataLoaded = ref(false)
const isLoadingSpecificDictionary = ref(false) // 用于按需加载时的临时加载状态

const loading = ref(true) // 主加载状态，用于初始加载
const isExtracting = ref(false)

const activeDictionaryType = ref('xiehouyu') // 默认尝试加载歇后语

const currentActiveDictionary = computed(() => {
  if (activeDictionaryType.value === 'word_entry') {
    return wordEntryDictionary.value
  } else if (activeDictionaryType.value === 'xiehouyu') {
    return xiehouyuDictionary.value
  }
  return [] 
})

const selectedLengths = ref([2, 3, 4]) 
const extractCount = ref(10)
const searchKeyword = ref('')
const searchMode = ref('word') 
const isSearching = ref(false)
const isSearchResult = ref(false)
const selectedWords = ref([])
const detailVisible = ref(false)
const currentWord = ref(null)

const totalWords = computed(() => currentActiveDictionary.value.length)

const availableCategories = computed(() => {
    const categories = new Set()
  currentActiveDictionary.value.forEach(item => {
    if (item.category) categories.add(item.category)
  })
  return Array.from(categories).map(category => ({ value: category, label: category })).sort((a, b) => a.label.localeCompare(b.label, 'zh-CN'))
})

const getCategoryLabel = (item) => {
  if (!item) return '未分类'
  return item.category || (item.type === 'xiehouyu' ? '歇后语' : (item.type === 'word_entry' ? '词汇' : '未分类'))
}

const formatPinyin = (pinyin) => {
  if (!pinyin) return '';
  if (/[^a-zA-Zāáǎàēéěèīíǐìōóǒòūúǔùǖǘǚǜü\s]/.test(pinyin)) return `【${pinyin}】`;
    return `【${pinyin}】`;
  }
  
// 按需加载特定词典的函数
const loadSpecificDictionary = async (type) => {
  if (type === 'word_entry' && !wordEntryDataLoaded.value) {
    isLoadingSpecificDictionary.value = true;
    try {
      const wordModule = await import('@/constants/word.json');
      let wordRaw = wordModule.default || wordModule;
      if (Array.isArray(wordRaw)) {
        wordEntryDictionary.value = wordRaw.map((item, index) => ({
          type: 'word_entry', id: `word-${index}`, word: item.word, pinyin: item.pinyin || '',
          definition: item.explanation, category: '词汇', abbr: item.abbr
        })).filter(item => item.word && item.word.trim().length > 0);
      } else if (typeof wordRaw === 'object' && wordRaw !== null) {
        wordEntryDictionary.value = Object.values(wordRaw).map((item, index) => ({
          type: 'word_entry', id: `word-${index}`, word: item.word, pinyin: item.pinyin || '',
          definition: item.explanation, category: '词汇', abbr: item.abbr
        })).filter(item => item.word && item.word.trim().length > 0);
                  } else {
        console.warn('Word.json 数据格式不符合预期或为空');
      }
      wordEntryDataLoaded.value = true;
      ElNotification({ title: '成功', message: `现代词汇数据加载完成 (${wordEntryDictionary.value.length} 条)。`, type: 'success' });
    } catch (e) {
      console.error('Failed to load word.json on demand', e);
      ElMessage.error('现代词汇数据加载失败。');
    } finally {
      isLoadingSpecificDictionary.value = false;
    }
  } else if (type === 'xiehouyu' && !xiehouyuDataLoaded.value) {
    isLoadingSpecificDictionary.value = true;
    try {
      const xiehouyuModule = await import('@/constants/xiehouyu.json');
      let xiehouyuRaw = xiehouyuModule.default || xiehouyuModule;
      if (Array.isArray(xiehouyuRaw)) {
        xiehouyuDictionary.value = xiehouyuRaw.map((item, index) => ({
          type: 'xiehouyu', id: `xh-${index}`, word: item.riddle, definition: item.answer,
          pinyin: '', category: '歇后语', riddle: item.riddle, answer: item.answer
        })).filter(item => item.riddle && item.riddle.trim().length > 0);
              } else {
        console.warn('歇后语数据格式不符合预期或为空');
      }
      xiehouyuDataLoaded.value = true;
      ElNotification({ title: '成功', message: `歇后语数据加载完成 (${xiehouyuDictionary.value.length} 条)。`, type: 'success' });
    } catch (e) {
      console.error('Failed to load xiehouyu.json on demand', e);
      ElMessage.error('歇后语数据加载失败。');
    } finally {
      isLoadingSpecificDictionary.value = false;
    }
  }
}

// 初始加载默认词典 (例如 word_entry)
const initialLoad = async () => {
  loading.value = true;
  // 优先加载歇后语
  await loadSpecificDictionary('xiehouyu'); 

  if (xiehouyuDataLoaded.value && xiehouyuDictionary.value.length > 0) {
    activeDictionaryType.value = 'xiehouyu'; // 确保激活的是歇后语
    searchMode.value = 'riddle'; // 设置对应搜索模式
    } else {
    // 如果歇后语加载失败或为空，则尝试加载词汇作为备选
    activeDictionaryType.value = 'word_entry'; // 先尝试切换到 word_entry
    await loadSpecificDictionary('word_entry');
    if (wordEntryDataLoaded.value && wordEntryDictionary.value.length > 0) {
      // word_entry 加载成功
      searchMode.value = 'word';
    } else {
      // 如果两者都失败或为空，恢复 activeDictionaryType 为 xiehouyu (或初始默认)
      activeDictionaryType.value = 'xiehouyu'; 
      searchMode.value = 'riddle'; // 保持对应 searchMode
      ElNotification({
          title: '提示',
          message: '所有词典数据均未能加载，请检查数据文件或网络连接。',
        type: 'warning'
      });
    }
  }
  loading.value = false;
};

watch(activeDictionaryType, async (newType, oldType) => {
  selectedWords.value = [];
  searchKeyword.value = '';
  isSearchResult.value = false;

  if (newType === 'word_entry') {
    searchMode.value = 'word';
    if (!wordEntryDataLoaded.value) {
      await loadSpecificDictionary('word_entry');
    }
  } else if (newType === 'xiehouyu') {
    searchMode.value = 'riddle';
    if (!xiehouyuDataLoaded.value) {
      await loadSpecificDictionary('xiehouyu');
    }
  }
});

const extractRandomWords = () => {
  const source = currentActiveDictionary.value;
  if (source.length === 0) {
    ElMessage.warning(`当前${activeDictionaryType.value === 'word_entry' ? '词汇' : '歇后语'}词典数据为空，无法抽取`);
    return;
  }
  isExtracting.value = true
  selectedWords.value = []
  try {
    let filteredWords = source.filter(item => {
      let lengthMatch = true;
      const textToMeasure = item.type === 'xiehouyu' ? item.riddle : item.word;
      if (selectedLengths.value.length > 0) { 
        lengthMatch = selectedLengths.value.includes(textToMeasure?.length || 0);
      }
      return lengthMatch;
    });
    
    if (filteredWords.length === 0) {
      ElMessage.warning('没有符合条件的条目，请调整筛选条件')
      isExtracting.value = false
      return
    }
    const count = Math.min(extractCount.value, filteredWords.length)
    const result = []
    while (result.length < count && filteredWords.length > 0) {
      const randomIndex = Math.floor(Math.random() * filteredWords.length)
      result.push(filteredWords[randomIndex])
      filteredWords.splice(randomIndex, 1)
    }
    selectedWords.value = result
    isSearchResult.value = false
    ElMessage.success(`成功抽取 ${result.length} 个条目`)
  } catch (error) {
    console.error('随机抽取失败:', error)
    ElMessage.error('抽取失败，请重试')
  } finally {
    isExtracting.value = false
  }
};

const searchDictionary = () => {
  const source = currentActiveDictionary.value;
   if (isLoadingSpecificDictionary.value) {
    ElMessage.warning('词典数据正在加载中，请稍候...');
    return;
  }
  if (source.length === 0) {
    ElMessage.warning(`当前${activeDictionaryType.value === 'word_entry' ? '词汇' : '歇后语'}词典数据为空，无法搜索`);
    return;
  }
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  isSearching.value = true
  selectedWords.value = []
  try {
    const keyword = searchKeyword.value.trim().toLowerCase()
    let searchResults = []
    
    if (activeDictionaryType.value === 'word_entry') {
    if (searchMode.value === 'word') {
        searchResults = source.filter(item => item.word && item.word.toLowerCase().includes(keyword))
    } else if (searchMode.value === 'pinyin') {
        searchResults = source.filter(item => item.pinyin && item.pinyin.toLowerCase().includes(keyword))
    } else if (searchMode.value === 'definition') {
        searchResults = source.filter(item => item.definition && item.definition.toLowerCase().includes(keyword))
      } else { 
         searchResults = source.filter(item => 
            (item.word && item.word.toLowerCase().includes(keyword)) ||
            (item.definition && item.definition.toLowerCase().includes(keyword))
        );
      }
    } else if (activeDictionaryType.value === 'xiehouyu') {
      if (searchMode.value === 'riddle' || searchMode.value === 'word') { 
        searchResults = source.filter(item => item.riddle && item.riddle.toLowerCase().includes(keyword))
      } else if (searchMode.value === 'answer' || searchMode.value === 'definition') { 
        searchResults = source.filter(item => item.answer && item.answer.toLowerCase().includes(keyword))
      } else { 
         searchResults = source.filter(item => 
            (item.riddle && item.riddle.toLowerCase().includes(keyword)) ||
            (item.answer && item.answer.toLowerCase().includes(keyword))
        );
      }
    }
    
    if (searchResults.length === 0) {
      let typeText = activeDictionaryType.value === 'word_entry' ? '词汇' : '歇后语';
      ElMessage.warning(`在${typeText}中未找到相关条目`)
      isSearching.value = false
      return
    }
    const maxResults = 100
    if (searchResults.length > maxResults) {
      selectedWords.value = searchResults.slice(0, maxResults)
      ElMessage.info(`共找到 ${searchResults.length} 个结果，仅显示前 ${maxResults} 个`)
    } else {
      selectedWords.value = searchResults
      ElMessage.success(`找到 ${searchResults.length} 个结果`)
    }
    isSearchResult.value = true
  } catch (error) {
    console.error('搜索词典失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    isSearching.value = false
  }
};

const copyToClipboard = () => {
  if (selectedWords.value.length === 0) {
    ElMessage.warning('没有可复制的内容')
    return
  }
  const content = selectedWords.value.map(item => {
    let text = '';
    if (item.type === 'word_entry') { 
      text = item.word || ''
      if (item.pinyin) {
        text += ` ${formatPinyin(item.pinyin)}`
      }
      if (item.definition) {
        text += `：${item.definition}`
      }
      if (item.abbr) { 
        text += ` (缩写: ${item.abbr})`
      }
    } else if (item.type === 'xiehouyu') {
      text = `歇后语：${item.riddle} —— ${item.answer}`
    }
    return text
  }).join('\n')
  window.pywebview.api.copy_to_clipboard(content)
    .then(() => {
      ElMessage.success('已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      try {
        const textArea = document.createElement("textarea");
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        ElMessage.success('已复制到剪贴板 (备用方法)');
      } catch (execErr) {
        console.error('备用复制方法也失败:', execErr);
        ElMessage.error('复制失败，请手动复制');
      }
    })
};

const showWordDetail = (item) => {
  currentWord.value = item 
  detailVisible.value = true
};

onMounted(() => {
  initialLoad(); // 调用初始加载函数
});
</script>

<style scoped>
.chinese-dictionary {
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  background: rgba(240, 245, 255, 0.5);
  position: relative;
  overflow: hidden;
  padding: 0;
  user-select: none;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(135deg, rgba(235, 245, 255, 0.7) 0%, rgba(245, 250, 255, 0.7) 100%);
}

.bg-circles {
  display: none;
}

.app-container {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.page-header {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-grow: 1;
  margin-right: 16px;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: clamp(24px, 5vw, 30px);
  color: var(--el-color-primary-dark-2);
  margin: 0 0 8px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.subtitle {
  font-size: clamp(14px, 3vw, 16px);
  color: var(--el-text-color-secondary);
  margin: 0;
}

.dictionary-type-selector-wrapper {
  flex-shrink: 0;
}

.dictionary-type-selector {
  display: flex;
  justify-content: center;
  padding: 0;
}

.dictionary-type-selector .el-radio-button {
}

.dictionary-type-selector .el-radio-button__inner {
  font-size: 14px;
  padding: 8px 15px;
}

.main-content {
  display: flex;
  gap: 20px;
  flex: 1;
  position: relative;
  min-height: 0;
  flex-direction: column;
}

.settings-panel {
  width: 100%;
  flex-shrink: 0;
  margin-bottom: 20px;
}

.results-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.glass-card {
  background: white;
  border-radius: 16px;
  box-shadow: 
    0 2px 12px rgba(0, 0, 0, 0.05),
    0 1px 4px rgba(0, 0, 0, 0.02);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
}

.settings-panel .glass-card {
  /* 左侧面板的卡片高度根据内容自适应 */
}

.results-panel .result-card, 
.results-panel .empty-tip {
  flex: 1;
  min-height: 300px;
}

.card-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f9ff;
  border-bottom: 1px solid rgba(var(--el-color-primary-rgb), 0.1);
  flex-shrink: 0;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-color-primary-dark-2);
  display: flex;
  align-items: center;
  gap: 8px;
}

.count-badge,
.result-count {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  border: 1px solid;
  white-space: nowrap;
}

.count-badge {
  background: rgba(var(--el-color-info-rgb), 0.1);
  color: var(--el-color-info-dark-2);
  border-color: rgba(var(--el-color-info-rgb), 0.2);
}

.result-count {
  background: rgba(var(--el-color-success-rgb), 0.1);
  color: var(--el-color-success-dark-2);
  border-color: rgba(var(--el-color-success-rgb), 0.2);
}

.settings {
  display: flex;
  flex-direction: column;
  gap: 18px;
  padding: 20px;
}

.search-section {
  padding: 20px;
}

.search-box {
  margin-bottom: 15px;
}

.search-options {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  flex-wrap: wrap;
  gap: 8px;
}

.search-options .el-radio-button {
  margin-bottom: 5px;
}

.separator {
  display: flex;
  align-items: center;
  margin: 10px 20px 15px;
}

.separator-line {
  flex: 1;
  height: 1px;
  background: rgba(var(--el-color-primary-rgb), 0.1);
}

.separator-text {
  padding: 0 12px;
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 15px;
}

.setting-control {
  width: 100%;
}
.setting-control .el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.actions {
  margin-top: 24px;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.button-group .el-button {
  flex-grow: 1;
  min-width: 120px;
  max-width: 200px;
  border-radius: 8px;
  padding: 10px 0;
  font-size: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.extract-button {
  background: var(--el-color-primary);
  border: none;
}

.extract-button:hover {
  background: var(--el-color-primary-dark-1);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.copy-button {
  background: var(--el-color-success);
  border: none;
}

.copy-button:hover {
  background: var(--el-color-success-dark-1);
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  gap: 12px;
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}

.large-grid {
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
}

.medium-grid {
  grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
}

.small-grid {
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
}

.word-card {
  background: white;
  border-radius: 10px;
  padding: 12px;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.03),
    0 1px 3px rgba(0, 0, 0, 0.01);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.word-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 6px 16px rgba(0, 0, 0, 0.06),
    0 3px 6px rgba(0, 0, 0, 0.03);
  border-color: rgba(var(--el-color-primary-rgb), 0.3);
  background: linear-gradient(135deg, 
    white 0%, 
    rgba(var(--el-color-primary-rgb), 0.05) 100%);
  z-index: 1;
}

.word-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.word-text {
  font-size: clamp(18px, 3.5vw, 22px);
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--el-color-primary-dark-2);
  word-break: break-all;
  user-select: text;
}

.riddle-text {
  font-size: clamp(16px, 3vw, 20px);
}

.word-pinyin {
  font-size: clamp(12px, 2.5vw, 14px);
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
  word-break: break-all;
  user-select: text;
}

.xiehouyu-answer {
  font-size: clamp(13px, 2.8vw, 15px);
  color: var(--el-color-success-dark-1);
  font-style: italic;
  margin-top: 4px;
  font-weight: 500;
  user-select: text;
}

.word-footer {
  display: flex;
  justify-content: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed rgba(var(--el-color-primary-rgb), 0.1);
}

.word-category {
  font-size: 11px;
  color: var(--el-color-info-dark-1);
  background: rgba(var(--el-color-info-rgb), 0.08);
  padding: 3px 8px;
  border-radius: 20px;
  border: 1px solid rgba(var(--el-color-info-rgb), 0.15);
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--el-text-color-secondary);
  height: 100%;
  position: relative;
  overflow: hidden;
}

.empty-tip .el-icon {
  font-size: 50px;
  margin-bottom: 18px;
  color: rgba(var(--el-color-primary-rgb), 0.5);
  animation: pulse 2s infinite ease-in-out;
}

.empty-tip p {
  font-size: clamp(16px, 3vw, 18px);
  margin: 0;
  text-align: center;
  max-width: 350px;
  line-height: 1.6;
  padding: 0 15px;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.08); opacity: 1; }
  100% { transform: scale(1); opacity: 0.7; }
}

.loading-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  flex: 1;
}

.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
  color: var(--el-text-color-secondary);
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 18px;
  border-radius: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.loading-icon {
  font-size: 16px;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 词语详情弹窗样式 */
.word-detail {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 18px 22px;
  border-bottom: 1px solid rgba(var(--el-color-primary-rgb), 0.1);
  background: rgba(var(--el-color-primary-rgb), 0.05);
}

.word-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-right: 10px;
}

.word-title h3 {
  margin: 0;
  font-size: clamp(22px, 4vw, 28px);
  color: var(--el-color-primary-dark-2);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-break: break-word;
  user-select: text;
}

.detail-category {
  background: rgba(var(--el-color-success-rgb), 0.1);
  color: var(--el-color-success-dark-2);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid rgba(var(--el-color-success-rgb), 0.2);
  white-space: nowrap;
  flex-shrink: 0;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 18px;
  padding: 0 22px 22px;
  max-height: 60vh;
  overflow-y: auto;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  padding-bottom: 6px;
  border-bottom: 1px dashed rgba(var(--el-color-primary-rgb), 0.15);
}

.section-content {
  padding: 8px 12px;
  color: var(--el-text-color-regular);
  line-height: 1.7;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  font-size: 14px;
  user-select: text;
}

.multi-definitions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.definition-item {
  margin: 0;
  padding: 6px 10px;
  background: rgba(var(--el-color-primary-rgb), 0.03);
  border-radius: 6px;
  border-left: 3px solid rgba(var(--el-color-primary-rgb), 0.2);
  position: relative;
  user-select: text;
}

.definition-item + .definition-item {
  margin-top: 5px;
}

.definition-item:not(:last-child)::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 10%;
  right: 10%;
  height: 1px;
  background: rgba(var(--el-color-primary-rgb), 0.1);
}

/* 暗色主题适配 */
.dark .chinese-dictionary {
  background: linear-gradient(135deg, #1a1e2a 0%, #121520 100%);
}

.dark .bg-gradient {
  background: 
    radial-gradient(circle at 20% 20%, rgba(var(--el-color-primary-rgb), 0.2) 0%, transparent 40%),
    radial-gradient(circle at 80% 80%, rgba(var(--el-color-success-rgb), 0.15) 0%, transparent 40%);
}

.dark .glass-card {
  background: rgba(30, 30, 35, 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.2),
    0 1px 8px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.dark .card-header {
  background: rgba(20, 20, 25, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .card-header h3 {
  color: rgba(255, 255, 255, 0.9);
}

.dark .count-badge,
.dark .result-count {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.8);
}

.dark .empty-tip .el-icon {
  color: rgba(var(--el-color-primary-rgb), 0.7);
}

.dark .empty-tip p {
  color: rgba(255, 255, 255, 0.7);
}

.dark .loading-text {
  background: rgba(30, 30, 35, 0.7);
  color: rgba(255, 255, 255, 0.8);
}

.dark .word-detail {
  background: rgba(30, 30, 35, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .detail-header {
  background: rgba(var(--el-color-primary-rgb), 0.15);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .word-title h3 {
  color: rgba(255, 255, 255, 0.95);
}

.dark .word-pinyin {
  color: rgba(255, 255, 255, 0.7);
}

.dark .detail-category {
  background: rgba(var(--el-color-success-rgb), 0.2);
  color: rgba(255,255,255,0.9);
  border-color: rgba(var(--el-color-success-rgb), 0.3);
}

.dark .section-title {
  color: rgba(255, 255, 255, 0.9);
  border-bottom: 1px dashed rgba(255, 255, 255, 0.15);
}

.dark .section-content {
  background: rgba(40, 40, 50, 0.5);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.dark .definition-item {
  background: rgba(var(--el-color-primary-rgb), 0.1);
  border-left: 3px solid rgba(var(--el-color-primary-rgb), 0.3);
}

.dark .word-card {
  background: rgba(40, 40, 50, 0.6);
  border-color: rgba(255, 255, 255, 0.08);
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.25),
    0 2px 6px rgba(0, 0, 0, 0.15);
}

.dark .word-card:hover {
  background: linear-gradient(135deg, 
    rgba(45, 45, 55, 0.8) 0%, 
    rgba(var(--el-color-primary-rgb), 0.25) 100%);
  border-color: rgba(var(--el-color-primary-rgb), 0.35);
  box-shadow: 
    0 12px 30px rgba(0, 0, 0, 0.3),
    0 6px 15px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.dark .word-footer {
  border-top: 1px dashed rgba(255, 255, 255, 0.1);
}

.dark .word-category {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* 响应式布局核心调整 */
@media (min-width: 992px) {
  .main-content {
    flex-direction: row;
  }
  .settings-panel {
    width: 350px;
    margin-bottom: 0;
  }
  .results-panel {
    /* 在行布局中，结果面板将自动填充剩余空间 */
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }
  .page-header {
    flex-direction: column;
  align-items: center;
    text-align: center;
  }
  .header-content {
    user-select: none;
  align-items: center;
    margin-right: 0;
    margin-bottom: 16px;
  }
  .title {
  gap: 8px;
}
  .settings {
    padding: 15px;
    gap: 15px;
  }
  .search-section {
    padding: 15px;
  }
.card-header {
    padding: 12px 15px;
  }
.card-header h3 {
    font-size: 17px;
  }
  .result-grid {
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 10px;
    padding: 10px;
  }
  .word-card {
    padding: 10px;
  }
  .word-text {
    margin-bottom: 2px;
  }
  .word-pinyin {
    margin-bottom: 6px;
  }
  .dialog-width-responsive {
    width: 90vw !important;
    max-width: 600px;
  }
  .detail-header {
    padding: 15px 18px;
  }
  .detail-content {
    padding: 0 18px 18px;
  }
}

/* 确保el-dialog的宽度可以在小屏幕上更好地适应 */
:global(.el-dialog.detail-dialog-bg) {
    max-width: 95vw;
}

@media (min-width: 600px) {
    :global(.el-dialog.detail-dialog-bg) {
        /* 对于宽度大于600px的屏幕，可以恢复到js中设置的宽度，或特定值 */
    }
}

/* 歇后语详情弹窗中的谜底样式 */
.dialog-xiehouyu-answer {
  color: var(--el-color-success-dark-1); /* 与卡片中的颜色保持一致 */
  font-weight: 500; /* 可以考虑加粗或保持默认 */
  font-style: italic; /* 可以考虑斜体或保持默认 */
}

.dark .dialog-xiehouyu-answer {
  color: var(--el-color-success-light-3); /* 暗黑模式下与卡片中的颜色保持一致 */
}

.dark .xiehouyu-answer {
  color: var(--el-color-success-light-3); /* 暗黑模式下使用亮一点的成功色 */
}

/* 确保搜索输入框可选择 */
.search-box :deep(input) {
  user-select: text;
}

/* 确保拼音可选择 */
.word-pinyin {
  font-size: clamp(12px, 2.5vw, 14px);
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
  word-break: break-all;
  user-select: text;
}

/* 确保词条标题可选择 */
.word-title h3 {
  margin: 0;
  font-size: clamp(22px, 4vw, 28px);
  color: var(--el-color-primary-dark-2);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-break: break-word;
  user-select: text;
}

/* 确保定义项可选择 */
.definition-item {
  margin: 0;
  padding: 6px 10px;
  background: rgba(var(--el-color-primary-rgb), 0.03);
  border-radius: 6px;
  border-left: 3px solid rgba(var(--el-color-primary-rgb), 0.2);
  position: relative;
  user-select: text;
}

</style> 