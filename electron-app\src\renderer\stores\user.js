import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref({
    username: '',
    isActivated: false,
    activationType: 'trial',
    trialDaysLeft: 30,
    features: {}
  })

  const isLoggedIn = ref(false)
  const loading = ref(false)

  // 计算属性
  const isActivated = computed(() => userInfo.value.isActivated)
  const activationType = computed(() => userInfo.value.activationType)
  const trialDaysLeft = computed(() => userInfo.value.trialDaysLeft)

  // 方法
  async function login(credentials) {
    loading.value = true
    try {
      const result = await window.pywebview.api.login(credentials)
      
      if (result.status === 'success') {
        isLoggedIn.value = true
        userInfo.value.username = credentials.username
        
        // 获取激活状态
        await checkActivationStatus()
        
        return result
      } else {
        throw new Error(result.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  async function logout() {
    try {
      // 调用后端登出API
      await window.pywebview.api.logout?.()
      
      // 清除本地状态
      isLoggedIn.value = false
      userInfo.value = {
        username: '',
        isActivated: false,
        activationType: 'trial',
        trialDaysLeft: 30,
        features: {}
      }
      
      return true
    } catch (error) {
      console.error('登出失败:', error)
      throw error
    }
  }

  async function checkActivationStatus() {
    try {
      const result = await window.pywebview.api.check_activation_status()
      
      if (result.status === 'success') {
        userInfo.value = {
          ...userInfo.value,
          isActivated: result.data.isActivated,
          activationType: result.data.activationType,
          trialDaysLeft: result.data.trialDaysLeft || 0,
          features: result.data.features || {}
        }
      }
      
      return result
    } catch (error) {
      console.error('检查激活状态失败:', error)
      throw error
    }
  }

  async function verifyActivationCode(activationData) {
    try {
      const result = await window.pywebview.api.verify_activation_code(activationData)
      
      if (result.status === 'success') {
        // 重新检查激活状态
        await checkActivationStatus()
      }
      
      return result
    } catch (error) {
      console.error('验证激活码失败:', error)
      throw error
    }
  }

  async function getMachineCode() {
    try {
      const result = await window.pywebview.api.get_machine_code()
      return result
    } catch (error) {
      console.error('获取机器码失败:', error)
      throw error
    }
  }

  // 检查功能权限
  function hasFeature(featureName) {
    const features = userInfo.value.features
    if (!features) return false
    
    const featureValue = features[featureName]
    
    // 如果是数字类型，-1表示无限制，0表示禁用，正数表示限制数量
    if (typeof featureValue === 'number') {
      return featureValue !== 0
    }
    
    // 如果是布尔类型，直接返回
    if (typeof featureValue === 'boolean') {
      return featureValue
    }
    
    // 如果是数组类型，检查是否包含
    if (Array.isArray(featureValue)) {
      return featureValue.length > 0
    }
    
    return !!featureValue
  }

  // 获取功能限制
  function getFeatureLimit(featureName) {
    const features = userInfo.value.features
    if (!features) return 0
    
    const featureValue = features[featureName]
    
    if (typeof featureValue === 'number') {
      return featureValue
    }
    
    return featureValue ? -1 : 0
  }

  // 检查是否可以使用某个功能
  function canUseFeature(featureName, currentUsage = 0) {
    const limit = getFeatureLimit(featureName)
    
    // -1 表示无限制
    if (limit === -1) return true
    
    // 0 表示禁用
    if (limit === 0) return false
    
    // 正数表示限制数量
    return currentUsage < limit
  }

  // 初始化用户状态
  async function initialize() {
    try {
      // 检查是否已登录（可以通过检查激活状态来判断）
      const activationResult = await checkActivationStatus()
      
      if (activationResult.status === 'success') {
        isLoggedIn.value = true
        // 这里可以设置一个默认用户名或从其他地方获取
        userInfo.value.username = 'User'
      }
    } catch (error) {
      console.error('初始化用户状态失败:', error)
      // 初始化失败不抛出错误，保持默认状态
    }
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    loading,
    
    // 计算属性
    isActivated,
    activationType,
    trialDaysLeft,
    
    // 方法
    login,
    logout,
    checkActivationStatus,
    verifyActivationCode,
    getMachineCode,
    hasFeature,
    getFeatureLimit,
    canUseFeature,
    initialize
  }
})
