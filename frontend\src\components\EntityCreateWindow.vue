<template>
  <div>
    <FloatingWindow
      ref="windowRef"
      title="新建实体"
      :visible="visible"
      :initial-position="windowConfig.position"
      :width="windowConfig.size.width"
      :height="windowConfig.size.height"
      :pinnable="true"
      class="entity-create-window"
      @close="emit('update:visible', false)"
      @move="handleWindowMove"
      @resize="handleWindowResize"
    >
      <template #title-extra>
        <div class="title-extra">
          <el-tooltip
            content="从JSON导入实体"
            placement="bottom"
            effect="light"
          >
            <el-button
              type="success"
              size="small"
              :disabled="!entityForm.template_id"
              @click="showImportDialog"
              class="import-btn"
            >
              导入
            </el-button>
          </el-tooltip>
          <el-tooltip
            content="Ctrl + Enter 快速创建"
            placement="bottom"
            effect="light"
          >
            <el-button
              type="primary"
              size="small"
              :disabled="!entityForm.template_id"
              @click="saveEntity"
              class="create-btn"
            >
              创建
            </el-button>
          </el-tooltip>
        </div>
      </template>

      <div class="window-content">
        <el-form :model="entityForm" label-width="100px">
          <el-form-item label="选择模板" required>
            <el-select
              v-model="entityForm.template_id"
              placeholder="请选择模板"
              class="template-select"
              @change="handleTemplateSelect"
            >
              <el-option
                v-for="template in templateList"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span class="template-name">{{ template.name }}</span>
                  <span class="template-info">
                    {{ template.dimensions?.length || 0 }}个维度
                  </span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="实体名称" required>
            <el-input
              v-model="entityForm.name"
              placeholder="输入实体名称"
              clearable
              @keyup.enter="saveEntity"
            />
          </el-form-item>

          <el-form-item label="描述">
            <el-input
              v-model="entityForm.description"
              type="textarea"
              :rows="3"
              placeholder="输入实体描述"
            />
          </el-form-item>

          <!-- 动态维度表单项 -->
          <template v-if="currentTemplate">
            <el-divider content-position="left">
              <div class="divider-content">
                <span>维度信息</span>
                <el-tag size="small" type="info" effect="plain" class="dimension-count">
                  {{ currentTemplate.dimensions.length }}个维度
                </el-tag>
              </div>
            </el-divider>
            <el-form-item
              v-for="dimension in currentTemplate.dimensions"
              :key="dimension.name"
              :label="dimension.name"
            >
              <el-input
                v-model="entityForm.dimensions[dimension.name]"
                :placeholder="'输入' + dimension.name"
                type="textarea"
                :rows="2"
                class="dimension-input"
              />
            </el-form-item>
          </template>
        </el-form>
      </div>

      <template #footer>
        <!-- <div class="window-footer">
          <el-button @click="emit('update:visible', false)">取消</el-button>
          <el-button type="primary" @click="saveEntity">保存</el-button>
        </div> -->
      </template>
    </FloatingWindow>

    <!-- 导入实体对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入实体"
      width="600px"
      class="import-dialog"
      :append-to-body="true"
    >
      <div class="import-content">
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="format-hint-title">
                <el-icon><InfoFilled /></el-icon>
                <span>查看JSON格式示例</span>
              </div>
            </template>
            <div class="format-hint-content">
              <pre>{
  "name": "实体名称",
  "description": "实体描述",
  "dimensions": {
    "维度1": "值1",
    "维度2": "值2"
  }
}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-input
          v-model="importJsonContent"
          type="textarea"
          :rows="10"
          placeholder="请输入JSON字符串"
          class="import-input"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确认导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useConfigStore } from '../stores/config'
import FloatingWindow from './FloatingWindow.vue'

const props = defineProps({
  visible: Boolean,
  bookId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['close', 'created', 'update:visible'])

// 弹窗引用
const windowRef = ref(null)

const configStore = useConfigStore()

// 实体相关状态
const templateList = ref([])
const entityForm = ref({
  template_id: '',
  name: '',
  description: '',
  dimensions: {}
})
const currentTemplate = ref(null)

// 导入相关状态
const importDialogVisible = ref(false)
const importJsonContent = ref('')

// 计算窗口配置
const windowConfig = computed(() => {
  const editorConfig = configStore.state.config.editor
  return editorConfig?.entityWindow || {
    position: { x: 120, y: 120 },
    size: { width: 600, height: 500 }
  }
})

// 窗口事件处理
const handleWindowMove = (position) => {
  configStore.updateConfigItem('editor.entityWindow.position', position)
}

const handleWindowResize = (size) => {
  configStore.updateConfigItem('editor.entityWindow.size', size)
}

// 加载模板列表
const loadTemplates = async () => {
  try {
    const response = await window.pywebview.api.book_controller.get_templates(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      templateList.value = result.data || []
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败：' + error.message)
  }
}

// 处理模板选择
const handleTemplateSelect = (templateId) => {
  currentTemplate.value = templateList.value.find(t => t.id === templateId)
  if (currentTemplate.value) {
    entityForm.value.dimensions = currentTemplate.value.dimensions.reduce((acc, dim) => {
      acc[dim.name] = ''
      return acc
    }, {})
  }
}

// 保存实体
const saveEntity = async () => {
  try {
    if (!entityForm.value.template_id) {
      ElMessage.error('请选择模板')
      return
    }
    if (!entityForm.value.name) {
      ElMessage.error('请输入实体名称')
      return
    }

    // 将空维度设置为"未设定"
    if (currentTemplate.value) {
      currentTemplate.value.dimensions.forEach(dim => {
        if (!entityForm.value.dimensions[dim.name] || entityForm.value.dimensions[dim.name].trim() === '') {
          entityForm.value.dimensions[dim.name] = '未设定'
        }
      })
    }

    const response = await window.pywebview.api.book_controller.save_entity({
      ...entityForm.value,
      book_id: props.bookId
    })
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {

      emit('created')
      ElMessage.success('实体创建成功')
      // 保存当前的模板ID
      const currentTemplateId = entityForm.value.template_id
      
      // 重置表单但保持当前模板
      entityForm.value = {
        template_id: currentTemplateId, // 保持当前模板选中
        name: '',
        description: '',
        dimensions: {}
      }
      
      // 重新初始化维度
      if (currentTemplate.value) {
        entityForm.value.dimensions = currentTemplate.value.dimensions.reduce((acc, dim) => {
          acc[dim.name] = ''
          return acc
        }, {})
      }
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存实体失败:', error)
    ElMessage.error('保存失败：' + error.message)
  }
}

// 显示导入对话框
const showImportDialog = () => {
  if (!entityForm.value.template_id) {
    ElMessage.error('请先选择模板')
    return
  }
  importDialogVisible.value = true
  importJsonContent.value = ''
}

// 确认导入
const confirmImport = async () => {
  try {
    if (!importJsonContent.value.trim()) {
      ElMessage.error('请输入JSON字符串')
      return
    }

    // 解析JSON
    const jsonContent = JSON.parse(importJsonContent.value)
    
    // 验证必要字段
    if (!jsonContent.name) {
      ElMessage.error('导入失败：缺少实体名称')
      return
    }

    // 获取当前模板
    const targetTemplateId = entityForm.value.template_id
    if (!targetTemplateId) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 获取当前模板
    const template = templateList.value.find(t => t.id === targetTemplateId)
    if (!template) {
      ElMessage.error('模板不存在')
      return
    }

    // 检查是否有未定义的维度
    const unknownDimensions = Object.keys(jsonContent.dimensions).filter(
      dim => !template.dimensions.some(d => d.name === dim)
    )
    if (unknownDimensions.length > 0) {
      ElMessage.warning(`以下维度在模板中未定义，将被忽略: [${unknownDimensions.join(', ')}]`)
      // 从dimensions中移除未定义的维度
      unknownDimensions.forEach(dim => {
        delete jsonContent.dimensions[dim]
      })
    }

    // 检查实体名称是否已存在
    const response = await window.pywebview.api.book_controller.get_entities(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      const existingEntities = result.data || []
      const isDuplicate = existingEntities.some(entity => 
        entity.name === jsonContent.name && entity.template_id === targetTemplateId
      )
      
      if (isDuplicate) {
        ElMessage.error('导入失败：该模板下已存在同名实体')
        return
      }
    }

    // 准备实体数据
    const entityData = {
      name: jsonContent.name,
      description: jsonContent.description || '',
      dimensions: {},
      template_id: targetTemplateId,
      book_id: props.bookId
    }

    // 处理维度数据，确保所有维度值都是字符串类型，如果维度不存在则设置为"未设定"
    template.dimensions.forEach(dim => {
      const value = jsonContent.dimensions[dim.name]
      entityData.dimensions[dim.name] = value !== undefined && value !== null 
        ? String(value)
        : '未设定'
    })

    // 保存实体
    const saveResponse = await window.pywebview.api.book_controller.save_entity(entityData)
    const saveResult = typeof saveResponse === 'string' ? JSON.parse(saveResponse) : saveResponse
    
    if (saveResult.status === 'success') {
      ElMessage.success('导入成功')
      importDialogVisible.value = false
      emit('created')
      
      // 重置表单但保持当前模板
      entityForm.value = {
        template_id: targetTemplateId,
        name: '',
        description: '',
        dimensions: {}
      }
      
      // 重新初始化维度
      if (currentTemplate.value) {
        entityForm.value.dimensions = currentTemplate.value.dimensions.reduce((acc, dim) => {
          acc[dim.name] = ''
          return acc
        }, {})
      }
    } else {
      throw new Error(saveResult.message || '导入失败')
    }
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('导入失败：JSON格式不正确')
    } else {
      console.error('导入失败:', error)
      ElMessage.error('导入失败：' + error.message)
    }
  }
}

// 监听可见性变化
watch(() => props.visible, async (newVal) => {
  if (newVal && templateList.value.length === 0) {
    await loadTemplates()
  }
})

// 在 script setup 部分添加 keydown 处理
const handleKeydown = (e) => {
  // 只有在弹窗可见且焦点在弹窗内时才处理快捷键
  if (!props.visible) return

  // 检查焦点是否在弹窗内
  const activeElement = document.activeElement
  const windowElement = windowRef.value?.$el
  if (!windowElement || !windowElement.contains(activeElement)) {
    return
  }

  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
    e.preventDefault()
    e.stopPropagation()
    saveEntity()
  }
}

// 监听弹窗可见性变化，只在弹窗可见时添加快捷键监听
watch(() => props.visible, (newVal) => {
  if (newVal) {
    window.addEventListener('keydown', handleKeydown)
  } else {
    window.removeEventListener('keydown', handleKeydown)
  }
})

onMounted(() => {
  // 确保配置存在
  if (!configStore.state.config.editor.entityWindow) {
    configStore.updateConfigItem('editor.entityWindow', {
      position: { x: 120, y: 120 },
      size: { width: 600, height: 500 }
    })
  }
  // 如果弹窗已经可见，添加键盘事件监听
  if (props.visible) {
    window.addEventListener('keydown', handleKeydown)
  }
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.entity-create-window {
  min-width: 320px;
  min-height: 400px;
  
  :deep(.floating-window-content) {
    background: var(--el-bg-color-page);
  }

  :deep(.floating-window-header) {
    .window-title {
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.window-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;

  .el-form {
    height: 100%;
    
    :deep(.el-form-item__label) {
      font-size: 15px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      line-height: 32px;
      padding-right: 24px;
    }
    
    :deep(.el-input__wrapper),
    :deep(.el-textarea__inner) {
      box-shadow: none;
      border: 1px solid var(--el-border-color);
      border-radius: 8px;
      transition: all 0.3s ease;
      background: var(--el-bg-color-blank);
      font-size: 15px;
      line-height: 1.6;
      padding: 8px 16px;
      
      &:hover {
        border-color: var(--el-border-color-darker);
      }
      
      &.is-focus {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
      }

      &::placeholder {
        font-size: 14px;
        color: var(--el-text-color-placeholder);
      }
    }

    :deep(.el-select) {
      .el-input__wrapper {
        padding: 0 16px;
        height: 40px;
        line-height: 40px;
      }
    }
  }

  .template-select {
    width: 100%;
  }

  .el-divider {
    margin: 32px 0 24px;
    
    .divider-content {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 17px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      
      .dimension-count {
        font-weight: normal;
        font-size: 13px;
        padding: 0 8px;
        height: 24px;
        line-height: 22px;
      }
    }
  }
}

.template-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 12px;
}

.template-name {
  flex: 1;
  font-size: 15px;
  font-weight: 500;
}

.template-info {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.dimension-input {
  :deep(.el-textarea__inner) {
    font-size: 15px;
    line-height: 1.6;
    min-height: 80px !important;
    
    &::placeholder {
      color: var(--el-text-color-placeholder);
    }
  }
}

.title-extra {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .create-btn, .import-btn {
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    height: 36px;
    line-height: 20px;
  }

  .create-btn {
    background: rgba(var(--el-color-primary-rgb), 0.1);
    border-color: rgba(var(--el-color-primary-rgb), 0.2);
    color: var(--el-color-primary);

    &:hover:not(:disabled) {
      background: var(--el-color-primary);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
    }

    &:disabled {
      background: var(--el-fill-color-light);
      border-color: var(--el-border-color-lighter);
      color: var(--el-text-color-disabled);
      cursor: not-allowed;
    }
  }

  .import-btn {
    background: rgba(var(--el-color-success-rgb), 0.1);
    border-color: rgba(var(--el-color-success-rgb), 0.2);
    color: var(--el-color-success);

    &:hover:not(:disabled) {
      background: var(--el-color-success);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--el-color-success-rgb), 0.2);
    }

    &:disabled {
      background: var(--el-fill-color-light);
      border-color: var(--el-border-color-lighter);
      color: var(--el-text-color-disabled);
      cursor: not-allowed;
    }
  }
}

.import-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    background: var(--el-bg-color);
    
    .el-dialog__header {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-light);
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      background: var(--el-bg-color-page);
    }
    
    .el-dialog__footer {
      margin: 0;
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
    }
  }

  .import-content {
    .el-collapse {
      margin-bottom: 24px;
    }

    .el-collapse-item {
      .format-hint-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 15px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .format-hint-content {
        padding: 16px;
        background: var(--el-fill-color-blank);
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        margin: 16px 0;
      }
    }

    .import-input {
      margin-top: 16px;
      
      :deep(.el-textarea__inner) {
        font-size: 16px;
        line-height: 1.6;
        padding: 16px;
        border-radius: 8px;
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color);
        color: var(--el-text-color-primary);
        transition: all 0.3s ease;
        min-height: 240px !important;
        
        &:hover {
          border-color: var(--el-border-color-darker);
        }
        
        &:focus {
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
        }
      }
    }
  }
}
</style> 