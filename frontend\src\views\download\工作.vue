<template>
  <div class="download-list">
工作
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Plus, VideoPlay, VideoPause, Delete } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

const loading = ref(false)
const downloadList = ref([
  {
    id: 1,
    name: '示例文件.mp4',
    size: 1024 * 1024 * 100, // 100MB
    progress: 45,
    status: '下载中',
    speed: 1024 * 1024 // 1MB/s
  }
])

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond) => {
  return formatFileSize(bytesPerSecond) + '/s'
}

const getStatusType = (status) => {
  const types = {
    '下载中': 'primary',
    '暂停': 'warning',
    '完成': 'success',
    '错误': 'danger'
  }
  return types[status] || 'info'
}

const startNewDownload = () => {
  // TODO: 实现新建下载功能
  ElMessageBox.prompt('请输入下载链接', '新建下载', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^https?:\/\/.+/,
    inputErrorMessage: '请输入有效的URL'
  }).then(({ value }) => {
    console.log('开始下载:', value)
  })
}

const toggleDownload = (row) => {
  row.status = row.status === '暂停' ? '下载中' : '暂停'
}

const deleteDownload = (row) => {
  ElMessageBox.confirm(
    '确定要删除这个下载任务吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = downloadList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      downloadList.value.splice(index, 1)
    }
  })
}
</script>

<style scoped>
.download-list {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

:deep(.el-button-group) {
  display: flex;
  gap: 4px;
}
</style>
