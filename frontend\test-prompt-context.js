// 测试提示词生成是否能正确获取上下文信息
import { MindmapContextBuilder } from './src/components/mindmap/contextBuilder.js'
import { generatePrompts } from './src/components/mindmap/promptTemplates.js'

// 模拟思维导图数据结构
const mockMindmapData = {
  id: 'root',
  title: '小说创作',
  content: '小说创作知识体系',
  children: [
    {
      id: 'characters',
      title: '人物',
      content: '小说中的角色设定',
      children: [
        {
          id: 'protagonist',
          title: '主角',
          content: '故事的主要人物',
          children: []
        },
        {
          id: 'supporting',
          title: '配角',
          content: '辅助主角的人物',
          children: []
        }
      ]
    },
    {
      id: 'worldbuilding',
      title: '世界设定',
      content: '小说的背景世界',
      children: [
        {
          id: 'geography',
          title: '地理环境',
          content: '世界的地理特征',
          children: []
        }
      ]
    }
  ]
}

console.log('=== 测试提示词上下文信息 ===')

const contextBuilder = new MindmapContextBuilder()

// 测试不同场景的提示词生成
const testCases = [
  {
    name: '小说人物节点 - 子主题模式',
    nodeId: 'characters',
    mode: 'subtopics',
    domain: 'character'
  },
  {
    name: '主角节点 - 要点分解模式',
    nodeId: 'protagonist',
    mode: 'children',
    domain: 'character'
  },
  {
    name: '世界设定节点 - 子主题模式',
    nodeId: 'worldbuilding',
    mode: 'subtopics',
    domain: 'worldbuilding'
  }
]

// 找到节点的辅助函数
const findNode = (data, targetId) => {
  if (data.id === targetId) return data
  if (data.children) {
    for (const child of data.children) {
      const found = findNode(child, targetId)
      if (found) return found
    }
  }
  return null
}

testCases.forEach(testCase => {
  console.log(`\n--- ${testCase.name} ---`)
  
  const testNode = findNode(mockMindmapData, testCase.nodeId)
  if (!testNode) {
    console.log(`❌ 找不到节点: ${testCase.nodeId}`)
    return
  }
  
  // 构建完整上下文
  const fullContext = contextBuilder.buildFullContext(testNode, mockMindmapData, testCase.domain)
  
  console.log('上下文信息:')
  console.log(`- 当前主题: ${fullContext.title}`)
  console.log(`- 层级深度: 第${fullContext.depth}层`)
  console.log(`- 层级路径: ${fullContext.hierarchy.fullPath}`)
  console.log(`- 同级主题: ${fullContext.relationships.siblings.map(s => s.title).join(', ') || '无'}`)
  console.log(`- 已有子主题: ${fullContext.relationships.children.map(c => c.title).join(', ') || '无'}`)
  console.log(`- 当前内容: ${fullContext.content.currentContent || '无'}`)
  
  // 生成提示词
  try {
    const prompts = generatePrompts(testCase.mode, fullContext)
    
    console.log('\n生成的提示词:')
    console.log('系统提示词 (前100字符):')
    console.log(prompts.system.substring(0, 100) + '...')
    
    console.log('\n用户提示词:')
    console.log(prompts.user)
    
    // 检查上下文信息是否正确包含在提示词中
    const userPrompt = prompts.user
    const contextChecks = [
      { name: '当前主题', value: fullContext.title, found: userPrompt.includes(fullContext.title) },
      { name: '层级深度', value: `第${fullContext.depth}层`, found: userPrompt.includes(`第${fullContext.depth}层`) },
      { name: '层级路径', value: fullContext.hierarchy.fullPath, found: userPrompt.includes(fullContext.hierarchy.fullPath) }
    ]
    
    console.log('\n上下文信息检查:')
    contextChecks.forEach(check => {
      console.log(`${check.found ? '✅' : '❌'} ${check.name}: ${check.value}`)
    })
    
    // 检查是否包含同级和子主题信息
    if (fullContext.relationships.siblings.length > 0) {
      const siblingsIncluded = fullContext.relationships.siblings.some(s => userPrompt.includes(s.title))
      console.log(`${siblingsIncluded ? '✅' : '❌'} 同级主题信息`)
    }
    
    if (fullContext.relationships.children.length > 0) {
      const childrenIncluded = fullContext.relationships.children.some(c => userPrompt.includes(c.title))
      console.log(`${childrenIncluded ? '✅' : '❌'} 子主题信息`)
    }
    
  } catch (error) {
    console.log('❌ 提示词生成失败:', error.message)
  }
})

console.log('\n=== 特别测试：小说人物创建 vs 一般人物定义 ===')

// 测试领域特定的提示词
const characterNode = findNode(mockMindmapData, 'characters')
const characterContext = contextBuilder.buildFullContext(characterNode, mockMindmapData, 'character')

console.log('小说人物节点上下文:')
console.log(`- 领域: ${characterContext.domain}`)
console.log(`- 父级: ${characterContext.hierarchy.parentTitle || '无'}`)
console.log(`- 根节点: ${characterContext.hierarchy.rootTitle}`)

const characterPrompts = generatePrompts('subtopics', characterContext)
console.log('\n小说人物子主题提示词:')
console.log(characterPrompts.user)

// 检查是否包含小说创作相关的上下文
const novelContextKeywords = ['小说', '角色', '故事', '创作']
const hasNovelContext = novelContextKeywords.some(keyword => 
  characterPrompts.user.includes(keyword) || 
  characterContext.hierarchy.fullPath.includes(keyword)
)

console.log(`\n${hasNovelContext ? '✅' : '❌'} 包含小说创作上下文`)

if (hasNovelContext) {
  console.log('🎉 提示词能够正确识别这是小说人物创建，而不是一般的人物定义！')
} else {
  console.log('❌ 提示词可能会误解为一般的人物定义')
}
