<template>
  <div :class="['message-bubble-container', { 'from-user': isUser }]">
    <div class="avatar" :class="{ 'user-avatar': isUser, 'ai-avatar': !isUser }">
      <span style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;">{{ isUser ? '我' : 'AI' }}</span>
    </div>
    
    <div class="message-content" :style="messageContentStyle">
      <div class="message-header" style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;"  >
        <div class="sender-name">{{ isUser ? '您' : selectedModel }}</div>
        <div class="message-time">{{ formatTime(timestamp) }}</div>
      </div>
      
      <!-- 思考过程 (仅AI消息) -->
      <div v-if="reasoning && !isUser" class="reasoning-container">
        <div class="reasoning-header" @click="toggleReasoning">
          <div class="reasoning-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"></path>
            </svg>
          </div>
          <div class="reasoning-title">
            思考过程
            <span class="reasoning-time" :class="{ 'thinking': reasoningTime === '思考中...' }">
              {{ reasoningTime }}
            </span>
          </div>
          <div class="reasoning-toggle">
            <svg class="toggle-icon" :class="{ 'is-active': !reasoningCollapsed }" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </div>
        </div>
        
        <div class="reasoning-content" :class="{ 'collapsed': reasoningCollapsed }">
          <div class="markdown-body" v-html="formattedReasoning"></div>
        </div>
      </div>
      
      <!-- 消息内容 -->
      <div 
        class="message-bubble" 
        :class="{ 'user-bubble': isUser, 'ai-bubble': !isUser, 'error-bubble': isError }"
        style="user-select: text !important;"
      >
        <div class="markdown-body" v-html="formattedContent" style="user-select: text !important;"></div>
      </div>
      
      <!-- 消息操作按钮 -->
      <div class="message-actions">
        <button v-if="isUser" class="action-button resend-button" @click="$emit('resend')" :disabled="disabled">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 2v6h-6"></path>
            <path d="M3 12a9 9 0 0 1 15-6.7L21 8"></path>
            <path d="M3 22v-6h6"></path>
            <path d="M21 12a9 9 0 0 1-15 6.7L3 16"></path>
          </svg>
          <span>重发</span>
        </button>

        <!-- 修改复制按钮使用普通的click事件 -->
        <button 
          ref="copyButtonRef" 
          class="action-button copy-button" 
          :class="`copy-button-${isUser ? 'user' : 'ai'}`"
          @click="handleCopy" 
          :disabled="disabled"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
          <span>复制</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onUnmounted, getCurrentInstance } from 'vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import hljs from 'highlight.js/lib/core';
// 强制引入highlight.js样式
import 'highlight.js/styles/github.css';
// 暗色模式样式
import 'highlight.js/styles/github-dark.css';
import { ElMessage } from 'element-plus';

// 添加Array.prototype.at polyfill，解决PyQt5 WebEngine兼容性问题
if (!Array.prototype.at) {
  Array.prototype.at = function(index) {
    // 将负索引转换为从末尾开始的索引
    return index >= 0 ? this[index] : this[this.length + index];
  };
}

// 为String添加相同的polyfill
if (!String.prototype.at) {
  String.prototype.at = function(index) {
    // 将负索引转换为从末尾开始的索引
    return index >= 0 ? this[index] : this[this.length + index];
  };
}

// 注册常用语言
import javascript from 'highlight.js/lib/languages/javascript';
import java from 'highlight.js/lib/languages/java';
import python from 'highlight.js/lib/languages/python';
import xml from 'highlight.js/lib/languages/xml';
import json from 'highlight.js/lib/languages/json';
import bash from 'highlight.js/lib/languages/bash';
import sql from 'highlight.js/lib/languages/sql';
import typescript from 'highlight.js/lib/languages/typescript';
import css from 'highlight.js/lib/languages/css';

hljs.registerLanguage('javascript', javascript);
hljs.registerLanguage('java', java);
hljs.registerLanguage('python', python);
hljs.registerLanguage('xml', xml);
hljs.registerLanguage('html', xml);
hljs.registerLanguage('json', json);
hljs.registerLanguage('bash', bash);
hljs.registerLanguage('sql', sql);
hljs.registerLanguage('typescript', typescript);
hljs.registerLanguage('css', css);

const props = defineProps({
  content: { type: String, required: true },
  isUser: { type: Boolean, default: false },
  isError: { type: Boolean, default: false },
  timestamp: { type: Number, default: () => Date.now() },
  selectedModel: { type: String, default: 'AI助手' },
  reasoning: { type: String, default: '' },
  reasoningTime: { type: String, default: '' },
  disabled: { type: Boolean, default: false },
  hasEditor: { type: Boolean, default: false }
});

const emit = defineEmits(['resend', 'insertCode', 'insert', 'copy-full-message']);

// 控制思考过程的展开/折叠
const reasoningCollapsed = ref(true);

const toggleReasoning = () => {
  reasoningCollapsed.value = !reasoningCollapsed.value;
};

// 代码存储
const codeBlocks = ref({});

// 从配置中获取字体设置
const messageContentStyle = computed(() => {
  return {
    fontSize: `${chatConfig?.fontSize || 16}px`,
    fontFamily: chatConfig?.fontFamily || 'sans-serif'
  };
});

// 获取配置对象
const chatConfig = (() => {
  try {
    // 尝试从window获取配置
    if (window.configStore && window.configStore.chat) {
      return window.configStore.chat;
    }
    // 如果没有配置，返回默认值
    return {
      fontSize: 16,
      fontFamily: '微软雅黑, sans-serif'
    };
  } catch (e) {
    console.error('获取配置失败:', e);
    return {
      fontSize: 16,
      fontFamily: '微软雅黑, sans-serif'
    };
  }
})();

// 检测代码语言
const detectLanguage = (code) => {
  if (!code) return 'plaintext';
  
  // 检测是否是JSON
  if (code.trim().startsWith('{') && code.trim().endsWith('}')) {
    try {
      JSON.parse(code);
      return 'json';
    } catch (e) {
      // 不是有效的JSON
    }
  }
  
  // 检测常见语言特征
  if (code.includes('public class') || code.includes('private class') || 
      code.includes('import java.') || code.includes('@Override')) {
    return 'java';
  }
  
  if (code.includes('func ') && code.includes('package main')) {
    return 'go';
  }
  
  if (code.includes('def ') && code.includes('import ') || 
      code.includes('print(') || code.includes('import os')) {
    return 'python';
  }
  
  if (code.includes('<template>') && code.includes('<script>')) {
    return 'html';
  }
  
  if (code.includes('const ') && code.includes('=>') || 
      code.includes('function(') || code.includes('export default')) {
    return 'javascript';
  }
  
  if (code.includes('<div') || code.includes('<span') || 
      code.includes('<html') || code.includes('</body>')) {
    return 'html';
  }
  
  if (code.includes('SELECT ') && code.includes(' FROM ') || 
      code.includes('INSERT INTO')) {
    return 'sql';
  }
  
  return 'plaintext';
};

// 自定义渲染器，增强代码块处理
const renderer = new marked.Renderer();
renderer.code = (code, language) => {
  // --- Reinstated and refined code and language normalization ---
  let lang = language;
  let codeContent = code;

  // 1. Handle if code is an object (e.g., from complex markdown structures)
  if (codeContent && typeof codeContent === 'object') {
    if (codeContent.type === 'code') { // Check for a specific structure if applicable
      if (codeContent.text) {
        lang = codeContent.lang || lang || 'plaintext';
        codeContent = codeContent.text;
      } else if (codeContent.raw) { // Handle cases where code might be in a 'raw' property
        const rawParts = codeContent.raw.split('\n', 1);
        lang = rawParts[0]?.trim() || codeContent.lang || lang || 'plaintext';
        codeContent = codeContent.raw.substring(rawParts[0].length + 1);
      } else {
        codeContent = JSON.stringify(codeContent, null, 2);
        lang = 'json';
      }
    } else {
      codeContent = JSON.stringify(codeContent, null, 2);
      lang = 'json'; 
    }
  }

  // 2. Ensure codeContent is a string
  if (codeContent === null || typeof codeContent === 'undefined') {
    codeContent = '';
  } else if (typeof codeContent !== 'string') {
    try {
      codeContent = String(codeContent);
    } catch (e) {
      console.error('Code content to string conversion error:', e);
      codeContent = '/* Error converting code to string */';
    }
  }

  // 3. Validate and determine language for highlight.js
  const detectedLang = lang ? lang.toLowerCase().trim() : detectLanguage(codeContent); // Use detectLanguage as fallback
  const finalLang = hljs.getLanguage(detectedLang) ? detectedLang : 'plaintext';
  // --- End of reinstated normalization ---

  const codeId = `code-mb-${Date.now()}-${Math.floor(Math.random() * 100000)}`;
  codeBlocks.value[codeId] = codeContent;

  let highlightedCode;
  try {
    highlightedCode = hljs.highlight(codeContent, { language: finalLang, ignoreIllegals: true }).value;
  } catch (e) {
    console.error('代码高亮错误:', e, 'Lang:', finalLang, 'Original lang:', language);
    try {
      // Fallback to plaintext if specific language highlighting fails
      highlightedCode = hljs.highlight(codeContent, { language: 'plaintext', ignoreIllegals: true }).value;
    } catch (err_plain) {
      console.error('纯文本高亮错误:', err_plain);
      // Final fallback: escape HTML entities
      highlightedCode = codeContent
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;');
    }
  }
  
  const languageDisplay = finalLang === 'plaintext' ? 'text' : finalLang;
  
  const copyCodeButtonHtml = `
    <button class="hljs-code-action-btn hljs-code-copy-specific" onclick="window.copySpecificCodeBlock_MB('${codeId}', this)">
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>
      <span>Copy Code</span>
    </button>
  `;

  const collapseButtonHtml = `
    <button class="hljs-code-action-btn hljs-code-collapse-toggle" onclick="window.toggleCodeBlockCollapse_MB('${codeId}', this)">
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg>
      <span>Collapse</span>
    </button>
  `;

  const wrapButtonHtml = `
    <button class="hljs-code-action-btn hljs-code-wrap-toggle" onclick="window.toggleCodeWrap_MB('${codeId}', this)">
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="7 7 17 7 17 17"></polyline></svg>
      <span>Wrap</span>
    </button>
  `;

  const insertCodeButtonHtml = props.hasEditor ? `
    <button class="hljs-code-action-btn hljs-code-insert-specific" onclick="window.emitInsertCodeEvent_MB('${codeId}', this)">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline></svg>
      <span>Insert Code</span>
    </button>
  ` : '';

  return `
    <div class="hljs-code-wrapper">
      <div class="hljs-code-header">
        <span class="hljs-code-lang">${languageDisplay}</span>
        <div class="hljs-code-actions">
          ${copyCodeButtonHtml}
          ${collapseButtonHtml}
          ${wrapButtonHtml}
          ${insertCodeButtonHtml}
        </div>
      </div>
      <pre class="hljs"><code id="${codeId}" class="language-${finalLang}">${highlightedCode}</code></pre>
    </div>
  `;
};

// 设置marked选项
marked.setOptions({
  renderer: renderer,
  gfm: true,
  breaks: true,
  sanitize: false,
  smartypants: false
});

// 格式化消息内容（带高亮的Markdown渲染）
const formattedContent = computed(() => {
  if (!props.content) return '';
  
  // 处理错误消息
  if (props.isError) {
    return `<div class="error-message">${DOMPurify.sanitize(props.content)}</div>`;
  }
  
  try {
    // 尝试特殊处理JSON
    if (props.content.trim().startsWith('{') && props.content.trim().endsWith('}')) {
      try {
        const jsonObj = JSON.parse(props.content);
        const formattedJson = JSON.stringify(jsonObj, null, 2);
        // 使用代码块渲染JSON
        const renderedContent = marked('```json\n' + formattedJson + '\n```');
        return DOMPurify.sanitize(renderedContent, {
          ADD_ATTR: ['class', 'onclick'],
          ADD_TAGS: ['button', 'pre', 'code']
        });
      } catch (e) {
        // 如果不是有效的JSON，则以普通markdown处理
      }
    }
    
    return DOMPurify.sanitize(marked(props.content), {
      ADD_ATTR: ['class', 'onclick'],
      ADD_TAGS: ['button', 'pre', 'code']
    });
  } catch (error) {
    console.error('渲染Markdown失败:', error);
    return `<pre>${DOMPurify.sanitize(props.content)}</pre>`;
  }
});

// 格式化思考过程
const formattedReasoning = computed(() => {
  if (!props.reasoning) return '';
  return DOMPurify.sanitize(marked(props.reasoning));
});

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  return `${year}/${month}/${day} ${hours}:${minutes}`;
};

// Add ref for the copy button
const copyButtonRef = ref(null);

// Function to show success feedback
const showCopySuccess = () => {
  ElMessage.success('复制成功');
  
  // Visual feedback using the ref
  if (copyButtonRef.value) {
    const originalHTML = copyButtonRef.value.innerHTML;
    const originalClass = copyButtonRef.value.className;
    
    copyButtonRef.value.innerHTML = '<span style="color: var(--el-color-success);">已复制 ✓</span>';
    copyButtonRef.value.className += ' copied-success';
    
    setTimeout(() => {
      if (copyButtonRef.value) {
        copyButtonRef.value.innerHTML = originalHTML;
        copyButtonRef.value.className = originalClass;
      }
    }, 2000);
  }
};

// Show error message with permission hint
const showCopyError = (err) => {
  console.error('复制失败:', err);
  
  // 判断是否可能是权限问题
  const isPossiblePermissionError = err.name === 'NotAllowedError' || 
                                   err.message?.includes('permission') ||
                                   err.message?.includes('denied');
  
  if (isPossiblePermissionError) {
    ElMessage.error('复制失败: 可能需要授予剪贴板访问权限');
  } else {
    ElMessage.error('复制失败: ' + (err.message || '未知错误'));
  }
};

// 更新为更简洁的按钮点击复制逻辑 - 只处理按钮点击
const handleCopy = () => {
  // 检查是否有选择的文本
  const selectedText = window.getSelection().toString();
  
  // 如果用户选择了文本，则复制选择的文本，否则复制整个消息内容
  const textToCopy = selectedText || props.content;
  window.pywebview.api.copy_to_clipboard(textToCopy).then(() => {
    ElMessage.success('复制成功');
  }).catch((err) => {
    ElMessage.error('复制失败');
  });
  

};

// 更新为更简单的回退方法
const fallbackCopy = (textToCopy) => {
  try {
    // 先保存当前选择
    const currentSelection = window.getSelection();
    const selectedRange = currentSelection.rangeCount > 0 ? 
                         currentSelection.getRangeAt(0) : null;
    
    // 使用最基本的DOM API复制文本
    const tempTextArea = document.createElement('textarea');
    tempTextArea.value = textToCopy;
    
    // 确保元素在视觉上隐藏，但仍然存在于DOM中
    Object.assign(tempTextArea.style, {
      position: 'fixed',
      left: '-9999px',
      top: '-9999px',
      opacity: '0'
    });
    
    document.body.appendChild(tempTextArea);
    tempTextArea.select();
    
    const successful = document.execCommand('copy');
    document.body.removeChild(tempTextArea);
    
    // 恢复原来的选择
    if (selectedRange) {
      currentSelection.removeAllRanges();
      currentSelection.addRange(selectedRange);
    }
    
    if (successful) {
      showCopySuccess();
    } else {
      throw new Error('复制失败');
    }
  } catch (err) {
    console.error('Fallback copy failed:', err);
    showCopyError(err);
    
    // 最后尝试通过事件发送
    emit('copy-full-message', textToCopy);
  }
};

// 在组件挂载时初始全局函数
onMounted(() => {
  // 配置DOMPurify允许highlight.js相关的类和属性
  DOMPurify.addHook('uponSanitizeElement', (node, data) => {
    if (data.tagName === 'button' || data.tagName === 'pre' || data.tagName === 'code') {
      // 允许这些标签
      return node;
    }
  });
  
  DOMPurify.addHook('uponSanitizeAttribute', (node, data) => {
    // 允许onclick属性（用于复制代码）
    if (data.attrName === 'onclick' && 
        (data.attrValue.includes('copyCodeToClipboard') || 
         data.attrValue.includes('toggleCodeExpansion'))) {
      data.forceKeepAttr = true;
    }
    
    // 允许class属性，特别是hljs相关的类
    if (data.attrName === 'class') {
      data.forceKeepAttr = true;
    }
  });
  
  // 重新定义全局函数
  window.copySpecificCodeBlock_MB = (codeId, buttonElement) => {
    const codeToCopy = codeBlocks.value[codeId];
    if (codeToCopy) {
      window.pywebview.api.copy_to_clipboard(codeToCopy)
        .then(() => {
          const originalHTML = buttonElement.innerHTML;
          buttonElement.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"></path></svg><span>Copied</span>';
          buttonElement.classList.add('copied');
          setTimeout(() => {
            buttonElement.innerHTML = originalHTML;
            buttonElement.classList.remove('copied');
          }, 2000);
        })
        .catch(err => {
          console.error('Copy specific code failed (MB):', err);
          const originalHTML = buttonElement.innerHTML;
          buttonElement.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg><span>Error</span>';
          setTimeout(() => { buttonElement.innerHTML = originalHTML; }, 2000);
        });
    }
  };

  window.toggleCodeWrap_MB = (codeId, buttonElement) => {
    const codeElement = document.getElementById(codeId);
    const preElement = codeElement ? codeElement.closest('pre.hljs') : null;

    if (preElement && codeElement && buttonElement) {
      const isNowWrapped = preElement.classList.toggle('wrapped');
      codeElement.classList.toggle('wrapped', isNowWrapped);
      
      if (isNowWrapped) {
        buttonElement.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg><span>Unwrap</span>';
        buttonElement.classList.add('active');
      } else {
        buttonElement.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="7 7 17 7 17 17"></polyline></svg><span>Wrap</span>';
        buttonElement.classList.remove('active');
      }
    }
  };

  // This function now emits an event for the parent to handle actual insertion and feedback.
  window.emitInsertCodeEvent_MB = (codeId, buttonElement) => {
    const codeToInsert = codeBlocks.value[codeId];
    if (codeToInsert) {
      emit('insertCode', codeToInsert);
      // Provide immediate button feedback. Parent will handle ElMessage.
      if (buttonElement) {
        const originalHTML = buttonElement.innerHTML;
        buttonElement.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"></path></svg><span>Requested</span>'; // Or 'Emitted'
        buttonElement.classList.add('copied'); // Re-use 'copied' style for temporary feedback
        setTimeout(() => {
          buttonElement.innerHTML = originalHTML;
          buttonElement.classList.remove('copied');
        }, 1500); // Shorter timeout as parent gives main feedback
      }
    }
  };

  window.toggleCodeBlockCollapse_MB = (codeElementId, buttonElement) => {
    const codeTag = document.getElementById(codeElementId);
    const preElement = codeTag ? codeTag.closest('pre.hljs') : null;

    if (preElement && buttonElement) {
      const isNowCollapsed = preElement.classList.toggle('content-collapsed');
      
      if (isNowCollapsed) {
        buttonElement.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg><span>Expand</span>';
        buttonElement.classList.add('active'); 
      } else {
        buttonElement.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg><span>Collapse</span>';
        buttonElement.classList.remove('active');
      }
    }
  };
});

// 组件卸载时清理事件监听和全局对象
onUnmounted(() => {
  // 移除全局函数
  delete window.copySpecificCodeBlock_MB;
  delete window.toggleCodeWrap_MB;
  delete window.emitInsertCodeEvent_MB;
  delete window.toggleCodeBlockCollapse_MB;
  // delete window.codeStorage_MB;
});
</script>

<style lang="scss">
/* 这里使用无作用域的样式以应用于v-html内容 */
.hljs-code-wrapper {
  margin: 12px 0;
  border-radius: 8px;
  overflow-y: auto;
  border: 1px solid var(--code-border-color, #ddd);
  background: var(--code-bg-color, #f8f8f8);
}

.hljs-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--code-header-bg, #e8e8e8);
  border-bottom: 1px solid var(--code-border-color, #ddd);
}

.hljs-code-lang {

  font-size: 12px;
  font-weight: bold;
  color: #666;
  text-transform: uppercase;
}

.hljs-code-actions {
  display: flex;
  gap: 8px;
}

/* APPLY NEW STYLES for .hljs-code-action-btn, mimicking .message-actions .action-button */
.hljs-code-action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px; /* Consistent with .action-button */
  border: none; /* Key: no border */
  background-color: var(--el-fill-color-light, #f5f7fa); /* From .action-button */
  color: var(--el-text-color-secondary, #909399); /* From .action-button */
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  line-height: 1; /* Added for better vertical alignment of text and SVG */
}

.hljs-code-action-btn svg {
  flex-shrink: 0;
  width: 14px; 
  height: 14px;
}

.hljs-code-action-btn:hover {
  background-color: var(--el-fill-color, #f0f2f5);
  color: var(--el-color-primary, #409eff);
}

/* Copied state for code block buttons */
.hljs-code-action-btn.copied {
  background-color: var(--el-color-success-light-7, #b3e1c8); /* From .action-button.copied */
  color: var(--el-color-success, #67c23a); /* From .action-button.copied */
}

/* Active state for code block buttons (e.g., Wrap button when active) */
/* Let's use a primary color accent, similar to hover but distinct */
.hljs-code-action-btn.active {
  background-color: var(--el-color-primary-dark-2, #3375b9); /* Darker primary */
  color: var(--el-color-primary-light-5, #9acaf7);
}

/* Styles for code block collapse/expand animation */
pre.hljs {
  overflow-x: auto; 
  overflow-y: hidden; 
  max-height: 600px; // Adjust as needed, or remove for natural height and rely on scroll for very long blocks
  transition: max-height 0.35s cubic-bezier(0.4, 0, 0.2, 1), 
              padding-top 0.35s cubic-bezier(0.4, 0, 0.2, 1), 
              padding-bottom 0.35s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  // Ensure margin and border are also handled if they exist and need to transition
  // margin: 0 !important; // from scoped, no need to transition if always 0
  // border-width: 0 !important; // from scoped, no need to transition if always 0 (or handled by wrapper)
}

pre.hljs.content-collapsed {
  max-height: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  opacity: 0 !important;
  overflow: hidden !important; 
  margin-top: 0 !important; 
  margin-bottom: 0 !important;
  // border-top-width: 0 !important; // Only if pre itself has a top/bottom border to hide
  // border-bottom-width: 0 !important;
}

/* 确保代码块样式正确 */
pre.hljs {
  margin: 0 !important;
  padding: 12px !important;
  border-radius: 0 !important;
  background: var(--code-bg-color, #f8f8f8) !important;
  color: var(--code-text-color, #333) !important;
  font-size: var(--code-font-size, 16px) !important;
}

code.hljs {
  
  font-size: var(--code-font-size, 16px) !important;
  padding: 0 !important;
  background: transparent !important;
}

/* 增加代码换行的支持 */
code.hljs.wrapped {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
}

pre.hljs.wrapped {
  white-space: pre-wrap !important;
}

/* 错误消息样式 */
.error-message {
  color: #f56c6c;
  padding: 10px;
  border-radius: 4px;
  background-color: #fef0f0;
  border-left: 3px solid #f56c6c;
  font-weight: 500;
  font-size: 16px;
}

/* highlight.js 常用语言基础样式 */
.hljs-comment,
.hljs-quote {
  color: #998;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #d73a49;
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: #008080;
}

.hljs-string,
.hljs-doctag {
  color: #d14;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #900;
  font-weight: bold;
}

.hljs-subst {
  font-weight: normal;
}

.hljs-type,
.hljs-class .hljs-title {
  color: #458;
  font-weight: bold;
}

/* 暗色模式 */
html.dark {
  .hljs-code-wrapper {
    border-color: #444;
    background: #1e1e1e;
  }

  .hljs-code-header {
    background: #2d2d2d;
    border-color: #444;
  }

  .hljs-code-lang {
    color: #bbb;
  }

  /* Dark mode for .hljs-code-action-btn */
  .hljs-code-action-btn {
    background-color: var(--el-bg-color-darker, #2c2c2c); /* Adapted from .action-button dark */
    color: var(--el-text-color-secondary-dark, #a8abb2); /* Adapted from .action-button dark */
  }
  
  .hljs-code-action-btn:hover {
    background-color: var(--el-fill-color-darker, #3a3a3a); /* Adapted from .action-button:hover dark */
    color: var(--el-color-primary-light-3, #79bbff); /* Adapted from .action-button:hover dark */
  }

  .hljs-code-action-btn.copied {
    background-color: var(--el-color-success-dark-2, #4e8e4f); /* Darker success */
    color: var(--el-color-success-light-5, #a4da89);
  }

  .hljs-code-action-btn.active {
    background-color: var(--el-color-primary-dark-2, #3375b9); /* Darker primary */
    color: var(--el-color-primary-light-5, #9acaf7);
  }

  pre.hljs {
    color: #ddd !important;
    background: #1e1e1e !important;
    background-image: none !important;
    box-shadow: none !important;
  }

  .hljs-comment,
  .hljs-quote {
    color: #7ec699;
  }

  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-subst {
    color: #569cd6;
  }

  .hljs-number,
  .hljs-literal,
  .hljs-variable,
  .hljs-template-variable,
  .hljs-tag .hljs-attr {
    color: #b5cea8;
  }

  .hljs-string,
  .hljs-doctag {
    color: #ce9178;
  }
  
  .error-message {
    background-color: rgba(245, 108, 108, 0.1);
    color: #f89898;
  }
}
</style>

<style lang="scss" scoped>
.message-bubble-container {
  display: flex;
  margin-bottom: 24px;
  gap: 12px;
  user-select: text !important; /* 强制确保文本可选择 */
  
  &.from-user {
    flex-direction: row-reverse;
  }
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-weight: 500;
  font-size: 14px;
  
  &.user-avatar {
    background-color: var(--el-color-primary, #409eff);
    color: white;
  }
  
  &.ai-avatar {
    background-color: var(--el-color-success, #67c23a);
    color: white;
  }
}

.message-content {
  max-width: 85%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 16px;
  
  .message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 6px;
    font-size: 12px;
    
    .sender-name {
      font-weight: 500;
      color: var(--el-text-color-secondary, #909399);
    }
    
    .message-time {
      color: var(--el-text-color-secondary, #909399);
      opacity: 0.7;
    }
  }
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 16px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  word-break: break-word;
  width: 100%;
  overflow-x: auto;
  user-select: text !important; /* 强制确保文本可选择 */
  
  &.user-bubble {
    background-color: var(--el-color-primary, #409eff);
    color: white;
    border-bottom-right-radius: 0;
    
    :deep(.code-block-wrapper) {
      border-color: rgba(255, 255, 255, 0.2);
      background-color: rgba(0, 0, 0, 0.2);
      
      .code-block-header {
        background-color: rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.1);
        
        .code-language {
          color: rgba(255, 255, 255, 0.8);
        }
        
        .code-action-btn {
          color: rgba(255, 255, 255, 0.8);
          
          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }
    
    :deep(code:not(.hljs)) {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
    }
    
    :deep(a) {
      color: white;
      text-decoration: underline;
    }
    
    :deep(blockquote) {
      border-left-color: rgba(255, 255, 255, 0.5);
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
  
  &.ai-bubble {
    background-color: var(--el-bg-color-overlay, #516481);
    color: var(--el-text-color-primary, #303133);
    border-bottom-left-radius: 0;
  }
  
  &.error-bubble {
    background-color: var(--el-color-danger-light-9, #fff0f0);
    border-left: 3px solid var(--el-color-danger, #f56c6c);
    color: var(--el-color-danger, #f56c6c);
  }
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
  
  .action-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: none;
    background-color: var(--el-fill-color-light, #f5f7fa);
    color: var(--el-text-color-secondary, #909399);
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
    
    &:hover {
      background-color: var(--el-fill-color, #f0f2f5);
      color: var(--el-color-primary, #409eff);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    svg {
      flex-shrink: 0;
    }
  }
}

.message-content:hover .message-actions {
  opacity: 1;
}

.reasoning-container {
  margin-bottom: 8px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light, #e4e7ed);
  overflow: hidden;
}

.reasoning-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--el-fill-color-light, #f5f7fa);
  cursor: pointer;
  gap: 8px;
  
  .reasoning-icon {
    display: flex;
    align-items: center;
    color: var(--el-color-info, #909399);
  }
  
  .reasoning-title {
    flex: 1;
    font-size: 13px;
    color: var(--el-text-color-secondary, #909399);
    display: flex;
    align-items: center;
    gap: 8px;
    
    .reasoning-time {
      font-size: 12px;
      opacity: 0.8;
      
      &.thinking {
        color: var(--el-color-warning, #e6a23c);
      }
    }
  }
  
  .reasoning-toggle {
    display: flex;
    align-items: center;
    
    .toggle-icon {
      transition: transform 0.2s;
      
      &.is-active {
        transform: rotate(180deg);
      }
    }
  }
}

.reasoning-content {
  padding: 12px;
  background-color: var(--el-bg-color, #ffffff);
  border-top: 1px solid var(--el-border-color-light, #e4e7ed);
  max-height: 400px; /* Default max-height when expanded */
  overflow-y: auto;
  font-size: 15px;
  opacity: 1;
  transition: max-height 0.35s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              padding-top 0.35s cubic-bezier(0.4, 0, 0.2, 1),
              padding-bottom 0.35s cubic-bezier(0.4, 0, 0.2, 1),
              border-top-width 0.35s cubic-bezier(0.4, 0, 0.2, 1);

  &.collapsed {
    max-height: 0;
    opacity: 0;
    padding-top: 0;
    padding-bottom: 0;
    overflow-y: hidden;
    border-top-width: 0;
    // If reasoning-container has margin/padding that should also collapse visually:
    // margin-top: 0;
    // margin-bottom: 0;
  }
}

/* Markdown 样式覆盖 */
:deep(.markdown-body) {
  background: transparent !important;
  margin: 0 !important;
  
  padding-left: 20px !important;
  font-size: inherit !important;

  line-height: 1.7 !important;
  
  p {
    margin-bottom: 0.75em !important;
    
    &:last-child {
      margin-bottom: 0 !important;
    }
  }
  
  pre:not(.hljs) {
    margin: 0.75em 0 !important;
    border-radius: 6px !important;
    background-color: var(--el-fill-color-light, #f5f7fa) !important;
    padding: 0.5em 0.75em !important;
    
    code:not(.hljs) {
      padding: 0 !important;
      background-color: transparent !important;
    }
  }
  
  code:not(.hljs) {
    padding: 0.2em 0.4em !important;
    border-radius: 3px !important;
    background-color: var(--el-fill-color-light, #f5f7fa) !important;
    color: var(--el-text-color-primary, #303133) !important;
    font-size: 15px !important;
  }
  
  img {
    max-width: 100% !important;
    border-radius: 8px !important;
  }
  
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 0.75em 0 !important;
    overflow-x: auto;
    display: block;
    
    th, td {
      border: 1px solid var(--el-border-color-light, #e4e7ed);
      padding: 0.5em 0.75em;
      text-align: left;
      font-size: 15px !important;
    }
    
    th {
      background-color: var(--el-fill-color-light, #f5f7fa);
      font-weight: 600;
    }
    
    tr:nth-child(even) {
      background-color: var(--el-fill-color-lighter, #f9fafc);
    }
  }
  
  /* 额外增强代码块样式 */
  .code-block-wrapper {
    margin: 12px 0 !important;
    
    .code-block-content {
      &.collapsed {
        max-height: 200px;
        overflow-y: hidden;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 40px;
          background: linear-gradient(to bottom, transparent, var(--el-bg-color, #ffffff));
          pointer-events: none;
        }
      }
      
      &.long-code {
        max-height: 400px;
        overflow-y: auto;
      }
      
      .line-numbers {
        user-select: none;
        text-align: right;
        color: var(--el-text-color-placeholder, #a8abb2);
        padding-right: 12px;
        min-width: 3em;
        position: sticky;
        left: 0;
        background-color: var(--el-fill-color-lighter, #f9fafc);
        z-index: 1;
        border-right: 1px solid var(--el-border-color-light, #e4e7ed);
      }
      
      pre.hljs {
        margin: 0 !important;
        overflow: visible !important;
        min-width: 100%;
        tab-size: 4;
        
        code {
          
          font-size: 15px !important;
          padding: 0 !important;
        }
      }
      
      /* 优化滚动条 */
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: var(--el-border-color, #dcdfe6);
        border-radius: 4px;
        
        &:hover {
          background-color: var(--el-border-color-darker, #c0c4cc);
        }
      }
    }
  }
}

/* 代码按钮样式增强 */
:deep(.code-btn) {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  &.active {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
}

// 暗色模式适配
:deep(html.dark) {
  .message-bubble {
    &.ai-bubble {
      background-color: #1e1e1e;
      background-image: none;
      color: var(--el-text-color-primary, #e0e0e0);
    }
    
    &.error-bubble {
      background-color: rgba(245, 108, 108, 0.1);
      color: var(--el-color-danger-light-3, #f89898);
    }
  }
  
  .reasoning-container {
    border-color: var(--el-border-color-darker, #444);
  }
  
  .reasoning-header {
    background-color: var(--el-bg-color-darker, #1a1a1a);
  }
  
  .reasoning-content {
    background-color: var(--el-bg-color, #1e1e1e);
    border-color: var(--el-border-color-darker, #444);
  }
  
  .action-button {
    background-color: var(--el-bg-color-darker, #1a1a1a);
    
    &:hover {
      background-color: var(--el-fill-color-darker, #2c2c2c);
    }
  }
  
  .markdown-body {
    code:not(.hljs) {
      background-color: var(--el-fill-color-darker, #2c2c2c) !important;
      color: var(--el-text-color-primary, #e0e0e0) !important;
    }
    
    pre:not(.hljs) {
      background-color: var(--el-fill-color-darker, #2c2c2c) !important;
    }
    
    table {
      th {
        background-color: var(--el-fill-color-darker, #2c2c2c);
      }
      
      tr:nth-child(even) {
        background-color: var(--el-bg-color-darker, #1a1a1a);
      }
      
      th, td {
        border-color: var(--el-border-color-darker, #444);
      }
    }
    
    /* 暗色模式下的代码块样式增强 */
    .code-block-wrapper {
      border-color: var(--el-border-color-darker, #444) !important;
      background-color: #1a1a1a !important;
      
      .code-block-header {
        background-color: #2d2d2d !important;
        border-color: var(--el-border-color-darker, #444) !important;
        
        .code-language {
          color: #aaa !important;
        }
        
        .code-action-btn {
          color: #aaa !important;
          
          &:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: var(--el-color-primary, #409eff) !important;
          }
          
          &.copied {
            color: var(--el-color-success, #67c23a) !important;
          }
        }
      }
      
      .code-block-content {
        &.collapsed::after {
          background: linear-gradient(to bottom, transparent, #1a1a1a) !important;
        }
        
        .line-numbers {
          background-color: #252525 !important;
          color: #888 !important;
          border-right-color: #444 !important;
        }
        
        &::-webkit-scrollbar-thumb {
          background-color: #444 !important;
          
          &:hover {
            background-color: #555 !important;
          }
        }
      }
    }
  }
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  background-color: var(--el-fill-color-light, #f5f7fa);
  color: var(--el-text-color-secondary, #909399);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  
  &:hover {
    background-color: var(--el-fill-color, #f0f2f5);
    color: var(--el-color-primary, #409eff);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  svg {
    flex-shrink: 0;
  }
  
  &.copy-button {
    position: relative; /* Make the button position relative for the ::after pseudo element */
    
    &:active {
      background-color: var(--el-color-success-light-8);
      transform: translateY(1px);
    }
    
    &::after {
      content: "";
      position: absolute;
      inset: 0;
      border-radius: 4px;
      opacity: 0;
      background-color: var(--el-color-success-light-7);
      transition: opacity 0.3s;
    }
    
    &:focus::after {
      opacity: 0.3;
    }
    
    /* Add styles for user vs AI buttons to help debugging */
    &.copy-button-user {
      border-left: 2px solid var(--el-color-primary);
    }
    
    &.copy-button-ai {
      border-left: 2px solid var(--el-color-success);
    }
  }
}
</style>

<style>
/* 确保AI响应文本可选择 */
.message-bubble:not(.user-bubble),
.message-bubble:not(.user-bubble) *,
.markdown-body,
.markdown-body * {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;
}

/* 确保按钮等交互元素保持正确的光标 */
.message-bubble button,
.message-bubble .action-button,
.message-bubble .hljs-code-actions button {
  cursor: pointer;
  user-select: none;
}

/* 添加更强大的样式覆盖 */
body.prevent-select .message-bubble,
body.prevent-select .markdown-body {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* 防止任何可能的干扰选择的样式 */
[class*="prevent-select"],
[class*="no-select"] {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* 覆盖可能的全局设置 */
html, body {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
</style> 