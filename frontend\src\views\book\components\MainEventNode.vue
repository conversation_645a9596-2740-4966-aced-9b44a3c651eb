<template>
  <div 
    class="main-event-node" 
    :class="{ 'selected': selected }"
  >
    <!-- 节点内容 -->
    <div class="main-event-content">
      <div class="event-date" v-if="formattedDate">{{ formattedDate }}</div>
      <div class="event-title">{{ data.label }}</div>
      <div class="event-desc" v-if="data.content">{{ data.content }}</div>
    </div>
    
    <!-- 连接点容器，使用绝对定位确保位置固定 -->
    <div class="handles-container">
      <!-- 左侧连接点 -->
      <Handle 
        id="left" 
        type="source" 
        :position="Position.Left" 
        class="main-handle left-handle"
      />
      <Handle 
        id="left-target" 
        type="target" 
        :position="Position.Left" 
        class="main-handle left-handle"
      />
      
      <!-- 右侧连接点 -->
      <Handle 
        id="right" 
        type="source" 
        :position="Position.Right" 
        class="main-handle right-handle"
      />
      <Handle 
        id="right-target" 
        type="target" 
        :position="Position.Right" 
        class="main-handle right-handle"
      />
      
      <!-- 顶部连接点 -->
      <Handle 
        id="top" 
        type="target" 
        :position="Position.Top" 
        class="main-handle top-handle"
      />
      
      <!-- 底部连接点 -->
      <Handle 
        id="bottom" 
        type="source" 
        :position="Position.Bottom" 
        class="main-handle bottom-handle"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Handle, Position } from '@vue-flow/core';

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  data: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
});

// 格式化日期
const formattedDate = computed(() => {
  // 如果使用自定义时间，直接返回
  if (props.data.useCustomTime && props.data.customTime) {
    return props.data.customTime;
  }
  
  // 否则使用标准年月日格式
  const { year, month, day } = props.data;
  if (!year) return '';
  return `${year}/${month || ''}/${day || ''}`;
});
</script>

<style scoped>
/* 主干事件节点样式 */
.main-event-node {
  padding: 10px 15px;
  border-radius: 8px;
  width: 280px; /* 固定宽度 */
  max-width: 280px; /* 最大宽度限制 */
  background-color: var(--el-color-primary-light-9);
  border: 2px solid var(--el-color-primary);
  color: var(--el-text-color-primary);
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.main-event-node.selected {
  box-shadow: 0 0 0 2px var(--el-color-primary);
}

.main-event-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
  overflow-wrap: break-word;
}

/* 连接点容器，确保连接点位于正确位置 */
.handles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 避免阻挡内容点击 */
}

.main-handle {
  background-color: var(--el-color-primary);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  z-index: 2;
  pointer-events: auto; /* 确保连接点可以交互 */
}

.top-handle,
.bottom-handle {
  width: 12px;
  height: 12px;
}

/* 事件内容样式 */
.event-title {
  font-weight: bold;
  font-size: 14px;
  text-align: center;
}

.event-date {
  font-size: 12px;
  opacity: 0.8;
  text-align: center;
}

.event-desc {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 4px;
  max-height: none; /* 移除高度限制 */
  white-space: pre-line; /* 支持换行符 */
  word-break: break-word; /* 确保长词可以换行 */
  overflow-wrap: break-word;
}
</style> 