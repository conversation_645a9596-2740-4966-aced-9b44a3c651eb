<!doctype html>
<html lang="en" class="light">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + Vue</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module">
        import { disableZoom } from '/src/utils/disableZoom.js';
        document.addEventListener('DOMContentLoaded', () => {
            disableZoom();
        });
    </script>
    <script type="module">
      import { initWindowResize } from '/src/utils/resizable.js';
      document.addEventListener('DOMContentLoaded', () => {
        initWindowResize();
      });
    </script>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>