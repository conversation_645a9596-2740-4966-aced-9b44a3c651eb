<template>
  <div class="timeline-flow-wrapper">
    <VueFlow
      :nodes="nodes"
      :edges="edges"
      :default-viewport="{ x: 0, y: 0, zoom: 0.85 }"
      :min-zoom="0.3"
      :max-zoom="2"
      :nodes-draggable="true"
      :snap-to-grid="true"
      :snap-grid="[20, 20]"
      :connection-line-style="{ stroke: '#409EFF', strokeWidth: 2 }"
      :connection-line-type="'smoothstep'"
      :default-edge-options="defaultEdgeOptions"
      :connect-on-click="false"
      :edges-updatable="true"
      :edges-draggable="false"
      :edge-updatable="true"
      :enable-pan-on-drag="true" 
      :enable-pan-on-scroll="true"
      :enable-zoom-on-scroll="true"
      :fit-view-on-init="true"
      :fit-view-padding="[50, 50]"
      class="timeline-flow"
      @nodesChange="onNodesChange"
      @edgesChange="onEdgesChange"
      @connect="onConnect"
      @edge-update="onEdgeUpdate"
      @edge-click="onEdgeClick"
      @edge-contextmenu="onEdgeContextMenu"
      @paneclick="onPaneClick"
      @node-click="onNodeClick"
      @node-drag="onNodeDrag"
      @node-drag-stop="onNodeDragStop"
      @node-context-menu="onNodeContextMenu"
    >
      <!-- 自定义节点 -->
      <template #node-main-event="nodeProps">
        <MainEventNode v-bind="nodeProps" />
      </template>
      
      <template #node-branch-event="nodeProps">
        <BranchEventNode v-bind="nodeProps" />
      </template>
      
      <!-- 注册边类型 -->
      <template #edge-bezier="edgeProps">
        <BezierEdge v-bind="edgeProps" />
      </template>
      
      <template #edge-straight="edgeProps">
        <StraightEdge v-bind="edgeProps" />
      </template>
      
      <template #edge-smoothstep="edgeProps">
        <SmoothStepEdge v-bind="edgeProps" />
      </template>
      
      <template #edge-step="edgeProps">
        <StepEdge v-bind="edgeProps" />
      </template>
      
      <template #edge-simplebezier="edgeProps">
        <SimpleBezierEdge v-bind="edgeProps" />
      </template>
      
      <!-- 使用正确的组件用法 -->
      <Background pattern-color="#aaa" :gap="20" :size="1" />
      <Controls />
      <Panel position="top-right" class="panel-top">
        <div class="panel-buttons">
          <el-button type="primary" @click="toggleFullscreen" size="small" :icon="FullScreen">{{ isFullscreen ? '退出全屏' : '全屏' }}</el-button>
        </div>
      </Panel>
    </VueFlow>
    
    <!-- 自定义编辑对话框 - 确保在全屏模式下也能显示 -->
    <div v-if="editDialogVisible" class="custom-dialog-overlay" @click="handleDialogOverlayClick">
      <div class="custom-dialog" @click.stop>
        <div class="custom-dialog-header">
          <h3>编辑事件详情</h3>
          <button class="custom-dialog-close" @click="cancelEdit">×</button>
        </div>
        <div class="custom-dialog-body">
          <el-form :model="editingEvent" label-width="60px" class="edit-event-form" size="small">
            <el-form-item label="标题">
              <el-input v-model="editingEvent.label" placeholder="请输入事件标题"></el-input>
            </el-form-item>
            <el-form-item label="时间">
              <div class="time-mode-switch">
                <el-switch
                  v-model="isCustomTimeMode"
                  active-text="自定义时间"
                  inactive-text="标准时间"
                  inline-prompt
                  @change="handleTimeModeChange"
                />
              </div>

              <!-- 标准时间模式 -->
              <div class="date-inputs" v-if="!isCustomTimeMode">
                <el-input-number v-model="editingEvent.year" :min="1900" :max="2100" placeholder="年" :controls="false" class="date-input-year"></el-input-number>
                <span class="date-separator">年</span>
                <el-input-number v-model="editingEvent.month" :min="1" :max="12" placeholder="月" :controls="false" class="date-input-month"></el-input-number>
                <span class="date-separator">月</span>
                <el-input-number v-model="editingEvent.day" :min="1" :max="31" placeholder="日" :controls="false" class="date-input-day"></el-input-number>
                <span class="date-separator">日</span>
              </div>

              <!-- 自定义时间模式 -->
              <div class="custom-time-inputs" v-else>
                <el-input v-model="editingEvent.customTime" placeholder="如：第三纪元 2415年" class="custom-time-input"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="内容">
              <el-input
                v-model="editingEvent.content"
                type="textarea"
                :rows="4"
                placeholder="请输入事件内容描述"
                class="content-textarea"
              ></el-input>
            </el-form-item>
            <el-form-item v-if="editingEvent.nodeType === 'branch'" label="颜色">
              <div class="color-picker-container">
                <el-color-picker
                  v-model="editingEvent.color"
                  show-alpha
                  :predefine="predefineColors"
                ></el-color-picker>
                <div class="color-meaning-hint">
                  <el-tooltip
                    placement="right"
                    :content="getColorMeaning(editingEvent.color)"
                    effect="light"
                  >
                    <span>当前: {{ getColorMeaningShort(editingEvent.color) }}</span>
                  </el-tooltip>
                  <el-popover
                    placement="right"
                    :width="380"
                    trigger="click"
                    popper-class="color-popover"
                  >
                    <template #default>
                      <div class="color-popover-header">
                        <h4>选择事件颜色</h4>
                        <p class="color-popover-tip">点击颜色直接选择</p>
                      </div>
                      <div class="color-meanings-popover">
                        <div
                          class="color-item"
                          v-for="(color, index) in predefineColors"
                          :key="index"
                          @click="selectColor(color)"
                          :class="{ 'color-item-active': editingEvent.color === color }"
                        >
                          <el-tooltip
                            :content="`点击选择: ${getColorMeaning(color)}`"
                            placement="top"
                            effect="light"
                            :show-after="300"
                          >
                            <div class="color-swatch" :style="{ backgroundColor: color }"></div>
                          </el-tooltip>
                          <span class="color-desc">{{ getColorMeaning(color) }}</span>
                        </div>
                      </div>
                    </template>
                    <template #reference>
                      <el-button
                        type="primary"
                        size="small"
                        plain
                        class="color-help-button"
                        :icon="ArrowDown"
                      >
                        查看更多颜色含义
                      </el-button>
                    </template>
                  </el-popover>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="custom-dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveEdit">确认</el-button>
        </div>
      </div>
    </div>
    
    <!-- 自定义边样式编辑对话框 -->
    <div v-if="edgeStyleDialogVisible" class="custom-dialog-overlay" @click="handleEdgeDialogOverlayClick">
      <div class="custom-dialog custom-dialog-small" @click.stop>
        <div class="custom-dialog-header">
          <h3>编辑连接样式</h3>
          <button class="custom-dialog-close" @click="edgeStyleDialogVisible = false">×</button>
        </div>
        <div class="custom-dialog-body">
          <el-form :model="editingEdgeStyle" label-width="60px" class="edge-edit-form" size="small">
            <el-form-item label="线宽">
              <el-slider v-model="editingEdgeStyle.strokeWidth" :min="1" :max="10" :step="1"></el-slider>
            </el-form-item>
            <el-form-item label="颜色">
              <el-color-picker
                v-model="editingEdgeStyle.stroke"
                show-alpha
                :predefine="predefineColors"
              ></el-color-picker>
            </el-form-item>
            <el-form-item label="动画效果">
              <el-switch v-model="editingEdgeStyle.animated"></el-switch>
            </el-form-item>
          </el-form>
        </div>
        <div class="custom-dialog-footer">
          <el-button @click="edgeStyleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEdgeStyle">确认</el-button>
        </div>
      </div>
    </div>
    
    <!-- 右键菜单 -->
    <div v-show="contextMenuVisible" class="context-menu" :style="contextMenuStyle">
      <!-- 主干节点的右键菜单 -->
      <template v-if="contextMenuNodeType === 'main'">
        <div class="context-menu-item" @click="handleContextMenuAction('add-up')">
          向上添加事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('add-down')">
          向下添加事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('add-left')">
          向左添加事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('add-right')">
          向右添加事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('add-free')">
          添加自由事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('edit')">
          编辑节点
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('delete')">
          删除节点
        </div>
      </template>

      <!-- 分支节点的右键菜单 -->
      <template v-else-if="contextMenuNodeType === 'branch'">
        <div class="context-menu-item" @click="handleContextMenuAction('add-up')">
          向上添加事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('add-down')">
          向下添加事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('add-left')">
          向左添加事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('add-right')">
          向右添加事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('add-free')">
          添加自由事件
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('edit')">
          编辑节点
        </div>
        <div class="context-menu-item" @click="handleContextMenuAction('delete')">
          删除节点
        </div>
      </template>

      <!-- 边的右键菜单 -->
      <template v-else-if="contextMenuType === 'edge'">
        <div class="context-menu-item" @click="handleEdgeMenuAction('change-style')">
          修改样式
        </div>
        <div class="context-menu-item" @click="handleEdgeMenuAction('delete')">
          删除连接
        </div>
      </template>
    </div>
    
    <!-- 自定义导入数据对话框 -->
    <div v-if="importDialogVisible" class="custom-dialog-overlay" @click="handleImportDialogOverlayClick">
      <div class="custom-dialog" @click.stop>
        <div class="custom-dialog-header">
          <h3>导入时间线数据</h3>
          <button class="custom-dialog-close" @click="importDialogVisible = false">×</button>
        </div>
        <div class="custom-dialog-body">
          <div class="import-container">
            <el-alert
              title="数据格式提示"
              type="info"
              description="请粘贴有效的JSON格式数据，包含节点和连接信息"
              show-icon
              :closable="false"
              size="small"
            />
            <el-input
              v-model="importJsonData"
              type="textarea"
              :rows="9"
              placeholder="粘贴JSON数据..."
              class="import-textarea"
            />
          </div>
        </div>
        <div class="custom-dialog-footer">
          <el-button @click="importDialogVisible = false" size="small">取消</el-button>
          <el-button type="primary" @click="importTimelineData" size="small">导入</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, shallowRef, onMounted, defineExpose, nextTick, computed, onUnmounted } from 'vue';
import { nanoid } from 'nanoid';
import { VueFlow, useVueFlow, Panel, Position, isNode, isEdge } from '@vue-flow/core';
import { Background } from '@vue-flow/background';
import { Controls } from '@vue-flow/controls';
// 删除MiniMap的导入
// 导入边类型组件
import { BezierEdge, StraightEdge, StepEdge, SmoothStepEdge, SimpleBezierEdge } from '@vue-flow/core';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Plus, Connection, Refresh, FullScreen, ArrowDown } from '@element-plus/icons-vue';

// 导入自定义节点组件
import MainEventNode from './components/MainEventNode.vue';
import BranchEventNode from './components/BranchEventNode.vue';
// 移除MainLineEdge导入
// import MainLineEdge from './components/MainLineEdge.vue';

// 引入样式
import '@vue-flow/core/dist/style.css';
import '@vue-flow/core/dist/theme-default.css';
import '@vue-flow/controls/dist/style.css';
// 删除MiniMap样式导入

// 主干线中心X位置
const centerX = 400;

// 添加边类型常量
const EdgeTypeEnum = {
  Straight: 'straight',
  SmoothStep: 'smoothstep',
  SimpleBezier: 'simplebezier',
  Bezier: 'bezier',
  Step: 'step'
};

// 添加标记类型常量
const MarkerTypeEnum = {
  Arrow: 'arrow',
  ArrowClosed: 'arrowclosed'
};

// 节点和边状态
const nodes = ref([]);
const edges = ref([]);

// 时间模式相关状态
const isCustomTimeMode = ref(false);

// 拖拽状态
const isDraggingConnection = ref(false);
const draggedEdge = ref(null);
const draggedNode = ref(null);

// 编辑对话框状态
const editDialogVisible = ref(false);
const editingEvent = ref({});
const editingNodeId = ref('');

// 右键菜单状态
const contextMenuVisible = ref(false);
const contextMenuStyle = ref({
  top: '0px',
  left: '0px'
});
const contextMenuNodeId = ref('');
const contextMenuNodeType = ref('');
const contextMenuType = ref(''); // 'node' or 'edge'
const contextMenuEdgeId = ref('');

// 默认边样式
const defaultEdgeOptions = {
  type: EdgeTypeEnum.SmoothStep,
  animated: true,
  style: { 
    cursor: 'move',
    strokeWidth: 2
  },
  updatable: true,
  draggable: true
};

// Vue Flow工具函数
const vueFlowStore = useVueFlow();
const {
  findNode,
  getSelectedNodes,
  addNodes,
  addEdges,
  removeNodes,
  removeEdges,
  updateEdge,
  updateNode,
  fitView
} = vueFlowStore;

// 获取节点和边的辅助函数
const getNodes = () => {
  try {
    const nodes = vueFlowStore.getNodes.value;
    return Array.isArray(nodes) ? nodes : [];
  } catch (error) {
    console.error('获取节点失败:', error);
    return [];
  }
};

const getEdges = () => {
  try {
    const edges = vueFlowStore.getEdges.value;
    return Array.isArray(edges) ? edges : [];
  } catch (error) {
    console.error('获取边失败:', error);
    return [];
  }
};

// 查找边的辅助函数
const findEdge = (edgeId) => {
  try {
    const edges = getEdges();
    return edges.find(edge => edge.id === edgeId);
  } catch (error) {
    console.error('查找边时出错:', error);
    return null;
  }
};

// 更新节点位置的辅助函数
const updateNodePosition = (nodeId, position) => {
  try {
    const node = findNode(nodeId);
    if (!node) {
      console.error(`找不到ID为${nodeId}的节点`);
      return;
    }
    
    // 直接更新节点位置
    node.position = { ...position };
    
    // 也可以使用updateNode更新整个节点
    updateNode(nodeId, {
      ...node,
      position: position
    });
  } catch (error) {
    console.error(`更新节点位置时出错:`, error);
  }
};

// ===== 初始化数据 =====
// 初始化主干节点
const initialNodes = [
  { 
    id: 'main-1', 
    type: 'main-event',
    position: { x: centerX, y: 100 }, 
    data: { 
      label: '故事开始', 
      year: '2020',
      month: '1',
      day: '1',
      content: '主角踏上旅程',
      nodeType: 'main',
      color: '#409EFF'
    }
  },
  { 
    id: 'main-2', 
    type: 'main-event',
    position: { x: centerX, y: 250 }, 
    data: { 
      label: '主角出发', 
      year: '2021',
      month: '6',
      day: '15',
      content: '主角面临挑战',
      nodeType: 'main',
      color: '#409EFF'
    } 
  },
  { 
    id: 'main-3', 
    type: 'main-event',
    position: { x: centerX, y: 400 }, 
    data: { 
      label: '第一次危机', 
      year: '2022',
      month: '3',
      day: '10',
      content: '主角遇到危机',
      nodeType: 'main',
      color: '#409EFF'
    } 
  },
  // 分支节点 - 右侧
  { 
    id: 'branch-1', 
    type: 'branch-event',
    position: { x: centerX + 250, y: 100 }, 
    data: { 
      label: '初遇', 
      year: '2020',
      month: '3',
      day: '15',
      content: '与女主角相遇',
      nodeType: 'branch',
      color: '#e84393',
      parentId: 'main-1',
      isLeftSide: false
    },
    connectable: true
  },
  // 分支节点 - 左侧
  { 
    id: 'branch-2', 
    type: 'branch-event',
    position: { x: centerX - 250, y: 250 }, 
    data: { 
      label: '挫折', 
      year: '2021',
      month: '5',
      day: '20',
      content: '遭遇挫折',
      nodeType: 'branch',
      color: '#00b894',
      parentId: 'main-2',
      isLeftSide: true
    },
    connectable: true
  }
];

// 初始化连接
const initialEdges = [
  // 主干连接
  { 
    id: 'e-main1-main2', 
    source: 'main-1', 
    target: 'main-2',
    sourceHandle: 'bottom',
    targetHandle: 'top',
    type: EdgeTypeEnum.SmoothStep,
    style: { 
      strokeWidth: 4, 
      stroke: '#409EFF'
    },
    markerEnd: {
      type: MarkerTypeEnum.ArrowClosed,
      color: '#409EFF'
    },
    updatable: true,
    draggable: true
  },
  { 
    id: 'e-main2-main3', 
    source: 'main-2', 
    target: 'main-3',
    sourceHandle: 'bottom',
    targetHandle: 'top',
    type: EdgeTypeEnum.SmoothStep,
    style: { 
      strokeWidth: 4, 
      stroke: '#409EFF'
    },
    markerEnd: {
      type: MarkerTypeEnum.ArrowClosed,
      color: '#409EFF'
    },
    updatable: true,
    draggable: true
  },
  // 分支连接
  { 
    id: 'e-main1-branch1', 
    source: 'main-1', 
    sourceHandle: 'right',
    target: 'branch-1',
    targetHandle: 'left',
    type: EdgeTypeEnum.SmoothStep,
    animated: true,
    style: { 
      strokeWidth: 3, 
      stroke: '#e84393'
    },
    markerEnd: {
      type: MarkerTypeEnum.ArrowClosed,
      color: '#e84393'
    }
  },
  { 
    id: 'e-main2-branch2', 
    source: 'main-2', 
    sourceHandle: 'left',
    target: 'branch-2',
    targetHandle: 'right',
    type: EdgeTypeEnum.SmoothStep,
    animated: true,
    style: { 
      strokeWidth: 3, 
      stroke: '#00b894'
    },
    markerEnd: {
      type: MarkerTypeEnum.ArrowClosed,
      color: '#00b894'
    }
  }
];

// 全屏相关状态
const isFullscreen = ref(false);

// 切换全屏方法
const toggleFullscreen = () => {
  const element = document.querySelector('.timeline-flow-wrapper');
  
  if (!document.fullscreenElement) {
    // 进入全屏
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if (element.webkitRequestFullscreen) { /* Safari */
      element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) { /* IE11 */
      element.msRequestFullscreen();
    }
    isFullscreen.value = true;
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) { /* Safari */
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) { /* IE11 */
      document.msExitFullscreen();
    }
    isFullscreen.value = false;
  }
  
  // 调整视图适应全屏
  nextTick(() => {
    fitView();
  });
};

// 监听全屏变化事件
onMounted(() => {

  nodes.value = initialNodes;
  edges.value = initialEdges;
  
  // 监听点击事件，隐藏右键菜单
  document.addEventListener('click', () => {
    contextMenuVisible.value = false;
  });
  
  // 添加键盘事件监听，支持Ctrl+S保存
  document.addEventListener('keydown', handleKeyDown);
  
  // 监听全屏变化事件
  document.addEventListener('fullscreenchange', onFullscreenChange);
  document.addEventListener('webkitfullscreenchange', onFullscreenChange);
  document.addEventListener('mozfullscreenchange', onFullscreenChange);
  document.addEventListener('MSFullscreenChange', onFullscreenChange);
  
  console.log('TimelineFlow组件已挂载，节点数:', nodes.value.length, '边数:', edges.value.length);
  
  // 初始自动布局和适应视图
  nextTick(() => {
    fitView();
  });
  
  // 监听窗口大小变化，重新调整视图
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  
  // 移除Ctrl+S事件监听
  document.removeEventListener('keydown', handleKeyDown);
  
  // 移除全屏变化事件监听器
  document.removeEventListener('fullscreenchange', onFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', onFullscreenChange);
  document.removeEventListener('mozfullscreenchange', onFullscreenChange);
  document.removeEventListener('MSFullscreenChange', onFullscreenChange);
});

// 全屏状态变化处理函数
const onFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement || 
    !!document.webkitFullscreenElement || 
    !!document.mozFullScreenElement ||
    !!document.msFullscreenElement;
  
  // 调整视图适应全屏
  nextTick(() => {
    fitView();
  });
};

// 窗口大小变化处理函数
const handleResize = () => {
  // 使用节流函数避免频繁调用
  if (resizeTimeout.value) clearTimeout(resizeTimeout.value);
  resizeTimeout.value = setTimeout(() => {
    console.log('窗口大小变化，重新适应视图');
    fitView();
  }, 200);
};

// 缓存resize超时ID
const resizeTimeout = ref(null);

// ===== 通用工具方法 =====
// 生成随机颜色
const getRandomColor = () => {
  const colors = ['#e84393', '#00b894', '#e17055', '#6c5ce7', '#fdcb6e', '#00cec9', '#ff7675', '#74b9ff'];
  return colors[Math.floor(Math.random() * colors.length)];
};

// 查找主干节点
const findMainNodeById = (id) => {
  try {
    const nodes = getNodes();
    if (!nodes || !Array.isArray(nodes)) {
      console.error('getNodes() 返回值不是数组:', nodes);
      return null;
    }
    return nodes.find(n => n.id === id && n.data.nodeType === 'main');
  } catch (error) {
    console.error('查找主干节点时出错:', error);
    return null;
  }
};

// 查找分支节点
const findBranchNodeById = (id) => {
  try {
    const nodes = getNodes();
    if (!nodes || !Array.isArray(nodes)) {
      console.error('getNodes() 返回值不是数组:', nodes);
      return null;
    }
    return nodes.find(n => n.id === id && n.data.nodeType === 'branch');
  } catch (error) {
    console.error('查找分支节点时出错:', error);
    return null;
  }
};

// ===== 事件处理方法 =====
// 节点和边变化处理函数
const onNodesChange = (changes) => {

  // 触发数据变更事件
  triggerDataChanged();
};

const onEdgesChange = (changes) => {
  // Vue Flow 会自动更新边数据
  console.log('连接变化:', changes);
  // 触发数据变更事件
  triggerDataChanged();
};

// 节点拖动处理
const onNodeDrag = (event) => {
  try {
    const node = event.node;
    draggedNode.value = node;
    
    // 如果是主干节点，限制只能垂直移动
    if (node.data.nodeType === 'main') {
      // 强制X坐标固定在中心线
      node.position.x = centerX;
      
      // 无需主动触发连线刷新，让Vue Flow自行处理连线更新
      // 只在控制台记录一下当前状态
      console.log(`主干节点 ${node.id} 拖动中, Y位置: ${node.position.y}`);
    }
  } catch (error) {
    console.error('节点拖动时出错:', error);
  }
};

// 强制刷新边的显示
const forceEdgesRefresh = (edgesToRefresh) => {
  // 如果没有提供边，获取所有主干边
  if (!edgesToRefresh) {
    const allEdges = getEdges();
    if (!allEdges || !Array.isArray(allEdges) || allEdges.length === 0) return;
    
    edgesToRefresh = allEdges.filter(edge => {
      const sourceNode = findNode(edge.source);
      const targetNode = findNode(edge.target);
      return sourceNode?.data?.nodeType === 'main' && targetNode?.data?.nodeType === 'main';
    });
    
    if (edgesToRefresh.length === 0) return;
  }
  
  try {
    // 给边加上unique的id后缀，确保Vue Flow认为这是新的边
    const currentTime = Date.now();
    
    // 创建一份新的边数据
    const refreshedEdges = edgesToRefresh.map(edge => {
      const refreshedEdge = { ...edge };
      
      // 为了触发Vue Flow的重新渲染，修改ID但保持引用相同的源和目标
      if (!refreshedEdge.id.includes('refresh-')) {
        refreshedEdge.id = `refresh-${edge.id}-${currentTime}`;
      }
      
      return refreshedEdge;
    });
    
    // 先移除旧边
    removeEdges(edgesToRefresh.map(edge => edge.id));
    
    // 应用删除后再添加新边
    nextTick(() => {
      // 添加刷新后的边
      addEdges(refreshedEdges);
      
      // 更新响应式数组，过滤掉旧边并加入新边
      edges.value = [
        ...edges.value.filter(e => !edgesToRefresh.some(edge => edge.id === e.id)),
        ...refreshedEdges
      ];
    });
  } catch (error) {
    console.error('强制刷新边显示时出错:', error);
  }
};

// 处理连接到主干节点的分支节点
const handleBranchConnectionsToMainNode = (mainNodeId) => {
  try {
    // 获取所有连接到此主干节点的边
    const edges = getEdges();
    const branchEdges = edges.filter(edge => {
      // 找出连接到此主干节点的边
      if (edge.source === mainNodeId || edge.target === mainNodeId) {
        const otherNodeId = edge.source === mainNodeId ? edge.target : edge.source;
        const otherNode = findNode(otherNodeId);
        // 如果另一端是分支节点，则是我们要找的边
        return otherNode?.data?.nodeType === 'branch';
      }
      return false;
    });
    
    if (branchEdges.length === 0) return;
    console.log(`发现 ${branchEdges.length} 条连接到主干节点 ${mainNodeId} 的分支连线`);
    
    // 刷新这些连接，确保它们显示正确
    branchEdges.forEach(edge => {
      const branchNodeId = edge.source === mainNodeId ? edge.target : edge.source;
      const branchNode = findNode(branchNodeId);
      const mainNode = findNode(mainNodeId);
      
      if (!branchNode || !mainNode) return;
      
      // 使用现有的边ID，避免创建新边
      const updatedEdge = {
        ...edge,
        style: { ...edge.style }  // 创建样式的浅拷贝
      };
      
      // 更新边，保持样式但刷新显示
      updateEdge(edge, updatedEdge);
      console.log(`已刷新分支连线: ${edge.id}`);
    });
  } catch (error) {
    console.error('处理分支连接时出错:', error);
  }
};

// 确保分支到主干的连接
const ensureBranchToMainConnection = (branchNodeId, mainNodeId) => {
  try {
    // 获取分支节点和主干节点
    const branchNode = findNode(branchNodeId);
    const mainNode = findNode(mainNodeId);
    
    if (!branchNode || !mainNode) {
      console.error('无法找到节点:', branchNodeId, mainNodeId);
      return;
    }
    
    // 获取现有边
    const edges = getEdges();
    const existingEdge = edges.find(edge => 
      (edge.source === mainNodeId && edge.target === branchNodeId) || 
      (edge.source === branchNodeId && edge.target === mainNodeId)
    );
    
    // 如果已存在连接，刷新它
    if (existingEdge) {
      // 只更新样式，触发重绘，避免创建新的连接
      const updatedEdge = {
        ...existingEdge,
        style: { ...existingEdge.style }
      };
      updateEdge(existingEdge, updatedEdge);
      console.log(`已刷新分支到主干连接: ${existingEdge.id}`);
    } else {
      // 如果不存在连接，创建一个新的
      const isLeftSide = branchNode.data.isLeftSide;
      
      // 创建新的连接
      const newEdge = {
        id: `e-${mainNodeId}-${branchNodeId}-${Date.now()}`,
        source: mainNodeId,
        sourceHandle: isLeftSide ? 'left' : 'right',
        target: branchNodeId,
        targetHandle: isLeftSide ? 'right' : 'left',
        type: EdgeTypeEnum.SmoothStep,
        animated: true,
        style: {
          strokeWidth: 3,
          stroke: branchNode.data.color || getRandomColor()
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: branchNode.data.color || getRandomColor()
        }
      };
      
      // 使用Vue Flow的API添加边
      addEdges([newEdge]);
      
      // 同时更新分支节点的parentId属性，保持数据一致性
      updateNode(branchNodeId, {
        ...branchNode,
        data: {
          ...branchNode.data,
          parentId: mainNodeId
        }
      });
      
      console.log(`已创建新的分支到主干连接: ${newEdge.id}`);
    }
  } catch (error) {
    console.error('确保分支到主干连接时出错:', error);
  }
};

// 更新拖动结束处理
const onNodeDragStop = (event) => {
  try {
    const node = event.node;
    
    // 如果是主干节点，确保X位置居中并检查连线
    if (node.data.nodeType === 'main') {
      console.log(`主干节点 ${node.id} 拖动结束, 当前Y位置: ${node.position.y}`);
      
      // 强制保存当前位置
      updateNodePosition(node.id, {
        x: centerX,
        y: node.position.y
      });
      
      // 使用优化后的方法处理主干连接
      replaceMainConnections();
    }
    
    // 如果是分支节点，确保其与主干节点的连接正确
    if (node.data.nodeType === 'branch' && node.data.parentId) {
      console.log(`分支节点 ${node.id} 拖动结束, 当前位置: (${node.position.x}, ${node.position.y})`);
      setTimeout(() => {
        ensureBranchToMainConnection(node.id, node.data.parentId);
      }, 50);
    }
    
    draggedNode.value = null;
    
    // 触发数据变更事件
    triggerDataChanged();
  } catch (error) {
    console.error('节点拖动结束时出错:', error);
    draggedNode.value = null;
  }
};

// 重建主干连接的专用方法 - 优化版
const replaceMainConnections = () => {
  try {
    console.log('开始优化主干连接...');
    
    // 获取所有节点和边
    const nodes = getNodes();
    const allEdges = getEdges();
    
    if (!nodes || !nodes.length) return;
    
    // 获取并排序主干节点
    const mainNodes = nodes.filter(node => node.data.nodeType === 'main')
                         .sort((a, b) => a.position.y - b.position.y);
    
    if (mainNodes.length <= 1) return;
    
    console.log('排序后的主干节点顺序:', mainNodes.map(n => n.id));
    
    // 找出现有的主干连接
    const mainEdges = allEdges.filter(edge => {
      const sourceNode = findNode(edge.source);
      const targetNode = findNode(edge.target);
      return sourceNode?.data?.nodeType === 'main' && targetNode?.data?.nodeType === 'main';
    });
    
    // 创建一个映射表，记录哪些节点间已有连接
    const existingConnections = new Map();
    mainEdges.forEach(edge => {
      existingConnections.set(`${edge.source}-${edge.target}`, edge);
    });
    
    // 检查是否需要更新连接
    let needUpdate = false;
    let missingConnections = [];
    
    // 检测主干节点之间是否有连接缺失
    for (let i = 0; i < mainNodes.length - 1; i++) {
      const source = mainNodes[i].id;
      const target = mainNodes[i + 1].id;
      const key = `${source}-${target}`;
      
      if (!existingConnections.has(key)) {
        needUpdate = true;
        missingConnections.push({ source, target });
      }
    }
    
    // 如果不需要更新，直接返回
    if (!needUpdate) {
      console.log('主干连接已经是最新状态，无需更新');
      return;
    }
    
    console.log('发现缺失的主干连接，添加这些连接');
    
    // 为每个缺失的连接创建新边
    const newEdges = missingConnections.map(conn => ({
      id: `e-main-${conn.source}-${conn.target}-${Date.now()}`,
      source: conn.source,
      sourceHandle: 'bottom',
      target: conn.target,
      targetHandle: 'top',
      type: EdgeTypeEnum.SmoothStep,
      style: {
        strokeWidth: 4,
        stroke: '#409EFF'
      },
      markerEnd: {
        type: MarkerTypeEnum.ArrowClosed,
        color: '#409EFF'
      },
      animated: false,
      updatable: true,
      draggable: true
    }));
    
    // 添加新边
    if (newEdges.length > 0) {
      addEdges(newEdges);
      console.log(`已添加 ${newEdges.length} 条缺失的主干连接`);
    }
    
    // 检查多余的连接并移除
    const validConnections = new Set();
    for (let i = 0; i < mainNodes.length - 1; i++) {
      validConnections.add(`${mainNodes[i].id}-${mainNodes[i+1].id}`);
    }
    
    const redundantEdges = mainEdges.filter(edge => 
      !validConnections.has(`${edge.source}-${edge.target}`)
    );
    
    if (redundantEdges.length > 0) {
      console.log(`移除 ${redundantEdges.length} 条多余的主干连接`);
      removeEdges(redundantEdges.map(e => e.id));
    }
    
    return true;
  } catch (error) {
    console.error('优化主干连接时出错:', error);
    return false;
  }
};

// 点击画布空白处
const onPaneClick = () => {
  // 隐藏右键菜单
  contextMenuVisible.value = false;
  
  // 清空选中状态
  contextMenuType.value = '';
  contextMenuNodeId.value = '';
  contextMenuEdgeId.value = '';
};

// 节点点击处理
const onNodeClick = (event) => {
  console.log('节点被点击:', event.node);
  // 隐藏右键菜单
  contextMenuVisible.value = false;
};

// 节点右键菜单处理
const onNodeContextMenu = (event) => {
  // 阻止默认右键菜单
  event.event.preventDefault();

  // 获取鼠标位置
  const { clientX, clientY } = event.event;

  // 获取容器元素
  const container = document.querySelector('.timeline-flow-wrapper');
  if (!container) return;

  // 获取容器的边界矩形
  const containerRect = container.getBoundingClientRect();

  // 计算相对于容器的位置
  const relativeX = clientX - containerRect.left;
  const relativeY = clientY - containerRect.top;

  // 显示自定义右键菜单
  contextMenuVisible.value = true;
  contextMenuStyle.value = {
    position: 'absolute',
    top: `${relativeY}px`,
    left: `${relativeX}px`
  };
  contextMenuNodeId.value = event.node.id;
  contextMenuNodeType.value = event.node.data.nodeType;
  contextMenuType.value = 'node';
};

// 处理右键菜单操作
const handleContextMenuAction = (action) => {
  const nodeId = contextMenuNodeId.value;
  if (!nodeId) return;
  
  const node = findNode(nodeId);
  if (!node) return;
  
  switch (action) {
    case 'edit':
      openEditDialog(nodeId, node.data);
      break;
    case 'delete':
      deleteNode(nodeId);
      break;
    case 'add-up':
      prepareAddEvent(nodeId, 'up');
      break;
    case 'add-down':
      prepareAddEvent(nodeId, 'down');
      break;
    case 'add-left':
      prepareAddEvent(nodeId, 'left');
      break;
    case 'add-right':
      prepareAddEvent(nodeId, 'right');
      break;
    case 'add-free':
      prepareAddFreeEvent(nodeId);
      break;
  }
  
  // 隐藏右键菜单
  contextMenuVisible.value = false;
};

// 处理连接创建
const onConnect = (connection) => {
  console.log('创建新连接:', connection);
  
  if (!connection.source || !connection.target) return;
  
  try {
    // 获取源节点和目标节点
    const sourceNode = findNode(connection.source);
    const targetNode = findNode(connection.target);
    
    if (!sourceNode || !targetNode) return;
    
    // 创建边ID
    const edgeId = `e-${connection.source}-${connection.target}-${nanoid(4)}`;
    
    // 基本边属性
    let newEdge = {
      id: edgeId,
      ...connection,
      type: EdgeTypeEnum.SmoothStep,
      animated: true,
      style: { cursor: 'move', strokeWidth: 2 }
    };
    
    // 根据节点类型设置边样式
    if (sourceNode.data.nodeType === 'main' && targetNode.data.nodeType === 'main') {
      // 主干到主干连接
      newEdge.type = EdgeTypeEnum.Straight;
      newEdge.style = {
        ...newEdge.style,
        strokeWidth: 4,
        stroke: '#409EFF'
      };
      
      // 添加箭头标记
      newEdge.markerEnd = {
        type: MarkerTypeEnum.ArrowClosed,
        color: '#409EFF'
      };
      
      // 验证连接点是否是上下方向
      if (connection.sourceHandle === 'bottom' && connection.targetHandle === 'top') {
        // 正确的上下连接
      } else {
        // 如果不是期望的连接方式，强制使用上下连接
        newEdge.sourceHandle = 'bottom';
        newEdge.targetHandle = 'top';
      }
    } else if (sourceNode.data.nodeType === 'branch' && targetNode.data.nodeType === 'main') {
      // 分支到主干连接
      const color = sourceNode.data.color || getRandomColor();
      
      newEdge.type = EdgeTypeEnum.SmoothStep;
      newEdge.style = {
        ...newEdge.style,
        strokeWidth: 3,
        stroke: color
      };
      
      // 确保箭头在终点
      newEdge.markerEnd = {
        type: MarkerTypeEnum.ArrowClosed,
        color: color
      };
    } else if (sourceNode.data.nodeType === 'main' && targetNode.data.nodeType === 'branch') {
      // 主干到分支连接
      const color = targetNode.data.color || getRandomColor();
      
      newEdge.type = EdgeTypeEnum.SmoothStep;
      newEdge.style = {
        ...newEdge.style,
        strokeWidth: 3,
        stroke: color
      };
      
      // 确保箭头在终点
      newEdge.markerEnd = {
        type: MarkerTypeEnum.ArrowClosed,
        color: color
      };
    } else if (sourceNode.data.nodeType === 'branch' && targetNode.data.nodeType === 'branch') {
      // 分支到分支连接
      const color = sourceNode.data.color || getRandomColor();
      
      newEdge.type = EdgeTypeEnum.SmoothStep;
      newEdge.style = {
        ...newEdge.style,
        strokeWidth: 3,
        stroke: color
      };
      
      // 确保箭头在终点
      newEdge.markerEnd = {
        type: MarkerTypeEnum.ArrowClosed,
        color: color
      };
    }
    
    // 添加新边 - 使用Vue Flow的API添加边而不是直接修改数组
    addEdges([newEdge]);
    
    // 更新节点关系 - 这里是导致问题的地方
    if (sourceNode.data.nodeType === 'main' && targetNode.data.nodeType === 'branch') {
      // 主干到分支连接 - 更新targetNode的parentId，使用Vue的updateNode方法
      updateNode(targetNode.id, {
        ...targetNode,
        data: {
          ...targetNode.data,
          parentId: sourceNode.id
        }
      });
    }
    else if (sourceNode.data.nodeType === 'branch' && targetNode.data.nodeType === 'main') {
      // 分支到主干连接 - 更新sourceNode的targetId
      updateNode(sourceNode.id, {
        ...sourceNode,
        data: {
          ...sourceNode.data,
          targetId: targetNode.id
        }
      });
    }
    
    console.log('成功创建新连接');
    
    // 触发数据变更事件
    triggerDataChanged();
  } catch (error) {
    console.error('创建连接时出错:', error);
  }
};

// 边更新处理
const onEdgeUpdate = (params) => {
  const { edge, connection } = params;
  
  console.log('边更新:', edge.id, connection);
  
  try {
    // 保存原始边的拖动状态
    const originalEdge = findEdge(edge.id);
    const originalOffsetY = originalEdge?.data?.offsetY || 0;
    
    // 使用 Vue Flow 内置的 updateEdge 函数更新边
    const success = updateEdge(edge, connection);
    if (!success) {
      console.error('边更新失败');
      return;
    }
    
    // 获取源节点和目标节点
    const sourceNode = findNode(connection.source);
    const targetNode = findNode(connection.target);
    
    if (sourceNode && targetNode) {
      // 获取更新后的边
      const updatedEdge = getEdges().find(e => e.id === edge.id || (e.source === connection.source && e.target === connection.target));
      
      if (updatedEdge) {
        // 主线到主线的连接 - 保持SmoothStep类型
        if (sourceNode.data.nodeType === 'main' && targetNode.data.nodeType === 'main') {
          updatedEdge.type = EdgeTypeEnum.SmoothStep;
          
          // 确保有箭头标记
          if (!updatedEdge.markerEnd) {
            updatedEdge.markerEnd = {
              type: MarkerTypeEnum.ArrowClosed,
              color: updatedEdge.style?.stroke || '#409EFF'
            };
          }
        }
        // 分支到分支连接
        else if (sourceNode.data.nodeType === 'branch' && targetNode.data.nodeType === 'branch') {
          // 确保有箭头标记
          if (!updatedEdge.markerEnd) {
            updatedEdge.markerEnd = {
              type: MarkerTypeEnum.ArrowClosed,
              color: sourceNode.data.color || '#409EFF'
            };
          }
        }
      }
      
      // 处理分支节点连接
      if (sourceNode.data.nodeType === 'branch' && targetNode.data.nodeType === 'main') {
        // 分支到主干
        updateNode(sourceNode.id, {
          ...sourceNode,
          data: {
            ...sourceNode.data,
            targetId: targetNode.id
          }
        });
      } 
      else if (sourceNode.data.nodeType === 'main' && targetNode.data.nodeType === 'branch') {
        // 主干到分支
        updateNode(targetNode.id, {
          ...targetNode,
          data: {
            ...targetNode.data,
            parentId: sourceNode.id
          }
        });
      }
      
      // 触发重排来更新布局
      nextTick(() => {
        autoLayout();
      });
    }
  } catch (error) {
    console.error('边更新处理出错:', error);
  }
};

// 边点击处理
const onEdgeClick = (params) => {
  const { edge, event } = params;
  console.log('边被点击:', edge);
  
  // 双击删除边
  if (event.detail === 2) { 
    handleEdgeDelete(edge);
  }
};

// 边右键菜单
const onEdgeContextMenu = (params) => {
  // 阻止默认右键菜单
  params.event.preventDefault();

  // 获取鼠标位置
  const { clientX, clientY } = params.event;

  // 获取容器元素
  const container = document.querySelector('.timeline-flow-wrapper');
  if (!container) return;

  // 获取容器的边界矩形
  const containerRect = container.getBoundingClientRect();

  // 计算相对于容器的位置
  const relativeX = clientX - containerRect.left;
  const relativeY = clientY - containerRect.top;

  // 显示自定义右键菜单
  contextMenuVisible.value = true;
  contextMenuStyle.value = {
    position: 'absolute',
    top: `${relativeY}px`,
    left: `${relativeX}px`
  };
  contextMenuType.value = 'edge';
  contextMenuEdgeId.value = params.edge.id;

  console.log('显示边右键菜单:', params.edge.id);
};

// 处理边右键菜单操作
const handleEdgeMenuAction = (action) => {
  const edgeId = contextMenuEdgeId.value;
  if (!edgeId) return;
  
  const edge = getEdges().find(e => e.id === edgeId);
  if (!edge) return;
  
  console.log('边菜单操作:', action, edgeId);
  
  switch (action) {
    case 'change-style':
      openEdgeStyleDialog(edge);
      break;
    case 'delete':
      handleEdgeDelete(edge);
      break;
  }
  
  // 隐藏右键菜单
  contextMenuVisible.value = false;
};

// 边样式对话框状态
const edgeStyleDialogVisible = ref(false);
const editingEdge = ref(null);
const editingEdgeStyle = ref({
  strokeWidth: 2,
  stroke: '#409EFF',
  animated: false
});

// 打开边样式对话框
const openEdgeStyleDialog = (edge) => {
  editingEdge.value = edge;
  editingEdgeStyle.value = {
    strokeWidth: edge.style?.strokeWidth || 2,
    stroke: edge.style?.stroke || '#409EFF',
    animated: edge.animated || false
  };
  edgeStyleDialogVisible.value = true;
};

// 保存边样式
const saveEdgeStyle = () => {
  if (!editingEdge.value) return;
  
  try {
    const edge = findEdge(editingEdge.value.id);
    if (!edge) return;
    
    // 保存当前的拖动状态
    const currentOffsetY = edge.data?.offsetY || 0;
    
    // 更新边样式
    edge.style = {
      ...edge.style,
      strokeWidth: editingEdgeStyle.value.strokeWidth,
      stroke: editingEdgeStyle.value.stroke
    };
    edge.animated = editingEdgeStyle.value.animated;
    
    // 确保保持拖动状态
    if (!edge.data) edge.data = {};
    edge.data.offsetY = currentOffsetY;
    
    // 如果有箭头标记，也更新它的颜色
    if (edge.markerEnd) {
      edge.markerEnd.color = editingEdgeStyle.value.stroke;
    }
    
    console.log('边样式已更新:', edge.id);
    edgeStyleDialogVisible.value = false;
  } catch (error) {
    console.error('更新边样式时出错:', error);
  }
};

// 处理边删除
const handleEdgeDelete = (edge) => {
  try {
    // 检查是否是主干连接
    const sourceNode = findNode(edge.source);
    const targetNode = findNode(edge.target);
    
    if (sourceNode?.data.nodeType === 'main' && targetNode?.data.nodeType === 'main') {
      // 主干连接不允许直接删除，因为会破坏时间线连续性
      ElMessageBox.alert('主干连接不能直接删除，请移除相关节点', '提示');
      return;
    }
    
    // 确认是否删除
    ElMessageBox.confirm('确定删除此连接吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 删除边
      removeEdges([edge.id]);
      console.log('边已删除:', edge.id);
      
      // 如果是分支到主干或主干到分支的连接，更新节点数据
      if (sourceNode?.data.nodeType === 'branch' && targetNode?.data.nodeType === 'main') {
        // 分支到主干连接
        updateNode(sourceNode.id, {
          ...sourceNode,
          data: {
            ...sourceNode.data,
            targetId: null // 移除目标ID
          }
        });
      } else if (sourceNode?.data.nodeType === 'main' && targetNode?.data.nodeType === 'branch') {
        // 主干到分支连接
        updateNode(targetNode.id, {
          ...targetNode,
          data: {
            ...targetNode.data,
            parentId: null // 移除父节点ID
          }
        });
      }
    }).catch(() => {
      // 用户取消
    });
  } catch (error) {
    console.error('处理边删除时出错:', error);
  }
};

// ===== 节点操作方法 =====
// 添加主干事件
const addMainEvent = () => {
  try {
    // 获取当前最大Y坐标
    const nodes = getNodes();
    if (!nodes) {
      console.error('获取节点失败');
      return;
    }
    
    const mainNodes = nodes.filter(n => n.data.nodeType === 'main');
    const maxY = mainNodes.length > 0 
      ? Math.max(...mainNodes.map(n => n.position.y))
      : 0;
    
    // 创建新节点
    const newNodeId = `main-${nanoid(6)}`;
    const newNode = {
      id: newNodeId,
      type: 'main-event',
      position: { x: centerX, y: maxY + 150 },
      connectable: true,
      data: {
        label: '新事件',
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
        content: '',
        nodeType: 'main',
      color: '#409EFF'
      }
    };
    
    // 使用数组操作替换 addNodes
    nodes.value = [...nodes.value, newNode];
    
    // 如果有其他主干节点，就连接到最后一个
    if (mainNodes.length > 0) {
      const lastMainNode = mainNodes.reduce((a, b) => a.position.y > b.position.y ? a : b);
      
      // 创建边 - 使用上下连接
      const newEdge = {
        id: `e-${lastMainNode.id}-${newNodeId}`,
        source: lastMainNode.id,
        sourceHandle: 'bottom',
        target: newNodeId,
        targetHandle: 'top',
        type: EdgeTypeEnum.SmoothStep,
        style: {
          strokeWidth: 4,
          stroke: '#409EFF'
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: '#409EFF'
        },
        updatable: true,
        draggable: true,
        data: {
          editable: true,
          offsetY: 0
        }
      };
      
      // 使用数组操作替换 addEdges
      edges.value = [...edges.value, newEdge];
    }
    
    // 打开编辑对话框
    openEditDialog(newNodeId, newNode.data);
  } catch (error) {
    console.error('添加主干事件时出错:', error);
  }
};

// 添加分支事件
const addBranchEvent = () => {
  try {
    // 先检查是否有主干节点
    const nodes = getNodes();
    if (!nodes) {
      console.error('获取节点失败');
      return;
    }
    
    const mainNodes = nodes.filter(n => n.data.nodeType === 'main');
    if (mainNodes.length === 0) {
      ElMessageBox.alert('请先添加主干事件', '提示');
      return;
    }
    
    // 随机选择一个主干节点作为父节点
    const parentNode = mainNodes[Math.floor(Math.random() * mainNodes.length)];
    
    // 随机决定是左侧还是右侧分支
    const isLeftSide = Math.random() > 0.5;
    
    // 创建新节点
    const color = getRandomColor();
    const newNodeId = `branch-${nanoid(6)}`;
    const newNode = {
      id: newNodeId,
      type: 'branch-event',
      position: { 
        x: isLeftSide ? centerX - 250 : centerX + 250, 
        y: parentNode.position.y 
      },
      connectable: true,
      data: {
        label: '分支事件',
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
        content: '',
        nodeType: 'branch',
        color: color,
        parentId: parentNode.id,
        isLeftSide: isLeftSide
      }
    };
    
    // 添加节点
    addNodes([newNode]);
    
    // 创建边
    const newEdge = {
      id: `e-${parentNode.id}-${newNodeId}`,
      source: parentNode.id,
      sourceHandle: isLeftSide ? 'left' : 'right',
      target: newNodeId,
      targetHandle: isLeftSide ? 'right' : 'left',
      type: EdgeTypeEnum.SmoothStep,
      animated: true,
      style: {
        strokeWidth: 3,
        stroke: color
      },
      markerEnd: {
        type: MarkerTypeEnum.ArrowClosed,
        color: color
      }
    };
    
    // 添加边
    addEdges([newEdge]);
    
    // 打开编辑对话框
    openEditDialog(newNodeId, newNode.data);
  } catch (error) {
    console.error('添加分支事件时出错:', error);
  }
};

// 编辑节点
const openEditDialog = (nodeId, nodeData) => {
  // 复制节点数据到编辑对象，确保数字字段是数字类型
  editingEvent.value = {
    ...nodeData,
    year: nodeData.year ? Number(nodeData.year) : nodeData.year,
    month: nodeData.month ? Number(nodeData.month) : nodeData.month,
    day: nodeData.day ? Number(nodeData.day) : nodeData.day
  };

  // 根据是否有customTime字段决定当前时间模式
  isCustomTimeMode.value = !!nodeData.customTime;

  editingNodeId.value = nodeId;
  editDialogVisible.value = true;
};

// 保存编辑
const saveEdit = () => {
  try {
    // 如果是新节点，则创建节点
    if (editingNodeId.value === 'new-node') {
      createNewNodeAfterEdit();
      return;
    }
    
    // 更新现有节点
    if (!editingNodeId.value) return;
    
    // 获取节点
    const node = findNode(editingNodeId.value);
    if (!node) return;
    
    // 更新节点数据
    updateNode(editingNodeId.value, {
      ...node,
      data: {
        ...node.data,
        label: editingEvent.value.label,
        year: editingEvent.value.year,
        month: editingEvent.value.month,
        day: editingEvent.value.day,
        content: editingEvent.value.content,
        color: editingEvent.value.color || node.data.color,
        // 添加自定义时间字段
        customTime: isCustomTimeMode.value ? editingEvent.value.customTime : null,
        // 标记是否使用自定义时间
        useCustomTime: isCustomTimeMode.value
      }
    });
    
    // 如果是分支节点，更新连接线样式
    if (node.data.nodeType === 'branch' && editingEvent.value.color) {
      const edges = getEdges();
      if (!edges) {
        console.error('获取边失败');
        return;
      }
      
      const nodeEdges = edges.filter(e => 
        e.source === editingNodeId.value || e.target === editingNodeId.value
      );
      
      nodeEdges.forEach(edge => {
        edge.style = {
          ...edge.style,
          stroke: editingEvent.value.color
        };
        
        if (edge.markerEnd) {
          edge.markerEnd.color = editingEvent.value.color;
        }
      });
    }
    
    // 关闭对话框
    editDialogVisible.value = false;
    editingNodeId.value = '';
    
    // 触发数据变更事件
    triggerDataChanged();
  } catch (error) {
    console.error('保存编辑时出错:', error);
    editDialogVisible.value = false;
    editingNodeId.value = '';
  }
};

// 创建新节点
const createNewNodeAfterEdit = () => {
  try {
    const info = pendingEventInfo.value;
    if (!info.sourceNodeId) {
      console.error('源节点ID为空');
      editDialogVisible.value = false;
      return;
    }
    
    const sourceNode = findNode(info.sourceNodeId);
    if (!sourceNode) {
      console.error('找不到源节点');
      editDialogVisible.value = false;
      return;
    }
    
    // 创建新节点ID
    const newNodeId = info.nodeType === 'main' ? 
      `main-${nanoid(6)}` : `branch-${nanoid(6)}`;
    
    // 创建新节点
    const newNode = {
      id: newNodeId,
      type: info.nodeType === 'main' ? 'main-event' : 'branch-event',
      position: info.position,
      connectable: true,
      data: {
        label: editingEvent.value.label,
        year: editingEvent.value.year,
        month: editingEvent.value.month,
        day: editingEvent.value.day,
        content: editingEvent.value.content,
        nodeType: info.nodeType,
        color: editingEvent.value.color || (info.nodeType === 'main' ? '#409EFF' : getRandomColor()),
        parentId: info.nodeType === 'branch' && !info.isFreeEvent ? info.sourceNodeId : null,
        isLeftSide: info.direction === 'left',
        // 添加自定义时间字段
        customTime: isCustomTimeMode.value ? editingEvent.value.customTime : null,
        // 标记是否使用自定义时间
        useCustomTime: isCustomTimeMode.value
      }
    };
    
    // 添加节点
    addNodes([newNode]);
    console.log(`节点 ${newNodeId} 已添加`);
    
    // 如果不是自由事件，则创建连接线
    if (!info.isFreeEvent) {
      // 根据不同方向创建连接
      switch (info.direction) {
        case 'up':
          addUpEventConnection(sourceNode, newNode);
          break;
        case 'down':
          addDownEventConnection(sourceNode, newNode);
          break;
        case 'left':
          addLeftEventConnection(sourceNode, newNode);
          break;
        case 'right':
          addRightEventConnection(sourceNode, newNode);
          break;
      }
    }
    
    // 关闭对话框
    editDialogVisible.value = false;
    editingNodeId.value = '';
    
    // 重新布局
    if (info.nodeType === 'main' && (info.direction === 'up' || info.direction === 'down')) {
      setTimeout(() => {
        reorderMainNodes();
      }, 100);
    }
  } catch (error) {
    console.error('创建新节点时出错:', error);
    editDialogVisible.value = false;
    editingNodeId.value = '';
  }
};

// 添加向上事件的连接
const addUpEventConnection = (sourceNode, newNode) => {
  if (sourceNode.data.nodeType === 'main') {
    // 对于主干节点，使用简单的连接逻辑
    const edge = {
      id: `e-${newNode.id}-${sourceNode.id}`,
      source: newNode.id,
      sourceHandle: 'bottom',
      target: sourceNode.id,
      targetHandle: 'top',
      type: EdgeTypeEnum.SmoothStep,
      style: {
        strokeWidth: 4,
        stroke: '#409EFF'
      },
      markerEnd: {
        type: MarkerTypeEnum.ArrowClosed,
        color: '#409EFF'
      },
      updatable: true,
      draggable: true,
      data: {
        editable: true,
        offsetY: 0
      }
    };
    // 使用Vue Flow的addEdges API添加边
    addEdges([edge]);
  } else {
    // 对于分支节点
    const edgeColor = newNode.data.color;
    const newEdge = {
      id: `e-${newNode.id}-${sourceNode.id}`,
      source: newNode.id,
      sourceHandle: 'bottom',
      target: sourceNode.id,
      targetHandle: 'top',
      type: EdgeTypeEnum.SmoothStep,
      animated: true,
      style: {
        strokeWidth: 3,
        stroke: edgeColor
      },
      markerEnd: {
        type: MarkerTypeEnum.ArrowClosed,
        color: edgeColor
      }
    };
    // 使用Vue Flow的addEdges API添加边
    addEdges([newEdge]);
  }
};

// 添加向下事件的连接
const addDownEventConnection = (sourceNode, newNode) => {
  if (sourceNode.data.nodeType === 'main') {
    // 对于主干节点，找出当前的连接关系
    const edgesData = getEdges();
    const outgoingEdges = edgesData.filter(e => e.source === sourceNode.id && findNode(e.target)?.data.nodeType === 'main');
    const nextNodeId = outgoingEdges.length > 0 ? outgoingEdges[0].target : null;
    
    // 如果有后续节点，删除当前节点到后续节点的连接
    if (nextNodeId) {
      const edgeToRemove = edgesData.find(e => e.source === sourceNode.id && e.target === nextNodeId);
      if (edgeToRemove) {
        removeEdges([edgeToRemove.id]);
      }
    }
    
    // 添加当前节点到新节点的连接
    const edge1 = {
      id: `e-${sourceNode.id}-${newNode.id}`,
      source: sourceNode.id,
      sourceHandle: 'bottom',
      target: newNode.id,
      targetHandle: 'top',
      type: EdgeTypeEnum.SmoothStep,
      style: {
        strokeWidth: 4,
        stroke: '#409EFF'
      },
      markerEnd: {
        type: MarkerTypeEnum.ArrowClosed,
        color: '#409EFF'
      },
      updatable: true,
      draggable: true,
      data: {
        editable: true,
        offsetY: 0
      }
    };
    // 使用Vue Flow的addEdges API添加边
    addEdges([edge1]);
    
    // 如果有后续节点，添加新节点到后续节点的连接
    if (nextNodeId) {
      const edge2 = {
        id: `e-${newNode.id}-${nextNodeId}`,
        source: newNode.id,
        sourceHandle: 'bottom',
        target: nextNodeId,
        targetHandle: 'top',
        type: EdgeTypeEnum.SmoothStep,
        style: {
          strokeWidth: 4,
          stroke: '#409EFF'
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: '#409EFF'
        },
        updatable: true,
        draggable: true,
        data: {
          editable: true,
          offsetY: 0
        }
      };
      // 使用Vue Flow的addEdges API添加边
      addEdges([edge2]);
    }
  } else {
    // 对于分支节点
    const edgeColor = sourceNode.data.color;
    const newEdge = {
      id: `e-${sourceNode.id}-${newNode.id}`,
      source: sourceNode.id,
      sourceHandle: 'bottom',
      target: newNode.id,
      targetHandle: 'top',
      type: EdgeTypeEnum.SmoothStep,
      animated: true,
      style: {
        strokeWidth: 3,
        stroke: edgeColor
      },
      markerEnd: {
        type: MarkerTypeEnum.ArrowClosed,
        color: edgeColor
      }
    };
    // 使用Vue Flow的addEdges API添加边
    addEdges([newEdge]);
  }
};

// 添加向左事件的连接
const addLeftEventConnection = (sourceNode, newNode) => {
  const color = newNode.data.color;
  const newEdge = {
    id: `e-${sourceNode.id}-${newNode.id}`,
    source: sourceNode.id,
    sourceHandle: 'left',
    target: newNode.id,
    targetHandle: 'right',
    type: EdgeTypeEnum.SmoothStep,
    animated: true,
    style: {
      strokeWidth: 3,
      stroke: color
    },
    markerEnd: {
      type: MarkerTypeEnum.ArrowClosed,
      color: color
    }
  };
  // 使用Vue Flow的addEdges API添加边
  addEdges([newEdge]);
};

// 添加向右事件的连接
const addRightEventConnection = (sourceNode, newNode) => {
  const color = newNode.data.color;
  const newEdge = {
    id: `e-${sourceNode.id}-${newNode.id}`,
    source: sourceNode.id,
    sourceHandle: 'right',
    target: newNode.id,
    targetHandle: 'left',
    type: EdgeTypeEnum.SmoothStep,
    animated: true,
    style: {
      strokeWidth: 3,
      stroke: color
    },
    markerEnd: {
      type: MarkerTypeEnum.ArrowClosed,
      color: color
    }
  };
  // 使用Vue Flow的addEdges API添加边
  addEdges([newEdge]);
};

// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
  editingNodeId.value = '';
};

// 处理对话框遮罩层点击
const handleDialogOverlayClick = (event) => {
  // 只有点击遮罩层本身时才关闭对话框
  if (event.target === event.currentTarget) {
    cancelEdit();
  }
};

// 处理边对话框遮罩层点击
const handleEdgeDialogOverlayClick = (event) => {
  // 只有点击遮罩层本身时才关闭对话框
  if (event.target === event.currentTarget) {
    edgeStyleDialogVisible.value = false;
  }
};

// 处理导入对话框遮罩层点击
const handleImportDialogOverlayClick = (event) => {
  // 只有点击遮罩层本身时才关闭对话框
  if (event.target === event.currentTarget) {
    importDialogVisible.value = false;
  }
};

// 删除节点
const deleteNode = (nodeId) => {
  // 确认删除
  ElMessageBox.confirm('确定删除此节点吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    try {
      const node = findNode(nodeId);
      if (!node) return;
      
      // 获取节点类型
      const nodeType = node.data.nodeType;
      
      if (nodeType === 'main') {
        // 如果是主干节点，需要处理主干连接
        handleMainNodeDeletion(nodeId);
      } else {
        // 如果是分支节点，直接删除
        removeNodes([nodeId]);
        
        // 删除相关的边
        const edges = getEdges();
        const relatedEdges = edges.filter(e => e.source === nodeId || e.target === nodeId);
        if (relatedEdges.length > 0) {
          removeEdges(relatedEdges.map(edge => edge.id));
        }
      }
      
      // 触发数据变更事件
      triggerDataChanged();
    } catch (error) {
      console.error('删除节点时出错:', error);
    }
  }).catch(() => {});
};

// 处理主干节点删除
const handleMainNodeDeletion = (nodeId) => {
  // 获取所有主干节点和边
  const nodes = getNodes();
  const edgesData = getEdges();
  
  // 找出要删除节点的前后节点
  const incomingEdges = edgesData.filter(e => e.target === nodeId && findNode(e.source)?.data.nodeType === 'main');
  const outgoingEdges = edgesData.filter(e => e.source === nodeId && findNode(e.target)?.data.nodeType === 'main');
  
  // 获取前后节点ID
  const prevNodeId = incomingEdges.length > 0 ? incomingEdges[0].source : null;
  const nextNodeId = outgoingEdges.length > 0 ? outgoingEdges[0].target : null;
  
  console.log(`删除主干节点 ${nodeId}，前节点: ${prevNodeId || '无'}，后节点: ${nextNodeId || '无'}`);
  
  // 删除节点及相关的所有边
  removeNodes([nodeId]);
  const relatedEdges = edgesData.filter(e => e.source === nodeId || e.target === nodeId);
  if (relatedEdges.length > 0) {
    removeEdges(relatedEdges.map(edge => edge.id));
  }
  
  // 处理连接到已删除节点的分支节点
  const branchNodes = nodes.filter(n => n.data.nodeType === 'branch' && n.data.parentId === nodeId);
  
  // 如果有前一个节点，将分支节点连接到它
  if (prevNodeId && branchNodes.length > 0) {
    branchNodes.forEach(branchNode => {
      // 更新分支节点的父节点ID
      updateNode(branchNode.id, {
        ...branchNode,
        data: {
          ...branchNode.data,
          parentId: prevNodeId
        }
      });
      
      // 创建新连接
      const isLeftSide = branchNode.data.isLeftSide;
      const newEdge = {
        id: `e-${prevNodeId}-${branchNode.id}`,
        source: prevNodeId,
        sourceHandle: isLeftSide ? 'left' : 'right',
        target: branchNode.id,
        targetHandle: isLeftSide ? 'right' : 'left',
        type: EdgeTypeEnum.SmoothStep,
        animated: true,
        style: {
          strokeWidth: 3,
          stroke: branchNode.data.color
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: branchNode.data.color
        }
      };
      // 添加新边
      addEdges([newEdge]);
    });
  }
  
  // 使用新函数重建主干连接，代替之前的手动连接代码
  nextTick(() => {
    replaceMainConnections();
  });
};

// 向上添加事件
const addUpEvent = (nodeId) => {
  try {
    const node = findNode(nodeId);
    if (!node) return;
    
    // 创建新节点ID
    const newNodeId = node.data.nodeType === 'main' ? 
      `main-${nanoid(6)}` : `branch-${nanoid(6)}`;
    
    // 特意设置Y坐标比当前节点小，确保排序时在当前节点之前
    const position = {
      x: node.position.x,
      y: node.position.y - 180 // 用更大的间隔确保在排序时位于前方
    };
    
    console.log(`向上添加事件: 从节点 ${nodeId} 上方添加节点 ${newNodeId}, 位置: (${position.x}, ${position.y})`);
    
    // 创建新节点
    const newNode = {
      id: newNodeId,
      type: node.data.nodeType === 'main' ? 'main-event' : 'branch-event',
      position: position,
      connectable: true,
      data: {
        label: '新事件',
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
        content: '',
        nodeType: node.data.nodeType,
        color: node.data.nodeType === 'main' ? '#409EFF' : getRandomColor(),
        parentId: node.data.parentId,
        isLeftSide: node.data.isLeftSide
      }
    };
    
    // 添加节点
    addNodes([newNode]);
    console.log(`节点 ${newNodeId} 已添加`);
    
    if (node.data.nodeType === 'main') {
      // 对于主干节点，添加完成后直接使用replaceMainConnections重建主干连接
      nextTick(() => {
        replaceMainConnections();
        
        // 打开编辑对话框
        openEditDialog(newNodeId, newNode.data);
      });
    } else {
      // 对于分支节点，考虑分支连接
      const edgeType = EdgeTypeEnum.SmoothStep;
      const edgeColor = newNode.data.color;
      
      const newEdge = {
        id: `e-${newNodeId}-${nodeId}`,
        source: newNodeId,
        sourceHandle: 'bottom',
        target: nodeId,
        targetHandle: 'top',
        type: edgeType,
        animated: node.data.nodeType === 'branch',
        style: {
          strokeWidth: node.data.nodeType === 'branch' ? 3 : 4,
          stroke: edgeColor
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: edgeColor
        }
      };
      
      addEdges([newEdge]);
      
      // 打开编辑对话框
      openEditDialog(newNodeId, newNode.data);
    }
  } catch (error) {
    console.error('向上添加事件时出错:', error);
  }
};

// 向下添加事件
const addDownEvent = (nodeId) => {
  try {
    const node = findNode(nodeId);
    if (!node) return;
    
    // 创建新节点ID
    const newNodeId = node.data.nodeType === 'main' ? 
      `main-${nanoid(6)}` : `branch-${nanoid(6)}`;
    
    // 特意设置Y坐标稍大于当前节点，确保排序时在当前节点正后方
    const position = {
      x: node.position.x,
      y: node.position.y + 80 // 较小的偏移确保排序时紧跟当前节点
    };
    
    console.log(`向下添加事件: 从节点 ${nodeId} 下方添加节点 ${newNodeId}, 位置: (${position.x}, ${position.y})`);
    
    // 创建新节点
    const newNode = {
      id: newNodeId,
      type: node.data.nodeType === 'main' ? 'main-event' : 'branch-event',
      position: position,
      connectable: true,
      data: {
        label: '新事件',
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
        content: '',
        nodeType: node.data.nodeType,
        color: node.data.nodeType === 'main' ? '#409EFF' : getRandomColor(),
        parentId: node.data.parentId,
        isLeftSide: node.data.isLeftSide
      }
    };
    
    // 添加节点
    addNodes([newNode]);
    console.log(`节点 ${newNodeId} 已添加`);
    
    if (node.data.nodeType === 'main') {
      // 对于主干节点，添加完成后直接使用replaceMainConnections重建主干连接
      nextTick(() => {
        replaceMainConnections();
        
        // 打开编辑对话框
        openEditDialog(newNodeId, newNode.data);
      });
    } else {
      // 对于分支节点，考虑分支连接
      const edgeType = EdgeTypeEnum.SmoothStep;
      const edgeColor = node.data.color;
      
      const newEdge = {
        id: `e-${nodeId}-${newNodeId}`,
        source: nodeId,
        sourceHandle: 'bottom',
        target: newNodeId,
        targetHandle: 'top',
        type: edgeType,
        animated: node.data.nodeType === 'branch',
        style: {
          strokeWidth: node.data.nodeType === 'branch' ? 3 : 4,
          stroke: edgeColor
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: edgeColor
        }
      };
      
      addEdges([newEdge]);
      
      // 打开编辑对话框
      openEditDialog(newNodeId, newNode.data);
    }
  } catch (error) {
    console.error('向下添加事件时出错:', error);
  }
};

// 向左添加事件
const addLeftEvent = (nodeId) => {
  try {
    const node = findNode(nodeId);
    if (!node) return;
    
    // 创建新节点ID
    const newNodeId = `branch-${nanoid(6)}`;
    
    // 选择颜色
    const color = getRandomColor();
    
    // 计算位置 - 在当前节点左侧
    const position = {
      x: node.position.x - 250,
      y: node.position.y // 与源节点保持相同的Y坐标
    };
    
    // 创建新节点
    const newNode = {
      id: newNodeId,
      type: 'branch-event',
      position: position,
      connectable: true,
      data: {
        label: '分支事件',
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
        content: '',
        nodeType: 'branch',
        color: color,
        parentId: node.data.nodeType === 'main' ? nodeId : node.data.parentId,
        isLeftSide: true
      }
    };
    
    // 添加节点
    addNodes([newNode]);
    
    // 创建连接 - 如果源节点是主干节点，则主干节点作为source
    let newEdge;
    
    if (node.data.nodeType === 'main') {
      // 主干到分支的连接
      newEdge = {
        id: `e-${nodeId}-${newNodeId}`,
        source: nodeId,
        sourceHandle: 'left',
        target: newNodeId,
        targetHandle: 'right',
        type: EdgeTypeEnum.SmoothStep,
        animated: true,
        style: {
          strokeWidth: 3,
          stroke: color
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: color
        }
      };
    } else {
      // 分支到分支的连接，源节点为当前选中的分支节点
      newEdge = {
        id: `e-${nodeId}-${newNodeId}`,
        source: nodeId,
        sourceHandle: 'left',
        target: newNodeId,
        targetHandle: 'right',
        type: EdgeTypeEnum.SmoothStep,
        animated: true,
        style: {
          strokeWidth: 3,
          stroke: color
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: color
        }
      };
    }
    
    // 添加连接
    addEdges([newEdge]);
    
    // 打开编辑对话框
    openEditDialog(newNodeId, newNode.data);
  } catch (error) {
    console.error('向左添加事件时出错:', error);
  }
};

// 向右添加事件
const addRightEvent = (nodeId) => {
  try {
    const node = findNode(nodeId);
    if (!node) return;
    
    // 创建新节点ID
    const newNodeId = `branch-${nanoid(6)}`;
    
    // 选择颜色
    const color = getRandomColor();
    
    // 计算位置 - 在当前节点右侧
    const position = {
      x: node.position.x + 250,
      y: node.position.y // 与源节点保持相同的Y坐标
    };
    
    // 创建新节点
    const newNode = {
      id: newNodeId,
      type: 'branch-event',
      position: position,
      connectable: true,
      data: {
        label: '分支事件',
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
        content: '',
        nodeType: 'branch',
        color: color,
        parentId: node.data.nodeType === 'main' ? nodeId : node.data.parentId,
        isLeftSide: false
      }
    };
    
    // 添加节点
    addNodes([newNode]);
    
    // 创建连接 - 如果源节点是主干节点，则主干节点作为source
    let newEdge;
    
    if (node.data.nodeType === 'main') {
      // 主干到分支的连接
      newEdge = {
        id: `e-${nodeId}-${newNodeId}`,
        source: nodeId,
        sourceHandle: 'right',
        target: newNodeId,
        targetHandle: 'left',
        type: EdgeTypeEnum.SmoothStep,
        animated: true,
        style: {
          strokeWidth: 3,
          stroke: color
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: color
        }
      };
    } else {
      // 分支到分支的连接，源节点为当前选中的分支节点
      newEdge = {
        id: `e-${nodeId}-${newNodeId}`,
        source: nodeId,
        sourceHandle: 'right',
        target: newNodeId,
        targetHandle: 'left',
        type: EdgeTypeEnum.SmoothStep,
        animated: true,
        style: {
          strokeWidth: 3,
          stroke: color
        },
        markerEnd: {
          type: MarkerTypeEnum.ArrowClosed,
          color: color
        }
      };
    }
    
    // 添加连接
    addEdges([newEdge]);
    
    // 打开编辑对话框
    openEditDialog(newNodeId, newNode.data);
  } catch (error) {
    console.error('向右添加事件时出错:', error);
  }
};

// ===== 布局和辅助方法 =====
// 自动布局
const autoLayout = () => {
  try {
    console.log('开始自动布局...');
    
    // 获取所有节点
    const nodes = getNodes();
    if (!nodes) {
      console.error('获取节点失败');
      return;
    }
    
    // 确保所有主干节点在垂直中心线上
    const mainNodes = nodes.filter(n => n.data.nodeType === 'main');
    mainNodes.forEach(node => {
      if (node.position.x !== centerX) {
        updateNodePosition(node.id, {
          x: centerX,
          y: node.position.y
        });
      }
    });
    
    // 重排主干节点 - 这会使用replaceMainConnections处理主干连接
    reorderMainNodes();
    
    // 处理分支节点与主干的连接，采用更谨慎的逻辑
    const branchNodes = nodes.filter(n => n.data.nodeType === 'branch' && n.data.parentId);
    if (branchNodes.length > 0) {
      console.log(`正在检查 ${branchNodes.length} 个分支节点连接`);
      
      // 获取当前所有边
      const allEdges = getEdges();
      
      // 为每个分支节点检查连接是否存在
      branchNodes.forEach(node => {
        const parentId = node.data.parentId;
        if (!parentId) return;
        
        // 查找已存在的连接
        const existingConnection = allEdges.find(edge => 
          (edge.source === parentId && edge.target === node.id) || 
          (edge.source === node.id && edge.target === parentId)
        );
        
        // 仅当没有连接时才创建新连接
        if (!existingConnection) {
          console.log(`节点 ${node.id} 缺少到主干节点 ${parentId} 的连接，正在创建...`);
          setTimeout(() => {
            ensureBranchToMainConnection(node.id, parentId);
          }, 50);  // 使用延迟确保主干节点排序完成
        } else {
          // 连接存在，但可能需要刷新显示
          console.log(`节点 ${node.id} 到主干节点 ${parentId} 的连接已存在`);
          const sourceNode = existingConnection.source === node.id ? node : findNode(existingConnection.source);
          const targetNode = existingConnection.target === node.id ? node : findNode(existingConnection.target);
          
          // 只有在源节点或目标节点发生变化时才刷新连接
          if (sourceNode && targetNode && 
              (sourceNode.position.x !== sourceNode._rf?.position?.x || 
               sourceNode.position.y !== sourceNode._rf?.position?.y ||
               targetNode.position.x !== targetNode._rf?.position?.x ||
               targetNode.position.y !== targetNode._rf?.position?.y)) {
            
            console.log(`刷新节点 ${node.id} 的连接显示`);
            const updatedEdge = {
              ...existingConnection,
              style: { ...existingConnection.style }
            };
            updateEdge(existingConnection, updatedEdge);
          }
        }
      });
    }
    
    console.log('自动布局完成');
  } catch (error) {
    console.error('自动布局时出错:', error);
  }
};

// 重排主干节点
const reorderMainNodes = () => {
  try {
    console.log('开始重排主干节点...');
    // 获取所有主干节点
    const allNodes = getNodes();
    if (!allNodes || !Array.isArray(allNodes) || allNodes.length === 0) {
      console.error('获取节点失败或节点列表为空');
      return;
    }
    
    // 过滤并获取主干节点
    const mainNodes = allNodes.filter(node => node.data?.nodeType === 'main');
    console.log(`找到 ${mainNodes.length} 个主干节点`);
    if (mainNodes.length <= 1) {
      console.log('主干节点数量不足，无需重排');
      return; // 如果只有0或1个主节点，无需建立连接
    }
    
    // 按Y坐标排序
    mainNodes.sort((a, b) => a.position.y - b.position.y);

    // 确保所有主干节点的X坐标为centerX
    mainNodes.forEach(node => {
      if (node.position.x !== centerX) {
        updateNodePosition(node.id, {
          x: centerX,
          y: node.position.y
        });
      }
    });
    
    // 使用新函数重建主干连接
    replaceMainConnections();
    
  } catch (error) {
    console.error('重排主干节点时出错:', error);
  }
};

// 更新连接
const updateConnections = () => {
  try {
    console.log('开始更新连接...');
    
    // 使用replaceMainConnections维护主干连接
    replaceMainConnections();
    
    // 获取所有节点和边
    const nodes = getNodes();
    const allEdges = getEdges();
    if (!nodes || nodes.length === 0) {
      return;
    }
    
    // 只检查分支节点的连接，不强制重建所有连接
    const branchNodes = nodes.filter(n => n.data.nodeType === 'branch' && n.data.parentId);
    
    if (branchNodes.length > 0) {
      console.log(`检查 ${branchNodes.length} 个分支节点连接`);
      
      // 为每个分支节点验证连接
      branchNodes.forEach(node => {
        const mainNodeId = node.data.parentId;
        if (!mainNodeId) return;
        
        // 查找连接是否已存在
        const existingEdge = allEdges.find(edge => 
          (edge.source === mainNodeId && edge.target === node.id) || 
          (edge.source === node.id && edge.target === mainNodeId)
        );
        
        // 只有在连接不存在时才创建新连接
        if (!existingEdge) {
          console.log(`节点 ${node.id} 与主干节点 ${mainNodeId} 之间缺少连接，正在创建...`);
          ensureBranchToMainConnection(node.id, mainNodeId);
        } else {
          // 连接存在，但可能需要刷新显示
          console.log(`节点 ${node.id} 与主干节点 ${mainNodeId} 的连接已存在，刷新显示`);
          const updatedEdge = {
            ...existingEdge,
            style: { ...existingEdge.style }
          };
          updateEdge(existingEdge, updatedEdge);
        }
      });
    }
    
    console.log('连接更新完成');
  } catch (error) {
    console.error('更新连接时出错:', error);
  }
};

// 导出数据功能
const exportTimelineData = () => {
  try {
    // 获取当前节点和边
    const currentNodes = getNodes();
    const currentEdges = getEdges();
    
    // 转换节点格式为更简洁的结构
    const exportNodes = currentNodes.map(node => ({
      id: node.id,
      type: node.type,
      position: { x: node.position.x, y: node.position.y },
      x: node.position.x,
      y: node.position.y,
      data: {
        label: node.data.label,
        year: node.data.year,
        month: node.data.month,
        day: node.data.day,
        content: node.data.content,
        nodeType: node.data.nodeType,
        color: node.data.color,
        parentId: node.data.parentId,
        isLeftSide: node.data.isLeftSide,
        customTime: node.data.customTime,
        useCustomTime: node.data.useCustomTime
      },
      label: node.data.label,
      year: node.data.year,
      month: node.data.month,
      day: node.data.day,
      content: node.data.content,
      nodeType: node.data.nodeType,
      color: node.data.color,
      parentId: node.data.parentId,
      isLeftSide: node.data.isLeftSide,
      customTime: node.data.customTime,
      useCustomTime: node.data.useCustomTime
    }));
    
    // 转换边格式
    const exportEdges = currentEdges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      sourceHandle: edge.sourceHandle,
      targetHandle: edge.targetHandle,
      type: edge.type,
      animated: edge.animated,
      style: {
        strokeWidth: edge.style?.strokeWidth || 2,
        stroke: edge.style?.stroke || '#409EFF'
      },
      markerEnd: edge.markerEnd
    }));
    
    // 创建导出数据
    const exportData = {
      nodes: exportNodes,
      edges: exportEdges,
      version: "2.0"
    };
    
    // 转换为JSON字符串
    const jsonData = JSON.stringify(exportData, null, 2);
    
    // 尝试复制到剪贴板
    try {
      window.pywebview.api.copy_to_clipboard(jsonData).then(() => {
        ElMessage.success('数据已复制到剪贴板并开始下载');
      }).catch((err) => {
        console.error('剪贴板复制失败:', err);
        ElMessage.info('数据已导出为文件');
      });
    } catch (e) {
      console.error('剪贴板访问失败:', e);
      ElMessage.info('数据已导出为文件');
    }
    
    // 创建并下载文件
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `timeline-data-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('导出数据时出错:', error);
    ElMessage.error('导出数据失败');
  }
};

// 导入数据对话框状态
const importDialogVisible = ref(false);

// 数据输入框
const importJsonData = ref('');

// 数据操作方法
const handleDataOperation = (command) => {
  if (command === 'import') {
    importDialogVisible.value = true;
  } else if (command === 'export') {
    exportTimelineData();
  }
};
// 创建默认主线事件
const createDefaultMainEvent = () => {
  // 创建一个默认的主线事件节点
  const defaultNodeId = `main-${nanoid(6)}`;
  const defaultNode = {
    id: defaultNodeId,
    type: 'main-event',
    position: { x: centerX, y: 100 },
    connectable: true,
    data: {
      label: '故事起点',
      year: new Date().getFullYear(),
      month: new Date().getMonth() + 1,
      day: new Date().getDate(),
      content: '在此处开始您的故事',
      nodeType: 'main',
      color: '#409EFF'
    }
  };
  
  // 只添加一个默认节点，清空其他所有节点和边
  nodes.value = [defaultNode];
  edges.value = [];
  
  console.log('已创建默认主线事件节点:', defaultNodeId);
  
  // 调整视图以适应新节点
  nextTick(() => {
    fitView();
  });
  
  return {
    nodes: [defaultNode],
    edges: []
  };
};
// 增加对外暴露的方法
defineExpose({
  addMainEvent,
  addBranchEvent,
  autoLayout,
  getNodes,
  getEdges,
  fitView,
  exportTimelineData,
  createDefaultMainEvent,
  importFromJson: (data) => {
    try {
      // 如果是字符串，尝试解析
      if (typeof data === 'string') {
        try {
          data = JSON.parse(data);
        } catch (parseError) {
          console.error('解析数据失败:', parseError);
          return false;
        }
      }

      // 如果是后端返回的标准格式，提取其中的data字段
      if (data && data.status === 'success' && data.data) {
        data = data.data;
      }
      
      
      
      // 检查数据结构
      if (!data || (!data.nodes && !data.edges && !data.mainTrunkEvents)) {
        console.error('无效的时间线数据格式');
        return false;
      }
      
      // 检查是否为空数据（无节点或只有空数组）
      const isEmpty = 
        (!data.nodes || data.nodes.length === 0) && 
        (!data.mainTrunkEvents || data.mainTrunkEvents.length === 0);
        
      if (isEmpty) {
        console.log('导入的数据为空，创建一个默认节点');
        // 创建简单的默认节点而不是导入空数据
        createDefaultMainEvent();
        return true;
      }
      
      // 清空现有数据
      nodes.value = [];
      edges.value = [];
      
      // 导入节点数据
      nextTick(() => {
        // 判断数据格式并进行相应处理
        if (data.nodes && data.edges) {
          // 新格式：直接使用节点和边数据
          const nodesToAdd = data.nodes.map(node => ({
            id: node.id || `node-${nanoid(6)}`,
            type: node.nodeType === 'main' ? 'main-event' : 'branch-event',
            position: { 
              x: node.nodeType === 'main' ? centerX : (node.x || node.position?.x || (node.isLeftSide ? centerX - 250 : centerX + 250)), 
              y: node.y || node.position?.y || 100
            },
            data: {
              label: node.label || node.data?.label || '未命名事件',
              year: Number(node.year || node.data?.year || new Date().getFullYear()),
              month: Number(node.month || node.data?.month || new Date().getMonth() + 1),
              day: Number(node.day || node.data?.day || new Date().getDate()),
              content: node.content || node.data?.content || '',
              nodeType: node.nodeType || node.data?.nodeType || 'main',
              color: node.color || node.data?.color || (node.nodeType === 'main' ? '#409EFF' : getRandomColor()),
              parentId: node.parentId || node.data?.parentId,
              isLeftSide: node.isLeftSide || node.data?.isLeftSide,
              customTime: node.customTime || node.data?.customTime,
              useCustomTime: node.useCustomTime || node.data?.useCustomTime
            }
          }));
          
          console.log(`导入 ${nodesToAdd.length} 个节点`);
          
          // 一次性添加所有节点
          nodes.value = nodesToAdd;
          
          // 添加边
          const edgesToAdd = data.edges.map(edge => ({
            id: edge.id || `edge-${nanoid(6)}`,
            source: edge.source,
            target: edge.target,
            sourceHandle: edge.sourceHandle,
            targetHandle: edge.targetHandle,
            type: edge.type || EdgeTypeEnum.SmoothStep,
            animated: edge.animated || false,
            style: {
              strokeWidth: edge.style?.strokeWidth || 2,
              stroke: edge.style?.stroke || '#409EFF'
            },
            markerEnd: edge.markerEnd || {
              type: MarkerTypeEnum.ArrowClosed,
              color: edge.style?.stroke || '#409EFF'
            },
            data: edge.data
          }));
          
          console.log(`导入 ${edgesToAdd.length} 条边`);
          
          // 一次性添加所有边
          edges.value = edgesToAdd;
        } else if (data.mainTrunkEvents) {
          // 旧格式：主干事件、分支等
          // 创建主干节点
          const mainNodes = data.mainTrunkEvents.map((event, index) => ({
            id: event.id || `main-${nanoid(6)}`,
            type: 'main-event',
            position: { x: centerX, y: 100 + index * 150 },
            data: {
              label: event.title,
              year: Number(event.year),
              month: Number(event.month),
              day: Number(event.day),
              content: event.description || '',
              nodeType: 'main',
              color: '#409EFF'
            }
          }));
          
          // 创建主干连接
          const mainEdges = [];
          for (let i = 0; i < mainNodes.length - 1; i++) {
            mainEdges.push({
              id: `e-${mainNodes[i].id}-${mainNodes[i + 1].id}`,
              source: mainNodes[i].id,
              sourceHandle: 'bottom',
              target: mainNodes[i + 1].id,
              targetHandle: 'top',
              type: EdgeTypeEnum.SmoothStep,
              style: {
                strokeWidth: 4,
                stroke: '#409EFF'
              },
              markerEnd: {
                type: MarkerTypeEnum.ArrowClosed,
                color: '#409EFF'
              }
            });
          }
          
          // 添加分支节点和连接
          const branchNodes = [];
          const branchEdges = [];
          
          if (data.branches && data.branchEvents) {
            // 创建分支与主干的连接映射
            const branchToMainMap = {};
            data.branches.forEach(branch => {
              if (branch.origin && branch.origin.id) {
                branchToMainMap[branch.id] = {
                  mainNodeId: branch.origin.id,
                  point: branch.origin.point || 'right',
                  color: branch.color || getRandomColor()
                };
              }
            });
            
            // 创建分支节点
            data.branchEvents.forEach((event, index) => {
              const branchInfo = branchToMainMap[event.branchId];
              if (!branchInfo) return;
              
              const isLeftSide = branchInfo.point === 'left';
              const branchNode = {
                id: event.id || `branch-${nanoid(6)}`,
                type: 'branch-event',
                position: { 
                  x: isLeftSide ? centerX - 250 : centerX + 250, 
                  y: 100 + index * 100
                },
                data: {
                  label: event.title,
                  year: Number(event.year),
                  month: Number(event.month),
                  day: Number(event.day),
                  content: event.description || '',
                  nodeType: 'branch',
                  color: branchInfo.color,
                  parentId: branchInfo.mainNodeId,
                  isLeftSide: isLeftSide
                }
              };
              
              branchNodes.push(branchNode);
              
              // 创建连接到主干的边
              branchEdges.push({
                id: `e-${branchInfo.mainNodeId}-${branchNode.id}`,
                source: branchInfo.mainNodeId,
                sourceHandle: isLeftSide ? 'left' : 'right',
                target: branchNode.id,
                targetHandle: isLeftSide ? 'right' : 'left',
                type: EdgeTypeEnum.SmoothStep,
                animated: true,
                style: {
                  strokeWidth: 3,
                  stroke: branchInfo.color
                },
                markerEnd: {
                  type: MarkerTypeEnum.ArrowClosed,
                  color: branchInfo.color
                }
              });
            });
          }
          
          // 添加额外连接
          const extraEdges = [];
          if (data.connections && data.connections.length > 0) {
            data.connections.forEach(conn => {
              extraEdges.push({
                id: `e-custom-${nanoid(6)}`,
                source: conn.source,
                target: conn.target,
                type: EdgeTypeEnum.SmoothStep,
                animated: conn.animated || false,
                style: {
                  strokeWidth: conn.style?.strokeWidth || 2,
                  stroke: conn.style?.stroke || '#409EFF'
                },
                markerEnd: {
                  type: MarkerTypeEnum.ArrowClosed, 
                  color: conn.style?.stroke || '#409EFF'
                }
              });
            });
          }
          
          // 设置所有节点和边
          nodes.value = [...mainNodes, ...branchNodes];
          edges.value = [...mainEdges, ...branchEdges, ...extraEdges];
        }
        
        // 调整视图
        nextTick(() => {
          autoLayout();
          // 移除fitView调用，保持当前缩放级别
        });
      });
      
      return true;
    } catch (error) {
      console.error('导入数据时出错:', error);
      return false;
    }
  },
  // 获取当前时间线数据的方法，用于保存到后端
  getTimelineData: () => {
    try {
      const currentNodes = getNodes();
      const currentEdges = getEdges();
      
      // 转换节点格式为更简洁的结构
      const exportNodes = currentNodes.map(node => ({
        id: node.id,
        type: node.type,
        x: node.position.x,
        y: node.position.y,
        data: node.data,
        nodeType: node.data.nodeType
      }));
      
      // 转换边格式
      const exportEdges = currentEdges.map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        type: edge.type,
        animated: edge.animated,
        style: {
          strokeWidth: edge.style?.strokeWidth || 2,
          stroke: edge.style?.stroke || '#409EFF'
        },
        markerEnd: edge.markerEnd
      }));
      
      // 创建导出数据结构
      return {
        nodes: exportNodes,
        edges: exportEdges,
        version: "2.0" // 添加版本号，便于未来扩展
      };
    } catch (error) {
      console.error('获取时间线数据失败:', error);
      throw error;
    }
  },
  // 触发保存数据的方法
  triggerSave: () => {
    const customEvent = new CustomEvent('save-timeline');
    document.dispatchEvent(customEvent);
  }
});

// 导入数据
const importTimelineData = () => {
  try {
    // 解析JSON数据
    const data = JSON.parse(importJsonData.value);
    if (!data || !data.nodes || !data.edges) {
      ElMessageBox.alert('无效的数据格式', '错误', { type: 'error' });
      return;
    }

    // 清空当前数据
    nodes.value = [];
    edges.value = [];

    // 添加新节点
    data.nodes.forEach(node => {
      const position = {
        x: node.position?.x || node.x || (node.nodeType === 'main' ? centerX : (node.isLeftSide ? centerX - 250 : centerX + 250)),
        y: node.position?.y || node.y || 100
      };
      
      const nodeData = {
        label: node.data?.label || node.label || '未命名事件',
        year: node.data?.year || node.year || new Date().getFullYear(),
        month: node.data?.month || node.month || new Date().getMonth() + 1,
        day: node.data?.day || node.day || new Date().getDate(),
        content: node.data?.content || node.content || '',
        nodeType: node.data?.nodeType || node.nodeType || 'main',
        color: node.data?.color || node.color || (node.nodeType === 'main' ? '#409EFF' : getRandomColor()),
        parentId: node.data?.parentId || node.parentId,
        isLeftSide: node.data?.isLeftSide || node.isLeftSide,
        customTime: node.data?.customTime || node.customTime,
        useCustomTime: node.data?.useCustomTime || node.useCustomTime
      };
      
      addNodes([{
        id: node.id,
        type: node.type,
        position: position,
        data: nodeData
      }]);
    });

    // 先仅添加非主干连接
    const nonMainEdges = data.edges.filter(edge => {
      const sourceNodeData = data.nodes.find(n => n.id === edge.source)?.data || 
                           data.nodes.find(n => n.id === edge.source);
      const targetNodeData = data.nodes.find(n => n.id === edge.target)?.data || 
                           data.nodes.find(n => n.id === edge.target);
      
      return !(
        (sourceNodeData?.nodeType === 'main' || sourceNodeData?.data?.nodeType === 'main') && 
        (targetNodeData?.nodeType === 'main' || targetNodeData?.data?.nodeType === 'main')
      );
    });

    // 添加非主干边
    nonMainEdges.forEach(edge => {
      // 确保边有必要的属性
      const newEdge = {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        type: edge.type || EdgeTypeEnum.SmoothStep,
        animated: edge.animated || false,
        style: {
          strokeWidth: edge.style?.strokeWidth || 2,
          stroke: edge.style?.stroke || '#409EFF'
        },
        markerEnd: edge.markerEnd || {
          type: MarkerTypeEnum.ArrowClosed,
          color: edge.style?.stroke || '#409EFF'
        }
      };
      
      addEdges([newEdge]);
    });

    // 关闭对话框
    importDialogVisible.value = false;
    importJsonData.value = '';
    
    // 重新布局以确保正确显示
    nextTick(() => {
      // 使用replaceMainConnections来建立主干连接
      replaceMainConnections();
      
      // 调整视图
      fitView();
      
      ElMessage.success('数据导入成功');
    });
  } catch (error) {
    console.error('导入数据时出错:', error);
    ElMessageBox.alert('导入数据失败: ' + error.message, '错误', { type: 'error' });
  }
};

// 预定义颜色
const predefineColors = [
  '#409EFF', // 主线/主要事件 - 蓝色
  '#e84393', // 爱情/情感 - 粉红色
  '#00b894', // 成长/进步 - 绿色
  '#e17055', // 冲突/战斗 - 橙红色
  '#6c5ce7', // 神秘/魔法 - 紫色
  '#fdcb6e', // 发现/启示 - 黄色
  '#00cec9', // 旅程/探索 - 青色
  '#ff7675', // 危机/危险 - 红色
  '#74b9ff', // 回忆/过去 - 淡蓝色
  '#a29bfe', // 梦境/幻想 - 淡紫色
  '#55efc4', // 希望/治愈 - 薄荷绿
  '#fab1a0', // 友情/同伴 - 淡橙色
  '#ff9ff3', // 浪漫/爱慕 - 粉色
  '#ffeaa7', // 欢乐/庆祝 - 淡黄色
  '#636e72', // 悲伤/失落 - 灰色
  '#2d3436', // 黑暗/恐惧 - 深灰色
  '#b71540', // 仇恨/复仇 - 深红色
  '#0a3d62'  // 智慧/思考 - 深蓝色
];

// 临时存储添加事件的信息
const pendingEventInfo = ref({
  sourceNodeId: '',
  direction: '',
  position: { x: 0, y: 0 },
  isFreeEvent: false,
  nodeType: ''
});

// 准备添加事件 - 先打开编辑对话框
const prepareAddEvent = (nodeId, direction) => {
  const node = findNode(nodeId);
  if (!node) return;
  
  // 计算新节点位置
  let position = { x: node.position.x, y: node.position.y };
  
  switch (direction) {
    case 'up':
      position.y -= 180;
      break;
    case 'down':
      position.y += 80;
      break;
    case 'left':
      position.x -= 250;
      break;
    case 'right':
      position.x += 250;
      break;
  }
  
  // 存储临时信息
  pendingEventInfo.value = {
    sourceNodeId: nodeId,
    direction: direction,
    position: position,
    isFreeEvent: false,
    nodeType: direction === 'left' || direction === 'right' ? 'branch' : node.data.nodeType
  };
  
  // 创建临时编辑数据
  const tempEventData = {
    label: '新事件',
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    day: new Date().getDate(),
    content: '',
    nodeType: pendingEventInfo.value.nodeType,
    color: pendingEventInfo.value.nodeType === 'main' ? '#409EFF' : getRandomColor(),
    isLeftSide: direction === 'left'
  };
  
  // 打开编辑对话框
  editingEvent.value = tempEventData;
  editingNodeId.value = 'new-node'; // 标记为新节点
  editDialogVisible.value = true;
};

// 准备添加自由事件
const prepareAddFreeEvent = (nodeId) => {
  const node = findNode(nodeId);
  if (!node) return;
  
  // 计算新节点位置 - 偏移一定距离
  const position = {
    x: node.position.x + 100,
    y: node.position.y + 100
  };
  
  // 存储临时信息
  pendingEventInfo.value = {
    sourceNodeId: nodeId,
    direction: 'free',
    position: position,
    isFreeEvent: true,
    nodeType: node.data.nodeType
  };
  
  // 创建临时编辑数据
  const tempEventData = {
    label: '自由事件',
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    day: new Date().getDate(),
    content: '',
    nodeType: node.data.nodeType,
    color: node.data.nodeType === 'main' ? '#409EFF' : getRandomColor()
  };
  
  // 打开编辑对话框
  editingEvent.value = tempEventData;
  editingNodeId.value = 'new-node'; // 标记为新节点
  editDialogVisible.value = true;
};

// 获取颜色含义
const getColorMeaning = (color) => {
  const meanings = {
    '#409EFF': '主线/主要事件 - 蓝色',
    '#e84393': '爱情/情感 - 粉红色',
    '#00b894': '成长/进步 - 绿色',
    '#e17055': '冲突/战斗 - 橙红色',
    '#6c5ce7': '神秘/魔法 - 紫色',
    '#fdcb6e': '发现/启示 - 黄色',
    '#00cec9': '旅程/探索 - 青色',
    '#ff7675': '危机/危险 - 红色',
    '#74b9ff': '回忆/过去 - 淡蓝色',
    '#a29bfe': '梦境/幻想 - 淡紫色',
    '#55efc4': '希望/治愈 - 薄荷绿',
    '#fab1a0': '友情/同伴 - 淡橙色',
    '#ff9ff3': '浪漫/爱慕 - 粉色',
    '#ffeaa7': '欢乐/庆祝 - 淡黄色',
    '#636e72': '悲伤/失落 - 灰色',
    '#2d3436': '黑暗/恐惧 - 深灰色',
    '#b71540': '仇恨/复仇 - 深红色',
    '#0a3d62': '智慧/思考 - 深蓝色'
  };
  return meanings[color] || '未知颜色';
};

// 获取颜色含义的简短版本
const getColorMeaningShort = (color) => {
  const meanings = {
    '#409EFF': '主线/主要事件 - 蓝色',
    '#e84393': '爱情/情感 - 粉红色',
    '#00b894': '成长/进步 - 绿色',
    '#e17055': '冲突/战斗 - 橙红色',
    '#6c5ce7': '神秘/魔法 - 紫色',
    '#fdcb6e': '发现/启示 - 黄色',
    '#00cec9': '旅程/探索 - 青色',
    '#ff7675': '危机/危险 - 红色',
    '#74b9ff': '回忆/过去 - 淡蓝色',
    '#a29bfe': '梦境/幻想 - 淡紫色',
    '#55efc4': '希望/治愈 - 薄荷绿',
    '#fab1a0': '友情/同伴 - 淡橙色',
    '#ff9ff3': '浪漫/爱慕 - 粉色',
    '#ffeaa7': '欢乐/庆祝 - 淡黄色',
    '#636e72': '悲伤/失落 - 灰色',
    '#2d3436': '黑暗/恐惧 - 深灰色',
    '#b71540': '仇恨/复仇 - 深红色',
    '#0a3d62': '智慧/思考 - 深蓝色'
  };
  return meanings[color] || '未知颜色';
};

// 选择颜色
const selectColor = (color) => {
  editingEvent.value.color = color;
  ElMessage({
    message: `已选择: ${getColorMeaningShort(color)}`,
    type: 'success',
    duration: 1500
  });
};

// 处理时间模式切换
const handleTimeModeChange = (value) => {
  // 当从标准时间切换到自定义时间时，自动生成自定义时间字符串
  if (value && editingEvent.value.year) {
    // 如果还没有自定义时间值，则从标准时间生成
    if (!editingEvent.value.customTime) {
      editingEvent.value.customTime = `${editingEvent.value.year}年${editingEvent.value.month || ''}月${editingEvent.value.day || ''}日`;
    }
  } 
  // 从自定义时间切换到标准时间时，尝试解析自定义时间
  else if (!value && editingEvent.value.customTime) {
    // 尝试从自定义时间中提取年月日（简单实现）
    const yearMatch = editingEvent.value.customTime.match(/(\d+)\s*年/);
    const monthMatch = editingEvent.value.customTime.match(/(\d+)\s*月/);
    const dayMatch = editingEvent.value.customTime.match(/(\d+)\s*日/);
    
    if (yearMatch) editingEvent.value.year = parseInt(yearMatch[1]);
    if (monthMatch) editingEvent.value.month = parseInt(monthMatch[1]);
    if (dayMatch) editingEvent.value.day = parseInt(dayMatch[1]);
  }
};

// 添加handleKeyDown函数，处理键盘事件
const handleKeyDown = (event) => {
  // 检测Ctrl+S组合键 (Windows/Linux) 或 Cmd+S (Mac)
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    // 阻止浏览器默认的保存行为
    event.preventDefault();
    // 触发save事件，父组件可以监听此事件
    const customEvent = new CustomEvent('save-timeline');
    document.dispatchEvent(customEvent);
    console.log('触发保存事件: Ctrl+S');
  }
};

// 添加数据变更触发函数
const triggerDataChanged = () => {
  // 创建自定义事件
  const event = new CustomEvent('timeline-data-changed');
  document.dispatchEvent(event);
};

</script>

<style scoped>
.timeline-flow-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

.timeline-flow {
  width: 100%;
  height: 100%;
  background-color: var(--el-bg-color);
  flex: 1;
  position: relative;
  overflow: visible; /* 允许内容超出容器可见 */
}

/* 控制面板样式 */
.panel-top {
  margin-top: 10px;
  margin-right: 10px;
}

.panel-buttons {
  display: flex;
  gap: 8px;
}

/* 右键菜单样式 */
.context-menu {
  position: absolute;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  z-index: 9999; /* 提高z-index确保在全屏模式下也能显示 */
  padding: 5px 0;
  min-width: 120px;
  /* 确保在全屏模式下也能正确显示 */
  pointer-events: auto;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.context-menu-item:hover {
  background-color: var(--el-color-primary-light-9);
}

/* 编辑表单样式 */
.date-inputs {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
  width: 100%;
}

:deep(.date-input-year) {
  width: 100px !important;
}

:deep(.date-input-month), :deep(.date-input-day) {
  width: 50px !important;
}

:deep(.date-input-year .el-input-number__decrease),
:deep(.date-input-year .el-input-number__increase),
:deep(.date-input-month .el-input-number__decrease),
:deep(.date-input-month .el-input-number__increase),
:deep(.date-input-day .el-input-number__decrease),
:deep(.date-input-day .el-input-number__increase) {
  display: none;
}

:deep(.el-input-number .el-input__inner) {
  padding-left: 8px;
  padding-right: 8px;
  text-align: center;
}

.date-separator {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}



/* 导入数据对话框样式 */
.import-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.time-mode-switch {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.custom-time-inputs {
  display: flex;
  align-items: center;
  width: 100%;
}

.custom-time-input {
  width: 100% !important;
}

/* 调整日期输入框样式，将class改为date-form-item移除 */
:deep(.date-input-year) {
  width: 100px !important;
}

/* Vue Flow 控制按钮样式重写 - Light 主题 */
.light :deep(.vue-flow__controls-button) {
  background: #ffffff !important;
  border: none !important;
  box-sizing: content-box !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 16px !important;
  height: 16px !important;
  cursor: pointer !important;
  user-select: none !important;
  padding: 5px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  color: #333333 !important;
}

.light :deep(.vue-flow__controls-button:hover) {
  background-color: #f5f5f5 !important;

  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

.light :deep(.vue-flow__controls-button:active) {
  transform: translateY(0px) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Vue Flow 控制按钮样式重写 - Dark 主题 */
.dark :deep(.vue-flow__controls-button) {
  background-color: #2d2d2d !important;
  border: none !important;
  
  box-sizing: content-box !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 16px !important;
  height: 16px !important;
  cursor: pointer !important;
  user-select: none !important;
  padding: 5px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  color: #e5e5e5 !important;
}

.dark :deep(.vue-flow__controls-button:hover) {
  background-color: #3a3a3a !important;
  
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4) !important;
}

.dark :deep(.vue-flow__controls-button:active) {
  transform: translateY(0px) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* 颜色选择器容器样式 */
.color-picker-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-meaning-hint {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  flex-wrap: wrap;
}

.color-meaning-hint > span {
  padding: 2px 6px;
  background: var(--el-fill-color-light);
  border-radius: 3px;
  font-size: 12px;
  white-space: nowrap;
}

/* 颜色弹窗样式 */
:deep(.color-popover) {
  padding: 16px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  background: var(--el-bg-color) !important;
}

.color-popover-header {
  margin-bottom: 16px;
  text-align: center;
}

.color-popover-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.color-popover-tip {
  margin: 0;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 颜色网格布局 */
.color-meanings-popover {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  max-height: 320px;
  overflow-y: auto;
  padding: 4px;
}

/* 自定义滚动条样式 */
.color-meanings-popover::-webkit-scrollbar {
  width: 6px;
}

.color-meanings-popover::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.color-meanings-popover::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.color-meanings-popover::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 颜色项样式 */
.color-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background: var(--el-fill-color-lighter);
  min-height: 44px;
}

.color-item:hover {
  background: var(--el-color-primary-light-9);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.color-item-active {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
}

/* 颜色色块样式 */
.color-swatch {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  border: 2px solid var(--el-border-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  position: relative;
}

.color-swatch::after {
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

/* 颜色描述文字 */
.color-desc {
  font-size: 13px;
  color: var(--el-text-color-primary);
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
}

/* 颜色帮助按钮样式 */
.color-help-button {
  margin-left: 8px;
  font-size: 12px;
  padding: 4px 8px !important;
  height: auto !important;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.color-help-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.color-help-button .el-icon {
  margin-left: 4px;
  transition: transform 0.2s ease;
}

.color-help-button:hover .el-icon {
  transform: rotate(180deg);
}

/* 自定义对话框样式 - 确保在全屏模式下也能正确显示 */
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999; /* 非常高的z-index确保在全屏模式下也能显示 */
  padding: 20px;
}

.custom-dialog {
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 450px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.custom-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color);
  background: var(--el-bg-color);
}

.custom-dialog-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.custom-dialog-close {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--el-text-color-regular);
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  border-radius: 4px;
  transition: all 0.2s;
}

.custom-dialog-close:hover {
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

.custom-dialog-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.custom-dialog-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: var(--el-bg-color);
}

/* 小尺寸对话框样式 */
.custom-dialog-small {
  max-width: 380px;
}
</style>